package config

import (
	"context"
	"sync"
	"time"

	"github.com/MoeGolibrary/go-lib/conf"
	eventbus "github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/go-lib/zlog"
	"go.uber.org/zap"
)

var (
	once sync.Once
)

var config *Config

type Config struct {
	Port       int              `yaml:"port"`
	Debug      bool             `yaml:"debug"`
	JaegerURL  string           `yaml:"jaeger_url"`
	DB         DBConfig         `yaml:"db"`
	EventBus   *eventbus.Config `yaml:"event_bus"`
	GrowthBook GrowthBook       `yaml:"growth_book"`
}

type DBConfig struct {
	DSN                   string        `yaml:"dsn"`
	MaxOpenConnections    int           `yaml:"max_open_connections"`
	MaxIdleConnections    int           `yaml:"max_idle_connections"`
	MaxConnectionLifetime time.Duration `yaml:"max_connection_lifetime"`
}

type GrowthBook struct {
	Host      string        `yaml:"host"`
	ClientKey string        `yaml:"client_key"`
	Interval  time.Duration `yaml:"interval"`
}

func GetConfig() *Config {
	return config
}

// Init 读取配置文件
func Init() {
	once.Do(func() {
		config = &Config{}
		loader := conf.GetDefaultLoader()
		if err := loader.InitializeConfig(config); err != nil {
			zlog.Fatal(context.Background(),
				"initialize config failed",
				zap.Error(err))
		}
	})
}
