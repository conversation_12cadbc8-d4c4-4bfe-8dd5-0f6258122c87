port: 9090
db:
  dsn: postgresql://${secret.datasource.postgres.moego_fulfillment.username}:${secret.datasource.postgres.moego_fulfillment.password}@${secret.datasource.postgres.url.master}:${secret.datasource.postgres.port}/moego_fulfillment?sslmode=disable
  max_open_connections: 100
  max_idle_connections: 10
debug: false
jaeger_url: http://jaeger-collector.jaeger.svc.cluster.local:14268/api/traces
event_bus:
  brokers:
    - ${secret.mq.kafka.broker_url_0}
    - ${secret.mq.kafka.broker_url_1}
    - ${secret.mq.kafka.broker_url_2}
  consumer:
    group_id: moego.fulfillment
    topics:
      - moego.offering
      - moego.order
  producer:
    topic: moego.fulfillment
  credential:
    region: ${secret.aws.region}
    access_key_id: ${secret.aws.access_key_id}
    secret_access_key: ${secret.aws.secret_access_key}

growth_book:
  host: ${secret.growthbook.host}
  client_key: ${secret.growthbook.client_key}
  interval: 10s

secrets:
  - name: "moego/staging/datasource"
    prefix: "secret.datasource."
  - name: "mq"
    prefix: "secret.mq."
  - name: "aws"
    prefix: "secret.aws."
  - name: "moego/staging/growthbook"
    prefix: "secret.growthbook."
