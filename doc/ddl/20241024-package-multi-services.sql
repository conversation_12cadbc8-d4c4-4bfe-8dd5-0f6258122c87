-- DDL
alter table moe_retail.service_package
  add services json default (json_array()) not null comment 'package item 支持选择多个 service，多个 service 之间共享 quantity，单个 service 结构参考：com.moego.server.retail.dto.PackageInfoDto.Service';

alter table moe_grooming.moe_grooming_package_service
  add services json default (json_array()) not null comment 'package item 支持选择多个 service，多个 service 之间共享 quantity，单个 service 结构参考：com.moego.server.retail.dto.PackageInfoDto.Service';

-- 通过 package id 查询占用 package
create index moe_grooming_invoice_apply_package_package_id_index
  on moe_grooming.moe_grooming_invoice_apply_package (package_id);

-- 数据迁移脚本，数据量 2w+，可以直接执行
-- 执行时机：后端发完版，前端发版前执行
-- 可重复执行
update moe_retail.service_package
set services = concat('[{', '"serviceId":', service_id, ', "unitPrice":', unit_price, '}]')
where services = json_array();

-- 数据量 8k+
update moe_grooming.moe_grooming_package_service
set services = concat('[{', '"serviceId":', service_id, ', "unitPrice":', service_unit_price, '}]')
where services = json_array();
