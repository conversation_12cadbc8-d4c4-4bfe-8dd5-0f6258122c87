ALTER TABLE "public"."service_charge"
    ADD COLUMN "is_all_location" bool NOT NULL DEFAULT true;

CREATE SEQUENCE service_charge_location_id_seq;

CREATE TABLE "public"."service_charge_location" (
    "id" int8 NOT NULL DEFAULT nextval('service_charge_location_id_seq'::regclass),
    "company_id" int8 NOT NULL DEFAULT 0,
    "business_id" int8 NOT NULL DEFAULT 0,
    "service_charge_id" int8 NOT NULL DEFAULT 0,
    "price" numeric(20,2) DEFAULT NULL,
    "tax_id" int4 DEFAULT NULL,
    "deleted_at" timestamp DEFAULT NULL,
    "created_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY ("id")
);


-- pgsql
ALTER TABLE "public"."service_charge"
    ALTER COLUMN "business_id" SET DEFAULT 0;

    //order_line_discount_index_business_id_order_id

CREATE INDEX order_line_discount_index_order_id on "public"."order_line_discount" ("order_id");
CREATE INDEX order_line_extra_fee_index_order_id on "public"."order_line_extra_fee" ("order_id");
CREATE INDEX order_line_tax_index_order_id on "public"."order_line_tax" ("order_id");