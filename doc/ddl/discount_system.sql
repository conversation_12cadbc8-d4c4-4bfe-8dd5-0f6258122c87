-- auto-generated definition
create table cart_discount
(
  id               bigint auto_increment
    primary key,
  company_id       bigint         default 0                 not null comment 'company id',
  cart_id          int            default 0                 not null comment 'cart id',
  cart_item_ids    json           default (json_array())    not null comment 'cart item ids',
  apply_type       varchar(20)    default 'all'             not null comment 'apply type (all/item)',
  apply_sequence   int            default 0                 not null comment 'apply sequence',
  discount_type    varchar(20)    default ''                not null comment 'discount type (amount/percentage)',
  discount_amount  decimal(20, 2) default 0.00              not null comment 'discount amount',
  discount_rate    decimal(20, 2) default 0.00              not null comment 'discount rate',
  discount_code_id bigint         default 0                 not null comment 'discount code id',
  create_time      timestamp      default CURRENT_TIMESTAMP not null comment 'create time'
);

create index cart_discount_company_id_cart_id_index
  on cart_discount (company_id, cart_id);

