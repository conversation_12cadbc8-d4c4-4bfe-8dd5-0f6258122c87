CREATE TABLE `invoice_remove_log` (
                                      `id` int unsigned NOT NULL AUTO_INCREMENT,
                                      `invoice_id` int NOT NULL DEFAULT '0' COMMENT 'invoice id',
                                      `package_id` int NOT NULL DEFAULT '0' COMMENT 'package id',
                                      `package_name` varchar(255) COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT '' COMMENT 'package name',
                                      `staff_id` int NOT NULL DEFAULT '0' COMMENT 'staff id',
                                      `create_time` bigint NOT NULL DEFAULT '0' COMMENT 'create time',
                                      `update_time` bigint NOT NULL DEFAULT '0' COMMENT 'update time',
                                      PRIMARY KEY (`id`),
                                      KEY `invoice_remove_log_invoice_id_index` (`invoice_id`)
) ENGINE=InnoDB AUTO_INCREMENT=13 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_520_ci;