-- Add time_based_pricing_type, default Fixed Rate (1)
ALTER TABLE service_charge
    ADD COLUMN time_based_pricing_type SMALLINT NOT NULL DEFAULT 1;

-- Add multiple_pets_charge_type, default Same Charge Per Pet (1)
ALTER TABLE service_charge
    ADD COLUMN multiple_pets_charge_type SMALLINT NOT NULL DEFAULT 1;

-- Add hourly_exceed_rules, default empty JSON array
ALTER TABLE service_charge
    ADD COLUMN hourly_exceed_rules JSONB DEFAULT '[]'::JSONB;

-- Add column comments
COMMENT ON COLUMN service_charge.time_based_pricing_type IS 'Billing type: 1=Fixed rate, 2=Tiered rate';
COMMENT ON COLUMN service_charge.multiple_pets_charge_type IS 'Multiple pets billing method: 1=Same charge per pet, 2=Different charge for additional pets';
COMMENT ON COLUMN service_charge.hourly_exceed_rules IS 'List of 24-hour cycle rules, in JSON format. Each object contains fields such as id, fee_name, hour, base_price, additional_pet_price';
