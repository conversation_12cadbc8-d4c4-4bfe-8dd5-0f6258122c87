---- order_payment_retry_log
drop table if EXISTS order_payment_retry_log;
-- deprecated table
-- create table if not exists order_payment_retry_log (
--     id bigint generated always as identity primary key,
--     order_payment_id bigint default 0 not null,
--     create_time timestamp default now() not null,
--     retry_count int default 0 not null
-- );
--
-- create unique index idx_order_payment_id on order_payment_retry_log(order_payment_id);
-- create index idx_create_time on order_payment_retry_log(create_time);


---- order_payment
drop table if EXISTS order_payment;
create table if not exists order_payment (
         id bigint generated always as identity primary key,
         order_id             bigint default 0 not null,
         company_id           bigint default 0 not null,
         business_id         bigint default 0 not null,
         staff_id             bigint default 0 not null,
         customer_id          bigint default 0 not null,
         payment_id           bigint default 0 not null,
         payment_method_id    bigint default 0 not null,
         payment_method  TEXT   default '' not null,
         payment_method_extra JSONB  default '{}' not null,
         payment_method_vendor TEXT default '' not null,
         is_online            BOOLEAN default false not null,
         is_deposit           BOOLEAN default false not null,
         paid_by              TEXT default '' not null,
         currency             TEXT default '' not null,
         total_amount         numeric(20, 2) default 0.00 not null,
         amount               numeric(20, 2) default 0.00 not null,
         refunded_amount      numeric(20, 2) default 0.00 not null,
         processing_fee       numeric(20, 2) default -1.00 not null,
         convenience_fee       numeric(20, 2) default 0.00 not null,
         refunded_convenience_fee numeric(20, 2) default 0.00 not null,
         payment_tips         numeric(20, 2) default -1.00 not null,
         payment_tips_before_create         numeric(20, 2) default -1.00 not null,
         payment_tips_after_create          numeric(20, 2) default -1.00 not null,
         payment_status       TEXT default 'TYPE_UNSPECIFIED' not null,
         reason               TEXT default '' not null,
         pay_time              timestamp default default to_timestamp(0) not null,
         cancel_time           timestamp default default to_timestamp(0) not null,
         fail_time             timestamp default default to_timestamp(0) not null,
         create_time           timestamp default now() not null,
         update_time           timestamp default now() not null
);

create index idx_order_id on order_payment(order_id);
create index idx_company_customer on order_payment(company_id, customer_id);
-- order payment 添加 status + create_time 索引
create index idx_order_payment_create_time_pstc on order_payment(create_time) where payment_status = 'ORDER_PAYMENT_STATUS_TRANSACTION_CREATED';

CREATE TRIGGER auto_update_order_payment_update_time
    BEFORE UPDATE ON order_payment
    FOR EACH ROW
    EXECUTE PROCEDURE update_modified_column();


-- moego_order.order
ALTER TABLE public.order
    add COLUMN tax_round_mod smallint default  0 not null,
    ADD COLUMN company_id           bigint default 0 not null,
    ADD COLUMN currency_code TEXT default '' not null;

ALTER TABLE public.order_line_tax
    ADD COLUMN tax_name             TEXT default '' not null;

-- moego_order.order_line_item
ALTER TABLE public.order_line_item
    ADD COLUMN pet_id                      bigint default 0 not null,
    ADD COLUMN tax_id               bigint default 0 not null,
    ADD COLUMN tax_name             TEXT default '' not null,
    ADD COLUMN tax_rate      numeric(20, 4) default 0.00 not null,
    ADD COLUMN currency_code        TEXT default '' not null,
    ADD COLUMN refunded_quantity         int default 0 not null,
    ADD COLUMN refunded_amount      numeric(20, 2) default 0.00 not null,
    ADD COLUMN refunded_tax_amount      numeric(20, 2) default 0.00 not null,
    ADD COLUMN refunded_discount_amount      numeric(20, 2) default 0.00 not null,
    ADD COLUMN refunded_convenience_fee      numeric(20, 2) default 0.00 not null;

-- moego_order.order_staff_split_detail
ALTER TABLE public.order_staff_split_detail
    ADD COLUMN object_id         bigint default 0 not null,
    ADD COLUMN pet_id         bigint default 0 not null;

