-- 增加 source_id, source_type 唯一索引, 在 source_id <> 0 时生效
create unique index order_udx_source_id_type
  on public."order" (source_id, source_type)
  where (source_id <> 0);
drop index order_udx_source_id_type; -- 删除索引 for membership order


-- order 关单需求
-- alter table public."order" drop column fulfillment_status;
-- alter table public."order"  alter column order_type set default 'ORIGIN';
-- ADD COLUMN fulfillment_status varchar(64)    default '' NOT NULL,
-- alter table public."order" drop column refunded_amount_breakdown;
-- ALTER TABLE public.order ADD COLUMN refunded_amount_breakdown jsonb DEFAULT '{}'; -- 退款金额明细, 本期暂时不加


ALTER TABLE public.order
ADD COLUMN complete_time  timestamp without time zone null,
ADD COLUMN order_type varchar(64)    default 'ORIGIN' NOT NULL,
ADD COLUMN order_ref_id   bigint    default 0     NOT NULL,
ADD COLUMN extra_charge_reason varchar(64)    default '' NOT NULL,
add column order_version smallint default  0 not null;

comment on column public."order".order_type is 'ORIGIN/EXTRA';
comment on column public."order".extra_charge_reason is 'tips/product/service?';
comment on column public."order".order_ref_id is 'origin order primary id';
comment on column public."order".order_version is 'order version, 0: old data, 1: new data in white list';

CREATE INDEX idx_order_ref_id ON public.order (order_ref_id) WHERE order_type = 'EXTRA';

-- ALTER TABLE public.order_line_item ADD COLUMN "extra"  jsonb DEFAULT '{}';
-- comment on column public."order_line_item".extra is 'extra info for detail';

-- delete table order_grooming_detail_rel
DROP TABLE IF EXISTS order_grooming_detail_rel;
-- table order-grooming-pet-detail-relation
create table order_grooming_detail_rel
(
    id           bigserial
        constraint order_grooming_detail_rel_pkey primary key,
    order_id       bigint                                 not null,
    grooming_id    bigint                                 not null,
    pet_detail_id  bigint                                 not null,
    created_at   timestamp with time zone default CURRENT_TIMESTAMP
);

---- edit staff/tips需求 ----
-- table order_staff_split_detail
CREATE TABLE moego_order.public.order_staff_split_detail (
  id BIGSERIAL NOT NULL,
  create_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
  update_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
  order_id int8 DEFAULT 0 NOT NULL,
  order_item_id int8 DEFAULT 0 NOT NULL,
  type VARCHAR(128) NOT NULL DEFAULT '',
  staff_id int8 DEFAULT 0 NOT NULL,
  business_id int8 DEFAULT 0 NOT NULL,
  commission_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  commission_rate NUMERIC(10, 4) NOT NULL DEFAULT 0,
  total_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  refunded_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  split_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  split_rate NUMERIC(10, 4) NOT NULL DEFAULT 0,
  extra text DEFAULT ''::bpchar null,

  CONSTRAINT order_staff_split_detail_pk PRIMARY KEY (id)
);

CREATE INDEX idx_staff_split_order_id ON public.order_staff_split_detail(order_id);
CREATE INDEX idx_staff_split_business_staff_id ON public.order_staff_split_detail(business_id, staff_id);

COMMENT ON TABLE order_staff_split_detail IS 'order staff split detail';
COMMENT ON COLUMN order_staff_split_detail.id is 'primary key';
COMMENT ON COLUMN order_staff_split_detail.create_time is 'create time';
COMMENT ON COLUMN order_staff_split_detail.update_time is 'update time';
COMMENT ON COLUMN order_staff_split_detail.order_id is 'ref order id';
COMMENT ON COLUMN order_staff_split_detail.order_item_id is 'ref order item id';
COMMENT ON COLUMN order_staff_split_detail.type is 'type: product/service';
COMMENT ON COLUMN order_staff_split_detail.staff_id is 'staff id';
COMMENT ON COLUMN order_staff_split_detail.business_id is 'business id';
COMMENT ON COLUMN order_staff_split_detail.commission_amount is 'staff commission amount';
COMMENT ON COLUMN order_staff_split_detail.commission_rate is 'staff commission rate';
COMMENT ON COLUMN order_staff_split_detail.total_amount is 'order/item total amount';
COMMENT ON COLUMN order_staff_split_detail.refunded_amount is 'order/item refunded amount';
COMMENT ON COLUMN order_staff_split_detail.split_amount is 'staff split amount';
COMMENT ON COLUMN order_staff_split_detail.split_rate is 'staff split rate';
COMMENT ON COLUMN order_staff_split_detail.extra is 'extra info';

CREATE TRIGGER update_modified_column
    BEFORE UPDATE
    ON public.order_staff_split_detail
    FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();

-- table order_tips_split_detail
CREATE TABLE moego_order.public.order_tips_split_detail (
  id BIGSERIAL NOT NULL,
  create_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
  update_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
  order_id int8 DEFAULT 0 NOT NULL,
  staff_id int8 DEFAULT 0 NOT NULL,
  business_id int8 DEFAULT 0 NOT NULL,
  commission_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  commission_rate NUMERIC(10, 4) NOT NULL DEFAULT 0,
  total_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  refunded_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  split_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  split_rate NUMERIC(10, 4) NOT NULL DEFAULT 0,
  extra text DEFAULT ''::bpchar null,

  CONSTRAINT order_tips_split_detail_pk PRIMARY KEY (id)
);

CREATE INDEX idx_tips_split_order_id ON public.order_tips_split_detail(order_id);
CREATE INDEX idx_tips_split_business_staff_id ON public.order_tips_split_detail(business_id, staff_id);

COMMENT ON TABLE order_tips_split_detail IS 'order tips split detail';
COMMENT ON COLUMN order_tips_split_detail.id is 'primary key';
COMMENT ON COLUMN order_tips_split_detail.create_time is 'create time';
COMMENT ON COLUMN order_tips_split_detail.update_time is 'update time';
COMMENT ON COLUMN order_tips_split_detail.order_id is 'ref order id';
COMMENT ON COLUMN order_tips_split_detail.staff_id is 'staff id';
COMMENT ON COLUMN order_tips_split_detail.business_id is 'business id';
COMMENT ON COLUMN order_tips_split_detail.commission_amount is 'staff commission amount';
COMMENT ON COLUMN order_tips_split_detail.commission_rate is 'staff commission rate';
COMMENT ON COLUMN order_tips_split_detail.total_amount is 'order/item total amount';
COMMENT ON COLUMN order_tips_split_detail.refunded_amount is 'order/item refunded amount';
COMMENT ON COLUMN order_tips_split_detail.split_amount is 'staff split amount';
COMMENT ON COLUMN order_tips_split_detail.split_rate is 'staff split rate';
COMMENT ON COLUMN order_tips_split_detail.extra is 'extra info';

CREATE TRIGGER update_modified_column
    BEFORE UPDATE
    ON public.order_tips_split_detail
    FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();

-- table staff_payroll_change_log
CREATE TABLE moego_order.public.staff_payroll_change_log (
  id BIGSERIAL NOT NULL,
  create_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
  update_time timestamp DEFAULT (now() AT TIME ZONE 'UTC') NOT NULL,
  ref_type VARCHAR(128) NOT NULL DEFAULT '',
  ref_id int8 DEFAULT 0 NOT NULL,
  order_id int8 DEFAULT 0 NOT NULL,
  staff_id int8 DEFAULT 0 NOT NULL,
  business_id int8 DEFAULT 0 NOT NULL,
  amount_type VARCHAR(128) NOT NULL DEFAULT '',
  before_commission_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  commission_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  before_commission_rate NUMERIC(10, 4) NOT NULL DEFAULT 0,
  commission_rate NUMERIC(10, 4) NOT NULL DEFAULT 0,
  before_total_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  total_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  before_split_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  split_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  before_refunded_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  refunded_amount NUMERIC(20, 2) NOT NULL DEFAULT 0,
  before_split_rate NUMERIC(10, 4) NOT NULL DEFAULT 0,
  split_rate NUMERIC(10, 4) NOT NULL DEFAULT 0,
  extra text DEFAULT ''::bpchar null,

  CONSTRAINT staff_payroll_change_log_pk PRIMARY KEY (id)
);

CREATE INDEX idx_payroll_change_log_order_id ON public.staff_payroll_change_log(order_id);
CREATE INDEX idx_payroll_change_log_business_staff_id ON public.staff_payroll_change_log(business_id, staff_id);

COMMENT ON TABLE staff_payroll_change_log IS 'staff payroll change log';
COMMENT ON COLUMN staff_payroll_change_log.id is 'primary key';
COMMENT ON COLUMN staff_payroll_change_log.create_time is 'create time';
COMMENT ON COLUMN staff_payroll_change_log.update_time is 'update time';
COMMENT ON COLUMN staff_payroll_change_log.ref_type is 'ref type, e.g:edit_staff,edit_tips';
COMMENT ON COLUMN staff_payroll_change_log.ref_id is 'ref id';
COMMENT ON COLUMN staff_payroll_change_log.order_id is 'ref order id';
COMMENT ON COLUMN staff_payroll_change_log.staff_id is 'staff id';
COMMENT ON COLUMN staff_payroll_change_log.business_id is 'business id';
COMMENT ON COLUMN staff_payroll_change_log.amount_type is 'amount type, e.g:tips,item_amount';
COMMENT ON COLUMN staff_payroll_change_log.before_commission_amount is 'staff before commission amount';
COMMENT ON COLUMN staff_payroll_change_log.commission_amount is 'staff commission amount';
COMMENT ON COLUMN staff_payroll_change_log.before_commission_rate is 'staff before commission rate';
COMMENT ON COLUMN staff_payroll_change_log.commission_rate is 'staff commission rate';
COMMENT ON COLUMN staff_payroll_change_log.before_total_amount is 'order/item before total amount';
COMMENT ON COLUMN staff_payroll_change_log.total_amount is 'order/item total amount';
COMMENT ON COLUMN staff_payroll_change_log.before_refunded_amount is 'order/item before refunded amount';
COMMENT ON COLUMN staff_payroll_change_log.refunded_amount is 'order/item refunded amount';
COMMENT ON COLUMN staff_payroll_change_log.before_split_amount is 'staff before split amount';
COMMENT ON COLUMN staff_payroll_change_log.split_amount is 'staff split amount';
COMMENT ON COLUMN staff_payroll_change_log.before_split_rate is 'staff before split rate';
COMMENT ON COLUMN staff_payroll_change_log.split_rate is 'staff split rate';
COMMENT ON COLUMN staff_payroll_change_log.extra is 'extra info';

CREATE TRIGGER update_modified_column
    BEFORE UPDATE
    ON public.staff_payroll_change_log
    FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();