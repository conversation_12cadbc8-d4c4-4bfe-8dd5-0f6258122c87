-- service charge config table
create table service_charge
(
  id           bigserial
    constraint service_charge_pkey primary key,
  business_id  bigint                                 not null,
  name         varchar(255)                           not null,
  description  text                     default ''::bpchar,
  price        decimal(20, 2)                         not null,
  tax_id       bigint                   default 0     not null,
  sort         integer                  default 0     not null,
  is_mandatory boolean                  default false not null,
  is_active    boolean                  default true  not null,
  is_deleted   boolean                  default false not null,
  created_by   bigint                   default 0     not null,
  updated_by   bigint                   default 0     not null,
  created_at   timestamp with time zone default CURRENT_TIMESTAMP,
  updated_at   timestamp with time zone default CURRENT_TIMESTAMP
);

comment on table service_charge is 'service charge config table';
comment on column service_charge.name is 'service charge name, unique for business';
comment on column service_charge.description is 'description for service charge';
comment on column service_charge.price is 'price, positive, 2 decimal';
comment on column service_charge.tax_id is 'related tax id, nullable';
comment on column service_charge.sort is 'sort value, desc';
comment on column service_charge.is_mandatory is 'is mandatory, if true, will be added to order automatically';
comment on column service_charge.is_active is 'is active';

create index service_charge_idx_biz_id on service_charge (business_id);

-- trigger设置，自动更新update time字段
create function auto_set_updated_at() returns trigger
  language plpgsql
as
$$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$;

create trigger auto_update_service_charge_updated_at
  before update
  on service_charge
  for each row
execute procedure auto_set_updated_at();
