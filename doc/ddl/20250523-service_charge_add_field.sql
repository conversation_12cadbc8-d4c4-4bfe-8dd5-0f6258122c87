-- Whether the service filter is enabled
ALTER TABLE service_charge
    ADD COLUMN enable_service_filter boolean NOT NULL DEFAULT false;

COMMENT ON COLUMN service_charge.enable_service_filter IS 'Indicates if service filtering is enabled. When true, refer to service_filter_rules for details';

-- Service filter rules in JSONB format
ALTER TABLE service_charge
    ADD COLUMN service_filter_rules JSONB DEFAULT '[]'::JSONB;

COMMENT ON COLUMN service_charge.service_filter_rules IS 'Filter rules for service types and service IDs. Contains: service_item_type, available_for_all_services, available_service_id_list';


ALTER TABLE "public"."service_charge" 
  ADD COLUMN "source" int2 NOT NULL DEFAULT 1;

COMMENT ON COLUMN "public"."service_charge"."source" IS '1-MoeGo Platform 2-Enterprise Hub';