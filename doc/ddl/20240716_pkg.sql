alter table moe_grooming.moe_grooming_package
  add expiration_date varchar(50) not null default ''
    comment '过期日期，大于 expiration_date 意味着过期，用一个魔法值 9999-01-01 代表永不过期。这样在查询可用 package 时，只需要判断 expiration_date > current_date() 即可';

alter table moe_retail.package
  add column is_active bool not null default false;
alter table moe_retail.package
  add column expiration_days int not null default 0 comment '过期天数，用一个魔法值 99999999 代表永不过期。为什么不使用 -1/0 的原因：在查询 package 过期时间大于某个值（x）时，如果使用 -1，查询条件会变为 expiration_days > x or expiration_days = -1，使用一个无穷大值只需要判断 expiration_days > x 即可';

# 后端发完版之后执行（可重复执行）
UPDATE moe_retail.package
SET is_active = IF(end_time = 0 AND start_time = 0, true,
                   IF(CURDATE() < DATE(FROM_UNIXTIME(start_time)) OR
                      CURDATE() > DATE(FROM_UNIXTIME(end_time)), false,
                      true))
WHERE is_active = false
  and expiration_days = 0;

UPDATE moe_grooming.moe_grooming_package
SET expiration_date = DATE(FROM_UNIXTIME(end_time))
where expiration_date = '';
