ALTER TABLE `moe_retail`.`product` ADD COLUMN `barcode` varchar(128) DEFAULT NULL COMMENT 'product barcode';

ALTER TABLE `moe_retail`.`product` MODIFY COLUMN `supply_price` decimal(20,4) NOT NULL DEFAULT '0.0000' COMMENT 'supply price';
ALTER TABLE `moe_retail`.`product` MODIFY COLUMN `retail_price` decimal(20,4) NOT NULL DEFAULT '0.0000' COMMENT '销售价格，必填项';
ALTER TABLE `moe_retail`.`product` MODIFY COLUMN `special_price` decimal(20,4) DEFAULT NULL COMMENT '非必填；若填写，则在product list和sales list会显示special price';


ALTER TABLE `moe_retail`.`cart` MODIFY COLUMN `sub_total_amount` decimal(20,4) NOT NULL DEFAULT '0.0000';
ALTER TABLE `moe_retail`.`cart` MODIFY COLUMN `discount_amount` decimal(20,2) NOT NULL DEFAULT '0.00';
ALTER TABLE `moe_retail`.`cart` MODIFY COLUMN `tip_amount` decimal(20,2) NOT NULL DEFAULT '0.00';
ALTER TABLE `moe_retail`.`cart` MODIFY COLUMN `tax_amount` decimal(20,4) NOT NULL DEFAULT '0.0000';
ALTER TABLE `moe_retail`.`cart` MODIFY COLUMN `total_amount` decimal(20,4) NOT NULL DEFAULT '0.0000';
ALTER TABLE `moe_retail`.`cart` MODIFY COLUMN `payment_amount` decimal(20,2) NOT NULL DEFAULT '0.00';
ALTER TABLE `moe_retail`.`cart` MODIFY COLUMN `discounted_sub_total_amount` decimal(20,4) NOT NULL DEFAULT '0.0000';
ALTER TABLE `moe_retail`.`cart` MODIFY COLUMN `discount_rate` decimal(20,4) NOT NULL DEFAULT '0.0000';
