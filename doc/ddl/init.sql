-- order表
create table "order"
(
  id                 bigserial
    constraint order_pkey
      primary key,
  business_id        bigint                                  not null,
  status             smallint       default 0                not null,
  payment_status     varchar(50)    default 'unpaid'::bpchar not null,
  fulfillment_status varchar(50)    default 'unfulfilled'::bpchar,
  guid               varchar(100)   default ''::bpchar,
  source_type        varchar(20)    default 'appointment'::bpchar,
  source_id          bigint         default 0,
  line_item_types    integer        default 0                not null,
  version            integer        default 1                not null,
  customer_id        bigint                                  not null,
  tips_amount        numeric(20, 2) default 0                not null,
  tax_amount         numeric(20, 2) default 0                not null,
  discount_amount    numeric(20, 2) default 0                not null,
  extra_fee_amount   numeric(20, 2) default 0                not null,
  sub_total_amount   numeric(20, 2) default 0                not null,
  tips_based_amount  numeric(20, 2) default 0                not null,
  total_amount       numeric(20, 2) default 0                not null,
  paid_amount        numeric(20, 2) default 0                not null,
  remain_amount      numeric(20, 2) default 0                not null,
  refunded_amount    numeric(20, 2) default 0                not null,
  title              text,
  description        text           default ''::bpchar,
  create_by          bigint,
  update_by          bigint,
  create_time        timestamp with time zone                not null,
  update_time        timestamp with time zone                not null
);

-- 备注
comment on table "order" is 'order record table';
comment on column "order".payment_status is 'order payment status';
comment on column "order".fulfillment_status is 'fulfillment status, reserved field';
comment on column "order".guid is 'order guid, for online pay';
comment on column "order".source_type is 'original invoice type, option: appointment/noshow/product/package';
comment on column "order".source_id is 'original grooming invoice''s grooming id, releated to appointment';
comment on column "order".version is 'order version, once update, auto + 1, currently not used';
comment on column "order".sub_total_amount is 'all items total prices, not include tax,tips,discount,extra fee';
comment on column "order".total_amount is 'order should pay-amount, include tax, tips, discount, extra fee';
comment on column "order".title is 'original retail invoice title, currently record all item names';
comment on column "order".description is 'original retail invoice title, currently record all item descriptions';

-- 索引
create index order_index_business_id on "order" (business_id);
create index order_index_customer_id on "order" (customer_id);
create index order_index_guid on "order" (guid);
create index order_index_source_id_type on "order" (source_id, source_type);

alter sequence order_id_seq increment by *********;

-- order item
create table order_line_item
(
  id                 bigserial
    constraint order_line_item_pkey
      primary key,
  business_id        bigint                                   not null,
  staff_id           bigint         default 0                 not null,
  order_id           bigint                                   not null,
  object_id          bigint                                   not null,
  type               varchar(50)    default 'service'::bpchar not null,
  is_deleted         boolean        default false             not null,
  name               varchar(150)                             not null,
  description        text,
  unit_price         numeric(20, 2) default 0                 not null,
  quantity           integer        default 0                 not null,
  purchased_quantity integer        default 0                 not null,
  tips_amount        numeric(20, 2) default 0                 not null,
  tax_amount         numeric(20, 2) default 0                 not null,
  discount_amount    numeric(20, 2) default 0                 not null,
  extra_fee_amount   numeric(20, 2) default 0                 not null,
  sub_total_amount   numeric(20, 2) default 0                 not null,
  total_amount       numeric(20, 2) default 0                 not null,
  create_time        timestamp with time zone                 not null,
  update_time        timestamp with time zone                 not null
);

comment on table order_line_item is 'tax applied on item or order record';
comment on column "order_line_item".object_id is 'item object id, depend on type';
comment on column "order_line_item".type is 'item type, option: service/noshow/product/package';
comment on column "order_line_item".sub_total_amount is 'unit price x (quantity - purchased quantity)';
comment on column "order_line_item".total_amount is 'sub total - discount';

create index order_line_item_index_business_id_order_id on order_line_item (business_id, order_id);

alter sequence order_line_item_id_seq increment by *********;

-- line extra fee
create table order_line_extra_fee
(
  id             bigserial
    constraint order_line_extra_fee_pkey
      primary key,
  business_id    bigint                               not null,
  order_id       bigint                               not null,
  order_item_id  bigint,
  apply_type     varchar(20)  default 'order'::bpchar not null,
  is_deleted     boolean      default false           not null,
  fee_type       varchar(50)                          not null,
  amount         numeric(20, 2),
  name           varchar(150) default ''::bpchar      not null,
  description    text,
  collect_type   varchar(20)  default 'add'::bpchar,
  apply_by       bigint,
  apply_sequence integer      default 0,
  create_time    timestamp with time zone             not null,
  update_time    timestamp with time zone             not null
);

comment on table order_line_extra_fee is 'order extra fee record, currently only convenience fee type';
comment on column "order_line_extra_fee".apply_type is 'apply type, option: all/service/product/package/item/none';
comment on column "order_line_extra_fee".fee_type is 'fee type, option: convenience fee';
comment on column "order_line_extra_fee".collect_type is 'collect type, option: add/subtract';
comment on column "order_line_extra_fee".apply_sequence is 'reserved field';

create index order_line_extra_fee_index_business_id_order_id
  on order_line_extra_fee (business_id, order_id);

alter sequence order_line_extra_fee_id_seq increment by *********;

-- line discount
create table order_line_discount
(
  id              bigserial
    constraint order_line_discount_pkey
      primary key,
  business_id     bigint                              not null,
  order_id        bigint                              not null,
  order_item_id   bigint      default 0               not null,
  apply_type      varchar(20) default 'order'::bpchar not null,
  is_deleted      boolean     default false           not null,
  discount_type   varchar(20) default ''::bpchar      not null,
  discount_amount numeric(20, 2)                      not null,
  discount_rate   numeric(20, 4),
  apply_by        bigint,
  apply_sequence  integer     default 0,
  create_time     timestamp with time zone            not null,
  update_time     timestamp with time zone            not null
);

comment on table order_line_discount is 'discount applied on item or order record';
comment on column "order_line_discount".apply_type is 'apply type, option: all/service/product/package/item/none';
comment on column "order_line_discount".discount_type is 'discount type, option: percentage/amount';
comment on column "order_line_discount".apply_sequence is 'reserved field';

create index order_line_discount_index_business_id_order_id on order_line_discount (business_id, order_id);

alter sequence order_line_discount_id_seq increment by *********;

-- line tax
create table order_line_tax
(
  id             bigserial
    constraint order_line_tax_pkey
      primary key,
  business_id    bigint                                not null,
  order_id       bigint                                not null,
  order_item_id  bigint         default 0,
  apply_type     varchar(20)    default 'item'::bpchar not null,
  is_deleted     boolean        default false          not null,
  tax_id         bigint                                not null,
  tax_rate       numeric(20, 4) default 0.0000         not null,
  tax_amount     numeric(20, 2) default 0              not null,
  apply_by       bigint         default 0              not null,
  apply_sequence integer        default 0,
  create_time    timestamp with time zone              not null,
  update_time    timestamp with time zone              not null
);

comment on table order_line_tax is 'tax applied on item or order record';
comment on column "order_line_tax".apply_type is 'apply type, option: all/service/product/package/item/none';
comment on column "order_line_tax".apply_sequence is 'reserved field';

create index order_line_tax_index_business_id_order_id on order_line_tax (business_id, order_id);

alter sequence order_line_tax_id_seq increment by *********;
