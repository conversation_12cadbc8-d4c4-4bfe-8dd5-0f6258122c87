CREATE TABLE IF NOT EXISTS public."moe_customer_address"
(
    id          bigserial
        constraint moe_customer_address_pk
            primary key,
    account_id  bigint       default 0   not null,
    address1    varchar(255) default ''  not null,
    address2    varchar(255) default ''  not null,
    country     varchar(50)  default ''  not null,
    city        varchar(50)  default ''  not null,
    state       varchar(50)  default ''  not null,
    zipcode     varchar(10)  default ''  not null,
    lat         varchar(50)  default ''  not null,
    lng         varchar(50)  default ''  not null,
    is_primary  smallint     default 0   not null,
    status      smallint     default 1   not null,
    create_time timestamp with time zone not null,
    update_time timestamp with time zone not null
);

CREATE INDEX index_address_account_id ON moe_customer_address (account_id);

CREATE TABLE IF NOT EXISTS public."moe_customer_pet"
(
    id                      bigserial
        constraint moe_customer_pet_pk
            primary key,
    account_id              bigint       default 0   not null,
    pet_name                varchar(50)  default ''  not null,
    pet_type_metadata_id    integer      default 0   not null,
    avatar_path             varchar(255) default ''  not null,
    breed_id                integer      default 0   not null,
    breed_mix               smallint     default 0   not null,
    birthday                varchar(50)  default ''  not null,
    gender                  smallint     default 0   not null,
    hair_length_metadata_id integer      default 0   not null,
    behavior_metadata_id    integer      default 0   not null,
    weight                  varchar(50)  default ''  not null,
    weight_unit_metadata_id smallint     default 1   not null,
    fixed_metadata_id       integer      default 0   not null,
    life_status             smallint     default 1   not null,
    status                  smallint     default 1   not null,
    create_time             timestamp with time zone not null,
    update_time             timestamp with time zone not null
);

CREATE INDEX index_pet_account_id ON moe_customer_pet (account_id);

CREATE TABLE IF NOT EXISTS public."moe_customer_pet_vaccine"
(
    id                  bigserial
        constraint moe_customer_pet_vaccine_pk
            primary key,
    pet_id              bigint        default 0    not null,
    vaccine_metadata_id integer       default 0    not null,
    expiration_date     varchar(20)   default ''   not null,
    document_urls       varchar(1000) default '[]' not null,
    status              smallint      default 1    not null,
    create_time         timestamp with time zone   not null,
    update_time         timestamp with time zone   not null
);

CREATE INDEX index_pet_vaccine_customer_pet_id ON moe_customer_pet_vaccine (pet_id);

CREATE TABLE IF NOT EXISTS public."moe_customer_pet_metadata"
(
    id          serial
        constraint moe_customer_pet_metadata_pk
            primary key,
    category    smallint    default 0    not null,
    name        varchar(50) default ''   not null,
    sort        integer     default 0    not null,
    status      smallint    default 1    not null,
    create_time timestamp with time zone not null,
    update_time timestamp with time zone not null
);

CREATE TABLE IF NOT EXISTS public."moe_customer_pet_breed"
(
    id                   serial
        constraint moe_customer_pet_breed_pk
            primary key,
    pet_type_metadata_id integer     default 0    not null,
    name                 varchar(50) default ''   not null,
    sort                 integer     default 0    not null,
    status               smallint    default 1    not null,
    create_time          timestamp with time zone not null,
    update_time          timestamp with time zone not null
);


insert into public.moe_customer_pet_metadata (id, category, name, sort, status, create_time, update_time)
values (1, 1, 'Dog', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (2, 1, 'Cat', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (3, 1, 'Bird', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (4, 1, 'Rabbit', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (5, 1, 'Guinea Pig', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (6, 1, 'Horse', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (7, 1, 'Rat', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (8, 1, 'Mouse', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (9, 1, 'Hamster', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (10, 1, 'Chinchilla', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (11, 1, 'Other', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (12, 2, 'Short coat', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (13, 2, 'Wired hair', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (14, 2, 'Smooth coat', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (15, 2, 'Medium coat', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (16, 3, 'Friendly', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (17, 3, 'Noisy', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (18, 3, 'Biter', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (19, 3, 'Shy', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (20, 3, 'Fearful', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (21, 3, 'Nervous', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (22, 4, 'Spayed', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (23, 4, 'Neutered', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (24, 5, 'Rabies', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (25, 5, 'Distemper', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (26, 5, 'Parvovirus', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (27, 5, 'Adenovirus', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (28, 5, 'Parainfluenza', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (29, 5, 'Bordetella', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (30, 5, 'Lyme Disease', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (31, 5, 'Leptospirosis', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (32, 5, 'Canine Influenza', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (33, 5, 'Feline Herpesvirus (FHV)', 0, 1, '2022-12-16 16:47:48.852000 +00:00',
        '2022-12-16 16:47:48.852000 +00:00'),
       (34, 5, 'Feline Calicivirus (FCV)', 0, 1, '2022-12-16 16:47:48.852000 +00:00',
        '2022-12-16 16:47:48.852000 +00:00'),
       (35, 5, 'Feline Leukaemia (FeLV)', 0, 1, '2022-12-16 16:47:48.852000 +00:00',
        '2022-12-16 16:47:48.852000 +00:00'),
       (36, 5, 'Hepatitis', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (37, 5, 'Feline infectious enteritis (FIE)', 0, 1, '2022-12-16 16:47:48.852000 +00:00',
        '2022-12-16 16:47:48.852000 +00:00'),
       (38, 5, 'Feline immunodeficiency virus (FIV)', 0, 1, '2022-12-16 16:47:48.852000 +00:00',
        '2022-12-16 16:47:48.852000 +00:00'),
       (39, 5, 'Panleucopenia', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (40, 5, 'Felocell 3', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (41, 5, 'Nobivac Tricat Trio', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (42, 5, 'Felocell 4', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (43, 6, 'Pounds', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00'),
       (44, 6, 'Kilogram', 0, 1, '2022-12-16 16:47:48.852000 +00:00', '2022-12-16 16:47:48.852000 +00:00');

insert into public.moe_customer_pet_breed (id, pet_type_metadata_id, name, sort, status, create_time, update_time)
values (1, 1, 'Affenpinscher', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (2, 1, 'American Bulldog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (3, 1, 'Appenzell Mountain Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (4, 1, 'Afghan Hound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (5, 1, 'American Bullnese', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (6, 1, 'Atlas Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (7, 1, 'Airedale Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (8, 1, 'American Coonhound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (9, 1, 'Aussiedoodle (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (10, 1, 'Akbash Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (11, 1, 'American Eskimo Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (12, 1, 'Akita Inu', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (13, 1, 'American Foxhound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (14, 1, 'Australian Bulldog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (15, 1, 'Alano Espanol', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (16, 1, 'American Hairless Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (17, 1, 'Australian Cattle Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (18, 1, 'Alapaha Blue Blood Bulldog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (19, 1, 'American Indian Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (20, 1, 'Australian Kelpie', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (21, 1, 'Alaskan Husky', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (22, 1, 'American Lo Sze Pugg', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (23, 1, 'Australian Labradoodle', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (24, 1, 'Alaskan Klee Kai', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (25, 1, 'American Mastiff', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (26, 1, 'Australian Shepherd', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (27, 1, 'Alaskan Malamute', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (28, 1, 'American Mastiff Panja', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (29, 1, 'Australian Stumpy Tail Cattle Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (30, 1, 'Alopekis', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (31, 1, 'American Pit Bull Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (32, 1, 'Australian Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (33, 1, 'Alpine Dachsbracke', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (34, 1, 'American Staffordshire Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (35, 1, 'Austrian Shorthaired Pinscher', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (36, 1, 'American Alsatian', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (37, 1, 'American Water Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (38, 1, 'Azawakh Hound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (39, 1, 'American Bull Molosser', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (40, 1, 'American White Shepherd', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (41, 1, 'Banter Bulldogge', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (42, 1, 'Bergamasco Sheepdog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (43, 1, 'Border Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (44, 1, 'Barbet', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (45, 1, 'Berger De Picard', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (46, 1, 'Borzoi', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (47, 1, 'Basenji', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (48, 1, 'Bernese Mountain Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (49, 1, 'Boston Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (50, 1, 'Basset Artesian Normand', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (51, 1, 'Bich Poo (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (52, 1, 'Bouvier des Flandres', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (53, 1, 'Basset Hound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (54, 1, 'Bichon Frise', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (55, 1, 'Boxer', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (56, 1, 'Beagle', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (57, 1, 'Biewer', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (58, 1, 'Boykin Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (59, 1, 'Bearded Collie', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (60, 1, 'Black Mouth Cur', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (61, 1, 'Braque du Bourbonnais', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (62, 1, 'Beauceron', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (63, 1, 'Black Russian Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (64, 1, 'Brazilian Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (65, 1, 'Bedlington Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (66, 1, 'Bloodhound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (67, 1, 'Briard', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (68, 1, 'Belgian Groenendael', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (69, 1, 'Blue Lacy', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (70, 1, 'Brittany Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (71, 1, 'Belgian Laekenois', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (72, 1, 'Bluetick Coonhound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (73, 1, 'Broholmer', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (74, 1, 'Belgian Malinois', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (75, 1, 'Boerboel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (76, 1, 'Brussels Griffon', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (77, 1, 'Belgian Mastiff', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (78, 1, 'Bohemian Shepherd', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (79, 1, 'Bukovina Sheepdog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (80, 1, 'Belgian Tervuren', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (81, 1, 'Bolognese', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (82, 1, 'Bull Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (83, 1, 'Belgrade Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (84, 1, 'Border Collie', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (85, 1, 'Bullmastiff', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (86, 1, 'Cairn Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (87, 1, 'Carpathian Sheepdog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (88, 1, 'Chinook', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (89, 1, 'Canaan Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (90, 1, 'Catahoula Bulldog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (91, 1, 'ChiPoo (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (92, 1, 'Canary Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (93, 1, 'Caucasian Ovtcharka', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (94, 1, 'Chorkie (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (95, 1, 'Cane Corso Italiano', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (96, 1, 'Cavachon (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (97, 1, 'Chow Chow', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (98, 1, 'Canis Panther', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (99, 1, 'Cavalier King Charles Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (100, 1, 'Clumber Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (101, 1, 'Canoe Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (102, 1, 'Cavapoo (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (103, 1, 'Cockalier (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (104, 1, 'Cao da Serra de Aires', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (105, 1, 'Central Asian Ovtcharka', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (106, 1, 'Cocker Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (107, 1, 'Cao de Castro Laboreiro', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (108, 1, 'Cesky Fousek', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (109, 1, 'Collie', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (110, 1, 'Cao de Fila de Sao Miguel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (111, 1, 'Cesky Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (112, 1, 'Coton De Tulear', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (113, 1, 'Cao Dos Mourey', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (114, 1, 'Chesapeake Bay Retriever', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (115, 1, 'Cretan Hound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (116, 1, 'Caravan Hound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (117, 1, 'Chihuahua', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (118, 1, 'Croatian Sheepdog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (119, 1, 'Cardigan Welsh Corgi', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (120, 1, 'Chinese Chongqing Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (121, 1, 'Curly Coated Retriever', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (122, 1, 'Carlin Pinscher', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (123, 1, 'Chinese Crested', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (124, 1, 'Czechoslovakian Wolfdog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (125, 1, 'Carolina Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (126, 1, 'Chinese Imperial Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (127, 1, 'Dachshund', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (128, 1, 'Danish Swedish Farm Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (129, 1, 'Dogue de Bordeaux', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (130, 1, 'Dakotah Shepherd', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (131, 1, 'Deutscher Wachtelhund', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (132, 1, 'Dorset Olde Tyme Bulldogge', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (133, 1, 'Dalmatian', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (134, 1, 'Doberman Pinscher', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (135, 1, 'Drever', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (136, 1, 'Dandie Dinmont Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (137, 1, 'Dogo Argentino', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (138, 1, 'Dutch Shepherd', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (139, 1, 'Danish Broholmer', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (140, 1, 'Dogue Brasileiro', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (141, 1, 'Dutch Smoushond', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (142, 1, 'East European Shepherd', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (143, 1, 'English Foxhound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (144, 1, 'English Toy Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (145, 1, 'English Bulldog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (146, 1, 'English Setter', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (147, 1, 'Entlebucher Sennenhund', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (148, 1, 'English Cocker Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (149, 1, 'English Shepherd', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (150, 1, 'Estonian Hound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (151, 1, 'English Coonhound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (152, 1, 'English Springer Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (153, 1, 'Eurasier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (154, 1, 'Field Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (155, 1, 'Finnish Spitz', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (156, 1, 'French Bulldog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (157, 1, 'Fila Brasileiro', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (158, 1, 'Flat Coated Retriever', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (159, 1, 'French Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (160, 1, 'Finnish Hound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (161, 1, 'French Brittany', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (162, 1, 'Galgo Espanol', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (163, 1, 'German Spitz', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (164, 1, 'Gordon Setter', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (165, 1, 'German Hunt Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (166, 1, 'German Wirehaired Pointer', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (167, 1, 'Great Dane', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (168, 1, 'German Longhaired Pointer', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (169, 1, 'Giant Maso Mastiff', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (170, 1, 'Great Pyrenees', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (171, 1, 'German Pinscher', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (172, 1, 'Giant Schnauzer', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (173, 1, 'Greater Swiss Mountain Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (174, 1, 'German Sheeppoodle', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (175, 1, 'Glen of Imaal Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (176, 1, 'Greenland Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (177, 1, 'German Shepherd', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (178, 1, 'Golden Retriever', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (179, 1, 'Greyhound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (180, 1, 'German Shorthaired Pointer', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (181, 1, 'Goldendoodle (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (182, 1, 'Hairless Khala', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (183, 1, 'Havanese', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (184, 1, 'Hellenikos Poimenikos', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (185, 1, 'Harrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (186, 1, 'Hawaiian Poi Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (187, 1, 'Hokkaido', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (188, 1, 'Havamalt (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (189, 1, 'Hellenikos Ichnilatis', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (190, 1, 'Hovawart', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (191, 1, 'Ibizan Hound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (192, 1, 'Irish Setter', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (193, 1, 'Irish Wolfhound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (194, 1, 'Icelandic Sheepdog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (195, 1, 'Irish Staffordshire Bull Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (196, 1, 'Italian Greyhound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (197, 1, 'Irish Red and White Setter', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (198, 1, 'Irish Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (199, 1, 'Jack Russell Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (200, 1, 'Japanese Spitz', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (201, 1, 'Japanese Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (202, 1, 'Jindo', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (203, 1, 'kangal', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (204, 1, 'Kemmer Feist', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (205, 1, 'Koolie', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (206, 1, 'Karakachan', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (207, 1, 'Kerry Blue Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (208, 1, 'Kunming Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (209, 1, 'Karelian Bear Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (210, 1, 'King Shepherd', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (211, 1, 'Kuvasz', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (212, 1, 'Karst Shepherd', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (213, 1, 'Komondor', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (214, 1, 'Kyi Leo', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (215, 1, 'Keeshond', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (216, 1, 'Kooikerhondje', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (217, 1, 'Labradoodle (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (218, 1, 'Lancashire Heeler', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (219, 1, 'Lithuanian Hound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (220, 1, 'Landseer Newfoundland', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (221, 1, 'Llewellin Setter', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (222, 1, 'Labrador Husky', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (223, 1, 'Large Munsterlander', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (224, 1, 'Louisiana Catahoula Leopard Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (225, 1, 'Labrador Retriever', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (226, 1, 'Larson Lakeview Bulldogge', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (227, 1, 'Lowchen', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (228, 1, 'Lagotto Romagnolo', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (229, 1, 'Leonberger', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (230, 1, 'Lundehund', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (231, 1, 'Lakeland Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (232, 1, 'Lhasa Apso', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (233, 1, 'Maltese', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (234, 1, 'Miniature Australian Shepherd', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (235, 1, 'Moscow Watchdog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (236, 1, 'Malti Poo (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (237, 1, 'Miniature Fox Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (238, 1, 'Mountain Cur', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (239, 1, 'Manchester Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (240, 1, 'Miniature Littlefield Sheepdog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (241, 1, 'Mountain Feist', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (242, 1, 'Maremma Sheepdog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (243, 1, 'Miniature Pinscher', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (244, 1, 'Mountain View Cur', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (245, 1, 'Markiesje', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (246, 1, 'Miniature Poodle', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (247, 1, 'Mucuchies', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (248, 1, 'Mastiff', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (249, 1, 'Miniature Schnauzer', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (250, 1, 'Mudi', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (251, 1, 'Mi Ki', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (252, 1, 'Mioritic Sheepdog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (253, 1, 'Miniature Australian Bulldog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (254, 1, 'Morkie (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (255, 1, 'Native American Indian Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (256, 1, 'New Zealand Huntaway', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (257, 1, 'Norwegian Elkhound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (258, 1, 'Neapolitan Mastiff', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (259, 1, 'Newfoundland', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (260, 1, 'Norwich Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (261, 1, 'Nebolish Mastiff', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (262, 1, 'Norfolk Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (263, 1, 'Nova Scotia Duck Tolling Retriever', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (264, 1, 'New Guinea Singing Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (265, 1, 'Norwegian Buhund', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (266, 1, 'Old English Sheepdog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (267, 1, 'Olde English Bulldogge', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (268, 1, 'Ori Pei (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (269, 1, 'Olde Boston Bulldogge', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (270, 1, 'Olde Victorian Bulldogge', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (271, 1, 'Otterhound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (272, 1, 'Papillon', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (273, 1, 'Pharaoh Hound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (274, 1, 'Portuguese Water Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (275, 1, 'Parson Russell Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (276, 1, 'Plott Hound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (277, 1, 'Prazsky Krysarik', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (278, 1, 'Patterdale Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (279, 1, 'Pointer', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (280, 1, 'Pug', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (281, 1, 'Pekingese', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (282, 1, 'Polski Owczarek Nizinny', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (283, 1, 'Puggle (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (284, 1, 'Pembroke Welsh Corgi', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (285, 1, 'Pomchi (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (286, 1, 'Puli', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (287, 1, 'Perdiguero de Burgos', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (288, 1, 'Pomeranian', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (289, 1, 'Pumi', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (290, 1, 'Peruvian Inca Orchid', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (291, 1, 'Porcelaine', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (292, 1, 'Petit Basset Griffon Vendeen', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (293, 1, 'Portuguese Hound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (294, 1, 'Rafeiro do Alentejo', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (295, 1, 'Redbone Coonhound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (296, 1, 'Rottweiler', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (297, 1, 'Raibs Suns', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (298, 1, 'Rhodesian Ridgeback', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (299, 1, 'Russian Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (300, 1, 'Rat Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (301, 1, 'Roman Rottweiler', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (302, 1, 'Russian Toy Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (303, 1, 'Saarlooswolfhond', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (304, 1, 'Shiba Inu', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (305, 1, 'Smooth Fox Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (306, 1, 'Saint Berdoodle (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (307, 1, 'Shichon (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (308, 1, 'Soft Coated Wheaten Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (309, 1, 'Saint Bernard', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (310, 1, 'Shih Tzu', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (311, 1, 'South Russian Ovtcharka', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (312, 1, 'Saluki', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (313, 1, 'Shikoku', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (314, 1, 'Spanish Mastiff', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (315, 1, 'Samoyed', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (316, 1, 'Shiloh Shepherd', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (317, 1, 'Spanish Water Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (318, 1, 'Sarplaninac', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (319, 1, 'Shiranian (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (320, 1, 'Spinone Italiano', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (321, 1, 'Schipperke', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (322, 1, 'Shorkie (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (323, 1, 'Stabyhoun', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (324, 1, 'Schnoodle (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (325, 1, 'Siberian Husky', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (326, 1, 'Staffordshire Bull Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (327, 1, 'Scottish Deerhound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (328, 1, 'Silken Windhound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (329, 1, 'Standard Poodle', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (330, 1, 'Scottish Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (331, 1, 'Silky Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (332, 1, 'Standard Schnauzer', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (333, 1, 'Sealyham Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (334, 1, 'Skye Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (335, 1, 'Sussex Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (336, 1, 'Shar Pei', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (337, 1, 'Sloughi', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (338, 1, 'Swedish Vallhund', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (339, 1, 'Shetland Sheepdog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (340, 1, 'Slovensky Cuvac', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (341, 1, 'Tamaskan Dog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (342, 1, 'Tibetan Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (343, 1, 'Tosa Inu', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (344, 1, 'Tenterfield Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (345, 1, 'Tibetan Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (346, 1, 'Toy Fox Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (347, 1, 'Thai Ridgeback', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (348, 1, 'Titan Bull Dogge', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (349, 1, 'Toy Poodle', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (350, 1, 'Tibetan Mastiff', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (351, 1, 'Tornjak', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (352, 1, 'Treeing Walker Coonhound', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (353, 1, 'Valley Bulldog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (354, 1, 'Victorian Bulldog', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (355, 1, 'Vizsla', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (356, 1, 'Weimaraner', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (357, 1, 'West Highland White Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (358, 1, 'Wirehaired Pointing Griffon', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (359, 1, 'Weimardoodle (h)', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (360, 1, 'Wetterhoun', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (361, 1, 'Wirehaired Vizsla', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (362, 1, 'Welsh Springer Spaniel', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (363, 1, 'Whippet', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (364, 1, 'Welsh Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (365, 1, 'Wirehaired Fox Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (366, 1, 'Xoloitzcuintli', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (367, 1, 'Yorkshire Terrier', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (368, 2, 'Abyssinian', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (369, 2, 'American Bobtail', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (370, 2, 'American Curl', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (371, 2, 'American Shorthair', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (372, 2, 'American Wirehair', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (373, 2, 'Balinese', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (374, 2, 'Bengal', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (375, 2, 'Birman', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (376, 2, 'Bombay', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (377, 2, 'British Shorthair', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (378, 2, 'Burmese', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (379, 2, 'Burmilla', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (380, 2, 'Chartreux', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (381, 2, 'Colorpoint Shorthair', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (382, 2, 'Cornish Rex', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (383, 2, 'Devon Rex', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (384, 2, 'Egyptian Mau', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (385, 2, 'European Burmese', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (386, 2, 'Exotic', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (387, 2, 'Havana Brown', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (388, 2, 'Japanese Bobtail', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (389, 2, 'Korat', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (390, 2, 'LaPerm', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (391, 2, 'Maine Coon', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (392, 2, 'Manx', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (393, 2, 'Norwegian Forest Cat', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (394, 2, 'Ocicat', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (395, 2, 'Oriental', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (396, 2, 'Persian', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (397, 2, 'Ragamuffin', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (398, 2, 'Ragdoll', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (399, 2, 'Russian Blue', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (400, 2, 'Scottish Fold', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (401, 2, 'Selkirk Rex', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (402, 2, 'Siamese', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (403, 2, 'Siberian', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (404, 2, 'Singapura', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (405, 2, 'Somali', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (406, 2, 'Sphynx', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (407, 2, 'Tonkinese', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (408, 2, 'Turkish Angora', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (409, 2, 'Turkish Van', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (410, 2, 'Snowshoe', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (411, 2, 'Himalayan', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (412, 2, 'Angora', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (413, 2, 'Pixiebob', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (414, 2, 'Short hair domestic', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (415, 2, 'Medium hair domestic', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
        (416, 2, 'Long hair domestic', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
        (417, 1, 'VVVV', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
        (418, 1, 'xxx', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
        (419, 1, 'dvv', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
        (420, 1, 'Mini poodle mix', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
        (421, 1, 'Lab Shepherd', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
        (422, 1, 'Lab Shepherd mix', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
        (423, 1, 'Yokie/Poodle mix', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
        (424, 1, 'Cockapoo', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
        (425, 1, 'Pitbull', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (426, 1, 'Portuguese podengo Mix', 0, 1, '2022-12-16 16:29:40.364000 +00:00',
        '2022-12-16 16:29:40.364000 +00:00'),
       (427, 3, 'Bird', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (428, 4, 'Rabbit', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (429, 5, 'Geuinea pig', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (430, 6, 'Horse', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (431, 7, 'Rat', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (432, 8, 'Mouse', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (433, 9, 'Hamster', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (434, 10, 'Chinchilla', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00'),
       (435, 11, 'Other', 0, 1, '2022-12-16 16:29:40.364000 +00:00', '2022-12-16 16:29:40.364000 +00:00');


SELECT setval('moe_customer_pet_breed_id_seq', (SELECT MAX(id) FROM moe_customer_pet_breed) + 1);

SELECT setval('moe_customer_pet_metadata_id_seq', (SELECT MAX(id) FROM moe_customer_pet_metadata) + 1);
