-- tip split记录表
create table order_tip_split_record
(
  id                bigserial
    constraint order_tip_split_pkey
      primary key,
  business_id       bigint                                 not null,
  order_id          bigint                                 not null,
  split_method      smallint                 default 1     not null,
  customized_type   smallint                 default 1     not null,
  customized_config text,
  apply_by          bigint                   default 0     not null,
  is_deleted        boolean                  default false not null,
  create_time       timestamp with time zone default CURRENT_TIMESTAMP,
  update_time       timestamp with time zone default CURRENT_TIMESTAMP
);

comment on table order_tip_split_record is 'order tip split record';
comment on column order_tip_split_record.split_method is 'tip split method: 1-by service, 2-by equally, 3-customized';
comment on column order_tip_split_record.customized_type is 'customized tip split type: 1-by amount, 2-by percentage';
comment on column order_tip_split_record.customized_config is 'customized tip config json string';
comment on column order_tip_split_record.apply_by is 'insert/update staff id';

create index tip_split_index_business_id_order_id
  on order_tip_split_record (business_id, order_id);

-- trigger设置，自动更新update time字段
create function update_modified_column() returns trigger
  language plpgsql
as
$$
BEGIN
  NEW.update_time = now();
  RETURN NEW;
END;
$$;

create trigger auto_update_order_tip_split_record_update_time
  after update
  on order_tip_split_record
  for each row
execute procedure update_modified_column();
