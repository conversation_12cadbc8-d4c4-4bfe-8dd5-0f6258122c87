CREATE TABLE moego_order.public.message_delivery (
    id BIGSERIAL NOT NULL,
    create_time TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC') NOT NULL,
    update_time TIMESTAMP DEFAULT (NOW() AT TIME ZONE 'UTC') NOT NULL,
    message_type VARCHAR(128) NOT NULL DEFAULT '',
    reference_id VARCHAR(256) NOT NULL DEFAULT '',
    payload TEXT NOT NULL DEFAULT '',
    status VARCHAR(32) NOT NULL DEFAULT '',
    retry_count INT DEFAULT 0,
    last_attempt_time TIMESTAMP,

    CONSTRAINT message_delivery_pk PRIMARY KEY (id)
);

CREATE UNIQUE INDEX uniq_type_reference_id ON public.message_delivery (message_type,reference_id);
CREATE INDEX idx_message_delivery_status ON public.message_delivery (status);

COMMENT ON TABLE message_delivery IS 'order tips split detail';
COMMENT ON COLUMN message_delivery.id is 'primary key';
COMMENT ON COLUMN message_delivery.create_time is 'Creation time';
COMMENT ON COLUMN message_delivery.update_time is 'Last update time';
COMMENT ON COLUMN message_delivery.message_type is 'Type of message (e.g., order, refund, etc.)';
COMMENT ON COLUMN message_delivery.reference_id is 'Foreign key to the relevant entity (e.g., order ID, refund ID)';
COMMENT ON COLUMN message_delivery.payload is 'Actual message content stored as JSON';
COMMENT ON COLUMN message_delivery.status is 'Status of the message (PENDING, SENT, FAILED, etc.)';
COMMENT ON COLUMN message_delivery.retry_count is 'Number of retry attempts';
COMMENT ON COLUMN message_delivery.last_attempt_time is 'Timestamp of the last attempt to send';

CREATE TRIGGER update_modified_column
    BEFORE UPDATE
    ON public.message_delivery
    FOR EACH ROW
EXECUTE PROCEDURE update_modified_column();

-- optimize for message_delivery task
-- message_delivery add column delete_time
alter table message_delivery add column delete_time TIMESTAMP DEFAULT NULL;
-- message_delivery add index on delete_time

-- 创建新索引
create unique index uniq_type_refId_none_delete on message_delivery (message_type, reference_id) where delete_time is null;
COMMENT ON INDEX uniq_type_refId_none_delete IS 'unique index for message_type and reference id for all none delete records';

-- 删除旧索引
drop index if exists uniq_type_reference_id;