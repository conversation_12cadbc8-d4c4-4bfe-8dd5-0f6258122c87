init_hook:
	git config core.hooksPath ./hooks

install:
	@echo "Installing dependencies..."
	curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/HEAD/install.sh | sh -s -- -b $$(go env GOPATH)/bin v2.4.0
	@echo "Dependencies installed successfully!"

wire:
	@echo "Generating wire code..."
	GOWORK=off go run -mod=mod github.com/google/wire/cmd/wire gen ./cmd

init: init_hook install
	@echo "Init done!"

.PHONY: init