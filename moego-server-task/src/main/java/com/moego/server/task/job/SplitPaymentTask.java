package com.moego.server.task.job;

import com.google.protobuf.Empty;
import com.moego.idl.service.split_payment.v1.SplitPaymentServiceGrpc;
import com.moego.server.payment.client.IPaymentSplitPaymentClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SplitPaymentTask {

    @Autowired
    private SplitPaymentServiceGrpc.SplitPaymentServiceBlockingStub splitPaymentServiceBlockingStub;

    @Autowired
    private IPaymentSplitPaymentClient paymentSplitPaymentClient;

    /**
     * 重试分账同步，每分钟调用一次
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void retryInvokeSplitPayment() {
        log.info("retryInvokeSplitPayment task begin");
        paymentSplitPaymentClient.retryStripeSplitPaymentTask();
        log.info("retryInvokeSplitPayment task invoke success");
    }

    /**
     * 重试分账退款，每分钟调用一次
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void retrySplitRefund() {
        log.info("retrySplitRefund task begin");
        paymentSplitPaymentClient.retrySplitPaymentRefundTask();
        log.info("retrySplitRefund task invoke success");
    }

    /**
     * 重试分账，每分钟调用一次
     */
    @Scheduled(cron = "0 0/1 * * * ?")
    public void retrySplitPayment() {
        log.info("retrySplitPayment task begin");
        var resp = splitPaymentServiceBlockingStub.retrySplitPaymentForTask(
                Empty.newBuilder().build());
        log.info("retrySplitPayment task invoke success,{}", resp);
    }

    /**
     * 打批charge重试，这里是重试调用三方进行charge
     * 每5分钟执行一次
     */
    @Scheduled(cron = "0 0/5 * * * ?")
    public void retryBatchCharge() {
        log.info("retryBatchCharge task begin");
        var resp = splitPaymentServiceBlockingStub.retryBatchChargeForTask(
                Empty.newBuilder().build());
        log.info("retryBatchCharge task invoke success,{}", resp);
    }

    /**
     * 打批任务触发
     * LA时间每天22点执行，在商户提现之前
     */
    @Scheduled(cron = "0 0 22 * * ?", zone = "America/Los_Angeles")
    public void batchChargeTask() {
        log.info("batchChargeTask begin");
        var resp = splitPaymentServiceBlockingStub.batchChargeForTask(
                Empty.newBuilder().build());
        log.info("batchChargeTask task invoke success,{}", resp);
    }
}
