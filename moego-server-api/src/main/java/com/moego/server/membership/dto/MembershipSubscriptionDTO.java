package com.moego.server.membership.dto;

public record MembershipSubscriptionDTO(MembershipDTO membership, SubscriptionDTO subscription) {
    public record MembershipDTO(
            Long id,
            Long companyId,
            Long internalProductId,
            String name,
            String description,
            Integer status, // 0: unspecifed, 1: active, 2: inactive
            Long priceId,
            Double price,
            Long taxId,
            Integer billingCycle, // 0: unspecified, 1: monthly, 2: annually
            String policy,
            Integer revision,
            BillingCyclePeriod
                    billingCyclePeriod, // billingCyclePeriod as a nested structure // 0: unspecified, 1: day, 2: week
            // 3: fortnight 4: month 5: quarter 6:half-year
            // 7:year
            Integer billingCycleDayOfWeek, // 0: unspecified, 1: Sunday, 2: Monday, ..., 7: Saturday
            Long createdAt,
            Long updatedAt,
            Long deletedAt,
            Money totalPrice,
            Money totalTax) {
        public record Money(String currencyCode, Long units, Long nanos) {}

        public record BillingCyclePeriod(Integer period, Integer value) {}
    }

    public record SubscriptionDTO(
            Long id,
            Long internalSubscriptionId,
            Long latestOrderId,
            String latestCardOnFileId,
            Long companyId,
            Long customerId,
            Long businessId,
            Long membershipId,
            Double price,
            Integer membershipRevision,
            Integer billingCycle, // 0: unspecified, 1: monthly, 2: annually
            Interval validityPeriod,
            Long nextBillingDate,
            Long expiresAt,
            Boolean cancelAtPeriodEnd,
            Integer status, // 0: unspecified, 2 - pending, 3 - active, 4 - cancelled
            Integer subStatus, // proto:models/subscription.Status
            Long sellLinkId,
            Long createdAt,
            Long updatedAt,
            Long deletedAt) {
        public record Interval(Long startTime, Long endTime) {}
    }
}
