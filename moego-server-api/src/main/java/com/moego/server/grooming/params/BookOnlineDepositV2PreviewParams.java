package com.moego.server.grooming.params;

import jakarta.annotation.Nullable;
import java.util.List;
import lombok.Data;

/**
 * 记录创建 customer, pet, appointment 等实体之前的相关信息。
 */
@Data
public class BookOnlineDepositV2PreviewParams {
    /**
     * 前端传过来的 customerId。
     */
    @Nullable
    private Long customerIdFromRequest;

    /**
     * 是否是 new visitor。Lead 也会认为是 new visitor。
     */
    private boolean isNewVisitor;

    /**
     * 和请求的 petData 一一对应，记录每个 pet 是否是新宠物。
     */
    private List<Boolean> isNewPetList;
}
