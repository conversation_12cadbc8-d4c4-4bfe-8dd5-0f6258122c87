package com.moego.server.grooming.params;

import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class UpdateServiceByCareTypeParams {

    private Long businessId;

    private Long companyId;

    private List<Long> serviceIds;

    private List<Long> deselectIds;

    private Boolean isAllAvailable;

    private ServiceType serviceType;

    private ServiceItemType serviceItemType;
}
