package com.moego.server.grooming.params;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AppointmentBlockRepeatParams {

    @NotNull
    private String appointmentTime;

    @NotNull
    private Integer startTime;

    @NotNull
    private Integer endTime;

    @NotNull
    private Integer staffId;

    private Integer repeatId;

    private Integer tokenBusinessId;
    private Integer tokenStaffId;
    private Long tokenCompanyId;

    @NotNull
    private String desc;

    @NotNull
    private String colorCode;

    private Integer source;
}
