package com.moego.server.grooming.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.moego.common.enums.RepeatModifyTypeEnum;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import java.util.List;
import lombok.Data;

@Data
public class AppointmentRepeatModifyParams {

    /**
     * @see RepeatModifyTypeEnum
     */
    @NotNull
    @Min(1)
    @Max(3)
    private Integer repeatType;

    @NotNull
    @Positive
    private Integer id;

    @Pattern(regexp = "^(\\d{4}-\\d{2}-\\d{2})$", message = "Invalid date format, valid example: 2022-02-08")
    private String appointmentDateString;

    @PositiveOrZero
    private Integer appointmentStartTime;

    @Valid
    private List<PetDetailParams> serviceList;

    private Boolean startAtSameTime;

    private Integer businessId;

    private Integer staffId;

    private PreAuthParams preAuthParams;
    /**
     * gc sync 是否要延时
     */
    @JsonIgnore
    private Boolean isGcSyncDelay;

    private String endDate;
}
