package com.moego.server.grooming.params;

import com.moego.common.utils.Pagination;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@Builder(toBuilder = true)
public class AdminQueryAppointmentParams {
    private Integer businessId;
    private Integer id;
    private String appointmentDate;

    @NotNull
    @Valid
    private Pagination pagination;
}
