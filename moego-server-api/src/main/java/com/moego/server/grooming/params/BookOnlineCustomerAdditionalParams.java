package com.moego.server.grooming.params;

import lombok.Data;

@Data
public class BookOnlineCustomerAdditionalParams {

    /**
     * referral source
     */
    private Integer referralSourceId;

    /**
     * referral source
     */
    private String referralSourceDesc;

    /**
     * Preferred groomer
     */
    private Integer preferredGroomerId;

    /**
     * Preferred frequency
     */
    private Integer preferredFrequencyDay;

    /**
     * Preferred frequency 0-by days 1-by weeks
     */
    private Byte preferredFrequencyType;

    /**
     * Preferred day of the week
     */
    private Integer[] preferredDay;

    /**
     * Preferred time of the day
     */
    private Integer[] preferredTime;
}
