package com.moego.server.grooming.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.util.List;
import lombok.Data;

@Data
public class SaveRepeatAppointmentListParams {

    public static final byte SAVED_TYPE_REPEAT_RULES = 1;
    public static final byte SAVED_TYPE_SELECTED_DATES = 2;

    @JsonIgnore
    private Integer companyId;

    @JsonIgnore
    private Integer businessId;

    @JsonIgnore
    private Integer staffId;

    private Integer appointmentId;

    @Schema(description = "预约保存类型: 1-repeat rules, 2-selected dates")
    @NotNull
    @Max(2)
    @Min(1)
    private Byte saveType;

    @Schema(description = "repeat rules id, 当 type = 1 时必填")
    @Positive
    private Integer repeatId;

    @NotEmpty
    private List<@Valid SaveRepeatAppointmentParams> appointmentList;
}
