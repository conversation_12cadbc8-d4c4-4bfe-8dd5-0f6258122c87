package main

import (
	fulfillmentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/fulfillment/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/cmd"
	"github.com/MoeGolibrary/moego-svc-fulfillment/resource"
	"log"
	"os"
	"os/signal"
	"syscall"

	"github.com/MoeGolibrary/go-lib/server"
	"github.com/MoeGolibrary/go-lib/zlog"

	"github.com/MoeGolibrary/moego-svc-fulfillment/config"
)

func main() {
	config.Init()

	resource.InitTimezone()
	// 初始化日志
	zlog.InitLogger(zlog.NewConfig())
	// 初始化数据库
	resource.InitDB(config.GetConfig().DB)
	// 初始化 grpc client
	handlers := cmd.Init()
	s := server.NewDefaultServer()
	s.RegisterService(&fulfillmentsvcpb.FulfillmentService_ServiceDesc, handlers.FulfillmentHandler)
	s.RegisterService(&fulfillmentsvcpb.GroupClassDetailService_ServiceDesc, handlers.GroupClassDetailHandler)
	s.RegisterService(&fulfillmentsvcpb.GroupClassAttendanceService_ServiceDesc, handlers.GroupClassAttendanceHandler)
	// 启动消费者
	handlers.Consumer.Start()
	defer handlers.Consumer.Stop()

	s.Start()
	log.Println("server started")

	c := make(chan os.Signal, 1)
	signal.Notify(c, os.Interrupt, syscall.SIGTERM)
	<-c

	s.Stop()
	log.Println("server stopped")
}
