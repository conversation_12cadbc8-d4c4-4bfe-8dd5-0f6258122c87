package com.moego.server.payment.service.strategy.splitpayment;

import com.google.type.Money;
import com.moego.api.thirdparty.IPaymentSlackTriggersClient;
import com.moego.common.distributed.LockManager;
import com.moego.common.enums.SubscriptionConst;
import com.moego.common.enums.payment.SplitSyncRecordStatusEnum;
import com.moego.common.enums.payment.SplitSyncRecordTypeEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.split_payment.v1.AccountType;
import com.moego.idl.models.split_payment.v1.SplitDetailModel;
import com.moego.idl.models.split_payment.v1.SplitType;
import com.moego.idl.service.split_payment.v1.ReverseSplitPaymentRequest;
import com.moego.idl.service.split_payment.v1.SplitPaymentRequest;
import com.moego.idl.service.split_payment.v1.SplitPaymentServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.payment.mapper.MmStripeDisputeMapper;
import com.moego.server.payment.mapper.MoePayDetailMapper;
import com.moego.server.payment.mapper.PaymentMapper;
import com.moego.server.payment.mapper.RefundMapper;
import com.moego.server.payment.mapper.SplitSyncRecordMapper;
import com.moego.server.payment.mapperbean.MoePayDetail;
import com.moego.server.payment.mapperbean.Payment;
import com.moego.server.payment.mapperbean.SplitSyncRecord;
import com.moego.server.payment.params.SlackPostBody;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import javax.annotation.Resource;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;

/**
 * <AUTHOR>
 * @since 2024/7/8
 */
@Setter
@Slf4j
public abstract class AbstractSplitStrategy implements SplitStrategy {

    @Resource
    protected SplitPaymentServiceGrpc.SplitPaymentServiceBlockingStub splitPaymentServiceStub;

    @Resource
    protected PaymentMapper paymentMapper;

    @Resource
    protected MoePayDetailMapper payDetailMapper;

    @Resource
    protected SplitSyncRecordMapper splitSyncRecordMapper;

    @Resource
    private LockManager lockManager;

    @Resource
    protected RefundMapper refundMapper;

    @Resource
    protected MmStripeDisputeMapper mmStripeDisputeMapper;

    @Resource
    protected IPaymentSlackTriggersClient slackTriggersClient;

    @Value("${spring.profiles.active:local}")
    private String env;

    // booking fee
    protected static final String DESCRIPTION_BOOKING_FEE = "booking fee";
    // processing fee
    protected static final String DESCRIPTION_PROCESSING_FEE = "processing fee";

    protected abstract Boolean doProcess(Integer entity, SplitSyncRecordTypeEnum type);

    /**
     * false 代表两种情况
     * 一种是传进来的entity id(payment/refund)找不到sync record, 或者是entity id找不到对应的entity(payment/refund)
     * 一种是rpc 调用分账服务处理分账失败
     * 可以总结为 分账失败
     *
     * @param entityId 分账实体id
     * @return 分账是否成功
     */
    @Override
    public boolean split(Integer entityId, SplitSyncRecordTypeEnum type) {
        var record = splitSyncRecordMapper.selectOneByEntityIdAndType(entityId, type.name());
        if (Objects.isNull(record)) return false;
        preProcess(entityId, type);
        return doProcess(entityId, type);
    }

    protected void preProcess(Integer entityId, SplitSyncRecordTypeEnum type) {
        log.info("process split start, entity id = {}, type = {}", entityId, type);
    }

    @Override
    public void retrySplitPayment() {
        var splitSyncRecords = splitSyncRecordMapper.selectListByStatus(SplitSyncRecordStatusEnum.INIT.name());
        // 并发处理
        splitSyncRecords.stream()
                .filter(record -> getVendor().name().equals(record.getSyncVendor()))
                .forEach(record -> ThreadPool.execute(() -> this.processRecord(record)));
    }

    /**
     * retry split payment 的具体逻辑
     *
     * @param record sync split payment 的记录
     */
    private void processRecord(SplitSyncRecord record) {
        String resourceKey = lockManager.getResourceKey(LockManager.PAYMENT_SPLIT_PAYMENT, record.getId());
        var value = CommonUtil.getUuid();
        try {
            // 锁有效期60s
            if (lockManager.lock(resourceKey, value)) {
                doProcess(record.getSplitEntityId().intValue(), SplitSyncRecordTypeEnum.valueOf(record.getType()));
            }
        } catch (Exception e) {
            log.error("retry split payment error, record: {}", record.getId(), e);
        } finally {
            lockManager.unlock(resourceKey, value);
        }
    }

    protected void sendSlackAlert(String errMsg, SplitSyncRecord record) {
        StringBuilder message = new StringBuilder();
        LocalDateTime now = LocalDateTime.now();
        message.append(SubscriptionConst.TIME)
                .append(now)
                .append(System.lineSeparator())
                .append(SubscriptionConst.ENV)
                .append(env)
                .append(System.lineSeparator())
                .append(System.lineSeparator())
                .append("😅😅😅Split Payment error: ")
                .append(System.lineSeparator())
                .append("task record id:  ")
                .append(record.getId())
                .append(System.lineSeparator())
                .append("error msg: ")
                .append(errMsg)
                .append(System.lineSeparator())
                .append(System.lineSeparator())
                .append("details: ")
                .append(JsonUtil.toJson(record))
                .append(System.lineSeparator());
        slackTriggersClient.sendSplitPaymentAlertMessage(
                SlackPostBody.builder().text(message.toString()).build());
    }

    /**
     * rpc 调用split payment 的split payment 后需要扭转状态
     *
     * @param status     根据rpc结果设置record status
     * @param syncRecord 变更的record
     */
    protected void handlerProcess(SplitSyncRecordStatusEnum status, SplitSyncRecord syncRecord) {
        // 分账受理失败记录重试次数, 并输出日志
        if (SplitSyncRecordStatusEnum.INIT.equals(status)) {
            Integer retryTimes = syncRecord.getRetryTimes();
            if (retryTimes > 3) {
                sendSlackAlert(String.format("split payment failed, retry times: %s", retryTimes), syncRecord);
            }
            syncRecord.setRetryTimes(retryTimes + 1);
            log.error("Error in accepting split payment, sync record id: {}", syncRecord.getId());
        }
        syncRecord.setStatus(status.name());
        splitSyncRecordMapper.updateByPrimaryKeySelective(syncRecord);
    }

    /**
     * 处理有异常的sync record, 比如payment 有sync record 但没有payment
     * @param record 需要取消的sync record
     */
    protected void cancelSyncRecord(SplitSyncRecord record) {
        record.setStatus(SplitSyncRecordStatusEnum.CANCEL.name());
        splitSyncRecordMapper.updateByPrimaryKeySelective(record);
        log.info("cancel split sync record, id:{}", record.getId());
    }

    /**
     * rpc 调用, 正向资金流
     *
     * @return rpc 调用结果
     */
    protected Boolean invokeForward(SplitPaymentRequest splitPaymentRequest) {
        try {
            var resp = splitPaymentServiceStub.splitPayment(splitPaymentRequest);
            var result = resp.getResult();
            if (!result) {
                log.error(
                        "forward split payment result false, error code:{}, error msg: {}",
                        resp.getResultCode(),
                        resp.getResultMsg());
            }
            return result;
        } catch (Exception e) {
            log.error("invoke forward split payment error", e);
            throw ExceptionUtil.bizException(Code.CODE_SPLIT_PAYMENT_ACCEPTANCE_FAILED, e.getMessage());
        }
    }

    /**
     * rpc 调用处理逆向资金流
     * @param reverseSplitPayment 逆向资金请求
     * @return 处理成功与否
     */
    protected Boolean invokeReverse(ReverseSplitPaymentRequest reverseSplitPayment) {
        try {
            var resp = splitPaymentServiceStub.reverseSplitPayment(reverseSplitPayment);
            var result = resp.getResult();
            if (!result) {
                log.error(
                        "reverse split payment result false, error code:{}, error msg: {}",
                        resp.getResultCode(),
                        resp.getResultMsg());
            }
            return result;
        } catch (Exception e) {
            log.error("invoke reverse split payment error", e);
            throw ExceptionUtil.bizException(Code.CODE_SPLIT_PAYMENT_ACCEPTANCE_FAILED, e.getMessage());
        }
    }

    protected Boolean invokeSyncReverse(ReverseSplitPaymentRequest reverseSplitPaymentRequest) {
        try {
            var resp = splitPaymentServiceStub.syncReverseSplitPayment(reverseSplitPaymentRequest);
            var result = resp.getResult();
            if (!result) {
                log.error(
                        "sync reverse split payment result false, error code:{}, error msg: {}",
                        resp.getResultCode(),
                        resp.getResultMsg());
            }
            return result;
        } catch (Exception e) {
            log.error("invoke sync reverse split payment error", e);
            throw ExceptionUtil.bizException(Code.CODE_SPLIT_PAYMENT_ACCEPTANCE_FAILED, e.getMessage());
        }
    }

    /**
     * 通过Payment 查询payment details
     * 并通过payment details 构造 platform split list
     * 主要目的是从payment details 找到哪部分属于platform
     *
     * @param payment payment 实体
     * @return platform split detail
     */
    protected List<SplitDetailModel> buildPlatformSplitList(Payment payment, String currency) {
        Integer paymentId = payment.getId();
        var detail = queryPayDetail(paymentId);
        List<SplitDetailModel> result = new ArrayList<>();

        // booking fee
        Optional.ofNullable(createDetailModel(
                        detail.getBookingFee(), currency, SplitType.BOOKING_FEE, DESCRIPTION_BOOKING_FEE))
                .ifPresent(result::add);

        if (BigDecimal.ZERO.equals(payment.getProcessingFee())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_SPLIT_PAYMENT_ACCEPTANCE_FAILED, "the payment processing fee is zero!");
        }
        // processing fee
        Optional.ofNullable(createDetailModel(
                        payment.getProcessingFee(), currency, SplitType.PROCESSING_FEE, DESCRIPTION_PROCESSING_FEE))
                .ifPresent(result::add);
        return result;
    }

    /**
     * 构造platform split detail,
     * Split Detail Model 表示了分账给platform时的具体金额和类型
     *
     * @param fee      分账金额
     * @param currency 货币单位
     * @return Split Detail Model
     */
    protected SplitDetailModel createDetailModel(BigDecimal fee, String currency, SplitType type, String description) {
        if (fee.compareTo(BigDecimal.ZERO) <= 0) return null;
        return SplitDetailModel.newBuilder()
                .setSplitType(type)
                .setAmount(buildMoney(fee, currency))
                .setAccountType(AccountType.PLATFORM)
                .setDescription(description)
                .build();
    }

    protected SplitSyncRecord querySyncRecordByEntity(Integer entity, SplitSyncRecordTypeEnum type) {
        return splitSyncRecordMapper.selectOneByEntityIdAndType(entity, type.name());
    }

    protected MoePayDetail queryPayDetail(Integer paymentId) {
        return payDetailMapper.getByPaymentIs(List.of(paymentId)).stream()
                .findFirst()
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "payment detail not found!"));
    }

    protected Money buildMoney(BigDecimal amount, String currency) {
        long units = amount.longValue();
        int nanos = amount.subtract(new BigDecimal(units))
                // money 类 小数部分会默认偏移9位(即单位为纳米), 所以这里要偏移9位对齐
                .movePointRight(9)
                .intValue();
        return Money.newBuilder()
                .setCurrencyCode(currency)
                .setNanos(nanos)
                .setUnits(units)
                .build();
    }
}
