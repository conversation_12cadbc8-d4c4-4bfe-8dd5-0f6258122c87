package com.moego.server.payment.server;

import com.alibaba.fastjson.JSON;
import com.moego.idl.models.split_payment.v1.Vendor;
import com.moego.server.payment.api.IPaymentSplitPaymentService;
import com.moego.server.payment.api.IPaymentSplitPaymentServiceBase;
import com.moego.server.payment.service.RefundService;
import com.moego.server.payment.service.SplitPaymentService;
import java.util.HashMap;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @since 2024/7/18
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class SplitPaymentServer extends IPaymentSplitPaymentServiceBase {

    private final SplitPaymentService splitPaymentService;
    private final RefundService refundService;

    @Override
    public void retryStripeSplitPaymentTask() {
        log.info("retry stripe split payment");
        splitPaymentService.retrySplitPayment(Vendor.STRIPE);
    }

    /**
     * @see IPaymentSplitPaymentService#retrySplitPaymentRefundTask()
     */
    @Override
    public void retrySplitPaymentRefundTask() {
        log.info("retry split refund");
        refundService.retrySplitRefund();
    }

    @Override
    public void setRatio(Integer ratio) {
        splitPaymentService.setTrafficRation(ratio);
    }

    @Override
    public void addTrafficWhiteList(Integer businessId) {
        splitPaymentService.addWhiteRationList(businessId);
    }

    @Override
    public void delTrafficWhiteList(Integer businessId) {
        splitPaymentService.delWhiteRationList(businessId);
    }

    @Override
    public void addTrafficBlackList(Integer businessId) {
        splitPaymentService.addBlackRationList(businessId);
    }

    @Override
    public void delTrafficBlackList(Integer businessId) {
        splitPaymentService.delBlackRationList(businessId);
    }

    @Override
    public String getAllSplitPaymentTrafficSwitchConfig() {
        // 获取所有配置信息
        Map<String, Object> map = new HashMap<>();
        map.put("blacklist", splitPaymentService.getBlackList());
        map.put("whitelist", splitPaymentService.getWhiteList());
        map.put("ratio", splitPaymentService.getTrafficRatioForWeb());
        map.put("loanSwitch", splitPaymentService.getLoanSwitch());
        return JSON.toJSONString(map);
    }

    @Override
    public void setLoanSwitch(String loanSwitch) {
        splitPaymentService.setLoanSwitch(loanSwitch);
    }
}
