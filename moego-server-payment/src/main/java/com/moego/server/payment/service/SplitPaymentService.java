package com.moego.server.payment.service;

import com.moego.common.enums.payment.SplitSyncRecordStatusEnum;
import com.moego.common.enums.payment.SplitSyncRecordSyncModeEnum;
import com.moego.common.enums.payment.SplitSyncRecordTypeEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.RedisUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.split_payment.v1.Vendor;
import com.moego.idl.service.split_payment.v1.SplitPaymentServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.payment.mapper.MmStripeAccountMapper;
import com.moego.server.payment.mapper.RefundMapper;
import com.moego.server.payment.mapper.SplitSyncRecordMapper;
import com.moego.server.payment.mapperbean.MmStripeAccount;
import com.moego.server.payment.mapperbean.SplitSyncRecord;
import com.moego.server.payment.service.strategy.splitpayment.SplitStrategy;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/7/8
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class SplitPaymentService {

    private final List<SplitStrategy> splitStrategyList;
    private final RedisUtil redisUtil;
    private final SplitSyncRecordMapper splitSyncRecordMapper;
    private final RefundMapper refundMapper;
    private final MmStripeAccountMapper mmStripeAccountMapper;

    @Resource
    private SplitPaymentServiceGrpc.SplitPaymentServiceBlockingStub splitPaymentServiceStub;

    private static final String SPLIT_TRAFFIC_RATIO_KEY = "SPLIT_TRAFFIC:RATIO";
    private static final String SPLIT_TRAFFIC_WHITE_LIST_KEY = "SPLIT_TRAFFIC:WHITE_LIST";
    private static final String SPLIT_TRAFFIC_BLACK_LIST_KEY = "SPLIT_TRAFFIC:BLACK_LIST";
    private static final String SPLIT_PAYMENT_LOAN_SWITCH_KEY = "SPLIT_PAYMENT:LOAN_SWITCH";

    private static final String SPLIT_PAYMENT_SUPPORTED_CURRENCY = "usd";

    private Map<Vendor, SplitStrategy> splitStrategyMap;

    @PostConstruct
    public void initMap() {
        splitStrategyMap = new HashMap<>();
        for (SplitStrategy splitStrategy : splitStrategyList) {
            splitStrategyMap.put(splitStrategy.getVendor(), splitStrategy);
        }
    }

    /**
     * 根据策略模式对不同第三方 处理不同的分账逻辑, 供应商通过key 区分
     *
     * @param key      供应商 {@link com.moego.idl.models.split_payment.v1.Vendor}
     * @param entityId 需要进行分账的entity id
     * @return 返回的boolean 只代表是否成功调用分账，不关注分账结果
     */
    public boolean split(Vendor key, Integer entityId, SplitSyncRecordTypeEnum type) {
        if (!splitStrategyMap.containsKey(key)) return false;
        return splitStrategyMap.get(key).split(entityId, type);
    }

    /**
     * 定时任务调度, 这里会处理所有状态位init的 sync record, 是split payment方法的补偿机制
     *
     * @param key 供应商 {@link com.moego.idl.models.split_payment.v1.Vendor}
     */
    public void retrySplitPayment(Vendor key) {
        if (!splitStrategyMap.containsKey(key)) return;
        splitStrategyMap.get(key).retrySplitPayment();
    }

    /**
     * 创建同步执行的分账记录, 状态为init
     *
     * @param entityId  处理分账的payment
     * @param type 分账单据类型
     */
    public void createSplitRecordSync(Integer entityId, SplitSyncRecordTypeEnum type) {
        createRecord(entityId, Vendor.STRIPE, type, SplitSyncRecordSyncModeEnum.MODE_SYNC);
    }

    /**
     * 创建分账记录, 状态为init, 默认为异步进行
     *
     * @param entityId  处理分账的payment
     * @param type 分账单据类型
     */
    public void createSplitRecord(Integer entityId, SplitSyncRecordTypeEnum type) {
        // 如果已经存在了，直接返回
        if (checkRecordExist(entityId, type)) {
            return;
        }
        createRecord(entityId, Vendor.STRIPE, type, SplitSyncRecordSyncModeEnum.MODE_ASYNC);
    }

    /**
     * 创建分账记录的私有方法
     */
    private void createRecord(
            Integer entityId, Vendor vendor, SplitSyncRecordTypeEnum type, SplitSyncRecordSyncModeEnum mode) {
        var record = new SplitSyncRecord();
        record.setSplitEntityId(entityId.longValue());
        record.setStatus(SplitSyncRecordStatusEnum.INIT.name());
        record.setRetryTimes(0);
        record.setIsReverse(isReverse(type));
        record.setTransferGroup(CommonUtil.getUuid());
        record.setType(type.name());
        record.setSyncMode(mode.name());
        record.setSyncVendor(vendor.name());
        splitSyncRecordMapper.insertSelective(record);
    }

    private Boolean isReverse(SplitSyncRecordTypeEnum type) {
        return Set.of(SplitSyncRecordTypeEnum.DISPUTE_REVERSE, SplitSyncRecordTypeEnum.REFUND)
                .contains(type);
    }

    /**
     * 取消分账, split sync record 设置成cancel之后就不会被定时任务补偿(即不会调用分账逻辑)
     *
     * @param paymentId 支付记录id
     */
    public void cancelSplitSyncRecord(Integer paymentId, SplitSyncRecordTypeEnum type) {
        var record = splitSyncRecordMapper.selectOneByEntityIdAndType(paymentId, type.name());
        if (Objects.isNull(record)) return;
        record.setStatus(SplitSyncRecordStatusEnum.CANCEL.name());
        splitSyncRecordMapper.updateByPrimaryKeySelective(record);
    }

    /**
     * 根据entity id 和 type 确认是否存在同步单据
     *
     * @param entityId entity id
     * @param type     单据类型
     * @return 是否存在
     */
    public Boolean checkRecordExist(Integer entityId, SplitSyncRecordTypeEnum type) {
        var reverse = (byte) (isReverse(type) ? 1 : 0);
        return Objects.nonNull(
                splitSyncRecordMapper.selectOneByEntityIdAndTypeAndReverse(entityId, type.name(), reverse));
    }

    /*
     以上是调用分账相关的接口
    */

    /**
     * 切流方法, 通过business id 控制通过的流量比率
     * 策略如下:
     * 1. 如果business id 在黑名单中, 则不予通过
     * 2. 如果business id 在白名单中, 直接放行
     * 3. 对business id 哈希, 如果 最后比率小于ratio 则放行
     *
     * @param businessId business id
     * @return 是否允许
     */
    public Boolean checkTrafficRatio(Integer businessId) {

        // 不是stripe的business，或者币种不是usd的，都不能走分账
        MmStripeAccount mmStripeAccount = mmStripeAccountMapper.selectByBusinessId(businessId);
        if (null == mmStripeAccount) {
            return Boolean.FALSE;
        }
        String defaultCurrency = mmStripeAccount.getDefaultCurrency();
        if (!SPLIT_PAYMENT_SUPPORTED_CURRENCY.equals(defaultCurrency)) {
            return Boolean.FALSE;
        }
        var blackList = getTrafficRationList(SPLIT_TRAFFIC_BLACK_LIST_KEY);
        if (blackList.contains(businessId)) return Boolean.FALSE;
        var whiteList = getTrafficRationList(SPLIT_TRAFFIC_WHITE_LIST_KEY);
        if (whiteList.contains(businessId)) return Boolean.TRUE;
        var ratio = getTrafficRatio();
        var hash = Math.abs(((businessId << 4) ^ businessId) % 100 + 1);
        return hash <= ratio;
    }

    /**
     * 控制切流比例
     *
     * @param ratio 比例值
     */
    public void setTrafficRation(Integer ratio) {
        if (ratio < 0 || ratio > 100)
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "ratio can only be a value between 0 and 100");
        redisUtil.set(SPLIT_TRAFFIC_RATIO_KEY, String.valueOf(ratio));
    }

    /**
     * 获取redis 中的流量控制值, 即切流比例
     *
     * @return traffic control
     */
    private Integer getTrafficRatio() {
        var ratio = redisUtil.get(SPLIT_TRAFFIC_RATIO_KEY);
        return Objects.isNull(ratio) ? 0 : Integer.parseInt(ratio);
    }

    /**
     * 获取切流比率的名单
     *
     * @return list
     */
    private Set<Integer> getTrafficRationList(String key) {
        var list = redisUtil.lRange(key, 0, -1);
        if (Objects.isNull(list) || list.isEmpty()) return Set.of();
        return list.stream().map(Integer::valueOf).collect(Collectors.toSet());
    }

    public Set<Integer> getBlackList() {
        return getTrafficRationList(SPLIT_TRAFFIC_BLACK_LIST_KEY);
    }

    public Set<Integer> getWhiteList() {
        return getTrafficRationList(SPLIT_TRAFFIC_WHITE_LIST_KEY);
    }

    public Integer getTrafficRatioForWeb() {
        return getTrafficRatio();
    }

    public void addWhiteRationList(Integer businessId) {
        addRationList(SPLIT_TRAFFIC_WHITE_LIST_KEY, businessId);
    }

    public void addBlackRationList(Integer businessId) {
        addRationList(SPLIT_TRAFFIC_BLACK_LIST_KEY, businessId);
    }

    public void delWhiteRationList(Integer businessId) {
        delRationList(SPLIT_TRAFFIC_WHITE_LIST_KEY, businessId);
    }

    public void delBlackRationList(Integer businessId) {
        delRationList(SPLIT_TRAFFIC_BLACK_LIST_KEY, businessId);
    }

    private void addRationList(String key, Integer businessId) {
        redisUtil.lLeftPush(key, String.valueOf(businessId));
    }

    private void delRationList(String key, Integer businessId) {
        redisUtil.lRemove(key, 0, String.valueOf(businessId));
    }

    public void setLoanSwitch(String loanSwitch) {
        redisUtil.set(SPLIT_PAYMENT_LOAN_SWITCH_KEY, loanSwitch);
    }

    public String getLoanSwitch() {
        return redisUtil.get(SPLIT_PAYMENT_LOAN_SWITCH_KEY);
    }
}
