package com.moego.server.payment.mapper;

import com.moego.server.payment.mapperbean.SplitSyncRecord;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface SplitSyncRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table split_sync_record
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table split_sync_record
     *
     * @mbg.generated
     */
    int insert(SplitSyncRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table split_sync_record
     *
     * @mbg.generated
     */
    int insertSelective(SplitSyncRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table split_sync_record
     *
     * @mbg.generated
     */
    SplitSyncRecord selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table split_sync_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(SplitSyncRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table split_sync_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(SplitSyncRecord record);

    SplitSyncRecord selectOneByEntityId(Integer paymentId);

    SplitSyncRecord selectOneByEntityIdAndType(@Param("entityId") Integer entityId, @Param("type") String type);

    SplitSyncRecord selectOneByEntityIdAndTypeAndReverse(
            @Param("entityId") Integer entityId, @Param("type") String type, @Param("reverse") Byte reverse);

    List<SplitSyncRecord> selectListByStatus(@Param("status") String status);
}
