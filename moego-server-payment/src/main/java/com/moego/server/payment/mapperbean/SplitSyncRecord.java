package com.moego.server.payment.mapperbean;

import java.util.Date;

/**
 * Database Table Remarks:
 *   分账受理同步表
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table split_sync_record
 */
public class SplitSyncRecord {
    /**
     * Database Column Remarks:
     *   自增id, 唯一标识
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column split_sync_record.id
     *
     * @mbg.generated
     */
    private Long id;

    /**
     * Database Column Remarks:
     *   创建时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column split_sync_record.created_time
     *
     * @mbg.generated
     */
    private Date createdTime;

    /**
     * Database Column Remarks:
     *   修改时间
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column split_sync_record.updated_time
     *
     * @mbg.generated
     */
    private Date updatedTime;

    /**
     * Database Column Remarks:
     *   原支付/退款单据id
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column split_sync_record.split_entity_id
     *
     * @mbg.generated
     */
    private Long splitEntityId;

    /**
     * Database Column Remarks:
     *   是否逆向单据
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column split_sync_record.is_reverse
     *
     * @mbg.generated
     */
    private Boolean isReverse;

    /**
     * Database Column Remarks:
     *   状态(INIT,PROCESSING,SUCCESS,FAILED)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column split_sync_record.status
     *
     * @mbg.generated
     */
    private String status;

    /**
     * Database Column Remarks:
     *   重试次数
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column split_sync_record.retry_times
     *
     * @mbg.generated
     */
    private Integer retryTimes;

    /**
     * Database Column Remarks:
     *   分割组
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column split_sync_record.transfer_group
     *
     * @mbg.generated
     */
    private String transferGroup;

    /**
     * Database Column Remarks:
     *   分账单据类型(PAYMENT,REFUND,DISPUTE)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column split_sync_record.type
     *
     * @mbg.generated
     */
    private String type;

    /**
     * Database Column Remarks:
     *   同步模式(sync, async)
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column split_sync_record.sync_mode
     *
     * @mbg.generated
     */
    private String syncMode;

    /**
     * Database Column Remarks:
     *   供应商
     *
     * This field was generated by MyBatis Generator.
     * This field corresponds to the database column split_sync_record.sync_vendor
     *
     * @mbg.generated
     */
    private String syncVendor;

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column split_sync_record.id
     *
     * @return the value of split_sync_record.id
     *
     * @mbg.generated
     */
    public Long getId() {
        return id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column split_sync_record.id
     *
     * @param id the value for split_sync_record.id
     *
     * @mbg.generated
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column split_sync_record.created_time
     *
     * @return the value of split_sync_record.created_time
     *
     * @mbg.generated
     */
    public Date getCreatedTime() {
        return createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column split_sync_record.created_time
     *
     * @param createdTime the value for split_sync_record.created_time
     *
     * @mbg.generated
     */
    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column split_sync_record.updated_time
     *
     * @return the value of split_sync_record.updated_time
     *
     * @mbg.generated
     */
    public Date getUpdatedTime() {
        return updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column split_sync_record.updated_time
     *
     * @param updatedTime the value for split_sync_record.updated_time
     *
     * @mbg.generated
     */
    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column split_sync_record.split_entity_id
     *
     * @return the value of split_sync_record.split_entity_id
     *
     * @mbg.generated
     */
    public Long getSplitEntityId() {
        return splitEntityId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column split_sync_record.split_entity_id
     *
     * @param splitEntityId the value for split_sync_record.split_entity_id
     *
     * @mbg.generated
     */
    public void setSplitEntityId(Long splitEntityId) {
        this.splitEntityId = splitEntityId;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column split_sync_record.is_reverse
     *
     * @return the value of split_sync_record.is_reverse
     *
     * @mbg.generated
     */
    public Boolean getIsReverse() {
        return isReverse;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column split_sync_record.is_reverse
     *
     * @param isReverse the value for split_sync_record.is_reverse
     *
     * @mbg.generated
     */
    public void setIsReverse(Boolean isReverse) {
        this.isReverse = isReverse;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column split_sync_record.status
     *
     * @return the value of split_sync_record.status
     *
     * @mbg.generated
     */
    public String getStatus() {
        return status;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column split_sync_record.status
     *
     * @param status the value for split_sync_record.status
     *
     * @mbg.generated
     */
    public void setStatus(String status) {
        this.status = status == null ? null : status.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column split_sync_record.retry_times
     *
     * @return the value of split_sync_record.retry_times
     *
     * @mbg.generated
     */
    public Integer getRetryTimes() {
        return retryTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column split_sync_record.retry_times
     *
     * @param retryTimes the value for split_sync_record.retry_times
     *
     * @mbg.generated
     */
    public void setRetryTimes(Integer retryTimes) {
        this.retryTimes = retryTimes;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column split_sync_record.transfer_group
     *
     * @return the value of split_sync_record.transfer_group
     *
     * @mbg.generated
     */
    public String getTransferGroup() {
        return transferGroup;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column split_sync_record.transfer_group
     *
     * @param transferGroup the value for split_sync_record.transfer_group
     *
     * @mbg.generated
     */
    public void setTransferGroup(String transferGroup) {
        this.transferGroup = transferGroup == null ? null : transferGroup.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column split_sync_record.type
     *
     * @return the value of split_sync_record.type
     *
     * @mbg.generated
     */
    public String getType() {
        return type;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column split_sync_record.type
     *
     * @param type the value for split_sync_record.type
     *
     * @mbg.generated
     */
    public void setType(String type) {
        this.type = type == null ? null : type.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column split_sync_record.sync_mode
     *
     * @return the value of split_sync_record.sync_mode
     *
     * @mbg.generated
     */
    public String getSyncMode() {
        return syncMode;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column split_sync_record.sync_mode
     *
     * @param syncMode the value for split_sync_record.sync_mode
     *
     * @mbg.generated
     */
    public void setSyncMode(String syncMode) {
        this.syncMode = syncMode == null ? null : syncMode.trim();
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method returns the value of the database column split_sync_record.sync_vendor
     *
     * @return the value of split_sync_record.sync_vendor
     *
     * @mbg.generated
     */
    public String getSyncVendor() {
        return syncVendor;
    }

    /**
     * This method was generated by MyBatis Generator.
     * This method sets the value of the database column split_sync_record.sync_vendor
     *
     * @param syncVendor the value for split_sync_record.sync_vendor
     *
     * @mbg.generated
     */
    public void setSyncVendor(String syncVendor) {
        this.syncVendor = syncVendor == null ? null : syncVendor.trim();
    }
}
