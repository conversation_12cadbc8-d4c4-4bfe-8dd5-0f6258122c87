package com.moego.server.payment.service.strategy.splitpayment;

import com.moego.common.enums.PaymentStatusEnum;
import com.moego.common.enums.payment.SplitSyncRecordStatusEnum;
import com.moego.common.enums.payment.SplitSyncRecordSyncModeEnum;
import com.moego.common.enums.payment.SplitSyncRecordTypeEnum;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.split_payment.v1.SplitDetailModel;
import com.moego.idl.models.split_payment.v1.SplitEntity;
import com.moego.idl.models.split_payment.v1.SplitEntityType;
import com.moego.idl.models.split_payment.v1.SplitType;
import com.moego.idl.models.split_payment.v1.Vendor;
import com.moego.idl.service.split_payment.v1.ReverseSplitPaymentRequest;
import com.moego.idl.service.split_payment.v1.SplitPaymentRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.payment.enums.DisputeStatusTypeEnum;
import com.moego.server.payment.mapperbean.MmStripeDispute;
import com.moego.server.payment.mapperbean.MoePayDetail;
import com.moego.server.payment.mapperbean.Payment;
import com.moego.server.payment.mapperbean.SplitSyncRecord;
import com.stripe.exception.StripeException;
import com.stripe.model.Dispute;
import com.stripe.model.PaymentIntent;
import com.stripe.model.Refund;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/7/8
 */
@Service
@Slf4j
public class StripeSplitStrategy extends AbstractSplitStrategy {

    private static final String SOURCE_CHANNEL_PAYMENT_TYPE = "payment_intent";
    private static final String SOURCE_CHANNEL_REFUND_TYPE = "refund";
    private static final String SOURCE_CHANNEL_DISPUTE_TYPE = "issuing.dispute";

    @Override
    public Vendor getVendor() {
        return Vendor.STRIPE;
    }

    @Override
    protected Boolean doProcess(Integer entityId, SplitSyncRecordTypeEnum type) {
        var syncRecord = querySyncRecordByEntity(entityId, type);
        var result =
                switch (type) {
                    case PAYMENT -> processPayment(entityId, syncRecord);
                    case REFUND -> processRefund(entityId, syncRecord);
                    case DISPUTE_FORWARD, DISPUTE_REVERSE -> processDispute(entityId, syncRecord);
                };
        postProcess(result, syncRecord, type);
        return result;
    }

    private void postProcess(Boolean result, SplitSyncRecord syncRecord, SplitSyncRecordTypeEnum type) {
        // 在前面已经被取消了, 这里就不处理了
        if (SplitSyncRecordStatusEnum.CANCEL.name().equals(syncRecord.getStatus())) return;

        if (!result) {
            // payment 分账失败, 验证状态是否需要处理
            if (SplitSyncRecordTypeEnum.PAYMENT.equals(type)) {
                var pay = paymentMapper.selectByPrimaryKey(
                        syncRecord.getSplitEntityId().intValue());
                if (PaymentStatusEnum.CREATED.equals(pay.getStatus())
                        || PaymentStatusEnum.PROCESSING.equals(pay.getStatus())) {
                    return;
                }
            }
        }

        log.info(
                "process split payment result:{}, record id:{}, record entity id: {}",
                result,
                syncRecord.getId(),
                syncRecord.getSplitEntityId());
        ThreadPool.execute(() -> {
            var e = result ? SplitSyncRecordStatusEnum.SUCCESS : SplitSyncRecordStatusEnum.INIT;
            handlerProcess(e, syncRecord);
        });
    }

    private Boolean processPayment(Integer paymentId, SplitSyncRecord syncRecord) {
        return Optional.ofNullable(paymentMapper.selectByPrimaryKey(paymentId))
                .filter(payment -> !PaymentStatusEnum.FAILED.equals(payment.getStatus()))
                .map(payment -> {
                    // paid 状态要考虑
                    if (PaymentStatusEnum.PAID.equals(payment.getStatus())
                            || PaymentStatusEnum.COMPLETED.equals(payment.getStatus())) {
                        return splitPayment(payment, syncRecord);
                    }
                    return Boolean.FALSE;
                })
                .orElseGet(() -> {
                    // payment 不存在, 或者是payment.status = failed 都cancel这个同步记录
                    cancelSyncRecord(syncRecord);
                    return Boolean.FALSE;
                });
    }

    private Boolean processRefund(Integer refundId, SplitSyncRecord syncRecord) {
        return Optional.ofNullable(refundMapper.selectByPrimaryKey(refundId))
                // refund 状态只有create 和 failed T^T
                .filter(refund -> PaymentStatusEnum.CREATED.equals(refund.getStatus())
                        || PaymentStatusEnum.INIT.equals(refund.getStatus()))
                .map(refund -> splitRefund(refund, syncRecord))
                .orElseGet(() -> {
                    // refund 不存在, 或者是refund.status = failed 都cancel这个同步记录
                    cancelSyncRecord(syncRecord);
                    return Boolean.FALSE;
                });
    }

    private Boolean processDispute(Integer disputeId, SplitSyncRecord syncRecord) {
        return Optional.ofNullable(mmStripeDisputeMapper.selectByPrimaryKey(disputeId))
                .map(dispute -> {
                    // 如果是逆向 dispute分账, 这里的状态是need response
                    if (SplitSyncRecordTypeEnum.DISPUTE_REVERSE.name().equals(syncRecord.getType())
                            && DisputeStatusTypeEnum.NEEDS_RESPONSE
                                    .getDescription()
                                    .equals(dispute.getStatus())) {
                        return splitDispute(dispute, syncRecord);
                    }
                    // 如果是正向dispute分账, 这里的状态是 won
                    if (SplitSyncRecordTypeEnum.DISPUTE_FORWARD.name().equals(syncRecord.getType())
                            && DisputeStatusTypeEnum.WON.getDescription().equals(dispute.getStatus())) {
                        return splitDispute(dispute, syncRecord);
                    }
                    return false;
                })
                .orElseGet(() -> {
                    // dispute 不存在, cancel这个同步记录
                    cancelSyncRecord(syncRecord);
                    return Boolean.FALSE;
                });
    }

    private Boolean splitDispute(MmStripeDispute dispute, SplitSyncRecord syncRecord) {
        var stripeDispute = getStripeDispute(dispute.getDisputeId());
        if (Objects.isNull(stripeDispute)) {
            log.error(
                    "stripe dispute is null, mmDispute id: {}, stripe refund id: {}",
                    dispute.getId(),
                    dispute.getDisputeId());
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "invoke reverse split payment, stripe refund not found!");
        }
        var currency = stripeDispute.getCurrency();
        // dispute 表里存的Long 类型美分，需要转成decimal
        var amount = new BigDecimal(dispute.getAmount()).movePointLeft(2);
        if (syncRecord.getIsReverse()) {
            ReverseSplitPaymentRequest reverseSplitPayment = ReverseSplitPaymentRequest.newBuilder()
                    .setReverseSplitEntity(SplitEntity.newBuilder()
                            .setEntityType(SplitEntityType.DISPUTE_REVERSE)
                            .setEntityId(dispute.getId().longValue())
                            .build())
                    .setBusinessId(dispute.getBusinessId().longValue())
                    .setReverseAmount(buildMoney(amount, currency))
                    .addAllPlatformReverseList(List.of())
                    .setCurrency(currency)
                    .setTransferGroup(syncRecord.getTransferGroup())
                    .setSourceChannelReverseId(dispute.getDisputeId())
                    .setSourceChannelReverseType(SOURCE_CHANNEL_DISPUTE_TYPE)
                    .setSourceChannelReverseMethod(Strings.EMPTY)
                    .setSourceSplitEntity(SplitEntity.newBuilder()
                            .setEntityId(dispute.getPaymentId())
                            .setEntityType(SplitEntityType.PAYMENT)
                            .build())
                    .setVendor(getVendor())
                    .build();

            return invokeReverse(reverseSplitPayment);
        } else {
            SplitPaymentRequest splitPaymentRequest = SplitPaymentRequest.newBuilder()
                    .setSplitEntity(SplitEntity.newBuilder()
                            .setEntityType(SplitEntityType.DISPUTE)
                            .setEntityId(dispute.getId().longValue())
                            .build())
                    .setBusinessId(dispute.getBusinessId().longValue())
                    .setTotalAmount(buildMoney(amount, currency))
                    .addAllPlatformSplitList(List.of())
                    .setCurrency(currency)
                    .setTransferGroup(syncRecord.getTransferGroup())
                    .setSourceChannelPaymentId(dispute.getDisputeId())
                    .setSourceChannelPaymentType(SOURCE_CHANNEL_DISPUTE_TYPE)
                    .setSourceChannelPaymentMethod(Strings.EMPTY)
                    // 分账逻辑只涉及到 stripe or adyen, 这里每个策略会重写逻辑
                    .setVendor(getVendor())
                    .build();
            return invokeForward(splitPaymentRequest);
        }
    }

    private static class RefundContext {
        RefundContext(String currency, String sourceChannelReverseId) {
            this.currency = currency;
            this.sourceChannelReverseId = sourceChannelReverseId;
        }

        public String currency;
        public String sourceChannelReverseId;
    }

    private RefundContext getRefundContext(com.moego.server.payment.mapperbean.Refund refund) {
        String stripeRefundId = refund.getStripeRefundId();
        // 有stripe调用，说明是异步操作
        if (StringUtils.hasLength(stripeRefundId)) {
            Refund stripeRefund = getStripeRefund(stripeRefundId);
            if (Objects.isNull(stripeRefund)) {
                log.error(
                        "refund is null, refund id: {}, stripe refund id: {}",
                        refund.getId(),
                        refund.getStripeRefundId());
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "invoke reverse split payment, stripe refund not found!");
            }
            return new RefundContext(stripeRefund.getCurrency(), stripeRefundId);
        }
        // 走到这需要从payment获取
        Payment payment = paymentMapper.selectByPrimaryKey(refund.getOriginPaymentId());
        if (payment == null) {
            log.error("original payment not exist, refundID:{}", refund.getId());
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format(
                            "invoke reverse split payment, payment record not found, refundID:%d", refund.getId()));
        }
        PaymentIntent paymentIntent = getStripePaymentIntent(payment.getStripeIntentId());
        if (paymentIntent == null) {
            log.error("original payment intent not exist, refundID:{},paymentID:{}", refund.getId(), payment.getId());
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    String.format(
                            "invoke reverse split payment, original payment intent not exist, refundID:%d,paymentID:%d",
                            refund.getId(), payment.getId()));
        }
        return new RefundContext(paymentIntent.getCurrency(), Strings.EMPTY);
    }

    private Boolean splitRefund(com.moego.server.payment.mapperbean.Refund refund, SplitSyncRecord syncRecord) {

        RefundContext refundContext = getRefundContext(refund);

        var bookingFee = refund.getBookingFee();
        var platformSplitList = new ArrayList<SplitDetailModel>();
        // 退款单只关注booking fee
        if (bookingFee.compareTo(BigDecimal.ZERO) > 0) {
            platformSplitList.add(createDetailModel(
                    bookingFee, refundContext.currency, SplitType.BOOKING_FEE, DESCRIPTION_BOOKING_FEE));
        }

        ReverseSplitPaymentRequest reverseSplitPayment = ReverseSplitPaymentRequest.newBuilder()
                .setReverseSplitEntity(SplitEntity.newBuilder()
                        .setEntityType(SplitEntityType.REFUND)
                        .setEntityId(refund.getId())
                        .build())
                .setBusinessId(refund.getBusinessId().longValue())
                .setReverseAmount(buildMoney(refund.getAmount().add(bookingFee), refundContext.currency))
                .addAllPlatformReverseList(platformSplitList)
                .setCurrency(refundContext.currency)
                .setTransferGroup(syncRecord.getTransferGroup())
                .setSourceChannelReverseId(refundContext.sourceChannelReverseId)
                .setSourceChannelReverseType(SOURCE_CHANNEL_REFUND_TYPE)
                .setSourceChannelReverseMethod(
                        Optional.ofNullable(refund.getMethod()).orElse(Strings.EMPTY))
                .setSourceSplitEntity(SplitEntity.newBuilder()
                        .setEntityId(refund.getOriginPaymentId())
                        .setEntityType(SplitEntityType.PAYMENT)
                        .build())
                .setVendor(getVendor())
                .build();
        return SplitSyncRecordSyncModeEnum.MODE_SYNC.name().equals(syncRecord.getSyncMode())
                ? invokeSyncReverse(reverseSplitPayment)
                : invokeReverse(reverseSplitPayment);
    }

    private Boolean splitPayment(Payment payment, SplitSyncRecord syncRecord) {
        var intent = getStripePaymentIntent(payment.getStripeIntentId());
        if (Objects.isNull(intent)) {
            log.error(
                    "payment intent is null, payment id: {}, payment intent id: {}",
                    payment.getId(),
                    payment.getStripeIntentId());
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "invoke forward split payment, stripe payment intent not found!");
        }

        var currency = intent.getCurrency();
        // payment details 可能有 booking fee等 平台分账费率
        var platformSplitList = buildPlatformSplitList(payment, currency);

        // 总分账金额需要加上booking fee
        BigDecimal totalAmount = payment.getAmount();
        MoePayDetail moePayDetail = queryPayDetail(payment.getId());
        if (moePayDetail != null && moePayDetail.getBookingFee().compareTo(BigDecimal.ZERO) > 0) {
            totalAmount = totalAmount.add(moePayDetail.getBookingFee());
        }

        SplitPaymentRequest splitPaymentRequest = SplitPaymentRequest.newBuilder()
                .setSplitEntity(SplitEntity.newBuilder()
                        .setEntityType(SplitEntityType.PAYMENT)
                        .setEntityId(payment.getId())
                        .build())
                .setBusinessId(payment.getBusinessId().longValue())
                .setTotalAmount(buildMoney(totalAmount, currency))
                .addAllPlatformSplitList(platformSplitList)
                .setCurrency(currency)
                .setTransferGroup(syncRecord.getTransferGroup())
                .setSourceChannelPaymentId(payment.getStripeIntentId())
                .setSourceChannelPaymentType(SOURCE_CHANNEL_PAYMENT_TYPE)
                .setSourceChannelPaymentMethod(
                        Optional.ofNullable(payment.getMethod()).orElse(Strings.EMPTY))
                // 分账逻辑只涉及到 stripe or adyen, 这里每个策略会重写逻辑
                .setVendor(getVendor())
                .build();

        return invokeForward(splitPaymentRequest);
    }

    /**
     * 获取stripe dispute 实体
     *
     * @param disputeId stripe dispute id
     * @return stripe dispute
     */
    private Dispute getStripeDispute(String disputeId) {
        try {
            return Dispute.retrieve(disputeId);
        } catch (StripeException e) {
            log.error(String.format("get stripe dispute error! msg: %s", e.getMessage()));
        }
        return null;
    }

    /**
     * 获取stripe refund 实体
     *
     * @param stripeRefundId stripe refund id
     * @return stripe refund
     */
    private Refund getStripeRefund(String stripeRefundId) {
        try {
            return Refund.retrieve(stripeRefundId);
        } catch (Exception e) {
            log.error("get stripe refund error", e);
        }
        return null;
    }

    /**
     * 获取stripe payment intent
     *
     * @param paymentIntentId payment intent id
     * @return payment intent, 可能为空
     */
    private PaymentIntent getStripePaymentIntent(String paymentIntentId) {
        try {
            return PaymentIntent.retrieve(paymentIntentId);
        } catch (StripeException e) {
            log.error(String.format("get stripe payment Intent error! msg: %s", e.getMessage()));
        }
        return null;
    }
}
