package resource

import (
	"github.com/MoeGolibrary/go-lib/gorm"
	"gorm.io/driver/postgres"
	"gorm.io/plugin/opentelemetry/tracing"

	"github.com/MoeGolibrary/moego-svc-fulfillment/config"
)

var fulfillmentDB *gorm.DB

func GetDB() *gorm.DB {
	return fulfillmentDB
}

func InitDB(dbConfig config.DBConfig) {
	db, err := gorm.Open(postgres.Open(dbConfig.DSN), &gorm.Config{
		SkipDefaultTransaction: true,
		TranslateError:         true,
		QueryFields:            true,
	})
	if err != nil {
		panic(err)
	}
	// set only tracing
	if err = db.Use(tracing.NewPlugin(tracing.WithoutMetrics(), tracing.WithoutQueryVariables())); err != nil {
		panic(err)
	}
	sql, err := db.DB()
	if err != nil {
		panic(err)
	}
	sql.SetMaxIdleConns(dbConfig.MaxIdleConnections)
	sql.SetMaxOpenConns(dbConfig.MaxOpenConnections)
	sql.SetConnMaxLifetime(dbConfig.MaxConnectionLifetime)
	if err := sql.Ping(); err != nil {
		panic(err)
	}

	fulfillmentDB = db
}
