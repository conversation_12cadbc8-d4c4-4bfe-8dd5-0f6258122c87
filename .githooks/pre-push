#!/usr/bin/env bash

# Check if current branch is behind main
currentBranch=$(git branch --show-current)
if [ "$currentBranch" != "main" ]; then
    git fetch origin main:main
    behindCount=$(git rev-list --count HEAD..main)
    if [ "$behindCount" -gt 0 ]; then
        echo "ERROR: Your branch is $behindCount commit(s) behind main."
        echo "Please merge or rebase with main before committing:"
        echo "                  "
        echo "  git merge main  "
        echo "                  "
        exit 1
    else
        echo "branch '${currentBranch}' is up to date with main."
    fi
fi

BRANCH=$(git rev-parse --abbrev-ref HEAD)
PATTERN='^(feature|bugfix|master|staging|online|gate|main|release).*$'

if [[ "$BRANCH" =~ $PATTERN ]]; then
  exit 0
fi

function err_print() {
  echo "\033[0;31m$1\033[0m"
}

err_print "branch name '$BRANCH' does not match the pattern: $PATTERN"

exit 1
