#!/usr/bin/env bash

INPUT_FILE=$1
HEADER=$(head -n1 "$INPUT_FILE")

if [[ "$HEADER" =~ ^(Merge|Revert) ]]; then
  exit 0
fi

PATTERN="^(feat|fix|docs|style|refactor|perf|test|chore)(\(.+\))?: .{1,100} (BETA-|CS-|APP-|MOEG-|MC-|OBV-|DBO-|PEC-|UF-|GROOM-|WT-|MOE-|ERP-|TECH-|FDN-|IFRBE-|CA-|MER-|CRM-|FIN-|ENT-|GRM-)[0-9]+\$"

if [[ "$HEADER" =~ $PATTERN ]]; then
  exit 0
fi

function err_print() {
  echo "\033[0;31m$1\033[0m"
}

err_print "commit message '$HEADER' does not match the pattern: $PATTERN"

exit 1
