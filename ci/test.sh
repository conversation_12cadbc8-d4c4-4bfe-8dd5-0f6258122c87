#!/usr/bin/env bash

source ci/env.sh

set -xeuo pipefail

export CONFIG_FILE="$PWD/config-unit-test.yaml"

go install golang.org/x/tools/cmd/cover@latest
go install github.com/boumenot/gocover-cobertura@latest
go install github.com/jstemmer/go-junit-report/v2@latest

go test -coverprofile output/cover.out -coverpkg ./internal/service/... -v ./internal/... 2>&1 \
  | go-junit-report -set-exit-code -iocopy -out output/report.xml
gocover-cobertura -by-files -ignore-gen-files < output/cover.out > output/cover.xml
