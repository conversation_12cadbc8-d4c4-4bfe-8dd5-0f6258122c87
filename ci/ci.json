{"$schema": "https://moego.s3.us-west-2.amazonaws.com/ops/github-actions/ci-json-schema.json", "service_name": "moego-svc-fulfillment", "language": {"type": "go", "version": "1.24"}, "install": {"commands": ["go mod download"]}, "lint": {"commands": ["curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/HEAD/install.sh | sh -s -- -b $(go env GOPATH)/bin v2.4.0", "golangci-lint run -v --allow-parallel-runners --timeout 3m"]}, "test": {"commands": ["mkdir -p output", "go test -coverprofile output/cover.out -coverpkg ./internal/service/... -v ./internal/... 2>&1 | go-junit-report -set-exit-code -iocopy -out output/report.xml", "gocov convert output/cover.out | gocov-xml > output/cover.xml"], "report": "./output/report.xml", "coverage": "./output/cover.xml", "coverage_gate": 0}, "build": {"commands": ["CGO_ENABLED=0 go build -o moego-svc-fulfillment"], "build_image": [{"dockerfile": "ci/Dockerfile", "context": "."}]}, "deploy": {"type": "service"}}