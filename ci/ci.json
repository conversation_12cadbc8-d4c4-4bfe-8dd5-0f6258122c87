{"$schema": "https://moego.s3.us-west-2.amazonaws.com/ops/github-actions/ci-json-schema.json", "service_name": "moego-java-lib", "language": {"type": "java", "version": "17"}, "install": {"commands": ["./gradlew classes --stacktrace --no-daemon -Dsbc.scriptBranch=GitHubActions"], "cache_dir": ".gradle"}, "lint": {"commands": ["./gradlew spotlessCheck spotbugsMain --stacktrace --no-daemon -Dsbc.scriptBranch=GitHubActions"]}, "test": {"commands": ["./gradlew test --stacktrace --no-daemon -Dsbc.scriptBranch=GitHubActions"]}}