plugins {
  id 'net.razvan.jacoco-to-cobertura' version '1.3.0'
  id 'java'
  id 'jacoco'
  id 'io.spring.dependency-management' version "${springDependencyManagementVersion}"
  id 'org.springframework.boot' version "${springBootVersion}"
  id 'com.diffplug.spotless' version "${spotlessVersion}"
  id "com.github.spotbugs" version "${spotbugsVersion}"
}

repositories {
  mavenCentral()
  maven { url 'https://jitpack.io' }
}

dependencyManagement {
  imports {
    mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
  }
}

apply from: "https://moego.s3.us-west-2.amazonaws.com/ops/sbc/${System.getProperty('sbc.scriptBranch', '')}SourceBranchControl.gradle"
applySourceBranchControlDependencies()

dependencies {
  implementation("com.moego.lib:moego-lib-feature-flag")

  compileOnly 'org.projectlombok:lombok'
  annotationProcessor 'org.projectlombok:lombok'
  annotationProcessor 'org.projectlombok:lombok-mapstruct-binding:0.2.0'
  annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

  implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
  implementation "org.springframework.boot:spring-boot-starter-web"

  testImplementation 'org.springframework.boot:spring-boot-starter-test'
  testImplementation 'org.springframework.boot:spring-boot-starter-validation'

  implementation "org.mapstruct:mapstruct:$mapstructVersion"
  annotationProcessor "org.mapstruct:mapstruct-processor:$mapstructVersion"
  testAnnotationProcessor "org.mapstruct:mapstruct-processor:$mapstructVersion"

  annotationProcessor 'no.entur.mapstruct.spi:protobuf-spi-impl:1.43'
  testAnnotationProcessor 'no.entur.mapstruct.spi:protobuf-spi-impl:1.43'

  implementation("org.springframework.boot:spring-boot-starter-data-redis")
  implementation "io.awspring.cloud:spring-cloud-aws-starter-secrets-manager:${springCloudAWSVersion}"

}

compileJava {
  options.compilerArgs << '-parameters'
}

tasks.withType(JavaCompile) {
  options.encoding = 'UTF-8'
}

tasks.named("bootJar") {
  archiveBaseName = 'moego-server'
}

tasks.named('test') {
  useJUnitPlatform()
}

spotless {
  encoding 'UTF-8'
  java {
    toggleOffOn()
    removeUnusedImports()
    trimTrailingWhitespace()
    endWithNewline()
    palantirJavaFormat()

    targetExclude "build/generated/**"
    custom('Refuse wildcard imports', {
      if (it =~ /\nimport .*\*;/) {
        throw new IllegalStateException("Do not use wildcard imports, 'spotlessApply' cannot resolve this issue, please fix it manually.")
      }
    } as Closure<String>)
  }
}

spotbugs {
  spotbugsTest.enabled = false
  omitVisitors.addAll 'FindReturnRef', 'MethodReturnCheck'
  excludeFilter.set(file("${rootDir}/config/spotbugs/excludeFilter.xml"))
}

jacocoTestReport {
  reports {
    xml {
      required = true
      destination file("${rootProject.buildDir}/reports/jacoco/test/jacocoTestReport.xml")
    }
  }
  afterEvaluate {
    classDirectories.setFrom(files(classDirectories.files.collect {
      fileTree(dir: it, exclude: [
        "**/*ConverterImpl*",
        "**/*MapperImpl*",
        "**/jooq/generated/**",
      ])
    }))
  }
}



jacocoToCobertura {
  inputFile = layout.buildDirectory.file("reports/jacoco/test/jacocoTestReport.xml")
  outputFile = layout.buildDirectory.file("reports/jacoco/test/cobertura-jacocoTestReport.xml")
  rootPackageToRemove = ''
}

jacocoToCobertura.dependsOn jacocoTestReport

