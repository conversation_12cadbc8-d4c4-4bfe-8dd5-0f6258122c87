plugins {
    id 'java'
    id 'org.springframework.boot' version "${springBootVersion}"
    id 'io.spring.dependency-management' version "${springDependencyManagementVersion}"
    id 'com.diffplug.spotless' version "${spotlessVersion}"
    id "com.github.spotbugs" version "${spotbugsVersion}"
    id "com.qqviaja.gradle.MybatisGenerator" version "${mybatisGeneratorGradlePlugin}"
    // 添加 jacoco 插件
    id 'jacoco'
    id 'net.razvan.jacoco-to-cobertura' version "${jacocoVersion}"
    // other plugins
}

repositories {
    mavenLocal()
    mavenCentral()
    maven { url 'https://jitpack.io' }
}

compileJava {
    options.encoding = 'UTF-8'
    options.compilerArgs << '-parameters'
}
compileTestJava {
    options.encoding = 'UTF-8'
    options.compilerArgs << '-parameters'
}
test {
    useJUnitPlatform()
}

dependencyManagement {
    imports {
        mavenBom "org.springframework.boot:spring-boot-dependencies:${springBootVersion}"
        mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
    }
}

apply from: "https://moego.s3.us-west-2.amazonaws.com/ops/sbc/${System.getProperty('sbc.scriptBranch', '')}SourceBranchControl.gradle"
applySourceBranchControlDependencies()

dependencies {
    implementation("com.moego.lib:moego-lib-feature-flag")

    implementation "org.mybatis.spring.boot:mybatis-spring-boot-starter:${mybatisBootStarterVersion}"
    implementation "org.mybatis.dynamic-sql:mybatis-dynamic-sql:${mybatisDynamicSqlVersion}"
    implementation 'org.postgresql:postgresql'
    implementation 'org.springframework.boot:spring-boot-starter-data-redis'
    implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
    implementation 'io.awspring.cloud:spring-cloud-aws-starter-secrets-manager:3.2.1'
    // aws jdbc driver
    implementation("software.amazon.jdbc:aws-advanced-jdbc-wrapper:2.5.3")

    implementation "io.grpc:grpc-services:${grpcVersion}"

    annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'
    testRuntimeOnly 'com.h2database:h2'
    testImplementation "org.mybatis.spring.boot:mybatis-spring-boot-starter-test:${mybatisBootStarterVersion}"
    testImplementation 'org.springframework.boot:spring-boot-starter-test'
    compileOnly 'org.projectlombok:lombok'
    annotationProcessor 'org.projectlombok:lombok'
    // see https://mapstruct.org/faq/#Can-I-use-MapStruct-together-with-Project-Lombok
    annotationProcessor("org.projectlombok:lombok-mapstruct-binding:${lombokMapstructBindingVersion}")

    // mapstruct
    implementation "org.mapstruct:mapstruct:${mapstructVersion}"
    annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"
    testAnnotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"
}

bootJar {
    archiveBaseName = 'moego-server'
    version = ''
}

configurations {
    mybatisGenerator
}
mybatisGenerator {
    verbose = true
    configFile = "${projectDir}/MyBatisGeneratorConfig.xml"
}

// spotless
spotless {
    encoding 'UTF-8'
    java {
        toggleOffOn()
        removeUnusedImports()
        trimTrailingWhitespace()
        endWithNewline()
        palantirJavaFormat()

        targetExclude(
                "build/generated/**",
                "src/main/java/**/mapper/*DynamicSqlSupport.java", // Mybatis dynamic sql support (generated code)
                "src/main/java/**/mapper/*Mapper.java", // Mybatis mapper (generated code)
                "src/main/java/**/entity/*"
        )

        custom('Refuse wildcard imports', {
            if (it =~ /\nimport .*\*;/) {
                throw new IllegalStateException("Do not use wildcard imports, 'spotlessApply' cannot resolve this issue, please fix it manually.")
            }
        } as Closure<String>)
    }
}
// spotbugs
spotbugs {
    spotbugsTest.enabled = false
    omitVisitors.addAll("FindReturnRef", "MethodReturnCheck", "DontReusePublicIdentifiers")
    excludeFilter.set(file("${rootDir}/config/spotbugs/exclude.xml"))
}

jacocoTestReport {
    reports {
        xml {
            required = true
            destination file("${rootProject.buildDir}/reports/jacoco/test/jacocoTestReport.xml")
        }
    }
    afterEvaluate {
        classDirectories.setFrom(files(classDirectories.files.collect {
            fileTree(dir: it, exclude: [
                    "**/*ConverterImpl*",
                    "**/*MapperImpl*",
                    "**/jooq/generated/**",
            ])
        }))
    }
}