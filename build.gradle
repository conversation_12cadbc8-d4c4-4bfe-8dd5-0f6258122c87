plugins {
  id 'java'
  id 'jacoco'
  id 'io.spring.dependency-management' version "${springDependencyManagementVersion}"
  id 'org.springframework.boot' version "${springBootVersion}"
  id "com.qqviaja.gradle.MybatisGenerator" version "${mybatisGeneratorGradlePlugin}"
  id 'com.diffplug.spotless' version "${spotlessVersion}" apply false
  id "com.github.spotbugs" version "${spotbugsVersion}" apply false
  id 'net.razvan.jacoco-to-cobertura' version "${jacocoToCoberturaPlugin}"
}

repositories {
  mavenCentral()
  maven { url 'https://jitpack.io' }
}

dependencyManagement {
  imports {
    mavenBom "org.springframework.cloud:spring-cloud-dependencies:${springCloudVersion}"
  }
}

dependencies {

  implementation("com.moego.api:moego-api-java")
  implementation("com.moego.lib:moego-lib-common")
  implementation("com.moego.lib:moego-lib-activemq")
  implementation("com.moego.lib:moego-lib-event-bus")
  implementation("com.moego.lib:moego-lib-feature-flag")
  implementation("com.moego:moego-server-common")
  implementation("com.moego:moego-server-api")

  implementation("org.mybatis.spring.boot:mybatis-spring-boot-starter:${mybatisBootStarterVersion}")
  implementation("com.github.pagehelper:pagehelper-spring-boot-starter:${pagehelperBootStarterVersion}")
  implementation("org.springframework.boot:spring-boot-starter-activemq")
  implementation("org.springframework.boot:spring-boot-starter-data-redis")
  implementation 'org.springframework.cloud:spring-cloud-starter-openfeign'
  implementation "io.awspring.cloud:spring-cloud-aws-starter-secrets-manager:${springCloudAWSVersion}"
  // aws jdbc driver
  implementation("software.amazon.jdbc:aws-advanced-jdbc-wrapper:2.5.3")
  implementation("org.mybatis.dynamic-sql:mybatis-dynamic-sql:${mybatisDynamicSqlVersion}")
  implementation 'org.postgresql:postgresql'
  implementation 'org.json:json:20231013'

  compileOnly 'org.projectlombok:lombok'
  annotationProcessor 'org.projectlombok:lombok'
  annotationProcessor 'org.springframework.boot:spring-boot-configuration-processor'

  testImplementation 'org.springframework.boot:spring-boot-starter-test'

  implementation "org.mapstruct:mapstruct:${mapstructVersion}"
  annotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"
  testAnnotationProcessor "org.mapstruct:mapstruct-processor:${mapstructVersion}"

  annotationProcessor 'no.entur.mapstruct.spi:protobuf-spi-impl:1.46'
  testAnnotationProcessor 'no.entur.mapstruct.spi:protobuf-spi-impl:1.46'
}

compileJava {
  options.compilerArgs << '-parameters'
}

tasks.withType(JavaCompile) {
  options.encoding = 'UTF-8'
}

bootJar {
  archiveBaseName = 'moego-server'
}

tasks.named('test') {
  useJUnitPlatform()
  testLogging {
    events 'failed'
    exceptionFormat 'full'
  }
}

// spotless
apply plugin: 'com.diffplug.spotless'
spotless {
  encoding 'UTF-8'
  java {
    toggleOffOn()
    removeUnusedImports()
    trimTrailingWhitespace()
    endWithNewline()
    palantirJavaFormat()

    targetExclude(
      "build/generated/**",
      "src/main/java/**/mapper/*",
      "src/main/java/**/entity/*",
    )

    custom('Refuse wildcard imports', {
      if (it =~ /\nimport .*\*;/) {
        throw new IllegalStateException("Do not use wildcard imports, 'spotlessApply' cannot resolve this issue, please fix it manually.")
      }
    } as Closure<String>)
  }
}
// spotbugs
apply plugin: 'com.github.spotbugs'
spotbugs {
  spotbugsTest.enabled = false
  omitVisitors.addAll("FindReturnRef", "MethodReturnCheck", "DontReusePublicIdentifiers")
  excludeFilter.set(file("${rootDir}/config/spotbugs/exclude.xml"))
}

configurations {
  mybatisGenerator
}
mybatisGenerator {
  verbose = true
  configFile = "${rootDir}/MyBatisGeneratorConfig.xml"

  dependencies {
    mybatisGenerator "org.mybatis.generator:mybatis-generator-core:${mybatisGeneratorCoreVersion}"
    mybatisGenerator "org.postgresql:postgresql"
    mybatisGenerator "com.moego.lib:moego-lib-mybatis-plugins"
  }
}

// mbGenerator needs moego-lib-mybatis-plugins.jar, so we need to make sure it is built before mbGenerator
mbGenerator.dependsOn gradle.includedBuild("moego-java-lib").task(":moego-lib-mybatis-plugins:jar")

jacocoTestReport {
  reports {
    xml.required = true
  }

  afterEvaluate {
    classDirectories.setFrom(files(classDirectories.files.collect {
      fileTree(dir: it, include: ["**/service/**", "**/mapstruct/**", "**/server/**"])
    }))
  }
}
