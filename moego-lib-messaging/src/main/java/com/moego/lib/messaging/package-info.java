/**
 * This package abstracts the pub/sub messaging model.
 *
 * <p> {@link com.moego.lib.messaging.MessagingOperations} for publisher.
 * <p> {@link com.moego.lib.messaging.EventListener} for consumer.
 *
 * <p> If you want to implement the model base on another MQ, you need to implement the following interfaces:
 * <ul>
 *     <li>{@link com.moego.lib.messaging.MessagingOperations}</li>
 *     <li>{@link com.moego.lib.messaging.MessagingConsume}</li>
 *     <li>{@link com.moego.lib.messaging.MessagingConsumeCollector}</li>
 *     <li>{@link com.moego.lib.messaging.TopicDecider}</li>
 *     <li>{@link com.moego.lib.messaging.Msg}</li>
 * </ul>
 */
package com.moego.lib.messaging;
