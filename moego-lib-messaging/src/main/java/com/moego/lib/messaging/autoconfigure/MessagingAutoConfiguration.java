package com.moego.lib.messaging.autoconfigure;

import com.moego.lib.common.util.Env;
import com.moego.lib.messaging.Consumer;
import com.moego.lib.messaging.ConsumerExecutor;
import com.moego.lib.messaging.EventListener;
import com.moego.lib.messaging.GracefulShutter;
import com.moego.lib.messaging.MessagingConsumeCollector;
import com.moego.lib.messaging.MessagingOperations;
import com.moego.lib.messaging.NonTimeoutGracefulShutter;
import com.moego.lib.messaging.TimeoutGracefulShutter;
import com.moego.lib.messaging.TopicDecider;
import com.moego.lib.messaging.pulsar.PulsarMessagingConsumeCollector;
import com.moego.lib.messaging.pulsar.PulsarMessagingOperations;
import com.moego.lib.messaging.pulsar.PulsarTopicDecider;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import org.apache.pulsar.client.api.AuthenticationFactory;
import org.apache.pulsar.client.api.ClientBuilder;
import org.apache.pulsar.client.api.PulsarClient;
import org.apache.pulsar.client.api.PulsarClientException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.env.Environment;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @since 2022/9/29
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(prefix = MessagingProperties.PREFIX, name = "enabled", matchIfMissing = true)
@EnableConfigurationProperties(MessagingProperties.class)
@Import(MessagesConfiguration.class)
public class MessagingAutoConfiguration {

    private static final Logger log = LoggerFactory.getLogger(MessagingAutoConfiguration.class);

    @Bean
    @ConditionalOnBean(MessagingConsumeCollector.class)
    public ConsumerExecutor consumerExecutor(MessagingConsumeCollector messagingConsumeCollector) {
        return new ConsumerExecutor(messagingConsumeCollector);
    }

    @Bean
    @ConditionalOnMissingBean
    public GracefulShutter timeoutGracefulShutter(MessagingProperties properties) {
        long timeout = properties.getGracefulShutdownTimeout();
        return timeout > 0 ? new TimeoutGracefulShutter(timeout) : new NonTimeoutGracefulShutter();
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass(PulsarClient.class)
    @ConditionalOnProperty(value = MessagingProperties.PREFIX + ".pulsar.enabled", matchIfMissing = true)
    static class Pulsar {

        @Bean
        @Lazy
        @ConditionalOnMissingBean
        public PulsarClient pulsarClient(MessagingProperties messagingProperties) throws PulsarClientException {
            MessagingProperties.Pulsar pulsar = messagingProperties.getPulsar();
            // prevent app startup
            Assert.notNull(pulsar.getTenant(), "Pulsar tenant must not be null");

            ClientBuilder builder = PulsarClient.builder();
            Optional.of(pulsar.getServiceUrl()).ifPresent(url -> {
                if (!url.contains("://")) {
                    url = "pulsar://" + url;
                }
                builder.serviceUrl(url);
            });
            Optional.ofNullable(pulsar.getAuthentication())
                    .ifPresent(auth -> builder.authentication(AuthenticationFactory.token(auth)));
            return builder.build();
        }

        @Bean
        @Lazy // Only as a consumer service, this bean is not required
        @ConditionalOnMissingBean
        public MessagingOperations pulsarMessagingOperations(PulsarClient pulsarClient, TopicDecider topicDecider) {
            return new PulsarMessagingOperations(pulsarClient, topicDecider);
        }

        @Bean
        @Lazy
        @ConditionalOnMissingBean
        public TopicDecider pulsarTopicDecider(MessagingProperties messagingProperties, Environment environment) {
            return new PulsarTopicDecider(messagingProperties, getStandardEnv(environment));
        }

        @Bean
        @ConditionalOnBean(EventListener.class)
        @ConditionalOnMissingBean
        public MessagingConsumeCollector pulsarMessagingConsumeCollector(
                ObjectProvider<EventListener<?>> listeners,
                PulsarClient pulsarClient,
                TopicDecider topicDecider,
                ObjectProvider<GracefulShutter> gracefulShutter,
                MessagingProperties messagingProperties) {
            if (gracefulShutter.stream().count() > 1) {
                throw new IllegalStateException("More than one GracefulShutter found: "
                        + gracefulShutter.stream().count());
            }
            return new PulsarMessagingConsumeCollector(
                    filterListeners(listeners), pulsarClient, topicDecider, gracefulShutter, messagingProperties);
        }

        @SuppressWarnings("unchecked")
        private static List<EventListener<Object>> filterListeners(ObjectProvider<EventListener<?>> listeners) {
            List<EventListener<Object>> result = new ArrayList<>();
            listeners.stream()
                    .filter(lsn -> {
                        Consumer anno = AnnotationUtils.findAnnotation(lsn.getClass(), Consumer.class);
                        if (anno != null) {
                            return true;
                        }
                        log.warn(
                                "{} implements EventListener but not annotated with @Consumer",
                                lsn.getClass().getName());
                        return false;
                    })
                    .forEach(lsn -> result.add((EventListener<Object>) lsn));
            return result;
        }

        private static Env getStandardEnv(Environment environment) {
            try {
                return Env.fromEnvironment(environment);
            } catch (Exception e) {
                log.warn("Failed to determine environment, using {} as default", Env.LOCAL);
                return Env.LOCAL;
            }
        }
    }
}
