package com.moego.lib.messaging.pulsar;

import com.moego.lib.messaging.Consumer;
import com.moego.lib.messaging.InitialPosition;
import com.moego.lib.messaging.SubscribeType;
import com.moego.lib.messaging.TopicDecider;
import com.moego.lib.messaging.autoconfigure.MessagingProperties;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
public final class PulsarConsumerModelUtil {

    /**
     * Generate {@link PulsarConsumeModel} from {@link Consumer} and {@link MessagingProperties}.
     *
     * <p> Configurations in {@link MessagingProperties} have higher priority.
     *
     * @param consumer        consumer
     * @param properties      {@link MessagingProperties}
     * @param applicationName application name
     * @param topicDecider    {@link TopicDecider}
     * @return merged {@link PulsarConsumeModel}
     */
    public static PulsarConsumeModel merge(
            Consumer consumer, MessagingProperties properties, String applicationName, TopicDecider topicDecider) {
        Assert.notNull(consumer, "Consumer must not be null");
        Assert.notNull(properties, "MessagingProperties must not be null");
        if (!StringUtils.hasText(consumer.name()) && consumer.topics().length == 0) {
            throw new IllegalArgumentException("Consumer 'name' or 'topics' must not be empty");
        }

        MessagingProperties.Consumer configConsumer = StringUtils.hasText(consumer.name())
                ? properties.getConsumers().stream()
                        .filter(c -> Objects.equals(c.getName(), consumer.name()))
                        .findFirst()
                        .orElse(null)
                : null;

        List<String> configTopics = Optional.ofNullable(configConsumer)
                .map(MessagingProperties.Consumer::getTopics)
                .orElse(List.of());
        List<String> unsortedTopics =
                !CollectionUtils.isEmpty(configTopics) ? configTopics : List.of(consumer.topics());
        // [t1,t2] is same as [t2,t1], so sort it to make sure the same subscription name
        List<String> topics =
                unsortedTopics.stream().filter(StringUtils::hasText).sorted().toList();
        Assert.notEmpty(topics, "Topics must not be empty");

        Integer configThreadCount = Optional.ofNullable(configConsumer)
                .map(MessagingProperties.Consumer::getThreadCount)
                .orElse(null);
        int threadCount = configThreadCount != null ? configThreadCount : consumer.threadCount();

        InitialPosition configInitialPosition = Optional.ofNullable(configConsumer)
                .map(MessagingProperties.Consumer::getInitialPosition)
                .orElse(null);
        InitialPosition initialPosition =
                configInitialPosition != null ? configInitialPosition : consumer.initialPosition();

        SubscribeType configSubscribeType = Optional.ofNullable(configConsumer)
                .map(MessagingProperties.Consumer::getSubscribeType)
                .orElse(null);
        SubscribeType subscribeType = configSubscribeType != null ? configSubscribeType : consumer.subscribeType();

        String subscriptionName =
                switch (subscribeType) {
                    case ONCE -> String.format("%s", topics);
                    case ONCE_EACH_SERVICE -> String.format("%s-%s", topics, applicationName);
                    case BROADCAST -> String.format("%s-%s-%d", topics, applicationName, System.currentTimeMillis());
                    default -> throw new IllegalArgumentException("Unsupported event type: " + subscribeType);
                };

        String customerName = String.format("%s-%d", topics, System.currentTimeMillis());

        List<String> realTopics = topics.stream().map(topicDecider::determine).toList();

        return new PulsarConsumeModel(
                customerName, realTopics, subscriptionName, threadCount, initialPosition, subscribeType);
    }
}
