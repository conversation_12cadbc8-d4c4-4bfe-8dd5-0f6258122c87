package com.moego.lib.messaging;

import java.util.function.Supplier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

/**
 * {@link GracefulShutter} implementation that waits for a timeout before closing a {@link MessagingConsume}.
 *
 * <AUTHOR>
 */
public class TimeoutGracefulShutter implements GracefulShutter {

    private static final Logger log = LoggerFactory.getLogger(TimeoutGracefulShutter.class);

    private static final long INTERVAL = 100;

    /**
     * The Timeout, in milliseconds, less or equal to 0 means no timeout.
     */
    private final long timeout;

    public TimeoutGracefulShutter(long timeout) {
        Assert.isTrue(timeout > 0, "Timeout must be greater than 0");
        this.timeout = timeout;
    }

    @Override
    public void shutdown(Supplier<Boolean> finishCalculator) {
        // Wait for the consumer to finish processing.
        long timeout = this.timeout;

        while (finishCalculator.get() && timeout > 0) {
            try {
                Thread.sleep(INTERVAL);
            } catch (InterruptedException e) {
                // Ignore
            }
            timeout -= INTERVAL;
        }

        if (timeout <= 0) {
            // Already did our best, but the job still not finished.
            // DO NOT interrupt the thread, just log a warning,
            // maybe the MQ will do the graceful shutdown itself.
            log.warn("Graceful shutdown timeout, current event may be re-consumed");
        }
    }
}
