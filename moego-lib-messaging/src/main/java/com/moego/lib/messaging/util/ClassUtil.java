package com.moego.lib.messaging.util;

import com.moego.lib.messaging.EventListener;
import org.springframework.core.ResolvableType;

/**
 * Class util.
 *
 * <AUTHOR>
 */
public final class ClassUtil {

    private ClassUtil() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Get {@link EventListener}'s generic type.
     *
     * @param listener {@link EventListener}
     * @return event type
     */
    @SuppressWarnings("unchecked")
    public static <T> Class<T> getEventTypeForListener(EventListener<T> listener) {
        ResolvableType[] interfaces =
                ResolvableType.forClass(listener.getClass()).getInterfaces();
        for (ResolvableType anInterface : interfaces) {
            Class<?> rawClass = anInterface.getRawClass();
            if (rawClass != null && EventListener.class.isAssignableFrom(rawClass)) {
                Class<?> clz = anInterface.getGeneric(0).getRawClass();
                if (clz != null) {
                    return (Class<T>) clz;
                }
                // TODO(Freeman): deal with lambda
                // no generic type declared
                throw new IllegalStateException(
                        "EventListener must declare its generic type, e.g. class MyEventListener implements EventListener<MyEvent>");
            }
        }
        throw new IllegalStateException("Not implementation of EventListener: " + listener.getClass());
    }
}
