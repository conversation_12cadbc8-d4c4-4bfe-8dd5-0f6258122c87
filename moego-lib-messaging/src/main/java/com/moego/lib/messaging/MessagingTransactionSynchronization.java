package com.moego.lib.messaging;

import java.util.LinkedHashSet;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import org.springframework.core.OrderComparator;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import org.springframework.transaction.support.TransactionSynchronizationUtils;

/**
 * Messaging transaction synchronization.
 *
 * <p> No need to implement the {@link #getOrder()} method here, Spring will use {@link LinkedHashSet} to save {@link TransactionSynchronization}.
 * <p> Since the sorting algorithm used is stable, {@link TransactionSynchronization} with the same order will be executed in the order of addition.
 *
 * <AUTHOR>
 * @see TransactionSynchronizationUtils#triggerAfterCommit()
 * @see TransactionSynchronizationManager#getSynchronizations()
 * @see OrderComparator#sort(List)
 */
class MessagingTransactionSynchronization implements TransactionSynchronization {

    private final AtomicBoolean invoked = new AtomicBoolean(false);
    private final Runnable afterCommitRunnable;

    MessagingTransactionSynchronization(Runnable afterCommitRunnable) {
        this.afterCommitRunnable = afterCommitRunnable;
    }

    @Override
    public void afterCompletion(int status) {
        if (invoked.compareAndSet(false, true) && status == STATUS_COMMITTED) {
            afterCommitRunnable.run();
        }
    }
}
