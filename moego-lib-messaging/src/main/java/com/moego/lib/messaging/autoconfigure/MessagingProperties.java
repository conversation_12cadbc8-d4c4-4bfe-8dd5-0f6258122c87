package com.moego.lib.messaging.autoconfigure;

import static com.moego.lib.messaging.autoconfigure.MessagingProperties.PREFIX;

import com.moego.lib.messaging.InitialPosition;
import com.moego.lib.messaging.SubscribeType;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 * @since 2022/11/30
 */
@Data
@ConfigurationProperties(PREFIX)
public class MessagingProperties {

    public static final String PREFIX = "moego.messaging";

    /**
     * Whether to enable messaging.
     */
    private boolean enabled = true;

    /**
     * Only when producer and consumer are in the same group, the consumers can receive the messages.
     *
     * <p> It's very useful when you want to test the consumer in local environment, you can specify the producer and the consumer in the same group, so that the consumer can receive the messages.
     * <p color="orange"> <strong>NOTE: group only for test environments (test2/staging), it will be ignored in production environment!</strong>
     */
    private String testGroup;

    /**
     * The timeout for the graceful shutdown, less or equal to 0 means no timeout, default is 3000ms.
     *
     * <p> This is the time for waiting message processing to be completed.
     * <p> If this time is exceeded, whether the message can be processed is not our concern, it depends on the implementation of the MQ.
     */
    private long gracefulShutdownTimeout = 3000L;
    /**
     * Whether to enable automatic acknowledgment, default is {@code false}.
     */
    private boolean autoAck = false;

    private List<Consumer> consumers = new ArrayList<>();

    private Pulsar pulsar = new Pulsar();

    @Data
    public static class Pulsar {

        /**
         * Whether to enable pulsar messaging.
         */
        private boolean enabled = true;
        /**
         * The tenant name.
         */
        private String tenant;
        /**
         * Service URL for the Pulsar cluster.
         * <p> e.g. localhost:6650
         */
        private String serviceUrl;
        /**
         * Authentication token.
         */
        private String authentication;
    }

    @Data
    public static class Consumer {

        private String name;
        private List<String> topics;
        private Integer threadCount;
        private SubscribeType subscribeType;
        private InitialPosition initialPosition;
    }
}
