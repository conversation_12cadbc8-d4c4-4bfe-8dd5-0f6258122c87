package com.moego.lib.messaging.pulsar;

import com.moego.lib.messaging.InitialPosition;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.regex.Pattern;
import org.apache.pulsar.client.api.Consumer;
import org.apache.pulsar.client.api.ConsumerBuilder;
import org.apache.pulsar.client.api.PulsarClient;
import org.apache.pulsar.client.api.PulsarClientException;
import org.apache.pulsar.client.api.Schema;
import org.apache.pulsar.client.api.SubscriptionInitialPosition;
import org.apache.pulsar.client.api.SubscriptionType;

/**
 * Pulsar util class.
 *
 * <AUTHOR>
 */
public class PulsarUtil {

    public static Consumer<String> consumer(PulsarClient pulsarClient, PulsarConsumeModel model) {
        ConsumerBuilder<String> builder = pulsarClient.newConsumer(Schema.STRING);
        builder.consumerName(model.consumerName());
        builder.subscriptionName(model.subscriptionName());
        if (model.initialPosition() == InitialPosition.LATEST) {
            builder.subscriptionInitialPosition(SubscriptionInitialPosition.Latest);
        } else if (model.initialPosition() == InitialPosition.EARLIEST) {
            builder.subscriptionInitialPosition(SubscriptionInitialPosition.Earliest);
        } else {
            throw new IllegalArgumentException("Unsupported initial position: " + model.initialPosition());
        }
        builder.subscriptionType(SubscriptionType.Shared);
        List<String> topics = model.topics();
        if (topics.stream().anyMatch(topic -> topic.contains("*"))) {
            // only allow one topic when using wildcard
            if (topics.size() > 1) {
                throw new IllegalArgumentException("Only allow one topic when using wildcard: " + topics);
            }
            // grooming.appt.* -> grooming\.appt\..*
            String topic = topics.get(0).replace(".", "\\.").replace("*", ".*");
            builder.topicsPattern(Pattern.compile(topic));
        } else {
            builder.topics(topics);
        }
        builder.negativeAckRedeliveryDelay(1, TimeUnit.SECONDS);
        Consumer<String> consumer;
        try {
            consumer = builder.subscribe();
        } catch (PulsarClientException e) {
            throw new RuntimeException(e);
        }
        return consumer;
    }
}
