package com.moego.lib.messaging.pulsar;

import com.moego.lib.common.grey.grpc.Cache;
import org.apache.pulsar.client.api.Producer;
import org.apache.pulsar.client.api.PulsarClientException;

/**
 * <AUTHOR>
 * @since 2022/11/30
 */
public class ProducerCache extends Cache<Producer<String>> {

    @Override
    protected void afterRemove(Producer<String> item) {
        asyncDo(() -> {
            try {
                item.close();
            } catch (PulsarClientException e) {
                throw new RuntimeException(e);
            }
        });
    }
}
