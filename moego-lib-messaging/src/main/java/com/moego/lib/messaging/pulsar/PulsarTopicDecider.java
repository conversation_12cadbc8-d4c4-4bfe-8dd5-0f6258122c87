package com.moego.lib.messaging.pulsar;

import com.moego.lib.common.util.Env;
import com.moego.lib.messaging.TopicDecider;
import com.moego.lib.messaging.autoconfigure.MessagingProperties;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * Pulsar implementation of {@link TopicDecider}.
 *
 * <AUTHOR>
 */
public class PulsarTopicDecider implements TopicDecider {

    /**
     * Using messaging module, the default namespace if not specified.
     */
    private static final String DEFAULT_NAMESPACE = "default";

    private static final String DELIMITER = ":";

    /**
     * Topic format, persistent://{tenant}/{namespace}/{topic}
     * <p> tenant: environment, e.g. test2, staging, prod
     * <p> namespace: stay the same, 'service'
     * <p> topic: topic name
     */
    private static final String TOPIC_FORMAT = "persistent://%s/%s/%s";

    private final MessagingProperties properties;
    private final Env env;

    public PulsarTopicDecider(MessagingProperties properties, Env env) {
        this.properties = properties;
        this.env = env;
    }

    @Override
    public String determine(String topic) {
        String tenant = properties.getPulsar().getTenant();
        Assert.hasText(tenant, "Pulsar tenant must not be empty");

        String[] namespaceAndTopic = topic.split(DELIMITER);
        Assert.isTrue(namespaceAndTopic.length < 3, "Topic format error, e.g. <topic> or <namespace>:<topic>");

        String namespace = namespaceAndTopic.length == 2 ? namespaceAndTopic[0] : DEFAULT_NAMESPACE;
        String topicToUse = namespaceAndTopic.length == 2 ? namespaceAndTopic[1] : namespaceAndTopic[0];

        if (env != Env.PROD && StringUtils.hasText(properties.getTestGroup())) {
            topicToUse = properties.getTestGroup() + "-" + topicToUse;
        }
        return String.format(TOPIC_FORMAT, tenant, namespace, topicToUse);
    }
}
