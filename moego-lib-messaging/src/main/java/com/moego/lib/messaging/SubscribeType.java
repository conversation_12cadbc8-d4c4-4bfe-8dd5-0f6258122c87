package com.moego.lib.messaging;

/**
 * Subscribe type.
 *
 * <AUTHOR>
 */
public enum SubscribeType {
    /**
     * Cluster level event, only be consumed once.
     */
    ONCE,
    /**
     * Service level event, consumed once by each service.
     *
     * <p> This is the default value for the most cases.
     */
    ONCE_EACH_SERVICE,
    /**
     * Instance level event, consumed once by each instance.
     */
    BROADCAST,
}
