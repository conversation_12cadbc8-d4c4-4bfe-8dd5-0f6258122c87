package com.moego.lib.messaging;

import java.util.Map;

/**
 * Message abstraction.
 *
 * @param <T> event type
 */
public interface Msg<T> {
    /**
     * Get event from message.
     *
     * @return event
     */
    T getBody();

    /**
     * Get message headers.
     *
     * @return immutable headers
     */
    Map<String, String> getHeaders();

    /**
     * Acknowledge the message.
     */
    void ack();

    /**
     * Negative acknowledge the message.
     */
    void nack();
}
