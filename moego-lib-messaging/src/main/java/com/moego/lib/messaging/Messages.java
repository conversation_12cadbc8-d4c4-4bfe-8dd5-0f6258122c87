package com.moego.lib.messaging;

import lombok.experimental.UtilityClass;
import org.springframework.util.ClassUtils;

/**
 * Utility class for sending messages.
 *
 * <AUTHOR>
 */
@UtilityClass
public class Messages {

    private static final boolean hasSpringTx =
            ClassUtils.isPresent("org.springframework.transaction.support.TransactionSynchronizationManager", null);

    private static MessagingOperations messagingOperations;

    public static void setMessagingOperations(MessagingOperations messagingOperations) {
        Messages.messagingOperations = messagingOperations;
    }

    /**
     * Send an event to a topic.
     *
     * @param topic topic
     * @param event event
     */
    public static void send(String topic, Object event) {
        if (messagingOperations != null) {
            messagingOperations.send(topic, event);
        }
    }

    /**
     * Send an event to a topic asynchronously.
     *
     * @param topic topic
     * @param event event
     */
    public static void asyncSend(String topic, Object event) {
        if (messagingOperations != null) {
            messagingOperations.asyncSend(topic, event);
        }
    }

    /**
     * Transactional send an event to a topic.
     *
     * <p> If in a transaction, the event will be sent after the transaction is committed.
     * <p> If not in a transaction, the event will be sent immediately.
     *
     * @param topic topic
     * @param event event
     */
    public static void sendTx(String topic, Object event) {
        if (messagingOperations != null) {
            if (hasSpringTx) {
                SpringTx.addAfterCommitCallback(() -> send(topic, event));
            } else {
                send(topic, event);
            }
        }
    }

    /**
     * Transactional send an event to a topic asynchronously.
     *
     * <p> If in a transaction, the event will be sent asynchronously after the transaction is committed.
     * <p> If not in a transaction, the event will be async sent immediately.
     *
     * @param topic topic
     * @param event event
     */
    public static void asyncSendTx(String topic, Object event) {
        if (messagingOperations != null) {
            if (hasSpringTx) {
                SpringTx.addAfterCommitCallback(() -> asyncSend(topic, event));
            } else {
                asyncSend(topic, event);
            }
        }
    }
}
