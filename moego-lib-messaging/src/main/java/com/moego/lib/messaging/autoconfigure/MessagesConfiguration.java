package com.moego.lib.messaging.autoconfigure;

import com.moego.lib.messaging.Messages;
import com.moego.lib.messaging.MessagingOperations;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.context.annotation.Configuration;

/**
 * Messages configuration.
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@RequiredArgsConstructor
public class MessagesConfiguration implements SmartInitializingSingleton {

    private final ObjectProvider<MessagingOperations> messagingOperations;

    @Override
    public void afterSingletonsInstantiated() {
        MessagingOperations operations = messagingOperations.getIfUnique();
        if (operations != null) {
            Messages.setMessagingOperations(operations);
        }
    }
}
