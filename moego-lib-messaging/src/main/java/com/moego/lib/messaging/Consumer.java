package com.moego.lib.messaging;

import static java.lang.annotation.ElementType.TYPE;
import static java.lang.annotation.RetentionPolicy.RUNTIME;

import java.lang.annotation.Retention;
import java.lang.annotation.Target;
import org.springframework.core.annotation.AliasFor;
import org.springframework.stereotype.Component;

/**
 * Identify a consumer.
 *
 * <p> {@link Consumer} only used on {@link EventListener}.
 *
 * <AUTHOR>
 * @see EventListener
 */
@Retention(RUNTIME)
@Target(TYPE)
@Component
public @interface Consumer {
    /**
     * Topics to consume, alias for {@link #topics}.
     *
     * @return consumer name
     * @see #topics
     */
    @AliasFor("topics")
    String[] value() default {};

    /**
     * Consumer name.
     *
     * @return consumer name
     */
    String name() default "";

    /**
     * Topics to consume.
     *
     * <p> Support wildcard '*'.
     * <p> "grooming.appointment.*", means all topics start with "grooming.appointment."
     *
     * @return consumer name
     * @see #value
     */
    @AliasFor("value")
    String[] topics() default {};

    /**
     * Subscribe type, default is {@link SubscribeType#ONCE_EACH_SERVICE}.
     *
     * @return Subscribe type
     */
    SubscribeType subscribeType() default SubscribeType.ONCE_EACH_SERVICE;

    /**
     * Initial position to consume, default is {@link InitialPosition#LATEST}.
     *
     * @return initial position
     */
    InitialPosition initialPosition() default InitialPosition.LATEST;

    /**
     * Thread count to consume, default is 1.
     *
     * @return thread count
     */
    int threadCount() default 1;
}
