package com.moego.lib.messaging.pulsar;

import com.moego.lib.messaging.EventListener;
import com.moego.lib.messaging.GracefulShutter;
import com.moego.lib.messaging.MessagingConsume;
import com.moego.lib.messaging.util.ClassUtil;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicBoolean;
import org.apache.pulsar.client.api.Consumer;
import org.apache.pulsar.client.api.Message;
import org.apache.pulsar.client.api.PulsarClientException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectProvider;

/**
 * Pulsar implementation of {@link MessagingConsume}.
 *
 * <AUTHOR>
 */
public class PulsarMessagingConsume implements Runnable, MessagingConsume {

    private static final Logger log = LoggerFactory.getLogger(PulsarMessagingConsume.class);

    private static final ConcurrentMap<Class<?>, Integer> idMap = new ConcurrentHashMap<>();

    private final Consumer<String> consumer;
    private final EventListener<Object> listener;
    private final Class<?> eventType;
    /**
     * Mark application is processing message.
     */
    private final AtomicBoolean processing = new AtomicBoolean(false);
    /**
     * Mark application is shutting down.
     */
    private final AtomicBoolean stopping = new AtomicBoolean(false);
    /**
     * Optional gracefulShutter.
     */
    private final ObjectProvider<GracefulShutter> gracefulShutter;

    private final boolean autoAckEnabled;

    public PulsarMessagingConsume(
            Consumer<String> consumer,
            EventListener<Object> listener,
            ObjectProvider<GracefulShutter> gracefulShutter,
            boolean autoAck) {
        this.consumer = consumer;
        this.listener = listener;
        this.gracefulShutter = gracefulShutter;
        this.eventType = ClassUtil.getEventTypeForListener(listener);
        this.autoAckEnabled = autoAck;
    }

    @Override
    public void run() {
        while (true) {
            if (log.isTraceEnabled()) {
                log.trace("Receiving message from topic '{}'...", consumer.getTopic());
            }
            Message<String> msg;
            try {
                msg = consumer.receive();
                if (log.isTraceEnabled()) {
                    log.trace("Received message from topic: {}, message: {}", consumer.getTopic(), msg.getValue());
                }
            } catch (PulsarClientException.AlreadyClosedException e) {
                if (stopping.get()) {
                    // it's ok to receive this exception when shutdown
                    log.info("Consumer closed when shutdown phase, stop the consumer thread");
                } else {
                    log.warn("Consumer closed when non-shutdown phase, stop the consumer thread", e);
                }
                break;
            } catch (PulsarClientException.InvalidConfigurationException e) {
                log.warn("Invalid configuration, stop the consumer thread", e);
                break;
            } catch (PulsarClientException e) {
                log.warn(
                        "Occurred a unknown exception when receive message, should never happen, stop the consumer thread",
                        e);
                break;
            }

            if (stopping.get()) {
                // in shutdown phase, we don't want to process any more messages,
                // but may still have unprocessed messages in the receiver queue,
                // so we let the consumer continue to consume the message that in processing,
                // and negative ack the rest of the messages in the receiver queue.

                // NOTE: consumers of same subscription will re-consume the message!
                // So if the EventType is SERVICE, there will be no double consumption problem caused by negative ack of
                // other services.
                // see https://pulsar.apache.org/docs/2.11.x/concepts-messaging/#negative-acknowledgment
                consumer.negativeAcknowledge(msg);
                consumer.redeliverUnacknowledgedMessages();
                if (log.isDebugEnabled()) {
                    log.debug("Negative ack message: {}", msg.getMessageId());
                }
                continue;
            }

            // received a message, mark consumer as processing
            processing.set(true);

            PulsarMsg<Object> pulsarMsg = new PulsarMsg<>(msg, consumer, eventType);

            try {
                listener.onEvent(pulsarMsg);
            } catch (Exception e) {
                // can't throw exception here, otherwise the thread will be terminated
                log.error(
                        "Exception occurred when processing message, id: {}, body: {}",
                        msg.getMessageId(),
                        msg.getValue(),
                        e);
            }

            if (autoAckEnabled && !pulsarMsg.manuallyAcked()) {
                pulsarMsg.ack();
            }

            // mark consumer is not processing, this status is used by GracefulShutter
            processing.set(false);
        }
        log.info("Consumer thread stopped: {}", Thread.currentThread().getName());
    }

    @Override
    public void start() {
        Thread t = new Thread(this);
        t.setDaemon(false);
        t.setName(String.format(
                "%s-%d", listener.getClass().getSimpleName(), idMap.merge(listener.getClass(), 1, Integer::sum)));
        t.start();
    }

    @Override
    public void stop() {
        pause();

        gracefulShutter.ifUnique(shutter -> {
            long start = System.currentTimeMillis();

            if (log.isDebugEnabled()) {
                log.debug(
                        "Consumer {} is waiting for the message currently being processed to be completed",
                        consumer.getConsumerName());
            }
            shutter.shutdown(processing::get);
            if (log.isDebugEnabled()) {
                log.debug(
                        "Consumer {} finished after {} ms",
                        consumer.getConsumerName(),
                        System.currentTimeMillis() - start);
            }
        });
    }

    private void pause() {
        // stop receiving messages, but still process the messages in the receiver queue
        consumer.pause();

        // don't process any more messages, instead, negative ack them.
        stopping.compareAndSet(false, true);
    }
}
