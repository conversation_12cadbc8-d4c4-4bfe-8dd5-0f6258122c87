package com.moego.lib.messaging;

import java.util.function.Supplier;

/**
 * {@link GracefulShutter} implementation that waits forever util the job is done.
 *
 * <AUTHOR>
 */
public class NonTimeoutGracefulShutter implements GracefulShutter {

    private static final long INTERVAL = 100;

    @Override
    public void shutdown(Supplier<Boolean> finishCalculator) {
        while (finishCalculator.get()) {
            try {
                Thread.sleep(INTERVAL);
            } catch (InterruptedException e) {
                // Ignore
            }
        }
    }
}
