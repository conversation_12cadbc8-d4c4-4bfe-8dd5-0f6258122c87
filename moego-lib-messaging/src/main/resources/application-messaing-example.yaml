moego:
  messaging:
    enabled: true
    pulsar:
      service-url: mq.t2.moego.pet:40150
      authentication: <Please Ask Frank>
    graceful-shutdown-timeout: 3000
    test-group: feature-a
    auto-ack: false
    consumers:
      - name: consumer-1
        topics: [grooming.appointment.AddAppointmentEvent]
        subscribe-type: once_each_service
        initial-position: latest
        thread-count: 3
      - name: consumer-2
        topics: [grooming.appointment.*]
        subscribe-type: once_each_service
        initial-position: latest
        thread-count: 5
