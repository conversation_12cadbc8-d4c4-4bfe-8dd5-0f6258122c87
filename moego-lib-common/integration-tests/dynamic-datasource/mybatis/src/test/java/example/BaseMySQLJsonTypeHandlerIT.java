package example;

import static example.mapper.mysql.MoeGroomingPackageServiceDynamicSqlSupport.moeGroomingPackageService;
import static org.assertj.core.api.Assertions.assertThat;

import example.entity.mysql.MoeGroomingPackageService;
import example.entity.po.PackageService;
import example.mapper.mysql.MoeGroomingPackageServiceMapper;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

/**
 * {@link com.moego.lib.common.mybatis.typehandler.BaseMySQLJsonTypeHandler}
 */
@SpringBootTest
public class BaseMySQLJsonTypeHandlerIT {

    @Autowired
    MoeGroomingPackageServiceMapper packageServiceMapper;

    @Test
    @Transactional
    void testServices_Success() {
        var packageService = packageServiceMapper
                .selectOne(c -> c.orderBy(moeGroomingPackageService.id).limit(1))
                .orElseThrow();

        assertThat(packageService.getServices()).isNotEmpty();

        var updateBean = new MoeGroomingPackageService();
        updateBean.setId(packageService.getId());
        updateBean.setServices(List.of(new PackageService() {
            {
                setServiceId(1L);
                setUnitPrice(2d);
            }
        }));
        packageServiceMapper.updateByPrimaryKeySelective(updateBean);

        var updated =
                packageServiceMapper.selectByPrimaryKey(packageService.getId()).orElseThrow();
        assertThat(updated.getServices()).isEqualTo(updateBean.getServices());
    }
}
