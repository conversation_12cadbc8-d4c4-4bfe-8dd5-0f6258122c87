package example;

import static example.mapper.postgres.BookingRequestDynamicSqlSupport.bookingRequest;
import static org.assertj.core.api.Assertions.assertThat;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;

import com.github.pagehelper.page.PageMethod;
import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import com.moego.lib.common.autoconfigure.datasource.mybatis.MyBatisDynamicDataSourceBeanPostProcessor;
import example.entity.postgres.BookingRequest;
import example.mapper.postgres.BookingRequestMapper;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * <p> {@link MyBatisDynamicDataSourceBeanPostProcessor}
 * <p> {@link DynamicDataSource}
 */
@SpringBootTest
class MyBatisDynamicDataSourceIT {

    @Autowired
    BookingRequestMapper bookingRequestMapper;

    @Test
    void testUseDataSource_whenInsertDataToMaster_thenDataNotExistsInOtherDataSource() {

        var entity = buildBookingRequest();
        bookingRequestMapper.insertSelective(entity);
        var id = entity.getId();

        try {
            // Exists in main
            var bookingRequestInMain = bookingRequestMapper.selectByPrimaryKey(id);
            assertThat(bookingRequestInMain).isPresent();

            // Not exists in ds1
            var bookingRequestInDs1 = bookingRequestMapper.useDataSource("ds1").selectByPrimaryKey(id);
            assertThat(bookingRequestInDs1).isEmpty();

            // Not exists in ds2
            var bookingRequestInDs2 = bookingRequestMapper.useDataSource("ds2").selectByPrimaryKey(id);
            assertThat(bookingRequestInDs2).isEmpty();
        } finally {
            deleteByIds(bookingRequestMapper, List.of(id));
        }
    }

    @Test
    void testWorkWithPageHelper() {

        var idsForMain = insertBookingRequest(bookingRequestMapper, 5);
        var ds1Mapper = bookingRequestMapper.useDataSource("ds1");
        var idsForDs1 = insertBookingRequest(ds1Mapper, 5);
        try {
            try (var page = PageMethod.startPage(2, 2)) {
                bookingRequestMapper.select(c -> c.orderBy(bookingRequest.id.descending()));

                assertThat(page.getTotal()).isGreaterThan(2);
                assertThat(page.getResult()).hasSize(2);
            }

            try (var page = PageMethod.startPage(2, 2)) {
                ds1Mapper.select(c -> c.orderBy(bookingRequest.id.descending()));

                assertThat(page.getTotal()).isGreaterThan(2);
                assertThat(page.getResult()).hasSize(2);
            }
        } finally {
            deleteByIds(bookingRequestMapper, idsForMain);
            deleteByIds(ds1Mapper, idsForDs1);
        }
    }

    @Test
    void testUseDataSourceReturnNewInstance() {
        var ds1Mapper = bookingRequestMapper.useDataSource("ds1");
        var ds1Mapper2 = bookingRequestMapper.useDataSource("ds1");
        var ds2Mapper = bookingRequestMapper.useDataSource("ds2");

        // Create a new instance the first time use useDataSource.
        assertThat(ds1Mapper).isNotSameAs(ds2Mapper); // different data source
        assertThat(ds1Mapper).isNotSameAs(bookingRequestMapper);
        assertThat(ds1Mapper).isSameAs(ds1Mapper2); // same data source, multiple invocation
    }

    @Test
    void testNoDataSourceReturnSameInstance() {
        // no data source found, return the same instance
        var mapper = bookingRequestMapper.useDataSource("not-exist");
        assertThat(mapper).isSameAs(bookingRequestMapper);

        // data source found, return new instance
        var mapper2 = mapper.useDataSource("ds1");
        assertThat(mapper2).isNotSameAs(mapper);

        // no data source found, return the same instance
        var mapper3 = mapper2.useDataSource("not-exist-2");
        assertThat(mapper3).isSameAs(mapper2);
    }

    @Test
    void testSameDataSourceReturnSameInstance() {
        var mapper1 = bookingRequestMapper.useDataSource("ds1");
        var mapper2 = bookingRequestMapper.useDataSource("ds1").useDataSource("ds1");
        var mapper3 = bookingRequestMapper.useDataSource("not-exist").useDataSource("ds1");
        var mapper4 = bookingRequestMapper.useDataSource("ds1").useDataSource("not-exist"); // still use ds1
        assertThat(mapper1).isSameAs(mapper2).isSameAs(mapper3).isSameAs(mapper4);
        assertThat(mapper1).isNotSameAs(bookingRequestMapper);

        var mapper5 = bookingRequestMapper.useDataSource("not-exist");
        assertThat(mapper5).isSameAs(bookingRequestMapper);
    }

    private void deleteByIds(BookingRequestMapper bookingRequestMapper, List<Long> idsForMain) {
        if (idsForMain.isEmpty()) {
            return;
        }
        bookingRequestMapper.delete(c -> c.where(bookingRequest.id, isIn(idsForMain)));
    }

    private static List<Long> insertBookingRequest(BookingRequestMapper mapper, int count) {
        var ids = new ArrayList<Long>();
        for (int i = 0; i < count; i++) {
            var entity = buildBookingRequest();
            mapper.insertSelective(entity);
            ids.add(entity.getId());
        }
        return ids;
    }

    private static BookingRequest buildBookingRequest() {
        var entity = new BookingRequest();
        entity.setCompanyId(111L);
        entity.setBusinessId(111L);
        entity.setCustomerId(1111L);
        entity.setStartDate(LocalDate.now());
        entity.setStartTime(0);
        entity.setEndDate(LocalDate.now());
        entity.setEndTime(0);
        entity.setStatus(0);
        entity.setCreatedAt(LocalDateTime.now());
        entity.setUpdatedAt(LocalDateTime.now());
        return entity;
    }
}
