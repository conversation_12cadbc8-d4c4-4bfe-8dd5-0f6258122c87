package example;

import static example.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.moeGroomingAppointment;
import static org.assertj.core.api.Assertions.assertThat;

import example.entity.postgres.BookingRequest;
import example.mapper.postgres.BookingRequestMapper;
import example.models.BookingRequestModel;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

/**
 * {@link com.moego.lib.common.mybatis.typehandler.BasePostgresJsonbTypeHandler}
 */
@SpringBootTest
public class BasePostgresJsonbTypeHandlerIT {

    @Autowired
    BookingRequestMapper bookingRequestMapper;

    @Test
    @Transactional
    void testAttr_Success() {
        var bookingRequest = bookingRequestMapper
                .selectOne(c -> c.orderBy(moeGroomingAppointment.id).limit(1))
                .orElseThrow();

        assertThat(bookingRequest.getAttr()).isNotNull();

        var updateBean = new BookingRequest();
        updateBean.setId(bookingRequest.getId());
        updateBean.setAttr(
                BookingRequestModel.Attr.newBuilder().setIsNewVisitor(true).build());
        bookingRequestMapper.updateByPrimaryKeySelective(updateBean);

        var updated =
                bookingRequestMapper.selectByPrimaryKey(bookingRequest.getId()).orElseThrow();
        assertThat(updated.getAttr()).isEqualTo(updateBean.getAttr());
    }
}
