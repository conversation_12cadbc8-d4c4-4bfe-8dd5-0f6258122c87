package example;

import static example.mapper.mysql.MoeGroomingAppointmentDynamicSqlSupport.moeGroomingAppointment;
import static org.assertj.core.api.Assertions.assertThat;

import example.entity.mysql.MoeGroomingAppointment;
import example.mapper.mysql.MoeGroomingAppointmentMapper;
import example.models.AppointmentModel;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

/**
 * {@link com.moego.lib.common.mybatis.typehandler.BaseProtobufEnumTypeHandler}
 */
@SpringBootTest
public class BaseProtobufEnumTypeHandlerIT {

    @Autowired
    MoeGroomingAppointmentMapper appointmentMapper;

    @Test
    @Transactional
    void testStatus_Success() {
        var appointment = appointmentMapper
                .selectOne(c -> c.orderBy(moeGroomingAppointment.id).limit(1))
                .orElseThrow();

        assertThat(appointment.getStatus()).isNotNull();

        var updateBean = new MoeGroomingAppointment();
        updateBean.setId(appointment.getId());
        updateBean.setStatus(AppointmentModel.Status.CONFIRMED);
        appointmentMapper.updateByPrimaryKeySelective(updateBean);

        var updated = appointmentMapper.selectByPrimaryKey(appointment.getId()).orElseThrow();
        assertThat(updated.getStatus()).isEqualTo(updateBean.getStatus());
    }
}
