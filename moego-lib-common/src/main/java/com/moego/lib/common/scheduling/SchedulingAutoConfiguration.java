package com.moego.lib.common.scheduling;

import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.ScheduledAnnotationBeanPostProcessor;

@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(prefix = SchedulingProperties.PREFIX, name = "enabled", matchIfMissing = true)
@EnableConfigurationProperties(SchedulingProperties.class)
@ConditionalOnBean(ScheduledAnnotationBeanPostProcessor.class)
public class SchedulingAutoConfiguration {

    @Bean
    @ConditionalOnProperty(prefix = SchedulingProperties.PREFIX, name = "gen-request-id", matchIfMissing = true)
    public SchedulingGenerateRequestIdAspect schedulingGenerateRequestIdAspect() {
        return new SchedulingGenerateRequestIdAspect();
    }
}
