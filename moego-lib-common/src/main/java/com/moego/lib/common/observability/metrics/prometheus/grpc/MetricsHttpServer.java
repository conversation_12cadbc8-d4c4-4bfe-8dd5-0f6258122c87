package com.moego.lib.common.observability.metrics.prometheus.grpc;

import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.grpc.ServerStarter;
import io.prometheus.client.exporter.HTTPServer;
import io.prometheus.client.hotspot.DefaultExports;
import java.io.IOException;
import java.util.concurrent.atomic.AtomicBoolean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.context.SmartLifecycle;

/**
 * The built-in http server is specially used to provide prometheus monitoring indicators.
 *
 * <p> NOTE: we need to keep this server as simple as possible.
 *
 * <AUTHOR>
 * @since 2022/8/23
 */
public class MetricsHttpServer implements SmartLifecycle, ApplicationEventPublisherAware {

    private static final Logger log = LoggerFactory.getLogger(MetricsHttpServer.class);

    private HTTPServer server;
    private final GrpcProperties properties;
    private final ServerStarter serverStarter;
    private final AtomicBoolean running = new AtomicBoolean(false);

    private ApplicationEventPublisher publisher;

    public MetricsHttpServer(GrpcProperties properties, ServerStarter serverStarter) {
        this.properties = properties;
        this.serverStarter = serverStarter;
    }

    @Override
    public void start() {
        if (!serverStarter.isNeedStartup()) {
            log.info("no available gRPC service found, will not start a HTTP metrics server!");
            return;
        }
        if (isRunning()) {
            return;
        }
        DefaultExports.initialize();
        GrpcProperties.Observability.Metrics metrics =
                properties.getServer().getObservability().getMetrics();
        try {
            server = new HTTPServer(Math.max(metrics.getPort(), 0));
            running.set(true);
            publisher.publishEvent(new MetricsHttpServerStartedEvent(server));
            if (log.isInfoEnabled()) {
                log.info("Metrics http server started on port: {}", server.getPort());
            }
        } catch (IOException e) {
            throw new IllegalStateException("start metrics http server failed!", e);
        }
    }

    @Override
    public void stop() {
        if (isRunning()) {
            server.close();
            if (log.isInfoEnabled()) {
                log.info("Metrics HttpServer stopped");
            }
        }
    }

    @Override
    public boolean isRunning() {
        return running.get();
    }

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.publisher = applicationEventPublisher;
    }
}
