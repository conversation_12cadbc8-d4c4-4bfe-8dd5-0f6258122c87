package com.moego.lib.common.grpc.health;

import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import io.grpc.health.v1.HealthCheckRequest;
import io.grpc.health.v1.HealthCheckResponse.ServingStatus;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import javax.sql.DataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.util.Assert;

/**
 * Detects the health of data source.
 *
 * <AUTHOR>
 */
public class DataSourceHealthDetector implements HealthDetector, BeanFactoryAware, SmartInitializingSingleton {

    private static final Logger log = LoggerFactory.getLogger(DataSourceHealthDetector.class);

    private BeanFactory beanFactory;
    private final List<DataSource> dataSources = new ArrayList<>();
    private final GrpcProperties grpcProperties;

    public DataSourceHealthDetector(GrpcProperties grpcProperties) {
        Assert.notNull(grpcProperties, "grpcProperties can't be null");
        this.grpcProperties = grpcProperties;
    }

    @Override
    public ServingStatus check(HealthCheckRequest request) {
        String validationQuerySql =
                grpcProperties.getServer().getHealth().getDataSource().getValidationQuery();

        for (DataSource dataSource : dataSources) {
            try (Connection conn = dataSource.getConnection();
                    PreparedStatement statement = conn.prepareStatement(validationQuerySql)) {
                statement.executeQuery();
            } catch (SQLException e) {
                log.warn("DataSource health check failed!", e);
                return ServingStatus.NOT_SERVING;
            }
        }

        return ServingStatus.SERVING;
    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }

    @Override
    public void afterSingletonsInstantiated() {
        // NOTE: we don't want to change the order of bean initialization, so we use this method to get the bean
        // NOT inject the bean in the constructor
        beanFactory.getBeanProvider(DataSource.class).forEach(dataSources::add);
    }
}
