package com.moego.lib.common.http.health;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import javax.sql.DataSource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * TODO(<PERSON>): use actuator/health instead.
 *
 * <AUTHOR>
 * @since 2021/1/14 3:43 PM
 */
@RestController
@Slf4j
public class InitController {

    private final Environment env;

    @Autowired
    private Optional<DataSource> dataSource;

    public InitController(Environment env) {
        this.env = env;
    }

    private static final int HEALTH_CHECK_DEFAULT = 0;
    private static final int HEALTH_CHECK_DATASOURCE = 1;

    private static boolean checkMaskBit(int mask, int bit) {
        return bit == (mask & bit);
    }

    @GetMapping("/healthCheck")
    public String healthCheck(@RequestParam(value = "healthMask", defaultValue = "0") Integer healthMask)
            throws Exception {
        try {
            if (checkMaskBit(healthMask, HEALTH_CHECK_DATASOURCE)) {
                if (!checkDataSource()) {
                    throw new Exception("check datasource connection failed !");
                }
            }
        } catch (Exception e) {
            throw new Exception("health check failed !");
        }

        return "ok";
    }

    private boolean checkDataSource() throws SQLException {
        AtomicBoolean isHealthy = new AtomicBoolean(true);
        if (dataSource.isPresent()) {
            try (Connection connection = dataSource.get().getConnection()) {
                isHealthy.set(connection.isValid(1));
            }
        }
        return isHealthy.get();
    }

    @GetMapping("/checkProfile")
    public String[] afterUpdate() {
        return env.getActiveProfiles();
    }
}
