package com.moego.lib.common.autoconfigure.config;

import java.util.List;
import java.util.Set;
import org.apache.commons.logging.Log;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.boot.logging.DeferredLogFactory;
import org.springframework.core.env.ConfigurableEnvironment;

/**
 * Remove {@link #PROXY_PROPERTIES proxy properties} when {@link #REMOVE_PROXY_PROPERTIES} is set to {@code true} in the environment.
 *
 * <AUTHOR>
 * @see <a href="https://docs.oracle.com/javase/6/docs/technotes/guides/net/proxies.html">Java Networking and Proxies</a>
 */
public class RemoveProxyPropertiesEnvironmentPostProcessor implements EnvironmentPostProcessor {

    public static final String REMOVE_PROXY_PROPERTIES = "REMOVE_PROXY_PROPERTIES";
    /**
     * @see <a href="https://docs.oracle.com/javase/6/docs/technotes/guides/net/proxies.html">Java Networking and Proxies</a>
     */
    public static final Set<String> PROXY_PROPERTIES = Set.of(
            "http.proxyHost",
            "http.proxyPort",
            "https.proxyHost",
            "https.proxyPort",
            "socksProxyHost",
            "socksProxyPort");

    private final Log log;

    public RemoveProxyPropertiesEnvironmentPostProcessor(DeferredLogFactory logFactory) {
        this.log = logFactory.getLog(getClass());
    }

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        String removeProxyProperties = environment.getProperty(REMOVE_PROXY_PROPERTIES, String.class, "false");
        if ("true".equalsIgnoreCase(removeProxyProperties)) {
            removeProxyProperties();
        }
    }

    /**
     * visible for testing
     */
    void removeProxyProperties() {
        List<String> removedProperties = PROXY_PROPERTIES.stream()
                .filter(System.getProperties()::containsKey)
                .toList();

        removedProperties.forEach(System::clearProperty);

        if (log.isInfoEnabled()) {
            log.info("Removed proxy system properties: " + removedProperties);
        }
    }
}
