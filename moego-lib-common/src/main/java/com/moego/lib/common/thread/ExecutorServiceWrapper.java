package com.moego.lib.common.thread;

import java.util.Collection;
import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * Wrap {@link ExecutorService} to transfer {@link ThreadLocal} context when switching threads.
 *
 * <AUTHOR>
 */
public class ExecutorServiceWrapper implements ExecutorService {

    private final ExecutorService delegate;

    private ExecutorServiceWrapper(ExecutorService executorService) {
        this.delegate = executorService;
    }

    public static ExecutorService wrap(ExecutorService executorService) {
        return new ExecutorServiceWrapper(executorService);
    }

    @Override
    public void execute(Runnable command) {
        delegate.execute(wrap(command));
    }

    @Override
    public Future<?> submit(Runnable task) {
        return delegate.submit(wrap(task));
    }

    @Override
    public <T> Future<T> submit(Runnable task, T result) {
        return delegate.submit(wrap(task), result);
    }

    @Override
    public void shutdown() {
        delegate.shutdown();
    }

    @Override
    public List<Runnable> shutdownNow() {
        return delegate.shutdownNow();
    }

    @Override
    public boolean isShutdown() {
        return delegate.isShutdown();
    }

    @Override
    public boolean isTerminated() {
        return delegate.isTerminated();
    }

    @Override
    public boolean awaitTermination(long timeout, TimeUnit unit) throws InterruptedException {
        return delegate.awaitTermination(timeout, unit);
    }

    @Override
    public <T> Future<T> submit(Callable<T> task) {
        return delegate.submit(wrap(task));
    }

    @Override
    public <T> T invokeAny(Collection<? extends Callable<T>> tasks) throws InterruptedException, ExecutionException {
        return delegate.invokeAny(
                tasks.stream().map(ExecutorServiceWrapper::wrap).toList());
    }

    @Override
    public <T> T invokeAny(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit)
            throws InterruptedException, ExecutionException, TimeoutException {
        return delegate.invokeAny(
                tasks.stream().map(ExecutorServiceWrapper::wrap).toList(), timeout, unit);
    }

    @Override
    public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks) throws InterruptedException {
        return delegate.invokeAll(
                tasks.stream().map(ExecutorServiceWrapper::wrap).toList());
    }

    @Override
    public <T> List<Future<T>> invokeAll(Collection<? extends Callable<T>> tasks, long timeout, TimeUnit unit)
            throws InterruptedException {
        return delegate.invokeAll(
                tasks.stream().map(ExecutorServiceWrapper::wrap).toList(), timeout, unit);
    }

    public ExecutorService unwrap() {
        return delegate;
    }

    private static RunHandler wrap(Runnable runnable) {
        if (runnable instanceof RunHandler rh) {
            return rh;
        }
        return new RunHandler(runnable, false);
    }

    private static <V> CallHandler<V> wrap(Callable<V> callable) {
        if (callable instanceof CallHandler<V> ch) {
            return ch;
        }
        return new CallHandler<>(callable);
    }
}
