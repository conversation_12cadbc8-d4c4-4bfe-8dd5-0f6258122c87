package com.moego.lib.common.json.jackson.protobuf;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.google.protobuf.Message;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.util.JsonFormat;
import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import org.springframework.util.ReflectionUtils;

/**
 * <AUTHOR>
 * @since 2025/4/19
 */
public final class ProtobufMessage {

    /**
     * Protobuf message serializer, use {@link JsonFormat#printer()} to serialize protobuf message.
     *
     * @param <T> protobuf message type
     */
    public static final class Serializer<T extends MessageOrBuilder> extends JsonSerializer<T> {

        private static final JsonFormat.Printer printer =
                JsonFormat.printer().omittingInsignificantWhitespace().includingDefaultValueFields();

        @Override
        public void serialize(T value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeRawValue(printer.print(value));
        }
    }

    /**
     * Protobuf message deserializer, use {@link JsonFormat#parser()} to deserialize protobuf message.
     *
     * @param <T> protobuf message type
     */
    public static final class Deserializer<T extends MessageOrBuilder> extends JsonDeserializer<T> {

        private static final JsonFormat.Parser parser = JsonFormat.parser().ignoringUnknownFields();

        private final Class<T> clazz;

        public Deserializer(Class<T> clazz) {
            this.clazz = clazz;
        }

        @Override
        public T deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {

            var treeNode = p.readValueAsTree();

            String json = treeNode.toString();

            var newBuilderMethod = ReflectionUtils.findMethod(clazz, "newBuilder");
            if (newBuilderMethod == null) {
                throw new IllegalStateException("No newBuilder method found for class " + clazz);
            }

            try {
                var builder = (Message.Builder) newBuilderMethod.invoke(null);

                parser.merge(json, builder);

                @SuppressWarnings("unchecked")
                T result = (T) builder.build();

                return result;
            } catch (IllegalAccessException | InvocationTargetException e) {
                throw new IllegalStateException("Failed to deserialize protobuf message", e);
            }
        }
    }
}
