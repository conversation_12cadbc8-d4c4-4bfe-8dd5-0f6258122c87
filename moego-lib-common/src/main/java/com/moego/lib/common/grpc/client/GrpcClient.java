package com.moego.lib.common.grpc.client;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @since 2022/10/11
 * @deprecated since 2023/4/17 by <PERSON>, prefer to use {@link Autowired}.
 */
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
@Deprecated
public @interface GrpcClient {
    /**
     * @return gRPC client name.
     */
    String value();
}
