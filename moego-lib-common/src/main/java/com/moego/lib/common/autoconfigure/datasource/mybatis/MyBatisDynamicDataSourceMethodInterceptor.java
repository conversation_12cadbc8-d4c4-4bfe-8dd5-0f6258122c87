package com.moego.lib.common.autoconfigure.datasource.mybatis;

import com.github.pagehelper.PageInterceptor;
import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Objects;
import javax.sql.DataSource;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.aop.ProxyMethodInvocation;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.aop.framework.ProxyFactory;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.util.ClassUtils;
import org.springframework.util.ReflectionUtils;

/**
 * <AUTHOR>
 * @since 2024/12/13
 */
final class MyBatisDynamicDataSourceMethodInterceptor implements MethodInterceptor {
    private static final Logger log = LoggerFactory.getLogger(MyBatisDynamicDataSourceMethodInterceptor.class);

    private static final boolean pageHelperPresent = ClassUtils.isPresent(
            "com.github.pagehelper.PageInterceptor", MyBatisDynamicDataSourceMethodInterceptor.class.getClassLoader());

    private final Object originClient;
    private final ApplicationContext ctx;
    private final Class<?> mapperInterface;

    public MyBatisDynamicDataSourceMethodInterceptor(Object originMapper, ApplicationContext ctx) {
        this.originClient = originMapper;
        this.mapperInterface = AopProxyUtils.proxiedUserInterfaces(originMapper)[0];
        this.ctx = ctx;
    }

    @Override
    public Object invoke(MethodInvocation invocation) throws Throwable {
        Method method = invocation.getMethod();
        if (Objects.equals(method, DynamicDataSource.useDataSourceMethod)
                && invocation instanceof ProxyMethodInvocation pmi) {
            return getCachedMapper(pmi);
        }

        ReflectionUtils.makeAccessible(method);

        try {
            return method.invoke(originClient, invocation.getArguments());
        } catch (InvocationTargetException e) {
            throw e.getTargetException();
        }
    }

    private Object getCachedMapper(ProxyMethodInvocation invocation) {
        var datasourceName = (String) invocation.getArguments()[0];

        // quick check if there is cached mapper
        var cachedMapper = MyBatisCache.getMapper(mapperInterface, datasourceName);
        if (cachedMapper != null) {
            return cachedMapper;
        }

        DataSource dataSource;
        try {
            dataSource = ctx.getBean(datasourceName, DataSource.class);
        } catch (BeansException e) {
            log.warn(
                    "No such datasource: {}, available datasource(s): {}",
                    datasourceName,
                    ctx.getBeanNamesForType(DataSource.class));
            // no such datasource, return current proxy, means do nothing
            return invocation.getProxy();
        }

        var mybatisAutoConfiguration =
                ctx.getBeanProvider(MybatisAutoConfiguration.class).getIfUnique();
        if (mybatisAutoConfiguration == null) {
            log.warn(
                    "No MybatisAutoConfiguration found, you need to add 'mybatis-spring-boot-starter' to your dependencies.");
            // no mybatis-spring-boot-starter, should not happen
            // return current proxy, means do nothing
            return invocation.getProxy();
        }

        if (mybatisAutoConfiguration.getClass().getName().contains(ClassUtils.CGLIB_CLASS_SEPARATOR)) {
            log.error(
                    "MybatisAutoConfiguration is a CGLIB proxy, because the @Configuration is not annotated without `proxyBeanMethods = false`. To avoid this issue, please upgrade to mybatis-spring-boot-starter 3.0.2 or later.");
        }

        // Creates SqlSession/Mapper using the specified DataSource, and caches them.

        var sqlSessionTemplate = MyBatisCache.getSqlSession(mapperInterface, datasourceName, () -> {
            SqlSessionFactory sqlSessionFactory;
            try {
                sqlSessionFactory = mybatisAutoConfiguration.sqlSessionFactory(dataSource);
            } catch (Exception e) {
                throw new IllegalStateException("Failed to create SqlSessionFactory", e);
            }
            if (pageHelperPresent && noPageInterceptor(sqlSessionFactory.getConfiguration())) {
                // see com.github.pagehelper.autoconfigure.PageHelperAutoConfiguration.afterPropertiesSet
                sqlSessionFactory.getConfiguration().addInterceptor(new PageInterceptor());
            }
            return mybatisAutoConfiguration.sqlSessionTemplate(sqlSessionFactory);
        });

        return MyBatisCache.getMapper(mapperInterface, datasourceName, () -> {
            var configuration = sqlSessionTemplate.getConfiguration();
            // see org.mybatis.spring.mapper.MapperFactoryBean.checkDaoConfig
            if (!configuration.hasMapper(mapperInterface)) {
                configuration.addMapper(mapperInterface);
            }
            var newMapper = sqlSessionTemplate.getMapper(mapperInterface);
            return createProxy(newMapper, ctx);
        });
    }

    private static boolean noPageInterceptor(Configuration configuration) {
        return configuration.getInterceptors().stream()
                .noneMatch(e -> e.getClass().equals(PageInterceptor.class));
    }

    public static Object createProxy(Object originMapper, ApplicationContext ctx) {
        var interfaces = AopProxyUtils.proxiedUserInterfaces(originMapper);
        var proxyFactory = new ProxyFactory(interfaces);
        proxyFactory.addAdvice(new MyBatisDynamicDataSourceMethodInterceptor(originMapper, ctx));
        return proxyFactory.getProxy();
    }
}
