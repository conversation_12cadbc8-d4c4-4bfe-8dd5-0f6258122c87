package com.moego.lib.common.util;

import com.moego.lib.common.thread.ExecutorServiceWrapper;
import java.time.Duration;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import lombok.experimental.UtilityClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.lang.Nullable;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

/**
 * <AUTHOR>
 */
@UtilityClass
public class ThreadPoolUtil {

    private static final Logger log = LoggerFactory.getLogger(ThreadPoolUtil.class);

    /**
     * Default shutdown timeout.
     */
    private static final Duration SHUTDOWN_TIMEOUT = Duration.ofSeconds(5);

    // ==============================
    // Default thread pool settings
    // ==============================
    private static final Duration KEEP_ALIVE_TIME = Duration.ofSeconds(60);
    private static final int QUEUE_SIZE = 200;
    private static final RejectedExecutionHandler DEFAULT_HANDLER = new ThreadPoolExecutor.AbortPolicy();

    /**
     * Gracefully shutdown an executor service.
     *
     * @param executor the executor to shut down
     * @param timeout  the timeout for the shutdown
     */
    public static void shutdown(@Nullable ExecutorService executor, Duration timeout) {
        if (executor == null) {
            return;
        }
        executor.shutdown();
        try {
            if (!executor.awaitTermination(timeout.toMillis(), TimeUnit.MILLISECONDS)) {
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            log.warn("Interrupted while waiting for executor to shutdown", e);
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * Shutdown an executor service with a default timeout ({@link #SHUTDOWN_TIMEOUT}).
     *
     * @param executor the executor to shut down
     * @see #shutdown(ExecutorService, Duration)
     */
    public static void shutdown(ExecutorService executor) {
        shutdown(executor, SHUTDOWN_TIMEOUT);
    }

    /**
     * Factory method for creating a new thread pool.
     *
     * @param corePoolSize     the number of threads to keep in the pool, even if they are idle
     * @param threadNamePrefix thread name prefix
     * @return warped thread pool
     * @see #newExecutorService(int, int, Duration, int, String, RejectedExecutionHandler)
     */
    public static ExecutorService newExecutorService(int corePoolSize, String threadNamePrefix) {
        return newExecutorService(
                corePoolSize,
                Math.max(corePoolSize, Runtime.getRuntime().availableProcessors() * 2),
                KEEP_ALIVE_TIME,
                QUEUE_SIZE,
                threadNamePrefix);
    }

    /**
     * Factory method for creating a new thread pool.
     *
     * @param corePoolSize     the number of threads to keep in the pool, even if they are idle
     * @param maximumPoolSize  the maximum number of threads to allow in the pool
     * @param keepAliveTime    when the number of threads is greater than the core, this is the maximum time that excess idle threads will wait for new tasks before terminating.
     * @param queueCapacity    queue capacity
     * @param threadNamePrefix thread name prefix
     * @return wrapped thread pool
     * @see ExecutorServiceWrapper
     * @see #newExecutorService(int, int, Duration, int, String, RejectedExecutionHandler)
     */
    public static ExecutorService newExecutorService(
            int corePoolSize, int maximumPoolSize, Duration keepAliveTime, int queueCapacity, String threadNamePrefix) {
        return newExecutorService(
                corePoolSize, maximumPoolSize, keepAliveTime, queueCapacity, threadNamePrefix, DEFAULT_HANDLER);
    }

    /**
     * Factory method for creating a new thread pool.
     *
     * @param corePoolSize     the number of threads to keep in the pool, even if they are idle
     * @param maximumPoolSize  the maximum number of threads to allow in the pool
     * @param keepAliveTime    when the number of threads is greater than the core, this is the maximum time that excess idle threads will wait for new tasks before terminating.
     * @param queueCapacity    queue capacity
     * @param threadNamePrefix thread name prefix
     * @param rejectedHandler  rejected execution handler
     * @return wrapped thread pool
     * @see ExecutorServiceWrapper
     */
    public static ExecutorService newExecutorService(
            int corePoolSize,
            int maximumPoolSize,
            Duration keepAliveTime,
            int queueCapacity,
            String threadNamePrefix,
            RejectedExecutionHandler rejectedHandler) {
        ThreadPoolExecutor executor = new ThreadPoolExecutor(
                corePoolSize,
                maximumPoolSize,
                keepAliveTime.toMillis(),
                TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<>(queueCapacity),
                new CustomizableThreadFactory(threadNamePrefix),
                rejectedHandler);
        return ExecutorServiceWrapper.wrap(executor);
    }
}
