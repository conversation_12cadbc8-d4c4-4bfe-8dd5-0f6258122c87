package com.moego.lib.common.grpc.health;

import static io.grpc.health.v1.HealthCheckResponse.ServingStatus.SERVING;

import io.grpc.health.v1.HealthCheckRequest;
import io.grpc.health.v1.HealthCheckResponse;
import io.grpc.health.v1.HealthGrpc;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * Grpc health check basic implementation.
 *
 * <AUTHOR>
 * @since 2022/11/7
 */
public class HealthChecker extends HealthGrpc.HealthImplBase {

    private final List<HealthDetector> healthDetectors;

    private static final String liveness = "liveness";
    private static final String readiness = "readiness";
    private static final String startup = "startup";

    public HealthChecker(List<HealthDetector> healthDetectors) {
        this.healthDetectors = Optional.ofNullable(healthDetectors).orElse(new ArrayList<>());
    }

    @Override
    public void check(HealthCheckRequest request, StreamObserver<HealthCheckResponse> responseObserver) {
        HealthCheckResponse.ServingStatus result =
                switch (request.getService()) {
                    case startup -> startupCheck(request) ? SERVING : HealthCheckResponse.ServingStatus.NOT_SERVING;
                    case liveness -> livenessCheck() ? SERVING : HealthCheckResponse.ServingStatus.NOT_SERVING;
                    case readiness, "" -> readinessCheck(request)
                            ? SERVING
                            : HealthCheckResponse.ServingStatus.NOT_SERVING;
                    default -> SERVING;
                };

        responseObserver.onNext(
                HealthCheckResponse.newBuilder().setStatus(result).build());
        responseObserver.onCompleted();
    }

    private static boolean livenessCheck() {
        return true;
    }

    private boolean readinessCheck(HealthCheckRequest request) {
        for (HealthDetector detector : healthDetectors) {
            HealthCheckResponse.ServingStatus status = detector.check(request);
            if (status != SERVING) {
                // oops, we have a problem
                return false;
            }
        }
        return true;
    }

    private boolean startupCheck(HealthCheckRequest request) {
        for (HealthDetector detector : healthDetectors) {
            HealthCheckResponse.ServingStatus status = detector.check(request);
            if (status != SERVING) {
                // oops, we have a problem
                return false;
            }
        }
        return true;
    }

    @Override
    public void watch(HealthCheckRequest request, StreamObserver<HealthCheckResponse> responseObserver) {
        // should not be called
        super.watch(request, responseObserver);
    }
}
