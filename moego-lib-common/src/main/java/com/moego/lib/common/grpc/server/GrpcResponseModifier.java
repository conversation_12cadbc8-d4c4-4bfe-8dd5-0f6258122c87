package com.moego.lib.common.grpc.server;

import io.grpc.Metadata;
import java.util.ArrayList;
import java.util.List;
import java.util.function.Consumer;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class GrpcResponseModifier {

    private final List<Consumer<Metadata>> metadataActions = new ArrayList<>();

    /**
     * Add a metadata action.
     *
     * @param action consumer
     */
    public void addMetadataAction(Consumer<Metadata> action) {
        metadataActions.add(action);
    }
}
