package com.moego.lib.common.scheduling;

import com.moego.lib.common.observability.tracing.TracingContext;
import com.moego.lib.common.observability.tracing.TracingMetadataProcessor;
import com.moego.lib.common.thread.MetadataContext;
import com.moego.lib.common.thread.ThreadContext;
import com.moego.lib.common.thread.ThreadContextHolder;
import java.util.Map;
import java.util.UUID;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;

/**
 * <AUTHOR>
 */
@Aspect
public class SchedulingGenerateRequestIdAspect {

    @Pointcut("@annotation(org.springframework.scheduling.annotation.Scheduled)")
    public void pointcut() {}

    @Around("pointcut()")
    public Object around(ProceedingJoinPoint pjp) throws Throwable {
        ThreadContext tc = ThreadContextHolder.get();
        if (tc == null) {
            ThreadContextHolder.set(buildThreadContext());
        }
        try {
            return pjp.proceed();
        } finally {
            ThreadContextHolder.remove();
        }
    }

    private static ThreadContext buildThreadContext() {
        TracingMetadataProcessor processor = new TracingMetadataProcessor();
        TracingContext t = processor.build(
                Map.of(TracingContext.HK_REQUEST_ID, UUID.randomUUID().toString()));
        MetadataContext mc = new MetadataContext();
        mc.put(TracingContext.class, t);
        return new ThreadContext(mc);
    }
}
