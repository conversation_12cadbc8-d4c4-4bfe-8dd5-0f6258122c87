package com.moego.lib.common.observability.metrics.prometheus.http;

import io.prometheus.client.servlet.jakarta.exporter.MetricsServlet;
import org.springframework.boot.web.servlet.ServletRegistrationBean;

/**
 * Register endpoint '/metrics' for Prometheus.
 *
 * <AUTHOR>
 * @since 2022/10/12
 */
public class HttpMetricsServletRegistrationBean extends ServletRegistrationBean<MetricsServlet> {

    public HttpMetricsServletRegistrationBean() {
        super(new MetricsServlet(), "/metrics");
    }
}
