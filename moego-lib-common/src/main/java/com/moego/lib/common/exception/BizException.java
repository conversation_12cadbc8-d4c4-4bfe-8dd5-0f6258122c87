/*
 * @since 2022-06-22 20:38:04
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.lib.common.exception;

import com.moego.lib.common.observability.tracing.TracingContext;
import java.util.Optional;
import lombok.Getter;
import lombok.ToString;

@Getter
@ToString
public class BizException extends RuntimeException {

    private final Integer code;
    private final String message;
    private final String xRequestId;
    private final Long timestamp;
    private final Object data;
    private final String causedBy;

    /**
     * Please do not use this constructor directly, use {@link ExceptionUtil} instead.
     *
     * @param code code
     */
    public BizException(Integer code) {
        this(code, null, null, (String) null);
    }

    /**
     * Please do not use this constructor directly, use {@link ExceptionUtil} )} instead.
     *
     * @param code    code
     * @param message message
     */
    public BizException(Integer code, String message) {
        this(code, message, null, (String) null);
    }

    /**
     * Please do not use this constructor directly, use {@link ExceptionUtil} )} instead.
     *
     * @param code    code
     * @param message message
     * @param data    data
     */
    public BizException(Integer code, String message, Object data) {
        this(code, message, data, (String) null);
    }

    /**
     * Please do not use this constructor directly, use {@link ExceptionUtil} )} instead.
     *
     * @param code      code
     * @param throwable cause
     */
    public BizException(Integer code, Throwable throwable) {
        this(code, null, null, throwable);
    }

    /**
     * Please do not use this constructor directly, use {@link ExceptionUtil} )} instead.
     *
     * @param code      code
     * @param throwable cause
     * @param message   message
     */
    public BizException(Integer code, Throwable throwable, String message) {
        this(code, message, null, throwable);
    }

    /**
     * Please do not use this constructor directly, use {@link ExceptionUtil} )} instead.
     *
     * @param code    code
     * @param message message
     * @param data    data
     * @param cause   cause
     */
    public BizException(Integer code, String message, Object data, Throwable cause) {
        super(message, cause);
        this.code = code;
        this.message = message;
        this.xRequestId = Optional.ofNullable(TracingContext.get())
                .map(TracingContext::getRequestId)
                .orElse(null);
        this.timestamp = System.currentTimeMillis();
        this.data = data;
        this.causedBy = Optional.ofNullable(cause).map(Throwable::toString).orElse(null);
    }

    /**
     * Please do not use this constructor directly, use {@link ExceptionUtil} )} instead.
     *
     * @param code    code
     * @param message message
     * @param data    data
     * @param cause   cause
     */
    public BizException(Integer code, String message, Object data, String cause) {
        super(message);
        this.code = code;
        this.message = message;
        this.xRequestId = Optional.ofNullable(TracingContext.get())
                .map(TracingContext::getRequestId)
                .orElse(null);
        this.timestamp = System.currentTimeMillis();
        this.data = data;
        this.causedBy = cause;
    }
}
