/*
 * @since 2022-04-29 18:48:08
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.lib.common.thread;

import java.util.Objects;
import java.util.concurrent.Callable;
import lombok.extern.slf4j.Slf4j;

@Slf4j
class CallHandler<V> implements Callable<V> {

    private final Callable<V> delegate;
    private final ThreadContext context;

    CallHandler(Callable<V> callable) {
        this.delegate = callable;
        this.context = ThreadContextHolder.get();
    }

    @Override
    public V call() throws Exception {
        boolean sameThread = ThreadContextHolder.get() == context;
        try {
            if (!sameThread) {
                ThreadContextHolder.set(context);
            }
            return delegate.call();
        } catch (RuntimeException e) {
            // The error stack information will be swallowed, so we need to print
            log.error(
                    "CallHand<PERSON> caught RuntimeException in thread '{}': ",
                    Thread.currentThread().getName(),
                    e);
            throw e;
        } finally {
            if (!sameThread) {
                ThreadContextHolder.remove();
            }
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        CallHandler<?> that = (CallHandler<?>) o;
        // NOTE: only compare delegate !!!
        return Objects.equals(delegate, that.delegate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(delegate);
    }
}
