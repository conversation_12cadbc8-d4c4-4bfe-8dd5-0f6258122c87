package com.moego.lib.common.grpc.client;

import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toMap;

import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.autoconfigure.grpc.GrpcProperties.Client.Stub;
import com.moego.lib.common.util.ClassUtil;
import io.grpc.Channel;
import io.grpc.ClientInterceptor;
import io.grpc.ManagedChannelBuilder;
import io.grpc.inprocess.InProcessChannelBuilder;
import io.grpc.stub.AbstractStub;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.Assert;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.unit.DataSize;

/**
 * Register gRPC stubs as Spring beans.
 *
 * <AUTHOR>
 */
public class GrpcStubsBeanDefinitionRegistry
        implements BeanDefinitionRegistryPostProcessor, BeanFactoryAware, EnvironmentAware {

    private static final String SERVICE_NAME_FIELD = "SERVICE_NAME";
    private static final String NEW_BLOCKING_STUB_METHOD = "newBlockingStub";
    private static final String NEW_FUTURE_STUB_METHOD = "newFutureStub";
    private static final String NEW_STUB_METHOD = "newStub";

    private static final AntPathMatcher matcher = new AntPathMatcher(".");

    private BeanFactory beanFactory;
    private Environment environment;

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        GrpcProperties.Client clientConfig = Binder.get(environment)
                .bind(GrpcProperties.PREFIX + ".client", GrpcProperties.Client.class)
                .orElseGet(GrpcProperties.Client::new);
        clientConfig.mergeConfig();

        List<Class<?>> stubClasses = ClassUtil.getClasses(
                clientConfig.getBasePackages(),
                className -> className.endsWith("Stub"),
                AbstractStub.class::isAssignableFrom);

        Map</*service*/ String, /*stubs*/ List<Class<?>>> serviceToStubs = stubClasses.stream()
                .filter(GrpcStubsBeanDefinitionRegistry::hasGrpcServiceName)
                .collect(groupingBy(GrpcStubsBeanDefinitionRegistry::getServiceName));

        for (Map.Entry<String, List<Class<?>>> entry : serviceToStubs.entrySet()) {
            List<Class<?>> stubs = entry.getValue();
            registerBeans(registry, stubs, clientConfig);
        }
    }

    @SuppressWarnings("unchecked")
    private void registerBeans(
            BeanDefinitionRegistry registry, List<Class<?>> stubs, GrpcProperties.Client clientConfig) {
        LinkedHashMap<String, Stub> servicePatternToConfig = clientConfig.getStubs().stream()
                .collect(toMap(Stub::getService, Function.identity(), (o, n) -> o, LinkedHashMap::new));
        for (Class<?> stubClass : stubs) {
            String service = getServiceName(stubClass);
            Method stubMethod = ReflectionUtils.findMethod(
                    stubClass.getEnclosingClass(), getStubMethodName(stubClass), Channel.class);
            if (stubMethod != null) {
                AbstractBeanDefinition bd = BeanDefinitionBuilder.genericBeanDefinition(
                                (Class<Object>) stubClass, () -> {
                                    Stub stub = servicePatternToConfig.keySet().stream()
                                            .filter(pattern -> matcher.match(pattern, service))
                                            .map(servicePatternToConfig::get)
                                            .findFirst()
                                            .orElseGet(() -> defaultStub(service, clientConfig));
                                    // see
                                    // https://stackoverflow.com/questions/47022097/should-i-share-grpc-stubs-or-channels
                                    // gRPC encourages sharing channels,
                                    // so we create stubs from one single channel,
                                    // means one stub config create one channel.
                                    Channel channel = ChannelHolder.getOrSupply(stub, () -> getChannel(stub, service));
                                    return ReflectionUtils.invokeMethod(stubMethod, null, channel);
                                })
                        .getBeanDefinition();
                bd.setLazyInit(true);
                bd.setAutowireMode(AbstractBeanDefinition.AUTOWIRE_BY_TYPE);
                registry.registerBeanDefinition(stubClass.getName(), bd);
            }
        }
    }

    private Channel getChannel(Stub stub, String service) {
        DataSize maxMessageSize = stub.getMaxInboundMessageSize();
        DataSize maxMetadataSize = stub.getMaxInboundMetadataSize();
        ManagedChannelBuilder<?> builder;
        if (stub.getInProcess() == null) {
            Assert.hasText(stub.getAuthority(), "authority is required for " + service);
            builder = ManagedChannelBuilder.forTarget(stub.getAuthority());
        } else {
            Assert.hasText(stub.getInProcess().getName(), "in-process name is required for " + service);
            builder = InProcessChannelBuilder.forName(stub.getInProcess().getName())
                    .directExecutor();
        }

        List<ClientInterceptor> interceptors = beanFactory
                .getBeanProvider(ClientInterceptor.class)
                .orderedStream()
                .collect(Collectors.toList());
        // TODO(Freeman): remove this after all @GrpcClient changed to @Autowired
        interceptors.removeIf(interceptor -> Objects.equals(interceptor.getClass(), CompositeClientInterceptor.class));
        // gRPC invoke interceptor in reverse order
        Collections.reverse(interceptors);
        builder.intercept(interceptors);

        Optional.ofNullable(maxMessageSize).ifPresent(size -> builder.maxInboundMessageSize((int) size.toBytes()));
        Optional.ofNullable(maxMetadataSize).ifPresent(size -> builder.maxInboundMetadataSize((int) size.toBytes()));

        builder.usePlaintext();

        return builder.build();
    }

    private String getStubMethodName(Class<?> stubClass) {
        if (stubClass.getName().endsWith("BlockingStub")) {
            return NEW_BLOCKING_STUB_METHOD;
        } else if (stubClass.getName().endsWith("FutureStub")) {
            return NEW_FUTURE_STUB_METHOD;
        } else {
            return NEW_STUB_METHOD;
        }
    }

    private static String getServiceName(Class<?> stubClass) {
        Class<?> enclosingClass = stubClass.getEnclosingClass();
        Field serviceName = ReflectionUtils.findField(enclosingClass, SERVICE_NAME_FIELD);
        Assert.notNull(serviceName, "Not found " + SERVICE_NAME_FIELD + " field in " + enclosingClass);
        return (String) ReflectionUtils.getField(serviceName, null);
    }

    private static boolean hasGrpcServiceName(Class<?> stubClass) {
        Class<?> enclosingClass = stubClass.getEnclosingClass();
        if (enclosingClass == null) {
            return false;
        }
        Field serviceName = ReflectionUtils.findField(enclosingClass, SERVICE_NAME_FIELD);
        return serviceName != null;
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {
        // nothing to do
    }

    @Override
    public void setEnvironment(Environment environment) {
        this.environment = environment;
    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }

    private static Stub defaultStub(String service, GrpcProperties.Client client) {
        return new Stub(
                service,
                client.getAuthority(),
                client.getMaxInboundMessageSize(),
                client.getMaxInboundMetadataSize(),
                client.getInProcess());
    }
}
