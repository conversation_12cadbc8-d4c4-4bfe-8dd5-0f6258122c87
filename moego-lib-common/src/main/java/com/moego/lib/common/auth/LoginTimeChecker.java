package com.moego.lib.common.auth;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.organization.v1.CheckStaffLoginTimeRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import jakarta.annotation.Nullable;
import java.time.Duration;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LoginTimeChecker {
    @Nullable
    private final StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub;

    public LoginTimeChecker(@Nullable StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub) {
        this.staffServiceBlockingStub = staffServiceBlockingStub;
    }

    private final LoadingCache<Long, CheckResult> getStaffLoginTimeCache = Caffeine.newBuilder()
            .maximumSize(10_000)
            .expireAfterWrite(Duration.ofSeconds(30))
            .build(this::getStaffLoginTimeRemote);

    public record CheckResult(boolean isAllowed, String popUpMessage) {}

    public void check(long staffId) {
        if (staffServiceBlockingStub == null) {
            return;
        }
        if (staffId <= 0) {
            return;
        }
        var result = getStaffLoginTime(staffId);
        if (result == null) {
            return;
        }
        if (!result.isAllowed()) {
            throw ExceptionUtil.bizException(Code.CODE_STAFF_LOGIN_LIMIT, result.popUpMessage);
        }
    }

    private CheckResult getStaffLoginTime(long staffId) {
        return getStaffLoginTimeCache.get(staffId);
    }

    private CheckResult getStaffLoginTimeRemote(long staffId) {
        try {
            if (staffServiceBlockingStub == null) {
                return new CheckResult(true, null);
            }
            var res = staffServiceBlockingStub.checkStaffLoginTime(
                    CheckStaffLoginTimeRequest.newBuilder().setStaffId(staffId).build());
            return new CheckResult(res.getIsAllowed(), res.getPopUpMessage());
        } catch (Exception e) {
            log.error("Failed to get staff login time", e);
            return new CheckResult(true, null);
        }
    }
}
