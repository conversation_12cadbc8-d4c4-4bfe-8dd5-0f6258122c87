package com.moego.lib.common.autoconfigure.grpc;

import io.grpc.internal.GrpcUtil;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;
import org.springframework.util.unit.DataSize;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(GrpcProperties.PREFIX)
public class GrpcProperties implements InitializingBean {

    public static final String PREFIX = "moego.grpc";

    private boolean enabled = true;

    @NestedConfigurationProperty
    private Client client = new Client();

    @NestedConfigurationProperty
    private Server server = new Server();

    @Override
    public void afterPropertiesSet() throws Exception {
        client.mergeConfig();
    }

    @Data
    public static class Client {

        /**
         * Whether to enable gRPC client.
         */
        private boolean enabled = true;
        /**
         * Whether to enable gRPC client grey function.
         */
        private boolean greyEnabled = true;
        /**
         * Whether to enable gRPC client argument validation.
         */
        private boolean validateEnabled = true;
        /**
         * Max inbound message size for gRPC client, default 4MB (global config).
         */
        private DataSize maxInboundMessageSize = DataSize.ofBytes(GrpcUtil.DEFAULT_MAX_MESSAGE_SIZE);
        /**
         * Max inbound metadata size for gRPC client, default 8KB (global config).
         */
        private DataSize maxInboundMetadataSize = DataSize.ofBytes(GrpcUtil.DEFAULT_MAX_HEADER_LIST_SIZE);

        /**
         * gRPC client channels config.
         * <p> e.g. grey-gateway: localhost:8080
         *
         * @deprecated since 2023/4/17 by Freeman, use {@link #stubs} instead.
         */
        @Deprecated
        private Map<String, String> channels = new LinkedHashMap<>();

        /**
         * Authority for gRPC client (global config).
         */
        private String authority;
        /**
         * In process config for gRPC client (global config), all the stubs will use in-process channel if set.
         */
        @NestedConfigurationProperty
        private Stub.InProcess inProcess;
        /**
         * Base packages to register gRPC client stubs as Spring beans.
         */
        private List<String> basePackages = new ArrayList<>();
        /**
         * gRPC client stubs config.
         */
        private List<Stub> stubs = new ArrayList<>();

        @Data
        @NoArgsConstructor
        @AllArgsConstructor
        public static class Stub {

            /**
             * The gRPC service name, support Ant Pattern.
             *
             * <p> e.g. moego.service.grey_gateway.v1.GreyGatewayService
             * <p> e.g. moego.service.sms.**
             *
             * @see org.springframework.util.AntPathMatcher
             */
            private String service;
            /**
             * The gRPC client authority.
             */
            private String authority;
            /**
             * Max inbound message size for gRPC client, use global config if not set.
             */
            private DataSize maxInboundMessageSize;
            /**
             * Max inbound metadata size for gRPC client, use global config if not set.
             */
            private DataSize maxInboundMetadataSize;
            /**
             * In process config for each gRPC client stub.
             */
            @NestedConfigurationProperty
            private InProcess inProcess;

            @Data
            public static class InProcess {

                /**
                 * In process server name.
                 */
                private String name;
            }
        }

        public void mergeConfig() {
            List<Stub> allStub = this.stubs;
            for (Client.Stub stub : allStub) {
                if (stub.getAuthority() == null) {
                    stub.setAuthority(authority);
                }
                if (stub.getMaxInboundMessageSize() == null) {
                    stub.setMaxInboundMessageSize(maxInboundMessageSize);
                }
                if (stub.getMaxInboundMetadataSize() == null) {
                    stub.setMaxInboundMetadataSize(maxInboundMetadataSize);
                }
                if (stub.getInProcess() == null) {
                    stub.setInProcess(inProcess);
                }
            }
        }
    }

    @Data
    public static class Server {

        public static final String PREFIX = GrpcProperties.PREFIX + ".server";

        /**
         * Whether to enable gRPC server.
         */
        private boolean enabled = true;
        /**
         * Whether to enable debug mode, only enabled in dev.
         */
        private boolean debugEnabled = false;
        /**
         * Whether to enable gRPC server argument validation.
         */
        private boolean validateEnabled = true;
        /**
         * The gRPC server port to bind to, or 0 to use a random port, default is 9090.
         */
        private int port = 9090;
        /**
         * Start gRPC server even if there is no gRPC service.
         * <p> Typically for service health check.
         */
        private boolean emptyServerEnabled = false;
        /**
         * Graceful shutdown timeout in milliseconds, default is 5000.
         */
        private long shutdownTimeout = 5000;
        /**
         * The maximum message size allowed to be received on the server, default is 4MB.
         */
        private DataSize maxInboundMessageSize = DataSize.ofBytes(GrpcUtil.DEFAULT_MAX_MESSAGE_SIZE);
        /**
         * The maximum metadata size allowed to be received on the server, default is 8KB.
         */
        private DataSize maxInboundMetadataSize = DataSize.ofBytes(GrpcUtil.DEFAULT_MAX_HEADER_LIST_SIZE);
        /**
         * In process config for gRPC server.
         */
        @NestedConfigurationProperty
        private InProcess inProcess;

        @NestedConfigurationProperty
        private Auth auth = new Auth();

        @NestedConfigurationProperty
        private Health health = new Health();

        @NestedConfigurationProperty
        private Observability observability = new Observability();

        @Data
        public static class InProcess {

            /**
             * In process server name.
             */
            private String name;
        }
    }

    @Data
    public static class Auth {

        /**
         * Whether to enable gRPC server auth function.
         */
        private boolean enabled = true;
    }

    @Data
    public static class Health {

        @NestedConfigurationProperty
        private DataSource dataSource = new DataSource();

        @NestedConfigurationProperty
        private Redis redis = new Redis();

        @Data
        public static class DataSource {

            /**
             * Whether to enable DataSource health check, enabled by default.
             */
            private boolean enabled = true;
            /**
             * Validation query SQL, default is "SELECT 1;".
             */
            private String validationQuery = "SELECT 1;";
        }

        @Data
        public static class Redis {

            /**
             * Whether to enable Redis health check, enabled by default.
             */
            private boolean enabled = true;
        }
    }

    @Data
    public static class Observability {

        @NestedConfigurationProperty
        private Metrics metrics = new Metrics();

        @Data
        public static class Metrics {

            public static final String PREFIX = GrpcProperties.PREFIX + ".server.observability.metrics";

            /**
             * Whether to enable metrics, enabled by default.
             */
            private boolean enabled = true;
            /**
             * The Http server port to bind to, or 0 to use a random port, default is 9999.
             * Will read from moego.grpc.server.observability.metrics.port.
             */
            private int port = 9999;
        }
    }
}
