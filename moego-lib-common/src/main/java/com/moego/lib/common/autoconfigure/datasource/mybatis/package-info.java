/**
 * {@link com.moego.lib.common.autoconfigure.datasource.DynamicDataSource} implementation for MyBatis mappers.
 *
 * <pre>{@code
 * // application.yaml
 * spring:
 *   datasource: # This configuration will be ALWAYS used as the default datasource.
 *     url: ...
 *     username: ...
 *     password: ...
 * moego:
 *   data-sources:
 *     - name: slave-01
 *       url: ...
 *       username: ...
 *       password: ...
 *     - name: slave-02
 *       url: ...
 *       username: ...
 *       password: ...
 *
 * // AppointmentsMapper.java
 * @Mapper
 * public interface AppointmentMapper extends DynamicDataSource<AppointmentMapper> {
 *    Appointment selectByPrimaryKey(@Param("id") Long id);
 * }
 *
 * // AppointmentService.java
 * @Service
 * @RequiredArgsConstructor
 * public class AppointmentService {
 *
 *    private final AppointmentMapper appointmentMapper;
 *
 *    public Appointment getAppointmentWithMainDataSource(long id) {
 *        return appointmentMapper.selectByPrimaryKey(id);
 *    }
 *
 *    public Appointment getAppointmentWithSlaveDataSource(long id) {
 *        return appointmentMapper.useDataSource("slave").selectByPrimaryKey(id);
 *    }
 * }
 * }</pre>
 */
package com.moego.lib.common.autoconfigure.datasource.mybatis;
