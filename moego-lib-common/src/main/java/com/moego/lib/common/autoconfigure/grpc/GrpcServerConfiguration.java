package com.moego.lib.common.autoconfigure.grpc;

import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcServerEnabled;
import com.moego.lib.common.grpc.ServerStarter;
import com.moego.lib.common.grpc.server.GrpcRequestContextServerInterceptor;
import com.moego.lib.common.grpc.server.GrpcServer;
import com.moego.lib.common.thread.DefaultObservableThreadPool;
import com.moego.lib.common.thread.ThreadPoolMetricExporter;
import io.grpc.BindableService;
import io.grpc.ServerInterceptor;
import io.grpc.protobuf.services.ProtoReflectionService;
import jakarta.annotation.Nullable;
import java.lang.reflect.Field;
import java.util.List;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.Assert;
import org.springframework.util.ReflectionUtils;

/**
 * <AUTHOR>
 * @since 2022/11/7
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnGrpcServerEnabled
public class GrpcServerConfiguration {
    private static final Logger log = LoggerFactory.getLogger(GrpcServerConfiguration.class);

    @Bean
    @ConditionalOnMissingBean
    public GrpcRequestContextServerInterceptor grpcRequestContextServerInterceptor() {
        return new GrpcRequestContextServerInterceptor();
    }

    @Bean
    public ServerStarter serverStarter(ObjectProvider<BindableService> servicesProvider) {
        List<BindableService> services = servicesProvider.orderedStream().collect(Collectors.toList());
        return new ServerStarter(services);
    }

    @Bean
    public GrpcServer grpcServer(
            ObjectProvider<ServerInterceptor> interceptorsProvider,
            GrpcProperties properties,
            ServerStarter serverStarter) {
        List<ServerInterceptor> interceptors =
                interceptorsProvider.orderedStream().collect(Collectors.toList());
        return new GrpcServer(interceptors, properties, serverStarter);
    }

    /**
     * Register grpc server thread pool metrics after grpc server started.
     */
    @Bean
    public ApplicationRunner grpcServerExecutorMetricExporter(
            GrpcServer grpcServer, ObjectProvider<ThreadPoolMetricExporter> exporter) {
        return args -> {
            if (getGrpcServerExecutor(grpcServer) instanceof ThreadPoolExecutor tpe) {
                exporter.ifUnique(it -> it.register("grpc", new DefaultObservableThreadPool(tpe)));
            }
        };
    }

    @Bean
    @ConditionalOnProperty(value = GrpcProperties.PREFIX + ".server.debug-enabled")
    public BindableService reflectionBindableService() {
        return ProtoReflectionService.newInstance();
    }

    @Nullable
    private static Object getGrpcServerExecutor(GrpcServer grpcServer) {
        try {
            return getField(getField(grpcServer, "server"), "executor");
        } catch (Exception e) {
            log.warn("Failed to get grpc server executor, can't export grpc thread pool metrics.", e);
            return null;
        }
    }

    @SuppressWarnings("unchecked")
    private static <T> T getField(Object obj, String fieldName) {
        Field field = ReflectionUtils.findField(obj.getClass(), fieldName);
        Assert.notNull(field, "Field '" + fieldName + "' not found in " + obj.getClass());

        ReflectionUtils.makeAccessible(field);
        return (T) ReflectionUtils.getField(field, obj);
    }
}
