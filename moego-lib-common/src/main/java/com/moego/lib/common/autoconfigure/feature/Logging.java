package com.moego.lib.common.autoconfigure.feature;

import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcServerEnabled;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnHttpServerEnabled;
import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.autoconfigure.http.HttpProperties;
import com.moego.lib.common.observability.logging.grpc.LoggingClientInterceptor;
import com.moego.lib.common.observability.logging.grpc.LoggingServerInterceptor;
import com.moego.lib.common.observability.logging.http.LoggingHandlerInterceptor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @since 2022/11/7
 */
@Configuration(proxyBeanMethods = false)
public class Logging {

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnHttpServerEnabled
    static class Http {

        @Bean
        @ConditionalOnProperty(
                value = HttpProperties.PREFIX + ".server.observability.logging.enabled",
                matchIfMissing = true)
        public WebMvcConfigurer loggingWebMvcConfigurer(HttpProperties properties) {
            return new WebMvcConfigurer() {
                @Override
                public void addInterceptors(InterceptorRegistry registry) {
                    HttpProperties.Observability.Logging logging =
                            properties.getServer().getObservability().getLogging();
                    registry.addInterceptor(new LoggingHandlerInterceptor())
                            .addPathPatterns(logging.getPathPatterns())
                            .excludePathPatterns(logging.getExcludePathPatterns());
                }
            };
        }
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnGrpcServerEnabled
    static class Grpc {

        @Bean
        @ConditionalOnProperty(
                value = GrpcProperties.PREFIX + ".client.observability.logging.enabled",
                matchIfMissing = true)
        public LoggingClientInterceptor loggingClientInterceptor() {
            return new LoggingClientInterceptor();
        }

        /**
         * TODO(Freeman): needs to be completed
         */
        //        @Bean
        @ConditionalOnProperty(
                value = GrpcProperties.PREFIX + ".server.observability.logging.enabled",
                matchIfMissing = true)
        public LoggingServerInterceptor loggingServerInterceptor() {
            return new LoggingServerInterceptor();
        }
    }
}
