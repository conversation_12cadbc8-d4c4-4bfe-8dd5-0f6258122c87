package com.moego.lib.common.mybatis.typehandler;

import static java.util.stream.Collectors.toMap;

import com.google.protobuf.ProtocolMessageEnum;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.springframework.core.ResolvableType;

/**
 * 将数据库 int 类型转换为 Protobuf enum。
 *
 * <p> 未知的 int 值将被转换为 Protobuf enum 的 UNRECOGNIZED 值。
 *
 * <AUTHOR>
 * @since 2025/1/26
 */
public abstract class BaseProtobufEnumTypeHandler<T extends Enum<T> & ProtocolMessageEnum> extends BaseTypeHandler<T> {

    private static final String UNRECOGNIZED = "UNRECOGNIZED";

    private final Map<Integer, ProtocolMessageEnum> numberToEnum;
    private final ProtocolMessageEnum unrecognizedEnum;

    protected BaseProtobufEnumTypeHandler() {
        var clz = ResolvableType.forClass(getClass())
                .as(BaseProtobufEnumTypeHandler.class)
                .resolveGeneric(0);
        this.numberToEnum = Optional.ofNullable(clz).map(Class::getEnumConstants).stream()
                .flatMap(Arrays::stream)
                .map(Enum.class::cast)
                .filter(e -> !Objects.equals(e.name(), UNRECOGNIZED))
                .map(ProtocolMessageEnum.class::cast)
                .collect(toMap(ProtocolMessageEnum::getNumber, Function.identity(), (o, n) -> o, LinkedHashMap::new));
        this.unrecognizedEnum = Optional.ofNullable(clz).map(Class::getEnumConstants).stream()
                .flatMap(Arrays::stream)
                .map(Enum.class::cast)
                .filter(e -> Objects.equals(e.name(), UNRECOGNIZED))
                .map(ProtocolMessageEnum.class::cast)
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException("Protobuf enum should have an UNRECOGNIZED value"));
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {
        ps.setInt(i, parameter.getNumber());
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return fromInt(rs.getInt(columnName));
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return fromInt(rs.getInt(columnIndex));
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return fromInt(cs.getInt(columnIndex));
    }

    @SuppressWarnings("unchecked")
    /*private*/ T fromInt(int number) {
        var result = (T) numberToEnum.get(number);
        if (result == null) {
            return (T) unrecognizedEnum;
        }
        return result;
    }
}
