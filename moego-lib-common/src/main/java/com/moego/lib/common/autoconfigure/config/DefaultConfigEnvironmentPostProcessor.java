package com.moego.lib.common.autoconfigure.config;

import com.moego.lib.common.util.Env;
import jakarta.annotation.Nullable;
import java.util.Arrays;
import java.util.Properties;
import org.apache.commons.logging.Log;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.boot.logging.DeferredLogFactory;
import org.springframework.core.Ordered;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

/**
 * Load default configurations.
 * <p> Our configurations can be classified into the following categories:
 * <ul>
 *     <li>common configuration: for all applications, not contains business logic</li>
 *     <li>business configuration: for single application, contains its own business logic</li>
 * </ul>
 * <p> The purpose of this class is to extract the common configuration.
 *
 * <p> The common configuration is stored in the file named "application-default-config.yml".
 *
 * <p> The profile-based common configuration is stored in the file named "application-default-config-{profile}.yml".
 *
 * <AUTHOR>
 * @see Env
 * @since 2022/9/26
 */
public class DefaultConfigEnvironmentPostProcessor implements EnvironmentPostProcessor, Ordered {

    private final Log log;

    public DefaultConfigEnvironmentPostProcessor(DeferredLogFactory logFactory) {
        this.log = logFactory.getLog(getClass());
    }

    /**
     * Configurations to be loaded.
     */
    private static final String[] FILES = {"application-default-config.yml"};

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {

        // profile-based configuration should be loaded before default configuration
        var env = getEnv(environment);
        if (env != null) {
            var configName = String.format(
                    "application-default-config-%s.yml", env.getValue().toLowerCase());
            var config = new ClassPathResource(configName);
            if (config.exists()) {
                environment.getPropertySources().addLast(loadProperties(config));
                log.info("Loaded profile-based configuration: " + config.getFilename());
            }
        }

        // load default configuration, which has the lowest priority than profile-based configuration
        for (String location : FILES) {
            ClassPathResource resource = new ClassPathResource(location);
            environment.getPropertySources().addLast(loadProperties(resource));
            log.info("Loaded default configuration: " + resource.getFilename());
        }
    }

    @Nullable
    private static Env getEnv(ConfigurableEnvironment environment) {
        return Arrays.stream(environment.getActiveProfiles())
                .filter(profile -> Arrays.stream(Env.values())
                        .anyMatch(env -> env.getValue().equalsIgnoreCase(profile)))
                .findFirst()
                .map(Env::fromValue)
                .orElse(null);
    }

    private PropertySource<?> loadProperties(Resource resource) {
        String filename = resource.getFilename();
        if (!resource.exists()) {
            log.warn(filename + " doesn't exist");
        }

        // parse yaml
        YamlPropertiesFactoryBean bean = new YamlPropertiesFactoryBean();
        bean.setResources(resource);

        Properties prop = bean.getObject();

        assert filename != null;
        assert prop != null;
        return new PropertiesPropertySource(filename, prop);
    }

    @Override
    public int getOrder() {
        return Ordered.LOWEST_PRECEDENCE - 10;
    }
}
