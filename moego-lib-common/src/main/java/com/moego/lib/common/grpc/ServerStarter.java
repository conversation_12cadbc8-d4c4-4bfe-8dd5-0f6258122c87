package com.moego.lib.common.grpc;

import com.moego.lib.common.grpc.health.HealthChecker;
import io.grpc.BindableService;
import java.util.ArrayList;
import java.util.List;
import lombok.Getter;

/**
 * Determine if we need to start the servers (gRPC, Metrics).
 *
 * <AUTHOR>
 * @since 2022/9/17
 */
@Getter
public class ServerStarter {

    private static final List<Class<? extends BindableService>> omitServices = getOmitServiceClasses();

    private final boolean needStartup;
    private final List<BindableService> services;

    public ServerStarter(List<BindableService> services) {
        this.services = services;
        this.needStartup = services.stream().anyMatch(service -> {
            return omitServices.stream().noneMatch(svc -> svc.isAssignableFrom(service.getClass()));
        });
    }

    @SuppressWarnings("unchecked")
    private static List<Class<? extends BindableService>> getOmitServiceClasses() {
        List<Class<? extends BindableService>> result = new ArrayList<>();

        // omit health check service
        result.add(HealthChecker.class);

        // omit reflection service
        try {
            Class<?> protoReflectionService = Class.forName("io.grpc.protobuf.services.ProtoReflectionService");
            result.add((Class<? extends BindableService>) protoReflectionService);
        } catch (ClassNotFoundException ignored) {
            // ignore
        }

        return result;
    }
}
