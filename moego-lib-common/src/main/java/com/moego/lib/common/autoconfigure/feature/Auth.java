package com.moego.lib.common.autoconfigure.feature;

import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.auth.grpc.AuthServerInterceptor;
import com.moego.lib.common.auth.grpc.GrpcMethodAuthHolder;
import com.moego.lib.common.auth.http.AuthArgumentResolver;
import com.moego.lib.common.auth.http.AuthControllerMethodValidator;
import com.moego.lib.common.auth.http.AuthHandlerInterceptor;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcEnabled;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcServerEnabled;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnHttpEnabled;
import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.autoconfigure.http.HttpProperties;
import com.moego.lib.common.autoconfigure.util.BeanOrder;
import io.grpc.BindableService;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnSingleCandidate;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

/**
 * <AUTHOR>
 * @since 2022/11/7
 */
@Configuration(proxyBeanMethods = false)
public class Auth {

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnHttpEnabled
    static class Http {
        @Bean
        @Order(BeanOrder.GrpcServer.AUTH)
        public WebMvcConfigurer authWebMvcConfigurer(HttpProperties httpProperties, ApplicationContext ctx) {
            return new WebMvcConfigurer() {
                @Override
                public void addInterceptors(InterceptorRegistry registry) {
                    if (httpProperties.getServer().getAuth().isEnabled()) {
                        StaffServiceGrpc.StaffServiceBlockingStub stub = null;
                        try {
                            stub = ctx.getBean(StaffServiceGrpc.StaffServiceBlockingStub.class);
                        } catch (BeansException ignored) {
                        }
                        registry.addInterceptor(new AuthHandlerInterceptor(stub));
                    }
                }

                @Override
                public void addArgumentResolvers(List<HandlerMethodArgumentResolver> argumentResolvers) {
                    // always AuthArgumentResolver, otherwise the AuthContext can't be injected, and it's a breaking
                    // change.
                    argumentResolvers.add(new AuthArgumentResolver());
                }
            };
        }

        @Bean
        public AuthControllerMethodValidator authControllerMethodValidator(
                RequestMappingHandlerMapping requestMappingHandlerMapping) {
            return new AuthControllerMethodValidator(requestMappingHandlerMapping);
        }
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnGrpcEnabled
    static class Grpc {

        @Bean
        @ConditionalOnGrpcServerEnabled
        @ConditionalOnProperty(value = GrpcProperties.PREFIX + ".server.auth.enabled", matchIfMissing = true)
        public GrpcMethodAuthHolder grpcMethodAuthHolder(ObjectProvider<BindableService> servicesProvider) {
            return new GrpcMethodAuthHolder(servicesProvider.orderedStream().collect(Collectors.toList()));
        }

        @Bean
        @Order(BeanOrder.GrpcServer.AUTH)
        @ConditionalOnSingleCandidate(GrpcMethodAuthHolder.class)
        public AuthServerInterceptor authServerInterceptor(
                GrpcMethodAuthHolder grpcMethodAuthHolder, ApplicationContext context) {
            StaffServiceGrpc.StaffServiceBlockingStub stub = null;
            try {
                stub = context.getBean(StaffServiceGrpc.StaffServiceBlockingStub.class);
            } catch (BeansException ignored) {
            }
            return new AuthServerInterceptor(grpcMethodAuthHolder, stub);
        }
    }
}
