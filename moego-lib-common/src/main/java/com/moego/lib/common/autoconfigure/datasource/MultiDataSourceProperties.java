package com.moego.lib.common.autoconfigure.datasource;

import com.zaxxer.hikari.HikariConfig;
import java.util.ArrayList;
import java.util.List;
import lombok.Data;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DatabaseDriver;

/**
 * Multi {@link javax.sql.DataSource} support.
 *
 * <pre>{@code
 * // application.yaml
 * spring:
 *   datasource: # This configuration will be ALWAYS used as the default datasource.
 *     url: ...
 *     username: ...
 *     password: ...
 * moego:
 *   data-sources:
 *     - name: slave-1
 *       url: ...
 *       username: ...
 *       password: ...
 *     - name: slave-2
 *       url: ...
 *       username: ...
 *       password: ...
 * }</pre>
 *
 * <AUTHOR>
 * @since 2024/12/10
 */
@Data
@ConfigurationProperties(MultiDataSourceProperties.PREFIX)
public class MultiDataSourceProperties implements InitializingBean {
    public static final String PREFIX = "moego";

    private List<DataSource> dataSources = new ArrayList<>();

    @Data
    public static class DataSource {
        /**
         * DataSource name, this will be used as the bean name.
         */
        private String name;

        private String driverClassName;
        private String url;
        private String username;
        private String password;
        private HikariConfig hikari = new HikariConfig();
    }

    @Override
    public void afterPropertiesSet() {
        init();
    }

    private void init() {
        for (var dataSource : dataSources) {
            if (dataSource.getDriverClassName() == null) {
                dataSource.setDriverClassName(
                        DatabaseDriver.fromJdbcUrl(dataSource.getUrl()).getDriverClassName());
            }

            var hikari = dataSource.getHikari();
            if (hikari.getDriverClassName() == null) {
                hikari.setDriverClassName(dataSource.getDriverClassName());
            }
            if (hikari.getUsername() == null) {
                hikari.setUsername(dataSource.getUsername());
            }
            if (hikari.getPassword() == null) {
                hikari.setPassword(dataSource.getPassword());
            }
            if (hikari.getJdbcUrl() == null) {
                hikari.setJdbcUrl(dataSource.getUrl());
            }
            if (hikari.getPoolName() == null) {
                hikari.setPoolName(dataSource.getName());
            }
        }
    }

    /**
     * Merge the properties of the specified {@link HikariConfig} into this {@link MultiDataSourceProperties}.
     *
     * @param hikariConfig the {@link HikariConfig} to merge
     */
    public void merge(HikariConfig hikariConfig) {

        afterPropertiesSet();

        for (var dataSource : dataSources) {
            var hikari = dataSource.getHikari();
            if (hikari.getMaximumPoolSize() == -1) {
                var other = hikariConfig.getMaximumPoolSize();
                if (other > 0) {
                    hikari.setMaximumPoolSize(other);
                }
            }
            if (hikari.getMinimumIdle() == -1) {
                var other = hikariConfig.getMinimumIdle();
                if (other > 0) {
                    hikari.setMinimumIdle(other);
                }
            }
        }
    }
}
