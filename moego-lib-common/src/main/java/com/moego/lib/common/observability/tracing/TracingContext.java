/*
 * @since 2022-04-29 17:57:08
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.lib.common.observability.tracing;

import com.moego.lib.common.thread.ThreadContextHolder;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import lombok.Getter;

@Getter
public class TracingContext {

    public static final String HK_FROM_APP = "X-From-App";
    public static final String HK_REQUEST_ID = "x-request-id";
    public static final String HK_B3_TRACE_ID = "x-b3-traceid";
    public static final String HK_B3_SPAN_ID = "x-b3-spanid";
    public static final String HK_B3_PARENT_SPAN_ID = "x-b3-parentspanid";
    public static final String HK_B3_SAMPLED = "x-b3-sampled";
    public static final String HK_B3_FLAGS = "x-b3-flags";
    public static final String HK_DATADOG_TRACE_ID = "x-datadog-trace-id";
    public static final String HK_DATADOG_PARENT_ID = "x-datadog-parent-id";
    public static final String HK_DATADOG_SAMPLING_PRIORITY = "x-datadog-sampling-priority";
    public static final String HK_OT_SPAN_CONTEXT = "x-ot-span-context";
    public static final String HK_GRPC_TRACE_BIN = "grpc-trace-bin";
    public static final String HK_TRACE_PARENT = "traceparent";
    public static final String HK_CLOUD_TRACE_CONTEXT = "x-cloud-trace-context";

    public static final String LK_REQUEST_ID = "id";

    static final String[] HK_LIST = new String[] {TracingContext.HK_REQUEST_ID};

    private final String requestId;
    private final ConcurrentMap<String, Object> data = new ConcurrentHashMap<>();

    TracingContext(Map<String, String> items) {
        if (items != null) {
            items.forEach((k, v) -> {
                if (k != null && v != null) {
                    data.put(k.toLowerCase(), v);
                }
            });
        }

        this.data.computeIfAbsent(
                TracingContext.HK_REQUEST_ID, k -> UUID.randomUUID().toString());
        this.requestId = this.data.get(TracingContext.HK_REQUEST_ID).toString();
    }

    public static TracingContext get() {
        return ThreadContextHolder.getContext(TracingContext.class);
    }
}
