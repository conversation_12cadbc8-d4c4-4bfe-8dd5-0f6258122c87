package com.moego.lib.common.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.BeanDescription;
import com.fasterxml.jackson.databind.DeserializationConfig;
import com.fasterxml.jackson.databind.JavaType;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonMappingException;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationConfig;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.fasterxml.jackson.databind.deser.Deserializers;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.ser.Serializers;
import com.google.protobuf.Message;
import com.google.protobuf.MessageOrBuilder;
import com.google.protobuf.ProtocolMessageEnum;
import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.json.jackson.LongSafeSerializer;
import com.moego.lib.common.json.jackson.protobuf.ProtobufEnum;
import com.moego.lib.common.json.jackson.protobuf.ProtobufMessage;
import java.lang.reflect.Type;
import java.util.List;
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder;
import org.springframework.util.ClassUtils;

/**
 * JSON utility class for serializing Java objects to JSON and deserializing JSON to Java objects.
 *
 * <p>This utility provides a consistent way to handle JSON serialization and deserialization
 * across the application. It uses Jackson's ObjectMapper to support both regular Java objects and Protocol Buffers (protobuf) messages.</p>
 *
 * <AUTHOR>
 * @since 2023/1/19
 */
public final class JsonUtil {

    private JsonUtil() {
        throw new IllegalStateException("Utility class cannot be instantiated!");
    }

    private static final ObjectMapper om;

    static {
        om = new Jackson2ObjectMapperBuilder() // 使用 Jackson2ObjectMapperBuilder 为了和 Spring Boot 行为保持一致
                .serializers(new LongSafeSerializer()) // 处理大数值的 Long，防止在 JavaScript 中精度丢失
                // see org.springframework.boot.autoconfigure.jackson.JacksonAutoConfiguration.FEATURE_DEFAULTS
                .featuresToDisable(
                        SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, // 使用 ISO-8601 格式而不是时间戳
                        SerializationFeature.WRITE_DURATIONS_AS_TIMESTAMPS) // 使用 ISO-8601 格式而不是时间戳
                .failOnEmptyBeans(false) // 允许序列化空对象
                .build();

        if (ClassUtils.isPresent("com.google.protobuf.Message", null)) {
            om.registerModule(new ProtobufModule());
        }
    }

    /**
     * Convert the object/{@link Message} to JSON string.
     *
     * <p>Include all fields for Java beans, even if they are null; include default values for protobuf messages.</p>
     *
     * <p>Examples:</p>
     *
     * <pre>{@code
     * // Serialize a simple Java object
     * User user = new User().setName("Freeman").setAge(18);
     * String json = JsonUtil.toJson(user);
     * // Result: {"name":"Freeman","age":18}
     *
     * // Serialize a Map
     * Map<String, Object> map = Map.of("name", "Freeman", "age", 18);
     * String json = JsonUtil.toJson(map);
     * // Result: {"name":"Freeman","age":18}
     *
     * // Serialize a List
     * List<String> list = List.of("apple", "banana", "orange");
     * String json = JsonUtil.toJson(list);
     * // Result: ["apple","banana","orange"]
     *
     * // Serialize a Protocol Buffer message, including default values
     * GreyItemModel model = GreyItemModel.newBuilder()
     *         .setName("test-item")
     *         .addJiraTickets("TECH-1")
     *         .build();
     * String json = JsonUtil.toJson(model);
     * // Result: {"id":"0","name":"test-item","namespace":"","description":"","jiraTickets":["TECH-1"],"svcBranchMap":{}}
     * }</pre>
     *
     * @param obj the object/{@link Message} to encode
     * @return json string
     */
    public static String toJson(Object obj) {
        try {
            return om.writeValueAsString(obj);
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException(e);
        }
    }

    /**
     * Convert the json string to object/{@link Message}.
     *
     * <p>Examples:</p>
     *
     * <pre>{@code
     * // Deserialize JSON to a Java object
     * String json = "{\"name\":\"Freeman\",\"age\":18}";
     * User user = JsonUtil.toBean(json, User.class);
     * // Result: User(name=Freeman, age=18)
     *
     * // Deserialize JSON to a Protocol Buffer message
     * String json = "{\"name\":\"Freeman\",\"age\":18}";
     * Struct struct = JsonUtil.toBean(json, Struct.class);
     * // Result: A Struct with fields "name" and "age"
     * }</pre>
     *
     * @param json  the json string to decode
     * @param clazz the class of the object/{@link Message}
     * @param <T>   the type of the object
     * @return the decoded object
     */
    public static <T> T toBean(String json, Class<T> clazz) {
        try {
            return om.readValue(json, clazz);
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException(e);
        }
    }

    /**
     * Convert the json string to a specific bean list.
     *
     * <p>This method is specifically designed for deserializing JSON arrays into lists of objects.</p>
     *
     * <p>Examples:</p>
     *
     * <pre>{@code
     * // Deserialize JSON array to a List of User objects
     * String json = "[{\"name\":\"Bob\",\"age\":18},{\"name\":\"Jason\",\"age\":18}]";
     * List<User> users = JsonUtil.toList(json, User.class);
     * // Result: List containing User(name=Bob, age=18) and User(name=Jason, age=18)
     *
     * // Deserialize JSON array to a List of Protocol Buffer messages
     * String json = "[{\"name\":\"Bob\",\"age\":18},{\"name\":\"Jason\",\"age\":18}]";
     * List<Struct> structs = JsonUtil.toList(json, Struct.class);
     * // Result: List containing two Struct messages
     * }</pre>
     *
     * @param json  json string
     * @param clazz class
     * @param <T>   the type of the object
     * @return list of objects of type T
     */
    public static <T> List<T> toList(String json, Class<T> clazz) {
        try {
            return om.readValue(json, om.getTypeFactory().constructCollectionType(List.class, clazz));
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException(e);
        }
    }

    /**
     * Convert the JSON string to object.
     *
     * <p>This method is particularly useful for deserializing JSON into complex generic types
     * or when the exact type is not known at compile time.</p>
     *
     * <p>Examples:</p>
     *
     * <pre>{@code
     * // Deserialize JSON to a Map
     * String json = """
     *      {"name": "fm", "age": 18, "hobbies": ["reading", "coding"]}
     *    """;
     * Map<String, Object> map = JsonUtil.toBean(json, new TypeRef<>() {});
     * // Result: {name=fm, age=18, hobbies=[reading, coding]}
     * }</pre>
     *
     * <pre>{@code
     * // Deserialize JSON array to a List
     * String json = """
     *      [1, 2, 3]
     *    """;
     * List<Integer> list = JsonUtil.toBean(json, new TypeRef<>() {});
     * // Result: [1, 2, 3]
     * }</pre>
     *
     * <pre>{@code
     * // Deserialize JSON to a complex generic type
     * String json = """
     *      {"data": [{"id": 1, "name": "item1"}, {"id": 2, "name": "item2"}], "total": 2}
     *    """;
     * PageResult<Item> result = JsonUtil.toBean(json, new TypeRef<>() {});
     * // Result: PageResult with data=[Item(id=1, name=item1), Item(id=2, name=item2)] and total=2
     * }</pre>
     *
     * <pre>{@code
     * // Deserialize JSON to a Protobuf Message
     * String json = """
     *      [1, 2, 3]
     *    """;
     * Value value = JsonUtil.toBean(json, new TypeRef<>() {}); // Protobuf Message
     * // Result: A Value containing an array value with elements 1, 2, 3
     * }</pre>
     *
     * @param json    json string
     * @param typeRef type reference
     * @param <T>     the type of the object, maybe protobuf message
     * @return java bean
     */
    public static <T> T toBean(String json, TypeRef<T> typeRef) {
        try {
            return om.readValue(json, new TypeReference<T>() {
                @Override
                public Type getType() {
                    return typeRef.getType();
                }
            });
        } catch (JsonProcessingException e) {
            throw new IllegalArgumentException(e);
        }
    }

    private static final class ProtobufModule extends SimpleModule {
        @Override
        public void setupModule(SetupContext context) {
            context.addSerializers(new Serializers.Base() {
                @Override
                public JsonSerializer<?> findSerializer(
                        SerializationConfig config, JavaType type, BeanDescription beanDesc) {
                    if (MessageOrBuilder.class.isAssignableFrom(type.getRawClass())) {
                        return new ProtobufMessage.Serializer<>();
                    }
                    if (ProtocolMessageEnum.class.isAssignableFrom(type.getRawClass()) && type.isEnumType()) {
                        return new ProtobufEnum.Serializer<>();
                    }
                    return super.findSerializer(config, type, beanDesc);
                }
            });
            context.addDeserializers(new Deserializers.Base() {
                @Override
                @SuppressWarnings({"rawtypes", "unchecked"})
                public JsonDeserializer<?> findEnumDeserializer(
                        Class<?> type, DeserializationConfig config, BeanDescription beanDesc)
                        throws JsonMappingException {
                    if (ProtocolMessageEnum.class.isAssignableFrom(type) && type.isEnum()) {
                        return new ProtobufEnum.Deserializer(type);
                    }
                    return super.findEnumDeserializer(type, config, beanDesc);
                }

                @Override
                @SuppressWarnings({"rawtypes", "unchecked"})
                public JsonDeserializer<?> findBeanDeserializer(
                        JavaType type, DeserializationConfig config, BeanDescription beanDesc)
                        throws JsonMappingException {
                    if (MessageOrBuilder.class.isAssignableFrom(type.getRawClass())) {
                        return new ProtobufMessage.Deserializer(type.getRawClass());
                    }
                    return super.findBeanDeserializer(type, config, beanDesc);
                }
            });
        }
    }
}
