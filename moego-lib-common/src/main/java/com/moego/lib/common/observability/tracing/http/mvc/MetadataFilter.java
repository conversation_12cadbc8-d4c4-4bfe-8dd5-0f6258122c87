package com.moego.lib.common.observability.tracing.http.mvc;

import com.moego.lib.common.observability.tracing.Headers;
import com.moego.lib.common.observability.tracing.MetadataProcessor;
import com.moego.lib.common.observability.tracing.RequestHolder;
import com.moego.lib.common.thread.MetadataContext;
import com.moego.lib.common.thread.ThreadContext;
import com.moego.lib.common.thread.ThreadContextHolder;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

/**
 * For http server to invoke grpc client.
 *
 * <AUTHOR>
 * @since 2022/9/16
 */
public class MetadataFilter extends OncePerRequestFilter {

    private final List<MetadataProcessor<?>> processors;

    public MetadataFilter(List<MetadataProcessor<?>> processors) {
        this.processors = processors;
    }

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        MetadataContext metadataContext = new MetadataContext();
        MvcRequestHolder headerHolder = new MvcRequestHolder(request);
        Headers transferHeaders = new Headers();

        for (MetadataProcessor<?> processor : processors) {
            Map<String, String> mapping = new HashMap<>();
            for (String k : processor.keys(headerHolder)) {
                String v = request.getHeader(k);
                if (StringUtils.hasText(v)) {
                    transferHeaders.put(k, v);
                }
                mapping.put(k, v);
            }
            Object ctx = processor.build(mapping);
            if (ctx != null) {
                metadataContext.put(ctx.getClass(), ctx);
            }
        }

        // headers need to be transferred to upstream
        metadataContext.put(Headers.class, transferHeaders);
        // http header holder
        metadataContext.put(RequestHolder.class, headerHolder);

        ThreadContext threadContext = new ThreadContext(metadataContext);
        ThreadContextHolder.set(threadContext);
        try {
            filterChain.doFilter(request, response);
        } finally {
            ThreadContextHolder.remove();
        }
    }
}
