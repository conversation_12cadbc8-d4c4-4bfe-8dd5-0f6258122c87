package com.moego.lib.common.autoconfigure.feature;

import com.moego.lib.common.autoconfigure.datasource.MultiDataSourceBeanDefinitionRegistry;
import com.moego.lib.common.autoconfigure.datasource.MultiDataSourceProperties;
import com.moego.lib.common.autoconfigure.datasource.mybatis.MyBatisCache;
import com.moego.lib.common.autoconfigure.datasource.mybatis.MyBatisDynamicDataSourceBeanPostProcessor;
import org.apache.ibatis.session.SqlSession;
import org.mybatis.spring.boot.autoconfigure.MybatisAutoConfiguration;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass(name = "com.zaxxer.hikari.HikariDataSource")
@EnableConfigurationProperties(MultiDataSourceProperties.class)
public class DataSource {

    /**
     * Support {@code moego.data-sources} configuration.
     */
    @Bean
    static MultiDataSourceBeanDefinitionRegistry multiDataSourceBeanDefinitionRegistry() {
        return new MultiDataSourceBeanDefinitionRegistry();
    }

    /**
     * Support dynamic {@link javax.sql.DataSource} for MyBatis mappers.
     */
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass({SqlSession.class, MybatisAutoConfiguration.class})
    static class Mybatis implements DisposableBean {

        @Bean
        static MyBatisDynamicDataSourceBeanPostProcessor dynamicDataSourceBeanPostProcessor() {
            return new MyBatisDynamicDataSourceBeanPostProcessor();
        }

        @Override
        public void destroy() throws Exception {
            MyBatisCache.clear();
        }
    }
}
