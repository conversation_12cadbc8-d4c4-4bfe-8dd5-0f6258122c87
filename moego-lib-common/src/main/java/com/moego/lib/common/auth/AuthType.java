/*
 * @since 2022-06-22 20:32:48
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.lib.common.auth;

public enum AuthType {
    /**
     * Demo 用, 独立的一个授权机制
     */
    TODO_USER,

    /**
     * 匿名, 表示无需权限
     */
    ANONYMOUS,

    /**
     * Account, 表示必须登录 MoeGo Account, 适用于需要 MoeGo Account 登录态访问, 但是不需要区分业务线的接口.
     * 适用于携带 `MGSID-` 前缀的 cookie 的请求, 并兼容携带 Account Token 的历史请求 (这类请求在后续会下线).
     * <p>
     * 使用这个 AuthType 时, AuthContext 可以拿到的信息有:
     * <li> accountId: account id
     * <li> sessionId: 如果是携带 cookie 的请求, session id 为 cookie 对应的会话 id; 如果是携带 Account Token 的请求, 没有 session id
     */
    ACCOUNT,

    /**
     * Company, 表示必须登录 MoeGo Account 并且是 B 端,
     * 适用于携带 `MGSID-B(-T2/S1)` cookie 的请求, 并兼容携带 Account Token + Staff Token 的历史请求 (这类请求在后续会下线).
     * <p>
     * 使用这个 AuthType 时, AuthContext 可以拿到的信息有:
     * <li> accountId: account id
     * <li> sessionId: 如果是携带 cookie 的请求, session id 为 cookie 对应的会话 id; 如果是携带 Account Token + Staff Token 的请求, 没有 session id
     * <li> companyId: company id
     * <li> businessId: business id, 不保证能拿到, 通常情况下应该认为没有 business id
     * <li> staffId: staff id
     */
    COMPANY,

    /**
     * Business, 表示必须登录 MoeGo Account 并且是 B 端,
     * 适用于携带 `MGSID-B(-T2/S1)` cookie 的请求, 并兼容携带 Account Token + Staff Token 的历史请求 (这类请求在后续会下线).
     * <p>
     * 使用这个 AuthType 时, AuthContext 可以拿到的信息有:
     * <li> accountId: account id
     * <li> sessionId: 如果是携带 cookie 的请求, session id 为 cookie 对应的会话 id; 如果是携带 Account Token + Staff Token 的请求, 没有 session id
     * <li> companyId: company id, 通常情况下是都能拿到的, 但是没有做必填校验
     * <li> businessId: business id
     * <li> staffId: staff id
     */
    BUSINESS,

    /**
     * OB, 表示必须携带 OB cookie 且处于登录态 (同时有主会话和子会话) 才能访问的接口.
     * OB anti spam 项目上线后, 所有 new client 和 existing client 在登录后都必定创建子会话, 登录前只有主会话.
     * 适用于在登录 OB 后, 需要同时提供给 new client 和 existing client 访问的接口, 这类接口不要使用 `ANONYMOUS`.
     * 如果是只希望给 existing client 访问的接口, 请使用 `OB_EXISTING_CLIENT`.
     * 如果是登录 OB 之前的接口, 请使用 `ANONYMOUS`.
     * <p>
     * 使用这个 AuthType 时, AuthContext 可以拿到的信息有:
     * <li> accountId: 取值 < -1, 表示一个 unique visitor
     * <li> sessionId: 主会话 id
     * <li> subSessionId: 子会话 id
     * <li> customerId: existing client 的 id, 如果是 new client, 取值为 null
     * <li> sessionData: 主会话的 session data
     */
    OB,

    /**
     * OB existing client, 表示必须携带 OB cookie 且处于 existing client 的登录态 (同时有主会话和 existing client 子会话) 才能访问的接口.
     * <p>
     * 使用这个 AuthType 时, AuthContext 可以拿到的信息有:
     * <li> accountId: 取值 < -1, 表示一个 unique visitor
     * <li> sessionId: 主会话 id
     * <li> subSessionId: 子会话 id
     * <li> customerId: existing client 的 id
     * <li> sessionData: 主会话的 session data
     */
    OB_EXISTING_CLIENT,

    /**
     * 拒绝访问, 会返回 403 错误码
     */
    DENY,
}
