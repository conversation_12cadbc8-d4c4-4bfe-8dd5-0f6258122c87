package com.moego.lib.common.thread;

import com.moego.lib.common.util.ThreadPoolUtil;
import java.util.concurrent.Callable;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Future;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.function.Supplier;
import lombok.experimental.UtilityClass;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

/**
 * 全局公共线程池
 *
 * <AUTHOR>
 */
@UtilityClass
public class ThreadPool {

    private static final ExecutorService executePool = getExecutePool();
    private static final ExecutorService submitPool = getSubmitPool();

    /**
     * 用于 execute 的线程池，execute 任务不用关心返回值，允许排队。
     */
    private static ExecutorService getExecutePool() {
        return ExecutorServiceWrapper.wrap(new ThreadPoolExecutor(
                30,
                500,
                60,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(200),
                new CustomizableThreadFactory("moego-execute-"),
                new ThreadPoolExecutor.CallerRunsPolicy()));
    }

    /**
     * 用于 submit 的线程池，submit 任务会使用其返回值，所以不能排队。
     */
    private static ExecutorService getSubmitPool() {
        return ExecutorServiceWrapper.wrap(new ThreadPoolExecutor(
                30,
                500,
                60,
                TimeUnit.SECONDS,
                new SynchronousQueue<>(),
                new CustomizableThreadFactory("moego-submit-"),
                new ThreadPoolExecutor.CallerRunsPolicy()));
    }

    /**
     * Shutdown the executor service.
     */
    public static synchronized void shutdown() {
        ThreadPoolUtil.shutdown(submitPool);
        ThreadPoolUtil.shutdown(executePool);
    }

    /**
     * 获得 {@link ExecutorService}
     *
     * @return {@link ExecutorService}
     * @deprecated by Freeman since 2023/12/22, use {@link #getSubmitExecutor()} instead.
     */
    @Deprecated
    public static ExecutorService getExecutor() {
        return submitPool;
    }

    /**
     * Get {@link ExecutorService} for submit.
     *
     * @return {@link ExecutorService} for submit
     */
    public static ExecutorService getSubmitExecutor() {
        return submitPool;
    }

    /**
     * Get {@link ExecutorService} for execute.
     *
     * @return {@link ExecutorService} for execute
     */
    public static ExecutorService getExecuteExecutor() {
        return executePool;
    }

    /**
     * 直接在公共线程池中执行线程
     *
     * @param runnable 可运行对象
     */
    public static void execute(Runnable runnable) {
        executePool.execute(new RunHandler(runnable, true));
    }

    /**
     * 执行有返回值的异步方法<br>
     * Future代表一个异步执行的操作，通过get()方法可以获得操作的结果，如果异步操作还没有完成，则，get()会使当前线程阻塞
     *
     * @param <T>  执行的Task
     * @param task {@link Callable}
     * @return Future
     */
    public static <T> Future<T> submit(Callable<T> task) {
        return submitPool.submit(new CallHandler<>(task));
    }

    /**
     * 执行无返回值的异步方法<br>
     * The Future's get method will return null upon successful completion.
     *
     * @param runnable 可运行对象
     * @return {@link Future}
     */
    public static Future<?> submit(Runnable runnable) {
        return submitPool.submit(new RunHandler(runnable, false));
    }

    /**
     * Using {@link #submitPool} to supply async task.
     *
     * @param task task to run
     * @param <T>   return type
     * @return future
     * @see CompletableFuture#supplyAsync(Supplier, Executor)
     */
    public static <T> CompletableFuture<T> supplyAsync(Supplier<T> task) {
        return CompletableFuture.supplyAsync(task, submitPool);
    }

    /**
     * Using {@link #submitPool} to run async task.
     *
     * @param task task to run
     * @return future
     * @see CompletableFuture#runAsync(Runnable, Executor)
     */
    public static CompletableFuture<Void> runAsync(Runnable task) {
        return CompletableFuture.runAsync(task, submitPool);
    }
}
