package com.moego.lib.common.grpc;

import static org.springframework.http.HttpStatus.BAD_REQUEST;
import static org.springframework.http.HttpStatus.CONFLICT;
import static org.springframework.http.HttpStatus.FORBIDDEN;
import static org.springframework.http.HttpStatus.INTERNAL_SERVER_ERROR;
import static org.springframework.http.HttpStatus.NOT_FOUND;
import static org.springframework.http.HttpStatus.NOT_IMPLEMENTED;
import static org.springframework.http.HttpStatus.OK;
import static org.springframework.http.HttpStatus.PRECONDITION_FAILED;
import static org.springframework.http.HttpStatus.SERVICE_UNAVAILABLE;
import static org.springframework.http.HttpStatus.TOO_MANY_REQUESTS;
import static org.springframework.http.HttpStatus.UNAUTHORIZED;

import com.google.rpc.Code;
import io.grpc.Channel;
import io.grpc.ManagedChannel;
import io.grpc.Status;
import io.grpc.stub.AbstractAsyncStub;
import io.grpc.stub.AbstractBlockingStub;
import io.grpc.stub.AbstractFutureStub;
import io.grpc.stub.AbstractStub;
import java.util.concurrent.TimeUnit;
import lombok.experimental.UtilityClass;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * <AUTHOR>
 * @since 2022/10/11
 */
@UtilityClass
public class GrpcUtil {

    private static final Logger log = LoggerFactory.getLogger(GrpcUtil.class);

    /**
     * Convert code to Google RPC code.
     *
     * @param code code
     * @return Google RPC code.
     */
    public static Code toRpcCode(int code) {
        return switch (code) {
            case com.moego.idl.models.errors.v1.Code.CODE_SUCCESS_VALUE -> Code.OK;
            case com.moego.idl.models.errors.v1.Code.CODE_PARAMS_ERROR_VALUE -> Code.INVALID_ARGUMENT;
            case com.moego.idl.models.errors.v1.Code.CODE_UNAUTHORIZED_ERROR_VALUE -> Code.UNAUTHENTICATED;
            case com.moego.idl.models.errors.v1.Code.CODE_FORBIDDEN_VALUE -> Code.PERMISSION_DENIED;
            case com.moego.idl.models.errors.v1.Code.CODE_TOO_MANY_REQUESTS_VALUE -> Code.RESOURCE_EXHAUSTED;
            case com.moego.idl.models.errors.v1.Code.CODE_SERVER_ERROR_VALUE -> Code.INTERNAL;
            default -> Code.INVALID_ARGUMENT;
        };
    }

    /**
     * Convert {@link Status.Code} to HTTP status code.
     *
     * @param code {@link Status.Code}
     * @return HTTP status code.
     */
    public static int grpc2HttpCode(Status.Code code) {
        return switch (code) {
            case OK -> OK.value();
            case INTERNAL -> INTERNAL_SERVER_ERROR.value();
            case NOT_FOUND -> NOT_FOUND.value();
            case UNAVAILABLE -> SERVICE_UNAVAILABLE.value();
            case UNIMPLEMENTED -> NOT_IMPLEMENTED.value();
            case ALREADY_EXISTS -> CONFLICT.value();
            case UNAUTHENTICATED -> UNAUTHORIZED.value();
            case PERMISSION_DENIED -> FORBIDDEN.value();
            case RESOURCE_EXHAUSTED -> TOO_MANY_REQUESTS.value();
            case FAILED_PRECONDITION -> PRECONDITION_FAILED.value();
            default -> BAD_REQUEST.value();
        };
    }

    @SuppressWarnings("rawtypes")
    public static String getNewStubMethodName(Class<? extends AbstractStub> clz) {
        if (AbstractBlockingStub.class.isAssignableFrom(clz)) {
            return "newBlockingStub";
        }
        if (AbstractFutureStub.class.isAssignableFrom(clz)) {
            return "newFutureStub";
        }
        if (AbstractAsyncStub.class.isAssignableFrom(clz)) {
            return "newStub";
        }
        throw new IllegalArgumentException("Unsupported stub class: " + clz);
    }

    @SuppressWarnings("rawtypes")
    public static String determineRealClientName(String name, Class<? extends AbstractStub> stub) {
        if (AbstractBlockingStub.class.isAssignableFrom(stub)) {
            return String.format("%sBlockingStub[%s]", name, stub.getName());
        }
        if (AbstractFutureStub.class.isAssignableFrom(stub)) {
            return String.format("%sFutureStub[%s]", name, stub.getName());
        }
        if (AbstractAsyncStub.class.isAssignableFrom(stub)) {
            return String.format("%sAsyncStub[%s]", name, stub.getName());
        }
        throw new IllegalArgumentException("Unsupported stub type: " + stub);
    }

    /**
     * Shutdown a gRPC channel.
     *
     * @param channel channel, nullable.
     */
    public static void shutdownChannel(Channel channel) {
        if (channel instanceof ManagedChannel mc) {
            // Close the gRPC managed-channel if not shut down already.
            if (!mc.isShutdown()) {
                try {
                    mc.shutdown();
                    if (!mc.awaitTermination(2, TimeUnit.SECONDS)) {
                        log.warn("Timed out gracefully shutting down connection: {}. ", mc);
                    }
                } catch (Exception e) {
                    log.error("Unexpected exception while waiting for channel termination", e);
                }
            }

            // Forcefully shut down if still not terminated.
            if (!mc.isTerminated()) {
                try {
                    mc.shutdownNow();
                    if (!mc.awaitTermination(2, TimeUnit.SECONDS)) {
                        log.warn("Timed out forcefully shutting down connection: {}. ", mc);
                    }
                } catch (Exception e) {
                    log.error("Unexpected exception while waiting for channel termination", e);
                }
            }
        }
    }
}
