/*
 * @since 2022-06-25 10:09:55
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.lib.common.observability.tracing;

import com.moego.lib.common.thread.MetadataContext;
import java.util.Map;

public interface MetadataProcessor<T> {
    /**
     * Get keys that need to be passed to the upstream service.
     *
     * @param holder {@link RequestHolder}, not null.
     * @return keys
     */
    String[] keys(RequestHolder holder);

    /**
     * Build the {@link MetadataContext} value object.
     *
     * @param entries the header entries, not null.
     * @return the metadata context object, may be null.
     */
    T build(Map<String, String> entries);
}
