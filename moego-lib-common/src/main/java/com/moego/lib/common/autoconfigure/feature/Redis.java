package com.moego.lib.common.autoconfigure.feature;

import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;

/**
 * <AUTHOR>
 * @since 2025/3/28
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass(
        name = {
            "org.springframework.data.redis.connection.lettuce.LettuceConnectionFactory",
            "org.springframework.boot.autoconfigure.data.redis.LettuceClientConfigurationBuilderCustomizer"
        })
public class Redis {

    /**
     * Disable peer verification for local development.
     *
     * <p> 服务端证书 SAN 没有包含 redis.t2.moego.dev，client 在连接时会做 peer verification，导致连接失败。
     *
     * <p> 注意：只在 local profile 下才生效这个配置。
     *
     * @see <a href="https://moegoworkspace.slack.com/archives/C02LJP7ME9W/p1743146536140689?thread_ts=1743131426.978569&cid=C02LJP7ME9W">Slack</a>
     */
    @Bean
    @Profile("local")
    public LettuceClientConfigurationBuilderCustomizer
            disablePeerVerificationLettuceClientConfigurationBuilderCustomizer() {
        return builder -> builder.useSsl().disablePeerVerification();
    }
}
