package com.moego.lib.common.autoconfigure.feature;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.moego.lib.common.json.jackson.LongSafeSerializer;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2023/12/13
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass({ObjectMapper.class, Jackson2ObjectMapperBuilderCustomizer.class})
public class Jackson {

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer longSafeObjectMapperBuilderCustomizer() {
        return builder -> builder.serializers(new LongSafeSerializer());
    }
}
