package com.moego.lib.common.autoconfigure.config;

import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.MapPropertySource;
import org.springframework.util.ClassUtils;

/**
 * gRPC integrate with {@link SpringBootTest#webEnvironment}.
 *
 * <AUTHOR>
 * @since 2023/4/19
 */
public class GrpcTestConfigEnvironmentPostProcessor implements EnvironmentPostProcessor {

    private static final boolean IS_SPRING_BOOT_TEST_ENV =
            ClassUtils.isPresent("org.springframework.boot.test.context.SpringBootTest", null);

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        if (!IS_SPRING_BOOT_TEST_ENV) {
            return;
        }

        SpringBootTest anno =
                AnnotationUtils.findAnnotation(application.getMainApplicationClass(), SpringBootTest.class);
        if (anno == null) {
            return;
        }

        SpringBootTest.WebEnvironment webEnv = anno.webEnvironment();
        Map<String, Object> configMap = new HashMap<>();

        switch (webEnv) {
            case MOCK -> {
                startGrpcWithInProcess(environment, configMap);
                disableMetricsServer(configMap);
            }
            case RANDOM_PORT -> {
                startGrpcWithRandomPort(configMap);
                startMetricsWithRandomPort(configMap);
            }
            case DEFINED_PORT -> {
                // nothing to do
            }
            case NONE -> {
                disableGrpcServer(configMap);
                disableMetricsServer(configMap);
            }
            default -> {
                // nothing to do
            }
        }

        MapPropertySource ps = new MapPropertySource("moego-grpc-test-config", configMap);
        environment.getPropertySources().addFirst(ps);
    }

    private static void disableGrpcServer(Map<String, Object> configMap) {
        configMap.put(GrpcProperties.Server.PREFIX + ".enabled", false);
    }

    private static void startMetricsWithRandomPort(Map<String, Object> configMap) {
        configMap.put(GrpcProperties.Observability.Metrics.PREFIX + ".port", 0);
    }

    private static void startGrpcWithRandomPort(Map<String, Object> configMap) {
        configMap.put(GrpcProperties.Server.PREFIX + ".port", 0);
    }

    private static void disableMetricsServer(Map<String, Object> configMap) {
        configMap.put(GrpcProperties.Observability.Metrics.PREFIX + ".enabled", false);
    }

    private static void startGrpcWithInProcess(ConfigurableEnvironment environment, Map<String, Object> configMap) {
        // start gRPC server in-process way
        String serverProperty = GrpcProperties.PREFIX + ".server.in-process.name";
        String clientProperty = GrpcProperties.PREFIX + ".client.in-process.name";
        String name = UUID.randomUUID().toString();
        if (!environment.containsProperty(serverProperty)) {
            configMap.put(serverProperty, name);
        }
        if (!environment.containsProperty(clientProperty)) {
            configMap.put(clientProperty, name);
        }
    }
}
