package com.moego.lib.common.observability.logging.http;

import com.moego.lib.common.http.util.HttpUtil;
import com.moego.lib.common.observability.tracing.TracingContext;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * Logging interceptor.
 *
 * <p> Record request info, including uri, fromApp, consume time, etc.
 *
 * <AUTHOR>
 * @since 2022/9/7
 */
public class LoggingHandlerInterceptor implements HandlerInterceptor {

    private static final Logger log = LoggerFactory.getLogger(LoggingHandlerInterceptor.class);

    private static final String START_TIME = "REQUEST_START_TIME";

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        request.setAttribute(START_TIME, System.currentTimeMillis());
        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
            throws Exception {
        String fromApp = HttpUtil.getInternalOrigin(request.getHeader(TracingContext.HK_FROM_APP));
        long startTime = (long) request.getAttribute(START_TIME);
        if (ex == null) {
            log.info(
                    "uri: {}, fromApp: {}, consuming: {}, status: {} [CDATA]",
                    request.getRequestURI(),
                    fromApp,
                    System.currentTimeMillis() - startTime,
                    response.getStatus());
        } else {
            // Only happens when exception is not being resolved,
            // so no need to log stack trace here.
            log.warn(
                    "uri: {}, fromApp: {}, consuming: {}, status: {}, cause: {} [CDATA]",
                    request.getRequestURI(),
                    fromApp,
                    System.currentTimeMillis() - startTime,
                    response.getStatus(),
                    ex.getMessage());
        }
    }
}
