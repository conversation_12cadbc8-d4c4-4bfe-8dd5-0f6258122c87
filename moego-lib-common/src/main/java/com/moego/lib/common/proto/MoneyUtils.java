package com.moego.lib.common.proto;

import com.google.type.Money;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @since 2024/10/21
 */
public class MoneyUtils {

    // 保留的小数位数.
    static final int MONEY_SCALE = 2;
    // nanos 的精度， 由 google.type.Money 规定为 9.
    static final int NANO_PRECISION = 9;

    public static BigDecimal fromGoogleMoney(final Money googleMoney) {
        if (googleMoney == null) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(googleMoney.getUnits())
                .add(new BigDecimal(googleMoney.getNanos()).movePointLeft(NANO_PRECISION));
    }

    public static Money toGoogleMoney(final BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return Money.newBuilder().setUnits(0).setNanos(0).build();
        }
        return Money.newBuilder()
                .setUnits(bigDecimal.longValue())
                .setNanos(bigDecimal
                        .remainder(BigDecimal.ONE)
                        .movePointRight(NANO_PRECISION)
                        .intValue())
                .build();
    }

    public static Money toGoogleMoney(final BigDecimal bigDecimal, String currencyCode) {
        var money = toGoogleMoney(bigDecimal);
        var builder = money.toBuilder().setCurrencyCode(currencyCode);
        return builder.build();
    }

    public static boolean isPositive(final Money money) {
        return fromGoogleMoney(money).compareTo(BigDecimal.ZERO) > 0;
    }

    public static boolean isNegative(final Money money) {
        return fromGoogleMoney(money).compareTo(BigDecimal.ZERO) < 0;
    }

    public static boolean isPositive(final BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return false;
        }
        return bigDecimal.compareTo(BigDecimal.ZERO) > 0;
    }
}
