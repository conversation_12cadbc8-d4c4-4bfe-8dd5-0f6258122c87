package com.moego.lib.common.json.jackson.protobuf;

import com.fasterxml.jackson.core.JacksonException;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonSerializer;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.node.NumericNode;
import com.fasterxml.jackson.databind.node.TextNode;
import com.google.protobuf.ProtocolMessageEnum;
import java.io.IOException;
import java.util.Arrays;
import java.util.Objects;

/**
 * <AUTHOR>
 * @since 2025/4/19
 */
public final class ProtobufEnum {

    /**
     * Protobuf enum serializer, use {@link ProtocolMessageEnum#getNumber()} to serialize protobuf enum.
     *
     * @param <T> protobuf enum type
     */
    public static final class Serializer<T extends Enum<T> & ProtocolMessageEnum> extends JsonSerializer<T> {

        @Override
        public void serialize(T value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
            gen.writeNumber(value.getNumber());
        }
    }

    /**
     * Protobuf enum deserializer, unrecognized number will be deserialized as UNRECOGNIZED.
     *
     * @param <T> protobuf enum type
     */
    public static final class Deserializer<T extends Enum<T> & ProtocolMessageEnum> extends JsonDeserializer<T> {

        private final Class<T> clazz;

        public Deserializer(Class<T> clazz) {
            this.clazz = clazz;
        }

        @Override
        public T deserialize(JsonParser p, DeserializationContext ctxt) throws IOException, JacksonException {

            var treeNode = p.readValueAsTree();

            if (treeNode.isValueNode()) {
                if (treeNode instanceof NumericNode numericNode) {
                    return convertNumberToEnum(numericNode.intValue());
                }
                if (treeNode instanceof TextNode textNode) {
                    return convertTextToEnum(textNode.asText());
                }
            }

            throw new IllegalArgumentException(
                    "Can't deserialize protobuf enum '" + clazz.getSimpleName() + "' from " + treeNode);
        }

        private T convertTextToEnum(String text) {
            var enums = clazz.getEnumConstants();
            if (enums == null) {
                throw new IllegalStateException("No enum constants found for class " + clazz);
            }

            for (var e : enums) {
                if (Objects.equals(e.name(), text)) {
                    return e;
                }
            }

            return getUnrecognizedEnum(enums);
        }

        private T convertNumberToEnum(int number) {
            var enums = clazz.getEnumConstants();
            if (enums == null) {
                throw new IllegalStateException("No enum constants found for class " + clazz);
            }

            for (var e : enums) {
                if (!Objects.equals(e.name(), "UNRECOGNIZED")) { // UNRECOGNIZED getNumber() will throw exception
                    if (e.getNumber() == number) {
                        return e;
                    }
                }
            }

            // return UNRECOGNIZED
            return getUnrecognizedEnum(enums);
        }

        private T getUnrecognizedEnum(T[] enums) {
            return Arrays.stream(enums)
                    .filter(e -> Objects.equals(e.name(), "UNRECOGNIZED"))
                    .findFirst()
                    .orElseThrow(
                            () -> new IllegalStateException("No UNRECOGNIZED enum constant found for class " + clazz));
        }
    }
}
