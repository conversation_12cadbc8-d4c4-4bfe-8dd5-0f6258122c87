package com.moego.lib.common.autoconfigure.feature;

import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcClientEnabled;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcEnabled;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcServerEnabled;
import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.autoconfigure.util.BeanOrder;
import io.envoyproxy.pgv.ReflectiveValidatorIndex;
import io.envoyproxy.pgv.grpc.ValidatingClientInterceptor;
import io.envoyproxy.pgv.grpc.ValidatingServerInterceptor;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * <AUTHOR>
 * @since 2022/11/7
 */
@Configuration(proxyBeanMethods = false)
public class Validation {

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnGrpcEnabled
    static class Grpc {

        @Configuration(proxyBeanMethods = false)
        @ConditionalOnClass({ValidatingServerInterceptor.class, ValidatingClientInterceptor.class})
        static class Pgv {

            @Bean
            @ConditionalOnGrpcServerEnabled
            @ConditionalOnProperty(value = GrpcProperties.PREFIX + ".server.validate-enabled", matchIfMissing = true)
            @Order(BeanOrder.GrpcServer.VALIDATE)
            public ValidatingServerInterceptor validatingServerInterceptor() {
                return new ValidatingServerInterceptor(new ReflectiveValidatorIndex());
            }

            @Bean
            @ConditionalOnGrpcClientEnabled
            @ConditionalOnProperty(value = GrpcProperties.PREFIX + ".client.validate-enabled", matchIfMissing = true)
            @Order(BeanOrder.GrpcClient.VALIDATE)
            public ValidatingClientInterceptor validatingClientInterceptor() {
                return new ValidatingClientInterceptor(new ReflectiveValidatorIndex());
            }
        }
    }
}
