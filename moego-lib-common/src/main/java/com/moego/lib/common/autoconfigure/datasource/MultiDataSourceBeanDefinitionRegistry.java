package com.moego.lib.common.autoconfigure.datasource;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.util.StringUtils;

/**
 * Used to parse {@code moego.data-sources} configuration
 * and dynamically register {@link HikariDataSource} Beans.
 *
 * <AUTHOR>
 * @since 2024/12/10
 */
public class MultiDataSourceBeanDefinitionRegistry implements BeanDefinitionRegistryPostProcessor, EnvironmentAware {

    private Environment env;

    @Override
    public void setEnvironment(Environment environment) {
        this.env = environment;
    }

    @Override
    public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
        var properties =
                Binder.get(env).bindOrCreate(MultiDataSourceProperties.PREFIX, MultiDataSourceProperties.class);
        if (properties.getDataSources().isEmpty()) {
            return;
        }

        var hikariConfig = Binder.get(env).bindOrCreate("spring.datasource.hikari", HikariConfig.class);
        properties.merge(hikariConfig);

        for (var dataSource : properties.getDataSources()) {
            if (!StringUtils.hasText(dataSource.getName())) {
                throw new IllegalArgumentException("DataSource name must not be empty");
            }

            var beanDefinition = BeanDefinitionBuilder.rootBeanDefinition(
                            HikariDataSource.class, () -> new HikariDataSource(dataSource.getHikari()))
                    .getBeanDefinition();
            beanDefinition.setLazyInit(true);

            registry.registerBeanDefinition(dataSource.getName(), beanDefinition);
        }
    }

    @Override
    public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {}
}
