package com.moego.lib.common.exception.grpc;

import com.moego.lib.common.exception.BizException;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.exception.grpc.advice.GrpcAdvice;
import com.moego.lib.common.exception.grpc.advice.GrpcExceptionHandler;
import com.moego.lib.common.grpc.server.GrpcResponseUtil;
import com.moego.lib.common.http.util.Const;
import io.grpc.StatusRuntimeException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Basic gRPC exception advice.
 *
 * <AUTHOR>
 */
@GrpcAdvice
public class GrpcExceptionAdvice {

    private static final Logger log = LoggerFactory.getLogger(GrpcExceptionAdvice.class);

    @GrpcExceptionHandler(BizException.class)
    public StatusRuntimeException handleBizException(BizException e) {
        log.error("Caught BizException:", e);

        GrpcResponseUtil.putMetadata(Const.X_MOE_STATUS, String.valueOf(e.getCode()));

        return ExceptionUtil.toStatusRuntimeException(e);
    }

    @GrpcExceptionHandler(StatusRuntimeException.class)
    public StatusRuntimeException handleStatusRuntimeException(StatusRuntimeException e) {
        log.error("Caught StatusRuntimeException:", e);

        try {
            var code = ExceptionUtil.extractCode(e).getNumber();
            GrpcResponseUtil.putMetadata(Const.X_MOE_STATUS, String.valueOf(code));
        } catch (Exception ignored) {
        }

        return e;
    }
}
