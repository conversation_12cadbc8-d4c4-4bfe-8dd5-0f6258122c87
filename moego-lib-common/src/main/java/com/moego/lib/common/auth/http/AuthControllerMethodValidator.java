package com.moego.lib.common.auth.http;

import com.moego.lib.common.auth.Auth;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerMapping;

@Slf4j
@RequiredArgsConstructor
public class AuthControllerMethodValidator implements SmartInitializingSingleton {

    private final RequestMappingHandlerMapping handlerMapping;

    @Override
    public void afterSingletonsInstantiated() {
        validateAuthOnControllerMethod();
    }

    private void validateAuthOnControllerMethod() {
        List<String> invalidActionList = new ArrayList<>();
        handlerMapping.getHandlerMethods().forEach((key, value) -> {
            String name = value.getBeanType().getCanonicalName();
            if (!name.matches("^com\\.moego\\.server\\.\\w+\\.web\\..*$")) {
                return;
            }
            if (value.getMethodAnnotation(Auth.class) == null) {
                invalidActionList.add(name + " -> " + value.getMethod().getName());
            }
        });
        if (!invalidActionList.isEmpty()) {
            String message =
                    "All the web actions should be annotated by @Auth, please check the following actions: \n- "
                            + String.join("\n- ", invalidActionList);
            throw new IllegalStateException(message);
        }
    }
}
