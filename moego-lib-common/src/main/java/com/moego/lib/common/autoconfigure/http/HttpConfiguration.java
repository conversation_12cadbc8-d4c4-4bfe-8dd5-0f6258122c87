package com.moego.lib.common.autoconfigure.http;

import com.moego.lib.common.autoconfigure.condition.ConditionalOnHttpEnabled;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Core Configuration for HTTP services.
 *
 * <AUTHOR>
 * @since 2022/11/7
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnHttpEnabled
@EnableConfigurationProperties(HttpProperties.class)
@Import({HttpClientConfiguration.class, HttpServerConfiguration.class})
public class HttpConfiguration {}
