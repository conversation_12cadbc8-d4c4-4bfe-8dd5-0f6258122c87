package com.moego.lib.common.migrate;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.MigrateStatus;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyIdRequest;
import com.moego.idl.service.organization.v1.GetMigrateStatusRequest;
import com.moego.idl.service.organization.v1.MigrateServiceGrpc;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.SessionDataKey;
import com.moego.lib.common.exception.BizException;
import com.moego.lib.common.exception.ExceptionUtil;
import java.time.Duration;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * account structure 判断 company 是否迁移的类
 * 当所有 company 都迁移完成后，可以删除此类
 * <p>
 * 用到这个类的服务，记得配置 grpc client:
 * - service: moego.service.organization.**
 * authority: moego-svc-organization:9090
 */
@RequiredArgsConstructor
@Slf4j
public class MigrateHelper {

    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;
    private final MigrateServiceGrpc.MigrateServiceBlockingStub migrateServiceBlockingStub;
    private final LoadingCache<Long, MigrateInfo> migrationInfoByBusinessId = Caffeine.newBuilder()
            .maximumSize(10_000)
            .expireAfterWrite(Duration.ofMinutes(1))
            .build(this::getMigrationInfoRemote);

    private static final BizException ERROR =
            ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "Please refresh your page or upgrade your app.");

    public boolean isMigrate(long companyId) {
        return true;
    }

    public MigrateStatus getMigrateStatusByCompanyId(long companyId) {
        var request =
                GetMigrateStatusRequest.newBuilder().setCompanyId(companyId).build();
        var response = migrateServiceBlockingStub.getMigrateStatus(request);
        return response.getStatus();
    }

    public MigrateStatus getMigrateStatusByBusinessId(long businessId) {
        var request =
                GetMigrateStatusRequest.newBuilder().setBusinessId(businessId).build();
        var response = migrateServiceBlockingStub.getMigrateStatus(request);
        return response.getStatus();
    }

    public void blockMigrated(long companyId) {
        throw ERROR;
    }

    public boolean isMigrate(AuthContext context) {
        return context.getSessionDataBooleanValue(SessionDataKey.SESSION_DATA_COMPANY_IS_MIGRATE);
    }

    public void blockMigrated(AuthContext context) {
        if (isMigrate(context)) {
            throw ERROR;
        }
    }

    public MigrateInfo getMigrationInfo(long businessId) {
        return migrationInfoByBusinessId.get(businessId);
    }

    private MigrateInfo getMigrationInfoRemote(long businessId) {
        long companyId = businessServiceBlockingStub
                .getCompanyId(GetCompanyIdRequest.newBuilder()
                        .setBusinessId(businessId)
                        .build())
                .getCompanyId();
        boolean isMigrate = isMigrate(companyId);
        return new MigrateInfo(companyId, isMigrate);
    }
}
