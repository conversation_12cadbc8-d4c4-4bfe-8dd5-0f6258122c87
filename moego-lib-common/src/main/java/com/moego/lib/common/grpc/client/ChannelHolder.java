package com.moego.lib.common.grpc.client;

import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.grpc.GrpcUtil;
import io.grpc.Channel;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Supplier;
import lombok.experimental.UtilityClass;

/**
 * <AUTHOR>
 */
@UtilityClass
public class ChannelHolder {

    private static final ConcurrentMap<GrpcProperties.Client.Stub, Channel> cfgToChannel = new ConcurrentHashMap<>();

    /**
     * Clear cached channels.
     */
    public static void clear() {
        cfgToChannel.values().forEach(GrpcUtil::shutdownChannel);
        cfgToChannel.clear();
    }

    /**
     * Get or supply a {@link Channel}.
     *
     * @param stub  {@link GrpcProperties.Client.Stub}
     * @param channelSupplier {@link Channel} supplier
     */
    public static Channel getOrSupply(GrpcProperties.Client.Stub stub, Supplier<Channel> channelSupplier) {
        return cfgToChannel.computeIfAbsent(stub, k -> channelSupplier.get());
    }
}
