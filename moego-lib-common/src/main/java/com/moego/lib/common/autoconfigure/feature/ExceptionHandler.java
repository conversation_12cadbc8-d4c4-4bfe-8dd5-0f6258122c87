package com.moego.lib.common.autoconfigure.feature;

import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcServerEnabled;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnHttpServerEnabled;
import com.moego.lib.common.autoconfigure.util.BeanOrder;
import com.moego.lib.common.exception.grpc.GrpcExceptionAdvice;
import com.moego.lib.common.exception.grpc.advice.GrpcAdviceDiscoverer;
import com.moego.lib.common.exception.grpc.advice.GrpcAdviceExceptionHandler;
import com.moego.lib.common.exception.grpc.advice.GrpcExceptionHandlerMethodResolver;
import com.moego.lib.common.exception.grpc.advice.UncaughtExceptionHandler;
import com.moego.lib.common.exception.grpc.error.GrpcExceptionServerInterceptor;
import com.moego.lib.common.exception.http.FeignDecoderExceptionAdvice;
import com.moego.lib.common.exception.http.HttpExceptionAdvice;
import feign.Feign;
import java.util.stream.Collectors;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.cloud.openfeign.FeignClientFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;

/**
 * <AUTHOR>
 * @since 2022/11/7
 */
@Configuration(proxyBeanMethods = false)
public class ExceptionHandler {

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnHttpServerEnabled
    static class Http {

        @Bean
        public HttpExceptionAdvice httpExceptionAdvice() {
            return new HttpExceptionAdvice();
        }

        @Configuration(proxyBeanMethods = false)
        @ConditionalOnClass({Feign.class, FeignClientFactoryBean.class})
        static class FeignDecoderExceptionAdviceConfiguration {

            @Bean
            public FeignDecoderExceptionAdvice feignDecoderExceptionAdvice() {
                return new FeignDecoderExceptionAdvice();
            }
        }
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnGrpcServerEnabled
    static class Grpc {

        @Bean
        public GrpcAdviceDiscoverer grpcAdviceDiscoverer() {
            return new GrpcAdviceDiscoverer();
        }

        @Bean
        public GrpcExceptionHandlerMethodResolver grpcExceptionHandlerMethodResolver(
                GrpcAdviceDiscoverer grpcAdviceDiscoverer) {
            return new GrpcExceptionHandlerMethodResolver(grpcAdviceDiscoverer);
        }

        @Bean
        public GrpcAdviceExceptionHandler grpcAdviceExceptionHandler(
                GrpcExceptionHandlerMethodResolver grpcExceptionHandlerMethodResolver,
                ObjectProvider<UncaughtExceptionHandler> uncaughtExceptionHandlerProvider) {
            return new GrpcAdviceExceptionHandler(
                    grpcExceptionHandlerMethodResolver,
                    uncaughtExceptionHandlerProvider.orderedStream().collect(Collectors.toList()));
        }

        @Bean
        @Order(BeanOrder.GrpcServer.EXCEPTION_HANDLER)
        public GrpcExceptionServerInterceptor grpcExceptionServerInterceptor(
                GrpcAdviceExceptionHandler grpcAdviceExceptionHandler) {
            return new GrpcExceptionServerInterceptor(grpcAdviceExceptionHandler);
        }

        @Bean
        public GrpcExceptionAdvice grpcExceptionAdvice() {
            return new GrpcExceptionAdvice();
        }
    }
}
