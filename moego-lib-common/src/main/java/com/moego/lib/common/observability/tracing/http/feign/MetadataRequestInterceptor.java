package com.moego.lib.common.observability.tracing.http.feign;

import com.moego.lib.common.observability.tracing.Headers;
import com.moego.lib.common.observability.tracing.TracingContext;
import com.moego.lib.common.thread.ThreadContextHolder;
import feign.RequestInterceptor;
import feign.RequestTemplate;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;

/**
 * For FeignClient to transfer the specific headers to upstream service.
 *
 * <AUTHOR>
 * @since 2022/9/19
 */
public class MetadataRequestInterceptor implements RequestInterceptor, EnvironmentAware {

    private String applicationName;

    @Override
    public void apply(RequestTemplate template) {
        Headers headers = ThreadContextHolder.getContext(Headers.class);
        if (headers != null) {
            headers.getHeaders().forEach((key, value) -> {
                if (!template.headers().containsKey(key)) {
                    template.header(key, value);
                }
            });
        }
        addFromAppHeader(template);
    }

    private void addFromAppHeader(RequestTemplate template) {
        String fromAppKey = TracingContext.HK_FROM_APP;
        if (!template.headers().containsKey(fromAppKey) && applicationName != null) {
            template.header(fromAppKey, applicationName);
        }
    }

    @Override
    public void setEnvironment(Environment environment) {
        this.applicationName = environment.getProperty("spring.application.name");
    }
}
