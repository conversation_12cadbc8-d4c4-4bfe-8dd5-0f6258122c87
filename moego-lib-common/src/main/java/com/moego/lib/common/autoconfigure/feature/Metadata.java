package com.moego.lib.common.autoconfigure.feature;

import com.moego.lib.common.auth.AuthMetadataProcessor;
import com.moego.lib.common.autoconfigure.anno.NonProductionProfile;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcClientEnabled;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcEnabled;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcServerEnabled;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnHttpEnabled;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnHttpServerEnabled;
import com.moego.lib.common.autoconfigure.util.BeanOrder;
import com.moego.lib.common.grey.VersionMetadataProcessor;
import com.moego.lib.common.observability.tracing.MetadataProcessor;
import com.moego.lib.common.observability.tracing.TracingMetadataProcessor;
import com.moego.lib.common.observability.tracing.grpc.GrpcMetadataClientInterceptor;
import com.moego.lib.common.observability.tracing.grpc.GrpcMetadataServerInterceptor;
import com.moego.lib.common.observability.tracing.http.feign.MetadataRequestInterceptor;
import com.moego.lib.common.observability.tracing.http.mvc.MetadataFilter;
import feign.RequestInterceptor;
import jakarta.servlet.Filter;
import java.util.stream.Collectors;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.openfeign.FeignClientFactoryBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.Ordered;
import org.springframework.core.annotation.Order;

/**
 * <AUTHOR>
 * @since 2022/11/7
 */
@Configuration(proxyBeanMethods = false)
public class Metadata {

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnHttpEnabled
    static class Http {

        @Bean
        @ConditionalOnHttpServerEnabled
        public Filter metadataFilter(ObjectProvider<MetadataProcessor<?>> processors) {
            return new MetadataFilter(processors.orderedStream().collect(Collectors.toList()));
        }
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnGrpcEnabled
    static class Grpc {

        @Bean
        @ConditionalOnGrpcServerEnabled
        @Order(BeanOrder.GrpcServer.METADATA)
        public GrpcMetadataServerInterceptor metadataServerInterceptor(
                ObjectProvider<MetadataProcessor<?>> processors) {
            return new GrpcMetadataServerInterceptor(processors.orderedStream().collect(Collectors.toList()));
        }

        @Bean
        @ConditionalOnGrpcClientEnabled
        @Order(BeanOrder.GrpcClient.METADATA)
        public GrpcMetadataClientInterceptor metadataClientInterceptor() {
            return new GrpcMetadataClientInterceptor();
        }
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass({feign.Feign.class, FeignClientFactoryBean.class})
    static class Feign {

        @Bean
        @Order(Ordered.LOWEST_PRECEDENCE - 100)
        public RequestInterceptor metadataRequestInterceptor() {
            return new MetadataRequestInterceptor();
        }
    }

    @Configuration(proxyBeanMethods = false)
    static class MetadataProcessorConfiguration {

        @Bean
        public AuthMetadataProcessor authMetadataProcessor() {
            return new AuthMetadataProcessor();
        }

        @Bean
        public TracingMetadataProcessor tracingMetadataProcessor() {
            return new TracingMetadataProcessor();
        }

        @Bean
        @NonProductionProfile
        @ConditionalOnProperty(name = "moego.grey.enabled", havingValue = "true")
        public VersionMetadataProcessor versionMetadataProcessor() {
            return new VersionMetadataProcessor();
        }
    }
}
