package com.moego.lib.common.autoconfigure.http;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.NestedConfigurationProperty;

/**
 * <AUTHOR>
 * @since 2022/10/10
 */
@Data
@ConfigurationProperties(HttpProperties.PREFIX)
public class HttpProperties {

    public static final String PREFIX = "moego.http";

    private boolean enabled = true;

    @NestedConfigurationProperty
    private Client client = new Client();

    @NestedConfigurationProperty
    private Server server = new Server();

    @Data
    public static class Client {

        /**
         * Whether to enable http client grey function.
         */
        private boolean greyEnabled = true;
    }

    @Data
    public static class Server {

        @NestedConfigurationProperty
        private Auth auth = new Auth();

        @NestedConfigurationProperty
        private Observability observability = new Observability();
    }

    @Data
    public static class Auth {

        /**
         * Whether to enable http server auth function.
         */
        private boolean enabled = true;
    }

    @Data
    public static class Observability {

        @NestedConfigurationProperty
        private Metrics metrics = new Metrics();

        @NestedConfigurationProperty
        private Logging logging = new Logging();

        @Data
        public static class Metrics {

            private boolean enabled = true;
            private String[] pathPatterns = {"/**"};
            private String[] excludePathPatterns = {};
        }

        @Data
        public static class Logging {

            private boolean enabled = true;
            private String[] pathPatterns = {"/**"};
            private String[] excludePathPatterns = {};
        }
    }
}
