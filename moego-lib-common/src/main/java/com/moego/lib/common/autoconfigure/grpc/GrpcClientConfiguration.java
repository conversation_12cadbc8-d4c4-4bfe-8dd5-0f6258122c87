package com.moego.lib.common.autoconfigure.grpc;

import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcClientEnabled;
import com.moego.lib.common.grpc.client.ChannelHolder;
import com.moego.lib.common.grpc.client.GrpcClientBeanHolder;
import com.moego.lib.common.grpc.client.GrpcClientBeanPostProcessor;
import com.moego.lib.common.grpc.client.GrpcStubsBeanDefinitionRegistry;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @since 2022/11/7
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnGrpcClientEnabled
public class GrpcClientConfiguration implements DisposableBean {

    @Bean
    static GrpcClientBeanPostProcessor grpcClientBeanPostProcessor() {
        return new GrpcClientBeanPostProcessor();
    }

    @Bean
    static GrpcStubsBeanDefinitionRegistry grpcStubsBeanDefinitionRegistry() {
        return new GrpcStubsBeanDefinitionRegistry();
    }

    @Override
    public void destroy() {
        GrpcClientBeanHolder.clear();
        ChannelHolder.clear();
    }
}
