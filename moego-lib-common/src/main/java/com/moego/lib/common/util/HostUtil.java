package com.moego.lib.common.util;

import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/8/15
 */
public class HostUtil {

    private static final String PRIMARY_HOST = "pet";
    private static final String DEV_PRIMARY_HOST = "dev";
    private static final String CLIENT_HOST_PATTERN = "https://%sclient.%smoego.%s";
    private static final String BUSINESS_HOST_PATTERN = "https://%sgo.%smoego.%s";
    private static final String BRANCH_PREFIX = "feature-";
    public static final String NAMESPACE_PROD = "ns-production";
    public static final String NAMESPACE_STAGING = "ns-staging";
    public static final String NAMESPACE_TESTING = "ns-testing";
    public static final String T2_PRODUCTION_BRANCH = "production";
    public static final String NAMESPACE = System.getenv("NAMESPACE");
    public static final String BRANCH = System.getenv("APP_VERSION");

    public static String getPayOnlineClientHost(String nameSpace, String branch) {
        return getHostForDifferentEnv(nameSpace, branch, CLIENT_HOST_PATTERN);
    }

    public static String getPayOnlineClientHost() {
        return getPayOnlineClientHost(NAMESPACE, BRANCH);
    }

    public static String getBusinessHost(String nameSpace, String branch) {
        return getHostForDifferentEnv(nameSpace, branch, BUSINESS_HOST_PATTERN);
    }

    private static String getHostForDifferentEnv(String nameSpace, String branch, String hostPattern) {
        String firstDomain = PRIMARY_HOST;
        String secondaryDomain = "";
        String thirdDomain = "";
        if (NAMESPACE_STAGING.equals(nameSpace)) {
            firstDomain = DEV_PRIMARY_HOST;
            secondaryDomain = "s1.";
        } else if (NAMESPACE_TESTING.equals(nameSpace)) {
            if (!StringUtils.hasText(branch)) {
                branch = "online";
            } else if (branch.startsWith(BRANCH_PREFIX)) {
                branch = branch.substring(BRANCH_PREFIX.length());
            }
            firstDomain = DEV_PRIMARY_HOST;
            secondaryDomain = "t2.";
            thirdDomain = getTestingThirdDomainPrefix(branch, thirdDomain);
        }

        return String.format(hostPattern, thirdDomain, secondaryDomain, firstDomain);
    }

    private static String getTestingThirdDomainPrefix(String branch, String thirdDomain) {
        if (!T2_PRODUCTION_BRANCH.equals(branch)) {
            thirdDomain = branch + "-grey-";
        }
        return thirdDomain;
    }

    public static String getBusinessHost() {
        return getBusinessHost(NAMESPACE, BRANCH);
    }
}
