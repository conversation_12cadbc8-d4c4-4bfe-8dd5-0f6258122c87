package com.moego.lib.common.util;

import com.moego.lib.common.observability.tracing.RequestHolder;
import com.moego.lib.common.thread.ThreadContextHolder;
import jakarta.servlet.http.HttpServletRequest;
import lombok.experimental.UtilityClass;
import org.springframework.util.StringUtils;

@UtilityClass
public class RequestUtils {

    private static final String X_FORWARDED_FOR_HEADER = "X-Forwarded-For";

    private static final String X_FORWARDED_FOR_HEADER_DELIMITER = ",";

    private static final String USER_AGENT = "User-Agent";

    private static final int USER_AGENT_LENGTH = 255;

    private static final String REFERER = "Referer";

    private static final String DEVICE_ID = "X-Moe-Device-Id";

    private static final int REFERER_LENGTH = 255;

    /**
     * 获取请求的 IP
     *
     * @param request
     * @return
     */
    public static String getIP(HttpServletRequest request) {
        String xForwardedFor = request.getHeader(X_FORWARDED_FOR_HEADER);
        if (xForwardedFor != null) {
            return xForwardedFor.split(X_FORWARDED_FOR_HEADER_DELIMITER)[0].trim();
        }
        return request.getRemoteAddr();
    }

    /**
     * 获取请求的 user agent
     * @param request
     * @return
     */
    public static String getUserAgent(HttpServletRequest request) {
        String ua = request.getHeader(USER_AGENT);
        if (!StringUtils.hasText(ua)) {
            return "";
        }
        // user agent 长度判断，太长了就截断
        if (ua.length() > USER_AGENT_LENGTH) {
            ua = ua.substring(0, USER_AGENT_LENGTH);
        }
        return ua;
    }

    /**
     * 获取请求的 referer
     * @param request
     * @return
     */
    public static String getReferer(HttpServletRequest request) {
        String referer = request.getHeader(REFERER);
        if (!StringUtils.hasText(referer)) {
            return "";
        }
        // referer 长度判断，太长了就截断
        if (referer.length() > REFERER_LENGTH) {
            referer = referer.substring(0, REFERER_LENGTH);
        }
        return referer;
    }

    /**
     * 获取请求的 IP
     *
     * @return ip
     */
    public static String getIP() {
        String xForwardedFor = getHeader(X_FORWARDED_FOR_HEADER);
        if (xForwardedFor != null) {
            return xForwardedFor.split(X_FORWARDED_FOR_HEADER_DELIMITER)[0].trim();
        }

        RequestHolder holder = ThreadContextHolder.getContext(RequestHolder.class);
        if (holder == null) {
            return "";
        }

        String remoteAddr = holder.remoteAddr();
        return remoteAddr != null ? remoteAddr : "";
    }

    /**
     * 获取请求的 user agent
     *
     * @return user agent
     */
    public static String getUserAgent() {
        String ua = getHeader(USER_AGENT);
        if (!StringUtils.hasText(ua)) {
            return "";
        }
        // user agent 长度判断，太长了就截断
        if (ua.length() > USER_AGENT_LENGTH) {
            ua = ua.substring(0, USER_AGENT_LENGTH);
        }
        return ua;
    }

    /**
     * 获取请求的 referer
     *
     * @return referer
     */
    public static String getReferer() {
        String referer = getHeader(REFERER);
        if (!StringUtils.hasText(referer)) {
            return "";
        }
        // referer 长度判断，太长了就截断
        if (referer.length() > REFERER_LENGTH) {
            referer = referer.substring(0, REFERER_LENGTH);
        }
        return referer;
    }

    public static String getHost() {
        RequestHolder holder = ThreadContextHolder.getContext(RequestHolder.class);
        if (holder == null) {
            return "";
        }

        String host = holder.host();
        return host != null ? host : "";
    }

    public static String getDeviceId() {
        String deviceId = getHeader(DEVICE_ID);
        return StringUtils.hasText(deviceId) ? deviceId : "";
    }

    /**
     * Get header value from current thread context.
     *
     * <p> Can be used in HTTP/gRPC server side.
     *
     * @param key header key
     * @return header value
     */
    public static String getHeader(String key) {
        RequestHolder holder = ThreadContextHolder.getContext(RequestHolder.class);
        if (holder == null) {
            return null;
        }
        return holder.getHeader(key);
    }
}
