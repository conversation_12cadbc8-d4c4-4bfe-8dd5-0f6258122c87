package com.moego.lib.common.migrate;

import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.MigrateServiceGrpc;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Lazy;

@AutoConfiguration
@ConditionalOnClass(CompanyServiceGrpc.CompanyServiceBlockingStub.class)
public class MigrateAutoConfiguration {

    @Lazy
    @Bean
    public MigrateHelper migrateHelper(
            BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub,
            MigrateServiceGrpc.MigrateServiceBlockingStub migrateServiceBlockingStub) {
        return new MigrateHelper(businessServiceBlockingStub, migrateServiceBlockingStub);
    }
}
