package com.moego.lib.common.autoconfigure.grpc;

import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcEnabled;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

/**
 * Core Configuration for MoeGo gRPC services.
 *
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnGrpcEnabled
@EnableConfigurationProperties(GrpcProperties.class)
@Import({GrpcClientConfiguration.class, GrpcServerConfiguration.class})
public class GrpcConfiguration {}
