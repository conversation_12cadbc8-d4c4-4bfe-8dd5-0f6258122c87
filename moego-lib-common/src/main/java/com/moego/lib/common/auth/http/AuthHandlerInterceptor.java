package com.moego.lib.common.auth.http;

import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.auth.LoginTimeChecker;
import jakarta.annotation.Nullable;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

public class AuthHandlerInterceptor implements HandlerInterceptor {

    private final LoginTimeChecker loginTimeChecker;

    public AuthHandlerInterceptor(@Nullable StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub) {
        this.loginTimeChecker = new LoginTimeChecker(staffServiceBlockingStub);
    }

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        if (!(handler instanceof HandlerMethod method)) {
            return true;
        }
        Auth auth = method.getMethodAnnotation(Auth.class);
        if (auth == null || auth.value() == AuthType.ANONYMOUS) {
            return true;
        }
        AuthContext context = AuthContext.get();
        context.checkValid(auth.value());
        // impersonate 场景不校验
        if (!StringUtils.hasText(context.impersonator())
                && (auth.value().equals(AuthType.COMPANY) || auth.value().equals(AuthType.BUSINESS))) {
            loginTimeChecker.check(context.staffId());
        }
        return true;
    }
}
