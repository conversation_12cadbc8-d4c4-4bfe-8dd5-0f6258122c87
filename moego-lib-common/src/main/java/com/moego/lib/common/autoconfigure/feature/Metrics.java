package com.moego.lib.common.autoconfigure.feature;

import static org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication.Type.SERVLET;

import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcServerEnabled;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnHttpServerEnabled;
import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.autoconfigure.http.HttpProperties;
import com.moego.lib.common.autoconfigure.util.BeanOrder;
import com.moego.lib.common.grpc.ServerStarter;
import com.moego.lib.common.observability.metrics.prometheus.grpc.MetricsClientInterceptor;
import com.moego.lib.common.observability.metrics.prometheus.grpc.MetricsHttpServer;
import com.moego.lib.common.observability.metrics.prometheus.grpc.MetricsServerInterceptor;
import com.moego.lib.common.observability.metrics.prometheus.http.HttpMetricsServletRegistrationBean;
import com.moego.lib.common.observability.metrics.prometheus.http.MetricsHandlerInterceptor;
import com.moego.lib.common.observability.metrics.prometheus.timer.TimerMetricsAspect;
import com.moego.lib.common.thread.ThreadPoolMetricExporter;
import com.moego.lib.common.thread.http.TomcatThreadPoolRegister;
import io.grpc.ClientInterceptor;
import io.prometheus.client.Gauge;
import io.prometheus.client.exporter.HTTPServer;
import io.prometheus.client.hotspot.DefaultExports;
import io.prometheus.client.servlet.jakarta.exporter.MetricsServlet;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnNotWebApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.autoconfigure.condition.ConditionalOnSingleCandidate;
import org.springframework.boot.autoconfigure.condition.ConditionalOnWebApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.core.env.Environment;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * <AUTHOR>
 * @since 2022/11/7
 */
@Configuration(proxyBeanMethods = false)
public class Metrics {

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnHttpServerEnabled
    @ConditionalOnProperty(
            value = HttpProperties.PREFIX + ".server.observability.metrics.enabled",
            matchIfMissing = true)
    static class Http {

        @Configuration(proxyBeanMethods = false)
        @ConditionalOnClass({Gauge.class, MetricsServlet.class})
        static class Prometheus {

            @Bean
            public WebMvcConfigurer moeMetricsWebMvcConfigurer(HttpProperties properties, Environment env) {
                // init default prometheus exporter
                DefaultExports.initialize();
                return new WebMvcConfigurer() {
                    @Override
                    public void addInterceptors(InterceptorRegistry registry) {
                        HttpProperties.Observability.Metrics metrics =
                                properties.getServer().getObservability().getMetrics();
                        registry.addInterceptor(new MetricsHandlerInterceptor(
                                        env.getProperty("spring.application.name", "app")))
                                .addPathPatterns(metrics.getPathPatterns())
                                .excludePathPatterns(metrics.getExcludePathPatterns());
                    }
                };
            }

            @Bean
            @ConditionalOnMissingBean
            public HttpMetricsServletRegistrationBean httpMetricsServletRegistrationBean() {
                return new HttpMetricsServletRegistrationBean();
            }

            @Bean
            @ConditionalOnSingleCandidate(ThreadPoolMetricExporter.class)
            public TomcatThreadPoolRegister tomcatThreadPoolRegister(ThreadPoolMetricExporter exporter) {
                return new TomcatThreadPoolRegister(exporter);
            }
        }
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnGrpcServerEnabled
    @ConditionalOnProperty(value = GrpcProperties.Observability.Metrics.PREFIX + ".enabled", matchIfMissing = true)
    static class Grpc {

        @Configuration(proxyBeanMethods = false)
        @ConditionalOnClass({Gauge.class})
        static class Prometheus {

            @Bean
            @Order(BeanOrder.GrpcServer.METRICS)
            public MetricsServerInterceptor metricsServerInterceptor() {
                return new MetricsServerInterceptor();
            }

            @Bean
            @Order(BeanOrder.GrpcClient.METRICS)
            public ClientInterceptor metricsClientInterceptor() {
                return new MetricsClientInterceptor();
            }

            @Configuration(proxyBeanMethods = false)
            @ConditionalOnWebApplication(type = SERVLET)
            @ConditionalOnClass({MetricsServlet.class})
            static class WebApp {

                @Bean
                @ConditionalOnMissingBean
                public HttpMetricsServletRegistrationBean httpMetricsServletRegistrationBean() {
                    return new HttpMetricsServletRegistrationBean();
                }
            }

            @Configuration(proxyBeanMethods = false)
            @ConditionalOnNotWebApplication
            @ConditionalOnClass(HTTPServer.class)
            static class NoneWebApp {

                @Bean
                public MetricsHttpServer metricsHttpServer(GrpcProperties properties, ServerStarter serverStarter) {
                    return new MetricsHttpServer(properties, serverStarter);
                }
            }
        }
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass({Gauge.class})
    static class PrometheusConfiguration {

        @Bean
        public ThreadPoolMetricExporter threadPoolMetricExporter(ApplicationContext context) {
            return new ThreadPoolMetricExporter(context);
        }

        @Bean
        public TimerMetricsAspect timerMetricsAspect() {
            return new TimerMetricsAspect();
        }
    }
}
