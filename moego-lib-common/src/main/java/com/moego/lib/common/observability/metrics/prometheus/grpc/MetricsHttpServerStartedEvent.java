package com.moego.lib.common.observability.metrics.prometheus.grpc;

import io.prometheus.client.exporter.HTTPServer;
import org.springframework.context.ApplicationEvent;

/**
 * <AUTHOR>
 * @since 2022/8/23
 */
public class MetricsHttpServerStartedEvent extends ApplicationEvent {

    public MetricsHttpServerStartedEvent(HTTPServer server) {
        super(server);
    }

    @Override
    public HTTPServer getSource() {
        return (HTTPServer) super.getSource();
    }
}
