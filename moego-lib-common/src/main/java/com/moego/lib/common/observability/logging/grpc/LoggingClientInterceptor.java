package com.moego.lib.common.observability.logging.grpc;

import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;
import com.moego.lib.common.util.JsonUtil;
import io.grpc.CallOptions;
import io.grpc.Channel;
import io.grpc.ClientCall;
import io.grpc.ClientInterceptor;
import io.grpc.ForwardingClientCall;
import io.grpc.ForwardingClientCallListener;
import io.grpc.Metadata;
import io.grpc.Metadata.Key;
import io.grpc.MethodDescriptor;
import io.grpc.Status;
import java.util.LinkedHashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LoggingClientInterceptor implements ClientInterceptor {

    private static final Logger log = LoggerFactory.getLogger(LoggingClientInterceptor.class);
    private static final String X_MOE_FORCE_LOG = "x-moe-force-log";

    @Override
    public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(
            MethodDescriptor<ReqT, RespT> method, CallOptions callOptions, Channel next) {
        return new ForwardingClientCall.SimpleForwardingClientCall<ReqT, RespT>(next.newCall(method, callOptions)) {

            private final long startTime = System.currentTimeMillis();
            private ReqT requestMessage;
            private RespT responseMessage;

            @Override
            public void start(Listener<RespT> responseListener, Metadata headers) {
                super.start(
                        new ForwardingClientCallListener.SimpleForwardingClientCallListener<RespT>(responseListener) {

                            @Override
                            public void onMessage(RespT message) {
                                if (message instanceof Message) {
                                    Message original = (Message) message;
                                    responseMessage = (RespT) original;
                                } else {
                                    responseMessage = message;
                                }
                                super.onMessage(message);
                            }

                            @Override
                            public void onClose(Status status, Metadata trailers) {
                                if (shouldLog(headers, status)) {
                                    long duration = System.currentTimeMillis() - startTime;

                                    Map<String, Object> logFields = new LinkedHashMap<>();
                                    logFields.put("method", method.getFullMethodName());
                                    logFields.put("service_name", method.getServiceName());
                                    logFields.put("address", next.authority());
                                    logFields.put("duration_ms", duration);
                                    logFields.put("headers", headers);
                                    logFields.put("status", status.getCode().name());

                                    if (requestMessage != null) {
                                        logFields.put("request", formatMessage(requestMessage));
                                    }

                                    if (status.isOk()) {
                                        logFields.put(
                                                "response",
                                                formatMessage(responseMessage != null ? responseMessage : trailers));
                                        log.info("{}", JsonUtil.toJson(logFields));
                                    } else {
                                        logFields.put("error_response", formatMessage(trailers));
                                        log.error("{}", JsonUtil.toJson(logFields));
                                    }
                                }

                                super.onClose(status, trailers);
                            }
                        },
                        headers);
            }

            @Override
            public void sendMessage(ReqT message) {
                if (message instanceof Message) {
                    Message original = (Message) message;
                    requestMessage = (ReqT) original;
                    super.sendMessage((ReqT) original);
                } else {
                    requestMessage = message;
                    super.sendMessage(message);
                }
            }

            private Object formatMessage(Object message) {
                if (message instanceof Message) {
                    try {
                        return JsonFormat.printer().print((Message) message);
                    } catch (Exception e) {
                        // fallback
                        return message.toString();
                    }
                }
                return message;
            }
        };
    }

    private boolean shouldLog(Metadata headers, Status status) {
        if (headers.containsKey(Key.of(X_MOE_FORCE_LOG, Metadata.ASCII_STRING_MARSHALLER))) {
            return true;
        }
        if (!status.isOk()) {
            return true;
        }
        return false;
    }
}
