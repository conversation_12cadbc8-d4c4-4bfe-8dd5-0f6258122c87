/*
 * @since 2022-06-24 22:08:54
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.lib.common.proto;

import com.google.protobuf.Any;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.ListValue;
import com.google.protobuf.Message;
import com.google.protobuf.NullValue;
import com.google.protobuf.Struct;
import com.google.protobuf.Value;
import com.google.protobuf.util.JsonFormat;
import com.moego.lib.common.util.JsonUtil;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.experimental.UtilityClass;

@UtilityClass
public class ProtoUtils {

    private static final JsonFormat.Parser parser = JsonFormat.parser().ignoringUnknownFields();

    public static Any toAny(@Nullable Object obj) {
        if (obj == null) {
            return Any.pack(
                    Value.newBuilder().setNullValue(NullValue.NULL_VALUE).build());
        }
        if (obj instanceof Any) {
            return (Any) obj;
        }
        if (obj instanceof Message) {
            return Any.pack((Message) obj);
        }
        return Any.pack(toValue(obj));
    }

    public static Value toValue(Object obj) {
        if (obj instanceof Value) {
            return (Value) obj;
        }
        Value.Builder builder = Value.newBuilder();
        try {
            parser.merge(JsonUtil.toJson(obj), builder);
        } catch (InvalidProtocolBufferException e) {
            // TODO(Freeman): maybe ignore ?
            throw new RuntimeException(e);
        }
        return builder.build();
    }

    /**
     * Convert map to Protobuf {@link Struct}.
     *
     * @param map map
     * @return Protobuf {@link Struct}
     */
    public static Struct mapToStruct(Map<?, ?> map) {
        if (map == null) {
            return Struct.newBuilder().build();
        }

        var struct = Struct.newBuilder();
        map.forEach((k, v) -> struct.putFields(k.toString(), objectToValue(v)));
        return struct.build();
    }

    /**
     * Convert Protobuf {@link Struct} to map.
     *
     * @param struct protobuf {@link Struct}
     * @return map
     */
    public static Map<String, Object> structToMap(Struct struct) {
        if (struct == null) {
            return Map.of();
        }

        Map<String, Object> map = new LinkedHashMap<>();
        struct.getFieldsMap().forEach((key, structValue) -> {
            Object value = valueToObject(structValue);
            map.put(key, value);
        });
        return map;
    }

    public static Value objectToValue(Object obj) {
        if (obj == null) {
            return Value.newBuilder().setNullValue(NullValue.NULL_VALUE).build();
        }

        if (obj instanceof Map<?, ?> map) {
            return Value.newBuilder().setStructValue(mapToStruct(map)).build();
        }

        if (obj instanceof Collection<?> collection) {
            return Value.newBuilder()
                    .setListValue(collectionToListValue(collection))
                    .build();
        }

        if (obj instanceof Boolean b) {
            return Value.newBuilder().setBoolValue(b).build();
        }

        if (obj instanceof String s) {
            return Value.newBuilder().setStringValue(s).build();
        }

        if (obj instanceof Number n) {
            return Value.newBuilder().setNumberValue(n.doubleValue()).build();
        }

        // 其他对象类型尝试使用 json 序列化
        return toValue(obj);
    }

    public static Object valueToObject(Value value) {
        if (value == null) {
            return null;
        }

        return switch (value.getKindCase()) {
            case STRUCT_VALUE -> structToMap(value.getStructValue());
            case LIST_VALUE -> listValueToList(value.getListValue());
            case BOOL_VALUE -> value.getBoolValue();
            case STRING_VALUE -> value.getStringValue();
            case NUMBER_VALUE -> value.getNumberValue();
            default -> null;
        };
    }

    public static ListValue collectionToListValue(Collection<?> collection) {
        if (collection == null) {
            return ListValue.newBuilder().build();
        }

        var list = ListValue.newBuilder();
        collection.forEach(v -> list.addValues(objectToValue(v)));
        return list.build();
    }

    public static List<?> listValueToList(ListValue listValue) {
        if (listValue == null) {
            return List.of();
        }

        return listValue.getValuesList().stream().map(ProtoUtils::valueToObject).collect(Collectors.toList());
    }
}
