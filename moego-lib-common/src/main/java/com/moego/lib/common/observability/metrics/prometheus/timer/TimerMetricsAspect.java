package com.moego.lib.common.observability.metrics.prometheus.timer;

import io.prometheus.client.Gauge;
import java.time.Instant;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Value;

@Aspect
@Slf4j
public class TimerMetricsAspect {

    @Value("${spring.application.name:UNKNOWN-SERVER}")
    private String serverName;

    private static final Gauge gauge = Gauge.build()
            .name("moego_method_consuming")
            .help("Moego Method Consuming")
            .labelNames("server", "group", "method")
            .register();

    @Around("@annotation(com.moego.lib.common.observability.metrics.prometheus.timer.TimerMetrics)")
    public Object around(ProceedingJoinPoint joinPoint) throws Throwable {
        MethodSignature methodSignature = (MethodSignature) joinPoint.getSignature();
        // className#methodName
        String classAndMethodName = String.join("#", methodSignature.getDeclaringTypeName(), methodSignature.getName());
        TimerGroup group =
                methodSignature.getMethod().getAnnotation(TimerMetrics.class).group();

        long startMs = Instant.now().toEpochMilli();
        try {
            return joinPoint.proceed();
        } finally {
            long endMs = Instant.now().toEpochMilli();
            long duration = endMs - startMs;

            log.info("group: {}, method: {}, time usage (ms): {}", group.getValue(), classAndMethodName, duration);
            gauge.labels(serverName, group.getValue(), classAndMethodName).set(duration);
        }
    }
}
