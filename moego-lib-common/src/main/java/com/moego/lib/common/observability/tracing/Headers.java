package com.moego.lib.common.observability.tracing;

import com.moego.lib.common.observability.tracing.grpc.GrpcMetadataServerInterceptor;
import com.moego.lib.common.observability.tracing.http.mvc.MetadataFilter;
import java.util.HashMap;
import java.util.Map;

/**
 * Structure to hold headers, which will be forwarded to upstream services.
 *
 * <p> No matter what server framework you are using (gRPC, HTTP, etc.), uniformly use the structure to pass the headers.
 *
 * <AUTHOR>
 * @see GrpcMetadataServerInterceptor
 * @see MetadataFilter
 * @since 2022/9/27
 */
public class Headers {

    private final Map<String, String> headers;

    public Headers() {
        this(new HashMap<>());
    }

    public Headers(Map<String, String> headers) {
        this.headers = headers;
    }

    public void put(String key, String value) {
        headers.put(key, value);
    }

    public String get(String key) {
        return headers.get(key);
    }

    public Map<String, String> getHeaders() {
        return headers;
    }
}
