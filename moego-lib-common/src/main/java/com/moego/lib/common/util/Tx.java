package com.moego.lib.common.util;

import static org.springframework.transaction.support.TransactionSynchronizationManager.isActualTransactionActive;
import static org.springframework.transaction.support.TransactionSynchronizationManager.isSynchronizationActive;
import static org.springframework.transaction.support.TransactionSynchronizationManager.registerSynchronization;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Supplier;
import org.springframework.transaction.support.TransactionOperations;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.util.ClassUtils;
import org.springframework.util.function.SingletonSupplier;

/**
 * <AUTHOR>
 * @since 2024/8/1
 */
public final class Tx {

    private static final boolean IS_SPRING_TRANSACTION_PRESENT = ClassUtils.isPresent(
            "org.springframework.transaction.support.TransactionSynchronizationManager", Tx.class.getClassLoader());

    private static final SingletonSupplier<TransactionOperations> transactionOperations = SingletonSupplier.of(() ->
            SpringUtil.getContext().getBeanProvider(TransactionOperations.class).getIfUnique());

    private Tx() {
        throw new IllegalStateException("Utility class cannot be instantiated!");
    }

    /**
     * Register a callback to be invoked after transaction commit.
     * <p> If not in a transaction, the callback will be run immediately.
     *
     * <p> Example: publish a message after appointment is created, do nothing if the transaction is rolled back.
     *
     * <pre>{@code
     * @Service
     * public class AppointmentService {
     *
     *   private final Repository repository;
     *   private final MessageQueue mq;
     *   // ... constructor
     *
     *   @Transactional
     *   public void createAppointment(Appointment appointment) {
     *
     *     // ...
     *     repository.save(appointment);
     *
     *     // ...
     *
     *     Tx.doAfterCommit(() -> mq.publish(...));
     *   }
     * }
     * }</pre>
     *
     * @param runnable runnable
     */
    public static void doAfterCommit(Runnable runnable) {
        if (IS_SPRING_TRANSACTION_PRESENT && isSynchronizationActive() && isActualTransactionActive()) {
            registerSynchronization(new AfterCommitTask(runnable));
        } else {
            runnable.run();
        }
    }

    /**
     * Execute the runnable in a transaction.
     *
     * @param runnable runnable
     */
    public static void doInTransaction(Runnable runnable) {
        var tx = transactionOperations.get();
        if (tx != null) {
            tx.executeWithoutResult(__ -> runnable.run());
        } else {
            runnable.run();
        }
    }

    /**
     * Execute the supplier in a transaction and return the result.
     *
     * @param supplier supplier
     * @param <T> return type
     * @return result
     */
    public static <T> T doInTransaction(Supplier<T> supplier) {
        var tx = transactionOperations.get();
        if (tx != null) {
            return tx.execute(__ -> supplier.get());
        } else {
            return supplier.get();
        }
    }

    private static final class AfterCommitTask implements TransactionSynchronization {

        private final AtomicBoolean invoked = new AtomicBoolean(false); // make sure the callback is only invoked once
        private final Runnable runnable;

        AfterCommitTask(Runnable runnable) {
            this.runnable = runnable;
        }

        @Override
        public void afterCompletion(int status) {

            // 在触发 afterCompletion 之前会调用 TransactionSynchronizationManager.clearSynchronization()，
            // 因此在 afterCompletion 里 TransactionSynchronizationManager.isSynchronizationActive() 一定是 false
            // See org.springframework.transaction.support.AbstractPlatformTransactionManager#processCommit

            if (status == STATUS_COMMITTED && invoked.compareAndSet(false, true)) {
                runnable.run();
            }
        }
    }
}
