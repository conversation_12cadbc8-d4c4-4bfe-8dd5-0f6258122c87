package com.moego.lib.common.exception;

import com.google.protobuf.Any;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.util.Timestamps;
import com.google.rpc.Status;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.errors.v1.CommonError;
import com.moego.lib.common.grpc.GrpcUtil;
import com.moego.lib.common.proto.ProtoUtils;
import io.grpc.Metadata;
import io.grpc.StatusRuntimeException;
import io.grpc.protobuf.StatusProto;
import jakarta.annotation.Nullable;
import lombok.experimental.UtilityClass;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2022/10/21
 */
@UtilityClass
public class ExceptionUtil {

    public static BizException bizException(Code code) {
        return bizException(code, extractMessage(code));
    }

    public static BizException bizException(Code code, String message) {
        return bizException(code, message, message);
    }

    public static BizException bizException(Code code, String message, Object data) {
        return new BizException(code.getNumber(), message, data);
    }

    public static BizException bizException(Code code, Throwable throwable) {
        return bizException(code, throwable, extractMessage(code));
    }

    public static BizException bizException(Code code, Throwable throwable, String message) {
        return bizException(code, throwable, message, message);
    }

    public static BizException bizException(Code code, Throwable throwable, String message, Object data) {
        return new BizException(code.getNumber(), message, data, throwable);
    }

    public static BizException bizException(Code code, String message, Object data, String cause) {
        return new BizException(code.getNumber(), message, data, cause);
    }

    /**
     * Check whether the exception is a {@link BizException}.
     *
     * @param ex exception
     * @return true if the exception is a {@link BizException}
     */
    public static boolean isBizException(Throwable ex) {
        if (ex == null) {
            return false;
        }
        if (ex instanceof BizException) {
            return true;
        }
        return isBizException(ex.getCause());
    }

    @Nullable
    public static BizException extractBizException(@Nullable Throwable ex) {
        if (ex == null) {
            return null;
        }
        if (ex instanceof BizException bizException) {
            return bizException;
        }
        return extractBizException(ex.getCause());
    }

    static String extractMessage(Code code) {
        String name = code.name();
        if (name.startsWith("CODE_")) {
            name = name.substring("CODE_".length());
        }
        return StringUtils.capitalize(name.toLowerCase().replace("_", " "));
    }

    public static StatusRuntimeException toStatusRuntimeException(Throwable t) {
        if (t instanceof StatusRuntimeException e) {
            return e;
        }
        if (t instanceof BizException e) {
            return toStatusRuntimeException(e);
        }

        var status = com.google.rpc.Status.newBuilder()
                .setCode(com.google.rpc.Code.INTERNAL_VALUE)
                .setMessage(truncate(getRootCauseMessage(t), 2000))
                .addDetails(Any.pack(toCommonError(new BizException(500, t))))
                .build();
        return StatusProto.toStatusRuntimeException(status);
    }

    public static StatusRuntimeException toStatusRuntimeException(BizException e) {
        var code = e.getCode() == null ? Code.CODE_UNSPECIFIED : GrpcUtil.toRpcCode(e.getCode());
        var status = com.google.rpc.Status.newBuilder()
                .setCode(code.getNumber())
                .setMessage(truncate(e.getMessage(), 2000))
                .addDetails(Any.pack(toCommonError(e)))
                .build();
        return StatusProto.toStatusRuntimeException(status);
    }

    private static String getRootCauseMessage(Throwable throwable) {
        Throwable cause = throwable.getCause();
        if (cause == null) {
            return throwable.getMessage();
        }
        return getRootCauseMessage(cause);
    }

    /**
     * Convert {@link BizException} to {@link CommonError}
     *
     * @param e biz exception
     * @return {@link CommonError}
     */
    public static CommonError toCommonError(BizException e) {
        CommonError.Builder builder = CommonError.newBuilder();
        if (e.getCode() != null) {
            var code = Code.forNumber(e.getCode());
            if (code != null) {
                builder.setCode(code);
            }
        }
        if (e.getMessage() != null) {
            builder.setMessage(truncate(e.getMessage(), 2000));
        }
        if (e.getCausedBy() != null) {
            builder.setCausedBy(truncate(e.getCausedBy(), 1000));
        }
        if (e.getTimestamp() != null) {
            builder.setTimestamp(Timestamps.fromMillis(e.getTimestamp()));
        }
        if (e.getXRequestId() != null) {
            builder.setXRequestId(e.getXRequestId());
        }
        if (e.getData() != null) {
            builder.setData(ProtoUtils.toAny(e.getData()));
        }
        return builder.build();
    }

    private static String truncate(String str, int maxCount) {
        if (str == null) {
            return "";
        }
        if (str.length() <= maxCount) {
            return str;
        }

        return str.substring(0, maxCount) + " ... " + str.length() + "bytes";
    }

    /**
     * Extract details byte array from {@link StatusRuntimeException}.
     *
     * @param e gRPC StatusRuntimeException
     * @return byte array
     * @throws StatusRuntimeException if no details found in current {@link StatusRuntimeException}
     */
    /* For testing, not private */
    static byte[] extractDetails(StatusRuntimeException e) {
        Metadata trailers = e.getTrailers();
        Metadata.Key<byte[]> detailsKey = Metadata.Key.of("grpc-status-details-bin", Metadata.BINARY_BYTE_MARSHALLER);
        if (trailers == null || !trailers.containsKey(detailsKey)) {
            // no details in trailers, just throw the exception
            throw e;
        }
        return trailers.get(detailsKey);
    }

    /**
     * Extract {@link Status} from {@link StatusRuntimeException}'s details.
     *
     * @param e gRPC StatusRuntimeException
     * @return {@link Status}
     */
    /* For testing, not private */
    static Status extractDetailsStatus(StatusRuntimeException e) {
        try {
            return Status.parseFrom(extractDetails(e));
        } catch (InvalidProtocolBufferException ex) {
            throw new RuntimeException(ex);
        }
    }

    /**
     * Extract {@link CommonError} from {@link StatusRuntimeException}.
     *
     * @param e gRPC StatusRuntimeException
     * @return {@link CommonError}
     * @throws StatusRuntimeException if details list is empty in current {@link StatusRuntimeException}
     */
    public static CommonError extractCommonError(StatusRuntimeException e) {
        Status status = extractDetailsStatus(e);
        if (CollectionUtils.isEmpty(status.getDetailsList())) {
            // this is not what we want, throw the raw exception
            throw e;
        }
        Any any = status.getDetails(0);
        // we only care about CommonError
        if (any.getTypeUrl().endsWith(CommonError.getDescriptor().getFullName())) {
            try {
                return CommonError.parseFrom(any.getValue());
            } catch (InvalidProtocolBufferException ex) {
                throw new RuntimeException(ex);
            }
        }
        // this is not what we want, throw the raw exception
        // FIXME(freeman): This will result in an incorrect exception stack being caught
        // FIXME(freeman): This maybe result in an incorrect response status code?
        throw e;
    }

    /**
     * Extract {@link Code} from {@link StatusRuntimeException}.
     *
     * <p> If upstream returns an Exception that we expected, we need to be able to recognize it.
     *
     * <p> NOTE: this code is business code, not gRPC code.
     *
     * <p> Typical usage:
     * <pre>{@code
     * try {
     *     TodoModel model = todoBlockingClient.createTodo(xx);
     * } catch (StatusRuntimeException e) {
     *     if (Code.CODE_ORDERID_EXISTS != ExceptionUtil.extractCode(e)) {
     *         throw e;
     *     }
     *     // do your logic here ...
     * }
     * }</pre>
     * <p>
     * Extract {@link Code} from {@link StatusRuntimeException}.
     *
     * @param e gRPC StatusRuntimeException
     * @return {@link Code}
     */
    public static Code extractCode(StatusRuntimeException e) {
        return extractCommonError(e).getCode();
    }
}
