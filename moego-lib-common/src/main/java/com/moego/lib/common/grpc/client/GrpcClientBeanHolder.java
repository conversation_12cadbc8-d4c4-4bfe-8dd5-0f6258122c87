package com.moego.lib.common.grpc.client;

import com.moego.lib.common.grpc.GrpcUtil;
import io.grpc.stub.AbstractStub;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2022/10/11
 * @deprecated since 2023/4/17 by <PERSON>, prefer to use {@link GrpcStubsBeanDefinitionRegistry}.
 */
@Deprecated
public class GrpcClientBeanHolder {

    private static final Map<String, AbstractStub<?>> stubs = new HashMap<>();

    public static void put(String name, AbstractStub stub) {
        stubs.put(GrpcUtil.determineRealClientName(name, stub.getClass()), stub);
    }

    public static AbstractStub<?> get(String name, Class<? extends AbstractStub> stubClass) {
        return stubs.get(GrpcUtil.determineRealClientName(name, stubClass));
    }

    public static void clear() {
        Collection<AbstractStub<?>> stubList = stubs.values();
        stubList.forEach(stub -> GrpcUtil.shutdownChannel(stub.getChannel()));
        stubs.clear();
    }
}
