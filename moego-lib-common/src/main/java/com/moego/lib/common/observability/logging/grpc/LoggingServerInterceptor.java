package com.moego.lib.common.observability.logging.grpc;

import io.grpc.ForwardingServerCallListener;
import io.grpc.Metadata;
import io.grpc.ServerCall;
import io.grpc.ServerCallHandler;
import io.grpc.ServerInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class LoggingServerInterceptor implements ServerInterceptor {

    private static final Logger log = LoggerFactory.getLogger(LoggingServerInterceptor.class);

    @Override
    public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(
            ServerCall<ReqT, RespT> call, Metadata headers, ServerCallHandler<ReqT, RespT> next) {
        return new LoggingServerCallListener<>(call, headers, next);
    }

    @SuppressWarnings({"unchecked", "rawtypes"})
    static class LoggingServerCallListener<ReqT>
            extends ForwardingServerCallListener.SimpleForwardingServerCallListener<ReqT> {

        private final long startTime;
        private final ServerCall call;
        private final Metadata headers;

        public LoggingServerCallListener(ServerCall call, Metadata headers, ServerCallHandler next) {
            super(next.startCall(call, headers));
            this.call = call;
            this.headers = headers;
            this.startTime = System.currentTimeMillis();
        }

        @Override
        public void onCancel() {
            log.info("");
            super.onCancel();
        }

        @Override
        public void onComplete() {
            log.info("");
            super.onComplete();
        }

        @Override
        public void onReady() {
            super.onReady();
        }
    }
}
