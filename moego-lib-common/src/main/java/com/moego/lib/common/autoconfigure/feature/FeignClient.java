package com.moego.lib.common.autoconfigure.feature;

import com.moego.lib.common.http.feign.MoeDecoder;
import feign.codec.Decoder;
import org.springframework.beans.factory.ObjectFactory;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.cloud.openfeign.FeignClientFactoryBean;
import org.springframework.cloud.openfeign.support.HttpMessageConverterCustomizer;
import org.springframework.cloud.openfeign.support.SpringDecoder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass({Decoder.class, FeignClientFactoryBean.class})
public class FeignClient {

    @Bean
    public Decoder moeFeignDecoder(
            ObjectFactory<HttpMessageConverters> converters,
            ObjectProvider<HttpMessageConverterCustomizer> customizers) {
        return new MoeDecoder(new SpringDecoder(converters, customizers));
    }
}
