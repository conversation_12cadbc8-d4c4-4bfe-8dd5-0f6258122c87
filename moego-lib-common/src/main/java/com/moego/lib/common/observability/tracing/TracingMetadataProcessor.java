/*
 * @since 2022-06-25 10:25:55
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.lib.common.observability.tracing;

import java.util.Map;

public class TracingMetadataProcessor implements MetadataProcessor<TracingContext> {

    @Override
    public String[] keys(RequestHolder holder) {
        return new String[] {
            TracingContext.HK_REQUEST_ID,
            TracingContext.HK_B3_TRACE_ID,
            TracingContext.HK_B3_SPAN_ID,
            TracingContext.HK_B3_PARENT_SPAN_ID,
            TracingContext.HK_B3_SAMPLED,
            TracingContext.HK_B3_FLAGS
        };
    }

    @Override
    public TracingContext build(Map<String, String> entries) {
        return new TracingContext(entries);
    }
}
