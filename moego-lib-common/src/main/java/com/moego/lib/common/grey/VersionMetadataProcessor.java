package com.moego.lib.common.grey;

import com.moego.lib.common.observability.tracing.MetadataProcessor;
import com.moego.lib.common.observability.tracing.RequestHolder;
import com.moego.lib.common.util.GreyUtil;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Used to identify version information in metadata
 */
public class VersionMetadataProcessor implements MetadataProcessor<VersionContext> {

    private static final String GREY_VERSION_PREFIX = "gv-";

    @Override
    public String[] keys(RequestHolder holder) {
        return Optional.ofNullable(holder).map(RequestHolder::headers).orElse(List.of()).stream()
                .filter(Objects::nonNull)
                .filter(key ->
                        key.toLowerCase().startsWith(GREY_VERSION_PREFIX) || key.equalsIgnoreCase(GreyUtil.GERY_NAME))
                .toArray(String[]::new);
    }

    @Override
    public VersionContext build(Map<String, String> entries) {
        return new VersionContext(Map.copyOf(entries));
    }
}
