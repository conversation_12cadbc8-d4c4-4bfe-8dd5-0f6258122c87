package com.moego.lib.common.scheduling;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(SchedulingProperties.PREFIX)
public class SchedulingProperties {

    public static final String PREFIX = "moego.scheduling";

    private boolean enabled = true;

    /**
     * Whether to generate request id for each task invocation.
     */
    private boolean genRequestId = true;
}
