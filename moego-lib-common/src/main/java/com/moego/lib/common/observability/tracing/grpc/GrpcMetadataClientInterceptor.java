/*
 * @since 2022-06-25 11:53:38
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.lib.common.observability.tracing.grpc;

import com.moego.lib.common.observability.tracing.Headers;
import com.moego.lib.common.observability.tracing.TracingContext;
import com.moego.lib.common.thread.ThreadContextHolder;
import io.grpc.CallOptions;
import io.grpc.Channel;
import io.grpc.ClientCall;
import io.grpc.ClientInterceptor;
import io.grpc.ForwardingClientCall;
import io.grpc.Metadata;
import io.grpc.MethodDescriptor;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;

public class GrpcMetadataClientInterceptor implements ClientInterceptor, EnvironmentAware {

    private String applicationName;

    @Override
    public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(
            MethodDescriptor<ReqT, RespT> method, CallOptions callOptions, Channel next) {
        return new ForwardingClientCall.SimpleForwardingClientCall<ReqT, RespT>(next.newCall(method, callOptions)) {
            @Override
            public void start(Listener<RespT> responseListener, Metadata headers) {
                Headers transferHeaders = ThreadContextHolder.getContext(Headers.class);
                if (transferHeaders != null) {
                    transferHeaders.getHeaders().forEach((k, v) -> {
                        Metadata.Key<String> mk = Metadata.Key.of(k, Metadata.ASCII_STRING_MARSHALLER);
                        if (!headers.containsKey(mk)) {
                            headers.put(mk, v);
                        }
                    });
                }
                addFromAppHeader(headers);
                super.start(responseListener, headers);
            }
        };
    }

    private void addFromAppHeader(Metadata headers) {
        Metadata.Key<String> fromAppKey = Metadata.Key.of(TracingContext.HK_FROM_APP, Metadata.ASCII_STRING_MARSHALLER);
        if (!headers.containsKey(fromAppKey) && applicationName != null) {
            headers.put(fromAppKey, applicationName);
        }
    }

    @Override
    public void setEnvironment(Environment environment) {
        this.applicationName = environment.getProperty("spring.application.name");
    }
}
