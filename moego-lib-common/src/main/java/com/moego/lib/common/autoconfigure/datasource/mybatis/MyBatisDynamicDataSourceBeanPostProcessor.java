package com.moego.lib.common.autoconfigure.datasource.mybatis;

import com.moego.lib.common.autoconfigure.datasource.DynamicDataSource;
import jakarta.annotation.Nonnull;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.lang.Nullable;

/**
 * Proxy {@link DynamicDataSource#useDataSource(String)} to create a new instance of mapper with specified data source.
 *
 * <AUTHOR>
 * @see DynamicDataSource
 * @since 2024/12/13
 */
public class MyBatisDynamicDataSourceBeanPostProcessor implements BeanPostProcessor, ApplicationContextAware {

    private ApplicationContext ctx;

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.ctx = applicationContext;
    }

    @Nullable
    @Override
    public Object postProcessAfterInitialization(@Nonnull Object bean, @Nonnull String beanName) throws BeansException {
        if (bean instanceof DynamicDataSource<?> mapper) {
            return MyBatisDynamicDataSourceMethodInterceptor.createProxy(mapper, ctx);
        }
        return bean;
    }
}
