package com.moego.lib.common.grpc.server;

import io.grpc.Context;
import io.grpc.Metadata;
import io.grpc.ServerCall;
import org.springframework.lang.Nullable;

/**
 * <AUTHOR>
 * @since 2023/9/13
 */
public record GrpcRequestContext(ServerCall<?, ?> call, Metadata headers) {
    static final Context.Key<GrpcRequestContext> INSTANCE = Context.key("GrpcRequestContext");

    /**
     * Get request context bound to current gRPC request.
     *
     * @return current gRPC request context
     */
    @Nullable
    public static GrpcRequestContext get() {
        return INSTANCE.get();
    }
}
