package com.moego.lib.common.thread.http;

import com.moego.lib.common.thread.ThreadPoolMetricExporter;
import java.util.concurrent.Executor;
import org.apache.tomcat.util.threads.ThreadPoolExecutor;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.web.embedded.tomcat.TomcatWebServer;
import org.springframework.boot.web.server.WebServer;
import org.springframework.boot.web.servlet.context.ServletWebServerInitializedEvent;
import org.springframework.context.ApplicationListener;

/**
 * Register tomcat thread pool to {@link ThreadPoolMetricExporter}.
 *
 * <AUTHOR>
 * @since 2022/10/10
 */
public class TomcatThreadPoolRegister
        implements ApplicationRunner, ApplicationListener<ServletWebServerInitializedEvent> {

    private final ThreadPoolMetricExporter exporter;
    private ThreadPoolExecutor tomcatThreadPool;

    public TomcatThreadPoolRegister(ThreadPoolMetricExporter exporter) {
        this.exporter = exporter;
    }

    @Override
    public void onApplicationEvent(ServletWebServerInitializedEvent event) {
        // http thread pool
        WebServer webServer = event.getWebServer();
        if (webServer instanceof TomcatWebServer server) {
            Executor executor =
                    server.getTomcat().getConnector().getProtocolHandler().getExecutor();
            if (executor instanceof ThreadPoolExecutor pool) {
                this.tomcatThreadPool = pool;
            }
        }
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        if (tomcatThreadPool != null) {
            exporter.register("tomcat", new TomcatObservableThreadPool(tomcatThreadPool));
        }
    }
}
