package com.moego.lib.common.observability.metrics.prometheus.grpc;

import io.grpc.Status;
import lombok.experimental.UtilityClass;

/**
 * Grpc Converter.
 *
 * <AUTHOR>
 */
@UtilityClass
public class GrpcConverter {

    /**
     * Convert a gRPC status to http code.
     *
     * @param code grpc code
     * @return http status code
     */
    public static int toHttpCode(Status.Code code) {
        switch (code) {
            case OK:
                return 200;
            case CANCELLED:
                return 499;
            case UNKNOWN:
                return 500;
            case INVALID_ARGUMENT:
                return 400;
            case DEADLINE_EXCEEDED:
                return 504;
            case NOT_FOUND:
                return 404;
            case ALREADY_EXISTS:
                return 409;
            case PERMISSION_DENIED:
                return 403;
            case RESOURCE_EXHAUSTED:
                return 429;
            case FAILED_PRECONDITION:
                return 400;
            case ABORTED:
                return 409;
            case OUT_OF_RANGE:
                return 400;
            case UNIMPLEMENTED:
                return 501;
            case INTERNAL:
                return 500;
            case UNAVAILABLE:
                return 503;
            case DATA_LOSS:
                return 500;
            case UNAUTHENTICATED:
                return 401;
            default:
                return 400;
        }
    }
}
