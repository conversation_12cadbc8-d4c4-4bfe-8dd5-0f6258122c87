package com.moego.lib.common.autoconfigure.feature;

import com.moego.lib.common.autoconfigure.condition.ConditionalOnGrpcServerEnabled;
import com.moego.lib.common.autoconfigure.condition.ConditionalOnHttpServerEnabled;
import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.grpc.health.DataSourceHealthDetector;
import com.moego.lib.common.grpc.health.HealthChecker;
import com.moego.lib.common.grpc.health.HealthDetector;
import com.moego.lib.common.grpc.health.RedisHealthDetector;
import com.moego.lib.common.http.health.InitController;
import java.util.stream.Collectors;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.jdbc.core.JdbcTemplate;

/**
 * <AUTHOR>
 * @since 2022/11/7
 */
@Configuration(proxyBeanMethods = false)
public class Health {

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnHttpServerEnabled
    static class Http {

        @Bean
        public InitController initController(Environment env) {
            return new InitController(env);
        }
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnGrpcServerEnabled
    static class Grpc {

        @Bean
        public HealthChecker grpcHealthChecker(ObjectProvider<HealthDetector> detectors) {
            return new HealthChecker(detectors.orderedStream().collect(Collectors.toList()));
        }

        @Configuration(proxyBeanMethods = false)
        @ConditionalOnClass({JdbcTemplate.class})
        @ConditionalOnProperty(
                value = GrpcProperties.PREFIX + ".server.health.data-source.enabled",
                matchIfMissing = true)
        static class DataSource {

            @Bean
            public DataSourceHealthDetector dataSourceHealthDetector(GrpcProperties grpcProperties) {
                return new DataSourceHealthDetector(grpcProperties);
            }
        }

        @Configuration(proxyBeanMethods = false)
        @ConditionalOnClass({RedisTemplate.class, RedisConnectionFactory.class})
        @ConditionalOnProperty(value = GrpcProperties.PREFIX + ".server.health.redis.enabled", matchIfMissing = true)
        static class Redis {

            @Bean
            public RedisHealthDetector redisHealthDetector() {
                return new RedisHealthDetector();
            }
        }
    }
}
