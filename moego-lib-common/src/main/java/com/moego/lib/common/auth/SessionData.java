package com.moego.lib.common.auth;

import java.util.HashMap;
import java.util.Map;
import lombok.Data;

@Data
public class SessionData {
    EnterpriseHubSessionData enterpriseSessionData;
    PlatformSessionData platformSessionData;

    public Map<String, String> buildMap() {
        Map<String, String> m = new HashMap<>();
        if (platformSessionData != null) {
            m.put(SessionDataKey.SESSION_DATA_COMPANY_ID, String.valueOf(platformSessionData.getCompanyId()));
            m.put(SessionDataKey.SESSION_DATA_BUSINESS_ID, String.valueOf(platformSessionData.getBusinessId()));
            m.put(SessionDataKey.SESSION_DATA_STAFF_ID, String.valueOf(platformSessionData.getStaffId()));
            m.put(SessionDataKey.SESSION_DATA_CUSTOMER_ID, String.valueOf(platformSessionData.getCustomerId()));
        }
        if (enterpriseSessionData != null) {
            m.put(
                    SessionDataKey.SESSION_DATA_ENTERPRISE_ENTERPRISE_ID,
                    String.valueOf(enterpriseSessionData.getEnterpriseId()));
            m.put(SessionDataKey.SESSION_DATA_ENTERPRISE_STAFF_ID, String.valueOf(enterpriseSessionData.getStaffId()));
        }
        return m;
    }
}
