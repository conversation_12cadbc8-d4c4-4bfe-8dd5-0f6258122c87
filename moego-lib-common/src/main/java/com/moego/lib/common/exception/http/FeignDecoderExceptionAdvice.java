package com.moego.lib.common.exception.http;

import com.moego.lib.common.exception.BizException;
import com.moego.lib.common.http.util.Const;
import feign.codec.DecodeException;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.core.annotation.Order;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

/**
 * <AUTHOR>
 * @since 2022/10/26
 */
@Order(1) // before old ExceptionHandlerAdvice
@RestControllerAdvice
public class FeignDecoderExceptionAdvice {

    private CommonResponse handleBizException(BizException bizException, HttpServletResponse resp) {
        resp.setHeader(Const.X_MOE_STATUS, bizException.getCode().toString());
        return HttpExceptionUtil.toCommonResponse(bizException);
    }

    @ExceptionHandler(DecodeException.class)
    public CommonResponse handleDecodeException(DecodeException ex, HttpServletResponse resp) {
        Throwable cause = ex.getCause();
        if (cause instanceof BizException) {
            return handleBizException((BizException) cause, resp);
        } else {
            resp.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            return handleBizException(
                    new BizException(
                            HttpStatus.INTERNAL_SERVER_ERROR.value(),
                            ex.getClass().getName(),
                            ex),
                    resp);
        }
    }
}
