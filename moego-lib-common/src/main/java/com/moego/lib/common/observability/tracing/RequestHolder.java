package com.moego.lib.common.observability.tracing;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RequestHolder {
    /**
     * Get all header names.
     *
     * @return header names
     */
    List<String> headers();

    /**
     * Get the value of the specified key.
     *
     * @param key key
     * @return value associated with the key, maybe null
     */
    String getHeader(String key);

    /**
     * @return address (IP) for client
     */
    String remoteAddr();

    /**
     * @return host (domain)
     */
    String host();
}
