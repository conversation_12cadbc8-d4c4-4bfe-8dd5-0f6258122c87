package com.moego.lib.common.grey.grpc;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Supplier;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

/**
 * Cache with expiration time.
 *
 * <p> There are two ways to clean up expired items:
 * <ul>
 *     <li>Asynchronous timing cleanup</li>
 *     <li>Clean up when an expired key is queried</li>
 * </ul>
 *
 * <p>
 * NOTE: {@link Cache} will auto register a shutdown hook to shut down the internal thread pool and clear the cached items, so you don't need to do it yourself.
 *
 * @param <T> type of cache
 * <AUTHOR>
 */
public class Cache<T> {

    private static final Logger log = LoggerFactory.getLogger(Cache.class);

    private static final AtomicInteger counter = new AtomicInteger(0);

    private static final long DEFAULT_TTL = 60_000;
    private static final long DEFAULT_INTERNAL_TO_CLEAN = 60_000;

    private final ConcurrentHashMap<String, Item<T>> items = new ConcurrentHashMap<>();
    /**
     * Used to asynchronously clear expired cache items.
     */
    private final ExecutorService executorService;
    /**
     * Used to periodically clear expired cache items.
     */
    private final ScheduledExecutorService scheduledService;
    /**
     * default cache item timeout
     */
    private final long ttl;
    /**
     * interval for clearing cached items
     */
    private final long internalToClean;
    /**
     * Index of this cache.
     */
    private final int idx;

    public Cache() {
        this(DEFAULT_TTL, DEFAULT_INTERNAL_TO_CLEAN);
    }

    public Cache(long ttl) {
        this(ttl, DEFAULT_INTERNAL_TO_CLEAN);
    }

    public Cache(long ttl, long internalToClean) {
        this.ttl = ttl;
        this.internalToClean = internalToClean;

        this.idx = counter.getAndIncrement();
        this.executorService = getExecutorService();
        this.scheduledService = getScheduledService();

        // periodically delete expired cache items
        startCleaner();

        // register shutdown hook
        // 1. clear items
        // 2. shutdown scheduledService
        // 3. shutdown executorService
        registerShutdownHook();
    }

    private ThreadPoolExecutor getExecutorService() {
        CustomizableThreadFactory tf = new CustomizableThreadFactory(String.format("executorService-%d-", this.idx));
        // Prevent the main thread done, and the JVM still running.
        tf.setDaemon(true);
        return new ThreadPoolExecutor(
                1,
                Math.max(1, Runtime.getRuntime().availableProcessors()),
                60L,
                TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(),
                tf);
    }

    private ScheduledExecutorService getScheduledService() {
        CustomizableThreadFactory threadFactory =
                new CustomizableThreadFactory(String.format("scheduledService-%d-", this.idx));
        // In order to prevent the JVM not exit when the main thread exits,
        // thread need to be set as daemon.
        threadFactory.setDaemon(true);
        return new ScheduledThreadPoolExecutor(1, threadFactory);
    }

    private void registerShutdownHook() {
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            clear();
            scheduledService.shutdown();
            executorService.shutdown();
            try {
                if (!scheduledService.awaitTermination(10, TimeUnit.SECONDS)) {
                    scheduledService.shutdownNow();
                }
                if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                scheduledService.shutdownNow();
                Thread.currentThread().interrupt();
            }
            log.info("Cache {} ({}) shutdown", this.idx, this.getClass().getName());
        }));
    }

    private void startCleaner() {
        this.scheduledService.scheduleWithFixedDelay(
                () -> {
                    Map<String, Item<T>> copy = new HashMap<>(items);
                    for (Map.Entry<String, Item<T>> entry : copy.entrySet()) {
                        Item<T> item = entry.getValue();
                        if (item.isExpired()) {
                            remove(entry.getKey());
                        }
                    }
                },
                0,
                internalToClean,
                TimeUnit.MILLISECONDS);
    }

    /**
     * Do something after remove cache item.
     *
     * <p> The default implementation will close the cache item if it is {@link AutoCloseable}.
     * <p> Subclass can override this method to do something else.
     *
     * <p> NOTE: Usually some close operations are performed, such as closing the connection, which are generally time-consuming, so they are performed asynchronously here, and if the subclasses implement this method, the time consumption of afterRemove needs to be considered.
     *
     * @param v The value to be removed
     */
    protected void afterRemove(T v) {
        if (v instanceof AutoCloseable) {
            asyncDo(() -> {
                try {
                    ((AutoCloseable) v).close();
                } catch (Exception e) {
                    log.warn(String.format("Failed to close cache item: %s", v), e);
                }
            });
        }
    }

    /**
     * Asynchronously process the specified task.
     *
     * <p> NOTE: only used inside the {@link Cache} or subclass, not outside.
     *
     * @param runnable runnable
     */
    protected final void asyncDo(Runnable runnable) {
        executorService.execute(runnable);
    }

    /**
     * Put cache item with default timeout.
     *
     * @param key   key
     * @param value value
     */
    public void put(String key, T value) {
        put(key, value, ttl);
    }

    /**
     * Put cache item with specified timeout.
     *
     * @param key   key
     * @param value value
     * @param ttl   ttl
     */
    public void put(String key, T value, long ttl) {
        items.put(key, new Item<>(value, ttl));
    }

    /**
     * Get cache item, will refresh last used time (not thread-safe).
     *
     * @param key key
     * @return cache item
     */
    public T get(String key) {
        Item<T> item = items.get(key);
        if (item == null || item.isExpired()) {
            return null;
        }
        item.refresh();
        return item.getValue();
    }

    /**
     * Get cache item, will refresh last used time (thread-safe).
     *
     * @param key key
     * @return cache item
     */
    public synchronized T syncGet(String key) {
        return get(key);
    }

    /**
     * This method is thread-safe.
     *
     * @param key   key
     * @param value value if not exist key
     * @return not expired value or new value
     */
    public T getOrSupply(String key, Supplier<T> value) {
        return getOrSupply(key, value, ttl);
    }

    /**
     * This method is thread-safe.
     *
     * @param key   key
     * @param value value if not exist key
     * @param ttl   ttl
     * @return not expired value or new value
     */
    public T getOrSupply(String key, Supplier<T> value, long ttl) {
        return items.compute(key, (k, item) -> {
                    if (item == null) {
                        return new Item<>(value.get(), ttl);
                    }
                    if (item.isExpired()) {
                        afterRemove(item.getValue()); // remove expired item
                        return new Item<>(value.get(), ttl);
                    }
                    item.refresh();
                    return item;
                })
                .getValue();
    }

    /**
     * Remove cache item by given key.
     *
     * @param key key
     */
    public void remove(String key) {
        Item<T> item = items.remove(key);
        if (item != null) {
            afterRemove(item.getValue());
        }
    }

    /**
     * Clear all cache items.
     *
     * <p> Invoke this method on application shutdown if cached items are limited resources.
     */
    public synchronized void clear() {
        Set<String> keys = new HashSet<>(items.keySet());
        for (String key : keys) {
            remove(key);
        }
    }

    private static class Item<T> {

        T value;
        long ttl;
        long lastUsed;

        public Item(T value, long ttl) {
            this.value = value;
            this.ttl = ttl;
            this.lastUsed = System.currentTimeMillis();
        }

        public boolean isExpired() {
            return System.currentTimeMillis() - lastUsed > ttl;
        }

        public T getValue() {
            return value;
        }

        public long getTtl() {
            return ttl;
        }

        public long getLastUsed() {
            return lastUsed;
        }

        public void setLastUsed(long lastUsed) {
            this.lastUsed = lastUsed;
        }

        public void refresh() {
            setLastUsed(System.currentTimeMillis());
        }
    }
}
