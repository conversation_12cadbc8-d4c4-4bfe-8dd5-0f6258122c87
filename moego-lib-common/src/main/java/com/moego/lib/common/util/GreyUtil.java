package com.moego.lib.common.util;

import jakarta.annotation.Nullable;
import org.springframework.boot.info.GitProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.util.StringUtils;
import org.springframework.util.function.SingletonSupplier;

/**
 * <AUTHOR>
 * @since 2024/7/29
 */
public final class GreyUtil {

    public static final String DEFAULT_BRANCH = "production";
    public static final String GERY_NAME = "grey-name";
    public static final String APP_VERSION = "APP_VERSION";

    private static final SingletonSupplier<String> CURRENT_BRANCH = SingletonSupplier.of(GreyUtil::getBranch);

    private GreyUtil() {
        throw new UnsupportedOperationException("This is a utility class and cannot be instantiated");
    }

    /**
     * Get the current branch.
     *
     * @return the current branch
     */
    public static String getCurrentBranch() {
        return CURRENT_BRANCH.get();
    }

    private static String getBranch() {
        var b = getBranchFromEnv();
        if (StringUtils.hasText(b)) {
            return b;
        }

        // 本地测试没有环境变量，从 GitProperties 中获取
        b = getBranchFromGit(SpringUtil.getContext());
        return StringUtils.hasText(b) ? b : DEFAULT_BRANCH;
    }

    @Nullable
    static String getBranchFromGit(ApplicationContext applicationContext) {
        var gitProperties =
                applicationContext.getBeanProvider(GitProperties.class).getIfUnique();
        return gitProperties != null ? gitProperties.getBranch() : null;
    }

    @Nullable
    private static String getBranchFromEnv() {
        return System.getenv(APP_VERSION);
    }
}
