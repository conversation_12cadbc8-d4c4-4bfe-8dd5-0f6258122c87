package com.moego.lib.common.grpc.server;

import com.moego.lib.common.autoconfigure.grpc.GrpcProperties;
import com.moego.lib.common.grpc.ServerStarter;
import io.grpc.Server;
import io.grpc.ServerBuilder;
import io.grpc.ServerInterceptor;
import io.grpc.inprocess.InProcessServerBuilder;
import io.grpc.internal.GrpcUtil;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.context.SmartLifecycle;

/**
 * Embedded gRPC server.
 *
 * <AUTHOR>
 * @since 2022/8/3
 */
public class GrpcServer implements SmartLifecycle, ApplicationEventPublisherAware {

    private static final Logger log = LoggerFactory.getLogger(GrpcServer.class);

    private final Server server;
    private final AtomicBoolean isRunning = new AtomicBoolean(false);
    private final CountDownLatch latch = new CountDownLatch(1);
    private final ServerStarter serverStarter;
    private final GrpcProperties properties;

    private ApplicationEventPublisher publisher;

    public GrpcServer(List<ServerInterceptor> interceptors, GrpcProperties properties, ServerStarter serverStarter) {
        this.serverStarter = serverStarter;
        this.properties = properties;
        this.server = buildGrpcServer(interceptors, properties, serverStarter);
    }

    private static Server buildGrpcServer(
            List<ServerInterceptor> interceptors, GrpcProperties properties, ServerStarter serverStarter) {
        ServerBuilder<?> builder = properties.getServer().getInProcess() == null
                ? ServerBuilder.forPort(Math.max(properties.getServer().getPort(), 0))
                : InProcessServerBuilder.forName(
                                properties.getServer().getInProcess().getName())
                        .directExecutor();
        serverStarter.getServices().forEach(builder::addService);
        // grpc invoke interceptor in reverse order
        Collections.reverse(interceptors);
        interceptors.forEach(builder::intercept);
        builder.maxInboundMessageSize(
                (int) properties.getServer().getMaxInboundMessageSize().toBytes());
        builder.maxInboundMetadataSize(
                (int) properties.getServer().getMaxInboundMetadataSize().toBytes());
        return builder.build();
    }

    @Override
    public void start() {
        if (!serverStarter.isNeedStartup()) {
            if (properties.getServer().isEmptyServerEnabled()) {
                log.info(
                        "no available gRPC service found, but 'empty-server-enabled' is true, will start a empty gRPC server!");
            } else {
                log.info("no available gRPC service found, will not start a gRPC server!");
                return;
            }
        }
        if (isRunning()) {
            return;
        }
        try {
            server.start();
            isRunning.set(true);
            log.info("gRPC server started on port: {} ({})", server.getPort(), GrpcUtil.getGrpcBuildVersion());

            publisher.publishEvent(new GrpcServerStartedEvent(server));
            waitUntilShutdown();
        } catch (IOException e) {
            log.error("gRPC server start failed", e);
            isRunning.set(false);
            gracefulShutdown();
        }
    }

    private void waitUntilShutdown() {
        new Thread(
                        () -> {
                            try {
                                // wait here until terminate
                                latch.await();
                                log.info("gRPC server stopped");
                            } catch (InterruptedException e) {
                                log.warn("Grpc server await termination interrupted", e);
                                Thread.currentThread().interrupt();
                            }
                        },
                        "grpc-termination-awaiter")
                .start();
    }

    @Override
    public void stop() {
        if (isRunning.get()) {
            gracefulShutdown();
            isRunning.set(false);
            latch.countDown();
        }
    }

    @Override
    public boolean isRunning() {
        return isRunning.get();
    }

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.publisher = applicationEventPublisher;
    }

    private void gracefulShutdown() {
        long start = System.currentTimeMillis();
        server.shutdown();
        long timeout = properties.getServer().getShutdownTimeout();
        try {
            if (timeout > 0L) {
                server.awaitTermination(timeout, TimeUnit.MILLISECONDS);
            } else {
                server.awaitTermination();
            }
        } catch (InterruptedException e) {
            log.warn("gRPC server await termination interrupted", e);
            Thread.currentThread().interrupt();
        }
        if (!server.isTerminated()) {
            log.warn(
                    "gRPC server graceful shutdown timeout ({}ms), still has active requests, force shutdown now!",
                    timeout);
            server.shutdownNow();
        } else {
            log.info("gRPC server graceful shutdown in {} ms", System.currentTimeMillis() - start);
        }
    }
}
