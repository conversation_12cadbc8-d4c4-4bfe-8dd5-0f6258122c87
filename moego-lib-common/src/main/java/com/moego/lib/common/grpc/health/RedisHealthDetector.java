package com.moego.lib.common.grpc.health;

import io.grpc.health.v1.HealthCheckRequest;
import io.grpc.health.v1.HealthCheckResponse.ServingStatus;
import java.util.ArrayList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.data.redis.connection.RedisConnectionFactory;

/**
 * Detects the health of redis.
 *
 * <AUTHOR>
 */
public class RedisHealthDetector implements HealthDetector, BeanFactoryAware, SmartInitializingSingleton {

    private static final Logger log = LoggerFactory.getLogger(RedisHealthDetector.class);

    private BeanFactory beanFactory;
    private final List<RedisConnectionFactory> redisConnectionFactories = new ArrayList<>();

    @Override
    public ServingStatus check(HealthCheckRequest request) {

        for (RedisConnectionFactory redisConnectionFactory : redisConnectionFactories) {
            try {
                redisConnectionFactory.getConnection().ping();
            } catch (Exception e) {
                log.warn("Redis health check failed!", e);
                return ServingStatus.NOT_SERVING;
            }
        }

        return ServingStatus.SERVING;
    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }

    @Override
    public void afterSingletonsInstantiated() {
        // NOTE: we don't want to change the order of bean initialization, so we use this method to get the bean
        // NOT inject the bean in the constructor
        beanFactory.getBeanProvider(RedisConnectionFactory.class).forEach(redisConnectionFactories::add);
    }
}
