package com.moego.lib.common.exception.http;

import com.moego.lib.common.exception.BizException;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * In order to be compatible with the old logic.
 *
 * <p> NOTE: this class will be used for JSON deserialization and needs to always keep an empty constructor.
 *
 * <AUTHOR>
 * @since 2022/10/12
 */
@Data
public class CommonResponse {

    private static final Logger log = LoggerFactory.getLogger(CommonResponse.class);

    private Integer code;
    private String message;
    private Object data;
    private String causedBy;
    // 兼容旧逻辑：失败是标记success字段为false
    private Boolean success = false;

    /**
     * Convert {@link CommonResponse} to {@link BizException}.
     *
     * @return {@link BizException}
     */
    public BizException toBizException() {
        return new BizException(code, message, data, causedBy);
    }
}
