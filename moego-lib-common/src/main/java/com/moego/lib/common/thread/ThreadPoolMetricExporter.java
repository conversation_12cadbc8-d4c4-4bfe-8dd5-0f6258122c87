package com.moego.lib.common.thread;

import com.moego.lib.common.util.ThreadPoolUtil;
import io.prometheus.client.Gauge;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.SmartInitializingSingleton;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.context.ApplicationContext;
import org.springframework.scheduling.concurrent.CustomizableThreadFactory;

/**
 * {@link ThreadPoolMetricExporter} is used to export thread pool metrics to prometheus.
 *
 * <AUTHOR>
 * @since 2022/10/10
 */
public class ThreadPoolMetricExporter implements SmartInitializingSingleton, ApplicationRunner, DisposableBean {

    private static final Logger log = LoggerFactory.getLogger(ThreadPoolMetricExporter.class);

    private static final Gauge gauge = Gauge.build()
            .name("thread_pools")
            .help("Thread Pools.")
            .labelNames("thread_pool_name", "thread_pool_info_type")
            .register();

    /**
     * thread pool name -> thread pool
     */
    private final Map<String, ObservableThreadPool<?>> threadPools = new HashMap<>(8);

    private final ScheduledExecutorService scheduler;
    private final ApplicationContext context;

    public ThreadPoolMetricExporter(ApplicationContext context) {
        this.context = context;
        this.scheduler = initScheduler();
    }

    public void register(String poolName, ObservableThreadPool<?> pool) {
        threadPools.put(poolName, pool);
    }

    @Override
    public void afterSingletonsInstantiated() {
        context.getBeansOfType(Executor.class).forEach(this::registerIfNecessary);

        // moego thread pool
        registerIfNecessary("moego-submit", ThreadPool.getSubmitExecutor());
        registerIfNecessary("moego-execute", ThreadPool.getExecuteExecutor());
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        scheduler.scheduleWithFixedDelay(() -> threadPools.forEach(this::refresh), 0, 5, TimeUnit.SECONDS);
    }

    @Override
    public void destroy() {
        ThreadPoolUtil.shutdown(scheduler);
        log.info("Shut down thread pool metric exporter");
    }

    private static ScheduledExecutorService initScheduler() {
        CustomizableThreadFactory threadFactory = new CustomizableThreadFactory("thread-pool-metric-exporter");
        threadFactory.setDaemon(true);
        return Executors.newSingleThreadScheduledExecutor(threadFactory);
    }

    private void refresh(String threadPoolName, ObservableThreadPool<?> executor) {
        gauge.labels(threadPoolName, "current_pool_size").set(executor.getCurrentPoolSize());
        gauge.labels(threadPoolName, "active_count").set(executor.getActiveCount());
        gauge.labels(threadPoolName, "queue_size").set(executor.getQueueSize());
    }

    private void registerIfNecessary(String name, Executor executor) {
        // only export java.util.concurrent.ThreadPoolExecutor
        Executor executorToUse = unwrapIfNecessary(executor);
        if (executorToUse instanceof ThreadPoolExecutor pool) {
            register(name, new DefaultObservableThreadPool(pool));
        }
    }

    private static Executor unwrapIfNecessary(Executor executor) {
        if (executor instanceof ExecutorServiceWrapper wrapper) {
            return unwrapIfNecessary(wrapper.unwrap());
        }
        return executor;
    }
}
