package com.moego.lib.common.util;

import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;

/**
 * Standard environments.
 *
 * <AUTHOR>
 */
@Getter
@Slf4j
public enum Env {
    /**
     * 本地开发环境
     */
    LOCAL("local"),
    TEST2("test2"),
    STAGING("staging"),
    PROD("prod"),
    ;

    private final String value;

    Env(String value) {
        this.value = value;
    }

    /**
     * Obtain the corresponding standardized environment according to the given value.
     *
     * @param value environment value
     * @return standardized environment, {@link Env}
     * @throws IllegalArgumentException if the value is not a valid environment
     */
    public static Env fromValue(String value) {
        for (Env env : Env.values()) {
            if (env.getValue().equalsIgnoreCase(value)) {
                return env;
            }
        }
        throw new IllegalArgumentException("Unknown env: " + value);
    }

    /**
     * Get the corresponding standardized environment according to the current profile.
     *
     * @param environment Spring Environment
     * @return standardized environment, {@link Env}
     */
    public static Env fromEnvironment(Environment environment) {
        String[] profiles = environment.getActiveProfiles();
        if (profiles.length == 0) {
            // TEST2 profile 一些配置会用 k8s 内部域名，导致本地无法访问，所以默认使用 LOCAL
            return Env.LOCAL;
        }
        if (profiles.length > 1) {
            log.warn("More than one active profile found, using the first one: {}", profiles[0]);
        }
        return fromValue(profiles[0]);
    }
}
