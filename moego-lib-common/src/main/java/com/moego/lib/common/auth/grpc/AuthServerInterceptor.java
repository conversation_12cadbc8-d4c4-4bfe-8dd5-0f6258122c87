/*
 * @since 2022-06-25 11:53:32
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.lib.common.auth.grpc;

import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.auth.LoginTimeChecker;
import io.grpc.Metadata;
import io.grpc.ServerCall;
import io.grpc.ServerCallHandler;
import io.grpc.ServerInterceptor;
import org.springframework.util.StringUtils;

public class AuthServerInterceptor implements ServerInterceptor {

    private final GrpcMethodAuthHolder authHolder;
    private final LoginTimeChecker loginTimeChecker;

    public AuthServerInterceptor(
            GrpcMethodAuthHolder authHolder, StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub) {
        this.authHolder = authHolder;
        this.loginTimeChecker = new LoginTimeChecker(staffServiceBlockingStub);
    }

    @Override
    public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(
            ServerCall<ReqT, RespT> call, Metadata headers, ServerCallHandler<ReqT, RespT> next) {
        Auth auth = authHolder.getAnnotation(call.getMethodDescriptor().getFullMethodName());
        if (auth != null) {
            var context = AuthContext.get();
            context.checkValid(auth.value());
            // impersonate 场景不校验
            if (!StringUtils.hasText(context.impersonator())
                    && (auth.value().equals(AuthType.COMPANY) || auth.value().equals(AuthType.BUSINESS))) {
                loginTimeChecker.check(context.staffId());
            }
        }
        return next.startCall(call, headers);
    }
}
