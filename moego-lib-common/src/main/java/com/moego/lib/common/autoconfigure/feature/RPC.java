package com.moego.lib.common.autoconfigure.feature;

import com.moego.lib.common.observability.tracing.RpcMetadataProcessor;
import com.moego.lib.common.observability.tracing.RpcProperties;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(prefix = RpcProperties.PREFIX, name = "enabled", matchIfMissing = true)
@EnableConfigurationProperties(RpcProperties.class)
public class RPC {

    @Bean
    public RpcMetadataProcessor dynamicMetadataProcessor(RpcProperties rpcProperties) {
        return new RpcMetadataProcessor(rpcProperties);
    }
}
