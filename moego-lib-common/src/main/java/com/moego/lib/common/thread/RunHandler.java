package com.moego.lib.common.thread;

import java.util.Objects;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2021/2/2 3:47 PM
 */
@Slf4j
class RunHandler implements Runnable {

    private final Runnable delegate;
    private final boolean silent;
    private final ThreadContext context;

    <PERSON><PERSON><PERSON><PERSON>(Runnable delegate, boolean silent) {
        this.delegate = delegate;
        this.silent = silent;
        this.context = ThreadContextHolder.get();
    }

    @Override
    public void run() {
        boolean sameThread = ThreadContextHolder.get() == context;
        try {
            if (!sameThread) {
                ThreadContextHolder.set(context);
            }
            delegate.run();
        } catch (RuntimeException ex) {
            if (!silent) {
                throw ex;
            }
            log.error(
                    "<PERSON><PERSON><PERSON><PERSON> caught RuntimeException in thread '{}': ",
                    Thread.currentThread().getName(),
                    ex);
        } finally {
            if (!sameThread) {
                ThreadContextHolder.remove();
            }
        }
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RunHandler that = (RunHandler) o;
        // NOTE: only compare delegate !!!
        return Objects.equals(delegate, that.delegate);
    }

    @Override
    public int hashCode() {
        return Objects.hash(delegate);
    }
}
