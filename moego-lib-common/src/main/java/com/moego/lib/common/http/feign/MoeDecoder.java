package com.moego.lib.common.http.feign;

import com.moego.lib.common.exception.http.CommonResponse;
import com.moego.lib.common.http.util.Const;
import feign.FeignException;
import feign.Response;
import feign.codec.Decoder;
import java.io.IOException;
import java.lang.reflect.Type;
import java.util.Collection;
import org.springframework.cloud.openfeign.support.ResponseEntityDecoder;

/**
 * <AUTHOR>
 * <AUTHOR>
 * @since 2021/5/25 2:29 PM
 */
public class MoeDecoder extends ResponseEntityDecoder {

    public MoeDecoder(Decoder decoder) {
        super(decoder);
    }

    @Override
    public Object decode(Response response, Type type) throws IOException, FeignException {
        Collection<String> moeStatus = response.headers().get(Const.X_MOE_STATUS);
        if (moeStatus == null
                || moeStatus.isEmpty()
                || Const.MOE_STATUS_OK.equals(moeStatus.stream().findFirst().get())) {
            return super.decode(response, type);
        } else {
            CommonResponse commonResponse = (CommonResponse) super.decode(response, CommonResponse.class);
            throw commonResponse.toBizException();
        }
    }
}
