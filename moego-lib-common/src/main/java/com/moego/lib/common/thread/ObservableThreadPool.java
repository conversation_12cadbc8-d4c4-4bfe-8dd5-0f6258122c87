package com.moego.lib.common.thread;

/**
 * Abstraction for observable thread pools, for monitoring the status of thread pools.
 *
 * <AUTHOR>
 * @since 2022/10/10
 */
public interface ObservableThreadPool<T> {
    /**
     * @return the core size of the thread pool
     */
    int getCoreSize();

    /**
     * @return the max size of the thread pool
     */
    int getMaxSize();

    /**
     * @return the current pool size of the thread pool
     */
    int getCurrentPoolSize();

    /**
     * @return the active count of the thread pool
     */
    int getActiveCount();

    /**
     * @return the queue size of the thread pool
     */
    int getQueueSize();

    /**
     * @return the thread pool
     */
    T getThreadPool();
}
