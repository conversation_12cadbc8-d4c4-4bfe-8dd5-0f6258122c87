package com.moego.lib.common.thread;

import com.moego.lib.common.grpc.server.GrpcResponseModifier;
import com.moego.lib.common.observability.tracing.Headers;
import com.moego.lib.common.observability.tracing.MetadataProcessor;
import com.moego.lib.common.observability.tracing.RequestHolder;
import com.moego.lib.common.observability.tracing.grpc.GrpcMetadataServerInterceptor;
import io.grpc.Metadata;
import io.grpc.ServerCall;
import io.grpc.ServerCallHandler;
import java.util.Map;

/**
 * {@link ThreadContextHolder} binding {@link ThreadContext} for current thread.
 *
 * <AUTHOR> <<EMAIL>>
 * <AUTHOR>
 * @since 2022/6/25
 */
public class ThreadContextHolder {

    private static final ThreadLocal<ThreadContext> storage = new InheritableThreadLocal<>();

    /**
     * Get the thread bound context.
     *
     * <p> Support the following types:
     * <ul>
     *     <li>{@link Headers} (headers that pass to the upstream)</li>
     *     <li>{@link RequestHolder} (original headers)</li>
     *     <li>{@link GrpcResponseModifier} (if gRPC is used, use to modify gRPC response)</li>
     *     <li>types of {@link MetadataProcessor#build(Map)}</li>
     * </ul>
     *
     * @param clazz the class of the context value
     * @return the context value of the specified class, null if not present
     * @see RunHandler
     * @see CallHandler
     * @see GrpcMetadataServerInterceptor#interceptCall(ServerCall, Metadata, ServerCallHandler)
     */
    public static <T> T getContext(Class<T> clazz) {
        ThreadContext threadContext = storage.get();
        if (threadContext == null) {
            return null;
        }
        MetadataContext metadataContext = threadContext.getMetadataContext();
        if (metadataContext == null) {
            return null;
        }
        return metadataContext.get(clazz);
    }

    public static ThreadContext get() {
        return storage.get();
    }

    // 只允许在 local 线程中操作
    public static void set(ThreadContext context) {
        storage.set(context);
    }

    // 只允许在 local 线程中操作
    public static void remove() {
        storage.remove();
    }
}
