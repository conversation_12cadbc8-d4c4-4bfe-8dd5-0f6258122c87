package com.moego.lib.common.observability.tracing.grpc;

import com.moego.lib.common.observability.tracing.RequestHolder;
import io.grpc.Grpc;
import io.grpc.Metadata;
import io.grpc.ServerCall;
import java.net.SocketAddress;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * <AUTHOR>
 */
public class GrpcRequestHolder implements RequestHolder {

    private final Metadata metadata;

    private final ServerCall<?, ?> call;

    public GrpcRequestHolder(Metadata metadata, ServerCall<?, ?> call) {
        this.metadata = metadata;
        this.call = call;
    }

    @Override
    public List<String> headers() {
        Set<String> keys = metadata.keys();
        return List.copyOf(keys);
    }

    @Override
    public String getHeader(String key) {
        Metadata.Key<String> k = Metadata.Key.of(key, Metadata.ASCII_STRING_MARSHALLER);
        return metadata.get(k);
    }

    @Override
    public String remoteAddr() {
        SocketAddress address = call.getAttributes().get(Grpc.TRANSPORT_ATTR_REMOTE_ADDR);
        return Optional.ofNullable(address).map(Object::toString).orElse(null);
    }

    @Override
    public String host() {
        return call.getAuthority();
    }
}
