package com.moego.lib.common.grpc;

import com.moego.lib.common.auth.Auth;
import io.grpc.BindableService;
import io.grpc.ServerMethodDefinition;
import io.grpc.ServerServiceDefinition;
import jakarta.annotation.Nullable;
import java.lang.annotation.Annotation;
import java.lang.reflect.Method;
import java.lang.reflect.Modifier;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.aop.framework.AopProxyUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/9/8
 */
public abstract class AbstractAnnotationHolder<T extends Annotation> {

    public final Map<String, T> map = new HashMap<>();

    public AbstractAnnotationHolder(List<BindableService> services) {
        init(services);
        log();
    }

    protected void init(List<BindableService> services) {
        services.forEach(bindableService -> {
            ServerServiceDefinition serviceDefinition = bindableService.bindService();
            Class<?> clazz = AopProxyUtils.ultimateTargetClass(bindableService);
            for (Method method : clazz.getDeclaredMethods()) {
                ServerMethodDefinition<?, ?> methodDefinition = getMatchedMethodDefinition(method, serviceDefinition);
                if (methodDefinition != null) {
                    ensureExistAnnotationInApiLayer(serviceDefinition, method, Auth.class);
                    ensureNotExistAnnotationInServiceLayer(serviceDefinition, method, Auth.class);
                    putAnnotation(method, methodDefinition);
                }
            }
        });
    }

    public abstract void putAnnotation(Method method, ServerMethodDefinition<?, ?> methodDefinition);

    public abstract void log();

    public abstract T getAnnotation(String methodName);

    protected void ensureNotExistAnnotationInServiceLayer(
            ServerServiceDefinition serviceDefinition, Method method, Class<? extends Annotation> clazz) {
        Annotation annotation = method.getAnnotation(clazz);
        if (annotation != null
                && serviceDefinition.getServiceDescriptor().getName().startsWith("moego.service.")) {
            throw new IllegalStateException("must NOT have @" + clazz.getSimpleName() + " annotation for method '"
                    + method + "', because it's a service-layer service");
        }
    }

    protected void ensureExistAnnotationInApiLayer(
            ServerServiceDefinition serviceDefinition, Method method, Class<? extends Annotation> clazz) {
        Annotation annotation = method.getAnnotation(clazz);
        var name = serviceDefinition.getServiceDescriptor().getName();
        boolean isApi =
                name.startsWith("moego.api.") || name.startsWith("moego.admin.") || name.startsWith("moego.client.");
        if (annotation == null && isApi) {
            throw new IllegalStateException("must have @" + clazz.getSimpleName() + " annotation for method '" + method
                    + "', because it's a api-layer service");
        }
    }

    @Nullable
    protected ServerMethodDefinition<?, ?> getMatchedMethodDefinition(
            Method method, ServerServiceDefinition definition) {
        if (!Modifier.isPublic(method.getModifiers()) || Modifier.isStatic(method.getModifiers())) {
            return null;
        }
        return definition.getMethod(
                definition.getServiceDescriptor().getName() + "/" + StringUtils.capitalize(method.getName()));
    }
}
