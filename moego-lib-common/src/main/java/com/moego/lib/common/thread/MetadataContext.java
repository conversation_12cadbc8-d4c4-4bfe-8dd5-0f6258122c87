/*
 * @since 2022-06-25 13:06:14
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.lib.common.thread;

import java.util.HashMap;
import java.util.Map;

public class MetadataContext {

    private final Map<Class<?>, Object> map = new HashMap<>();

    @SuppressWarnings("unchecked")
    public <T> T get(Class<T> key) {
        return (T) map.get(key);
    }

    public void put(Class<?> key, Object value) {
        map.put(key, value);
    }
}
