package com.moego.lib.common.observability.metrics.prometheus.timer;

import lombok.Getter;

public enum TimerGroup {
    /**
     * 默认计时分组
     */
    DEFAULT("default"),

    /**
     * 定时任务计时专用分组
     */
    TASK("task"),

    /**
     * smart schedule 相关方法计时专用分组
     */
    SMART_SCHEDULE("smart-schedule");

    @Getter
    private final String value;

    TimerGroup(String value) {
        this.value = value;
    }
}
