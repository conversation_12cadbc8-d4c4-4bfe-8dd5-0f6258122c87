package com.moego.lib.common.autoconfigure.util;

import org.springframework.core.Ordered;

/**
 * <AUTHOR>
 * @since 2022/10/21
 */
public final class BeanOrder {

    public static class GrpcClient {

        public static final int VALIDATE = Ordered.HIGHEST_PRECEDENCE + 100;
        public static final int METADATA = Ordered.HIGHEST_PRECEDENCE + 200;
        public static final int METRICS = Ordered.HIGHEST_PRECEDENCE + 250;
        public static final int DISTRIBUTED_TRACING = Ordered.HIGHEST_PRECEDENCE + 300;
        public static final int PERMISSION = Ordered.HIGHEST_PRECEDENCE + 400;
        public static final int GREY = Ordered.LOWEST_PRECEDENCE - 100;
    }

    public static class GrpcServer {

        public static final int METADATA = Ordered.HIGHEST_PRECEDENCE + 50;
        public static final int METRICS = Ordered.HIGHEST_PRECEDENCE + 100;
        public static final int EXCEPTION_HANDLER = Ordered.HIGHEST_PRECEDENCE + 200;
        public static final int DISTRIBUTED_TRACING = Ordered.HIGHEST_PRECEDENCE + 300;
        public static final int AUTH = Ordered.HIGHEST_PRECEDENCE + 500;
        public static final int VALIDATE = Ordered.HIGHEST_PRECEDENCE + 600;
        public static final int PERMISSION = Ordered.HIGHEST_PRECEDENCE + 700;
    }
}
