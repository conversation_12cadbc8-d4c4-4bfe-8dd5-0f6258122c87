<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <statusListener class="ch.qos.logback.core.status.NopStatusListener" />

    <!-- use Spring default values in dev environment -->
    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>

    <appender name="JSON-CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <layout class="ch.qos.logback.contrib.json.classic.JsonLayout">
                <jsonFormatter class="com.moego.lib.common.observability.logging.LogJsonFormatter"/>
                <timestampFormat>yyyy-MM-dd' 'HH:mm:ss.SSS</timestampFormat>
                <appendLineSeparator>true</appendLineSeparator>
                <includeContextName>false</includeContextName>
            </layout>
        </encoder>
    </appender>

    <appender name="SPRING-DEFAULT-CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${CONSOLE_LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!-- t2, staging -->
    <springProfile name="test2,staging">
        <root level="INFO">
            <appender-ref ref="JSON-CONSOLE"/>
        </root>
    </springProfile>

    <!-- prod -->
    <springProfile name="prod,preview">
        <root level="INFO">
            <appender-ref ref="JSON-CONSOLE"/>
        </root>
    </springProfile>

    <!-- local -->
    <springProfile name="!(test2 | staging | prod | preview)">
        <root level="INFO">
            <appender-ref ref="SPRING-DEFAULT-CONSOLE"/>
        </root>
    </springProfile>

</configuration>
