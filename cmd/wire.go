//go:build wireinject
// +build wireinject

package cmd

import (
	eventbus "github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/moego-svc-fulfillment/config"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/consumer"
	handler "github.com/MoeGolibrary/moego-svc-fulfillment/internal/handler"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/customer"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/growthbook"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/offering"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/order"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/organization"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/query"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service"
	"github.com/MoeGolibrary/moego-svc-fulfillment/resource"
	"github.com/google/wire"
	"google.golang.org/protobuf/proto"
)

type Handlers struct {
	FulfillmentHandler          *handler.FulfillmentHandler
	GroupClassDetailHandler     *handler.GroupClassDetailHandler
	GroupClassAttendanceHandler *handler.GroupClassAttendanceHandler
	Consumer                    *consumer.Consumer
}

func eventBusConfigProvider() *eventbus.Config {
	return config.GetConfig().EventBus
}

func eventBusProducerProvider(cfg *eventbus.Config) eventbus.Producer[proto.Message] {
	producer, err := eventbus.NewProducer(cfg)
	if err != nil {
		panic(err)
	}
	return *producer
}

func gormQueryProvider() *query.Query {
	return query.Use(resource.GetDB())
}

func Init() *Handlers {
	wire.Build(
		wire.Struct(new(Handlers), "*"),
		// eventbus
		eventBusConfigProvider, eventBusProducerProvider,
		consumer.NewConsumer, consumer.NewEventDispatcher,
		consumer.NewOrderHandler, consumer.NewOfferingHandler,
		// handler
		handler.NewFulfillmentHandler, handler.NewGroupClassDetailHandler,
		handler.NewGroupClassAttendanceHandler,
		// service
		service.NewFulfillmentService, service.NewGroupClassService,
		service.NewStaffTimeSlotService,
		// fulfillment repository
		fulfillment.NewFulfillmentRepository, fulfillment.NewGroupClassDetailRepository,
		fulfillment.NewStaffTimeSlotRepository, fulfillment.NewGroupClassAttendanceRepository,
		// offering repository
		offering.NewGroupClassRepository, offering.NewServiceRepository,
		// customer repository
		customer.NewRepository, customer.NewPetRepository,
		// organization repository
		organization.NewTaxRepository,
		// order repository
		order.NewOrderRepository,
		// gorm
		gormQueryProvider,
		growthbook.New,
	)

	return &Handlers{}
}
