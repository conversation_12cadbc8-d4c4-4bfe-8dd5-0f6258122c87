spring:
  application:
    name: moego-svc-todo
  profiles:
    active: local
  cloud:
    nacos:
      config:
        # 使用变量控制只在集群内使用配置中心
        enabled: ${NACOS_ENABLED:false}
        file-extension: yaml
        server-addr: ${secret.nacos.server-addr:nacos-cs.nacos.svc.cluster.local:8848}
        username: ${secret.nacos.username}
        password: ${secret.nacos.password}
        namespace: ${secret.nacos.namespace}
        # 配置变更时是否刷新spring context，默认为true
        refresh-enabled: false
        # 是否开启远程配置
        enable-remote-sync-config: true
        # 默认为DEFAULT_GROUP，建议使用业务名，比如todo
        group: DEFAULT_GROUP
        # 共享配置
        extension-configs:
          - data-id: common.yaml
            group: DEFAULT_GROUP
            refresh: true

  config:
    import:
      - "optional:aws-secretsmanager:moego/${MOEGO_ENVIRONMENT:testing}/datasource?prefix=secret.datasource."
      - "optional:aws-secretsmanager:moego/${MOEGO_ENVIRONMENT:testing}/nacos?prefix=secret.nacos."
