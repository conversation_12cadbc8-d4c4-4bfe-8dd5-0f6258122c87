package com.moego.svc.todo.config;

import java.util.Set;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

@Component
public class ConfigChangeListener implements ApplicationListener<EnvironmentChangeEvent> {

    @Override
    public void onApplicationEvent(EnvironmentChangeEvent event) {
        // 获取所有发生变更的配置项
        Set<String> keys = event.getKeys();
        for (String key : keys) {
            System.out.println("配置项 " + key + " 已更新");
        }
        // 手动处理配置更新逻辑
        reloadConfiguration();
    }

    private void reloadConfiguration() {
        // 自定义逻辑，比如重新初始化某些组件
        System.out.println("重新加载配置...");
    }
}
