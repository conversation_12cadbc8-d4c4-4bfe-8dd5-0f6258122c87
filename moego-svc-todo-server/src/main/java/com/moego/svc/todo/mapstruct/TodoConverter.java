package com.moego.svc.todo.mapstruct;

import com.google.protobuf.Timestamp;
import com.google.protobuf.util.Timestamps;
import com.moego.idl.models.todo.v1.TodoModel;
import com.moego.svc.todo.entity.Todo;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public abstract class TodoConverter {

    public static final TodoConverter INSTANCE = Mappers.getMapper(TodoConverter.class);

    public abstract TodoModel entityToModel(Todo entity);

    protected Timestamp dateToTimestamp(LocalDateTime localDateTime) {
        return Timestamps.fromMillis(localDateTime.toInstant(ZoneOffset.UTC).toEpochMilli());
    }

    protected TodoModel.Status byteToStatus(Byte status) {
        return TodoModel.Status.forNumber(status);
    }
}
