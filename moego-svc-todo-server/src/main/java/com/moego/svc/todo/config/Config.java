package com.moego.svc.todo.config;

import jakarta.annotation.PostConstruct;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Slf4j
@RefreshScope
@Data
@Configuration(proxyBeanMethods = false)
@ConfigurationProperties(prefix = "moego.config")
public class Config {
    private String name;
    private String version;

    @PostConstruct
    public void init() {
        log.debug("Config initialized:{}", this);
    }
}
