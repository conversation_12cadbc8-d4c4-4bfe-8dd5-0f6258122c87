package com.moego.svc.todo.controller.grpc;

import static com.moego.idl.service.todo.v1.TodoServiceGrpc.TodoServiceImplBase;

import com.google.protobuf.Empty;
import com.moego.idl.models.todo.v1.TodoModel;
import com.moego.idl.models.universal.v1.EntityListModel;
import com.moego.idl.service.todo.v1.AddTodoRequest;
import com.moego.idl.service.todo.v1.EchoHzRequest;
import com.moego.idl.service.todo.v1.EchoHzResponse;
import com.moego.idl.service.todo.v1.HelloArkRequest;
import com.moego.idl.service.todo.v1.HelloArkResponse;
import com.moego.idl.service.todo.v1.HelloBrysonRequest;
import com.moego.idl.service.todo.v1.HelloBrysonResponse;
import com.moego.idl.service.todo.v1.HelloHarvieRequest;
import com.moego.idl.service.todo.v1.HelloHarvieResponse;
import com.moego.idl.service.todo.v1.HelloJettResponse;
import com.moego.idl.service.todo.v1.HelloKaiRequest;
import com.moego.idl.service.todo.v1.HelloKaiResponse;
import com.moego.idl.service.todo.v1.HelloKurokoRequest;
import com.moego.idl.service.todo.v1.HelloKurokoResponse;
import com.moego.idl.service.todo.v1.HelloPerqinRequest;
import com.moego.idl.service.todo.v1.HelloPerqinResponse;
import com.moego.idl.service.todo.v1.HelloYueyueRequest;
import com.moego.idl.service.todo.v1.HelloYueyueResponse;
import com.moego.idl.service.todo.v1.ListTodoRequest;
import com.moego.idl.service.todo.v1.UpdateTodoRequest;
import com.moego.idl.utils.v1.OwnId;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.svc.todo.entity.Todo;
import com.moego.svc.todo.mapstruct.TodoConverter;
import com.moego.svc.todo.service.TodoService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class TodoController extends TodoServiceImplBase {

    private final TodoService todoService;

    @Override
    public void addTodo(AddTodoRequest request, StreamObserver<TodoModel> responseObserver) {
        // 1. 转换成 service 需要的数据结构
        Todo todo = new Todo();
        todo.setUserId(request.getUserId());
        todo.setTitle(request.getTitle());

        // 2. 调用 service
        Todo insertedOne = todoService.insert(todo);

        // 3. 转换为 model
        TodoModel todoModel = TodoConverter.INSTANCE.entityToModel(insertedOne);

        // 4. return
        responseObserver.onNext(todoModel);
        responseObserver.onCompleted();
    }

    @Override
    public void getTodo(OwnId request, StreamObserver<TodoModel> responseObserver) {
        // 1. 调用 service
        Todo todo = todoService.findById(request.getId(), request.getOwnerId());

        // 3. 转换为 model
        TodoModel todoModel = TodoConverter.INSTANCE.entityToModel(todo);

        // 4. return
        responseObserver.onNext(todoModel);
        responseObserver.onCompleted();
    }

    @Override
    public void listTodo(ListTodoRequest request, StreamObserver<EntityListModel> responseObserver) {
        List<Todo> todos = todoService.findByOwnerId(request.getUserId());

        List<TodoModel> todoModels =
                todos.stream().map(TodoConverter.INSTANCE::entityToModel).toList();

        EntityListModel listModel =
                EntityListModel.newBuilder().addAllTodoList(todoModels).build();

        responseObserver.onNext(listModel);
        responseObserver.onCompleted();
    }

    @Override
    public void updateTodo(UpdateTodoRequest request, StreamObserver<TodoModel> responseObserver) {
        Todo todo = new Todo();
        todo.setId(request.getId());
        todo.setUserId(request.getUserId());
        todo.setTitle(request.getTitle());
        todo.setStatus((byte) request.getStatus().getNumber());

        Todo updateOne = todoService.update(todo);

        TodoModel todoModel = TodoConverter.INSTANCE.entityToModel(updateOne);

        responseObserver.onNext(todoModel);
        responseObserver.onCompleted();
    }

    @Override
    public void deleteTodo(OwnId request, StreamObserver<Empty> responseObserver) {
        todoService.deleteById(request.getId(), request.getOwnerId());

        responseObserver.onNext(Empty.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    public void helloJett(Empty request, StreamObserver<HelloJettResponse> responseObserver) {
        HelloJettResponse jettResponse =
                HelloJettResponse.newBuilder().setHelloJett("hello world").build();
        responseObserver.onNext(jettResponse);
        responseObserver.onCompleted();
    }

    @Override
    public void echoHz(EchoHzRequest request, StreamObserver<EchoHzResponse> responseObserver) {
        EchoHzResponse response = EchoHzResponse.newBuilder()
                .setId(request.getId())
                .setMsg(request.getMsg())
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void helloArk(HelloArkRequest message, StreamObserver<HelloArkResponse> replyStreamObserver) {
        HelloArkResponse resp =
                HelloArkResponse.newBuilder().setReply(message.getMessage()).build();
        replyStreamObserver.onNext(resp);
        replyStreamObserver.onCompleted();
    }

    @Override
    public void helloPerqin(HelloPerqinRequest request, StreamObserver<HelloPerqinResponse> responseObserver) {
        String reply = "Hello Perqin, " + request.getMessage();
        HelloPerqinResponse response =
                HelloPerqinResponse.newBuilder().setReply(reply).build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void helloYueyue(HelloYueyueRequest request, StreamObserver<HelloYueyueResponse> responseObserver) {
        String reply = "Hello yueyue, " + request.getMessage();
        HelloYueyueResponse response =
                HelloYueyueResponse.newBuilder().setReply(reply).build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void helloKai(HelloKaiRequest request, StreamObserver<HelloKaiResponse> responseObserver) {
        String reply = "Hello kai,  " + request.getMessage();
        HelloKaiResponse response =
                HelloKaiResponse.newBuilder().setReply(reply).build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void helloKuroko(HelloKurokoRequest request, StreamObserver<HelloKurokoResponse> responseObserver) {
        String reply = "Hello kuroko, " + request.getMessage();
        HelloKurokoResponse response =
                HelloKurokoResponse.newBuilder().setReply(reply).build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void helloBryson(HelloBrysonRequest request, StreamObserver<HelloBrysonResponse> responseObserver) {
        String reply = "Hello bryson, " + request.getMessage();
        HelloBrysonResponse response =
                HelloBrysonResponse.newBuilder().setReply(reply).build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    public void helloHarvie(HelloHarvieRequest request, StreamObserver<HelloHarvieResponse> responseObserver) {
        responseObserver.onNext(HelloHarvieResponse.newBuilder()
                .setReply("Hello Harvie, Get your Msg: 【" + request.getMessage() + "】")
                .build());
        responseObserver.onCompleted();
    }
}
