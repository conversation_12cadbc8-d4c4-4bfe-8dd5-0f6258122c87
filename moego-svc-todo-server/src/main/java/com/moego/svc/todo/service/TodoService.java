package com.moego.svc.todo.service;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;

import com.moego.idl.models.errors.v1.Code;
import com.moego.svc.todo.entity.Todo;
import com.moego.svc.todo.mapper.TodoDynamicSqlSupport;
import com.moego.svc.todo.mapper.TodoMapper;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
public class TodoService {

    private final TodoMapper todoMapper;

    public Todo findById(Long id, Long ownerId) {
        return todoMapper
                .selectOne(c -> c.where(TodoDynamicSqlSupport.id, isEqualTo(id))
                        .and(TodoDynamicSqlSupport.userId, isEqualTo(ownerId)))
                .orElse(null);
    }

    public List<Todo> findByOwnerId(Long ownerId) {
        return todoMapper.select(c -> c.where(TodoDynamicSqlSupport.userId, isEqualTo(ownerId)));
    }

    public Todo insert(Todo todo) {
        if (!StringUtils.hasText(todo.getTitle())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "params error");
        }
        todoMapper.insertSelective(todo);
        return findById(todo.getId(), todo.getUserId());
    }

    public Todo update(Todo todo) {
        todoMapper.update(c -> TodoMapper.updateSelectiveColumns(todo, c)
                .where(TodoDynamicSqlSupport.id, isEqualTo(todo.getId()))
                .and(TodoDynamicSqlSupport.userId, isEqualTo(todo.getUserId())));
        return findById(todo.getId(), todo.getUserId());
    }

    public int deleteById(Long id, Long ownerId) {
        return todoMapper.delete(c ->
                c.where(TodoDynamicSqlSupport.id, isEqualTo(id)).and(TodoDynamicSqlSupport.userId, isEqualTo(ownerId)));
    }

    public int batchDelete(Set<Long> ids, Long ownerId) {
        return todoMapper.delete(c ->
                c.where(TodoDynamicSqlSupport.id, isIn(ids)).and(TodoDynamicSqlSupport.userId, isEqualTo(ownerId)));
    }
}
