package com.moego.svc.todo.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.time.LocalDateTime;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class TodoDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: todo")
    public static final Todo todo = new Todo();

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: todo.id")
    public static final SqlColumn<Long> id = todo.id;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: todo.user_id")
    public static final SqlColumn<Long> userId = todo.userId;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: todo.title")
    public static final SqlColumn<String> title = todo.title;

    /**
     * Database Column Remarks:
     *   1: pending, 2: in proress, 3: done
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: todo.status")
    public static final SqlColumn<Byte> status = todo.status;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: todo.created_at")
    public static final SqlColumn<LocalDateTime> createdAt = todo.createdAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: todo.updated_at")
    public static final SqlColumn<LocalDateTime> updatedAt = todo.updatedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: todo.deleted_at")
    public static final SqlColumn<LocalDateTime> deletedAt = todo.deletedAt;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: todo")
    public static final class Todo extends AliasableSqlTable<Todo> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Long> userId = column("user_id", JDBCType.BIGINT);

        public final SqlColumn<String> title = column("title", JDBCType.CHAR);

        public final SqlColumn<Byte> status = column("status", JDBCType.TINYINT);

        public final SqlColumn<LocalDateTime> createdAt = column("created_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> updatedAt = column("updated_at", JDBCType.TIMESTAMP);

        public final SqlColumn<LocalDateTime> deletedAt = column("deleted_at", JDBCType.TIMESTAMP);

        public Todo() {
            super("todo", Todo::new);
        }
    }
}