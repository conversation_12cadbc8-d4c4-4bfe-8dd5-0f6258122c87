package com.moego.svc.todo;

import static java.time.ZoneOffset.UTC;

import java.util.TimeZone;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

@SpringBootApplication
public class TodoSvcApplication {

    public static void main(String[] args) {
        try {
            Class.forName("software.amazon.jdbc.Driver");
            System.out.println("✅ 类加载成功！");
        } catch (Exception e) {
            System.err.println("❌ 类加载失败：" + e.getMessage());
        }
        TimeZone.setDefault(TimeZone.getTimeZone(UTC));

        SpringApplication.run(TodoSvcApplication.class, args);
    }
}
