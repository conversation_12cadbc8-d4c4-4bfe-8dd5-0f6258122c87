package com.moego.svc.todo.controller.grpc;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.service.todo.v1.HelloKurokoRequest;
import com.moego.idl.service.todo.v1.HelloKurokoResponse;
import com.moego.idl.service.todo.v1.HelloPerqinRequest;
import com.moego.idl.service.todo.v1.HelloPerqinResponse;
import com.moego.idl.service.todo.v1.HelloYueyueRequest;
import com.moego.idl.service.todo.v1.HelloYueyueResponse;
import com.moego.idl.service.todo.v1.TodoServiceGrpc;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
class TodoControllerTest {
    @Autowired
    private TodoServiceGrpc.TodoServiceBlockingStub todoServiceBlockingStub;

    @Test
    void helloPerqinTest() {
        HelloPerqinRequest request =
                HelloPerqinRequest.newBuilder().setMessage("How are you?").build();
        HelloPerqinResponse response = todoServiceBlockingStub.helloPerqin(request);
        assertThat(response.getReply()).isEqualTo("Hello Perqin, How are you?");
    }

    @Test
    void helloYueyueTest() {
        HelloYueyueRequest request =
                HelloYueyueRequest.newBuilder().setMessage("How is it going?").build();
        HelloYueyueResponse response = todoServiceBlockingStub.helloYueyue(request);
        assertThat(response.getReply()).isEqualTo("Hello yueyue, How is it going?");
    }

    @Test
    void helloKurokoTest() {
        HelloKurokoRequest request =
                HelloKurokoRequest.newBuilder().setMessage("welcome to Moego!").build();
        HelloKurokoResponse response = todoServiceBlockingStub.helloKuroko(request);
        assertThat(response.getReply()).isEqualTo("Hello kuroko, welcome to Moego!");
    }
}
