package com.moego.api.v3.finance_tools.service;

import com.moego.idl.api.finance_tools.v1.CreateCashAdjustmentRequest;
import com.moego.idl.api.finance_tools.v1.CreateCashAdjustmentResponse;
import com.moego.idl.api.finance_tools.v1.CreateReportRequest;
import com.moego.idl.api.finance_tools.v1.CreateReportResponse;
import com.moego.idl.api.finance_tools.v1.GetLastReportResponse;
import com.moego.idl.api.finance_tools.v1.GetReportedCashTotalRequest;
import com.moego.idl.api.finance_tools.v1.GetReportedCashTotalResponse;
import com.moego.idl.api.finance_tools.v1.ListCashAdjustmentsRequest;
import com.moego.idl.api.finance_tools.v1.ListCashAdjustmentsResponse;
import com.moego.idl.api.finance_tools.v1.ListReportsRequest;
import com.moego.idl.api.finance_tools.v1.ListReportsResponse;
import com.moego.idl.api.finance_tools.v1.UpdateReportRequest;
import com.moego.idl.api.finance_tools.v1.UpdateReportResponse;
import com.moego.idl.service.finance_tools.v1.CashDrawerServiceGrpc;
import com.moego.idl.service.finance_tools.v1.GetLastReportRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class CashDrawerService {
    private final CashDrawerServiceGrpc.CashDrawerServiceBlockingStub cashDrawerServiceBlockingStub;

    public ListReportsResponse listReports(Long businessId, ListReportsRequest request) {
        var response = cashDrawerServiceBlockingStub.listReports(
                com.moego.idl.service.finance_tools.v1.ListReportsRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setPagination(request.getPagination())
                        .build());
        return ListReportsResponse.newBuilder()
                .setPagination(response.getPagination())
                .addAllReports(response.getReportsList())
                .build();
    }

    public GetLastReportResponse getLastReport(Long businessId) {
        var response = cashDrawerServiceBlockingStub.getLastReport(
                GetLastReportRequest.newBuilder().setBusinessId(businessId).build());
        var builder = GetLastReportResponse.newBuilder();
        if (response.hasLastReport()) {
            builder.setLastReport(response.getLastReport());
        }
        return builder.build();
    }

    public GetReportedCashTotalResponse getReportedCashTotal(Long businessId, GetReportedCashTotalRequest request) {
        var response = cashDrawerServiceBlockingStub.getReportedCashTotal(
                com.moego.idl.service.finance_tools.v1.GetReportedCashTotalRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setRange(request.getRange())
                        .build());
        return GetReportedCashTotalResponse.newBuilder()
                .setReportedCashTotal(response.getReportedCashTotal())
                .build();
    }

    public ListCashAdjustmentsResponse listCashAdjustments(Long businessId, ListCashAdjustmentsRequest request) {
        var builder = com.moego.idl.service.finance_tools.v1.ListCashAdjustmentsRequest.newBuilder()
                .setBusinessId(businessId)
                .setAsc(request.getAsc());
        if (request.hasPagination()) {
            builder.setPagination(request.getPagination());
        }
        if (request.hasRange()) {
            builder.setRange(request.getRange());
        }
        if (request.hasReportId()) {
            builder.setReportId(request.getReportId());
        }
        var response = cashDrawerServiceBlockingStub.listCashAdjustments(builder.build());
        return ListCashAdjustmentsResponse.newBuilder()
                .setPagination(response.getPagination())
                .addAllAdjustments(response.getAdjustmentsList())
                .build();
    }

    public CreateCashAdjustmentResponse createCashAdjustment(
            Long businessId, Long staffId, CreateCashAdjustmentRequest request) {
        var response = cashDrawerServiceBlockingStub.createCashAdjustment(
                com.moego.idl.service.finance_tools.v1.CreateCashAdjustmentRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setStaffId(staffId)
                        .setAdjustment(request.getAdjustment())
                        .build());
        return CreateCashAdjustmentResponse.newBuilder()
                .setAdjustment(response.getAdjustment())
                .build();
    }

    public CreateReportResponse createReport(
            Long companyId, Long businessId, Long staffId, CreateReportRequest request) {
        var response = cashDrawerServiceBlockingStub.createReport(
                com.moego.idl.service.finance_tools.v1.CreateReportRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .setStaffId(staffId)
                        .setReport(request.getReport())
                        .build());
        return CreateReportResponse.newBuilder().setReport(response.getReport()).build();
    }

    public UpdateReportResponse updateReport(Long businessId, UpdateReportRequest request) {
        cashDrawerServiceBlockingStub.updateReport(
                com.moego.idl.service.finance_tools.v1.UpdateReportRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setReport(request.getReport())
                        .build());
        return UpdateReportResponse.newBuilder().build();
    }
}
