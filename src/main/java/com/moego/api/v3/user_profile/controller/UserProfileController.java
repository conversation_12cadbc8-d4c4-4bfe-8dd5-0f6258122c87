package com.moego.api.v3.user_profile.controller;

import com.moego.api.v3.user_profile.service.UserProfileService;
import com.moego.idl.api.user_profile.v1.GetUserProfilesParams;
import com.moego.idl.api.user_profile.v1.GetUserProfilesResult;
import com.moego.idl.api.user_profile.v1.UserProfileServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/6/18
 */
@GrpcService
@RequiredArgsConstructor
public class UserProfileController extends UserProfileServiceGrpc.UserProfileServiceImplBase {
    private final UserProfileService userProfileService;

    @Override
    @Auth(AuthType.BUSINESS)
    public void getUserProfile(GetUserProfilesParams request, StreamObserver<GetUserProfilesResult> responseObserver) {
        var resp = userProfileService.getUserProfiles(request);
        responseObserver.onNext(resp);
        responseObserver.onCompleted();
    }
}
