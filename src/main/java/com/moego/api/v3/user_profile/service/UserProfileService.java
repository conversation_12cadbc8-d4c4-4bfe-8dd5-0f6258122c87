package com.moego.api.v3.user_profile.service;

import com.moego.idl.api.user_profile.v1.GetUserProfilesParams;
import com.moego.idl.api.user_profile.v1.GetUserProfilesResult;
import com.moego.idl.service.user_profile.v1.GetUserProfilesRequest;
import com.moego.idl.service.user_profile.v1.UserProfileServiceGrpc;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/6/18
 */
@Service
@AllArgsConstructor
public class UserProfileService {
    private final UserProfileServiceGrpc.UserProfileServiceBlockingStub userProfileServiceBlockingStub;

    public GetUserProfilesResult getUserProfiles(GetUserProfilesParams params) {
        var resp = userProfileServiceBlockingStub.getUserProfiles(GetUserProfilesRequest.newBuilder()
                .addAllUsers(params.getUsersList())
                .build());
        return GetUserProfilesResult.newBuilder()
                .addAllUserProfiles(resp.getUserProfilesList())
                .build();
    }
}
