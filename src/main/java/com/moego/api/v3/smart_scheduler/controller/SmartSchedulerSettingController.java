package com.moego.api.v3.smart_scheduler.controller;

import com.moego.idl.api.smart_scheduler.v1.GetSmartScheduleSettingParams;
import com.moego.idl.api.smart_scheduler.v1.GetSmartScheduleSettingResult;
import com.moego.idl.api.smart_scheduler.v1.SmartScheduleSettingServiceGrpc.SmartScheduleSettingServiceImplBase;
import com.moego.idl.api.smart_scheduler.v1.UpdateSmartScheduleSettingParams;
import com.moego.idl.api.smart_scheduler.v1.UpdateSmartScheduleSettingResult;
import com.moego.idl.service.smart_scheduler.v1.GetSmartScheduleSettingRequest;
import com.moego.idl.service.smart_scheduler.v1.GetSmartScheduleSettingResponse;
import com.moego.idl.service.smart_scheduler.v1.SmartScheduleSettingServiceGrpc;
import com.moego.idl.service.smart_scheduler.v1.UpdateSmartScheduleSettingRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class SmartSchedulerSettingController extends SmartScheduleSettingServiceImplBase {
    private final SmartScheduleSettingServiceGrpc.SmartScheduleSettingServiceBlockingStub smartScheduleSettingStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void getSmartScheduleSetting(
            GetSmartScheduleSettingParams request, StreamObserver<GetSmartScheduleSettingResult> responseObserver) {
        GetSmartScheduleSettingResponse response =
                smartScheduleSettingStub.getSmartScheduleSetting(GetSmartScheduleSettingRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .build());

        responseObserver.onNext(GetSmartScheduleSettingResult.newBuilder()
                .setSmartScheduleSetting(response.getSmartScheduleSetting())
                .addAllBusinessOverrideList(response.getBusinessOverrideListList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateSmartScheduleSetting(
            UpdateSmartScheduleSettingParams request,
            StreamObserver<UpdateSmartScheduleSettingResult> responseObserver) {
        UpdateSmartScheduleSettingRequest.Builder builder = UpdateSmartScheduleSettingRequest.newBuilder()
                .setTokenCompanyId(AuthContext.get().companyId())
                .setSmartScheduleSetting(request.getSmartScheduleSetting());

        smartScheduleSettingStub.updateSmartScheduleSetting(builder.build());

        responseObserver.onNext(UpdateSmartScheduleSettingResult.newBuilder().build());
        responseObserver.onCompleted();
    }
}
