package com.moego.api.v3.smart_scheduler.controller;

import com.moego.api.v3.appointment.utils.FutureService;
import com.moego.api.v3.appointment.utils.PetDetailUtil;
import com.moego.idl.api.smart_scheduler.v1.ListAllTimeSlotsParams;
import com.moego.idl.api.smart_scheduler.v1.ListAllTimeSlotsResult;
import com.moego.idl.api.smart_scheduler.v1.ListTimeSlotsParams;
import com.moego.idl.api.smart_scheduler.v1.ListTimeSlotsResult;
import com.moego.idl.api.smart_scheduler.v1.TimeSlotServiceGrpc;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.SelectedAddOnDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.smart_scheduler.v1.BookedInfo;
import com.moego.idl.models.smart_scheduler.v1.PetParam;
import com.moego.idl.models.smart_scheduler.v1.TimeSlot;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.dto.StaffTimeslotPetCountDTO;
import com.moego.server.grooming.client.ITimeSlotsClient;
import com.moego.server.grooming.dto.OBAvailableTimeSlotDetailDTO;
import com.moego.server.grooming.dto.OBAvailableTimeStaffDetailDTO;
import com.moego.server.grooming.dto.OBAvailableTimeWithNonAvailableDTO;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import com.moego.server.grooming.dto.ob.OBPetDataDTO;
import com.moego.server.grooming.dto.ob.OBTimeSlotDTO;
import com.moego.server.grooming.dto.ob.OBTimeSlotQueryDTO;
import io.grpc.stub.StreamObserver;
import jakarta.annotation.Nonnull;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.BinaryOperator;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@GrpcService
@RequiredArgsConstructor
public class TimeSlotController extends TimeSlotServiceGrpc.TimeSlotServiceImplBase {

    private final ITimeSlotsClient timeSlotsClient;
    private final PetDetailUtil petDetailUtil;
    private final FutureService futureService;

    @Override
    @Auth(AuthType.COMPANY)
    public void listAvailableTimeSlots(
            final ListTimeSlotsParams request, final StreamObserver<ListTimeSlotsResult> responseObserver) {

        var staffIds = request.getPetParamsList().stream()
                .filter(PetParam::hasStaffId)
                .map(PetParam::getStaffId)
                .map(Long::intValue)
                .collect(Collectors.toSet());

        var availableTimeBySlot = timeSlotsClient.getAvailableTimeBySlot(new OBTimeSlotQueryDTO()
                .setTimeSlotDTO(getObTimeSlotDTO(request))
                .setNeedQueryDays(request.getDatesList().stream().toList())
                .setAvailableStaffIds(staffIds));

        if (CollectionUtils.isEmpty(availableTimeBySlot)) {
            responseObserver.onNext(ListTimeSlotsResult.newBuilder().build());
            responseObserver.onCompleted();
            return;
        }

        var companyId = AuthContext.get().companyId();

        // get pet info
        var petIds = availableTimeBySlot.values().stream()
                .map(OBAvailableTimeWithNonAvailableDTO::getSlotDetailList)
                .flatMap(List::stream)
                .map(StaffTimeslotPetCountDTO::getPetDetails)
                .flatMap(List::stream)
                .map(SmartScheduleGroomingDetailsDTO::getPetId)
                .distinct()
                .map(Long::valueOf)
                .toList();
        var petMapFuture = CompletableFuture.supplyAsync(
                () -> petDetailUtil.getPetMap(companyId, petIds), ThreadPool.getSubmitExecutor());

        // get customer info
        var customerIds = availableTimeBySlot.values().stream()
                .map(OBAvailableTimeWithNonAvailableDTO::getSlotDetailList)
                .flatMap(List::stream)
                .map(StaffTimeslotPetCountDTO::getPetDetails)
                .flatMap(List::stream)
                .map(SmartScheduleGroomingDetailsDTO::getCustomerId)
                .distinct()
                .map(Long::valueOf)
                .toList();
        var customerMapFuture = futureService.getBusinessCustomerInfoMap(companyId, customerIds);

        CompletableFuture.allOf(petMapFuture, customerMapFuture).join();

        var results = getTimeSlots(availableTimeBySlot, petMapFuture.join(), customerMapFuture.join());

        responseObserver.onNext(
                ListTimeSlotsResult.newBuilder().addAllTimeSlots(results).build());
        responseObserver.onCompleted();
    }

    @Nonnull
    private static List<TimeSlot> getTimeSlots(
            final Map<String, OBAvailableTimeWithNonAvailableDTO> availableTimeBySlot,
            final Map<Long, BusinessCustomerPetInfoModel> petMap,
            final Map<Long, BusinessCustomerInfoModel> customerMap) {

        return availableTimeBySlot.entrySet().stream()
                .flatMap(entry -> processDateEntry(entry, petMap, customerMap))
                .collect(Collectors.toMap(
                        TimeSlot::getStartTime,
                        Function.identity(),
                        BinaryOperator.maxBy(Comparator.comparingInt(TimeSlot::getTimeSlotTypeValue))))
                .values()
                .stream()
                .sorted(Comparator.comparing(TimeSlot::getDate)
                        .thenComparing(TimeSlot::getStartTime)
                        .thenComparing(TimeSlot::getStaffId))
                .toList();
    }

    @Nonnull
    private static List<TimeSlot> getTimeSlotsReverse(
            final Map<String, OBAvailableTimeWithNonAvailableDTO> availableTimeBySlot,
            final Map<Long, BusinessCustomerPetInfoModel> petMap,
            final Map<Long, BusinessCustomerInfoModel> customerMap) {

        return availableTimeBySlot.entrySet().stream()
                .flatMap(entry -> processDateEntry(entry, petMap, customerMap))
                .sorted(Comparator.comparing(TimeSlot::getDate)
                        .thenComparing(TimeSlot::getStartTime, Comparator.reverseOrder())
                        .thenComparing(TimeSlot::getStaffId))
                .toList();
    }

    @Nonnull
    private static Stream<TimeSlot> processDateEntry(
            final Map.Entry<String, OBAvailableTimeWithNonAvailableDTO> entry,
            final Map<Long, BusinessCustomerPetInfoModel> petMap,
            final Map<Long, BusinessCustomerInfoModel> customerMap) {

        var date = entry.getKey();
        var availableTimeData = entry.getValue();
        var petDetailsLookup = createPetDetailsLookup(availableTimeData.getSlotDetailList());

        return availableTimeData.getStaffList().stream()
                .flatMap(staffDetail -> processStaffDetail(date, staffDetail, petDetailsLookup, petMap, customerMap));
    }

    @Nonnull
    private static Map<String, List<SmartScheduleGroomingDetailsDTO>> createPetDetailsLookup(
            final List<StaffTimeslotPetCountDTO> slotDetailList) {

        return slotDetailList.stream()
                .collect(Collectors.toMap(
                        dto -> createSlotKey(dto.getStaffId(), dto.getSlotDate(), dto.getSlotTime()),
                        StaffTimeslotPetCountDTO::getPetDetails,
                        (existing, replacement) -> existing));
    }

    @Nonnull
    private static String createSlotKey(final Integer staffId, final String date, final Integer time) {
        return staffId + "_" + date + "_" + time;
    }

    @Nonnull
    private static Stream<TimeSlot> processStaffDetail(
            final String date,
            final OBAvailableTimeStaffDetailDTO staffDetail,
            final Map<String, List<SmartScheduleGroomingDetailsDTO>> petDetailsLookup,
            final Map<Long, BusinessCustomerPetInfoModel> petMap,
            final Map<Long, BusinessCustomerInfoModel> customerMap) {

        var staffId = staffDetail.getStaffId();

        AtomicInteger endTime = new AtomicInteger(1439);
        return staffDetail.getSlotDetails().stream()
                .sorted(Comparator.comparing(OBAvailableTimeSlotDetailDTO::getTime)
                        .reversed())
                .map(slotDetail -> createTimeSlots(
                        date,
                        staffId,
                        slotDetail,
                        petDetailsLookup,
                        petMap,
                        customerMap,
                        endTime.getAndSet(slotDetail.getTime())))
                .flatMap(List::stream);
    }

    @Nonnull
    private static TimeSlot createTimeSlot(
            final String date,
            final Integer staffId,
            final OBAvailableTimeSlotDetailDTO slotDetail,
            final Map<String, List<SmartScheduleGroomingDetailsDTO>> petDetailsLookup,
            final Map<Long, BusinessCustomerPetInfoModel> petMap,
            final Map<Long, BusinessCustomerInfoModel> customerMap) {

        var builder = TimeSlot.newBuilder()
                .setDate(date)
                .setStartTime(slotDetail.getTime())
                .setStaffId(staffId)
                .setTimeSlotType(slotDetail.getTimeSlotType())
                .setBookedPetCount(slotDetail.getOccupiedCount())
                .setPetCapacity(slotDetail.getCapacity());

        var slotKey = createSlotKey(staffId, date, slotDetail.getTime());
        var petDetails = petDetailsLookup.getOrDefault(slotKey, List.of());
        builder.addAllBookedInfos(getBookedInfos(petDetails, petMap, customerMap));

        return builder.build();
    }

    @Nonnull
    private static List<TimeSlot> createTimeSlots(
            final String date,
            final Integer staffId,
            final OBAvailableTimeSlotDetailDTO slotDetail,
            final Map<String, List<SmartScheduleGroomingDetailsDTO>> petDetailsLookup,
            final Map<Long, BusinessCustomerPetInfoModel> petMap,
            final Map<Long, BusinessCustomerInfoModel> customerMap,
            final int endTime) {

        var timeSlotTypes = slotDetail.getTimeSlotTypes();
        if (CollectionUtils.isEmpty(timeSlotTypes)) {
            return List.of();
        }
        return timeSlotTypes.stream()
                .map(timeSlotType -> {
                    var builder = TimeSlot.newBuilder()
                            .setDate(date)
                            .setStartTime(slotDetail.getTime())
                            .setEndTime(endTime)
                            .setStaffId(staffId)
                            .setTimeSlotType(timeSlotType)
                            .setBookedPetCount(slotDetail.getOccupiedCount())
                            .setPetCapacity(slotDetail.getCapacity());

                    var slotKey = createSlotKey(staffId, date, slotDetail.getTime());
                    var petDetails = petDetailsLookup.getOrDefault(slotKey, List.of());
                    builder.addAllBookedInfos(getBookedInfos(petDetails, petMap, customerMap));

                    return builder.build();
                })
                .toList();
    }

    @Nonnull
    private static List<BookedInfo> getBookedInfos(
            final List<SmartScheduleGroomingDetailsDTO> petDetails,
            final Map<Long, BusinessCustomerPetInfoModel> petMap,
            final Map<Long, BusinessCustomerInfoModel> customerMap) {
        return petDetails.stream()
                .map(petDetail -> createBookedInfo(petDetail, petMap, customerMap))
                .distinct()
                .toList();
    }

    @Nonnull
    private static BookedInfo createBookedInfo(
            final SmartScheduleGroomingDetailsDTO petDetail,
            final Map<Long, BusinessCustomerPetInfoModel> petMap,
            final Map<Long, BusinessCustomerInfoModel> customerMap) {

        var petId = petDetail.getPetId().longValue();
        var customerId = petDetail.getCustomerId().longValue();

        var petName = petMap.getOrDefault(petId, BusinessCustomerPetInfoModel.getDefaultInstance())
                .getPetName();
        var customerLastName = customerMap
                .getOrDefault(customerId, BusinessCustomerInfoModel.getDefaultInstance())
                .getLastName();

        return BookedInfo.newBuilder()
                .setPetId(petDetail.getPetId())
                .setPetName(petName)
                .setCustomerId(petDetail.getCustomerId())
                .setCustomerLastName(customerLastName)
                .build();
    }

    @Nonnull
    private static OBTimeSlotDTO getObTimeSlotDTO(final ListTimeSlotsParams request) {
        var obTimeSlotDTO = new OBTimeSlotDTO();
        obTimeSlotDTO.setBusinessId(Math.toIntExact(request.getBusinessId()));
        var petParams = request.getPetParamsList().stream()
                .map(param -> {
                    var obPetDataDTO = new OBPetDataDTO();
                    obPetDataDTO.setPetIndex(Math.toIntExact(param.getPetIndex()));
                    obPetDataDTO.setPetTypeId(param.getPetType().getNumber());
                    obPetDataDTO.setBreed(param.getBreed());
                    if (param.hasWeight()) {
                        obPetDataDTO.setWeight(param.getWeight());
                    }
                    if (param.hasCoat()) {
                        obPetDataDTO.setCoat(param.getCoat());
                    }
                    if (param.hasStaffId()) {
                        obPetDataDTO.setStaffId(Math.toIntExact(param.getStaffId()));
                    }
                    if (!CollectionUtils.isEmpty(param.getServiceIdsList())) {
                        obPetDataDTO.setServiceIds(param.getServiceIdsList().stream()
                                .map(Long::intValue)
                                .toList());
                    }
                    return obPetDataDTO;
                })
                .toList();
        obTimeSlotDTO.setPetParamList(petParams);

        var serviceIds = request.getPetParamsList().stream()
                .map(PetParam::getServiceIdsList)
                .flatMap(List::stream)
                .distinct()
                .map(Long::intValue)
                .toList();
        obTimeSlotDTO.setServiceIds(serviceIds);

        if (request.hasFilterAppointmentId()) {
            obTimeSlotDTO.setFilterAppointmentId(Math.toIntExact(request.getFilterAppointmentId()));
        }

        return obTimeSlotDTO;
    }

    // 过滤出 service 类型的 petDetail，并按 serviceId -> serviceDetail 的形式返回
    public static Map<Long, SelectedServiceDef> getPetServiceMap(List<SelectedServiceDef> appointmentPetDetails) {
        return appointmentPetDetails.stream()
                .collect(Collectors.toMap(SelectedServiceDef::getServiceId, Function.identity(), (p1, p2) -> p1));
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listAllTimeSlots(
            final ListAllTimeSlotsParams request, final StreamObserver<ListAllTimeSlotsResult> responseObserver) {
        var businessId = request.getBusinessId();
        var companyId = AuthContext.get().companyId();

        var petDetailsList = request.getPetDetailsList();
        var petIds =
                petDetailsList.stream().map(PetDetailDef::getPetId).distinct().toList();

        var currentPetMap = petDetailUtil.getPetMap(companyId, petIds);

        var obTimeSlotDTO = new OBTimeSlotDTO();
        obTimeSlotDTO.setBusinessId(Math.toIntExact(businessId));
        List<Integer> serviceIds = new ArrayList<>();
        List<Integer> staffIds = new ArrayList<>();
        List<String> dates = new ArrayList<>();
        List<ServiceInfo> petDetails = new ArrayList<>();
        var petParams = petDetailsList.stream()
                .map(param -> {
                    var petId = param.getPetId();

                    var petServiceMap = getPetServiceMap(param.getServicesList());

                    var petInfoModel =
                            currentPetMap.getOrDefault(petId, BusinessCustomerPetInfoModel.getDefaultInstance());

                    Map<Long, List<Long>> staffToServiceMap = new HashMap<>(8);
                    Integer serviceStartTime = 1439;
                    for (SelectedServiceDef selectedServiceDef : param.getServicesList()) {
                        if (selectedServiceDef.hasStaffId()) {
                            if (selectedServiceDef.hasEnableOperation() && selectedServiceDef.getEnableOperation()) {
                                selectedServiceDef.getOperationsList().forEach(operation -> {
                                    staffToServiceMap
                                            .computeIfAbsent(operation.getStaffId(), k -> new ArrayList<>())
                                            .add(selectedServiceDef.getServiceId());
                                    staffIds.add(Math.toIntExact(operation.getStaffId()));
                                    petDetails.add(new ServiceInfo(
                                            petId,
                                            operation.getStaffId(),
                                            selectedServiceDef.getStartDate(),
                                            operation.getStartTime(),
                                            operation.getStartTime() + operation.getDuration()));
                                });
                            } else {
                                staffToServiceMap
                                        .computeIfAbsent(selectedServiceDef.getStaffId(), k -> new ArrayList<>())
                                        .add(selectedServiceDef.getServiceId());
                                staffIds.add(Math.toIntExact(selectedServiceDef.getStaffId()));
                                petDetails.add(new ServiceInfo(
                                        petId,
                                        selectedServiceDef.getStaffId(),
                                        selectedServiceDef.getStartDate(),
                                        selectedServiceDef.getStartTime(),
                                        selectedServiceDef.getEndTime()));
                            }
                            serviceIds.add(Math.toIntExact(selectedServiceDef.getServiceId()));
                            dates.add(selectedServiceDef.getStartDate());
                            serviceStartTime = Math.min(serviceStartTime, selectedServiceDef.getStartTime());
                        }
                    }
                    for (SelectedAddOnDef service : param.getAddOnsList()) {
                        if (service.hasStaffId()) {

                            var startDate = service.hasAddonDateType()
                                    ? switch (service.getAddonDateType()) {
                                        case PET_DETAIL_DATE_LAST_DAY -> petServiceMap
                                                .get(service.getAssociatedServiceId())
                                                .getEndDate();
                                        case PET_DETAIL_DATE_FIRST_DAY -> petServiceMap
                                                .get(service.getAssociatedServiceId())
                                                .getStartDate();
                                        default -> service.getStartDate();
                                    }
                                    : service.getStartDate();

                            if (!StringUtils.hasText(startDate)) {
                                continue;
                            }

                            if (service.hasEnableOperation() && service.getEnableOperation()) {
                                service.getOperationsList().forEach(operation -> {
                                    staffToServiceMap
                                            .computeIfAbsent(operation.getStaffId(), k -> new ArrayList<>())
                                            .add(service.getAddOnId());
                                    staffIds.add(Math.toIntExact(operation.getStaffId()));
                                    petDetails.add(new ServiceInfo(
                                            petId,
                                            operation.getStaffId(),
                                            startDate,
                                            operation.getStartTime(),
                                            operation.getStartTime() + operation.getDuration()));
                                });
                            } else {
                                staffToServiceMap
                                        .computeIfAbsent(service.getStaffId(), k -> new ArrayList<>())
                                        .add(service.getAddOnId());
                                staffIds.add(Math.toIntExact(service.getStaffId()));
                                petDetails.add(new ServiceInfo(
                                        petId,
                                        service.getStaffId(),
                                        startDate,
                                        service.getStartTime(),
                                        service.getStartTime() + service.getServiceTime()));
                            }
                            serviceIds.add(Math.toIntExact(service.getAddOnId()));
                            dates.add(startDate);
                            serviceStartTime = Math.min(serviceStartTime, service.getStartTime());
                        }
                    }

                    // 反转映射关系，创建 serviceToStaffMap
                    Map<Integer, Set<Integer>> serviceToStaffMap = new HashMap<>();
                    staffToServiceMap.forEach(
                            (staffId, serviceIdList) -> serviceIdList.forEach(serviceId -> serviceToStaffMap
                                    .computeIfAbsent(serviceId.intValue(), k -> new HashSet<>())
                                    .add(staffId.intValue())));

                    var obPetDataDTO = new OBPetDataDTO();
                    obPetDataDTO.setPetIndex(Math.toIntExact(petId));
                    obPetDataDTO.setPetTypeId(petInfoModel.getPetType().getNumber());
                    obPetDataDTO.setBreed(petInfoModel.getBreed());
                    obPetDataDTO.setWeight(petInfoModel.getWeight());
                    obPetDataDTO.setCoat(petInfoModel.getCoatType());
                    obPetDataDTO.setHairLength(petInfoModel.getCoatType());

                    obPetDataDTO.setServiceIds(
                            serviceToStaffMap.keySet().stream().toList());
                    obPetDataDTO.setServiceStaffMap(serviceToStaffMap);
                    obPetDataDTO.setServiceStartTime(serviceStartTime);

                    // obPetDataDTO.setStaffId(Math.toIntExact(staffId));
                    return obPetDataDTO;
                })
                .toList();
        obTimeSlotDTO.setPetParamList(petParams);

        obTimeSlotDTO.setServiceIds(serviceIds);

        if (CollectionUtils.isEmpty(serviceIds)) {
            responseObserver.onNext(ListAllTimeSlotsResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        var availableTimeBySlot = timeSlotsClient.getAvailableTimeBySlot(new OBTimeSlotQueryDTO()
                .setTimeSlotDTO(obTimeSlotDTO)
                .setNeedQueryDays(
                        dates.stream().filter(Objects::nonNull).distinct().toList())
                .setAvailableStaffIds(new HashSet<>(staffIds)));

        if (CollectionUtils.isEmpty(availableTimeBySlot)) {
            responseObserver.onNext(ListAllTimeSlotsResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // get pet info
        var newPetIds = availableTimeBySlot.values().stream()
                .map(OBAvailableTimeWithNonAvailableDTO::getSlotDetailList)
                .flatMap(List::stream)
                .map(StaffTimeslotPetCountDTO::getPetDetails)
                .flatMap(List::stream)
                .map(SmartScheduleGroomingDetailsDTO::getPetId)
                .distinct()
                .map(Long::valueOf)
                .toList();
        var petMapFuture = CompletableFuture.supplyAsync(
                () -> petDetailUtil.getPetMap(companyId, newPetIds), ThreadPool.getSubmitExecutor());

        // get customer info
        var customerIds = availableTimeBySlot.values().stream()
                .map(OBAvailableTimeWithNonAvailableDTO::getSlotDetailList)
                .flatMap(List::stream)
                .map(StaffTimeslotPetCountDTO::getPetDetails)
                .flatMap(List::stream)
                .map(SmartScheduleGroomingDetailsDTO::getCustomerId)
                .distinct()
                .map(Long::valueOf)
                .toList();
        var customerMapFuture = futureService.getBusinessCustomerInfoMap(companyId, customerIds);

        CompletableFuture.allOf(petMapFuture, customerMapFuture).join();

        var results = getTimeSlotsReverse(availableTimeBySlot, petMapFuture.join(), customerMapFuture.join());

        // 先聚合 service
        var staffTimeslotPetCountByPetDetails = getStaffTimeslotPetCountByPetDetails(petDetails);

        // 再偏移重置 start time
        if (CollectionUtils.isEmpty(staffTimeslotPetCountByPetDetails)) {
            responseObserver.onNext(
                    ListAllTimeSlotsResult.newBuilder().addAllTimeSlots(results).build());
            responseObserver.onCompleted();
            return;
        }

        var newResult = staffTimeslotPetCountByPetDetails.stream()
                .map(serviceSlot -> {
                    return results.stream()
                            .filter(result -> Objects.equals(result.getStaffId(), serviceSlot.staffId())
                                    && Objects.equals(result.getDate(), serviceSlot.startDate())
                                    && serviceSlot.startTime() >= result.getStartTime()
                                    && serviceSlot.startTime() < result.getEndTime())
                            .map(result -> {
                                var builder = result.toBuilder();
                                builder.addAllPreviewBookedInfos(serviceSlot.petIds().stream()
                                        .map(petId -> {
                                            return BookedInfo.newBuilder()
                                                    .setPetId(petId)
                                                    .setPetName(currentPetMap
                                                            .getOrDefault(
                                                                    petId,
                                                                    BusinessCustomerPetInfoModel.getDefaultInstance())
                                                            .getPetName())
                                                    .build();
                                        })
                                        .toList());
                                return builder.build();
                            })
                            .toList();
                })
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .toList();
        responseObserver.onNext(
                ListAllTimeSlotsResult.newBuilder().addAllTimeSlots(newResult).build());
        responseObserver.onCompleted();
    }

    private record ServiceInfo(Long petId, Long staffId, String startDate, Integer startTime, Integer endTime) {}

    private record GroupKey(String startDate, Long staffId, Long petId) {}

    private record ServiceKey(String startDate, Integer startTime, Long staffId) {}

    private record ServiceSlot(String startDate, Integer startTime, Long staffId, List<Long> petIds) {}

    /**
     * 根据 petDetails 计算每个 timeslot 的宠物数量和预约数量
     * 同一个pet下：
     * - service 可以连接上，(service2.start_time = service1.end_time)，这个pet只占用 service1 的 start_time 的slot
     * - service 不连接，中间有间隔，这个pet占用各个 service 的 start time 的那个slot
     *
     * @param petDetails 宠物预约详情列表
     * @return 每个 timeslot 的统计结果
     */
    public static List<ServiceSlot> getStaffTimeslotPetCountByPetDetails(List<ServiceInfo> petDetails) {
        // 过滤无效数据
        var filteredDetails = petDetails.stream()
                .filter(e -> e.petId() != 0)
                .filter(e -> e.startTime() != null && e.endTime() != null)
                .toList();

        // 按照 date、startTime、staffId 分组
        Map<ServiceKey, List<ServiceInfo>> slotStaffDetails = new HashMap<>();

        // 根据连续性处理每个 pet_detail
        filteredDetails.stream()
                .collect(Collectors.groupingBy(dto -> new GroupKey(dto.startDate(), dto.staffId(), dto.petId())))
                .forEach((petDetailKey, details) -> {
                    String date = petDetailKey.startDate();
                    Long staffId = petDetailKey.staffId();

                    // 对详情按开始时间排序
                    details.sort(Comparator.comparing(ServiceInfo::startTime));

                    // 检查连续性并记录 timeslot
                    for (int i = 0; i < details.size(); ) {
                        ServiceInfo currentPetDetail = details.get(i);
                        var slotTime = currentPetDetail.startTime();
                        var slotKey = new ServiceKey(date, slotTime, staffId);
                        List<ServiceInfo> slotDetails =
                                slotStaffDetails.computeIfAbsent(slotKey, k -> new ArrayList<>());
                        slotDetails.add(currentPetDetail);

                        // 检查是否有连续的服务
                        int j = i + 1;
                        while (j < details.size()
                                && details.get(j - 1)
                                        .endTime()
                                        .equals(details.get(j).startTime())) {
                            slotDetails.add(details.get(j));
                            j++;
                        }

                        // 跳过已处理的连续 pet_detail
                        i = j;
                    }
                });

        // 构建结果
        var results = new ArrayList<ServiceSlot>();

        for (var entry : slotStaffDetails.entrySet()) {
            var slotGroup = entry.getKey();
            List<ServiceInfo> slotDetails = entry.getValue();

            results.add(new ServiceSlot(
                    slotGroup.startDate(),
                    slotGroup.startTime(),
                    slotGroup.staffId(),
                    slotDetails.stream().map(ServiceInfo::petId).distinct().toList()));
        }

        return results;
    }
}
