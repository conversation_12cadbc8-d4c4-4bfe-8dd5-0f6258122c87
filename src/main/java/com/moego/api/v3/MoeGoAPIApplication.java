package com.moego.api.v3;

import com.moego.api.v3.account.config.SessionConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.ConfigurationPropertiesScan;
import org.springframework.cloud.openfeign.EnableFeignClients;

@Slf4j
@ConfigurationPropertiesScan
@EnableFeignClients({
    "com.moego.server.business.client",
    "com.moego.server.customer.client",
    "com.moego.server.grooming.client",
    "com.moego.server.payment.client",
    "com.moego.server.message.client",
    "com.moego.server.retail.client",
    "com.moego.api.thirdparty",
})
@SpringBootApplication
public class MoeGoAPIApplication {

    public static void main(String[] args) {
        var app = SpringApplication.run(MoeGoAPIApplication.class, args);

        var sessionConfig = app.getBean(SessionConfig.class);
        log.info("session config: {}", sessionConfig);
    }
}
