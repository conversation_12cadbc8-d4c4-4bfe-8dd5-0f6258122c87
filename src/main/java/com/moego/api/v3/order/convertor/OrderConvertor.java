package com.moego.api.v3.order.convertor;

import com.moego.idl.api.order.v1.CreateTipOrderParams;
import com.moego.idl.api.order.v1.ModifyItemTaxRequest;
import com.moego.idl.api.order.v2.ListOrdersForCombinedPaymentParams;
import com.moego.idl.service.order.v1.CreateTipOrderRequest;
import com.moego.idl.service.order.v1.ModifyItemTaxInput;
import com.moego.idl.service.order.v2.ListOrdersForCombinedPaymentRequest;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface OrderConvertor {

    OrderConvertor INSTANCE = Mappers.getMapper(OrderConvertor.class);

    ModifyItemTaxInput toModifyItemTaxInput(ModifyItemTaxRequest request);

    CreateTipOrderRequest toCreateTipOrderRequest(CreateTipOrderParams params);

    ListOrdersForCombinedPaymentRequest toRequest(ListOrdersForCombinedPaymentParams params);
}
