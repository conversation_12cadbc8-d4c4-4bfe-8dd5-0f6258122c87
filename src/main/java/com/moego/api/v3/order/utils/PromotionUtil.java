package com.moego.api.v3.order.utils;

import com.moego.idl.models.order.v1.ItemType;
import com.moego.idl.models.promotion.v1.CouponApplicationTarget;
import com.moego.idl.models.promotion.v1.CouponUsage;
import com.moego.idl.models.promotion.v1.Source;
import com.moego.idl.models.promotion.v1.TargetType;
import com.moego.idl.service.order.v2.PreviewCreateOrderRequest;
import com.moego.idl.service.promotion.v1.PromotionServiceGrpc;
import com.moego.idl.service.promotion.v1.RecommendCouponsRequest;
import com.moego.idl.service.promotion.v1.RecommendCouponsResponse;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class PromotionUtil {
    private final PromotionServiceGrpc.PromotionServiceBlockingStub promotionServiceBlockingStub;

    public static void sortAppliedPromotions(List<PreviewCreateOrderRequest.Promotion> appliedPromotions) {
        appliedPromotions.sort((item1, item2) -> {
            int p1 = PromotionUtil.TYPE_PRIORITY.getOrDefault(
                    item1.getCouponSource().getType(), Integer.MIN_VALUE);
            int p2 = PromotionUtil.TYPE_PRIORITY.getOrDefault(
                    item2.getCouponSource().getType(), Integer.MIN_VALUE);
            if (p1 != p2) {
                return Integer.compare(p1, p2);
            }
            return Long.compare(
                    item1.getCouponSource().getId(), item2.getCouponSource().getId());
        });
    }

    public static final Map<Source.Type, Integer> TYPE_PRIORITY = Map.of(
            Source.Type.PACKAGE, 0,
            Source.Type.DISCOUNT, 1,
            Source.Type.MEMBERSHIP_DISCOUNT, 2,
            Source.Type.MEMBERSHIP_QUANTITY, 2);

    public List<PreviewCreateOrderRequest.Promotion> recommendPromotions(
            Long customerId, List<PreviewCreateOrderRequest.CartItem> itemList) {
        // 构造一个假的 order item id
        Map<Long, PreviewCreateOrderRequest.CartItem> cartItemMap = new HashMap<>(itemList.size());
        List<CouponApplicationTarget> targetList = new ArrayList<>(itemList.size());
        for (int i = 0; i < itemList.size(); i++) {

            long orderItemId = i + 1;
            PreviewCreateOrderRequest.CartItem cartItem = itemList.get(i);
            cartItemMap.put(orderItemId, cartItem);

            TargetType targetType;
            if (cartItem.getItemType() == ItemType.ITEM_TYPE_SERVICE) {
                targetType = TargetType.SERVICE;
            } else if (cartItem.getItemType() == ItemType.ITEM_TYPE_PRODUCT) {
                targetType = TargetType.PRODUCT;
            } else if (cartItem.getItemType() == ItemType.ITEM_TYPE_SERVICE_CHARGE) {
                targetType = TargetType.SERVICE_CHARGE;
            } else {
                continue;
            }

            targetList.add(CouponApplicationTarget.newBuilder()
                    .setTargetType(targetType)
                    .setTargetId(cartItem.getItemId())
                    .setUnitPrice(cartItem.getUnitPrice())
                    .setTargetQuantity(cartItem.getQuantity())
                    .setOrderItemId(orderItemId)
                    .build());
        }

        // 请求获取 recommended coupons
        RecommendCouponsResponse recommendResp =
                promotionServiceBlockingStub.recommendCoupons(RecommendCouponsRequest.newBuilder()
                        .addAllTargets(targetList)
                        .setCustomerId(customerId)
                        .build());

        // 构造 applied promotion
        List<PreviewCreateOrderRequest.Promotion> promotionList =
                new ArrayList<>(recommendResp.getRecommendedCouponsList().size());
        for (CouponUsage couponUsage : recommendResp.getRecommendedCouponsList()) {
            List<String> externalUuidList =
                    new ArrayList<>(couponUsage.getTargetsList().size());
            for (CouponApplicationTarget target : couponUsage.getTargetsList()) {
                PreviewCreateOrderRequest.CartItem cartItem = cartItemMap.get(target.getOrderItemId());
                if (null != cartItem) {
                    externalUuidList.add(cartItem.getExternalUuid());
                }
            }

            promotionList.add(PreviewCreateOrderRequest.Promotion.newBuilder()
                    .setCouponSource(couponUsage.getCoupon().getSource())
                    .addAllCartItemExternalUuids(externalUuidList)
                    .build());
        }
        return promotionList;
    }
}
