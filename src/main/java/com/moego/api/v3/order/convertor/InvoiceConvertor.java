package com.moego.api.v3.order.convertor;

import com.moego.idl.models.order.v1.OrderDetailModelV1;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderModelV1;
import com.moego.idl.models.order.v1.OrderPaymentModel;
import com.moego.idl.models.order.v1.OrderPromotionModel;
import com.moego.idl.models.order.v1.OrderStatus;
import com.moego.idl.models.order.v1.RefundOrderDetailModel;
import com.moego.idl.models.order.v1.RefundOrderPaymentModel;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.util.ObjectUtils;

public class InvoiceConvertor {
    public static String buildInvoiceEmail(String imageUrl) {
        String emailTemplate =
                """
                <!DOCTYPE html>
                <html>
                <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width">
                </head>
                <body style="margin:0;padding:0;background:#f7f7f7;text-align:center;">
                  <table
                    role="presentation"
                    align="center"
                    width="100%%"
                    border="0"
                    cellpadding="0"
                    cellspacing="0"
                    style="max-width:800px;"
                  >
                    <tr>
                      <td align="center" style="padding:20px;">
                        <img
                          src="%s"
                          alt="estimate"
                          width="800"
                          style="display:block;max-width:100%%;height:auto;margin:0 auto;border:0;"
                        >
                      </td>
                    </tr>
                  </table>
                </body>
                </html>""";

        return emailTemplate.formatted(imageUrl);
    }

    public static OrderDetailModelV1 buildBigInvoice(
            Long bigInvoiceId,
            List<OrderDetailModelV1> orderDetails,
            List<OrderPaymentModel> orderPayments,
            List<RefundOrderPaymentModel> refundOrderPayments) {
        if (orderDetails == null || orderDetails.isEmpty()) {
            if (bigInvoiceId != null) {
                return OrderDetailModelV1.newBuilder()
                        .setOrder(OrderModelV1.newBuilder().setId(bigInvoiceId).build())
                        .build();
            }

            return OrderDetailModelV1.getDefaultInstance();
        }

        OrderDetailModelV1.Builder detailBuilder = OrderDetailModelV1.newBuilder()
                .addAllOrderPayments(orderPayments)
                .addAllRefundOrderPayments(refundOrderPayments);

        // Order 本体，只合并关键字段
        BigDecimal tipAmount = BigDecimal.ZERO;
        BigDecimal taxAmount = BigDecimal.ZERO;
        BigDecimal discountAmount = BigDecimal.ZERO;
        BigDecimal depositAmount = BigDecimal.ZERO;
        BigDecimal convenienceFee = BigDecimal.ZERO;
        BigDecimal subTotalAmount = BigDecimal.ZERO;
        BigDecimal totalAmount = BigDecimal.ZERO;
        BigDecimal paidAmount = BigDecimal.ZERO;
        BigDecimal remainAmount = BigDecimal.ZERO;
        BigDecimal refundedAmount = BigDecimal.ZERO;
        // Order version 取最小的版本.
        Integer orderVersion = null;

        for (OrderDetailModelV1 detail : orderDetails) {
            if (ObjectUtils.isEmpty(detail)) {
                continue;
            }

            OrderModelV1 order = detail.getOrder();

            if (orderVersion == null || orderVersion.compareTo(order.getOrderVersion()) > 0) {
                orderVersion = order.getOrderVersion();
            }

            // Canceled 的 Order 的 Items / Discounts / Order Promotion 以及本体的字段都不纳入 Big Invoice 的范围.
            if (OrderStatus.REMOVED.equals(order.getStatus())) {
                continue;
            }

            detailBuilder.addAllOrderItems(detail.getOrderItemsList().stream()
                    .filter(it -> !it.getIsDeleted())
                    .toList());
            detailBuilder.addAllOrderDiscounts(detail.getOrderDiscountsList().stream()
                    .filter(it -> !it.getIsDeleted())
                    .toList());
            detailBuilder.addAllOrderPromotions(detail.getOrderPromotionsList().stream()
                    .filter(it -> it.getStatus() != OrderPromotionModel.Status.CANCELLED)
                    .toList());

            tipAmount = tipAmount.add(MoneyConvertor.toBigDecimal(order.getTipsAmount()));
            taxAmount = taxAmount.add(MoneyConvertor.toBigDecimal(order.getTaxAmount()));
            discountAmount = discountAmount.add(MoneyConvertor.toBigDecimal(order.getDiscountAmount()));
            depositAmount = depositAmount.add(MoneyConvertor.toBigDecimal(order.getDepositAmount()));
            convenienceFee = convenienceFee.add(MoneyConvertor.toBigDecimal(order.getConvenienceFee()));
            subTotalAmount = subTotalAmount.add(MoneyConvertor.toBigDecimal(order.getSubTotalAmount()));
            totalAmount = totalAmount.add(MoneyConvertor.toBigDecimal(order.getTotalAmount()));
            paidAmount = paidAmount.add(MoneyConvertor.toBigDecimal(order.getPaidAmount()));
            remainAmount = remainAmount.add(MoneyConvertor.toBigDecimal(order.getRemainAmount()));
            refundedAmount = refundedAmount.add(MoneyConvertor.toBigDecimal(order.getRefundedAmount()));
        }

        String currencyCode = "";
        for (OrderDetailModelV1 detail : orderDetails) {
            if (detail.getOrder().getCurrencyCode().isBlank()) {
                continue;
            }

            currencyCode = detail.getOrder().getCurrencyCode();
            break;
        }

        if (bigInvoiceId == null) {
            bigInvoiceId = getInvoiceId(orderDetails);
        }

        OrderModelV1 template = orderDetails.get(0).getOrder();
        OrderModelV1.Builder orderBuilder = OrderModelV1.newBuilder()
                .setId(bigInvoiceId)
                // 兜底为 Legacy order.
                .setOrderVersion(Optional.ofNullable(orderVersion).orElse(0))
                .setCompanyId(template.getCompanyId())
                .setBusinessId(template.getBusinessId())
                .setCustomerId(template.getCustomerId())
                .setSourceType(template.getSourceType())
                .setSourceId(template.getSourceId())
                .setCurrencyCode(currencyCode)
                .setTipsAmount(MoneyConvertor.toMoney(tipAmount, currencyCode))
                .setTaxAmount(MoneyConvertor.toMoney(taxAmount, currencyCode))
                .setDiscountAmount(MoneyConvertor.toMoney(discountAmount, currencyCode))
                .setDepositAmount(MoneyConvertor.toMoney(depositAmount, currencyCode))
                .setConvenienceFee(MoneyConvertor.toMoney(convenienceFee, currencyCode))
                .setSubTotalAmount(MoneyConvertor.toMoney(subTotalAmount, currencyCode))
                .setTotalAmount(MoneyConvertor.toMoney(totalAmount, currencyCode))
                .setPaidAmount(MoneyConvertor.toMoney(paidAmount, currencyCode))
                .setRemainAmount(MoneyConvertor.toMoney(remainAmount, currencyCode))
                .setRefundedAmount(MoneyConvertor.toMoney(refundedAmount, currencyCode));

        if (remainAmount.compareTo(BigDecimal.ZERO) == 0) {
            orderBuilder.setStatus(OrderStatus.COMPLETED);
            orderBuilder.setPaymentStatus(OrderModel.PaymentStatus.PAID);
        } else if (paidAmount.compareTo(BigDecimal.ZERO) > 0) {
            orderBuilder.setStatus(OrderStatus.PROCESSING);
            orderBuilder.setPaymentStatus(OrderModel.PaymentStatus.PARTIAL_PAID);
        } else {
            orderBuilder.setStatus(OrderStatus.PROCESSING);
            orderBuilder.setPaymentStatus(OrderModel.PaymentStatus.UNPAID);
        }

        return detailBuilder.setOrder(orderBuilder).build();
    }

    public static long getInvoiceId(List<OrderDetailModelV1> orderDetails) {
        if (orderDetails == null || orderDetails.isEmpty()) {
            return 0L;
        }

        for (OrderDetailModelV1 detail : orderDetails) {
            if (detail.getOrder().getOrderRefId() > 0) {
                return detail.getOrder().getOrderRefId();
            }

            if (detail.getOrder().getId() > 0) {
                return detail.getOrder().getId();
            }
        }

        return 0L;
    }

    public static List<OrderPaymentModel> getAllOrderPayments(List<OrderDetailModelV1> orderDetails) {
        return orderDetails.stream()
                .flatMap(it -> it.getOrderPaymentsList().stream())
                .toList();
    }

    public static List<RefundOrderPaymentModel> getAllRefundOrderPayments(
            List<OrderDetailModelV1> orderDetails, List<RefundOrderDetailModel> refundOrderDetails) {
        return Stream.concat(
                        Optional.ofNullable(orderDetails).orElse(List.of()).stream()
                                .flatMap(it -> it.getRefundOrderPaymentsList().stream()),
                        Optional.ofNullable(refundOrderDetails).orElse(List.of()).stream()
                                .flatMap(it -> it.getRefundOrderPaymentsList().stream()))
                .collect(
                        Collectors.toMap(RefundOrderPaymentModel::getId, it -> it, (existing, replacement) -> existing))
                .values()
                .stream()
                .toList();
    }
}
