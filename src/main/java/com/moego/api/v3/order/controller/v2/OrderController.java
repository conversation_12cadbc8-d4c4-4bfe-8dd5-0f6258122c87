package com.moego.api.v3.order.controller.v2;

import com.google.type.Money;
import com.moego.api.v3.order.convertor.MoneyConvertor;
import com.moego.api.v3.order.convertor.OrderConvertor;
import com.moego.api.v3.order.service.OrderService;
import com.moego.api.v3.order.utils.PromotionUtil;
import com.moego.api.v3.subscription.util.MoneyUtil;
import com.moego.common.enums.PaymentStripeStatus;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.idl.api.order.v2.CombinedPayOrderParams;
import com.moego.idl.api.order.v2.CombinedPayOrderResult;
import com.moego.idl.api.order.v2.CreateOrderParams;
import com.moego.idl.api.order.v2.CreateOrderResult;
import com.moego.idl.api.order.v2.GetOrderGuidParams;
import com.moego.idl.api.order.v2.GetOrderGuidResult;
import com.moego.idl.api.order.v2.ListOrdersForCombinedPaymentParams;
import com.moego.idl.api.order.v2.ListOrdersForCombinedPaymentResult;
import com.moego.idl.api.order.v2.OrderServiceGrpc;
import com.moego.idl.api.order.v2.PayOrderParams;
import com.moego.idl.api.order.v2.PayOrderResult;
import com.moego.idl.api.order.v2.PreviewCreateOrderParams;
import com.moego.idl.api.order.v2.PreviewCreateOrderResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.order.v1.OrderDetailModelV1;
import com.moego.idl.models.order.v1.OrderDetailView;
import com.moego.idl.models.order.v1.OrderItemModel;
import com.moego.idl.models.order.v1.OrderPaymentModel;
import com.moego.idl.models.payment.v2.ChannelPayment;
import com.moego.idl.service.order.v2.CombinedPayOrderRequest;
import com.moego.idl.service.order.v2.CombinedPayOrderResponse;
import com.moego.idl.service.order.v2.CreateOrderRequest;
import com.moego.idl.service.order.v2.GetOrderGuidRequest;
import com.moego.idl.service.order.v2.PayOrderRequest;
import com.moego.idl.service.order.v2.PayOrderResponse;
import com.moego.idl.service.order.v2.PreviewCreateOrderRequest;
import com.moego.idl.service.order.v2.PreviewCreateOrderResponse;
import com.moego.idl.service.payment.v2.PayPaymentRequest;
import com.moego.idl.service.payment.v2.PayPaymentResponse;
import com.moego.idl.service.payment.v2.PaymentServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.GroomingCustomerInfoDTO;
import com.moego.server.grooming.api.IGroomingAppointmentService;
import com.moego.server.grooming.dto.AppointmentDTO;
import com.moego.server.payment.api.IPaymentPaymentService;
import com.moego.server.payment.dto.PaymentFeeDTO;
import com.moego.server.payment.params.ListPaymentFeeParams;
import com.moego.server.payment.params.PaymentFeeParams;
import io.grpc.stub.StreamObserver;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@GrpcService
@AllArgsConstructor
@Slf4j
public class OrderController extends OrderServiceGrpc.OrderServiceImplBase {
    private final PaymentServiceGrpc.PaymentServiceBlockingStub paymentServiceBlockingStub;
    private final com.moego.idl.service.order.v2.OrderServiceGrpc.OrderServiceBlockingStub orderServiceBlockingStub;
    private final IPetClient petClient;
    private final OrderService orderService;
    private final ICustomerCustomerService iCustomerCustomerService;
    private final IGroomingAppointmentService iGroomingAppointmentService;
    private final IPaymentPaymentService iPaymentPaymentService;
    private final IBusinessStaffClient iBusinessStaffClient;
    private final PromotionUtil promotionUtil;

    /**
     * payOrder，前端直接调用的支付接口
     * - 调order创建单据
     * - 调payment进行真正支付
     *
     * @param request          PayOrderParams
     * @param responseObserver StreamObserver<PayOrderResult>
     */
    @Override
    @Auth(AuthType.BUSINESS)
    public void payOrder(PayOrderParams request, StreamObserver<PayOrderResult> responseObserver) {
        // 参数校验
        if (null == request) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "request is null");
        }
        Integer companyId = AuthContext.get().getCompanyId();
        if (null == companyId) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id is null");
        }
        Integer businessId = AuthContext.get().getBusinessId();
        if (null == businessId) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "business id is null");
        }
        Integer staffId = AuthContext.get().getStaffId();
        if (null == staffId) {
            staffId = 0;
        }

        // 调用order v2创建order payment
        PayOrderResponse orderResponse = orderServiceBlockingStub.payOrder(PayOrderRequest.newBuilder()
                .setOrderId(request.getOrderId())
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setStaffId(staffId)
                .setAmount(request.getAmount())
                .setPaymentTipsBeforeCreate(request.getPaymentTipsBeforeCreate())
                .build());

        // 调用payment进行真正的支付
        OrderPaymentModel orderPayment = orderResponse.getOrderPayment();
        PayPaymentResponse payResponse = paymentServiceBlockingStub.payPayment(PayPaymentRequest.newBuilder()
                .setId(orderPayment.getTransactionId())
                .setPaymentMethodType(request.getPaymentMethodType())
                .setDetail(request.getPaymentMethodDetail())
                .setAddConvenienceFee(request.getAddConvenienceFee())
                .setPayer(request.getPayer())
                .setDescription(request.getPaymentDescription())
                .build());

        orderPayment = orderPayment.toBuilder()
                .setPaymentStatusReason(payResponse.getMsg())
                .build();
        responseObserver.onNext(PayOrderResult.newBuilder()
                .setOrderPayment(orderPayment)
                .setRawPaymentResult(payResponse.getChannelResponse())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void previewCreateOrder(
            PreviewCreateOrderParams request, StreamObserver<PreviewCreateOrderResult> responseObserver) {
        PreviewCreateOrderRequest.Builder previewBuilder = PreviewCreateOrderRequest.newBuilder()
                .setSourceType(request.getSourceType())
                .setSourceId(request.getSourceId())
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .setCustomerId(request.getCustomerId())
                .addAllItems(request.getItemsList())
                .setTipsAmount(request.getTipsAmount());

        if (request.hasAppliedPromotions()) {
            previewBuilder.setAppliedPromotions(request.getAppliedPromotions());
        } else if (request.getAutoApplyPromotions()) {
            List<PreviewCreateOrderRequest.Promotion> appliedPromotions =
                    promotionUtil.recommendPromotions(request.getCustomerId(), request.getItemsList());
            previewBuilder.setAppliedPromotions(PreviewCreateOrderRequest.AppliedPromotions.newBuilder()
                    .addAllPromotions(appliedPromotions)
                    .build());
        }

        PreviewCreateOrderResponse previewCreateOrderResp =
                orderServiceBlockingStub.previewCreateOrder(previewBuilder.build());

        responseObserver.onNext(PreviewCreateOrderResult.newBuilder()
                .setOrder(convertOrderDetailToView(previewCreateOrderResp.getOrder()))
                .setAppliedPromotions(previewCreateOrderResp.getAppliedPromotions())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void createOrder(CreateOrderParams request, StreamObserver<CreateOrderResult> responseObserver) {
        OrderDetailModelV1 orderDetail = orderServiceBlockingStub
                .createOrder(CreateOrderRequest.newBuilder()
                        .setSourceType(request.getSourceType())
                        .setSourceId(request.getSourceId())
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(request.getBusinessId())
                        .setCustomerId(request.getCustomerId())
                        .setTitle(request.getTitle())
                        .setDescription(request.getDescription())
                        .setTipsAmount(request.getTipsAmount())
                        .addAllItems(request.getItemsList())
                        .setAppliedPromotions(request.getAppliedPromotions())
                        .build())
                .getOrder();
        responseObserver.onNext(CreateOrderResult.newBuilder()
                .setOrder(convertOrderDetailToView(orderDetail))
                .build());
        responseObserver.onCompleted();
    }

    // 构造 OrderDetailView.
    private OrderDetailView convertOrderDetailToView(OrderDetailModelV1 orderDetail) {
        // 构造 OrderDetailView 里面的各种 View.
        OrderDetailView.Builder orderDetailViewBuilder = OrderDetailView.newBuilder()
                // Order detail.
                .setOrderDetail(orderDetail)
                // Pet brief.
                .addAllPetBriefs(batchGetPetBriefList(orderDetail.getOrderItemsList().stream()
                        .map(OrderItemModel::getPetId)
                        .filter(it -> it > 0)
                        .distinct()
                        .map(Long::intValue)
                        .toList()))
                // Staff brief.
                .addAllStaffBriefs(batchGetStaffBriefList(
                        Long.valueOf(orderDetail.getOrder().getBusinessId()).intValue(),
                        orderDetail.getOrderItemsList().stream()
                                .map(OrderItemModel::getStaffIdsList)
                                .filter(it -> !it.isEmpty())
                                .flatMap(List::stream)
                                .map(Long::intValue)
                                .sorted()
                                .distinct()
                                .toList()));

        // 构造 subtotal by item type
        Map<String, Money> subtotalByType = buildSubtotalByItemType(orderDetail.getOrderItemsList());
        if (!subtotalByType.isEmpty()) {
            orderDetailViewBuilder.putAllSubtotalByItemType(subtotalByType);
        }

        Map<String, Money> preDiscountSubtotalByType =
                buildPreDiscountSubtotalByItemType(orderDetail.getOrderItemsList());
        if (!preDiscountSubtotalByType.isEmpty()) {
            orderDetailViewBuilder.putAllPreDiscountSubtotalByItemType(preDiscountSubtotalByType);
        }

        // 构造 subtotal by tax. 按照 Tax ID 聚合的 TaxAmount.
        List<OrderDetailView.Tax> taxList = new ArrayList<>();
        //   构造 taxId -> orderItemList
        orderDetail.getOrderItemsList().stream()
                .filter(it -> it.getTaxId() > 0)
                .collect(Collectors.groupingBy(OrderItemModel::getTaxId))
                .forEach((taxId, orderItems) -> {
                    if (orderItems == null || orderItems.isEmpty()) {
                        return;
                    }
                    BigDecimal taxAmount = BigDecimal.ZERO;
                    for (OrderItemModel item : orderItems) {
                        taxAmount = taxAmount.add(MoneyConvertor.toBigDecimal(item.getTaxAmount()));
                    }

                    OrderItemModel firstItem = orderItems.get(0);
                    taxList.add(OrderDetailView.Tax.newBuilder()
                            .setId(firstItem.getTaxId())
                            .setName(firstItem.getTaxName())
                            .setRate(firstItem.getTaxRate())
                            .setAmount(MoneyConvertor.toMoney(taxAmount, firstItem.getCurrencyCode()))
                            .build());
                });
        if (!taxList.isEmpty()) orderDetailViewBuilder.addAllSubtotalByTax(taxList);

        return orderDetailViewBuilder.build();
    }

    private Map<String, Money> buildSubtotalByItemType(List<OrderItemModel> orderItems) {
        Map<String, Money> subtotalByType = new HashMap<>();
        orderItems.stream()
                .collect(Collectors.groupingBy(OrderItemModel::getType))
                .forEach((itemType, itemList) -> {
                    if (itemList == null || itemList.isEmpty()) {
                        return;
                    }

                    OrderItemModel firstItem = itemList.get(0);
                    BigDecimal subTotal = BigDecimal.ZERO;
                    for (OrderItemModel item : itemList) {
                        subTotal = subTotal.add(MoneyConvertor.toBigDecimal(item.getTotalAmount()));
                    }
                    subtotalByType.put(itemType, MoneyConvertor.toMoney(subTotal, firstItem.getCurrencyCode()));
                });

        if (subtotalByType.isEmpty()) {
            return Map.of();
        }

        String currencyCode = orderItems.get(0).getCurrencyCode();
        // 汇总一个.
        subtotalByType.put(
                "all",
                MoneyConvertor.toMoney(
                        orderItems.stream()
                                .map(item -> MoneyUtil.moneyToBigDecimal(item.getTotalAmount()))
                                .reduce(BigDecimal.ZERO, BigDecimal::add),
                        currencyCode));

        return subtotalByType;
    }

    private Map<String, Money> buildPreDiscountSubtotalByItemType(List<OrderItemModel> orderItems) {
        Map<String, Money> subtotalByType = new HashMap<>();
        orderItems.stream()
                .collect(Collectors.groupingBy(OrderItemModel::getType))
                .forEach((itemType, itemList) -> {
                    if (itemList == null || itemList.isEmpty()) {
                        return;
                    }

                    OrderItemModel firstItem = itemList.get(0);
                    BigDecimal subTotal = BigDecimal.ZERO;
                    for (OrderItemModel item : itemList) {
                        subTotal = subTotal.add(MoneyConvertor.toBigDecimal(item.getSubTotalAmount()));
                    }
                    subtotalByType.put(itemType, MoneyConvertor.toMoney(subTotal, firstItem.getCurrencyCode()));
                });

        if (subtotalByType.isEmpty()) {
            return Map.of();
        }

        String currencyCode = orderItems.get(0).getCurrencyCode();
        // 汇总一个.
        subtotalByType.put(
                "all",
                MoneyConvertor.toMoney(
                        orderItems.stream()
                                .map(item -> MoneyUtil.moneyToBigDecimal(item.getSubTotalAmount()))
                                .reduce(BigDecimal.ZERO, BigDecimal::add),
                        currencyCode));

        return subtotalByType;
    }

    private List<OrderDetailView.PetBrief> batchGetPetBriefList(List<Integer> petIdList) {
        if (petIdList.isEmpty()) {
            return List.of();
        }
        return petClient.getCustomerPetListByIdList(petIdList).stream()
                .map(it -> OrderDetailView.PetBrief.newBuilder()
                        .setId(it.getPetId())
                        .setBreed(it.getBreed())
                        .setName(it.getPetName())
                        .build())
                .toList();
    }

    private List<OrderDetailView.StaffBrief> batchGetStaffBriefList(int businessId, List<Integer> staffIdList) {
        if (staffIdList.isEmpty()) {
            return List.of();
        }

        StaffIdListParams staffIdListParams = new StaffIdListParams(businessId, staffIdList);
        return iBusinessStaffClient.getStaffList(staffIdListParams).stream()
                .map(it -> OrderDetailView.StaffBrief.newBuilder()
                        .setId(it.getId())
                        .setFirstName(it.getFirstName())
                        .setLastName(it.getLastName())
                        .build())
                .toList();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getOrderGuid(GetOrderGuidParams params, StreamObserver<GetOrderGuidResult> responseObserver) {

        var response = orderServiceBlockingStub.getOrderGuid(GetOrderGuidRequest.newBuilder()
                .addAllOrderIds(params.getOrderIdsList())
                .setRequiredCvf(params.getRequiredCvf())
                .build());
        responseObserver.onNext(
                GetOrderGuidResult.newBuilder().setGuid(response.getGuid()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listOrdersForCombinedPayment(
            ListOrdersForCombinedPaymentParams request,
            StreamObserver<ListOrdersForCombinedPaymentResult> responseObserver) {
        var resp = orderServiceBlockingStub.listOrdersForCombinedPayment(
                OrderConvertor.INSTANCE.toRequest(request).toBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .build());

        List<OrderDetailModelV1> ordersList = resp.getOrdersList();
        if (CollectionUtils.isEmpty(ordersList)) {
            responseObserver.onNext(
                    ListOrdersForCombinedPaymentResult.newBuilder().build());
            responseObserver.onCompleted();
            return;
        }

        // 填充 customer 信息
        long customerId = ordersList.get(0).getOrder().getCustomerId();
        long businessId = ordersList.get(0).getOrder().getBusinessId();
        GroomingCustomerInfoDTO customerInfo = iCustomerCustomerService.getCustomerContactInfo(
                Math.toIntExact(businessId), Math.toIntExact(customerId));
        ListOrdersForCombinedPaymentResult.CustomerBrief.Builder customerBuilder =
                ListOrdersForCombinedPaymentResult.CustomerBrief.newBuilder();
        if (customerInfo != null) {
            customerBuilder.setId(customerInfo.getCustomerId());
            customerBuilder.setEmail(customerInfo.getEmail());
            customerBuilder.setFirstName(customerInfo.getFirstName());
            customerBuilder.setLastName(customerInfo.getLastName());
        }

        // 填充 grooming 信息
        List<Integer> groomingIdList = ordersList.stream()
                .filter(item -> OrderSourceType.APPOINTMENT
                                .getSource()
                                .equals(item.getOrder().getSourceType())
                        || OrderSourceType.NO_SHOW
                                .getSource()
                                .equals(item.getOrder().getSourceType()))
                .map(item -> Math.toIntExact(item.getOrder().getSourceId()))
                .toList();
        List<AppointmentDTO> apptList = iGroomingAppointmentService.list(groomingIdList);
        Map<Long, Integer> apptOrderMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(apptList)) {
            apptList.sort(Comparator.comparing(AppointmentDTO::getAppointmentDate));
            for (int i = 0; i < apptList.size(); i++) {
                apptOrderMap.put(Long.valueOf(apptList.get(i).getId()), i);
            }
        }
        // 排序 ordersList，让它们的 apptId 顺序跟 apptList 一致
        ordersList = new ArrayList<>(ordersList);
        ordersList.sort(Comparator.comparingInt(
                order -> apptOrderMap.getOrDefault(order.getOrder().getSourceId(), Integer.MAX_VALUE)));

        // 补充fee
        ListPaymentFeeParams listPaymentFeeParams = new ListPaymentFeeParams();
        listPaymentFeeParams.setBusinessId(Math.toIntExact(businessId));
        List<PaymentFeeParams> feeParamsList = ordersList.stream()
                .map(item -> {
                    PaymentFeeParams params = new PaymentFeeParams();
                    params.setPaymentId(item.getOrder().getId());
                    params.setAmount(MoneyUtils.fromGoogleMoney(item.getOrder().getRemainAmount()));
                    params.setPaymentMethod(PaymentStripeStatus.CARD_PAY);
                    return params;
                })
                .toList();
        listPaymentFeeParams.setPaymentFeeParams(feeParamsList);
        List<PaymentFeeDTO> feeDTOS = iPaymentPaymentService.listPaymentFees(listPaymentFeeParams);

        responseObserver.onNext(ListOrdersForCombinedPaymentResult.newBuilder()
                .addAllOrders(ordersList.stream()
                        .map(orderService::convertOrderDetailToView)
                        .toList())
                .setCustomer(customerBuilder.build())
                .addAllAppointments(apptList.stream()
                        .map(item -> ListOrdersForCombinedPaymentResult.AppointmentBrief.newBuilder()
                                .setId(item.getId())
                                .setAppointmentDate(item.getAppointmentDate())
                                .setAppointmentStartTime(item.getAppointmentStartTime())
                                .setAppointmentEndDate(item.getAppointmentEndDate())
                                .setAppointmentEndTime(item.getAppointmentEndTime())
                                .setCheckInTime(item.getCheckInTime())
                                .setCheckOutTime(item.getCheckOutTime())
                                .build())
                        .toList())
                .addAllConvenienceFees(feeDTOS.stream()
                        .map(item -> ListOrdersForCombinedPaymentResult.InitConvenienceFeeBrief.newBuilder()
                                .setOrderId(item.getPaymentId())
                                .setAddCvf(BigDecimal.ZERO.compareTo(item.getConvenienceFee()) >= 0)
                                .setFee(MoneyUtils.toGoogleMoney(item.getConvenienceFee()))
                                .build())
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void combinedPayOrder(
            CombinedPayOrderParams request, StreamObserver<CombinedPayOrderResult> responseObserver) {
        // 参数校验
        if (null == request) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "request is null");
        }
        Integer companyId = AuthContext.get().getCompanyId();
        if (null == companyId) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id is null");
        }
        Integer businessId = AuthContext.get().getBusinessId();
        if (null == businessId) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "business id is null");
        }
        Integer staffId = AuthContext.get().getStaffId();
        if (null == staffId) {
            staffId = 0;
        }
        CombinedPayOrderResponse orderResponse =
                orderServiceBlockingStub.combinedPayOrder(CombinedPayOrderRequest.newBuilder()
                        .addAllCombinedItems(request.getCombinedItemsList())
                        .setBusinessId(businessId)
                        .setCompanyId(companyId)
                        .setStaffId(staffId)
                        .build());

        PayPaymentResponse payResponse = paymentServiceBlockingStub.payPayment(PayPaymentRequest.newBuilder()
                .setId(orderResponse.getTransactionId())
                .setPaymentMethodType(request.getPayDef().getPaymentMethodType())
                .setDetail(request.getPayDef().getPaymentMethodDetail())
                .setAddConvenienceFee(request.getPayDef().getAddConvenienceFee())
                .setPayer(request.getPayDef().getPayer())
                .setDescription(request.getPayDef().getPaymentDescription())
                .build());

        responseObserver.onNext(CombinedPayOrderResult.newBuilder()
                .addAllOrderPayments(orderResponse.getOrderPaymentModelsList())
                .setRawPaymentResult(payResponse.getChannelResponse())
                .setTransactionId(orderResponse.getTransactionId())
                .setChannelPayment(ChannelPayment.newBuilder()
                        .setStripeChannelPayment(payResponse.getChannelPayment().getStripeChannelPayment())
                        .build())
                .build());
        responseObserver.onCompleted();
    }
}
