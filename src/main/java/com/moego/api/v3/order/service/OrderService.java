package com.moego.api.v3.order.service;

import com.google.type.Money;
import com.moego.api.v3.order.convertor.MoneyConvertor;
import com.moego.api.v3.subscription.util.MoneyUtil;
import com.moego.idl.models.order.v1.OrderDetailModelV1;
import com.moego.idl.models.order.v1.OrderDetailView;
import com.moego.idl.models.order.v1.OrderItemModel;
import com.moego.idl.models.order.v1.RefundOrderDetailModel;
import com.moego.idl.models.order.v1.RefundOrderDetailView;
import com.moego.idl.models.order.v1.RefundOrderItemModel;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.customer.client.IPetClient;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/4/11
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class OrderService {

    private final IPetClient petClient;
    private final IBusinessStaffClient iBusinessStaffClient;

    // 构造 OrderDetailView.
    public OrderDetailView convertOrderDetailToView(OrderDetailModelV1 orderDetail) {
        // 构造 OrderDetailView 里面的各种 View.
        OrderDetailView.Builder orderDetailViewBuilder = OrderDetailView.newBuilder()
                // Order detail.
                .setOrderDetail(orderDetail)
                // Pet brief.
                .addAllPetBriefs(batchGetPetBriefList(orderDetail.getOrderItemsList().stream()
                        .map(OrderItemModel::getPetId)
                        .filter(it -> it > 0)
                        .distinct()
                        .map(Long::intValue)
                        .toList()))
                // Staff brief.
                .addAllStaffBriefs(batchGetStaffBriefList(
                        Long.valueOf(orderDetail.getOrder().getBusinessId()).intValue(),
                        orderDetail.getOrderItemsList().stream()
                                .map(OrderItemModel::getStaffIdsList)
                                .filter(it -> !it.isEmpty())
                                .flatMap(List::stream)
                                .map(Long::intValue)
                                .sorted()
                                .distinct()
                                .toList()));

        // 构造 subtotal by item type 和 pre-discount subtotal by item type.
        Map<String, Money> subtotalByType = buildSubtotalByItemType(orderDetail.getOrderItemsList());
        if (!subtotalByType.isEmpty()) {
            orderDetailViewBuilder.putAllSubtotalByItemType(subtotalByType);
        }

        Map<String, Money> preDiscountSubtotalByType =
                buildPreDiscountSubtotalByItemType(orderDetail.getOrderItemsList());
        if (!preDiscountSubtotalByType.isEmpty()) {
            orderDetailViewBuilder.putAllPreDiscountSubtotalByItemType(preDiscountSubtotalByType);
        }

        // 构造 subtotal by tax. 按照 Tax ID 聚合的 TaxAmount.
        List<OrderDetailView.Tax> taxList = new ArrayList<>();
        //   构造 taxId -> orderItemList
        orderDetail.getOrderItemsList().stream()
                .filter(it -> it.getTaxId() > 0)
                .collect(Collectors.groupingBy(OrderItemModel::getTaxId))
                .forEach((taxId, orderItems) -> {
                    if (orderItems == null || orderItems.isEmpty()) {
                        return;
                    }
                    BigDecimal taxAmount = BigDecimal.ZERO;
                    for (OrderItemModel item : orderItems) {
                        taxAmount = taxAmount.add(MoneyConvertor.toBigDecimal(item.getTaxAmount()));
                    }

                    OrderItemModel firstItem = orderItems.get(0);
                    taxList.add(OrderDetailView.Tax.newBuilder()
                            .setId(firstItem.getTaxId())
                            .setName(firstItem.getTaxName())
                            .setRate(firstItem.getTaxRate())
                            .setAmount(MoneyConvertor.toMoney(taxAmount, firstItem.getCurrencyCode()))
                            .build());
                });
        if (!taxList.isEmpty()) orderDetailViewBuilder.addAllSubtotalByTax(taxList);

        return orderDetailViewBuilder.build();
    }

    // 构造 RefundOrderDetailView.
    public RefundOrderDetailView convertRefundOrderDetailToView(
            OrderDetailModelV1 orderDetail, RefundOrderDetailModel refundOrderDetail) {
        RefundOrderDetailView.Builder refundOrderDetailViewBuilder = RefundOrderDetailView.newBuilder()
                // Refund order detail.
                .setRefundOrderDetail(refundOrderDetail)
                // Pet brief.
                .addAllPetBriefs(batchGetPetBriefList(orderDetail.getOrderItemsList().stream()
                        .map(OrderItemModel::getPetId)
                        .filter(it -> it > 0)
                        .distinct()
                        .map(Long::intValue)
                        .toList()))
                // Staff brief.
                .addAllStaffBriefs(batchGetStaffBriefList(
                        Long.valueOf(orderDetail.getOrder().getBusinessId()).intValue(),
                        orderDetail.getOrderItemsList().stream()
                                .map(OrderItemModel::getStaffIdsList)
                                .filter(it -> !it.isEmpty())
                                .flatMap(List::stream)
                                .map(Long::intValue)
                                .sorted()
                                .distinct()
                                .toList()));
        // 构造 subtotal by item type
        Map<String, Money> subtotalByType = buildRefundSubtotalByItemType(refundOrderDetail.getRefundOrderItemsList());
        if (!subtotalByType.isEmpty()) {
            refundOrderDetailViewBuilder.putAllSubtotalByItemType(subtotalByType);
        }

        // 构造 subtotal by tax
        List<OrderDetailView.Tax> taxList = new ArrayList<>();
        //   构造 taxId -> orderItemList
        refundOrderDetail.getRefundOrderItemsList().stream()
                .filter(it -> it.getTaxId() > 0)
                .collect(Collectors.groupingBy(RefundOrderItemModel::getTaxId))
                .forEach((taxId, orderItems) -> {
                    if (orderItems == null || orderItems.isEmpty()) {
                        return;
                    }
                    BigDecimal taxSubtotal = BigDecimal.ZERO;
                    for (RefundOrderItemModel item : orderItems) {
                        taxSubtotal = taxSubtotal.add(MoneyConvertor.toBigDecimal(item.getRefundTax()));
                    }

                    RefundOrderItemModel firstItem = orderItems.get(0);
                    taxList.add(OrderDetailView.Tax.newBuilder()
                            .setId(firstItem.getTaxId())
                            .setName(firstItem.getTaxName())
                            .setRate(firstItem.getTaxRate())
                            .setAmount(MoneyConvertor.toMoney(taxSubtotal, firstItem.getCurrencyCode()))
                            .build());
                });
        if (!taxList.isEmpty()) refundOrderDetailViewBuilder.addAllSubtotalByTax(taxList);

        return refundOrderDetailViewBuilder.build();
    }

    private Map<String, Money> buildPreDiscountSubtotalByItemType(List<OrderItemModel> orderItems) {
        Map<String, Money> subtotalByType = new HashMap<>();
        orderItems.stream()
                .collect(Collectors.groupingBy(OrderItemModel::getType))
                .forEach((itemType, itemList) -> {
                    if (itemList == null || itemList.isEmpty()) {
                        return;
                    }

                    OrderItemModel firstItem = itemList.get(0);
                    BigDecimal subTotal = BigDecimal.ZERO;
                    for (OrderItemModel item : itemList) {
                        subTotal = subTotal.add(MoneyConvertor.toBigDecimal(item.getSubTotalAmount()));
                    }
                    subtotalByType.put(itemType, MoneyConvertor.toMoney(subTotal, firstItem.getCurrencyCode()));
                });

        if (subtotalByType.isEmpty()) {
            return Map.of();
        }

        String currencyCode = orderItems.get(0).getCurrencyCode();
        // 汇总一个.
        subtotalByType.put(
                "all",
                MoneyConvertor.toMoney(
                        orderItems.stream()
                                .map(item -> MoneyUtil.moneyToBigDecimal(item.getSubTotalAmount()))
                                .reduce(BigDecimal.ZERO, BigDecimal::add),
                        currencyCode));

        return subtotalByType;
    }

    private Map<String, Money> buildRefundSubtotalByItemType(List<RefundOrderItemModel> orderItems) {
        Map<String, Money> subtotalByType = new HashMap<>();
        orderItems.stream()
                .collect(Collectors.groupingBy(RefundOrderItemModel::getItemType))
                .forEach((itemType, itemList) -> {
                    if (itemList == null || itemList.isEmpty()) {
                        return;
                    }

                    RefundOrderItemModel firstItem = itemList.get(0);
                    BigDecimal subTotal = BigDecimal.ZERO;
                    for (RefundOrderItemModel item : itemList) {
                        subTotal = subTotal.add(MoneyConvertor.toBigDecimal(item.getRefundTotalAmount()));
                    }
                    subtotalByType.put(itemType, MoneyConvertor.toMoney(subTotal, firstItem.getCurrencyCode()));
                });

        if (subtotalByType.isEmpty()) {
            return Map.of();
        }

        String currencyCode = orderItems.get(0).getCurrencyCode();
        // 汇总一个.
        subtotalByType.put(
                "all",
                MoneyConvertor.toMoney(
                        orderItems.stream()
                                .map(item -> MoneyUtil.moneyToBigDecimal(item.getRefundTotalAmount()))
                                .reduce(BigDecimal.ZERO, BigDecimal::add),
                        currencyCode));

        return subtotalByType;
    }

    private Map<String, Money> buildSubtotalByItemType(List<OrderItemModel> orderItems) {
        Map<String, Money> subtotalByType = new HashMap<>();
        orderItems.stream()
                .collect(Collectors.groupingBy(OrderItemModel::getType))
                .forEach((itemType, itemList) -> {
                    if (itemList == null || itemList.isEmpty()) {
                        return;
                    }

                    OrderItemModel firstItem = itemList.get(0);
                    BigDecimal subTotal = BigDecimal.ZERO;
                    for (OrderItemModel item : itemList) {
                        subTotal = subTotal.add(MoneyConvertor.toBigDecimal(item.getTotalAmount()));
                    }
                    subtotalByType.put(itemType, MoneyConvertor.toMoney(subTotal, firstItem.getCurrencyCode()));
                });

        if (subtotalByType.isEmpty()) {
            return Map.of();
        }

        String currencyCode = orderItems.get(0).getCurrencyCode();
        // 汇总一个.
        subtotalByType.put(
                "all",
                MoneyConvertor.toMoney(
                        orderItems.stream()
                                .map(item -> MoneyUtil.moneyToBigDecimal(item.getTotalAmount()))
                                .reduce(BigDecimal.ZERO, BigDecimal::add),
                        currencyCode));

        return subtotalByType;
    }

    private List<OrderDetailView.PetBrief> batchGetPetBriefList(List<Integer> petIdList) {
        if (petIdList.isEmpty()) {
            return List.of();
        }
        return petClient.getCustomerPetListByIdList(petIdList).stream()
                .map(it -> OrderDetailView.PetBrief.newBuilder()
                        .setId(it.getPetId())
                        .setBreed(it.getBreed())
                        .setName(it.getPetName())
                        .build())
                .toList();
    }

    private List<OrderDetailView.StaffBrief> batchGetStaffBriefList(int businessId, List<Integer> staffIdList) {
        if (staffIdList.isEmpty()) {
            return List.of();
        }

        StaffIdListParams staffIdListParams = new StaffIdListParams(businessId, staffIdList);
        return iBusinessStaffClient.getStaffList(staffIdListParams).stream()
                .map(it -> OrderDetailView.StaffBrief.newBuilder()
                        .setId(it.getId())
                        .setFirstName(it.getFirstName())
                        .setLastName(it.getLastName())
                        .build())
                .toList();
    }
}
