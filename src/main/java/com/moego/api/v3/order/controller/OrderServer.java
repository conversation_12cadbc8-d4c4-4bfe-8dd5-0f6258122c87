package com.moego.api.v3.order.controller;

import com.google.type.Money;
import com.microtripit.mandrillapp.lutung.view.MandrillMessage;
import com.moego.api.v3.business_customer.service.BusinessCustomerService;
import com.moego.api.v3.order.convertor.AppointmentServiceConvertor;
import com.moego.api.v3.order.convertor.InvoiceConvertor;
import com.moego.api.v3.order.convertor.MoneyConvertor;
import com.moego.api.v3.order.convertor.OrderConvertor;
import com.moego.api.v3.order.service.OrderService;
import com.moego.api.v3.order.utils.OrderUtil;
import com.moego.api.v3.order.utils.PromotionUtil;
import com.moego.api.v3.subscription.util.MoneyUtil;
import com.moego.common.constant.OrderConstant;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.common.utils.PermissionUtil;
import com.moego.idl.api.order.v1.AddOrRemoveServiceChargeRequest;
import com.moego.idl.api.order.v1.BatchGetOrderDetailParams;
import com.moego.idl.api.order.v1.BatchGetOrderDetailResult;
import com.moego.idl.api.order.v1.CancelOrderParams;
import com.moego.idl.api.order.v1.CancelOrderResult;
import com.moego.idl.api.order.v1.CheckInvoiceReinventParams;
import com.moego.idl.api.order.v1.CheckInvoiceReinventResult;
import com.moego.idl.api.order.v1.CompleteOrderParams;
import com.moego.idl.api.order.v1.CompleteOrderResult;
import com.moego.idl.api.order.v1.CreateDepositOrderParams;
import com.moego.idl.api.order.v1.CreateDepositOrderResult;
import com.moego.idl.api.order.v1.CreateExtraOrderParams;
import com.moego.idl.api.order.v1.CreateExtraOrderResult;
import com.moego.idl.api.order.v1.CreateTipOrderParams;
import com.moego.idl.api.order.v1.CreateTipOrderResult;
import com.moego.idl.api.order.v1.DeleteLineItemForExtraOrderParams;
import com.moego.idl.api.order.v1.DeleteLineItemForExtraOrderResult;
import com.moego.idl.api.order.v1.EditStaffCommissionParams;
import com.moego.idl.api.order.v1.EditStaffCommissionResult;
import com.moego.idl.api.order.v1.GetEstimateParams;
import com.moego.idl.api.order.v1.GetEstimateResult;
import com.moego.idl.api.order.v1.GetInvoiceParams;
import com.moego.idl.api.order.v1.GetInvoiceResult;
import com.moego.idl.api.order.v1.GetOrderDetailParam;
import com.moego.idl.api.order.v1.GetOrderDetailResult;
import com.moego.idl.api.order.v1.GetOrderHistoryParams;
import com.moego.idl.api.order.v1.GetOrderHistoryResult;
import com.moego.idl.api.order.v1.GetRefundOrderDetailParam;
import com.moego.idl.api.order.v1.GetRefundOrderDetailResult;
import com.moego.idl.api.order.v1.ListOrderDetailParam;
import com.moego.idl.api.order.v1.ListOrderDetailResult;
import com.moego.idl.api.order.v1.ListOrdersParams;
import com.moego.idl.api.order.v1.ListOrdersResult;
import com.moego.idl.api.order.v1.ModifyItemTaxRequest;
import com.moego.idl.api.order.v1.ModifyItemTaxResponse;
import com.moego.idl.api.order.v1.OperateServiceChargeToOrderResponse;
import com.moego.idl.api.order.v1.OrderServiceGrpc.OrderServiceImplBase;
import com.moego.idl.api.order.v1.PreviewRefundOrderParams;
import com.moego.idl.api.order.v1.PreviewRefundOrderPaymentsParams;
import com.moego.idl.api.order.v1.PreviewRefundOrderPaymentsResult;
import com.moego.idl.api.order.v1.PreviewRefundOrderResult;
import com.moego.idl.api.order.v1.QueryOrderDetailParams;
import com.moego.idl.api.order.v1.QueryOrderDetailResult;
import com.moego.idl.api.order.v1.RefundOrderParams;
import com.moego.idl.api.order.v1.RefundOrderResult;
import com.moego.idl.api.order.v1.SendInvoiceParams;
import com.moego.idl.api.order.v1.SendInvoiceResult;
import com.moego.idl.api.order.v1.SetTipsRequest;
import com.moego.idl.api.order.v1.SetTipsResponse;
import com.moego.idl.api.order.v1.UpdateExtraOrderParams;
import com.moego.idl.api.order.v1.UpdateExtraOrderResult;
import com.moego.idl.api.order.v1.UpdateLineItemForExtraOrderParams;
import com.moego.idl.api.order.v1.UpdateLineItemForExtraOrderResult;
import com.moego.idl.api.order.v1.UpgradeInvoiceReinventParams;
import com.moego.idl.api.order.v1.UpgradeInvoiceReinventResult;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.fulfillment.v1.ServiceItem;
import com.moego.idl.models.order.v1.OrderDetailModelV1;
import com.moego.idl.models.order.v1.OrderDetailView;
import com.moego.idl.models.order.v1.OrderItemModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderModelHistoryView;
import com.moego.idl.models.order.v1.OrderModelV1;
import com.moego.idl.models.order.v1.OrderStatus;
import com.moego.idl.models.order.v1.RefundOrderDetailModel;
import com.moego.idl.models.order.v1.RefundOrderItemModel;
import com.moego.idl.models.order.v1.TipsSplitMode;
import com.moego.idl.models.promotion.v1.Source;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.CreatePetDetailsForExtraOrderRequest;
import com.moego.idl.service.appointment.v1.CreatePetDetailsForExtraOrderResponse;
import com.moego.idl.service.appointment.v1.DeletePetDetailForExtraOrderRequest;
import com.moego.idl.service.appointment.v1.DeletePetDetailForExtraOrderResponse;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.service.appointment.v1.PreviewOrderLineItemsRequest;
import com.moego.idl.service.appointment.v1.UpdatePetDetailResponse;
import com.moego.idl.service.order.v1.CancelOrderRequest;
import com.moego.idl.service.order.v1.CreateDepositOrderRequest;
import com.moego.idl.service.order.v1.CreateOrderRequest;
import com.moego.idl.service.order.v1.CreateOrderResponse;
import com.moego.idl.service.order.v1.CreateTipOrderRequest;
import com.moego.idl.service.order.v1.CreateTipOrderResponse;
import com.moego.idl.service.order.v1.GetGroomingDetailRelationResponse;
import com.moego.idl.service.order.v1.GetOrderDetailRequest;
import com.moego.idl.service.order.v1.GetOrderHistoryRequest;
import com.moego.idl.service.order.v1.GetOrderHistoryResponse;
import com.moego.idl.service.order.v1.GetOrderRequest;
import com.moego.idl.service.order.v1.GetRefundOrderDetailRequest;
import com.moego.idl.service.order.v1.ListOrderDetailRequest;
import com.moego.idl.service.order.v1.ListOrderDetailResponse;
import com.moego.idl.service.order.v1.ListOrdersV1Request;
import com.moego.idl.service.order.v1.ListOrdersV1Response;
import com.moego.idl.service.order.v1.ModifyItemTaxInput;
import com.moego.idl.service.order.v1.ModifyItemTaxOutput;
import com.moego.idl.service.order.v1.OperateOrderServiceChargeInput;
import com.moego.idl.service.order.v1.OperateOrderServiceChargeOutput;
import com.moego.idl.service.order.v1.OrderServiceGrpc.OrderServiceBlockingStub;
import com.moego.idl.service.order.v1.PreviewRefundOrderPaymentsRequest;
import com.moego.idl.service.order.v1.PreviewRefundOrderPaymentsResponse;
import com.moego.idl.service.order.v1.PreviewRefundOrderRequest;
import com.moego.idl.service.order.v1.PreviewRefundOrderResponse;
import com.moego.idl.service.order.v1.QueryOrderDetailRequest;
import com.moego.idl.service.order.v1.RefundOrderRequest;
import com.moego.idl.service.order.v1.RefundOrderResponse;
import com.moego.idl.service.order.v1.SetTipsResult;
import com.moego.idl.service.order.v1.UpdateExtraOrderRequest;
import com.moego.idl.service.order.v1.UpdateExtraOrderResponse;
import com.moego.idl.service.order.v1.UpdateOrderIncrRequest;
import com.moego.idl.service.order.v1.UpdateOrderIncrResponse;
import com.moego.idl.service.order.v2.GetTipsSplitRequest;
import com.moego.idl.service.order.v2.GetTipsSplitResponse;
import com.moego.idl.service.order.v2.OrderServiceGrpc;
import com.moego.idl.service.order.v2.PreviewCreateOrderRequest;
import com.moego.idl.service.order.v2.SplitTipsServiceGrpc;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyIdRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.grooming.client.IGroomingAppointmentClient;
import com.moego.server.grooming.client.IGroomingInvoiceClient;
import com.moego.server.grooming.client.IGroomingPetDetailClient;
import com.moego.server.grooming.dto.AppointmentDTO;
import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.grooming.params.EditPetDetailStaffCommissionParam;
import com.moego.server.grooming.params.status.StatusUpdateParams;
import com.moego.server.message.api.IEmailService;
import com.moego.server.message.params.MailSendParams;
import io.grpc.stub.StreamObserver;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@GrpcService
@AllArgsConstructor
@Slf4j
public class OrderServer extends OrderServiceImplBase {

    private final OrderServiceBlockingStub orderClient;

    private final OrderServiceGrpc.OrderServiceBlockingStub orderV2Client;

    private final com.moego.idl.service.order.v2.OrderServiceGrpc.OrderServiceBlockingStub orderClientV2;

    private final SplitTipsServiceGrpc.SplitTipsServiceBlockingStub splitTipsClient;

    private final PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailClient;

    private final IGroomingPetDetailClient groomingPetDetailClient;

    private final IGroomingAppointmentClient groomingAppointmentClient;

    private final IGroomingInvoiceClient groomingInvoiceClient;

    private final IPetClient petClient;

    private final IBusinessStaffClient iBusinessStaffClient;

    private final OrderService orderService;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceStub;

    private final PermissionHelper permissionHelper;

    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentServiceStub;

    private final PromotionUtil promotionUtil;

    private final BusinessCustomerService businessCustomerService;

    private final IEmailService emailService;

    private final IBusinessBusinessClient businessClient;

    @Override
    @Auth(AuthType.BUSINESS)
    public void modifyItemTax(ModifyItemTaxRequest request, StreamObserver<ModifyItemTaxResponse> responseObserver) {
        ModifyItemTaxInput input = OrderConvertor.INSTANCE.toModifyItemTaxInput(request).toBuilder()
                .setBusinessId(AuthContext.get().businessId())
                .setCompanyId(AuthContext.get().companyId())
                .setOperatorId(AuthContext.get().staffId())
                .setCheckRefund(request.hasCheckRefund() && request.getCheckRefund())
                .build();

        ModifyItemTaxOutput output = orderClient.modifyItemTax(input);
        responseObserver.onNext(ModifyItemTaxResponse.newBuilder()
                .setRefundChannel(output.getRefundChannel())
                .setResult(output.getResult())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void addServiceChargeToOrder(
            AddOrRemoveServiceChargeRequest request,
            StreamObserver<OperateServiceChargeToOrderResponse> responseObserver) {
        OperateOrderServiceChargeInput input = OperateOrderServiceChargeInput.newBuilder()
                .setBusinessId(AuthContext.get().businessId())
                .setCompanyId(AuthContext.get().companyId())
                .setOrderId(request.getOrderId())
                .addAllServiceChargeId(request.getServiceChargeIdList())
                .build();

        OperateOrderServiceChargeOutput output = orderClient.addServiceChargeToOrder(input);
        responseObserver.onNext(OperateServiceChargeToOrderResponse.newBuilder()
                .setResult(output.getResult())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void removeServiceChargeFromOrder(
            AddOrRemoveServiceChargeRequest request,
            StreamObserver<OperateServiceChargeToOrderResponse> responseObserver) {
        OperateOrderServiceChargeInput input = OperateOrderServiceChargeInput.newBuilder()
                .setBusinessId(AuthContext.get().businessId())
                .setOrderId(request.getOrderId())
                .addAllServiceChargeId(request.getServiceChargeIdList())
                .setCheckRefund(request.hasCheckRefund() && request.getCheckRefund())
                .build();
        OperateOrderServiceChargeOutput output = orderClient.removeServiceChargeFromOrder(input);
        responseObserver.onNext(OperateServiceChargeToOrderResponse.newBuilder()
                .setResult(output.getResult())
                .setRefundChannel(output.getRefundChannel())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void setTips(SetTipsRequest request, StreamObserver<SetTipsResponse> responseObserver) {
        SetTipsResult setTipsResult = orderClient.setTips(com.moego.idl.service.order.v1.SetTipsRequest.newBuilder()
                .setBusinessId(AuthContext.get().businessId())
                .setStaffId(AuthContext.get().staffId())
                .setInvoiceId(request.getInvoiceId())
                .setOmitResult(request.getOmitResult())
                .setLastModifiedTime(request.getLastModifiedTime())
                .setValue(request.getValue())
                .setValueType(request.getValueType())
                .setCheckRefund(request.hasCheckRefund() && request.getCheckRefund())
                .build());
        responseObserver.onNext(SetTipsResponse.newBuilder()
                .setResult(setTipsResult.getResult())
                .setRefundChannel(setTipsResult.getRefundChannel())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.COMPLETE_INVOICE})
    public void completeOrder(CompleteOrderParams request, StreamObserver<CompleteOrderResult> responseObserver) {
        // 检查预约是否已完成，如果没有 主动标记为完成
        OrderModel orderModel = orderClient.getOrder(
                GetOrderRequest.newBuilder().setId(request.getOrderId()).build());
        // 针对 refund 版本，检查是否 partial paid，是的话不允许强制关单
        if (orderModel.getOrderVersion() >= OrderConstant.ORDER_VERSION_REFUND && orderModel.getRemainAmount() > 0) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "the invoice is partial paid, please fully pay the invoice first.");
        }
        // 检查是否 overpaid， 是的话不允许直接关单
        if (OrderUtil.getOutstandingBalance(orderModel) < 0) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "the invoice is over paid, please refund or update the invoice first.");
        }

        // source type should be appointment
        if (OrderSourceType.APPOINTMENT.getSource().equals(orderModel.getSourceType())) {
            AppointmentDTO existAppt = groomingAppointmentClient.getAppointmentById((int) orderModel.getSourceId());
            // finish appointment when it is not finished
            if (!AppointmentStatusEnum.FINISHED.getValue().equals(existAppt.getStatus())) {
                StatusUpdateParams updateParams =
                        new StatusUpdateParams(existAppt.getId(), AppointmentStatusEnum.FINISHED, null, null);
                groomingAppointmentClient.updateStatus(
                        existAppt.getBusinessId(), AuthContext.get().getStaffId(), updateParams);
            }
        }
        // 核销 package， 该接口幂等
        groomingInvoiceClient.usePackageForGrooming(
                Math.toIntExact(orderModel.getBusinessId()), Math.toIntExact(request.getOrderId()));

        UpdateOrderIncrResponse updateOrderResponse =
                orderClient.updateOrderIncremental(UpdateOrderIncrRequest.newBuilder()
                        .setOrderId(request.getOrderId())
                        .setOrder(OrderModel.newBuilder()
                                .setId(request.getOrderId())
                                .setFulfillmentStatus(OrderModel.FulfillmentStatus.COMPLETED.name())
                                .setStatus(InvoiceStatusEnum.INVOICE_STATUS_COMPLETED)
                                .build())
                        .build());
        responseObserver.onNext(CompleteOrderResult.newBuilder()
                .setResult(updateOrderResponse.getRecord() > 0)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.EXTRA_CHARGE_INVOICE})
    public void createExtraOrder(
            CreateExtraOrderParams request, StreamObserver<CreateExtraOrderResult> responseObserver) {
        // 创建额外订单: 校验是否已创建，如果已创建则直接返回额外订单ID
        // 前提：原始订单已关闭
        CreateOrderResponse response = orderClient.createOrder(CreateOrderRequest.newBuilder()
                .setOrder(OrderModel.newBuilder()
                        .setCreateBy(AuthContext.get().staffId())
                        .setUpdateBy(AuthContext.get().staffId())
                        .setOrderRefId(request.getOriginOrderId())
                        .setExtraChargeReason(request.getExtraChargeReason())
                        .setOrderType(OrderModel.OrderType.EXTRA)
                        .build())
                .build());
        responseObserver.onNext(CreateExtraOrderResult.newBuilder()
                .setExtraOrderId(response.getId())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ADD_EXTRA_TIPS})
    public void createTipOrder(CreateTipOrderParams params, StreamObserver<CreateTipOrderResult> responseObserver) {
        // 创建额外订单: 校验是否已创建，如果已创建则直接返回额外订单ID
        CreateTipOrderRequest request = OrderConvertor.INSTANCE.toCreateTipOrderRequest(params);

        CreateTipOrderResponse response = orderClient.createTipOrder(request.toBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setStaffId(AuthContext.get().staffId())
                .build());

        responseObserver.onNext(CreateTipOrderResult.newBuilder()
                .setOrder(orderService.convertOrderDetailToView(response.getOrder()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateExtraOrder(
            UpdateExtraOrderParams request, StreamObserver<UpdateExtraOrderResult> responseObserver) {
        OrderModel order = orderClient.getOrder(
                GetOrderRequest.newBuilder().setId(request.getExtraOrderId()).build());
        // source type 需要是 appointment 类型
        if (!OrderSourceType.APPOINTMENT.getSource().equals(order.getSourceType())) {
            responseObserver.onNext(
                    UpdateExtraOrderResult.newBuilder().setResult(false).build());
            responseObserver.onCompleted();
            return;
        }
        boolean updated = false;
        // 聚合服务：增加 service item
        if (request.hasPetDetail()) {
            // 创建 pet detail 记录
            CreatePetDetailsForExtraOrderResponse createPetDetailsResponse =
                    petDetailClient.createPetDetailsForExtraOrder(CreatePetDetailsForExtraOrderRequest.newBuilder()
                            .setAppointmentId(order.getSourceId())
                            .setExtraOrderId(request.getExtraOrderId())
                            .setPetDetail(request.getPetDetail())
                            .setCompanyId(AuthContext.get().companyId())
                            .build());
            // create relation between extra order and pet detail
            if (createPetDetailsResponse.getPetDetailIdsCount() > 0) {
                updated = true;
            }
        }
        if (request.hasExtraChargeReason()) {
            UpdateExtraOrderRequest.Builder requestBuilder = UpdateExtraOrderRequest.newBuilder()
                    .setExtraOrderId(request.getExtraOrderId())
                    .setOrder(OrderModel.newBuilder()
                            .setId(request.getExtraOrderId())
                            .setExtraChargeReason(request.getExtraChargeReason())
                            .build());
            UpdateExtraOrderResponse updateOrderResponse = orderClient.updateExtraOrder(requestBuilder.build());
            updated = updateOrderResponse.getRecord() > 0;
            log.info("update extra order {} result: {}", updated, request.getExtraOrderId());
        }
        responseObserver.onNext(
                UpdateExtraOrderResult.newBuilder().setResult(updated).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateLineItemForExtraOrder(
            UpdateLineItemForExtraOrderParams request,
            StreamObserver<UpdateLineItemForExtraOrderResult> responseObserver) {
        // 当前仅限更新 staff id 和 service price
        UpdatePetDetailResponse petDetailResponse =
                petDetailClient.updatePetDetail(com.moego.idl.service.appointment.v1.UpdatePetDetailRequest.newBuilder()
                        .setId(request.getId())
                        .setStaffId(request.getStaffId())
                        .setServicePrice(request.getServicePrice())
                        .build());
        responseObserver.onNext(UpdateLineItemForExtraOrderResult.newBuilder()
                .setResult(petDetailResponse.getAffectedCount() > 0)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deleteLineItemForExtraOrder(
            DeleteLineItemForExtraOrderParams request,
            StreamObserver<DeleteLineItemForExtraOrderResult> responseObserver) {
        // check extra order and pet detail id rel
        GetGroomingDetailRelationResponse relations = orderClient.getGroomingDetailRelation(
                com.moego.idl.service.order.v1.GetGroomingDetailRelationRequest.newBuilder()
                        .setOrderId(request.getExtraOrderId())
                        .setIsOriginOrder(false)
                        .build());
        if (relations.getGroomingDetailRelationModelsList().stream()
                .noneMatch(relation -> relation.getPetDetailId() == request.getPetDetailId())) {
            // 如果没有查到关联关系，则直接返回失败
            responseObserver.onNext(DeleteLineItemForExtraOrderResult.newBuilder()
                    .setResult(false)
                    .build());
            responseObserver.onCompleted();
            return;
        }
        DeletePetDetailForExtraOrderResponse petDetailResult =
                petDetailClient.deletePetDetailForExtraOrder(DeletePetDetailForExtraOrderRequest.newBuilder()
                        .setPetDetailId(request.getPetDetailId())
                        .build());
        responseObserver.onNext(DeleteLineItemForExtraOrderResult.newBuilder()
                .setResult(petDetailResult.getResult())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getOrderHistory(GetOrderHistoryParams request, StreamObserver<GetOrderHistoryResult> responseObserver) {
        GetOrderHistoryResponse orderHistory = orderClient.getOrderHistory(GetOrderHistoryRequest.newBuilder()
                .setOriginOrderId(request.getOriginOrderId())
                .build());
        List<OrderModel> orderModelsList = orderHistory.getOrderModelsList();
        List<OrderModelHistoryView> orderModelHistoryViews = orderModelsList.stream()
                .map(orderModel -> {
                    OrderModelHistoryView.Builder builder = OrderModelHistoryView.newBuilder();
                    builder.setId(orderModel.getId());
                    builder.setOrderType(orderModel.getOrderType());
                    OrderStatus orderStatus = OrderStatus.forNumber(orderModel.getStatus());
                    orderStatus = orderStatus == null ? OrderStatus.CREATED : orderStatus;
                    builder.setStatus(orderStatus.name().toLowerCase());
                    if (StringUtils.hasLength(orderModel.getSourceType())) {
                        builder.setSourceType(OrderUtil.convert2SourceType(orderModel.getSourceType()));
                    }
                    builder.setSourceId(orderModel.getSourceId());
                    if (StringUtils.hasLength(orderModel.getPaymentStatus())) {
                        builder.setPaymentStatus(OrderModel.PaymentStatus.valueOf(orderModel.getPaymentStatus()));
                    }
                    if (StringUtils.hasLength(orderModel.getFulfillmentStatus())) {
                        builder.setFulfillmentStatus(
                                OrderModel.FulfillmentStatus.valueOf(orderModel.getFulfillmentStatus()));
                    }
                    // 这里的时间戳是秒级
                    builder.setCompleteTime(orderModel.getCompleteTime());
                    builder.setPaidAmount(orderModel.getPaidAmount());
                    builder.setTotalAmount(orderModel.getTotalAmount());
                    return builder.build();
                })
                .toList();
        responseObserver.onNext(GetOrderHistoryResult.newBuilder()
                .addAllOrderView(orderModelHistoryViews)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.MANAGE_INVOICE_STAFF})
    public void editStaffCommission(
            EditStaffCommissionParams request, StreamObserver<EditStaffCommissionResult> responseObserver) {
        OrderModel existingOrder = orderClient.getOrder(
                GetOrderRequest.newBuilder().setId(request.getOrderId()).build());
        if (existingOrder == null) {
            throw ExceptionUtil.bizException(Code.CODE_INVOICE_NOT_FOUND, "invalid order id");
        }
        // check if this order has appt level split config
        GetTipsSplitResponse splitConfig = splitTipsClient.getTipsSplitDetails(GetTipsSplitRequest.newBuilder()
                .setSourceId(existingOrder.getSourceId())
                .setSourceType(com.moego.idl.models.order.v1.OrderSourceType.APPOINTMENT)
                .build());

        if (TipsSplitMode.TIPS_SPLIT_MODE_APPT.equals(splitConfig.getTipsSplitMode())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_FINTECH_BUSINESS_APP_UPDATE_CLOSABLE, "please upgrade to new version");
        }

        Long businessId = existingOrder.getBusinessId();
        Long companyId = existingOrder.getCompanyId();
        EditPetDetailStaffCommissionParam param = new EditPetDetailStaffCommissionParam();
        param.setOrderId(request.getOrderId());
        param.setEditPetDetailStaffCommissionItemList(request.getEditStaffCommissionItemsList().stream()
                .map(item -> {
                    EditPetDetailStaffCommissionParam.EditPetDetailStaffCommissionItem commissionItem =
                            new EditPetDetailStaffCommissionParam.EditPetDetailStaffCommissionItem();
                    commissionItem.setOrderItemType(item.getOrderItemType());
                    commissionItem.setOrderItemId(item.getOrderItemId());
                    commissionItem.setPetDetailId(item.getPetDetailId());
                    commissionItem.setServiceId(item.getServiceId());
                    commissionItem.setPetId(item.getPetId());
                    commissionItem.setStaffId(item.getStaffId());
                    commissionItem.setBusinessId(businessId);
                    commissionItem.setCompanyId(companyId);
                    commissionItem.setOperationItemList(item.getOperationList().stream()
                            .map(op -> {
                                EditPetDetailStaffCommissionParam.EditPetDetailStaffCommissionOperationItem
                                        operationItem =
                                                new EditPetDetailStaffCommissionParam
                                                        .EditPetDetailStaffCommissionOperationItem();
                                operationItem.setStaffId(op.getStaffId());
                                operationItem.setRatio(op.getRatio());
                                operationItem.setDuration(op.getDuration());
                                operationItem.setOperationName(op.getOperationName());
                                return operationItem;
                            })
                            .toList());
                    return commissionItem;
                })
                .toList());
        groomingPetDetailClient.updatePetDetailStaff(param);
        com.moego.idl.service.order.v1.EditStaffCommissionParams orderEditStaffCommissionRequest =
                com.moego.idl.service.order.v1.EditStaffCommissionParams.newBuilder()
                        .setOrderId(request.getOrderId())
                        .addAllEditStaffCommissionItems(request.getEditStaffCommissionItemsList())
                        .build();
        orderClient.editStaffCommission(orderEditStaffCommissionRequest);
        responseObserver.onNext(EditStaffCommissionResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void upgradeInvoiceReinvent(
            UpgradeInvoiceReinventParams request, StreamObserver<UpgradeInvoiceReinventResult> responseObserver) {
        Integer companyId = AuthContext.get().getCompanyId();
        if (companyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id is null");
        }
        orderClient.upgradeInvoiceReinvent(com.moego.idl.service.order.v1.UpgradeInvoiceReinventRequest.newBuilder()
                .setCompanyId(companyId)
                .build());
        responseObserver.onNext(UpgradeInvoiceReinventResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void checkInvoiceReinvent(
            CheckInvoiceReinventParams request, StreamObserver<CheckInvoiceReinventResult> responseObserver) {
        Integer companyId = AuthContext.get().getCompanyId();
        if (companyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id is null");
        }
        com.moego.idl.service.order.v1.CheckInvoiceReinventResponse resp =
                orderClient.checkInvoiceReinvent(com.moego.idl.service.order.v1.CheckInvoiceReinventRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build());
        responseObserver.onNext(CheckInvoiceReinventResult.newBuilder()
                .setIsAllBizInInvoiceReinventWhitelist(resp.getIsInInvoiceReinventWhitelist())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.CAN_PROCESS_REFUND})
    public void previewRefundOrder(
            PreviewRefundOrderParams request, StreamObserver<PreviewRefundOrderResult> responseObserver) {
        Integer businessId = AuthContext.get().getBusinessId();
        if (businessId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "business id is null");
        }

        PreviewRefundOrderRequest.Builder previewRefundOrderRequestBuilder = PreviewRefundOrderRequest.newBuilder()
                .setOrderId(request.getOrderId())
                .setBusinessId(businessId)
                .setRefundMode(request.getRefundMode())
                .addAllSourceOrderPayments(request.getSourceOrderPaymentsList());
        switch (request.getRefundMode()) {
            case REFUND_MODE_BY_ITEM -> previewRefundOrderRequestBuilder.setRefundByItem(request.getRefundByItem());
            case REFUND_MODE_BY_PAYMENT -> previewRefundOrderRequestBuilder.setRefundByPayment(
                    request.getRefundByPayment());
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "unsupported refund mode");
        }

        PreviewRefundOrderResponse response = orderClient.previewRefundOrder(previewRefundOrderRequestBuilder.build());

        responseObserver.onNext(PreviewRefundOrderResult.newBuilder()
                .setOrderDetail(orderService.convertOrderDetailToView(response.getOrder()))
                .setRefundOrderPreview(orderService.convertRefundOrderDetailToView(
                        response.getOrder(), response.getPreviewRefundOrder()))
                .addAllRelatedRefundOrders(response.getRelatedRefundOrdersList().stream()
                        .map(it -> PreviewRefundOrderResult.RelatedRefundOrder.newBuilder()
                                .setOrderDetail(orderService.convertOrderDetailToView(it.getOrder()))
                                .setRefundOrderPreview(orderService.convertRefundOrderDetailToView(
                                        it.getOrder(), it.getPreviewRefundOrder()))
                                .build())
                        .toList())
                .setRefundableTips(response.getRefundableTips())
                .setRefundFlags(response.getRefundFlags())
                .setRefundableConvenienceFee(response.getRefundableConvenienceFee())
                .addAllRefundableItems(response.getRefundableItemsList())
                .addAllRefundableOrderPayments(response.getRefundableOrderPaymentsList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ANONYMOUS) // 纯计算逻辑，结果来自于输入，可以安全开放给所有人.
    public void previewRefundOrderPayments(
            PreviewRefundOrderPaymentsParams request,
            StreamObserver<PreviewRefundOrderPaymentsResult> responseObserver) {
        PreviewRefundOrderPaymentsResponse response =
                orderClient.previewRefundOrderPayments(PreviewRefundOrderPaymentsRequest.newBuilder()
                        .addAllSourceOrderPayments(request.getSourceOrderPaymentsList())
                        .setRefundAmount(request.getRefundAmount())
                        .setRefundAmountFlag(request.getRefundAmountFlag())
                        .build());

        responseObserver.onNext(PreviewRefundOrderPaymentsResult.newBuilder()
                .addAllRefundOrderPayments(response.getRefundOrderPaymentsList())
                .setRefundTotalAmount(response.getRefundTotalAmount())
                .setRefundConvenienceFee(response.getRefundConvenienceFee())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.CAN_PROCESS_REFUND})
    public void refundOrder(RefundOrderParams request, StreamObserver<RefundOrderResult> responseObserver) {
        Integer businessId = AuthContext.get().getBusinessId();
        if (businessId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "business id is null");
        }

        Integer staffId = AuthContext.get().getStaffId();
        if (staffId == null) {
            staffId = 0;
        }

        long orderID = request.getOrderId();
        OrderModel order =
                orderClient.getOrder(GetOrderRequest.newBuilder().setId(orderID).build());
        if (order.getBusinessId() != businessId) {
            log.error(
                    "business ID in order[{}] and business ID from auth[{}] not matched",
                    order.getBusinessId(),
                    businessId);
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "");
        }

        RefundOrderRequest.Builder refundOrderRequestBuilder = RefundOrderRequest.newBuilder()
                .setStaffId(staffId)
                .setBusinessId(businessId)
                .setOrderId(request.getOrderId())
                .setRefundMode(request.getRefundMode())
                .setRefundReason(request.getRefundReason())
                .addAllSourceOrderPayments(request.getSourceOrderPaymentsList());
        switch (request.getRefundMode()) {
            case REFUND_MODE_BY_ITEM -> refundOrderRequestBuilder.setRefundByItem(request.getRefundByItem());
            case REFUND_MODE_BY_PAYMENT -> refundOrderRequestBuilder.setRefundByPayment(request.getRefundByPayment());
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "unsupported refund mode");
        }

        RefundOrderResponse refundOrderResponse = orderClient.refundOrder(refundOrderRequestBuilder.build());

        responseObserver.onNext(RefundOrderResult.newBuilder()
                .setRefundOrderDetail(orderService.convertRefundOrderDetailToView(
                        orderClient
                                .getOrderDetailV1(GetOrderDetailRequest.newBuilder()
                                        .setOrderId(orderID)
                                        .setBusinessId(businessId)
                                        .build())
                                .getOrder(),
                        RefundOrderDetailModel.newBuilder()
                                .setRefundOrder(refundOrderResponse.getRefundOrder())
                                .addAllRefundOrderItems(refundOrderResponse.getRefundOrderItemsList())
                                .addAllRefundOrderPayments(refundOrderResponse.getRefundOrderPaymentList())
                                .build()))
                // TODO(Perqin, P1): 不应该在循环里调用 RPC
                .addAllRelatedRefundOrderDetails(refundOrderResponse.getRelatedRefundOrdersList().stream()
                        .map(it -> orderService.convertRefundOrderDetailToView(
                                orderClient
                                        .getOrderDetailV1(GetOrderDetailRequest.newBuilder()
                                                .setOrderId(it.getRefundOrder().getOrderId())
                                                .setBusinessId(businessId)
                                                .build())
                                        .getOrder(),
                                RefundOrderDetailModel.newBuilder()
                                        .setRefundOrder(it.getRefundOrder())
                                        .addAllRefundOrderItems(it.getRefundOrderItemsList())
                                        .addAllRefundOrderPayments(it.getRefundOrderPaymentList())
                                        .build()))
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    private boolean isBusinessBelongToCompany(long businessId, Integer companyId) {
        long actualCompanyId = businessServiceStub
                .getCompanyId(GetCompanyIdRequest.newBuilder()
                        .setBusinessId(businessId)
                        .build())
                .getCompanyId();

        return companyId.longValue() == actualCompanyId;
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listOrders(ListOrdersParams request, StreamObserver<ListOrdersResult> responseObserver) {
        Integer tokenCompanyId = AuthContext.get().getCompanyId();
        if (tokenCompanyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id is null");
        }

        if (!isBusinessBelongToCompany(request.getBusinessId(), tokenCompanyId)) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "company id not matched");
        }

        ListOrdersV1Response listOrderResponse = orderClient.listOrdersV1(ListOrdersV1Request.newBuilder()
                .setBusinessId(request.getBusinessId())
                .setOriginOrderId(request.getOriginOrderId())
                .build());
        responseObserver.onNext(ListOrdersResult.newBuilder()
                .addAllOrders(listOrderResponse.getOrdersList())
                .addAllRefundOrders(listOrderResponse.getRefundOrdersList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void queryOrderDetail(
            QueryOrderDetailParams request, StreamObserver<QueryOrderDetailResult> responseObserver) {
        Integer tokenCompanyId = AuthContext.get().getCompanyId();
        if (tokenCompanyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id is null");
        }

        var response = orderClient.queryOrderDetail(QueryOrderDetailRequest.newBuilder()
                .setCompanyId(tokenCompanyId)
                .setSourceType(request.getSourceType())
                .setSourceId(request.getSourceId())
                .build());

        Map<Long, OrderDetailModelV1> orderIdToOrder = response.getOrdersList().stream()
                .collect(Collectors.toMap(it -> it.getOrder().getId(), it -> it));

        responseObserver.onNext(QueryOrderDetailResult.newBuilder()
                .addAllOrders(response.getOrdersList().stream()
                        .map(orderService::convertOrderDetailToView)
                        .toList())
                .addAllRefundOrders(response.getRefundOrdersList().stream()
                        .filter(it ->
                                orderIdToOrder.containsKey(it.getRefundOrder().getOrderId()))
                        .map(it -> orderService.convertRefundOrderDetailToView(
                                orderIdToOrder.get(it.getRefundOrder().getOrderId()), it))
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void batchGetOrderDetail(
            BatchGetOrderDetailParams request, StreamObserver<BatchGetOrderDetailResult> responseObserver) {
        ListOrderDetailResponse resp =
                orderClientV2.listOrderDetail(com.moego.idl.service.order.v2.ListOrderDetailRequest.newBuilder()
                        .addAllOrderIds(request.getOrderIdsList())
                        .build());

        Map<Long, OrderDetailModelV1> orderIdToOrder = resp.getOrdersList().stream()
                .collect(Collectors.toMap(it -> it.getOrder().getId(), it -> it));
        responseObserver.onNext(BatchGetOrderDetailResult.newBuilder()
                .addAllOrders(resp.getOrdersList().stream()
                        .map(orderService::convertOrderDetailToView)
                        .toList())
                .addAllRefundOrders(resp.getRefundOrdersList().stream()
                        .filter(it ->
                                orderIdToOrder.containsKey(it.getRefundOrder().getOrderId()))
                        .map(it -> orderService.convertRefundOrderDetailToView(
                                orderIdToOrder.get(it.getRefundOrder().getOrderId()), it))
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listOrderDetail(ListOrderDetailParam request, StreamObserver<ListOrderDetailResult> responseObserver) {
        Integer tokenCompanyId = AuthContext.get().getCompanyId();
        if (tokenCompanyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id is null");
        }

        OrderModel order = orderClient.getOrder(
                GetOrderRequest.newBuilder().setId(request.getOriginOrderId()).build());
        if (!isBusinessBelongToCompany(order.getBusinessId(), tokenCompanyId)) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "company id not matched");
        }

        ListOrderDetailResponse response = orderClient.listOrderDetail(ListOrderDetailRequest.newBuilder()
                .setBusinessId(order.getBusinessId())
                .setOriginOrderId(request.getOriginOrderId())
                .build());

        Map<Long, OrderDetailModelV1> orderIdToOrder = response.getOrdersList().stream()
                .collect(Collectors.toMap(it -> it.getOrder().getId(), it -> it));

        responseObserver.onNext(ListOrderDetailResult.newBuilder()
                .addAllOrders(response.getOrdersList().stream()
                        .map(orderService::convertOrderDetailToView)
                        .toList())
                .addAllRefundOrders(response.getRefundOrdersList().stream()
                        .filter(it ->
                                orderIdToOrder.containsKey(it.getRefundOrder().getOrderId()))
                        .map(it -> orderService.convertRefundOrderDetailToView(
                                orderIdToOrder.get(it.getRefundOrder().getOrderId()), it))
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getOrderDetail(GetOrderDetailParam request, StreamObserver<GetOrderDetailResult> responseObserver) {
        Integer tokenCompanyId = AuthContext.get().getCompanyId();
        if (tokenCompanyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id is null");
        }

        OrderModel order = orderClient.getOrder(
                GetOrderRequest.newBuilder().setId(request.getOrderId()).build());
        if (!isBusinessBelongToCompany(order.getBusinessId(), tokenCompanyId)) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "company id not matched");
        }

        responseObserver.onNext(GetOrderDetailResult.newBuilder()
                .setOrder(orderService.convertOrderDetailToView(orderClient
                        .getOrderDetailV1(GetOrderDetailRequest.newBuilder()
                                .setBusinessId(order.getBusinessId())
                                .setOrderId(request.getOrderId())
                                .build())
                        .getOrder()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void getRefundOrderDetail(
            GetRefundOrderDetailParam request, StreamObserver<GetRefundOrderDetailResult> responseObserver) {
        Integer businessId = AuthContext.get().getBusinessId();
        if (businessId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "business id is null");
        }

        RefundOrderDetailModel refundOrder = orderClient
                .getRefundOrderDetail(GetRefundOrderDetailRequest.newBuilder()
                        .setRefundOrderId(request.getRefundOrderId())
                        .setBusinessId(businessId)
                        .build())
                .getRefundOrder();

        OrderDetailModelV1 orderDetail = orderClient
                .getOrderDetailV1(GetOrderDetailRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setOrderId(refundOrder.getRefundOrder().getOrderId())
                        .build())
                .getOrder();

        responseObserver.onNext(GetRefundOrderDetailResult.newBuilder()
                .setRefundOrder(orderService.convertRefundOrderDetailToView(orderDetail, refundOrder))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void createDepositOrder(
            CreateDepositOrderParams request, StreamObserver<CreateDepositOrderResult> responseObserver) {
        // TODO(yunxiang): 检查 Appointment 与 Business/Company 的关系.
        Integer tokenCompanyId = AuthContext.get().getCompanyId();
        if (tokenCompanyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id is null");
        }

        if (!isBusinessBelongToCompany(request.getBusinessId(), tokenCompanyId)) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "company id not matched");
        }

        OrderDetailModelV1 depositOrder = orderClient
                .createDepositOrder(CreateDepositOrderRequest.newBuilder()
                        .setCompanyId(tokenCompanyId)
                        .setBusinessId(request.getBusinessId())
                        .setCustomerId(request.getCustomerId())
                        .setStaffId(request.getStaffId())
                        .setSourceType(request.getSourceType())
                        .setSourceId(request.getSourceId())
                        .setDepositAmount(request.getDepositAmount())
                        .build())
                .getOrder();

        responseObserver.onNext(CreateDepositOrderResult.newBuilder()
                .setOrder(orderService.convertOrderDetailToView(depositOrder))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void cancelOrder(CancelOrderParams request, StreamObserver<CancelOrderResult> responseObserver) {
        Integer tokenCompanyId = AuthContext.get().getCompanyId();
        if (tokenCompanyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id is null");
        }

        if (!isBusinessBelongToCompany(request.getBusinessId(), tokenCompanyId)) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "company id not matched");
        }

        orderClient.cancelOrder(CancelOrderRequest.newBuilder()
                .setBusinessId(request.getBusinessId())
                .setOrderId(request.getOrderId())
                .setStaffId(Optional.ofNullable(AuthContext.get().getStaffId()).orElse(0))
                .build());

        responseObserver.onNext(CancelOrderResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getInvoice(GetInvoiceParams request, StreamObserver<GetInvoiceResult> responseObserver) {
        Long tokenCompanyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();

        // 当前版本的 Invoice 是用第一个 order 来顶替的，可能是 empty order 也可能是 deposit order.
        OrderModel invoice = orderClient.getOrder(
                GetOrderRequest.newBuilder().setId(request.getInvoiceId()).build());
        if (tokenCompanyId == null || tokenCompanyId != invoice.getCompanyId()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id not matched");
        }

        BusinessCustomerInfoModel customer = businessCustomerService.getBusinessCustomerInfo(invoice.getCustomerId());

        // 这里混用了 java 老服务自定义的 order source type. 后面统一调整成 PB enum 的时候一并处理.
        if (!OrderSourceType.APPOINTMENT.getSource().equalsIgnoreCase(invoice.getSourceType())
                && !OrderSourceType.NO_SHOW.getSource().equalsIgnoreCase(invoice.getSourceType())) {
            getInvoiceForNonAppointment(invoice, customer, responseObserver);
            return;
        }

        AppointmentDTO appointmentDTO = groomingAppointmentClient.getAppointmentById(
                Long.valueOf(invoice.getSourceId()).intValue());

        var listOrderDetailResponse = orderClient.listOrderDetail(ListOrderDetailRequest.newBuilder()
                .setOriginOrderId(invoice.getId())
                .setBusinessId(invoice.getBusinessId())
                .build());
        List<OrderDetailModelV1> orders = listOrderDetailResponse.getOrdersList();
        List<RefundOrderDetailModel> refundOrders = listOrderDetailResponse.getRefundOrdersList();

        // 判断是否全部都是 V4 版本
        boolean allImmutableOrder = true;
        for (OrderDetailModelV1 order : orders) {
            if (!OrderUtil.isImmutableOrder(order)) {
                allImmutableOrder = false;
                break;
            }
        }

        // BigInvoiceID 需要从所有 Order 里面查询.
        long bigInvoiceId = InvoiceConvertor.getInvoiceId(orders);

        // 此处不能过滤订单状态，因为 Canceled order 的支付和退款记录也需要展示.
        List<OrderDetailModelV1> nonDepositOrders =
                orders.stream().filter(OrderUtil::isNotDepositOrder).toList();
        List<OrderDetailModelV1> depositOrders =
                orders.stream().filter(OrderUtil::isDepositOrder).toList();

        Long customerId = invoice.getCustomerId();

        // Deposit 正向和逆向都只包含 Deposit 的数据.
        Set<Long> depositOrderIds = depositOrders.stream()
                .collect(Collectors.toMap(it -> it.getOrder().getId(), it -> it))
                .keySet();
        OrderDetailModelV1 deposit = InvoiceConvertor.buildBigInvoice(
                null,
                depositOrders,
                InvoiceConvertor.getAllOrderPayments(depositOrders),
                InvoiceConvertor.getAllRefundOrderPayments(
                        depositOrders,
                        refundOrders.stream()
                                .filter(it -> depositOrderIds.contains(
                                        it.getRefundOrder().getOrderId()))
                                .toList()));

        // 处理 Appointment 被 Cancel 且有 NoShow Order 的情况.
        if (AppointmentStatus.CANCELED.equals(AppointmentStatus.forNumber(appointmentDTO.getStatus()))) {
            var noShowOrder = nonDepositOrders.stream()
                    .filter(it -> OrderSourceType.NO_SHOW
                            .getSource()
                            .equalsIgnoreCase(it.getOrder().getSourceType()))
                    .findFirst();

            GetInvoiceResult.InvoiceStatus invoiceStatus = GetInvoiceResult.InvoiceStatus.CLOSE;
            // 仅当有未完全支付的 no show 时，才让 invoice 变成 OPEN.
            if (noShowOrder.isPresent()
                    && !OrderModel.PaymentStatus.PAID.equals(
                            noShowOrder.get().getOrder().getPaymentStatus())) {
                invoiceStatus = GetInvoiceResult.InvoiceStatus.OPEN;
            }

            OrderDetailModelV1 bigInvoice = InvoiceConvertor.buildBigInvoice(
                    null,
                    List.of(noShowOrder.orElse(OrderDetailModelV1.getDefaultInstance())),
                    InvoiceConvertor.getAllOrderPayments(orders),
                    InvoiceConvertor.getAllRefundOrderPayments(orders, refundOrders));

            responseObserver.onNext(GetInvoiceResult.newBuilder()
                    .setAppointment(getAppointmentBriefForInvoice(appointmentDTO))
                    .setCustomer(buildCustomerBriefForInvoice(customer, tokenCompanyId, staffId))
                    .setInvoice(orderService.convertOrderDetailToView(bigInvoice))
                    .setDeposit(orderService.convertOrderDetailToView(deposit))
                    .setInvoiceStatus(invoiceStatus)
                    .build());
            responseObserver.onCompleted();
            return;
        }

        // 构造一个 orderID 到 paymentStatus 的映射.
        Map<Long, OrderModel.PaymentStatus> orderIDToPaymentStatus = nonDepositOrders.stream()
                .collect(Collectors.toMap(
                        it -> it.getOrder().getId(), it -> it.getOrder().getPaymentStatus()));

        // 取出所有 Order 上的 Items.
        List<OrderItemModel> orderItems = nonDepositOrders.stream()
                .map(OrderDetailModelV1::getOrderItemsList)
                .flatMap(List::stream)
                .filter(it -> !it.getIsDeleted())
                .toList();

        Map<String, OrderItemModel> uuidToItem = orderItems.stream()
                .filter(it -> !com.moego.lib.utils.StringUtils.isBlank(it.getExternalUuid()))
                .collect(Collectors.toMap(OrderItemModel::getExternalUuid, it -> it));

        var previewOrderLineItemsResponse =
                appointmentServiceStub.previewOrderLineItems(PreviewOrderLineItemsRequest.newBuilder()
                        .setAppointmentId(appointmentDTO.getId())
                        .build());

        // 用来 Preview 还未创建订单的 Item.
        List<PreviewCreateOrderRequest.CartItem> cartItems = new ArrayList<>();
        previewOrderLineItemsResponse.getPetServicesList().forEach(it -> {
            // 仅保留未创建 order 的 items.
            cartItems.addAll(AppointmentServiceConvertor.toCartItems(it).stream()
                    .filter(cit -> !uuidToItem.containsKey(cit.getExternalUuid()))
                    .toList());
        });
        previewOrderLineItemsResponse.getSurchargesList().forEach(it -> {
            if (uuidToItem.containsKey(it.getExternalId())) {
                return;
            }
            cartItems.add(AppointmentServiceConvertor.toCartItem(it));
        });

        OrderDetailModelV1 previewOrderDetail = null;
        // 存在未创建订单的 Items，需要走 Preview 流程.
        // 非 v4 一定不走 Preview.
        if (!cartItems.isEmpty() && allImmutableOrder) {
            List<PreviewCreateOrderRequest.Promotion> appliedPromotions = List.of();
            if (!customer.getDeleted()) {
                // 正常不应该走到这里，但是以防万一出现 appt 未完成，然后 customer 又被删除的情况.
                appliedPromotions = promotionUtil.recommendPromotions(customerId, cartItems);
                // Get invoice 里面 Preview 不需要过滤特定类型的优惠.
                PromotionUtil.sortAppliedPromotions(appliedPromotions);
            }

            // 直接预览整个 Appointment 一次性提交的情况.
            previewOrderDetail = orderV2Client
                    .previewCreateOrder(PreviewCreateOrderRequest.newBuilder()
                            .setSourceType(com.moego.idl.models.order.v1.OrderSourceType.APPOINTMENT)
                            .setSourceId(appointmentDTO.getId())
                            .setCompanyId(tokenCompanyId)
                            .setBusinessId(appointmentDTO.getBusinessId())
                            .setCustomerId(customerId)
                            .addAllItems(cartItems)
                            .setAppliedPromotions(PreviewCreateOrderRequest.AppliedPromotions.newBuilder()
                                    .addAllPromotions(appliedPromotions)
                                    .build())
                            .build())
                    .getOrder();
        }

        // 都是 V4 的数据才需要进行合并
        if (previewOrderDetail != null && allImmutableOrder) {
            nonDepositOrders = Stream.concat(nonDepositOrders.stream(), Stream.of(previewOrderDetail))
                    .toList();
        }

        OrderDetailModelV1 bigInvoice = InvoiceConvertor.buildBigInvoice(
                bigInvoiceId,
                nonDepositOrders,
                InvoiceConvertor.getAllOrderPayments(orders),
                InvoiceConvertor.getAllRefundOrderPayments(orders, refundOrders));

        // 在整个 Invoice 的角度，Deposit 的钱也是 Total 的一部分.
        // 因此在这里将 BigInvoice 的 DepositAmount 加回 Total.
        bigInvoice = OrderDetailModelV1.newBuilder(bigInvoice)
                .setOrder(OrderModelV1.newBuilder(bigInvoice.getOrder())
                        .setTotalAmount(MoneyConvertor.toMoney(
                                MoneyConvertor.toBigDecimal(
                                                bigInvoice.getOrder().getTotalAmount())
                                        .add(MoneyConvertor.toBigDecimal(
                                                bigInvoice.getOrder().getDepositAmount())),
                                bigInvoice.getOrder().getCurrencyCode())))
                .build();

        Map<Long, Boolean> orderItemIDToIsPaid = orderItems.stream()
                .collect(Collectors.toMap(
                        OrderItemModel::getId,
                        it -> OrderModel.PaymentStatus.PAID.equals(orderIDToPaymentStatus.getOrDefault(
                                it.getOrderId(), OrderModel.PaymentStatus.UNPAID))));

        GetInvoiceResult.InvoiceStatus invoiceStatus = GetInvoiceResult.InvoiceStatus.OPEN;
        // 没有未创建订单的 items，并且 Invoice 已全额支付时，认为该 Invoice 已经 Close.
        if ((cartItems.isEmpty() || !allImmutableOrder) // 非 v4 的订单不受此限制
                && OrderModel.PaymentStatus.PAID.equals(bigInvoice.getOrder().getPaymentStatus())) {
            invoiceStatus = GetInvoiceResult.InvoiceStatus.CLOSE;
        }

        responseObserver.onNext(GetInvoiceResult.newBuilder()
                .setInvoice(orderService.convertOrderDetailToView(bigInvoice))
                .setDeposit(orderService.convertOrderDetailToView(deposit))
                .setAppointment(getAppointmentBriefForInvoice(appointmentDTO))
                .setCustomer(buildCustomerBriefForInvoice(customer, tokenCompanyId, staffId))
                .addAllOrderItemExtras(buildOrderItemExtras(
                        bigInvoice.getOrderItemsList(),
                        AppointmentServiceConvertor.buildUUIDToServiceItem(
                                previewOrderLineItemsResponse.getPetServicesList()),
                        orderItemIDToIsPaid))
                .setInvoiceStatus(invoiceStatus)
                .build());
        responseObserver.onCompleted();
    }

    private void getInvoiceForNonAppointment(
            OrderModel invoice, BusinessCustomerInfoModel customer, StreamObserver<GetInvoiceResult> responseObserver) {
        // 非 Appointment 的 Invoice 没有 Appointment 数据，也不需要联动 Appointment 的状态，以及未创建 Order 的 items 等。
        var listOrderDetailResponse = orderClient.listOrderDetail(ListOrderDetailRequest.newBuilder()
                .setOriginOrderId(invoice.getId())
                .setBusinessId(invoice.getBusinessId())
                .build());
        List<OrderDetailModelV1> orders = listOrderDetailResponse.getOrdersList();
        List<RefundOrderDetailModel> refundOrders = listOrderDetailResponse.getRefundOrdersList();

        Map<Long, OrderStatus> idToStatus = orders.stream()
                .collect(Collectors.toMap(
                        it -> it.getOrder().getId(), it -> it.getOrder().getStatus()));
        Map<Long, Boolean> itemIDToIsPaid = orders.stream()
                .flatMap(it -> it.getOrderItemsList().stream())
                .collect(Collectors.toMap(
                        OrderItemModel::getId,
                        it -> OrderStatus.COMPLETED.equals(
                                idToStatus.getOrDefault(it.getOrderId(), OrderStatus.CREATED))));

        OrderDetailModelV1 bigInvoice = InvoiceConvertor.buildBigInvoice(
                invoice.getId(),
                orders,
                InvoiceConvertor.getAllOrderPayments(orders),
                InvoiceConvertor.getAllRefundOrderPayments(orders, refundOrders));

        Long tokenCompanyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();

        GetInvoiceResult.InvoiceStatus invoiceStatus = GetInvoiceResult.InvoiceStatus.OPEN;
        // Invoice 已全额支付时，认为该 Invoice 已经 Close.
        if (OrderModel.PaymentStatus.PAID.equals(bigInvoice.getOrder().getPaymentStatus())) {
            invoiceStatus = GetInvoiceResult.InvoiceStatus.CLOSE;
        }

        responseObserver.onNext(GetInvoiceResult.newBuilder()
                .setInvoice(orderService.convertOrderDetailToView(bigInvoice))
                .setCustomer(buildCustomerBriefForInvoice(customer, tokenCompanyId, staffId))
                .addAllOrderItemExtras(
                        buildOrderItemExtrasForNonAppointmentInvoice(bigInvoice.getOrderItemsList(), itemIDToIsPaid))
                .setInvoiceStatus(invoiceStatus)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void sendInvoice(SendInvoiceParams request, StreamObserver<SendInvoiceResult> responseObserver) {
        Long tokenCompanyId = AuthContext.get().companyId();
        Integer businessId = 0;

        // 不同类型用不同方式鉴权.
        if (request.hasEstimate()) {
            var estimate = request.getEstimate();
            if (!com.moego.idl.models.order.v1.OrderSourceType.APPOINTMENT.equals(estimate.getSourceType())) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "unsupported source type");
            }

            AppointmentDTO appointmentDTO = groomingAppointmentClient.getAppointmentById(
                    Long.valueOf(estimate.getSourceId()).intValue());
            if (!tokenCompanyId.equals(appointmentDTO.getCompanyId())) {
                throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "company id not matched");
            }

            businessId = appointmentDTO.getBusinessId();
        } else {
            var receipt = request.getReceipt();
            OrderModel order = orderClient.getOrder(
                    GetOrderRequest.newBuilder().setId(receipt.getInvoiceId()).build());

            if (order.getCompanyId() != tokenCompanyId) {
                throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "company id not matched");
            }

            businessId = Long.valueOf(order.getBusinessId()).intValue();
        }

        MoeBusinessDto businessInfo = businessClient.getBusinessInfo(
                InfoIdParams.builder().infoId(businessId).build());

        // 构建发送邮件参数
        String subject = String.format(
                "Estimated invoice for your upcoming appointment with %s", businessInfo.getBusinessName());
        if (request.hasReceipt()) {
            subject = String.format("Invoice for your appointment with %s", businessInfo.getBusinessName());
        }

        MailSendParams mailSendParams = new MailSendParams();
        mailSendParams.setSubject(subject);
        mailSendParams.setFrom_name(businessInfo.getBusinessName());
        mailSendParams.setHtml(InvoiceConvertor.buildInvoiceEmail(request.getEstimateImageUrl()));

        // 设置发送人
        MandrillMessage.Recipient recipient = new MandrillMessage.Recipient();

        recipient.setEmail(request.getTargetEmailAddress());
        recipient.setType(MandrillMessage.Recipient.Type.TO);

        mailSendParams.setTo(List.of(recipient));
        mailSendParams.setBusinessId(businessId);

        emailService.sendEmail(mailSendParams);

        responseObserver.onNext(SendInvoiceResult.newBuilder().getDefaultInstanceForType());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getEstimate(GetEstimateParams request, StreamObserver<GetEstimateResult> responseObserver) {
        if (!com.moego.idl.models.order.v1.OrderSourceType.APPOINTMENT.equals(request.getSourceType())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "unsupported source type");
        }

        Long companyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();

        AppointmentDTO appointmentDTO = groomingAppointmentClient.getAppointmentById(
                Long.valueOf(request.getSourceId()).intValue());
        if (!companyId.equals(appointmentDTO.getCompanyId())) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "company id not matched");
        }

        Long customerId = appointmentDTO.getCustomerId().longValue();
        BusinessCustomerInfoModel customer = businessCustomerService.getBusinessCustomerInfo(customerId);

        // Estimate 需要展示 Deposit 的内容.
        var queryOrderResponse = orderClient.queryOrderDetail(QueryOrderDetailRequest.newBuilder()
                .setSourceId(appointmentDTO.getId())
                .setCompanyId(appointmentDTO.getCompanyId())
                .setSourceType(com.moego.idl.models.order.v1.OrderSourceType.APPOINTMENT)
                .build());

        List<OrderDetailModelV1> depositOrders = queryOrderResponse.getOrdersList().stream()
                .filter(OrderUtil::isDepositOrder)
                .toList();

        // Refund 也只展示 Deposit 的 Refund 数据.
        Set<Long> depositOrderIds = depositOrders.stream()
                .collect(Collectors.toMap(it -> it.getOrder().getId(), it -> it))
                .keySet();
        List<RefundOrderDetailModel> refundOrders = queryOrderResponse.getRefundOrdersList().stream()
                .filter(it -> depositOrderIds.contains(it.getRefundOrder().getOrderId()))
                .toList();

        OrderDetailModelV1 deposit = InvoiceConvertor.buildBigInvoice(
                0L, // Estimate 没有 Invoice ID
                depositOrders,
                InvoiceConvertor.getAllOrderPayments(depositOrders),
                InvoiceConvertor.getAllRefundOrderPayments(depositOrders, refundOrders));

        // Appointment canceled 之后，Estimate 除了 Deposit 不展示任何内容.
        if (AppointmentStatus.CANCELED.equals(AppointmentStatus.forNumber(appointmentDTO.getStatus()))) {
            responseObserver.onNext(GetEstimateResult.newBuilder()
                    .setAppointment(getAppointmentBriefForInvoice(appointmentDTO))
                    .setCustomer(buildCustomerBriefForInvoice(customer, companyId, staffId))
                    .setEstimate(OrderDetailView.getDefaultInstance())
                    .setDeposit(orderService.convertOrderDetailToView(deposit))
                    .build());
            responseObserver.onCompleted();
            return;
        }

        // Estimate 只以 Appointment 的信息为准，叠加非 pkg 类型的优惠预览.
        var previewOrderLineItemsResponse =
                appointmentServiceStub.previewOrderLineItems(PreviewOrderLineItemsRequest.newBuilder()
                        .setAppointmentId(request.getSourceId())
                        .build());

        List<PreviewCreateOrderRequest.CartItem> cartItems = new ArrayList<>();
        previewOrderLineItemsResponse.getPetServicesList().forEach(it -> {
            cartItems.addAll(AppointmentServiceConvertor.toCartItems(it));
        });
        previewOrderLineItemsResponse.getSurchargesList().forEach(it -> {
            cartItems.add(AppointmentServiceConvertor.toCartItem(it));
        });

        List<PreviewCreateOrderRequest.Promotion> appliedPromotions = List.of();
        if (!customer.getDeleted()) {
            // 正常不应该到这里，但是以防万一.
            appliedPromotions = promotionUtil.recommendPromotions(customerId, cartItems);
            // Estimate 需要移除 pkg / membership pkg 类型.
            appliedPromotions = appliedPromotions.stream()
                    .filter(it -> !Set.of(Source.Type.PACKAGE, Source.Type.MEMBERSHIP_QUANTITY)
                            .contains(it.getCouponSource().getType()))
                    .collect(Collectors.toCollection(ArrayList::new));
            PromotionUtil.sortAppliedPromotions(appliedPromotions);
        }

        // 直接预览整个 Appointment 一次性提交的情况.
        OrderDetailModelV1 previewOrderDetail = orderV2Client
                .previewCreateOrder(PreviewCreateOrderRequest.newBuilder()
                        .setSourceType(request.getSourceType())
                        .setSourceId(request.getSourceId())
                        .setCompanyId(companyId)
                        .setBusinessId(appointmentDTO.getBusinessId())
                        .setCustomerId(customerId)
                        .addAllItems(cartItems)
                        .setAppliedPromotions(PreviewCreateOrderRequest.AppliedPromotions.newBuilder()
                                .addAllPromotions(appliedPromotions)
                                .build())
                        .build())
                .getOrder();

        // 在整个 Invoice 的角度，Deposit 的钱也是 Total 的一部分.
        // 因此在这里将 Preview 出来的 Order 里面的 DepositAmount 加回 Total.
        previewOrderDetail = OrderDetailModelV1.newBuilder(previewOrderDetail)
                .setOrder(OrderModelV1.newBuilder(previewOrderDetail.getOrder())
                        .setTotalAmount(MoneyConvertor.toMoney(
                                MoneyConvertor.toBigDecimal(
                                                previewOrderDetail.getOrder().getTotalAmount())
                                        .add(MoneyConvertor.toBigDecimal(
                                                previewOrderDetail.getOrder().getDepositAmount())),
                                previewOrderDetail.getOrder().getCurrencyCode())))
                .build();

        responseObserver.onNext(GetEstimateResult.newBuilder()
                .setEstimate(orderService.convertOrderDetailToView(previewOrderDetail))
                .setDeposit(orderService.convertOrderDetailToView(deposit))
                .setAppointment(getAppointmentBriefForInvoice(appointmentDTO))
                .setCustomer(buildCustomerBriefForInvoice(customer, companyId, staffId))
                .addAllOrderItemExtras(buildOrderItemExtras(
                        previewOrderDetail.getOrderItemsList(),
                        AppointmentServiceConvertor.buildUUIDToServiceItem(
                                previewOrderLineItemsResponse.getPetServicesList()),
                        Map.of()))
                .build());
        responseObserver.onCompleted();
    }

    private List<GetInvoiceResult.OrderItemExtra> buildOrderItemExtras(
            List<OrderItemModel> orderItems,
            Map<String, ServiceItem> externalIDToServiceItem,
            Map<Long, Boolean> orderItemIDToPaid) {
        return orderItems.stream()
                .map(it -> {
                    ServiceItem serviceItem = Optional.ofNullable(externalIDToServiceItem.get(it.getExternalUuid()))
                            .orElse(ServiceItem.newBuilder().getDefaultInstanceForType());

                    return GetInvoiceResult.OrderItemExtra.newBuilder()
                            .setOrderItemId(it.getId())
                            .setOrderId(it.getOrderId())
                            .setIsPaid(orderItemIDToPaid.getOrDefault(it.getId(), false))
                            .setServiceItemType(serviceItem.getServiceItemType())
                            .setServicePriceUnit(serviceItem.getPriceUnit())
                            .build();
                })
                .toList();
    }

    private List<GetInvoiceResult.OrderItemExtra> buildOrderItemExtrasForNonAppointmentInvoice(
            List<OrderItemModel> orderItems, Map<Long, Boolean> orderItemIDToPaid) {
        return orderItems.stream()
                .map(it -> GetInvoiceResult.OrderItemExtra.newBuilder()
                        .setOrderItemId(it.getId())
                        .setOrderId(it.getOrderId())
                        .setIsPaid(orderItemIDToPaid.getOrDefault(it.getId(), false))
                        .build())
                .toList();
    }

    private GetInvoiceResult.AppointmentBrief getAppointmentBriefForInvoice(AppointmentDTO appointmentDTO) {
        return GetInvoiceResult.AppointmentBrief.newBuilder()
                .setId(appointmentDTO.getId())
                .setStatus(AppointmentStatus.forNumber(appointmentDTO.getStatus()))
                .setAppointmentDate(appointmentDTO.getAppointmentDate())
                .setAppointmentEndDate(appointmentDTO.getAppointmentEndDate())
                .setAppointmentStartTime(appointmentDTO.getAppointmentStartTime())
                .setAppointmentEndTime(appointmentDTO.getAppointmentEndTime())
                .setCreateTime(appointmentDTO.getCreateTime())
                .build();
    }

    private GetInvoiceResult.CustomerBrief buildCustomerBriefForInvoice(
            BusinessCustomerInfoModel customer, Long companyId, Long staffId) {
        GetInvoiceResult.CustomerBrief.Builder builder = GetInvoiceResult.CustomerBrief.newBuilder()
                .setId(customer.getId())
                .setFirstName(customer.getFirstName())
                .setLastName(customer.getLastName());

        String email = customer.getEmail();
        String phoneNumber = customer.getPhoneNumber();

        if (!canStaffAccessEmailAndPhoneNumber(companyId, staffId)) {
            email = PermissionUtil.emailConfusion(email);
            phoneNumber = PermissionUtil.phoneNumberConfusion(phoneNumber);
        }

        return builder.setEmail(email).setPhoneNumber(phoneNumber).build();
    }

    private boolean canStaffAccessEmailAndPhoneNumber(Long companyId, Long staffId) {
        return permissionHelper.hasPermission(companyId, staffId, PermissionEnums.ACCESS_CLIENT_EMAIL_AND_PHONE_NUMBER);
    }

    private Map<String, Money> buildPreDiscountSubtotalByItemType(List<OrderItemModel> orderItems) {
        Map<String, Money> subtotalByType = new HashMap<>();
        orderItems.stream()
                .collect(Collectors.groupingBy(OrderItemModel::getType))
                .forEach((itemType, itemList) -> {
                    if (itemList == null || itemList.isEmpty()) {
                        return;
                    }

                    OrderItemModel firstItem = itemList.get(0);
                    BigDecimal subTotal = BigDecimal.ZERO;
                    for (OrderItemModel item : itemList) {
                        subTotal = subTotal.add(MoneyConvertor.toBigDecimal(item.getSubTotalAmount()));
                    }
                    subtotalByType.put(itemType, MoneyConvertor.toMoney(subTotal, firstItem.getCurrencyCode()));
                });

        if (subtotalByType.isEmpty()) {
            return Map.of();
        }

        String currencyCode = orderItems.get(0).getCurrencyCode();
        // 汇总一个.
        subtotalByType.put(
                "all",
                MoneyConvertor.toMoney(
                        orderItems.stream()
                                .map(item -> MoneyUtil.moneyToBigDecimal(item.getSubTotalAmount()))
                                .reduce(BigDecimal.ZERO, BigDecimal::add),
                        currencyCode));

        return subtotalByType;
    }

    private Map<String, Money> buildRefundSubtotalByItemType(List<RefundOrderItemModel> orderItems) {
        Map<String, Money> subtotalByType = new HashMap<>();
        orderItems.stream()
                .collect(Collectors.groupingBy(RefundOrderItemModel::getItemType))
                .forEach((itemType, itemList) -> {
                    if (itemList == null || itemList.isEmpty()) {
                        return;
                    }

                    RefundOrderItemModel firstItem = itemList.get(0);
                    BigDecimal subTotal = BigDecimal.ZERO;
                    for (RefundOrderItemModel item : itemList) {
                        subTotal = subTotal.add(MoneyConvertor.toBigDecimal(item.getRefundTotalAmount()));
                    }
                    subtotalByType.put(itemType, MoneyConvertor.toMoney(subTotal, firstItem.getCurrencyCode()));
                });

        if (subtotalByType.isEmpty()) {
            return Map.of();
        }

        String currencyCode = orderItems.get(0).getCurrencyCode();
        // 汇总一个.
        subtotalByType.put(
                "all",
                MoneyConvertor.toMoney(
                        orderItems.stream()
                                .map(item -> MoneyUtil.moneyToBigDecimal(item.getRefundTotalAmount()))
                                .reduce(BigDecimal.ZERO, BigDecimal::add),
                        currencyCode));

        return subtotalByType;
    }

    private Map<String, Money> buildSubtotalByItemType(List<OrderItemModel> orderItems) {
        Map<String, Money> subtotalByType = new HashMap<>();
        orderItems.stream()
                .collect(Collectors.groupingBy(OrderItemModel::getType))
                .forEach((itemType, itemList) -> {
                    if (itemList == null || itemList.isEmpty()) {
                        return;
                    }

                    OrderItemModel firstItem = itemList.get(0);
                    BigDecimal subTotal = BigDecimal.ZERO;
                    for (OrderItemModel item : itemList) {
                        subTotal = subTotal.add(MoneyConvertor.toBigDecimal(item.getTotalAmount()));
                    }
                    subtotalByType.put(itemType, MoneyConvertor.toMoney(subTotal, firstItem.getCurrencyCode()));
                });

        if (subtotalByType.isEmpty()) {
            return Map.of();
        }

        String currencyCode = orderItems.get(0).getCurrencyCode();
        // 汇总一个.
        subtotalByType.put(
                "all",
                MoneyConvertor.toMoney(
                        orderItems.stream()
                                .map(item -> MoneyUtil.moneyToBigDecimal(item.getTotalAmount()))
                                .reduce(BigDecimal.ZERO, BigDecimal::add),
                        currencyCode));

        return subtotalByType;
    }

    private List<OrderDetailView.PetBrief> batchGetPetBriefList(List<Integer> petIdList) {
        if (petIdList.isEmpty()) {
            return List.of();
        }
        return petClient.getCustomerPetListByIdList(petIdList).stream()
                .map(it -> OrderDetailView.PetBrief.newBuilder()
                        .setId(it.getPetId())
                        .setBreed(it.getBreed())
                        .setName(it.getPetName())
                        .build())
                .toList();
    }

    private List<OrderDetailView.StaffBrief> batchGetStaffBriefList(int businessId, List<Integer> staffIdList) {
        if (staffIdList.isEmpty()) {
            return List.of();
        }

        StaffIdListParams staffIdListParams = new StaffIdListParams(businessId, staffIdList);
        return iBusinessStaffClient.getStaffList(staffIdListParams).stream()
                .map(it -> OrderDetailView.StaffBrief.newBuilder()
                        .setId(it.getId())
                        .setFirstName(it.getFirstName())
                        .setLastName(it.getLastName())
                        .build())
                .toList();
    }
}
