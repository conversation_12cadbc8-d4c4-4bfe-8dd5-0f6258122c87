package com.moego.api.v3.order.controller;

import com.moego.api.v3.order.convertor.ServiceChargeConvertor;
import com.moego.idl.api.order.v1.AddServiceChargeRequest;
import com.moego.idl.api.order.v1.DeleteServiceChargeRequest;
import com.moego.idl.api.order.v1.GetServiceChargeListRequest;
import com.moego.idl.api.order.v1.GetServiceChargeListResponse;
import com.moego.idl.api.order.v1.OperateServiceChargeResponse;
import com.moego.idl.api.order.v1.ServiceChargeServiceGrpc.ServiceChargeServiceImplBase;
import com.moego.idl.api.order.v1.SortServiceChargeRequest;
import com.moego.idl.api.order.v1.UpdateServiceChargeRequest;
import com.moego.idl.models.order.v1.ServiceCharge;
import com.moego.idl.service.appointment.v1.ServiceChargeDetailServiceGrpc;
import com.moego.idl.service.appointment.v1.UpdateUpcomingServiceChargeDetailsRequest;
import com.moego.idl.service.order.v1.AddServiceChargeInput;
import com.moego.idl.service.order.v1.DeleteServiceChargeInput;
import com.moego.idl.service.order.v1.GetServiceChargeListInput;
import com.moego.idl.service.order.v1.GetServiceChargeListOutput;
import com.moego.idl.service.order.v1.OperateServiceChargeOutput;
import com.moego.idl.service.order.v1.ServiceChargeServiceGrpc.ServiceChargeServiceBlockingStub;
import com.moego.idl.service.order.v1.SortServiceChargeInput;
import com.moego.idl.service.order.v1.UpdateServiceChargeInput;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class ServiceChargeServer extends ServiceChargeServiceImplBase {

    private final ServiceChargeServiceBlockingStub serviceChargeClient;
    private final ServiceChargeDetailServiceGrpc.ServiceChargeDetailServiceBlockingStub serviceChargeDetailClient;

    @Override
    @Auth(AuthType.BUSINESS)
    public void getServiceChargeList(
            GetServiceChargeListRequest request, StreamObserver<GetServiceChargeListResponse> responseObserver) {
        GetServiceChargeListInput input =
                ServiceChargeConvertor.INSTANCE.toGetServiceChargeListInput(request).toBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(AuthContext.get().businessId())
                        .build();
        GetServiceChargeListOutput output = serviceChargeClient.getServiceChargeList(input);
        GetServiceChargeListResponse response = GetServiceChargeListResponse.newBuilder()
                .addAllServiceCharge(output.getServiceChargeList())
                .build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void addServiceCharge(AddServiceChargeRequest request, StreamObserver<ServiceCharge> responseObserver) {
        AddServiceChargeInput input = ServiceChargeConvertor.INSTANCE.toAddServiceChargeInput(request).toBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .setOperatorId(AuthContext.get().staffId())
                .build();

        ServiceCharge response = serviceChargeClient.addServiceCharge(input);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void updateServiceCharge(
            UpdateServiceChargeRequest request, StreamObserver<ServiceCharge> responseObserver) {
        UpdateServiceChargeInput input = ServiceChargeConvertor.INSTANCE.toUpdateServiceChargeInput(request).toBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .setOperatorId(AuthContext.get().staffId())
                .build();
        ServiceCharge response = serviceChargeClient.updateServiceCharge(input);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void sortServiceCharge(
            SortServiceChargeRequest request, StreamObserver<OperateServiceChargeResponse> responseObserver) {
        SortServiceChargeInput input = ServiceChargeConvertor.INSTANCE.toSortServiceChargeInput(request).toBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .setOperatorId(AuthContext.get().staffId())
                .build();
        OperateServiceChargeOutput output = serviceChargeClient.sortServiceCharge(input);
        OperateServiceChargeResponse response = OperateServiceChargeResponse.newBuilder()
                .setResult(output.getResult())
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void deleteServiceCharge(
            DeleteServiceChargeRequest request, StreamObserver<OperateServiceChargeResponse> responseObserver) {
        DeleteServiceChargeInput input = ServiceChargeConvertor.INSTANCE.toDeleteServiceChargeInput(request).toBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .setOperatorId(AuthContext.get().staffId())
                .build();
        OperateServiceChargeOutput output = serviceChargeClient.deleteServiceCharge(input);
        OperateServiceChargeResponse response = OperateServiceChargeResponse.newBuilder()
                .setResult(output.getResult())
                .build();
        if (request.getApplyUpcomingAppt()) {
            ThreadPool.execute(() -> serviceChargeDetailClient.updateUpcomingServiceChargeDetails(
                    UpdateUpcomingServiceChargeDetailsRequest.newBuilder()
                            .setActionType(UpdateUpcomingServiceChargeDetailsRequest.ActionType.DELETE)
                            .setServiceChargeId(request.getId())
                            .setCompanyId(AuthContext.get().companyId())
                            .build()));
        }
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }
}
