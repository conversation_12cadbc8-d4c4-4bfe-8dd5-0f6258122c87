package com.moego.api.v3.order.controller;

import com.moego.api.v3.order.convertor.ServiceChargeConvertor;
import com.moego.idl.api.order.v1.AddCompanyServiceChargeParams;
import com.moego.idl.api.order.v1.GetCompanyServiceChargeListParams;
import com.moego.idl.api.order.v1.GetCompanyServiceChargeListResult;
import com.moego.idl.api.order.v1.ListSurchargeAssociatedFoodSourceParams;
import com.moego.idl.api.order.v1.ListSurchargeAssociatedFoodSourceResult;
import com.moego.idl.api.order.v1.ServiceChargeCompanyServiceGrpc;
import com.moego.idl.api.order.v1.UpdateCompanyServiceChargeParams;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.order.v1.ServiceCharge;
import com.moego.idl.models.order.v1.SurchargeType;
import com.moego.idl.service.appointment.v1.ServiceChargeDetailServiceGrpc;
import com.moego.idl.service.appointment.v1.UpdateUpcomingServiceChargeDetailsRequest;
import com.moego.idl.service.order.v1.GetCompanyServiceChargeListRequest;
import com.moego.idl.service.order.v1.ListSurchargeAssociatedFoodSourceRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import io.grpc.stub.StreamObserver;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.util.CollectionUtils;

@GrpcService
@AllArgsConstructor
public class ServiceChargeCompanyServer extends ServiceChargeCompanyServiceGrpc.ServiceChargeCompanyServiceImplBase {

    private final com.moego.idl.service.order.v1.ServiceChargeCompanyServiceGrpc.ServiceChargeCompanyServiceBlockingStub
            serviceChargeCompanyClient;

    private final ServiceChargeDetailServiceGrpc.ServiceChargeDetailServiceBlockingStub serviceChargeDetailClient;

    @Override
    @Auth(AuthType.COMPANY)
    public void getCompanyServiceChargeList(
            GetCompanyServiceChargeListParams request,
            StreamObserver<GetCompanyServiceChargeListResult> responseObserver) {
        var builder = GetCompanyServiceChargeListRequest.newBuilder();
        builder.setCompanyId(AuthContext.get().companyId());
        if (request.hasIsActive()) {
            builder.setIsActive(request.getIsActive());
        }
        if (request.hasSurchargeType()) {
            builder.setSurchargeType(request.getSurchargeType());
        }
        if (!CollectionUtils.isEmpty(request.getBusinessIdsList())) {
            builder.addAllBusinessIds(request.getBusinessIdsList());
        }
        var response = serviceChargeCompanyClient.getCompanyServiceChargeList(builder.build());
        responseObserver.onNext(GetCompanyServiceChargeListResult.newBuilder()
                .addAllServiceCharge(response.getServiceChargeList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void addCompanyServiceCharge(
            AddCompanyServiceChargeParams request, StreamObserver<ServiceCharge> responseObserver) {
        var clientRequest = ServiceChargeConvertor.INSTANCE.toAddCompanyServiceChargeInput(request).toBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setOperatorId(AuthContext.get().staffId())
                .build();
        if (!clientRequest.hasSurchargeType()) {
            clientRequest = clientRequest.toBuilder()
                    .setSurchargeType(SurchargeType.CUSTOM_FEE)
                    .build();
        }
        var response = serviceChargeCompanyClient.addCompanyServiceCharge(clientRequest);
        if (request.getApplyUpcomingAppt()) {
            ThreadPool.execute(() -> serviceChargeDetailClient.updateUpcomingServiceChargeDetails(
                    UpdateUpcomingServiceChargeDetailsRequest.newBuilder()
                            .setActionType(UpdateUpcomingServiceChargeDetailsRequest.ActionType.ADD)
                            .setServiceChargeId(response.getId())
                            .setCompanyId(AuthContext.get().companyId())
                            .build()));
        }
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateCompanyServiceCharge(
            UpdateCompanyServiceChargeParams request, StreamObserver<ServiceCharge> responseObserver) {
        var oldServiceCharge = serviceChargeCompanyClient
                .getCompanyServiceChargeList(GetCompanyServiceChargeListRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .build())
                .getServiceChargeList()
                .stream()
                .filter(s -> Objects.equals(s.getId(), request.getId()))
                .findFirst()
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Custom fee not found."));

        var clientRequest =
                ServiceChargeConvertor.INSTANCE.toUpdateCompanyServiceChargeCompanyInput(request).toBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setOperatorId(AuthContext.get().staffId())
                        .build();
        var response = serviceChargeCompanyClient.updateCompanyServiceCharge(clientRequest);
        if (request.getApplyUpcomingAppt()) {
            ThreadPool.execute(() -> serviceChargeDetailClient.updateUpcomingServiceChargeDetails(
                    UpdateUpcomingServiceChargeDetailsRequest.newBuilder()
                            .setActionType(UpdateUpcomingServiceChargeDetailsRequest.ActionType.UPDATE)
                            .setServiceChargeId(request.getId())
                            .setOldServiceCharge(oldServiceCharge)
                            .setCompanyId(AuthContext.get().companyId())
                            .build()));
        }
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listSurchargeAssociatedFoodSource(
            ListSurchargeAssociatedFoodSourceParams request,
            StreamObserver<ListSurchargeAssociatedFoodSourceResult> responseObserver) {
        ListSurchargeAssociatedFoodSourceRequest.Builder builder = ListSurchargeAssociatedFoodSourceRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId());

        if (request.hasExistServiceChargeId()) {
            builder.setExistServiceChargeId(request.getExistServiceChargeId());
        }

        responseObserver.onNext(ListSurchargeAssociatedFoodSourceResult.newBuilder()
                .addAllFoodSourceIds(serviceChargeCompanyClient
                        .listSurchargeAssociatedFoodSource(builder.build())
                        .getFoodSourceIdsList())
                .build());
        responseObserver.onCompleted();
    }
}
