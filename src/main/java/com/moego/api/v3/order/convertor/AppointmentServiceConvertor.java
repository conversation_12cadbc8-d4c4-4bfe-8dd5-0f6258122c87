package com.moego.api.v3.order.convertor;

import com.google.type.Decimal;
import com.google.type.Money;
import com.moego.idl.models.fulfillment.v1.LineItem;
import com.moego.idl.models.fulfillment.v1.PetService;
import com.moego.idl.models.fulfillment.v1.ServiceItem;
import com.moego.idl.models.fulfillment.v1.SurchargeItem;
import com.moego.idl.models.order.v1.ItemType;
import com.moego.idl.models.order.v1.PriceDetailModel;
import com.moego.idl.models.organization.v1.TaxRuleModel;
import com.moego.idl.service.order.v2.PreviewCreateOrderRequest;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class AppointmentServiceConvertor {
    public static List<PreviewCreateOrderRequest.CartItem> toCartItems(PetService petService) {
        Long petId = petService.getPet().getId();
        return petService.getServicesList().stream()
                .map(it -> toCartItem(petId, it))
                .toList();
    }

    public static PreviewCreateOrderRequest.CartItem toCartItem(SurchargeItem surchargeItem) {
        return PreviewCreateOrderRequest.CartItem.newBuilder()
                .setItemType(ItemType.ITEM_TYPE_SERVICE_CHARGE)
                .setItemId(surchargeItem.getServiceChargeId())
                .setExternalUuid(surchargeItem.getExternalId())
                .setDescription(surchargeItem.getDescription())
                .setName(surchargeItem.getName())
                .setUnitPrice(surchargeItem.getUnitPrice())
                .setQuantity(surchargeItem.getQuantity())
                .setTax(parseTax(surchargeItem.getTax()))
                .build();
    }

    public static PreviewCreateOrderRequest.CartItem toCartItem(Long petId, ServiceItem serviceItem) {
        ItemType itemType =
                switch (serviceItem.getServiceItemType()) {
                    case GROOMING, GROUP_CLASS, DOG_WALKING, BOARDING, DAYCARE -> ItemType.ITEM_TYPE_SERVICE;
                    case EVALUATION -> ItemType.ITEM_TYPE_EVALUATION_SERVICE;
                    default -> ItemType.ITEM_TYPE_UNSPECIFIED;
                };

        return PreviewCreateOrderRequest.CartItem.newBuilder()
                .setItemType(itemType)
                .setItemId(serviceItem.getServiceId())
                .setExternalUuid(serviceItem.getExternalId())
                .setDescription(serviceItem.getDescription())
                .setName(serviceItem.getName())
                .setStaffId(serviceItem.getStaffId())
                .setPetId(petId)
                .setUnitPrice(serviceItem.getUnitPrice())
                .setSubtotalDetail(
                        PriceDetailModel.newBuilder().addAllPriceItems(parsePriceItems(serviceItem.getLineItemsList())))
                .setQuantity(serviceItem.getQuantity())
                .setTax(parseTax(serviceItem.getTax()))
                .build();
    }

    private static PreviewCreateOrderRequest.Tax parseTax(TaxRuleModel tax) {
        return PreviewCreateOrderRequest.Tax.newBuilder()
                .setId(tax.getId())
                .setName(tax.getName())
                .setRate(Decimal.newBuilder()
                        .setValue(BigDecimal.valueOf(tax.getRate()).toString())
                        .build())
                .build();
    }

    private static List<PriceDetailModel.PriceItem> parsePriceItems(List<LineItem> servicePriceItems) {
        return servicePriceItems.stream()
                .map(it -> {
                    PriceDetailModel.PriceItem.Operator operator = PriceDetailModel.PriceItem.Operator.ADD;
                    Money unitPrice = it.getUnitPrice();

                    if (MoneyConvertor.toBigDecimal(it.getUnitPrice()).compareTo(BigDecimal.ZERO) < 0) {
                        operator = PriceDetailModel.PriceItem.Operator.SUBTRACT;
                        unitPrice = MoneyConvertor.toMoney(
                                MoneyConvertor.toBigDecimal(it.getUnitPrice()).abs(), unitPrice.getCurrencyCode());
                    }

                    return PriceDetailModel.PriceItem.newBuilder()
                            .setName(it.getItemName())
                            .setUnitPrice(unitPrice)
                            .setQuantity(it.getQuantity())
                            .setOperator(operator)
                            .build();
                })
                .toList();
    }

    public static Map<String, ServiceItem> buildUUIDToServiceItem(List<PetService> petServices) {
        Map<String, ServiceItem> externalIDToServiceItem = new HashMap<>();

        petServices.forEach(ps -> {
            ps.getServicesList().forEach(service -> {
                externalIDToServiceItem.put(service.getExternalId(), service);
            });
        });

        return externalIDToServiceItem;
    }
}
