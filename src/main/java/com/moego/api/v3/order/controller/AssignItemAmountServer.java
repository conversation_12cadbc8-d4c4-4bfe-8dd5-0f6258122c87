package com.moego.api.v3.order.controller;

import com.moego.idl.api.order.v1.AssignItemAmountApiServiceGrpc.AssignItemAmountApiServiceImplBase;
import com.moego.idl.api.order.v1.AssignItemPaidAmountParams;
import com.moego.idl.api.order.v1.AssignItemPaidAmountResult;
import com.moego.idl.api.order.v1.GetAssignedItemPaidAmountParams;
import com.moego.idl.api.order.v1.GetAssignedItemPaidAmountResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyIdRequest;
import com.moego.idl.service.temp_order.v1.AssignItemAmountServiceGrpc;
import com.moego.idl.service.temp_order.v1.AssignItemPaidAmountRequest;
import com.moego.idl.service.temp_order.v1.AssignItemPaidAmountResponse;
import com.moego.idl.service.temp_order.v1.GetAssignedItemPaidAmountRequest;
import com.moego.idl.service.temp_order.v1.GetAssignedItemPaidAmountResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class AssignItemAmountServer extends AssignItemAmountApiServiceImplBase {

    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceStub;
    private AssignItemAmountServiceGrpc.AssignItemAmountServiceBlockingStub assignItemAmountServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void assignItemPaidAmount(
            AssignItemPaidAmountParams request, StreamObserver<AssignItemPaidAmountResult> responseObserver) {

        checkBusinessId(request.getBusinessId());
        AssignItemPaidAmountRequest params = AssignItemPaidAmountRequest.newBuilder()
                .setBusinessId(request.getBusinessId())
                .setOrderId(request.getOrderId())
                .addAllItems(request.getItemsList())
                .build();

        AssignItemPaidAmountResponse response = assignItemAmountServiceBlockingStub.assignItemPaidAmount(params);

        responseObserver.onNext(AssignItemPaidAmountResult.newBuilder()
                .setSuccess(response.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    private void checkBusinessId(long business_id) {
        Integer tokenCompanyId = AuthContext.get().getCompanyId();
        if (tokenCompanyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id is null");
        }
        long actualCompanyId = businessServiceStub
                .getCompanyId(GetCompanyIdRequest.newBuilder()
                        .setBusinessId(business_id)
                        .build())
                .getCompanyId();
        if (tokenCompanyId.longValue() != actualCompanyId) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "company id not matched");
        }
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getAssignedItemPaidAmount(
            GetAssignedItemPaidAmountParams request, StreamObserver<GetAssignedItemPaidAmountResult> responseObserver) {

        checkBusinessId(request.getBusinessId());
        GetAssignedItemPaidAmountRequest params = GetAssignedItemPaidAmountRequest.newBuilder()
                .setBusinessId(request.getBusinessId())
                .setOrderId(request.getOrderId())
                .build();
        GetAssignedItemPaidAmountResponse response =
                assignItemAmountServiceBlockingStub.getAssignedItemPaidAmount(params);
        responseObserver.onNext(GetAssignedItemPaidAmountResult.newBuilder()
                .addAllItems(response.getItemsList())
                .setOrderId(response.getOrderId())
                .build());
        responseObserver.onCompleted();
    }
}
