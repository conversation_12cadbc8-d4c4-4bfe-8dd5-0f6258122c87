package com.moego.api.v3.order.convertor;

import com.google.type.Money;
import java.math.BigDecimal;
import java.math.RoundingMode;

public class MoneyConvertor {
    // 保留的小数位数.
    static final int MONEY_SCALE = 2;
    // nanos 的精度， 由 google.type.Money 规定为 9.
    static final int NANO_PRECISION = 9;

    // toMoney 转换 BigDecimal 为 Money.
    // 保留 2 位精度.
    public static Money toMoney(BigDecimal amount, String currencyCode) {
        // 取整数部分构造 units.
        BigDecimal units = amount.setScale(0, RoundingMode.DOWN);
        // 取小数部分构造 nanos
        BigDecimal nanos = amount.subtract(units)
                .setScale(MONEY_SCALE, RoundingMode.HALF_UP)
                .movePointRight(NANO_PRECISION);

        return Money.newBuilder()
                .setCurrencyCode(currencyCode)
                .setUnits(units.longValue())
                .setNanos(nanos.intValue())
                .build();
    }

    // toBigDecimal 转换 Money 为 BigDecimal.
    // 保留 2 位精度，丢弃 currencyCode 信息.
    public static BigDecimal toBigDecimal(Money money) {
        // 取 units 构造整数部分.
        BigDecimal units = BigDecimal.valueOf(money.getUnits());
        // 取 nanos 构造小数部分.
        BigDecimal nanos = BigDecimal.valueOf(money.getNanos(), NANO_PRECISION);

        return units.add(nanos);
    }
}
