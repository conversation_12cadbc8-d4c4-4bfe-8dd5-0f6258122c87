package com.moego.api.v3.order.controller;

import com.moego.api.v3.order.convertor.DiscountCodeConvertor;
import com.moego.idl.api.order.v1.GetAvailableDiscountListRequest;
import com.moego.idl.api.order.v1.GetAvailableDiscountListResponse;
import com.moego.idl.api.order.v1.ListApplicableLineItemsParams;
import com.moego.idl.api.order.v1.ListApplicableLineItemsResult;
import com.moego.idl.api.order.v1.OrderDiscountCodeServiceGrpc.OrderDiscountCodeServiceImplBase;
import com.moego.idl.service.enterprise.v1.ListApplicableLineItemsRequest;
import com.moego.idl.service.enterprise.v1.ListApplicableLineItemsResponse;
import com.moego.idl.service.enterprise.v1.SpecificSupportServiceGrpc;
import com.moego.idl.service.order.v1.GetAvailableDiscountListInput;
import com.moego.idl.service.order.v1.GetAvailableDiscountListOutput;
import com.moego.idl.service.order.v1.OrderDiscountCodeServiceGrpc.OrderDiscountCodeServiceBlockingStub;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
public class OrderDiscountCodeServer extends OrderDiscountCodeServiceImplBase {

    @Resource
    private OrderDiscountCodeServiceBlockingStub discountCodeClient;

    @Resource
    private SpecificSupportServiceGrpc.SpecificSupportServiceBlockingStub specificSupportService;

    @Override
    @Auth(AuthType.COMPANY)
    public void getAvailableDiscountList(
            GetAvailableDiscountListRequest request,
            StreamObserver<GetAvailableDiscountListResponse> responseObserver) {
        GetAvailableDiscountListInput input = DiscountCodeConvertor.INSTANCE.toInput(request).toBuilder()
                .setBusinessId(AuthContext.get().businessId())
                .setCompanyId(AuthContext.get().companyId())
                .build();

        GetAvailableDiscountListOutput output = discountCodeClient.getAvailableDiscountList(input);
        responseObserver.onNext(DiscountCodeConvertor.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listApplicableLineItems(
            ListApplicableLineItemsParams params, StreamObserver<ListApplicableLineItemsResult> responseObserver) {
        ListApplicableLineItemsRequest request = DiscountCodeConvertor.INSTANCE.toRequest(params).toBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .build();
        ListApplicableLineItemsResponse response = specificSupportService.listApplicableLineItems(request);
        responseObserver.onNext(DiscountCodeConvertor.INSTANCE.toResult(response));
        responseObserver.onCompleted();
    }
}
