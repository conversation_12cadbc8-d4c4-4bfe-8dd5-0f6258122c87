package com.moego.api.v3.order.controller;

import com.moego.api.v3.order.convertor.SplitTipsConvertor;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.api.order.v1.ClearTipsSplitChangedStatusParams;
import com.moego.idl.api.order.v1.ClearTipsSplitChangedStatusResult;
import com.moego.idl.api.order.v1.CustomizedTipConfigRequest;
import com.moego.idl.api.order.v1.EditStaffAndTipsSplitParams;
import com.moego.idl.api.order.v1.EditTipsSplitResult;
import com.moego.idl.api.order.v1.GetTipsSplitChangedStatusParams;
import com.moego.idl.api.order.v1.GetTipsSplitChangedStatusResult;
import com.moego.idl.api.order.v1.GetTipsSplitParams;
import com.moego.idl.api.order.v1.GetTipsSplitResult;
import com.moego.idl.api.order.v1.PreviewEditTipsSplitResult;
import com.moego.idl.api.order.v1.SaveSplitTipsRequest;
import com.moego.idl.api.order.v1.SaveSplitTipsResponse;
import com.moego.idl.api.order.v1.SplitTipsServiceGrpc.SplitTipsServiceImplBase;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.order.v1.OrderDetailView;
import com.moego.idl.models.order.v1.TipsSplitDetailModel;
import com.moego.idl.models.order.v1.TipsSplitDetailView;
import com.moego.idl.models.order.v1.TipsSplitMode;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.service.order.v1.GetOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.order.v2.EditStaffAndTipsSplitRequest;
import com.moego.idl.service.order.v2.EditTipsSplitResponse;
import com.moego.idl.service.order.v2.GetTipsSplitChangedStatusResponse;
import com.moego.idl.service.order.v2.GetTipsSplitRequest;
import com.moego.idl.service.order.v2.GetTipsSplitResponse;
import com.moego.idl.service.order.v2.PreviewEditTipsSplitResponse;
import com.moego.idl.service.order.v2.SplitTipsServiceGrpc;
import com.moego.idl.service.organization.v1.QueryStaffByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.api.IGroomingInvoiceService;
import com.moego.server.grooming.dto.AmountPercentagePair;
import com.moego.server.grooming.params.SaveTipSplitParams;
import io.grpc.stub.StreamObserver;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.util.CollectionUtils;

@GrpcService
@AllArgsConstructor
public class SplitTipsServer extends SplitTipsServiceImplBase {

    private final OrderServiceGrpc.OrderServiceBlockingStub orderClient;

    private final IGroomingInvoiceService groomingInvoiceService;

    private final SplitTipsServiceGrpc.SplitTipsServiceBlockingStub splitTipsClient;

    private final StaffServiceGrpc.StaffServiceBlockingStub staffService;

    @Override
    @Auth(AuthType.BUSINESS)
    public void saveTipSplit(SaveSplitTipsRequest request, StreamObserver<SaveSplitTipsResponse> responseObserver) {
        var existingOrder = orderClient.getOrder(
                GetOrderRequest.newBuilder().setId(request.getOrderId()).build());
        if (existingOrder == null) {
            throw ExceptionUtil.bizException(Code.CODE_INVOICE_NOT_FOUND, "invalid order id");
        }
        // check if this order has appt level split config
        GetTipsSplitResponse splitConfig = splitTipsClient.getTipsSplitDetails(GetTipsSplitRequest.newBuilder()
                .setSourceId(existingOrder.getSourceId())
                .setSourceType(com.moego.idl.models.order.v1.OrderSourceType.APPOINTMENT)
                .build());

        if (TipsSplitMode.TIPS_SPLIT_MODE_APPT.equals(splitConfig.getTipsSplitMode())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_FINTECH_BUSINESS_APP_UPDATE_CLOSABLE, "please upgrade to new version");
        }

        SaveTipSplitParams params = new SaveTipSplitParams();
        params.setBusinessId(AuthContext.get().getBusinessId());
        params.setOrderId(request.getOrderId());
        params.setSplitMethod(request.getSplitMethod().getNumber());
        params.setApplyBy(AuthContext.get().staffId());

        params.setBusinessTipAmountEffective(request.hasBusinessTipAmount());
        if (request.hasBusinessTipAmount()) {
            params.setBusinessTipAmount(
                    new BigDecimal(request.getBusinessTipAmount().getValue()));
        }

        if (!CollectionUtils.isEmpty(request.getCustomizedConfigList())) {
            Map<Integer, AmountPercentagePair> staffTipsMap = new HashMap<>();
            for (CustomizedTipConfigRequest tipConfig : request.getCustomizedConfigList()) {
                AmountPercentagePair pair = new AmountPercentagePair();
                pair.setAmount(BigDecimal.valueOf(tipConfig.getAmount()).setScale(2, RoundingMode.HALF_UP));
                pair.setPercentage(tipConfig.getPercentage());
                staffTipsMap.put((int) tipConfig.getStaffId(), pair);
            }
            params.setStaffAmountMap(staffTipsMap);
        }
        if (request.hasCustomizedType()) {
            params.setCustomizedType(request.getCustomizedType().getNumber());
        }

        responseObserver.onNext(SaveSplitTipsResponse.newBuilder()
                .setSuccess(groomingInvoiceService.saveTipSplitRecord(params))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getTipsSplitDetails(GetTipsSplitParams params, StreamObserver<GetTipsSplitResult> responseObserver) {
        GetTipsSplitResponse getTipsSplitResponse = splitTipsClient.getTipsSplitDetails(GetTipsSplitRequest.newBuilder()
                .setSourceId(params.getSourceId())
                .setSourceType(params.getSourceType())
                .build());
        // check company id
        if (!AuthContext.get()
                .companyId()
                .equals(getTipsSplitResponse.getTipsSplit().getCompanyId())) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "company id mismatch");
        }
        // check business id
        if (params.getBusinessId() != getTipsSplitResponse.getTipsSplit().getBusinessId()) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "business id mismatch");
        }

        responseObserver.onNext(GetTipsSplitResult.newBuilder()
                .setTipsSplit(getTipsSplitResponse.getTipsSplit())
                .addAllTipsSplitDetail(buildTipsSplitDetailView(getTipsSplitResponse.getTipsSplitDetailsList()))
                .addAllOrders(getTipsSplitResponse.getOrdersList())
                .addAllRefundOrders(getTipsSplitResponse.getRefundOrdersList())
                .build());
        responseObserver.onCompleted();
    }

    private List<TipsSplitDetailView> buildTipsSplitDetailView(List<TipsSplitDetailModel> detailModels) {
        // collect all staff id and get staff names
        Set<Long> staffIds = detailModels.stream()
                .map(TipsSplitDetailModel::getStaffId)
                .filter(CommonUtil::isNormal)
                .collect(Collectors.toSet());
        Map<Long, StaffModel> staffModelMap = staffService
                .queryStaffByIds(QueryStaffByIdsRequest.newBuilder()
                        .addAllStaffIds(staffIds)
                        .build())
                .getStaffsList()
                .stream()
                .collect(Collectors.toMap(StaffModel::getId, Function.identity()));

        return detailModels.stream()
                .map(detail -> TipsSplitDetailView.newBuilder()
                        .setStaffBrief(OrderDetailView.StaffBrief.newBuilder()
                                .setId(detail.getStaffId())
                                .setFirstName(staffModelMap
                                        .getOrDefault(
                                                detail.getStaffId(),
                                                StaffModel.newBuilder().build())
                                        .getFirstName())
                                .setLastName(staffModelMap
                                        .getOrDefault(
                                                detail.getStaffId(),
                                                StaffModel.newBuilder().build())
                                        .getLastName())
                                .build())
                        .setTipsSplitDetail(detail)
                        .build())
                .toList();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void previewEditTipsSplit(
            EditStaffAndTipsSplitParams params, StreamObserver<PreviewEditTipsSplitResult> responseObserver) {
        PreviewEditTipsSplitResponse resp =
                splitTipsClient.previewEditTipsSplit(EditStaffAndTipsSplitRequest.newBuilder()
                        .setBusinessId(params.getBusinessId())
                        .setCompanyId(AuthContext.get().companyId())
                        .setApplyBy(AuthContext.get().staffId())
                        .setSourceId(params.getSourceId())
                        .setSourceType(params.getSourceType())
                        .addAllEditStaffs(params.getEditStaffsList())
                        .setTipsSplitConfig(params.getTipsSplitConfig())
                        .build());
        responseObserver.onNext(PreviewEditTipsSplitResult.newBuilder()
                .setSplitConfig(resp.getSplitConfig())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void editTipsSplit(
            EditStaffAndTipsSplitParams params, StreamObserver<EditTipsSplitResult> responseObserver) {
        EditTipsSplitResponse resp = splitTipsClient.editTipsSplit(EditStaffAndTipsSplitRequest.newBuilder()
                .setBusinessId(params.getBusinessId())
                .setCompanyId(AuthContext.get().companyId())
                .setApplyBy(AuthContext.get().staffId())
                .setSourceId(params.getSourceId())
                .setSourceType(params.getSourceType())
                .addAllEditStaffs(params.getEditStaffsList())
                .setTipsSplitConfig(params.getTipsSplitConfig())
                .build());
        responseObserver.onNext(EditTipsSplitResult.newBuilder()
                .addAllTipsSplitDetails(resp.getTipsSplitDetailsList())
                .setBusinessTipAmount(resp.getBusinessTipAmount())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getTipsSplitChangedStatus(
            GetTipsSplitChangedStatusParams params, StreamObserver<GetTipsSplitChangedStatusResult> responseObserver) {
        GetTipsSplitChangedStatusResponse svcResponse =
                splitTipsClient.getTipsSplitChangedStatus(SplitTipsConvertor.INSTANCE.toRequest(params));
        responseObserver.onNext(GetTipsSplitChangedStatusResult.newBuilder()
                .setIsChanged(svcResponse.getIsChanged())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void clearTipsSplitChangedStatus(
            ClearTipsSplitChangedStatusParams params,
            StreamObserver<ClearTipsSplitChangedStatusResult> resultStreamObserver) {
        splitTipsClient.clearTipsSplitChangedStatus(SplitTipsConvertor.INSTANCE.toRequest(params));

        resultStreamObserver.onNext(
                ClearTipsSplitChangedStatusResult.newBuilder().build());
        resultStreamObserver.onCompleted();
    }
}
