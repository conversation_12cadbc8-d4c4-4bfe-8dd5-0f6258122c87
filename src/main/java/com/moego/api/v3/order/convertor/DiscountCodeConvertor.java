package com.moego.api.v3.order.convertor;

import com.moego.idl.api.order.v1.GetAvailableDiscountListRequest;
import com.moego.idl.api.order.v1.GetAvailableDiscountListResponse;
import com.moego.idl.api.order.v1.ListApplicableLineItemsParams;
import com.moego.idl.api.order.v1.ListApplicableLineItemsResult;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.order.v1.OrderDetailView;
import com.moego.idl.service.enterprise.v1.ListApplicableLineItemsRequest;
import com.moego.idl.service.enterprise.v1.ListApplicableLineItemsResponse;
import com.moego.idl.service.order.v1.GetAvailableDiscountListInput;
import com.moego.idl.service.order.v1.GetAvailableDiscountListOutput;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface DiscountCodeConvertor {

    DiscountCodeConvertor INSTANCE = Mappers.getMapper(DiscountCodeConvertor.class);

    GetAvailableDiscountListInput toInput(GetAvailableDiscountListRequest request);

    GetAvailableDiscountListResponse toResponse(GetAvailableDiscountListOutput output);

    ListApplicableLineItemsRequest toRequest(ListApplicableLineItemsParams params);

    @Mapping(target = "petBriefs", source = "pets")
    ListApplicableLineItemsResult toResult(ListApplicableLineItemsResponse response);

    @Mapping(target = "name", source = "petName")
    OrderDetailView.PetBrief toBrief(BusinessCustomerPetInfoModel model);
}
