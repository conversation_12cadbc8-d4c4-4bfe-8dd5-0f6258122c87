package com.moego.api.v3.order.utils;

import com.moego.common.constant.OrderConstant;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.idl.models.order.v1.OrderDetailModelV1;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.models.order.v1.OrderStatus;
import java.math.BigDecimal;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/9/26
 */
public class OrderUtil {

    public static double getOutstandingBalance(OrderModel order) {
        return BigDecimal.valueOf(order.getTotalAmount())
                .subtract(BigDecimal.valueOf(order.getPaidAmount()))
                .add(BigDecimal.valueOf(order.getRefundedAmount()))
                .doubleValue();
    }

    public static OrderSourceType convert2SourceType(String orderSourceType) {
        if (!StringUtils.hasLength(orderSourceType)) {
            return OrderSourceType.UNRECOGNIZED;
        }

        if (InvoiceStatusEnum.TYPE_NOSHOW.equalsIgnoreCase(orderSourceType)) {
            return OrderSourceType.NO_SHOW;
        }
        return OrderSourceType.valueOf(orderSourceType.toUpperCase());
    }

    public static Boolean isDepositOrder(OrderDetailModelV1 order) {
        return order != null
                && OrderModel.OrderType.DEPOSIT.equals(order.getOrder().getOrderType());
    }

    public static Boolean isNotDepositOrder(OrderDetailModelV1 order) {
        return !isDepositOrder(order);
    }

    public static Boolean isCanceledOrder(OrderDetailModelV1 order) {
        return order != null && OrderStatus.REMOVED.equals(order.getOrder().getStatus());
    }

    public static Boolean isNotCanceledOrder(OrderDetailModelV1 order) {
        return !isCanceledOrder(order);
    }

    public static Boolean isImmutableOrder(OrderDetailModelV1 order) {
        return order != null
                && OrderConstant.ORDER_VERSION_IMMUTABLE_ORDER
                        <= order.getOrder().getOrderVersion();
    }
}
