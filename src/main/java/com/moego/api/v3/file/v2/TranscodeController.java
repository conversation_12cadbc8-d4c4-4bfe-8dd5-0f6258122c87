package com.moego.api.v3.file.v2;

import com.moego.idl.api.file.v2.CancelJobRequest;
import com.moego.idl.api.file.v2.CancelJobResponse;
import com.moego.idl.api.file.v2.CreateJobRequest;
import com.moego.idl.api.file.v2.CreateJobResponse;
import com.moego.idl.api.file.v2.GetJobRequest;
import com.moego.idl.api.file.v2.GetJobResponse;
import com.moego.idl.api.file.v2.TranscodeServiceGrpc;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.file.v2.FileModel;
import com.moego.idl.models.file.v2.InputSource;
import com.moego.idl.models.file.v2.TenantSourceDef;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.Objects;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class TranscodeController extends TranscodeServiceGrpc.TranscodeServiceImplBase {

    private final com.moego.idl.service.file.v2.FileServiceGrpc.FileServiceBlockingStub fileService;
    private final com.moego.idl.service.file.v2.TranscodeServiceGrpc.TranscodeServiceBlockingStub transcodeService;

    @Override
    @Auth(AuthType.COMPANY)
    public void getJob(GetJobRequest request, StreamObserver<GetJobResponse> responseObserver) {
        var response = this.transcodeService.getJob(toGetJobRequest(request.getJobId()));
        responseObserver.onNext(
                GetJobResponse.newBuilder().setJob(response.getJob()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void createJob(CreateJobRequest request, StreamObserver<CreateJobResponse> responseObserver) {
        var params = com.moego.idl.service.file.v2.QueryFileRequest.newBuilder()
                .setFileId(request.getFileId())
                .build();
        var file = this.fileService.queryFile(params);
        if (!file.hasFile()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "File not found");
        }
        if (!Objects.equals(file.getFile().getCompanyId(), AuthContext.get().companyId())) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "NO authorization");
        }

        var response = this.transcodeService.createJob(toCreateJobRequest(AuthContext.get(), file.getFile(), request));
        responseObserver.onNext(
                CreateJobResponse.newBuilder().setJob(response.getJob()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void cancelJob(CancelJobRequest request, StreamObserver<CancelJobResponse> responseObserver) {
        var job = this.transcodeService.getJob(toGetJobRequest(request.getJobId()));
        if (!job.hasJob()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Job not found");
        }
        if (!Objects.equals(job.getJob().getCompanyId(), AuthContext.get().companyId())) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "NO authorization");
        }

        var params = com.moego.idl.service.file.v2.CancelJobRequest.newBuilder()
                .setJobId(request.getJobId())
                .build();
        var response = this.transcodeService.cancelJob(params);
        responseObserver.onNext(
                CancelJobResponse.newBuilder().setJob(response.getJob()).build());
        responseObserver.onCompleted();
    }

    static com.moego.idl.service.file.v2.GetJobRequest toGetJobRequest(Long jobId) {
        return com.moego.idl.service.file.v2.GetJobRequest.newBuilder()
                .setJobId(jobId)
                .build();
    }

    static com.moego.idl.service.file.v2.CreateJobRequest toCreateJobRequest(
            AuthContext ctx, FileModel file, CreateJobRequest request) {
        var tenant = TenantSourceDef.newBuilder()
                .setCompanyId(ctx.companyId())
                .setBusinessId(ctx.businessId())
                .build();
        var builder = com.moego.idl.service.file.v2.CreateJobRequest.newBuilder();
        var path = String.valueOf(request.getFileId());
        var input = InputSource.newBuilder()
                .setInputSource("MoeGo")
                .setInputPath(path)
                .build();
        builder.setInputSource(input);
        if (request.hasInputSettings()) {
            builder.setInputSettings(request.getInputSettings());
        }
        if (request.hasTemplateId()) {
            builder.setTemplateId(request.getTemplateId());
        }
        if (0 < request.getOutputGroupsCount()) {
            builder.addAllOutputGroups(request.getOutputGroupsList());
        }
        builder.setCreatorId(ctx.accountId());
        builder.setOwnerType(file.getOwnerType());
        builder.setOwnerId(file.getOwnerId());
        builder.setTenant(tenant);
        return builder.build();
    }
}
