package com.moego.api.v3.file.v2;

import com.moego.idl.api.file.v2.FileServiceGrpc;
import com.moego.idl.api.file.v2.FlushFileRequest;
import com.moego.idl.api.file.v2.FlushFileResponse;
import com.moego.idl.api.file.v2.GetUploadPresignedUrlRequest;
import com.moego.idl.api.file.v2.GetUploadPresignedUrlResponse;
import com.moego.idl.api.file.v2.QueryFileRequest;
import com.moego.idl.api.file.v2.QueryFileResponse;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.file.v2.TenantSourceDef;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.Objects;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class FileController extends FileServiceGrpc.FileServiceImplBase {

    private final com.moego.idl.service.file.v2.FileServiceGrpc.FileServiceBlockingStub fileService;

    @Override
    @Auth(AuthType.COMPANY)
    public void getUploadPresignedUrl(
            GetUploadPresignedUrlRequest request, StreamObserver<GetUploadPresignedUrlResponse> responseObserver) {
        var response =
                this.fileService.getUploadPresignedUrl(toGetUploadPresignedUrlRequest(AuthContext.get(), request));
        responseObserver.onNext(from(response));
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void queryFile(QueryFileRequest request, StreamObserver<QueryFileResponse> responseObserver) {
        var res = fileService.queryFile(toQueryFileRequest(request.getFileId()));

        // companyId check: companyId 小于等于0 是系统内文件或 companyId 不等于当前 companyId 也不可访问
        if (!res.hasFile()
                || res.getFile().getCompanyId() <= 0
                || !Objects.equals(
                        res.getFile().getCompanyId(), AuthContext.get().companyId())) {
            throw ExceptionUtil.bizException(Code.CODE_FILE_RECORD_NOT_EXIST, "File not found");
        }

        responseObserver.onNext(
                QueryFileResponse.newBuilder().setFile(res.getFile()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void flushFile(FlushFileRequest request, StreamObserver<FlushFileResponse> responseObserver) {
        var file = this.fileService.queryFile(toQueryFileRequest(request.getFileId()));
        if (!file.hasFile()) {
            throw ExceptionUtil.bizException(Code.CODE_FILE_RECORD_NOT_EXIST, "File not found");
        }
        if (!Objects.equals(file.getFile().getCompanyId(), AuthContext.get().companyId())) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN, "NO authorization");
        }

        var params = com.moego.idl.service.file.v2.FlushFileRequest.newBuilder()
                .setFileId(request.getFileId())
                .build();
        var response = this.fileService.flushFile(params);
        var builder = FlushFileResponse.newBuilder();
        if (response.hasFile()) {
            builder.setFile(response.getFile());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    static com.moego.idl.service.file.v2.QueryFileRequest toQueryFileRequest(Long fileId) {
        return com.moego.idl.service.file.v2.QueryFileRequest.newBuilder()
                .setFileId(fileId)
                .build();
    }

    static com.moego.idl.service.file.v2.GetUploadPresignedUrlRequest toGetUploadPresignedUrlRequest(
            AuthContext ctx, GetUploadPresignedUrlRequest request) {
        var tenant = TenantSourceDef.newBuilder()
                .setCompanyId(ctx.companyId())
                .setBusinessId(ctx.businessId())
                .build();
        var builder = com.moego.idl.service.file.v2.GetUploadPresignedUrlRequest.newBuilder();
        builder.setUsage(request.getUsage());
        builder.setMd5(request.getMd5());
        builder.setFileName(request.getFileName());
        builder.setFileSizeByte(request.getFileSizeByte());
        builder.putAllMetadata(request.getMetadataMap());
        builder.setCreatorId(ctx.accountId());
        builder.setOwnerType(request.getOwnerType());
        builder.setOwnerId(request.getOwnerId());
        builder.setTenant(tenant);
        return builder.build();
    }

    static GetUploadPresignedUrlResponse from(com.moego.idl.service.file.v2.GetUploadPresignedUrlResponse response) {
        return GetUploadPresignedUrlResponse.newBuilder()
                .setFileId(response.getFileId())
                .setPresignedUrl(response.getPresignedUrl())
                .putAllMetadata(response.getMetadataMap())
                .build();
    }
}
