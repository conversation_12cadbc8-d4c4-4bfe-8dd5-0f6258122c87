package com.moego.api.v3.organization.controller;

import com.google.type.LatLng;
import com.moego.common.utils.RedisUtil;
import com.moego.idl.api.organization.v1.CreateStaffTrackingParams;
import com.moego.idl.api.organization.v1.CreateStaffTrackingResult;
import com.moego.idl.api.organization.v1.ListStaffTrackingParams;
import com.moego.idl.api.organization.v1.ListStaffTrackingResult;
import com.moego.idl.api.organization.v1.StaffTrackingServiceGrpc;
import com.moego.idl.models.account.v1.AccountNamespaceType;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.appointment.v1.AppointmentTrackingServiceGrpc;
import com.moego.idl.service.appointment.v1.SyncAppointmentTrackingRequest;
import com.moego.idl.service.branded_app.v1.BrandedAppConfigServiceGrpc;
import com.moego.idl.service.branded_app.v1.GetBrandedAppConfigRequest;
import com.moego.idl.service.organization.v1.CreateStaffTrackingRequest;
import com.moego.idl.service.organization.v1.ListStaffTrackingRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcRequestContext;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.Metadata;
import io.grpc.stub.StreamObserver;
import java.util.concurrent.TimeUnit;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;

@GrpcService
@RequiredArgsConstructor
public class StaffTrackingServer extends StaffTrackingServiceGrpc.StaffTrackingServiceImplBase {

    private final com.moego.idl.service.organization.v1.StaffTrackingServiceGrpc.StaffTrackingServiceBlockingStub
            staffService;
    private final AppointmentTrackingServiceGrpc.AppointmentTrackingServiceBlockingStub appointmentTrackingService;
    private final BrandedAppConfigServiceGrpc.BrandedAppConfigServiceBlockingStub brandedAppConfigService;
    private final RedisUtil redisUtil;

    @Override
    @Auth(AuthType.COMPANY)
    public void createStaffTracking(
            CreateStaffTrackingParams request, StreamObserver<CreateStaffTrackingResult> responseObserver) {
        String deviceId = null;
        var ctx = GrpcRequestContext.get();
        if (ctx != null) {
            Metadata headers = ctx.headers();
            deviceId = headers.get(Metadata.Key.of("mgdid", Metadata.ASCII_STRING_MARSHALLER));
        }
        if (!StringUtils.hasText(deviceId)) {
            deviceId = "";
        }

        // skip impersonator session
        if (!StringUtils.hasText(AuthContext.get().impersonator())) {
            createStaffTracking(
                    AuthContext.get().companyId(), AuthContext.get().staffId(), deviceId, request.getCoordinate());
        }
        final var d = deviceId;
        ThreadPool.execute(() -> {
            if (isBrandedAppUser(AuthContext.get().companyId())) {
                appointmentTrackingService.syncAppointmentTracking(SyncAppointmentTrackingRequest.newBuilder()
                        .setStaffId(AuthContext.get().staffId())
                        .setCompanyId(AuthContext.get().companyId())
                        .setCoordinate(request.getCoordinate())
                        .setDeviceId(d)
                        .build());
            }
        });
        responseObserver.onNext(
                CreateStaffTrackingResult.newBuilder().setResult(true).build());
        responseObserver.onCompleted();
    }

    private void createStaffTracking(Long companyId, Long staffId, String deviceId, LatLng coordinate) {
        String key = "createStaffTracking_time_" + staffId;
        String s = redisUtil.get(key);
        long ts;
        try {
            ts = Long.parseLong(s);
        } catch (NumberFormatException e) {
            ts = 0;
        }
        // 原来前端 60s 上报一次，现在 5s 一次，这里后台做一下限频
        var timeNow = System.currentTimeMillis() / 1000;
        if (timeNow - ts > 60) {
            staffService
                    .createStaffTracking(CreateStaffTrackingRequest.newBuilder()
                            .setTenant(
                                    Tenant.newBuilder().setCompanyId(companyId).build())
                            .setStaffId(staffId)
                            .setDeviceId(deviceId)
                            .setCoordinate(coordinate)
                            .build())
                    .getResult();
            redisUtil.setEx(key, String.valueOf(timeNow), 120, TimeUnit.SECONDS);
        }
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_STAFF_LOCATION})
    public void listStaffTracking(
            ListStaffTrackingParams request, StreamObserver<ListStaffTrackingResult> responseObserver) {
        var result = staffService.listStaffTracking(ListStaffTrackingRequest.newBuilder()
                .setTenant(Tenant.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .build())
                .addAllStaffIds(request.getStaffIdsList())
                .build());
        responseObserver.onNext(ListStaffTrackingResult.newBuilder()
                .addAllStaffTrackings(result.getStaffTrackingsList())
                .build());
        responseObserver.onCompleted();
    }

    private boolean isBrandedAppUser(long companyId) {
        return !brandedAppConfigService
                .getBrandedAppConfig(GetBrandedAppConfigRequest.newBuilder()
                        .setBrandedEntity(GetBrandedAppConfigRequest.BrandedEntity.newBuilder()
                                .setBrandedType(AccountNamespaceType.COMPANY)
                                .setBrandedId(companyId)
                                .build())
                        .build())
                .getConfig()
                .getBrandedAppId()
                .isBlank();
    }
}
