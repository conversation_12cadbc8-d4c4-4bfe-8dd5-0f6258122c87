package com.moego.api.v3.organization.convert;

import com.moego.idl.api.organization.v1.GetCameraListParams;
import com.moego.idl.api.organization.v1.UpdateCameraParams;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.LodgingUnitView;
import com.moego.idl.service.organization.v1.GetCameraListRequest;
import com.moego.idl.service.organization.v1.UpdateCameraRequest;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface CameraConvert {
    CameraConvert INSTANCE = Mappers.getMapper(CameraConvert.class);

    GetCameraListRequest paramsToRequest(GetCameraListParams params);

    LodgingUnitView toLodgingUnitView(LodgingUnitModel model);

    UpdateCameraRequest paramsToRequest(UpdateCameraParams params);
}
