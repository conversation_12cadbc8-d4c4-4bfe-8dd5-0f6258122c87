package com.moego.api.v3.organization.convert;

import com.moego.idl.api.organization.v1.CreateStaffParams;
import com.moego.idl.api.organization.v1.CreateStaffResult;
import com.moego.idl.api.organization.v1.DeleteStaffParams;
import com.moego.idl.api.organization.v1.DeleteStaffResult;
import com.moego.idl.api.organization.v1.GetStaffFullDetailParams;
import com.moego.idl.api.organization.v1.GetStaffFullDetailResult;
import com.moego.idl.api.organization.v1.GetStaffLoginTimeResult;
import com.moego.idl.api.organization.v1.GetStaffsByWorkingLocationIdsResult;
import com.moego.idl.api.organization.v1.QueryStaffListByPaginationParams;
import com.moego.idl.api.organization.v1.QueryStaffListByPaginationResult;
import com.moego.idl.api.organization.v1.UpdateStaffParams;
import com.moego.idl.api.organization.v1.UpdateStaffResult;
import com.moego.idl.service.organization.v1.CreateStaffRequest;
import com.moego.idl.service.organization.v1.CreateStaffResponse;
import com.moego.idl.service.organization.v1.DeleteStaffRequest;
import com.moego.idl.service.organization.v1.DeleteStaffResponse;
import com.moego.idl.service.organization.v1.GetStaffFullDetailRequest;
import com.moego.idl.service.organization.v1.GetStaffFullDetailResponse;
import com.moego.idl.service.organization.v1.GetStaffLoginTimeResponse;
import com.moego.idl.service.organization.v1.GetStaffsByWorkingLocationIdsResponse;
import com.moego.idl.service.organization.v1.QueryStaffListByPaginationRequest;
import com.moego.idl.service.organization.v1.QueryStaffListByPaginationResponse;
import com.moego.idl.service.organization.v1.UpdateStaffRequest;
import com.moego.idl.service.organization.v1.UpdateStaffResponse;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface StaffConvert {

    StaffConvert INSTANCE = Mappers.getMapper(StaffConvert.class);

    CreateStaffRequest toCreateStaffRequest(CreateStaffParams params);

    CreateStaffResult toCreateStaffResult(CreateStaffResponse response);

    GetStaffFullDetailRequest toGetStaffFullDetailRequest(GetStaffFullDetailParams params);

    GetStaffFullDetailResult toGetStaffFullDetailResult(GetStaffFullDetailResponse response);

    UpdateStaffRequest toUpdateStaffRequest(UpdateStaffParams params);

    UpdateStaffResult toUpdateStaffResult(UpdateStaffResponse response);

    DeleteStaffRequest toDeleteStaffRequest(DeleteStaffParams params);

    DeleteStaffResult toDeleteStaffResult(DeleteStaffResponse response);

    GetStaffLoginTimeResult toGetStaffLoginTimeResult(GetStaffLoginTimeResponse response);

    default QueryStaffListByPaginationRequest toQueryStaffListByPaginationRequest(
            QueryStaffListByPaginationParams params) {
        if (params == null) {
            return null;
        }
        QueryStaffListByPaginationRequest.Builder request = QueryStaffListByPaginationRequest.newBuilder();
        if (params.getBusinessIdsCount() > 0) {
            request.addAllBusinessIds(params.getBusinessIdsList());
        }
        if (params.hasPagination()) {
            request.setPagination(params.getPagination());
        }
        if (params.getOrderBysCount() > 0) {
            request.addAllOrderBys(params.getOrderBysList());
        }
        return request.build();
    }

    default QueryStaffListByPaginationResult toQueryStaffListByPaginationResult(
            QueryStaffListByPaginationResponse response) {
        if (response == null) {
            return null;
        }
        QueryStaffListByPaginationResult.Builder result = QueryStaffListByPaginationResult.newBuilder();
        if (response.hasPagination()) {
            result.setPagination(response.getPagination());
        }
        if (response.getStaffsCount() > 0) {
            result.addAllStaffs(response.getStaffsList());
        }
        return result.build();
    }

    default GetStaffsByWorkingLocationIdsResult toGetStaffsByWorkingLocationIdsResult(
            GetStaffsByWorkingLocationIdsResponse response) {
        if (response == null) {
            return null;
        }
        GetStaffsByWorkingLocationIdsResult.Builder result = GetStaffsByWorkingLocationIdsResult.newBuilder();
        if (response.getLocationStaffsCount() > 0) {
            result.addAllLocationStaffs(response.getLocationStaffsList());
        }
        result.setTotalStaffCount(response.getTotalStaffCount());
        result.setTotalLocationCount(response.getTotalLocationCount());
        return result.build();
    }
}
