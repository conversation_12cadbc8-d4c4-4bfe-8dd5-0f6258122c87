package com.moego.api.v3.organization.service;

import com.moego.api.v3.account.helper.SessionHelper;
import com.moego.idl.service.organization.v1.UpdateAccountLastVisitBusinessRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class OrganizationSessionService {
    private final SessionHelper sessionHelper;
    private final com.moego.idl.service.organization.v1.StaffServiceGrpc.StaffServiceBlockingStub staffService;

    public void doSwitch(long sessionId, Long accountId, Long staffId, Long companyId, Long businessId) {
        try {
            staffService.updateAccountLastVisitBusiness(UpdateAccountLastVisitBusinessRequest.newBuilder()
                    .setAccountId(accountId)
                    .setStaffId(staffId)
                    .setLastVisitBusinessId(businessId)
                    .setVisitedAt(System.currentTimeMillis())
                    .build());
        } catch (Exception e) {
            log.error("updateAccountLastVisitedAt error for account id {}, staff id {}", accountId, staffId, e);
        }
        sessionHelper.updateSessionData(sessionId, companyId, businessId, staffId, null);
    }
}
