package com.moego.api.v3.organization.service;

import com.moego.idl.models.organization.v1.LocationStaffIdsDef;
import com.moego.idl.models.organization.v1.StaffBasicView;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.service.organization.v1.GetShowOnCalendarStaffsRequest;
import com.moego.idl.service.organization.v1.GetStaffsByWorkingLocationRequest;
import com.moego.idl.service.organization.v1.QueryStaffByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class StaffService {
    private final StaffServiceGrpc.StaffServiceBlockingStub staffService;

    List<StaffModel> getStaffs(List<Long> staffIdList) {
        staffIdList =
                staffIdList.stream().filter(k -> k != null && k > 0).distinct().toList();
        if (CollectionUtils.isEmpty(staffIdList)) {
            return List.of();
        }
        return staffService
                .queryStaffByIds(QueryStaffByIdsRequest.newBuilder()
                        .addAllStaffIds(staffIdList)
                        .build())
                .getStaffsList();
    }

    public Map<Long, StaffModel> getStaffMap(List<Long> staffIdList) {
        return getStaffs(staffIdList).stream().collect(Collectors.toMap(StaffModel::getId, v -> v, (v1, v2) -> v1));
    }

    public List<StaffBasicView> listStaffForBusiness(long companyId, long businessId, Boolean isShowOnCalendar) {
        var staffs = staffService
                .getStaffsByWorkingLocation(GetStaffsByWorkingLocationRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setWorkingLocationId(businessId)
                        .build())
                .getStaffsList();
        if (isShowOnCalendar == null) {
            return staffs;
        }

        return staffs.stream()
                .filter(k -> Objects.equals(k.getIsShowOnCalendar(), isShowOnCalendar))
                .toList();
    }

    /**
     * Get the staff IDs that can be displayed on the calendar
     *
     * @param companyId company id
     * @param businessId selected business id
     * @param staffId login staff id
     * @return staff id list
     */
    public List<Long> getShowOnCalendarStaffIds(long companyId, long businessId, long staffId) {
        return staffService
                .getShowOnCalendarStaffIds(GetShowOnCalendarStaffsRequest.newBuilder()
                        .setTokenCompanyId(companyId)
                        .setTokenStaffId(staffId)
                        .addBusinessIds(businessId)
                        .build())
                .getLocationStaffIdsList()
                .stream()
                .map(LocationStaffIdsDef::getStaffIdsList)
                .flatMap(List::stream)
                .distinct()
                .toList();
    }
}
