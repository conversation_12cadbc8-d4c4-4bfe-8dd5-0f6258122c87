package com.moego.api.v3.organization.convert;

import com.moego.idl.models.organization.v1.CompanyBriefView;
import com.moego.idl.models.organization.v1.CompanyModel;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.models.organization.v1.LocationModel;
import com.moego.idl.models.organization.v1.StaffBriefView;
import com.moego.idl.models.organization.v1.StaffModel;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface OrganizationConverter {
    OrganizationConverter INSTANCE = Mappers.getMapper(OrganizationConverter.class);

    StaffBriefView toStaffBriefView(StaffModel model);

    CompanyBriefView toCompanyBriefView(CompanyModel model);

    LocationBriefView toLocationBriefView(LocationModel model);

    // 确认传入的全是working location才能用该方法
    @Mapping(target = "isWorkingLocation", defaultValue = "true")
    List<LocationBriefView> toWorkingLocationBriefViewList(List<LocationModel> model);
}
