package com.moego.api.v3.organization.service;

import com.moego.api.v3.organization.convert.CameraConvert;
import com.moego.idl.api.organization.v1.GetCameraListParams;
import com.moego.idl.api.organization.v1.GetCameraListResult;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.organization.v1.CameraModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.models.organization.v1.VisibilityType;
import com.moego.idl.service.offering.v1.GetLodgingUnitListRequest;
import com.moego.idl.service.offering.v1.LodgingUnitServiceGrpc;
import com.moego.idl.service.offering.v1.UpdateLodgingUnitRequest;
import com.moego.idl.service.organization.v1.CameraServiceGrpc;
import com.moego.idl.service.organization.v1.UpdateCameraRequest;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class CameraService {

    private final CameraServiceGrpc.CameraServiceBlockingStub cameraServiceBlockingStub;
    private final LodgingUnitServiceGrpc.LodgingUnitServiceBlockingStub lodgingUnitServiceBlockingStub;

    public List<GetCameraListResult.CameraView> getCameraList(Long companyId, GetCameraListParams params) {
        // query camera
        var request = CameraConvert.INSTANCE.paramsToRequest(params);
        var cameraList = cameraServiceBlockingStub
                .getCameraList(request.toBuilder()
                        .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                        .build())
                .getCamerasList();
        var cameraIdSet = cameraList.stream().map(CameraModel::getId).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(cameraIdSet)) {
            return List.of();
        }
        List<LodgingUnitModel> lodgingUnitList = lodgingUnitServiceBlockingStub
                .getLodgingUnitList(GetLodgingUnitListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllCameraIds(cameraIdSet)
                        .build())
                .getLodgingUnitListList();

        // lodgingUnitList convert to map by cameraId
        Map<Long, LodgingUnitModel> lodgingUnitByCameraId = lodgingUnitList.stream()
                .collect(java.util.stream.Collectors.toMap(
                        LodgingUnitModel::getCameraId,
                        lodgingUnitModel -> lodgingUnitModel,
                        (existing, replacement) -> existing));

        // cameraList convert to cameraView
        return cameraList.stream()
                .map(cameraModel -> {
                    var builder = GetCameraListResult.CameraView.newBuilder().setCamera(cameraModel);
                    if (cameraModel.getVisibilityType() == VisibilityType.PRIVATE
                            && lodgingUnitByCameraId.containsKey(cameraModel.getId())) {
                        builder.setLodgingUnit(CameraConvert.INSTANCE.toLodgingUnitView(
                                lodgingUnitByCameraId.get(cameraModel.getId())));
                    }
                    return builder.build();
                })
                .toList();
    }

    public void updateCamera(Long companyId, Long tokenStaffId, UpdateCameraRequest request, Long lodgingUnitId) {
        var hasVisibilityType = request.hasVisibilityType();
        if (lodgingUnitId != null && lodgingUnitId > 0) {
            hasVisibilityType = true;
            request.toBuilder().setVisibilityType(VisibilityType.PRIVATE);
        }

        if (request.hasIsActive() || request.hasVisibilityType()) {
            cameraServiceBlockingStub.updateCamera(request.toBuilder()
                    .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                    .build());
        }
        if (lodgingUnitId == null) {
            return;
        }
        List<LodgingUnitModel> lodgingUnitList = lodgingUnitServiceBlockingStub
                .getLodgingUnitList(GetLodgingUnitListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addCameraIds(request.getCameraId())
                        .build())
                .getLodgingUnitListList();
        // clear other unit
        if (!CollectionUtils.isEmpty(lodgingUnitList)) {
            var oldUnitId = lodgingUnitList.get(0).getId();
            if (oldUnitId != lodgingUnitId) {
                lodgingUnitServiceBlockingStub.updateLodgingUnit(UpdateLodgingUnitRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setTokenStaffId(tokenStaffId)
                        .setId(oldUnitId)
                        .setCameraId(0)
                        .build());
            }
        }
        if (lodgingUnitId > 0) {
            lodgingUnitServiceBlockingStub.updateLodgingUnit(UpdateLodgingUnitRequest.newBuilder()
                    .setCompanyId(companyId)
                    .setTokenStaffId(tokenStaffId)
                    .setId(lodgingUnitId)
                    .setCameraId(request.getCameraId())
                    .build());
        }
    }
}
