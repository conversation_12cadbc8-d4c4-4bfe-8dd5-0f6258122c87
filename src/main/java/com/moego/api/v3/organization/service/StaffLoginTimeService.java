package com.moego.api.v3.organization.service;

import com.google.type.TimeOfDay;
import com.moego.idl.models.organization.v1.StaffLoginLimitType;
import com.moego.idl.models.organization.v1.StaffLoginTimeDef;
import com.moego.idl.models.organization.v1.TimeRangeDef;
import com.moego.idl.models.organization.v1.WorkingHoursDef;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.ListBusinessWorkingHoursRequest;
import com.moego.idl.utils.v1.TimeOfDayInterval;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class StaffLoginTimeService {
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessService;

    public StaffLoginTimeDef getRecommendedStaffLoginTime(Long company) {
        var businessWorkingHoursMapMap = businessService
                .listBusinessWorkingHours(ListBusinessWorkingHoursRequest.newBuilder()
                        .setCompanyId(company)
                        .build())
                .getBusinessWorkingHoursMapMap();
        return StaffLoginTimeDef.newBuilder()
                .setLoginLimitType(StaffLoginLimitType.TIME_RANGE_IN_ONE_DAY)
                .setTimeRange(getWidestDayInterval(
                        businessWorkingHoursMapMap.values().stream().toList()))
                .build();
    }

    private TimeOfDayInterval getWidestDayInterval(List<WorkingHoursDef> workingHours) {
        var builder = TimeOfDayInterval.newBuilder()
                .setStart(TimeOfDay.getDefaultInstance())
                .setEnd(TimeOfDay.newBuilder().setHours(23).setMinutes(59));
        List<TimeRangeDef> timeRanges = new ArrayList<>();
        for (WorkingHoursDef workingHour : workingHours) {
            var t1 = getWidestRange(workingHour.getMondayList());
            var t2 = getWidestRange(workingHour.getTuesdayList());
            var t3 = getWidestRange(workingHour.getWednesdayList());
            var t4 = getWidestRange(workingHour.getThursdayList());
            var t5 = getWidestRange(workingHour.getFridayList());
            var t6 = getWidestRange(workingHour.getSaturdayList());
            var t7 = getWidestRange(workingHour.getSundayList());
            timeRanges.add(getWidestRange(List.of(t1, t2, t3, t4, t5, t6, t7)));
        }
        var widestRange = getWidestRange(timeRanges);
        if (widestRange.getStartTime() > widestRange.getEndTime()) {
            // working hours are not set for any day
            return builder.build();
        }
        builder.setStart(TimeOfDay.newBuilder()
                .setHours(widestRange.getStartTime() / 60)
                .setMinutes(widestRange.getStartTime() % 60)
                .build());
        builder.setEnd(TimeOfDay.newBuilder()
                .setHours(widestRange.getEndTime() / 60)
                .setMinutes(widestRange.getEndTime() % 60)
                .build());
        return builder.build();
    }

    private TimeRangeDef getWidestRange(List<TimeRangeDef> timeRanges) {
        // there are total 1440 minutes in a day
        var builder = TimeRangeDef.newBuilder().setStartTime(1439).setEndTime(0);
        for (TimeRangeDef timeRange : timeRanges) {
            if (timeRange.getStartTime() < builder.getStartTime()) {
                builder.setStartTime(timeRange.getStartTime());
            }
            if (timeRange.getEndTime() > builder.getEndTime()) {
                builder.setEndTime(timeRange.getEndTime());
            }
        }
        return builder.build();
    }
}
