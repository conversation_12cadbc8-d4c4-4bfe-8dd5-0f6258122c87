package com.moego.api.v3.organization.controller;

import com.moego.api.v3.organization.convert.CameraConvert;
import com.moego.api.v3.organization.service.CameraService;
import com.moego.idl.api.organization.v1.CameraServiceGrpc;
import com.moego.idl.api.organization.v1.GetCameraListParams;
import com.moego.idl.api.organization.v1.GetCameraListResult;
import com.moego.idl.api.organization.v1.GetCameraSettingParams;
import com.moego.idl.api.organization.v1.GetCameraSettingResult;
import com.moego.idl.api.organization.v1.UpdateCameraParams;
import com.moego.idl.api.organization.v1.UpdateCameraResult;
import com.moego.idl.api.organization.v1.UpdateCameraSettingParams;
import com.moego.idl.api.organization.v1.UpdateCameraSettingResult;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.organization.v1.GetCameraSettingRequest;
import com.moego.idl.service.organization.v1.UpdateCameraSettingRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class CameraServer extends CameraServiceGrpc.CameraServiceImplBase {

    private final CameraService cameraService;
    private final com.moego.idl.service.organization.v1.CameraServiceGrpc.CameraServiceBlockingStub
            cameraServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void getCameraList(GetCameraListParams request, StreamObserver<GetCameraListResult> responseObserver) {
        var cameraViewList = cameraService.getCameraList(AuthContext.get().companyId(), request);
        responseObserver.onNext(
                GetCameraListResult.newBuilder().addAllCameras(cameraViewList).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateCamera(UpdateCameraParams request, StreamObserver<UpdateCameraResult> responseObserver) {
        cameraService.updateCamera(
                AuthContext.get().companyId(),
                AuthContext.get().staffId(),
                CameraConvert.INSTANCE.paramsToRequest(request),
                request.hasLodgingUnitId() ? request.getLodgingUnitId() : null);
        responseObserver.onNext(UpdateCameraResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getCameraSetting(
            GetCameraSettingParams request, StreamObserver<GetCameraSettingResult> responseObserver) {
        var settingModel = cameraServiceBlockingStub
                .getCameraSetting(GetCameraSettingRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(AuthContext.get().companyId()))
                        .build())
                .getCameraSetting();

        responseObserver.onNext(GetCameraSettingResult.newBuilder()
                .setCameraSetting(settingModel)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateCameraSetting(
            UpdateCameraSettingParams request, StreamObserver<UpdateCameraSettingResult> responseObserver) {
        cameraServiceBlockingStub.updateCameraSetting(UpdateCameraSettingRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(AuthContext.get().companyId()))
                .setPublicAvailabilityType(request.getPublicAvailabilityType())
                .build());

        responseObserver.onNext(UpdateCameraSettingResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
