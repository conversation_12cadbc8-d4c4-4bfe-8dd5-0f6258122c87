package com.moego.api.v3.organization.controller;

import com.moego.api.v3.organization.convert.OrganizationConverter;
import com.moego.api.v3.organization.service.OrganizationSessionService;
import com.moego.idl.api.organization.v1.AddTaxRuleParams;
import com.moego.idl.api.organization.v1.AddTaxRuleResult;
import com.moego.idl.api.organization.v1.CompanyQuestionRecordParams;
import com.moego.idl.api.organization.v1.CompanyQuestionRecordQueryParams;
import com.moego.idl.api.organization.v1.CompanyQuestionRecordQueryResult;
import com.moego.idl.api.organization.v1.CompanyQuestionRecordResult;
import com.moego.idl.api.organization.v1.CompanyServiceGrpc;
import com.moego.idl.api.organization.v1.CreateCompanyParams;
import com.moego.idl.api.organization.v1.CreateCompanyResult;
import com.moego.idl.api.organization.v1.DeleteTaxRuleParams;
import com.moego.idl.api.organization.v1.DeleteTaxRuleResult;
import com.moego.idl.api.organization.v1.GetClockInOutSettingParams;
import com.moego.idl.api.organization.v1.GetClockInOutSettingResult;
import com.moego.idl.api.organization.v1.GetCompaniesExtraInfoParams;
import com.moego.idl.api.organization.v1.GetCompaniesExtraInfoResponse;
import com.moego.idl.api.organization.v1.GetCompanyPreferenceSettingParams;
import com.moego.idl.api.organization.v1.GetCompanyPreferenceSettingResult;
import com.moego.idl.api.organization.v1.GetTaxRuleListParams;
import com.moego.idl.api.organization.v1.GetTaxRuleListResult;
import com.moego.idl.api.organization.v1.QueryCompanyStaffByAccountParams;
import com.moego.idl.api.organization.v1.QueryCompanyStaffByAccountResult;
import com.moego.idl.api.organization.v1.QueryCompanyStaffByAccountWithLocationsParams;
import com.moego.idl.api.organization.v1.QueryCompanyStaffByAccountWithLocationsResult;
import com.moego.idl.api.organization.v1.SortCompanyParams;
import com.moego.idl.api.organization.v1.SortCompanyResult;
import com.moego.idl.api.organization.v1.SwitchCompanyParams;
import com.moego.idl.api.organization.v1.SwitchCompanyResult;
import com.moego.idl.api.organization.v1.UpdateClockInOutSettingParams;
import com.moego.idl.api.organization.v1.UpdateClockInOutSettingResult;
import com.moego.idl.api.organization.v1.UpdateCompanyPreferenceSettingParams;
import com.moego.idl.api.organization.v1.UpdateCompanyPreferenceSettingResult;
import com.moego.idl.api.organization.v1.UpdateTaxRuleParams;
import com.moego.idl.api.organization.v1.UpdateTaxRuleResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.CompanyExtraInfoDef;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.models.organization.v1.LocationModel;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.service.order.v1.GetCompanyServiceChargeListRequest;
import com.moego.idl.service.order.v1.ServiceChargeCompanyServiceGrpc;
import com.moego.idl.service.organization.v1.AddTaxRuleResponse;
import com.moego.idl.service.organization.v1.CompanyQuestionRecordQueryRequest;
import com.moego.idl.service.organization.v1.CompanyQuestionRecordRequest;
import com.moego.idl.service.organization.v1.CreateCompanyRequest;
import com.moego.idl.service.organization.v1.CreateCompanyResponse;
import com.moego.idl.service.organization.v1.DeleteTaxRuleResponse;
import com.moego.idl.service.organization.v1.GetClockInOutSettingRequest;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingResponse;
import com.moego.idl.service.organization.v1.GetStaffByCompanyRequest;
import com.moego.idl.service.organization.v1.GetStaffDetailRequest;
import com.moego.idl.service.organization.v1.GetStaffsByAccountIdRequest;
import com.moego.idl.service.organization.v1.GetTaxRuleListResponse;
import com.moego.idl.service.organization.v1.GetWorkingLocationListByCompaniesStaffRequest;
import com.moego.idl.service.organization.v1.GetWorkingLocationListByCompaniesStaffResponse;
import com.moego.idl.service.organization.v1.IsCompanyMigrateRequest;
import com.moego.idl.service.organization.v1.QueryCompaniesByIdsRequest;
import com.moego.idl.service.organization.v1.SortCompanyRequest;
import com.moego.idl.service.organization.v1.UpdateClockInOutSettingRequest;
import com.moego.idl.service.organization.v1.UpdateCompanyPreferenceSettingResponse;
import com.moego.idl.service.organization.v1.UpdateTaxRuleResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.vo.GetStaffsUnreadCountByBusinessListVo;
import com.moego.server.grooming.api.IGroomingServiceService;
import com.moego.server.grooming.params.QueryServiceByTagIdParams;
import com.moego.server.message.client.INotificationClient;
import com.moego.server.retail.api.IRetailService;
import com.moego.server.retail.param.TaxIdsParams;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@GrpcService
@Slf4j
@RequiredArgsConstructor
public class CompanyServer extends CompanyServiceGrpc.CompanyServiceImplBase {
    private final com.moego.idl.service.organization.v1.CompanyServiceGrpc.CompanyServiceBlockingStub companyService;
    private final com.moego.idl.service.organization.v1.StaffServiceGrpc.StaffServiceBlockingStub staffService;
    private final com.moego.idl.service.organization.v1.BusinessServiceGrpc.BusinessServiceBlockingStub businessService;
    private final INotificationClient notificationClient;
    private final OrganizationSessionService organizationSessionService;
    private final IBusinessBusinessClient businessClient;
    private final IGroomingServiceService groomingServiceClient;
    private final IRetailService retailServiceClient;
    private final ServiceChargeCompanyServiceGrpc.ServiceChargeCompanyServiceBlockingStub serviceChargeCompanyService;

    @Override
    @Auth(AuthType.ACCOUNT)
    public void queryCompanyStaffByAccount(
            QueryCompanyStaffByAccountParams request,
            StreamObserver<QueryCompanyStaffByAccountResult> responseObserver) {
        var accountId = AuthContext.get().accountId();
        var staffs = staffService
                .getStaffsByAccountId(GetStaffsByAccountIdRequest.newBuilder()
                        .setAccountId(accountId)
                        .build())
                .getStaffsList();
        Set<Long> companyIds = new HashSet<>();
        staffs = staffs.stream()
                .filter(staff -> {
                    if (companyIds.contains(staff.getCompanyId())) {
                        return false;
                    } else {
                        companyIds.add(staff.getCompanyId());
                        return true;
                    }
                })
                .toList();
        var companyIdToModel = companyService
                .queryCompaniesByIds(QueryCompaniesByIdsRequest.newBuilder()
                        .addAllCompanyIds(companyIds)
                        .build())
                .getCompanyIdToCompanyMap();
        var responseBuilder = QueryCompanyStaffByAccountResult.newBuilder();
        for (StaffModel staff : staffs) {
            if (companyIdToModel.containsKey(staff.getCompanyId())) {
                responseBuilder.addCompanyStaffs(QueryCompanyStaffByAccountResult.CompanyStaff.newBuilder()
                        .setStaff(OrganizationConverter.INSTANCE.toStaffBriefView(staff))
                        .setCompany(OrganizationConverter.INSTANCE.toCompanyBriefView(
                                companyIdToModel.get(staff.getCompanyId())))
                        .buildPartial());
            }
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void createCompany(CreateCompanyParams request, StreamObserver<CreateCompanyResult> responseObserver) {
        CreateCompanyRequest.Builder createCompanyRequest = CreateCompanyRequest.newBuilder()
                .setLocation(request.getLocation())
                .setAccountId(AuthContext.get().accountId())
                .setSource(request.getSource())
                .setCountry(request.getCountry())
                .setTimeZone(request.getTimeZone())
                .setPhoneNumber(request.getPhoneNumber())
                .setCurrencyCode(request.getCurrencyCode())
                .setCurrencySymbol(request.getCurrencySymbol())
                .setCompanyType(request.getCompanyType());
        if (request.hasKnowAboutUs()) {
            createCompanyRequest.setKnowAboutUs(request.getKnowAboutUs());
        }
        CreateCompanyResponse response = companyService.createCompany(createCompanyRequest.build());
        responseObserver.onNext(CreateCompanyResult.newBuilder()
                .setCompanyId(response.getCompanyId())
                .setBusinessId(response.getBusinessId())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateCompanyPreferenceSetting(
            UpdateCompanyPreferenceSettingParams request,
            StreamObserver<UpdateCompanyPreferenceSettingResult> responseObserver) {
        UpdateCompanyPreferenceSettingResponse response = companyService.updateCompanyPreferenceSetting(
                com.moego.idl.service.organization.v1.UpdateCompanyPreferenceSettingRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setTokenStaffId(AuthContext.get().staffId())
                        .setPreferenceSetting(request.getPreferenceSetting())
                        .build());
        responseObserver.onNext(UpdateCompanyPreferenceSettingResult.newBuilder()
                .setSuccess(response.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void getCompanyPreferenceSetting(
            GetCompanyPreferenceSettingParams request,
            StreamObserver<GetCompanyPreferenceSettingResult> responseObserver) {
        if (AuthContext.get().companyId() == null
                || AuthContext.get().companyId().equals(0L)) {
            responseObserver.onNext(GetCompanyPreferenceSettingResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        GetCompanyPreferenceSettingResponse response = companyService.getCompanyPreferenceSetting(
                com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .build());
        responseObserver.onNext(GetCompanyPreferenceSettingResult.newBuilder()
                .setPreferenceSetting(response.getPreferenceSetting())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void switchCompany(SwitchCompanyParams request, StreamObserver<SwitchCompanyResult> responseObserver) {

        long sessionId = AuthContext.get().sessionId();
        Long accountId = AuthContext.get().accountId();
        long toCompanyId = request.getCompanyId();
        var reqBid = request.getBusinessId();

        // if the company did not migrate and request has business_id, need to specify business_id
        var isToCompanyMigrate = companyService
                .isCompanyMigrate(IsCompanyMigrateRequest.newBuilder()
                        .setCompanyId(toCompanyId)
                        .build())
                .getIsCompanyMigrate();
        var getStaffReqBuilder =
                GetStaffByCompanyRequest.newBuilder().setCompanyId(toCompanyId).setAccountId(accountId);
        if (!isToCompanyMigrate && request.hasBusinessId()) {
            getStaffReqBuilder.setBusinessId(reqBid);
        }
        var staffRes = staffService.getStaffByCompany(getStaffReqBuilder.build());
        if (!staffRes.hasStaff()) {
            throw ExceptionUtil.bizException(Code.CODE_SWITCH_COMPANY_ERROR);
        }
        var staff = staffRes.getStaff();

        // if request has business_id, need to switch to specific business.
        // otherwise, if this staff is enterprise staff, or it's last visit business id is 0
        //  switch to the first working location
        // otherwise, switch to the last visit business id
        long toBusinessId;
        List<Long> workingLocationIdList = new ArrayList<>();
        try {
            if (isToCompanyMigrate || staff.getEnterpriseId() != 0) {
                workingLocationIdList = new ArrayList<>(staffService
                        .getStaffDetail(GetStaffDetailRequest.newBuilder()
                                .setCompanyId(toCompanyId)
                                .setId(staff.getId())
                                .build())
                        .getStaff()
                        .getWorkingLocationListList()
                        .stream()
                        .map(LocationBriefView::getId)
                        .toList());
            } else {
                workingLocationIdList.add(staff.getBusinessId());
            }
        } catch (StatusRuntimeException e) {
            if (ExceptionUtil.extractCode(e).equals(Code.CODE_STAFF_WORKING_LOCATION_LIST_IS_EMPTY)) {
                if (Objects.equals(staff.getBusinessId(), 0L)) {
                    throw ExceptionUtil.bizException(Code.CODE_STAFF_WORKING_LOCATION_LIST_IS_EMPTY);
                }
                // if staff has no working location, but has business id,it may be a staff not migrated,
                // staff's business id need to be added in working location list
                workingLocationIdList.add(staff.getBusinessId());
            }
        }
        if (request.hasBusinessId()) {
            if (!Objects.equals(reqBid, staff.getBusinessId())
                    && workingLocationIdList.stream().noneMatch(locationId -> locationId == reqBid)) {
                throw ExceptionUtil.bizException(Code.CODE_SWITCH_BUSINESS_ERROR);
            }
            toBusinessId = reqBid;
        } else {
            if (!Objects.equals(staff.getLastVisitBusinessId(), 0L)
                    && workingLocationIdList.stream()
                            .anyMatch(locationId -> locationId == staff.getLastVisitBusinessId())) {
                toBusinessId = staff.getLastVisitBusinessId();
            } else {
                toBusinessId = workingLocationIdList.get(0);
            }
        }
        organizationSessionService.doSwitch(sessionId, accountId, staff.getId(), staff.getCompanyId(), toBusinessId);
        responseObserver.onNext(SwitchCompanyResult.newBuilder().getDefaultInstanceForType());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getCompaniesExtraInfo(
            GetCompaniesExtraInfoParams request, StreamObserver<GetCompaniesExtraInfoResponse> responseObserver) {

        var accountId = AuthContext.get().accountId();
        var staffs = staffService
                .getStaffsByAccountId(GetStaffsByAccountIdRequest.newBuilder()
                        .setAccountId(accountId)
                        .build())
                .getStaffsList();
        Set<Long> companyIds = new HashSet<>();
        staffs = staffs.stream()
                .filter(staff -> {
                    if (companyIds.contains(staff.getCompanyId())) {
                        return false;
                    } else {
                        companyIds.add(staff.getCompanyId());
                        return true;
                    }
                })
                .toList();
        List<GetStaffsUnreadCountByBusinessListVo> vos = new ArrayList<>();
        // FIXME: 目前一个 account 的 company staff 都在5个内，先串行，后续有性能问题考虑并行
        // 迁移后的需要限定查询范围为 working location, 跳过迁移前的
        for (StaffModel staff : staffs) {
            GetStaffsUnreadCountByBusinessListVo vo = new GetStaffsUnreadCountByBusinessListVo();
            try {
                var businessIds = staffService
                        .getStaffDetail(GetStaffDetailRequest.newBuilder()
                                .setCompanyId(staff.getCompanyId())
                                .setId(staff.getId())
                                .build())
                        .getStaff()
                        .getWorkingLocationListList()
                        .stream()
                        .map(locationBriefView -> (int) locationBriefView.getId())
                        .toList();
                vo.setBusinessIdList(businessIds);
            } catch (StatusRuntimeException e) {
                if (!ExceptionUtil.extractCode(e).equals(Code.CODE_STAFF_WORKING_LOCATION_LIST_IS_EMPTY)) {
                    throw e;
                }
                continue;
            }
            vo.setStaffId((int) staff.getId());
            vo.setCompanyId(staff.getCompanyId());
            vos.add(vo);
        }
        var responseBuilder = GetCompaniesExtraInfoResponse.newBuilder();
        if (!vos.isEmpty()) {
            var companyUnreadCountMap = notificationClient.getStaffsUnreadCountByBusinessList(vos);
            companyUnreadCountMap.forEach((cId, unreadCount) -> {
                responseBuilder.putCompanyExtraInfoMap(
                        cId,
                        CompanyExtraInfoDef.newBuilder()
                                .setUnreadNotificationCount(unreadCount)
                                .build());
            });
        }
        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void queryCompanyStaffByAccountWithLocations(
            QueryCompanyStaffByAccountWithLocationsParams request,
            StreamObserver<QueryCompanyStaffByAccountWithLocationsResult> responseObserver) {
        var accountId = AuthContext.get().accountId();
        // get staffs
        var staffs = staffService
                .getStaffsByAccountId(GetStaffsByAccountIdRequest.newBuilder()
                        .setAccountId(accountId)
                        .build())
                .getStaffsList();
        var locationReqBuilder = GetWorkingLocationListByCompaniesStaffRequest.newBuilder();
        List<Long> companyIds = new ArrayList<>();
        staffs.forEach(staff -> {
            companyIds.add(staff.getCompanyId());
            locationReqBuilder.addParams(GetWorkingLocationListByCompaniesStaffRequest.Param.newBuilder()
                    .setCompanyId(staff.getCompanyId())
                    .setStaffId(staff.getId())
                    .build());
        });
        // get companies
        var companyIdToModel = companyService
                .queryCompaniesByIds(QueryCompaniesByIdsRequest.newBuilder()
                        .addAllCompanyIds(companyIds)
                        .build())
                .getCompanyIdToCompanyMap();
        // get working locations
        var locationRes = businessService.getWorkingLocationListByCompaniesStaff(locationReqBuilder.build());
        // build result
        var resultBuilder = QueryCompanyStaffByAccountWithLocationsResult.newBuilder();
        staffs.forEach(staff -> {
            List<LocationModel> locations = locationRes
                    .getCompanyLocationsMapMap()
                    .getOrDefault(
                            staff.getCompanyId(),
                            GetWorkingLocationListByCompaniesStaffResponse.LocationList.getDefaultInstance())
                    .getLocationsList();
            resultBuilder.addCompanyStaffsWithLocations(
                    QueryCompanyStaffByAccountWithLocationsResult.CompanyStaffWithLocations.newBuilder()
                            .setStaff(OrganizationConverter.INSTANCE.toStaffBriefView(staff))
                            .addAllLocations(OrganizationConverter.INSTANCE.toWorkingLocationBriefViewList(locations))
                            .setCompany(OrganizationConverter.INSTANCE.toCompanyBriefView(
                                    companyIdToModel.get(staff.getCompanyId())))
                            .buildPartial());
        });
        responseObserver.onNext(resultBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void addTaxRule(AddTaxRuleParams request, StreamObserver<AddTaxRuleResult> responseObserver) {
        AddTaxRuleResponse response =
                companyService.addTaxRule(com.moego.idl.service.organization.v1.AddTaxRuleRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setTokenStaffId(AuthContext.get().staffId())
                        .setTaxRule(request.getTaxRule())
                        .build());
        responseObserver.onNext(
                AddTaxRuleResult.newBuilder().setId(response.getId()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateTaxRule(UpdateTaxRuleParams request, StreamObserver<UpdateTaxRuleResult> responseObserver) {
        UpdateTaxRuleResponse response =
                companyService.updateTaxRule(com.moego.idl.service.organization.v1.UpdateTaxRuleRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setTokenStaffId(AuthContext.get().staffId())
                        .setId(request.getId())
                        .setTaxRule(request.getTaxRule())
                        .build());
        responseObserver.onNext(UpdateTaxRuleResult.newBuilder()
                .setSuccess(response.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deleteTaxRule(DeleteTaxRuleParams request, StreamObserver<DeleteTaxRuleResult> responseObserver) {
        QueryServiceByTagIdParams params = new QueryServiceByTagIdParams();
        params.setTagId((int) request.getId());
        params.setCompanyId(AuthContext.get().companyId());
        if (groomingServiceClient.selectServiceByTagId(params)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_TAX_IS_USED, "Cannot delete. There are services associated with this tax.");
        }
        // GROOM-990 product&package绑定tax后，不允许删除该tax
        if (retailServiceClient.isTaxExist(
                new TaxIdsParams(null, AuthContext.get().companyId(), List.of((int) request.getId())))) {
            throw ExceptionUtil.bizException(
                    Code.CODE_TAX_IS_USED_RETAIL,
                    "Cannot delete. There are products or packages associated with this tax.");
        }
        if (serviceChargeCompanyService
                        .getCompanyServiceChargeList(GetCompanyServiceChargeListRequest.newBuilder()
                                .setCompanyId(AuthContext.get().companyId())
                                .addAllTaxIds(List.of((int) request.getId()))
                                .build())
                        .getServiceChargeCount()
                > 0) {
            throw ExceptionUtil.bizException(
                    Code.CODE_TAX_IS_USED, "Cannot delete. There are service charges associated with this tax.");
        }

        DeleteTaxRuleResponse response =
                companyService.deleteTaxRule(com.moego.idl.service.organization.v1.DeleteTaxRuleRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setTokenStaffId(AuthContext.get().staffId())
                        .setId(request.getId())
                        .build());
        responseObserver.onNext(DeleteTaxRuleResult.newBuilder()
                .setSuccess(response.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getTaxRuleList(GetTaxRuleListParams request, StreamObserver<GetTaxRuleListResult> responseObserver) {
        GetTaxRuleListResponse response =
                companyService.getTaxRuleList(com.moego.idl.service.organization.v1.GetTaxRuleListRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setTokenStaffId(AuthContext.get().staffId())
                        .build());
        responseObserver.onNext(GetTaxRuleListResult.newBuilder()
                .addAllTaxRule(response.getRuleList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getClockInOutSetting(
            GetClockInOutSettingParams request, StreamObserver<GetClockInOutSettingResult> responseObserver) {
        var response = companyService.getClockInOutSetting(GetClockInOutSettingRequest.newBuilder()
                .setTokenCompanyId(AuthContext.get().companyId())
                .build());
        responseObserver.onNext(GetClockInOutSettingResult.newBuilder()
                .setClockInOutSetting(response.getClockInOutSetting())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.CLOCK_IN_OR_OUT})
    public void updateClockInOutSetting(
            UpdateClockInOutSettingParams request, StreamObserver<UpdateClockInOutSettingResult> responseObserver) {
        var response = companyService.updateClockInOutSetting(UpdateClockInOutSettingRequest.newBuilder()
                .setTokenCompanyId(AuthContext.get().companyId())
                .setTokenStaffId(AuthContext.get().staffId())
                .setClockInOutSetting(request.getClockInOutSetting())
                .build());
        responseObserver.onNext(UpdateClockInOutSettingResult.newBuilder()
                .setSuccess(response.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getCompanyQuestionRecord(
            CompanyQuestionRecordQueryParams request,
            StreamObserver<CompanyQuestionRecordQueryResult> responseObserver) {
        boolean isFillQuestion = true;
        long companyId = request.getCompanyId();
        if (companyId != 0) {
            var companyDto = businessClient.getCompanyById((int) companyId);
            if (companyDto != null
                    && AuthContext.get()
                            .accountId()
                            .equals(companyDto.getAccountId().longValue())) {
                var responseResult =
                        companyService.getCompanyQuestionRecord(CompanyQuestionRecordQueryRequest.newBuilder()
                                .setCompanyId(companyId)
                                .build());
                isFillQuestion = responseResult.getIsFillQuestion();
            } else {
                isFillQuestion = false;
            }
        }
        responseObserver.onNext(CompanyQuestionRecordQueryResult.newBuilder()
                .setIsFillQuestion(isFillQuestion)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void companyQuestionRecordSave(
            CompanyQuestionRecordParams request, StreamObserver<CompanyQuestionRecordResult> responseObserver) {
        long companyId = request.getCompanyId();
        boolean saveSuccess = false;
        if (companyId != 0) {
            var companyDto = businessClient.getCompanyById((int) companyId);
            if (companyDto != null
                    && AuthContext.get()
                            .accountId()
                            .equals(companyDto.getAccountId().longValue())) {
                var requestBuilder = CompanyQuestionRecordRequest.newBuilder()
                        .setPetPerMonth(request.getPetPerMonth())
                        .setMoveFrom(request.getMoveFrom())
                        .setSourceFrom(request.getSourceFrom())
                        .setTotalLocations(request.getTotalLocations())
                        .setTotalVans(request.getTotalVans())
                        .setSourceFromOther(request.getSourceFromOther())
                        .setCompanyId(companyId);
                var response = companyService.companyQuestionRecordSave(requestBuilder.build());
                saveSuccess = response.getSuccess();
            }
        }
        responseObserver.onNext(
                CompanyQuestionRecordResult.newBuilder().setSuccess(saveSuccess).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void sortCompany(SortCompanyParams request, StreamObserver<SortCompanyResult> responseObserver) {
        var accountId = AuthContext.get().accountId();
        SortCompanyResult.Builder responseBuilder = SortCompanyResult.newBuilder();

        SortCompanyRequest sortRequest = SortCompanyRequest.newBuilder()
                .setAccountId(accountId)
                .addAllIds(request.getIdsList())
                .build();

        companyService.sortCompany(sortRequest);

        responseObserver.onNext(responseBuilder.build());
        responseObserver.onCompleted();
    }
}
