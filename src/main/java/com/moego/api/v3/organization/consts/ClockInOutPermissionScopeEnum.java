package com.moego.api.v3.organization.consts;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

@Getter
@RequiredArgsConstructor
public enum ClockInOutPermissionScopeEnum {
    UNKNOWN(0),
    FOR_THEMSELVES_ONLY(1),
    FOR_ALL_STAFF_ACROSS_WORKING_LOCATIONS(2);

    private final long value;

    public static ClockInOutPermissionScopeEnum fromValue(long value) {
        for (ClockInOutPermissionScopeEnum permissionScopeEnum : ClockInOutPermissionScopeEnum.values()) {
            if (permissionScopeEnum.value == value) {
                return permissionScopeEnum;
            }
        }
        return UNKNOWN;
    }
}
