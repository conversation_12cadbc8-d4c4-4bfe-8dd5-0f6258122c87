package com.moego.api.v3.organization.controller;

import com.moego.api.v3.organization.service.OrganizationSessionService;
import com.moego.idl.api.organization.v1.AddClosedDateParams;
import com.moego.idl.api.organization.v1.AddClosedDateResult;
import com.moego.idl.api.organization.v1.BusinessServiceGrpc;
import com.moego.idl.api.organization.v1.CreateLocationParams;
import com.moego.idl.api.organization.v1.CreateLocationResult;
import com.moego.idl.api.organization.v1.DeleteClosedDateParams;
import com.moego.idl.api.organization.v1.DeleteClosedDateResult;
import com.moego.idl.api.organization.v1.EditClosedHolidayParams;
import com.moego.idl.api.organization.v1.EditClosedHolidayResult;
import com.moego.idl.api.organization.v1.GetClosedDateListParams;
import com.moego.idl.api.organization.v1.GetClosedDateListResult;
import com.moego.idl.api.organization.v1.GetClosedHolidayListParams;
import com.moego.idl.api.organization.v1.GetClosedHolidayListResult;
import com.moego.idl.api.organization.v1.GetLocationDetailParams;
import com.moego.idl.api.organization.v1.GetLocationDetailResult;
import com.moego.idl.api.organization.v1.GetLocationListParams;
import com.moego.idl.api.organization.v1.GetLocationListResult;
import com.moego.idl.api.organization.v1.GetWorkingHoursListParams;
import com.moego.idl.api.organization.v1.GetWorkingHoursListResult;
import com.moego.idl.api.organization.v1.MassEditClosedHolidayParams;
import com.moego.idl.api.organization.v1.MassEditClosedHolidayResult;
import com.moego.idl.api.organization.v1.SwitchBusinessParams;
import com.moego.idl.api.organization.v1.SwitchBusinessResult;
import com.moego.idl.api.organization.v1.UpdateClosedDateParams;
import com.moego.idl.api.organization.v1.UpdateClosedDateResult;
import com.moego.idl.api.organization.v1.UpdateLocationParams;
import com.moego.idl.api.organization.v1.UpdateLocationResult;
import com.moego.idl.api.organization.v1.UpdateOnlinePreferenceParams;
import com.moego.idl.api.organization.v1.UpdateOnlinePreferenceResult;
import com.moego.idl.api.organization.v1.UpdateWorkingHoursParams;
import com.moego.idl.api.organization.v1.UpdateWorkingHoursResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.organization.v1.AddClosedDateResponse;
import com.moego.idl.service.organization.v1.CreateLocationRequest;
import com.moego.idl.service.organization.v1.CreateLocationResponse;
import com.moego.idl.service.organization.v1.DeleteClosedDateResponse;
import com.moego.idl.service.organization.v1.EditClosedHolidayResponse;
import com.moego.idl.service.organization.v1.GetClosedDateListResponse;
import com.moego.idl.service.organization.v1.GetClosedHolidayListResponse;
import com.moego.idl.service.organization.v1.GetCompanyIdRequest;
import com.moego.idl.service.organization.v1.GetLocationDetailRequest;
import com.moego.idl.service.organization.v1.GetLocationDetailResponse;
import com.moego.idl.service.organization.v1.GetLocationListRequest;
import com.moego.idl.service.organization.v1.GetLocationListResponse;
import com.moego.idl.service.organization.v1.GetStaffDetailRequest;
import com.moego.idl.service.organization.v1.GetWorkingHourListResponse;
import com.moego.idl.service.organization.v1.MassEditClosedHolidayResponse;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.idl.service.organization.v1.UpdateClosedDateResponse;
import com.moego.idl.service.organization.v1.UpdateLocationRequest;
import com.moego.idl.service.organization.v1.UpdateLocationResponse;
import com.moego.idl.service.organization.v1.UpdateOnlinePreferenceResponse;
import com.moego.idl.service.organization.v1.UpdateWorkingHourResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class BusinessServer extends BusinessServiceGrpc.BusinessServiceImplBase {
    private final com.moego.idl.service.organization.v1.BusinessServiceGrpc.BusinessServiceBlockingStub businessService;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffService;
    private final OrganizationSessionService organizationSessionService;

    @Override
    @Auth(AuthType.COMPANY)
    public void createLocation(CreateLocationParams request, StreamObserver<CreateLocationResult> responseObserver) {
        CreateLocationResponse response = businessService.createLocation(CreateLocationRequest.newBuilder()
                .setLocation(request.getLocation())
                .setTokenCompanyId(AuthContext.get().companyId())
                .setTokenStaffId(AuthContext.get().staffId())
                .build());
        responseObserver.onNext(
                CreateLocationResult.newBuilder().setId(response.getId()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateLocation(UpdateLocationParams request, StreamObserver<UpdateLocationResult> responseObserver) {
        UpdateLocationResponse response = businessService.updateLocation(UpdateLocationRequest.newBuilder()
                .setLocation(request.getLocation())
                .setTokenCompanyId(AuthContext.get().companyId())
                .setTokenStaffId(AuthContext.get().staffId())
                .build());
        responseObserver.onNext(UpdateLocationResult.newBuilder()
                .setSuccess(response.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getLocationList(GetLocationListParams request, StreamObserver<GetLocationListResult> responseObserver) {
        GetLocationListResponse response = businessService.getLocationList(GetLocationListRequest.newBuilder()
                .setTokenCompanyId(AuthContext.get().companyId())
                .setTokenStaffId(AuthContext.get().staffId())
                .build());
        responseObserver.onNext(GetLocationListResult.newBuilder()
                .addAllLocation(response.getLocationList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getLocationDetail(
            GetLocationDetailParams request, StreamObserver<GetLocationDetailResult> responseObserver) {
        GetLocationDetailResponse response = businessService.getLocationDetail(GetLocationDetailRequest.newBuilder()
                .setTokenCompanyId(AuthContext.get().companyId())
                .setId(request.getId())
                .build());
        responseObserver.onNext(GetLocationDetailResult.newBuilder()
                .setLocation(response.getLocation())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateOnlinePreference(
            UpdateOnlinePreferenceParams request, StreamObserver<UpdateOnlinePreferenceResult> responseObserver) {
        UpdateOnlinePreferenceResponse response = businessService.updateOnlinePreference(
                com.moego.idl.service.organization.v1.UpdateOnlinePreferenceRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setTokenStaffId(AuthContext.get().staffId())
                        .setOnlinePreference(request.getOnlinePreference())
                        .build());
        responseObserver.onNext(UpdateOnlinePreferenceResult.newBuilder()
                .setSuccess(response.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getWorkingHoursList(
            GetWorkingHoursListParams request, StreamObserver<GetWorkingHoursListResult> responseObserver) {
        GetWorkingHourListResponse response = businessService.getWorkingHourList(
                com.moego.idl.service.organization.v1.GetWorkingHourListRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setBusinessId(request.getBusinessId())
                        .build());
        responseObserver.onNext(GetWorkingHoursListResult.newBuilder()
                .setWorkingHours(response.getWorkingHour())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateWorkingHours(
            UpdateWorkingHoursParams request, StreamObserver<UpdateWorkingHoursResult> responseObserver) {
        UpdateWorkingHourResponse response = businessService.updateWorkingHour(
                com.moego.idl.service.organization.v1.UpdateWorkingHourRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setTokenStaffId(AuthContext.get().staffId())
                        .setBusinessId(request.getBusinessId())
                        .setWorkingHour(request.getWorkingHours())
                        .build());
        responseObserver.onNext(UpdateWorkingHoursResult.newBuilder()
                .setSuccess(response.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void addClosedDate(AddClosedDateParams request, StreamObserver<AddClosedDateResult> responseObserver) {
        AddClosedDateResponse response =
                businessService.addClosedDate(com.moego.idl.service.organization.v1.AddClosedDateRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setTokenStaffId(AuthContext.get().staffId())
                        .setBusinessId(request.getBusinessId())
                        .setClosedDate(request.getClosedDate())
                        .build());
        responseObserver.onNext(
                AddClosedDateResult.newBuilder().setId(response.getId()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateClosedDate(
            UpdateClosedDateParams request, StreamObserver<UpdateClosedDateResult> responseObserver) {
        UpdateClosedDateResponse response = businessService.updateClosedDate(
                com.moego.idl.service.organization.v1.UpdateClosedDateRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setTokenStaffId(AuthContext.get().staffId())
                        .setBusinessId(request.getBusinessId())
                        .setClosedDate(request.getClosedDate())
                        .build());
        responseObserver.onNext(UpdateClosedDateResult.newBuilder()
                .setSuccess(response.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deleteClosedDate(
            DeleteClosedDateParams request, StreamObserver<DeleteClosedDateResult> responseObserver) {
        DeleteClosedDateResponse response = businessService.deleteClosedDate(
                com.moego.idl.service.organization.v1.DeleteClosedDateRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setTokenStaffId(AuthContext.get().staffId())
                        .setBusinessId(request.getBusinessId())
                        .setId(request.getId())
                        .build());
        responseObserver.onNext(DeleteClosedDateResult.newBuilder()
                .setSuccess(response.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getClosedDateList(
            GetClosedDateListParams request, StreamObserver<GetClosedDateListResult> responseObserver) {
        GetClosedDateListResponse response = businessService.getClosedDateList(
                com.moego.idl.service.organization.v1.GetClosedDateListRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setBusinessId(request.getBusinessId())
                        .build());
        responseObserver.onNext(GetClosedDateListResult.newBuilder()
                .addAllClosedDate(response.getClosedDateList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getClosedHolidayList(
            GetClosedHolidayListParams request, StreamObserver<GetClosedHolidayListResult> responseObserver) {
        GetClosedHolidayListResponse response = businessService.getClosedHolidayList(
                com.moego.idl.service.organization.v1.GetClosedHolidayListRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setBusinessId(request.getBusinessId())
                        .build());
        responseObserver.onNext(GetClosedHolidayListResult.newBuilder()
                .addAllClosedDate(response.getClosedDateList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void editClosedHoliday(
            EditClosedHolidayParams request, StreamObserver<EditClosedHolidayResult> responseObserver) {
        EditClosedHolidayResponse response = businessService.editClosedHoliday(
                com.moego.idl.service.organization.v1.EditClosedHolidayRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setTokenStaffId(AuthContext.get().staffId())
                        .setBusinessId(request.getBusinessId())
                        .setStartDate(request.getStartDate())
                        .setEndDate(request.getEndDate())
                        .setIsClosed(request.getIsClosed())
                        .build());
        responseObserver.onNext(EditClosedHolidayResult.newBuilder()
                .setSuccess(response.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void massEditClosedHoliday(
            MassEditClosedHolidayParams request, StreamObserver<MassEditClosedHolidayResult> responseObserver) {
        MassEditClosedHolidayResponse response = businessService.massEditClosedHoliday(
                com.moego.idl.service.organization.v1.MassEditClosedHolidayRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setTokenStaffId(AuthContext.get().staffId())
                        .setBusinessId(request.getBusinessId())
                        .addAllClosedHoliday(request.getClosedHolidayList())
                        .build());
        responseObserver.onNext(MassEditClosedHolidayResult.newBuilder()
                .setSuccess(response.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void switchBusiness(SwitchBusinessParams request, StreamObserver<SwitchBusinessResult> responseObserver) {
        var sessionId = AuthContext.get().sessionId();
        var tokenCompanyId = AuthContext.get().companyId();
        var tokenStaffId = AuthContext.get().staffId();

        var businessCompanyId = businessService
                .getCompanyId(GetCompanyIdRequest.newBuilder()
                        .setBusinessId(request.getBusinessId())
                        .build())
                .getCompanyId();
        if (!tokenCompanyId.equals(businessCompanyId)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "business not found in company");
        }
        // check staff permission
        var staff = staffService
                .getStaffDetail(GetStaffDetailRequest.newBuilder()
                        .setId(tokenStaffId)
                        .setCompanyId(tokenCompanyId)
                        .build())
                .getStaff();

        if (staff.getWorkingLocationListList().stream()
                .noneMatch(location -> location.getId() == request.getBusinessId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "staff not working in this business");
        }
        organizationSessionService.doSwitch(
                sessionId, staff.getAccountId(), staff.getId(), staff.getCompanyId(), request.getBusinessId());

        responseObserver.onNext(SwitchBusinessResult.newBuilder().getDefaultInstanceForType());
        responseObserver.onCompleted();
    }
}
