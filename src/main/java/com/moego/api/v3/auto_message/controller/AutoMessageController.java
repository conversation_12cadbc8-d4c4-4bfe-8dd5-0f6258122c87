package com.moego.api.v3.auto_message.controller;

import com.moego.api.v3.auto_message.service.ActivityLogService;
import com.moego.api.v3.auto_message.service.AutoMessageService;
import com.moego.api.v3.auto_message.service.AutoMessageTaskService;
import com.moego.idl.api.auto_message.v1.AutoMessageServiceGrpc;
import com.moego.idl.api.auto_message.v1.GetAppointmentAutoMessageDetailParams;
import com.moego.idl.api.auto_message.v1.GetAppointmentAutoMessageDetailResult;
import com.moego.idl.api.auto_message.v1.GetAppointmentAutoMessageListParams;
import com.moego.idl.api.auto_message.v1.GetAppointmentAutoMessageListResult;
import com.moego.idl.api.auto_message.v1.GetAppointmentReminderDetailParams;
import com.moego.idl.api.auto_message.v1.GetAppointmentReminderDetailResult;
import com.moego.idl.api.auto_message.v1.GetAppointmentReminderListParams;
import com.moego.idl.api.auto_message.v1.GetAppointmentReminderListResult;
import com.moego.idl.api.auto_message.v1.GetOBAutoMessageListParams;
import com.moego.idl.api.auto_message.v1.GetOBAutoMessageListResult;
import com.moego.idl.api.auto_message.v1.GetPayAutoMessageDetailParams;
import com.moego.idl.api.auto_message.v1.GetPayAutoMessageDetailResult;
import com.moego.idl.api.auto_message.v1.GetPayAutoMessageListParams;
import com.moego.idl.api.auto_message.v1.GetPayAutoMessageListResult;
import com.moego.idl.api.auto_message.v1.GetReminderDetailParams;
import com.moego.idl.api.auto_message.v1.GetReminderDetailResult;
import com.moego.idl.api.auto_message.v1.GetReminderListParams;
import com.moego.idl.api.auto_message.v1.GetReminderListResult;
import com.moego.idl.api.auto_message.v1.ListAppointmentReminderTaskParams;
import com.moego.idl.api.auto_message.v1.ListAppointmentReminderTaskResult;
import com.moego.idl.api.auto_message.v1.ListPetBirthdayReminderTaskParams;
import com.moego.idl.api.auto_message.v1.ListPetBirthdayReminderTaskResult;
import com.moego.idl.api.auto_message.v1.ListRebookReminderTaskParams;
import com.moego.idl.api.auto_message.v1.ListRebookReminderTaskResult;
import com.moego.idl.api.auto_message.v1.MessageTransferByBusinessParams;
import com.moego.idl.api.auto_message.v1.MessageTransferByBusinessResult;
import com.moego.idl.api.auto_message.v1.UpdateAppointmentAutoMessageParams;
import com.moego.idl.api.auto_message.v1.UpdateAppointmentAutoMessageResult;
import com.moego.idl.api.auto_message.v1.UpdateAppointmentReminderParams;
import com.moego.idl.api.auto_message.v1.UpdateAppointmentReminderResult;
import com.moego.idl.api.auto_message.v1.UpdatePayAutoMessageParams;
import com.moego.idl.api.auto_message.v1.UpdatePayAutoMessageResult;
import com.moego.idl.api.auto_message.v1.UpdateReminderParams;
import com.moego.idl.api.auto_message.v1.UpdateReminderResult;
import com.moego.idl.models.auto_message.v1.AppointmentAutoMsgConfigModel;
import com.moego.idl.models.auto_message.v1.AutoMessageConfigModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.message.v1.MessageTemplateUseCase;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;

@GrpcService
@RequiredArgsConstructor
public class AutoMessageController extends AutoMessageServiceGrpc.AutoMessageServiceImplBase {
    private final AutoMessageService autoMessageService;
    private final AutoMessageTaskService autoMessageTaskService;
    private final ActivityLogService activityLogService;

    // 支持全量迁移或迁移若干 business。支持重复操作
    @Override
    @Auth(AuthType.COMPANY)
    public void messageTransferByBusiness(
            MessageTransferByBusinessParams request, StreamObserver<MessageTransferByBusinessResult> responseObserver) {
        if (!request.getToken().equals("moego_new_auto_msg")) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "invalid token");
        }
        if (!CollectionUtils.isEmpty(request.getBusinessIdsList())) {
            autoMessageService.transToNewAutoMsg(request.getBusinessIdsList());
        } else {
            autoMessageService.transAllToNewAutoMsg();
        }
        responseObserver.onNext(MessageTransferByBusinessResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getOBAutoMessageList(
            GetOBAutoMessageListParams request, StreamObserver<GetOBAutoMessageListResult> responseObserver) {
        List<AppointmentAutoMsgConfigModel> configs = autoMessageService.getAppointmentAutoMsgConfigList(
                AuthContext.get().companyId(), request.getBusinessId(), autoMessageService.getUserCaseForOBAutoMsg());

        responseObserver.onNext(GetOBAutoMessageListResult.newBuilder()
                .addAllAutoMessages(autoMessageService.buildOBAutoMessageListView(configs))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getAppointmentAutoMessageList(
            GetAppointmentAutoMessageListParams request,
            StreamObserver<GetAppointmentAutoMessageListResult> responseObserver) {
        List<AppointmentAutoMsgConfigModel> configs = autoMessageService.getAppointmentAutoMsgConfigList(
                AuthContext.get().companyId(), request.getBusinessId(), autoMessageService.getUserCaseForAPPTAutoMsg());
        responseObserver.onNext(GetAppointmentAutoMessageListResult.newBuilder()
                .addAllAutoMessages(autoMessageService.buildAppointmentAutoMessageListView(configs))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getAppointmentAutoMessageDetail(
            GetAppointmentAutoMessageDetailParams request,
            StreamObserver<GetAppointmentAutoMessageDetailResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        AppointmentAutoMsgConfigModel config =
                autoMessageService.getAppointmentAutoMsgConfigDetail(companyId, request.getId());
        responseObserver.onNext(GetAppointmentAutoMessageDetailResult.newBuilder()
                .setAutoMessage(autoMessageService.buildAppointmentAutoMessageDetailView(
                        config, autoMessageService.getTemplatesByAppointmentConfig(config)))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateAppointmentAutoMessage(
            UpdateAppointmentAutoMessageParams request,
            StreamObserver<UpdateAppointmentAutoMessageResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        AppointmentAutoMsgConfigModel config = autoMessageService.updateAppointmentAutoMsgConfig(companyId, request);
        activityLogService.addAutoMsgUpdateLog(
                companyId, config.getBusinessId(), AuthContext.get().staffId(), request.getId(), request);
        responseObserver.onNext(UpdateAppointmentAutoMessageResult.newBuilder()
                .setAutoMessage(autoMessageService.buildAppointmentAutoMessageDetailView(
                        config, autoMessageService.getTemplatesByAppointmentConfig(config)))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getPayAutoMessageList(
            GetPayAutoMessageListParams request, StreamObserver<GetPayAutoMessageListResult> responseObserver) {
        List<AutoMessageConfigModel> configs = autoMessageService.getAutoMessageList(
                AuthContext.get().companyId(),
                request.getBusinessId(),
                autoMessageService.getUserCaseForPaymentAutoMsg());
        responseObserver.onNext(GetPayAutoMessageListResult.newBuilder()
                .addAllAutoMessages(autoMessageService.buildPayAutoMessageListView(configs))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getPayAutoMessageDetail(
            GetPayAutoMessageDetailParams request, StreamObserver<GetPayAutoMessageDetailResult> responseObserver) {
        AutoMessageConfigModel config =
                autoMessageService.getAutoMessageDetail(AuthContext.get().companyId(), request.getId());
        responseObserver.onNext(GetPayAutoMessageDetailResult.newBuilder()
                .setAutoMessage(autoMessageService.getPayAutoMessageDetailView(config))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updatePayAutoMessage(
            UpdatePayAutoMessageParams request, StreamObserver<UpdatePayAutoMessageResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        AutoMessageConfigModel config = autoMessageService.updatePayAutoMessageConfig(companyId, request);
        activityLogService.addAutoMsgUpdateLog(
                companyId, config.getBusinessId(), AuthContext.get().staffId(), request.getId(), request);
        responseObserver.onNext(UpdatePayAutoMessageResult.newBuilder()
                .setAutoMessage(autoMessageService.getPayAutoMessageDetailView(config))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getAppointmentReminderList(
            GetAppointmentReminderListParams request,
            StreamObserver<GetAppointmentReminderListResult> responseObserver) {
        List<MessageTemplateUseCase> useCases = new ArrayList<>(autoMessageService.getUserCaseForAPPTReminder());
        if (!CollectionUtils.isEmpty(request.getUseCasesList())) {
            useCases.retainAll(request.getUseCasesList());
        }
        List<AppointmentAutoMsgConfigModel> configs = autoMessageService.getAppointmentAutoMsgConfigList(
                AuthContext.get().companyId(), request.getBusinessId(), useCases);
        responseObserver.onNext(GetAppointmentReminderListResult.newBuilder()
                .addAllReminders(autoMessageService.buildAppointmentReminderListView(configs))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getAppointmentReminderDetail(
            GetAppointmentReminderDetailParams request,
            StreamObserver<GetAppointmentReminderDetailResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        AppointmentAutoMsgConfigModel config =
                autoMessageService.getAppointmentAutoMsgConfigDetail(companyId, request.getId());
        responseObserver.onNext(GetAppointmentReminderDetailResult.newBuilder()
                .setReminder(autoMessageService.buildAppointmentReminderDetailView(
                        config, autoMessageService.getTemplatesByAppointmentConfig(config)))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateAppointmentReminder(
            UpdateAppointmentReminderParams request, StreamObserver<UpdateAppointmentReminderResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        AppointmentAutoMsgConfigModel config = autoMessageService.updateAppointmentReminderConfig(companyId, request);
        activityLogService.addReminderUpdateLog(
                companyId, config.getBusinessId(), AuthContext.get().staffId(), request.getId(), request);
        responseObserver.onNext(UpdateAppointmentReminderResult.newBuilder()
                .setReminder(autoMessageService.buildAppointmentReminderDetailView(
                        config, autoMessageService.getTemplatesByAppointmentConfig(config)))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getReminderList(GetReminderListParams request, StreamObserver<GetReminderListResult> responseObserver) {
        List<AutoMessageConfigModel> configs = autoMessageService.getAutoMessageList(
                AuthContext.get().companyId(),
                request.getBusinessId(),
                autoMessageService.getUserCaseForAdditionalReminder());
        responseObserver.onNext(GetReminderListResult.newBuilder()
                .addAllReminders(configs.stream()
                        .map(autoMessageService::buildReminderListView)
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getReminderDetail(
            GetReminderDetailParams request, StreamObserver<GetReminderDetailResult> responseObserver) {
        AutoMessageConfigModel config =
                autoMessageService.getAutoMessageDetail(AuthContext.get().companyId(), request.getId());
        responseObserver.onNext(GetReminderDetailResult.newBuilder()
                .setReminder(autoMessageService.getReminderDetailView(config))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateReminder(UpdateReminderParams request, StreamObserver<UpdateReminderResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        AutoMessageConfigModel config = autoMessageService.updateReminderConfig(companyId, request);
        activityLogService.addReminderUpdateLog(
                companyId, config.getBusinessId(), AuthContext.get().staffId(), request.getId(), request);
        responseObserver.onNext(UpdateReminderResult.newBuilder()
                .setReminder(autoMessageService.getReminderDetailView(config))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listAppointmentReminderTask(
            ListAppointmentReminderTaskParams request,
            StreamObserver<ListAppointmentReminderTaskResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        long businessId = request.getBusinessId();

        var result = autoMessageTaskService.listAppointmentReminderTask(companyId, businessId, request.getPagination());

        responseObserver.onNext(ListAppointmentReminderTaskResult.newBuilder()
                .addAllAppointmentReminderTasks(result.getAppointmentReminderTasksList())
                .setPagination(result.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetBirthdayReminderTask(
            ListPetBirthdayReminderTaskParams request,
            StreamObserver<ListPetBirthdayReminderTaskResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        long businessId = request.getBusinessId();

        var result = autoMessageTaskService.listPetBirthdayReminderTask(companyId, businessId, request.getPagination());

        responseObserver.onNext(ListPetBirthdayReminderTaskResult.newBuilder()
                .addAllPetBirthdayReminderTasks(result.getPetBirthdayReminderTasksList())
                .setPagination(result.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listRebookReminderTask(
            ListRebookReminderTaskParams request, StreamObserver<ListRebookReminderTaskResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        long businessId = request.getBusinessId();

        var result = autoMessageTaskService.listRebookReminderTask(companyId, businessId, request.getPagination());

        responseObserver.onNext(ListRebookReminderTaskResult.newBuilder()
                .addAllRebookReminderTasks(result.getRebookReminderTasksList())
                .setPagination(result.getPagination())
                .build());
        responseObserver.onCompleted();
    }
}
