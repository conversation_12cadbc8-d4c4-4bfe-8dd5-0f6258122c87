package com.moego.api.v3.auto_message.service;

import com.google.protobuf.Timestamp;
import com.google.type.CalendarPeriod;
import com.google.type.Date;
import com.moego.common.dto.PageDTO;
import com.moego.common.enums.ApptReminderBy;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.api.auto_message.v1.ListAppointmentReminderTaskResult;
import com.moego.idl.api.auto_message.v1.ListPetBirthdayReminderTaskResult;
import com.moego.idl.api.auto_message.v1.ListRebookReminderTaskResult;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.idl.models.message.v1.MessageTemplateUseCase;
import com.moego.idl.models.message.v1.MessageType;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.GetAppointmentListRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentListResponse;
import com.moego.idl.service.appointment.v1.GetPetDetailListRequest;
import com.moego.idl.service.appointment.v1.GetPetDetailListResponse;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.service.auto_message.v1.AutoMessageTaskServiceGrpc;
import com.moego.idl.service.business_customer.v1.BatchGetPetRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.utils.v1.TimePeriod;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.server.customer.dto.CustomerPetReminderDTO;
import com.moego.server.customer.dto.GroomingCustomerInfoDTO;
import com.moego.server.grooming.dto.CustomerRebookReminderDTO;
import com.moego.server.grooming.dto.MoeGroomingAppointmentDTO;
import com.moego.server.message.api.IMessageService;
import com.moego.server.message.dto.ApptReminderDetailDTO;
import com.moego.server.message.dto.BirthdayReminderDetailDTO;
import com.moego.server.message.dto.MessageDetailDTO;
import com.moego.server.message.dto.RebookReminderDetailDTO;
import com.moego.server.message.dto.ReminderDetailDTO;
import com.moego.server.message.enums.ReminderTypeEnum;
import com.moego.server.message.params.ReminderDetailParams;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class AutoMessageTaskService {
    private final AutoMessageTaskServiceGrpc.AutoMessageTaskServiceBlockingStub autoMessageTaskClient;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentServiceClient;
    private final PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailServiceClient;
    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub businessCustomerPetService;
    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub businessCustomerServiceClient;
    private final IMessageService iMessageClient;

    public ListAppointmentReminderTaskResult listAppointmentReminderTask(
            Long companyId, Long businessId, PaginationRequest pagination) {

        ReminderDetailParams reminderDetailParams = new ReminderDetailParams();
        reminderDetailParams.setCompanyId(companyId);
        reminderDetailParams.setBusinessId(businessId.intValue());
        reminderDetailParams.setReminderTypeEnum(ReminderTypeEnum.APPOINT_FIRST);
        reminderDetailParams.setPageNo(pagination.getPageNum());
        reminderDetailParams.setPageSize(pagination.getPageSize());
        // 具体status 参数根据不同的reminder type 在查询时进行设置
        reminderDetailParams.setStatus(null);
        PageDTO<ApptReminderDetailDTO> result = iMessageClient.detailAppointmentReal(reminderDetailParams);
        return ListAppointmentReminderTaskResult.newBuilder()
                .addAllAppointmentReminderTasks(result.getDataList().stream()
                        .map(data -> {
                            GroomingCustomerInfoDTO customerInfo = data.getCustomerInfo();
                            MoeGroomingAppointmentDTO detail = data.getDetail();
                            MessageDetailDTO sendDetail = data.getSendDetail();

                            Integer planSendTime = Objects.nonNull(data.getActualSendTime())
                                    ? data.getActualSendTime()
                                    : data.getPlanSendTime();
                            return ListAppointmentReminderTaskResult.AppointmentReminderTaskView.newBuilder()
                                    .setId(data.getId())
                                    .setCustomerName(getCustomerName(customerInfo))
                                    .setAppointmentStartDate(detail.getAppointmentDate())
                                    .setAppointmentStartTime(detail.getAppointmentStartTime())
                                    .setAppointmentEndDate(detail.getAppointmentEndDate())
                                    .setAppointmentEndTime(detail.getAppointmentEndTime())
                                    .setStatus(getStatusString(sendDetail))
                                    .addAllMethods(customerInfo.getApptReminderByList().stream()
                                            .map(MessageType::forNumber)
                                            .toList())
                                    .setUseCase(getUseCase(data.getReminderType()))
                                    .setScheduledTime(Timestamp.newBuilder()
                                            .setSeconds(planSendTime)
                                            .build())
                                    .setAppointmentId(data.getId())
                                    .setCustomerId(customerInfo.getCustomerId())
                                    .build();
                        })
                        .toList())
                .setPagination(PaginationResponse.newBuilder()
                        .setPageNum(result.getPageNo())
                        .setPageSize(result.getPageSize())
                        .setTotal(Math.toIntExact(result.getTotal())))
                .build();

        // LocalDateTime startTime = getStartTimeByBusiness(businessId);
        // LocalDateTime endTime = startTime.plusDays(7);
        //
        // ListAutoMessageTaskResponse response =
        //     autoMessageTaskClient.listAutoMessageTask(ListAutoMessageTaskRequest.newBuilder()
        //         .setCompanyId(companyId)
        //         .setBusinessId(businessId)
        //         .setUseCases(MessageTemplateUseCaseEnumList.newBuilder()
        //             .addValues(MessageTemplateUseCaseEnum.USE_CASE_FIRST_REMINDER)
        //             .addValues(MessageTemplateUseCaseEnum.USE_CASE_SECOND_REMINDER)
        //             .addValues(MessageTemplateUseCaseEnum.USE_CASE_GENERAL_REMINDER)
        //             .build())
        //         .setStartTime(Timestamp.newBuilder()
        //             .setSeconds(startTime.getSecond())
        //             .build())
        //         .setEndTime(Timestamp.newBuilder()
        //             .setSeconds(endTime.getSecond())
        //             .build())
        //         .setPagination(pagination)
        //         .build());
        //
        // List<AutoMessageTaskModel> tasksList = response.getTasksList();
        // if (CollectionUtils.isEmpty(tasksList)) {
        //     return ListAppointmentReminderTaskResult.newBuilder()
        //         .setPagination(response.getPagination())
        //         .build();
        // }
        //
        // // appointment
        // List<Long> appointmentIdList = tasksList.stream()
        //     .map(AutoMessageTaskModel::getObjectId)
        //     .distinct()
        //     .toList();
        // GetAppointmentListResponse appointmentResponse =
        //     appointmentServiceClient.getAppointmentList(GetAppointmentListRequest.newBuilder()
        //         .setCompanyId(companyId)
        //         .setBusinessId(businessId)
        //         .addAllAppointmentId(appointmentIdList)
        //         .build());
        // List<AppointmentModel> appointmentsList = appointmentResponse.getAppointmentsList();
        // if (CollectionUtils.isEmpty(appointmentsList)) {
        //     return ListAppointmentReminderTaskResult.newBuilder()
        //         .setPagination(response.getPagination())
        //         .build();
        // }
        // Map<Long, AppointmentModel> appointmentMap =
        //     appointmentsList.stream().collect(Collectors.toMap(AppointmentModel::getId, Function.identity()));
        //
        // // customer
        // List<Long> customerIdList = appointmentsList.stream()
        //     .map(AppointmentModel::getCustomerId)
        //     .distinct()
        //     .toList();
        // Map<Long, BusinessCustomerModel> customerMap = businessCustomerServiceClient
        //     .batchGetCustomer(BatchGetCustomerRequest.newBuilder()
        //         .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
        //         .addAllIds(customerIdList)
        //         .build())
        //     .getCustomersList()
        //     .stream()
        //     .collect(Collectors.toMap(BusinessCustomerModel::getId, Function.identity()));
        //
        // return ListAppointmentReminderTaskResult.newBuilder()
        //     .addAllAppointmentReminderTasks(tasksList.stream()
        //         .map(task -> {
        //             long appointment = task.getObjectId();
        //             AppointmentModel appointmentModel = appointmentMap.get(appointment);
        //
        //             return ListAppointmentReminderTaskResult.AppointmentReminderTaskView.newBuilder()
        //                 .setId(task.getId())
        //                 .setCustomerName(getCustomerName(customerMap.get(appointmentModel.getCustomerId())))
        //                 .setAppointmentStartDate(appointmentModel.getAppointmentDate())
        //                 .setAppointmentStartTime(appointmentModel.getAppointmentStartTime())
        //                 .setAppointmentEndDate(appointmentModel.getAppointmentDate())
        //                 .setAppointmentEndTime(appointmentModel.getAppointmentEndTime())
        //                 .setStatus(getStatusString(task.getStatus()))
        //                 .setMethod(getMethod(customerMap.get(appointmentModel.getCustomerId())))
        //                 .setUseCase(task.getUseCase())
        //                 .setScheduledTime(task.getSendTime())
        //                 .setAppointmentId(appointmentModel.getId())
        //                 .setCustomerId(appointmentModel.getCustomerId())
        //                 .build();
        //         })
        //         .toList())
        //     .setPagination(response.getPagination())
        //     .build();
    }

    // private MessageTypeList getMethod(BusinessCustomerModel customer) {
    //     MessageTypeList.Builder builder = MessageTypeList.newBuilder();
    //     customer.getApptReminderByList().forEach(method -> builder.addMessageTypes(MessageType.forNumber(method)));
    //     return builder.build();
    // }

    // private String getStatusString(AutoMessageTaskStatus status) {
    //     return switch (status) {
    //         case AUTO_MESSAGE_TASK_STATUS_INIT, AUTO_MESSAGE_TASK_STATUS_SENDING -> "Scheduled";
    //         case AUTO_MESSAGE_TASK_STATUS_SUCCEED -> "Sent";
    //         case AUTO_MESSAGE_TASK_STATUS_FAILED -> "Failed";
    //         case AUTO_MESSAGE_TASK_STATUS_CANCELLED_BY_STAFF,
    //                 AUTO_MESSAGE_TASK_STATUS_CANCELLED_BY_SYSTEM -> "Dismissed";
    //         default -> "Unknown";
    //     };
    // }

    private String getStatusString(MessageDetailDTO sendDetail) {
        if (Objects.isNull(sendDetail)) {
            return "Scheduled";
        }
        if (Objects.equals(1, sendDetail.getIsSuccessed())) {
            return "Sent";
        } else {
            return "Failed";
        }
    }

    private MessageTemplateUseCase getUseCase(Integer reminderType) {
        return switch (reminderType) {
            case 1 -> MessageTemplateUseCase.USE_CASE_FIRST_REMINDER;
            case 2 -> MessageTemplateUseCase.USE_CASE_SECOND_REMINDER;
            case 3 -> MessageTemplateUseCase.USE_CASE_PET_BIRTHDAY;
            case 4 -> MessageTemplateUseCase.USE_CASE_REBOOK_REMINDER;
            case 7 -> MessageTemplateUseCase.USE_CASE_GENERAL_REMINDER;
            default -> MessageTemplateUseCase.USE_CASE_UNSPECIFIED;
        };
    }

    public ListPetBirthdayReminderTaskResult listPetBirthdayReminderTask(
            Long companyId, Long businessId, PaginationRequest pagination) {

        ReminderDetailParams reminderDetailParams = new ReminderDetailParams();
        reminderDetailParams.setCompanyId(companyId);
        reminderDetailParams.setBusinessId(businessId.intValue());
        reminderDetailParams.setReminderTypeEnum(ReminderTypeEnum.PET_BIRTHDAY);
        reminderDetailParams.setPageNo(pagination.getPageNum());
        reminderDetailParams.setPageSize(pagination.getPageSize());
        PageDTO<BirthdayReminderDetailDTO> result = iMessageClient.detailPetBirthday(reminderDetailParams);

        return ListPetBirthdayReminderTaskResult.newBuilder()
                .addAllPetBirthdayReminderTasks(result.getDataList().stream()
                        .map(data -> {
                            CustomerPetReminderDTO pet = data.getDetail();
                            LocalDate petBirthday = LocalDate.parse(pet.getBirthday());
                            Integer planSendTime = Objects.nonNull(data.getActualSendTime())
                                    ? data.getActualSendTime()
                                    : data.getPlanSendTime();
                            LocalDateTime localDateTime = LocalDateTime.ofEpochSecond(planSendTime, 0, ZoneOffset.UTC);
                            LocalDateTime realPlanSendTime =
                                    localDateTime.withYear(LocalDateTime.now().getYear());
                            return ListPetBirthdayReminderTaskResult.PetBirthdayReminderTaskView.newBuilder()
                                    .setId(data.getId())
                                    .setCustomerName(getCustomerName(pet))
                                    .setPetName(pet.getPetName())
                                    .setPetType(PetType.forNumber(pet.getPetTypeId()))
                                    .setPetAvatar(pet.getAvatarPath())
                                    .setPetBreed(pet.getBreed())
                                    .setPetBirthday(Date.newBuilder()
                                            .setYear(petBirthday.getYear())
                                            .setMonth(petBirthday.getMonthValue())
                                            .setDay(petBirthday.getDayOfMonth())
                                            .build())
                                    .setScheduledTime(Timestamp.newBuilder()
                                            .setSeconds(realPlanSendTime.toEpochSecond(ZoneOffset.UTC))
                                            .build())
                                    .setCustomerId(pet.getCustomerId())
                                    .setPetId(pet.getPetId())
                                    .setUseCase(MessageTemplateUseCase.USE_CASE_PET_BIRTHDAY)
                                    .build();
                        })
                        .toList())
                .setPagination(PaginationResponse.newBuilder()
                        .setPageNum(result.getPageNo())
                        .setPageSize(result.getPageSize())
                        .setTotal(Math.toIntExact(result.getTotal())))
                .build();

        // LocalDateTime startTime = getStartTimeByBusiness(businessId);
        // LocalDateTime endTime = startTime.plusDays(30);
        //
        // ListAutoMessageTaskResponse response =
        //     autoMessageTaskClient.listAutoMessageTask(ListAutoMessageTaskRequest.newBuilder()
        //         .setCompanyId(companyId)
        //         .setBusinessId(businessId)
        //         .setUseCases(MessageTemplateUseCaseEnumList.newBuilder()
        //             .addValues(MessageTemplateUseCaseEnum.USE_CASE_PET_BIRTHDAY)
        //             .build())
        //         .setStartTime(Timestamp.newBuilder()
        //             .setSeconds(startTime.getSecond())
        //             .build())
        //         .setEndTime(Timestamp.newBuilder()
        //             .setSeconds(endTime.getSecond())
        //             .build())
        //         .setPagination(pagination)
        //         .build());
        //
        // List<AutoMessageTaskModel> tasksList = response.getTasksList();
        // if (CollectionUtils.isEmpty(tasksList)) {
        //     return ListPetBirthdayReminderTaskResult.newBuilder()
        //         .setPagination(response.getPagination())
        //         .build();
        // }
        //
        // // pet
        // List<Long> petIdList = tasksList.stream()
        //     .map(AutoMessageTaskModel::getObjectId)
        //     .distinct()
        //     .toList();
        // Map<Long, BusinessCustomerPetModel> petMap = businessCustomerPetService
        //     .batchGetPet(BatchGetPetRequest.newBuilder()
        //         .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
        //         .addAllIds(petIdList)
        //         .build())
        //     .getPetsList()
        //     .stream()
        //     .collect(Collectors.toMap(BusinessCustomerPetModel::getId, Function.identity()));
        //
        // // customer
        // List<Long> customerIdList = petMap.values().stream()
        //     .map(BusinessCustomerPetModel::getCustomerId)
        //     .distinct()
        //     .toList();
        // Map<Long, BusinessCustomerModel> customerMap = businessCustomerServiceClient
        //     .batchGetCustomer(BatchGetCustomerRequest.newBuilder()
        //         .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
        //         .addAllIds(customerIdList)
        //         .build())
        //     .getCustomersList()
        //     .stream()
        //     .collect(Collectors.toMap(BusinessCustomerModel::getId, Function.identity()));
        //
        // return ListPetBirthdayReminderTaskResult.newBuilder()
        //     .addAllPetBirthdayReminderTasks(tasksList.stream()
        //         .map(task -> {
        //             long petId = task.getObjectId();
        //             BusinessCustomerPetModel pet = petMap.get(petId);
        //             BusinessCustomerModel customer = customerMap.get(pet.getCustomerId());
        //
        //             return ListPetBirthdayReminderTaskResult.PetBirthdayReminderTaskView.newBuilder()
        //                 .setId(task.getId())
        //                 .setCustomerName(getCustomerName(customer))
        //                 .setPetName(pet.getPetName())
        //                 .setPetType(pet.getPetType())
        //                 .setPetAvatar(pet.getAvatarPath())
        //                 .setPetBreed(pet.getBreed())
        //                 .setPetBirthday(pet.getBirthday())
        //                 .setScheduledTime(task.getSendTime())
        //                 .setCustomerId(customer.getId())
        //                 .setPetId(pet.getId())
        //                 .build();
        //         })
        //         .toList())
        //     .setPagination(response.getPagination())
        //     .build();
    }

    public ListRebookReminderTaskResult listRebookReminderTask(
            Long companyId, Long businessId, PaginationRequest pagination) {

        ReminderDetailParams reminderDetailParams = new ReminderDetailParams();
        reminderDetailParams.setCompanyId(companyId);
        reminderDetailParams.setBusinessId(businessId.intValue());
        reminderDetailParams.setReminderTypeEnum(ReminderTypeEnum.REBOOK);
        reminderDetailParams.setPageNo(pagination.getPageNum());
        reminderDetailParams.setPageSize(pagination.getPageSize());
        PageDTO<RebookReminderDetailDTO> result = iMessageClient.rebookReminderDetail(reminderDetailParams);

        // appointment
        List<Long> appointmentIds = result.getDataList().stream()
                .map(ReminderDetailDTO::getDetail)
                .map(CustomerRebookReminderDTO::getGroomingId)
                .map(Long::valueOf)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return ListRebookReminderTaskResult.newBuilder()
                    .setPagination(PaginationResponse.newBuilder()
                            .setPageNum(result.getPageNo())
                            .setPageSize(result.getPageSize())
                            .setTotal(Math.toIntExact(result.getTotal())))
                    .build();
        }

        GetAppointmentListResponse appointmentResponse =
                appointmentServiceClient.getAppointmentList(GetAppointmentListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .addAllAppointmentId(appointmentIds)
                        .build());
        Map<Long, Long> appointmentToCustomerMap = appointmentResponse.getAppointmentsList().stream()
                .collect(Collectors.toMap(AppointmentModel::getId, AppointmentModel::getCustomerId, (a, b) -> b));

        // get grooming service's start date
        GetPetDetailListResponse petDetailListResponse =
                petDetailServiceClient.getPetDetailList(GetPetDetailListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllAppointmentIds(appointmentIds)
                        .build());
        Map<Long, List<PetDetailModel>> customerToPetDetailMap = petDetailListResponse.getPetDetailsList().stream()
                .filter(petDetailModel -> Objects.equals(ServiceItemType.GROOMING, petDetailModel.getServiceItemType()))
                .collect(Collectors.groupingBy(
                        petDetailModel -> appointmentToCustomerMap.get(petDetailModel.getGroomingId())));

        // pet
        List<Long> petIdList = customerToPetDetailMap.values().stream()
                .flatMap(List::stream)
                .map(PetDetailModel::getPetId)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(petIdList)) {
            return ListRebookReminderTaskResult.newBuilder()
                    .setPagination(PaginationResponse.newBuilder()
                            .setPageNum(result.getPageNo())
                            .setPageSize(result.getPageSize())
                            .setTotal(Math.toIntExact(result.getTotal())))
                    .build();
        }
        Map<Long, BusinessCustomerPetModel> petMap = businessCustomerPetService
                .batchGetPet(BatchGetPetRequest.newBuilder()
                        .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                        .addAllIds(petIdList)
                        .build())
                .getPetsList()
                .stream()
                .collect(Collectors.toMap(BusinessCustomerPetModel::getId, Function.identity()));

        return ListRebookReminderTaskResult.newBuilder()
                .addAllRebookReminderTasks(result.getDataList().stream()
                        .map(data -> {
                            CustomerRebookReminderDTO customer = data.getDetail();
                            LocalDate expectedDate = LocalDate.parse(customer.getExpectedServiceDate());
                            LocalDateTime lastStartDateTime = LocalDate.parse(customer.getAppointmentDate())
                                    .atStartOfDay()
                                    .plusMinutes(customer.getAppointmentStartTime());

                            String avatarPath =
                                    Objects.nonNull(customer.getAvatarPath()) ? customer.getAvatarPath() : "";
                            return ListRebookReminderTaskResult.RebookReminderTaskView.newBuilder()
                                    .setId(data.getId())
                                    .setCustomerName(getCustomerName(customer))
                                    .addAllPetNames(getPetNames(
                                            petMap,
                                            Optional.ofNullable(customerToPetDetailMap.get(customer.getCustomerId()
                                                            .longValue()))
                                                    .map(l -> l.stream()
                                                            .map(PetDetailModel::getPetId)
                                                            .toList())
                                                    .orElse(List.of())))
                                    .setCustomerAvatar(avatarPath)
                                    .setExpectedDate(Date.newBuilder()
                                            .setYear(expectedDate.getYear())
                                            .setMonth(expectedDate.getMonthValue())
                                            .setDay(expectedDate.getDayOfMonth())
                                            .build())
                                    .setLastAppointmentDateTime(Timestamp.newBuilder()
                                            .setSeconds(lastStartDateTime.toEpochSecond(ZoneOffset.UTC))
                                            .build())
                                    .setPreferredGroomingFrequency(TimePeriod.newBuilder()
                                            .setPeriod(CalendarPeriod.DAY)
                                            .setValue(customer.getPreferredFrequencyDay()))
                                    .setCustomerId(customer.getCustomerId())
                                    .setUseCase(MessageTemplateUseCase.USE_CASE_REBOOK_REMINDER)
                                    .build();
                        })
                        .toList())
                .setPagination(PaginationResponse.newBuilder()
                        .setPageNum(result.getPageNo())
                        .setPageSize(result.getPageSize())
                        .setTotal(Math.toIntExact(result.getTotal())))
                .build();

        // LocalDateTime startTime = getStartTimeByBusiness(businessId);
        // LocalDateTime endTime = startTime.plusDays(30);
        //
        // ListAutoMessageTaskResponse response =
        //     autoMessageTaskClient.listAutoMessageTask(ListAutoMessageTaskRequest.newBuilder()
        //         .setCompanyId(companyId)
        //         .setBusinessId(businessId)
        //         .setUseCases(MessageTemplateUseCaseEnumList.newBuilder()
        //             .addValues(MessageTemplateUseCaseEnum.USE_CASE_REBOOK_REMINDER)
        //             .build())
        //         .setStartTime(Timestamp.newBuilder()
        //             .setSeconds(startTime.getSecond())
        //             .build())
        //         .setEndTime(Timestamp.newBuilder()
        //             .setSeconds(endTime.getSecond())
        //             .build())
        //         .setPagination(pagination)
        //         .build());
        //
        // List<AutoMessageTaskModel> tasksList = response.getTasksList();
        // if (CollectionUtils.isEmpty(tasksList)) {
        //     return ListRebookReminderTaskResult.newBuilder()
        //         .setPagination(response.getPagination())
        //         .build();
        // }
        //
        // // customer
        // List<Long> customerIdList = tasksList.stream()
        //     .map(AutoMessageTaskModel::getObjectId)
        //     .distinct()
        //     .toList();
        // Map<Long, BusinessCustomerModel> customerMap = businessCustomerServiceClient
        //     .batchGetCustomer(BatchGetCustomerRequest.newBuilder()
        //         .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
        //         .addAllIds(customerIdList)
        //         .build())
        //     .getCustomersList()
        //     .stream()
        //     .collect(Collectors.toMap(BusinessCustomerModel::getId, Function.identity()));
        //
        // // last appointment
        // GetCustomerLastAppointmentResponse customerLastAppointmentResponse =
        //     appointmentServiceClient.getCustomerLastAppointment(GetCustomerLastAppointmentRequest.newBuilder()
        //         .setCompanyId(companyId)
        //         .addAllCustomerId(customerIdList)
        //         .setFilter(GetCustomerLastAppointmentRequest.Filter.newBuilder()
        //             .addServiceItemTypes(ServiceItemType.GROOMING)
        //             .setFilterBookingRequest(true)
        //             .setFilterNoStartTime(true)
        //             .build())
        //         .build());
        // Map<Long, AppointmentModel> customerLastAppointmentMap =
        //     customerLastAppointmentResponse.getCustomerLastAppointmentMap();
        // Map<Long, Long> appointmentToCustomerMap = customerLastAppointmentMap.values().stream()
        //     .collect(Collectors.toMap(AppointmentModel::getId, AppointmentModel::getCustomerId, (a, b) -> b));
        //
        // // get grooming service's start date
        // List<Long> appointmentIds = customerLastAppointmentMap.values().stream()
        //     .map(AppointmentModel::getId)
        //     .distinct()
        //     .toList();
        // GetPetDetailListResponse petDetailListResponse =
        //     petDetailServiceClient.getPetDetailList(GetPetDetailListRequest.newBuilder()
        //         .setCompanyId(companyId)
        //         .addAllAppointmentIds(appointmentIds)
        //         .build());
        // Map<Long, List<PetDetailModel>> customerToPetDetailMap = petDetailListResponse.getPetDetailsList().stream()
        //     .filter(petDetailModel -> Objects.equals(ServiceItemType.GROOMING, petDetailModel.getServiceItemType()))
        //     .collect(Collectors.groupingBy(
        //         petDetailModel -> appointmentToCustomerMap.get(petDetailModel.getGroomingId())));
        //
        // // pet
        // List<Long> petIdList = customerToPetDetailMap.values().stream()
        //     .flatMap(List::stream)
        //     .map(PetDetailModel::getPetId)
        //     .distinct()
        //     .toList();
        // Map<Long, BusinessCustomerPetModel> petMap = businessCustomerPetService
        //     .batchGetPet(BatchGetPetRequest.newBuilder()
        //         .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
        //         .addAllIds(petIdList)
        //         .build())
        //     .getPetsList()
        //     .stream()
        //     .collect(Collectors.toMap(BusinessCustomerPetModel::getId, Function.identity()));
        //
        // return ListRebookReminderTaskResult.newBuilder()
        //     .addAllRebookReminderTasks(tasksList.stream()
        //         .map(task -> {
        //             long customerId = task.getObjectId();
        //
        //             BusinessCustomerModel customer = customerMap.get(customerId);
        //             String lastStartDate = customerToPetDetailMap
        //                 .get(customerId)
        //                 .get(0)
        //                 .getStartDate();
        //             int lastStartTime = customerToPetDetailMap
        //                 .get(customerId)
        //                 .get(0)
        //                 .getStartTime();
        //             LocalDateTime lastStartDateTime = LocalDate.parse(lastStartDate)
        //                 .atStartOfDay()
        //                 .plusMinutes(lastStartTime);
        //
        //             TimePeriod preferredGroomingFrequency = customer.getPreferredGroomingFrequency();
        //             int days =
        //                 switch (preferredGroomingFrequency.getPeriod()) {
        //                     case DAY -> preferredGroomingFrequency.getValue();
        //                     case WEEK -> 7 * preferredGroomingFrequency.getValue();
        //                     case MONTH -> 30 * preferredGroomingFrequency.getValue();
        //                     default -> 7;
        //                 };
        //             LocalDate expectedDate =
        //                 LocalDate.parse(lastStartDate).plusDays(days);
        //
        //             return ListRebookReminderTaskResult.RebookReminderTaskView.newBuilder()
        //                 .setId(task.getId())
        //                 .setCustomerName(getCustomerName(customer))
        //                 .addAllPetNames(getPetNames(
        //                     petMap,
        //                     customerToPetDetailMap.get(customerId).stream()
        //                         .map(PetDetailModel::getPetId)
        //                         .toList()))
        //                 .setCustomerAvatar(customer.getAvatarPath())
        //                 .setExpectedDate(Date.newBuilder()
        //                     .setYear(expectedDate.getYear())
        //                     .setMonth(expectedDate.getMonthValue())
        //                     .setDay(expectedDate.getDayOfMonth())
        //                     .build())
        //                 .setLastAppointmentDateTime(Timestamp.newBuilder()
        //                     .setSeconds(lastStartDateTime.toEpochSecond(ZoneOffset.UTC))
        //                     .build())
        //                 .setPreferredGroomingFrequency(preferredGroomingFrequency)
        //                 .setCustomerId(customerId)
        //                 .build();
        //         })
        //         .toList())
        //     .setPagination(response.getPagination())
        //     .build();
    }

    // private LocalDateTime getStartTimeByBusiness(long businessId) {
    //     BusinessModel business = businessBusinessClient.getBusiness(
    //             GetBusinessRequest.newBuilder().setId(businessId).build());
    //
    //     return LocalDate.now(ZoneId.of(business.getTimezoneName())).atStartOfDay();
    // }

    // private String getCustomerName(BusinessCustomerModel customer) {
    //     if (customer == null) {
    //         return "";
    //     }
    //     return customer.getFirstName() + " " + customer.getLastName();
    // }

    private String getCustomerName(GroomingCustomerInfoDTO customer) {
        if (customer == null) {
            return "";
        }
        return customer.getFirstName() + " " + customer.getLastName();
    }

    private String getCustomerName(CustomerPetReminderDTO pet) {
        if (pet == null) {
            return "";
        }
        return pet.getCustomerFirstName() + " " + pet.getCustomerLastName();
    }

    private String getCustomerName(CustomerRebookReminderDTO customer) {
        if (customer == null) {
            return "";
        }
        return customer.getCustomerFirstName() + " " + customer.getCustomerLastName();
    }

    private List<String> getPetNames(Map<Long, BusinessCustomerPetModel> petMap, List<Long> petIds) {
        if (CollectionUtils.isEmpty(petIds)) {
            return List.of();
        }
        return petIds.stream()
                .distinct()
                .sorted()
                .map(petMap::get)
                .map(BusinessCustomerPetModel::getPetName)
                .toList();
    }

    private static final byte BY_CALL = (byte) 0x84;
    private static final byte BY_EMAIL = (byte) 0x82;
    private static final byte BY_MSG = (byte) 0x81;

    /**
     * 根据查询出的数据库原始值，进行转换， 构造多选数组
     */
    public static List<Byte> getReminderList(Byte unconfirmedReminderBy) {
        List<Byte> choices = new ArrayList<>();
        if (PrimitiveTypeUtil.isNullOrZero(unconfirmedReminderBy)) {
            return choices;
        } else if (unconfirmedReminderBy > 0) {
            // 旧值处理
            choices.add(unconfirmedReminderBy);
        } else {
            // 新值转换
            byte originByte = unconfirmedReminderBy;
            if ((byte) (originByte & BY_MSG) == BY_MSG) {
                choices.add(ApptReminderBy.BY_MSG);
            }
            if ((byte) (originByte & BY_EMAIL) == BY_EMAIL) {
                choices.add(ApptReminderBy.BY_EMAIL);
            }
            if ((byte) (originByte & BY_CALL) == BY_CALL) {
                choices.add(ApptReminderBy.BY_CALL);
            }
        }
        return choices;
    }
}
