package com.moego.api.v3.auto_message.service;

import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_APPOINTMENT_BOOKED;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_APPOINTMENT_CANCELLED;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_APPOINTMENT_CANCELLED_BY_CLIENT;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_APPOINTMENT_CONFIRMED_BY_CLIENT;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_APPOINTMENT_MOVED_TO_WAITLIST;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_APPOINTMENT_RESCHEDULED;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_FIRST_REMINDER;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_GENERAL_REMINDER;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_ONLINE_BOOKING_REQUEST_ACCEPTED;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_ONLINE_BOOKING_REQUEST_AUTO_MOVED_TO_WAITLIST;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_ONLINE_BOOKING_REQUEST_DECLINED;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_ONLINE_BOOKING_REQUEST_SUBMITTED;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_PET_BIRTHDAY;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_READY_FOR_PICK_UP;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_REBOOK_REMINDER;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_SECOND_REMINDER;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_SEND_RECEIPT_WHEN_FULLY_PAID;
import static com.moego.idl.models.message.v1.MessageTemplateUseCase.USE_CASE_UNSUBMITTED_CARD_ON_FILE_REMINDER;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.api.auto_message.v1.UpdateAppointmentAutoMessageParams;
import com.moego.idl.api.auto_message.v1.UpdateAppointmentReminderParams;
import com.moego.idl.api.auto_message.v1.UpdatePayAutoMessageParams;
import com.moego.idl.api.auto_message.v1.UpdateReminderParams;
import com.moego.idl.models.auto_message.v1.AppointmentAutoMessageDetailView;
import com.moego.idl.models.auto_message.v1.AppointmentAutoMessageListView;
import com.moego.idl.models.auto_message.v1.AppointmentAutoMsgConfigModel;
import com.moego.idl.models.auto_message.v1.AppointmentReminderDetailView;
import com.moego.idl.models.auto_message.v1.AppointmentReminderListView;
import com.moego.idl.models.auto_message.v1.AutoMessageConfigModel;
import com.moego.idl.models.auto_message.v1.OBAutoMessageListView;
import com.moego.idl.models.auto_message.v1.PayAutoMessageDetailView;
import com.moego.idl.models.auto_message.v1.PayAutoMessageListView;
import com.moego.idl.models.auto_message.v1.ReminderDetailView;
import com.moego.idl.models.auto_message.v1.ReminderListView;
import com.moego.idl.models.auto_message.v1.ServiceTypeConfigDef;
import com.moego.idl.models.auto_message.v1.ServiceTypeConfigDefList;
import com.moego.idl.models.auto_message.v1.ServiceTypeConfigModel;
import com.moego.idl.models.auto_message.v1.ServiceTypeTemplateDef;
import com.moego.idl.models.auto_message.v1.ServiceTypeTemplateView;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.message.v1.BatchCreateTemplateItemDef;
import com.moego.idl.models.message.v1.MessageTemplateUseCase;
import com.moego.idl.models.message.v1.MessageTemplateUseCaseEnumList;
import com.moego.idl.models.message.v1.MessageType;
import com.moego.idl.models.message.v1.TemplateModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.auto_message.v1.AutoMessageConfigServiceGrpc;
import com.moego.idl.service.auto_message.v1.GetAppointmentAutoMsgConfigDetailRequest;
import com.moego.idl.service.auto_message.v1.GetAppointmentAutoMsgConfigListRequest;
import com.moego.idl.service.auto_message.v1.GetAutoMessageConfigDetailRequest;
import com.moego.idl.service.auto_message.v1.GetAutoMessageConfigListRequest;
import com.moego.idl.service.auto_message.v1.MessageTransferByBusinessRequest;
import com.moego.idl.service.auto_message.v1.UpdateAppointmentAutoMsgConfigRequest;
import com.moego.idl.service.auto_message.v1.UpdateAutoMessageConfigRequest;
import com.moego.idl.service.message.v1.BatchCreateTemplatesRequest;
import com.moego.idl.service.message.v1.MGetTemplatesRequest;
import com.moego.idl.service.message.v1.MessageTemplateServiceGrpc;
import com.moego.idl.service.message.v1.SyncSystemMessageTemplatesRequest;
import com.moego.idl.service.message.v1.TemplateServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.client.IBusinessBusinessClient;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class AutoMessageService {
    private final AutoMessageConfigServiceGrpc.AutoMessageConfigServiceBlockingStub autoMessageConfigClient;
    private final TemplateServiceGrpc.TemplateServiceBlockingStub templateClient;
    private final MessageTemplateServiceGrpc.MessageTemplateServiceBlockingStub messageTemplateClient;
    private final IBusinessBusinessClient iBusinessBusinessClient;

    public List<MessageTemplateUseCase> getUserCaseForOBAutoMsg() {
        return List.of(
                USE_CASE_ONLINE_BOOKING_REQUEST_SUBMITTED,
                USE_CASE_ONLINE_BOOKING_REQUEST_ACCEPTED,
                USE_CASE_ONLINE_BOOKING_REQUEST_DECLINED,
                USE_CASE_ONLINE_BOOKING_REQUEST_AUTO_MOVED_TO_WAITLIST);
    }

    public List<MessageTemplateUseCase> getUserCaseForAPPTAutoMsg() {
        return List.of(
                USE_CASE_APPOINTMENT_BOOKED,
                USE_CASE_APPOINTMENT_RESCHEDULED,
                USE_CASE_APPOINTMENT_CANCELLED,
                USE_CASE_APPOINTMENT_MOVED_TO_WAITLIST,
                USE_CASE_APPOINTMENT_CONFIRMED_BY_CLIENT,
                USE_CASE_APPOINTMENT_CANCELLED_BY_CLIENT,
                USE_CASE_READY_FOR_PICK_UP);
    }

    public List<MessageTemplateUseCase> getUserCaseForPaymentAutoMsg() {
        return List.of(USE_CASE_SEND_RECEIPT_WHEN_FULLY_PAID);
    }

    public List<MessageTemplateUseCase> getUserCaseForAPPTReminder() {
        return List.of(USE_CASE_FIRST_REMINDER, USE_CASE_SECOND_REMINDER, USE_CASE_GENERAL_REMINDER);
    }

    public List<MessageTemplateUseCase> getUserCaseForAdditionalReminder() {
        return List.of(USE_CASE_PET_BIRTHDAY, USE_CASE_REBOOK_REMINDER, USE_CASE_UNSUBMITTED_CARD_ON_FILE_REMINDER);
    }

    public List<OBAutoMessageListView> buildOBAutoMessageListView(List<AppointmentAutoMsgConfigModel> configModelList) {
        return configModelList.stream()
                .map(k -> OBAutoMessageListView.newBuilder()
                        .setId(k.getId())
                        .setIsEnabled(k.getIsEnabled())
                        .setUseCase(k.getUseCase())
                        .addAllClientReceive(getClientReceiveTypes(k))
                        .addAllServiceItemTypes(getAllServiceItemTypes(k.getTemplatesList()))
                        .build())
                .collect(Collectors.toList());
    }

    public List<AppointmentAutoMessageListView> buildAppointmentAutoMessageListView(
            List<AppointmentAutoMsgConfigModel> configModelList) {
        return configModelList.stream()
                .map(k -> AppointmentAutoMessageListView.newBuilder()
                        .setId(k.getId())
                        .setIsEnabled(k.getIsEnabled())
                        .setUseCase(k.getUseCase())
                        .addAllClientReceive(getClientReceiveTypes(k))
                        .addAllServiceItemTypes(getAllServiceItemTypes(k.getTemplatesList()))
                        .build())
                .collect(Collectors.toList());
    }

    public List<PayAutoMessageListView> buildPayAutoMessageListView(List<AutoMessageConfigModel> configModelList) {
        return configModelList.stream()
                .map(k -> PayAutoMessageListView.newBuilder()
                        .setId(k.getId())
                        .setIsEnabled(k.getIsEnabled())
                        .setUseCase(k.getUseCase())
                        .addAllClientReceive(getClientReceiveTypes(k))
                        .build())
                .collect(Collectors.toList());
    }

    public AppointmentAutoMessageDetailView buildAppointmentAutoMessageDetailView(
            AppointmentAutoMsgConfigModel configModel, Map<Long, TemplateModel> templatesMap) {
        return AppointmentAutoMessageDetailView.newBuilder()
                .setId(configModel.getId())
                .setIsEnabled(configModel.getIsEnabled())
                .setUseCase(configModel.getUseCase())
                .addAllClientReceive(getClientReceiveTypes(configModel))
                .addAllTemplate(buildServiceTypeTemplateViewList(configModel.getTemplatesList(), templatesMap))
                .build();
    }

    public PayAutoMessageDetailView getPayAutoMessageDetailView(AutoMessageConfigModel configModel) {
        Map<Long, TemplateModel> templateModelMap = getTemplates(List.of(
                configModel.getEmailTemplateId(), configModel.getSmsTemplateId(), configModel.getAppTemplateId()));
        TemplateModel emailTemplateModel = templateModelMap.get(configModel.getEmailTemplateId());
        TemplateModel smsTemplateModel = templateModelMap.get(configModel.getSmsTemplateId());
        TemplateModel appTemplateModel = templateModelMap.get(configModel.getAppTemplateId());
        return PayAutoMessageDetailView.newBuilder()
                .setId(configModel.getId())
                .setIsEnabled(configModel.getIsEnabled())
                .setUseCase(configModel.getUseCase())
                .addAllClientReceive(getClientReceiveTypes(configModel))
                .setEmailSubject(emailTemplateModel == null ? "" : emailTemplateModel.getSubject())
                .setEmailBody(emailTemplateModel == null ? "" : emailTemplateModel.getBody())
                .setSmsBody(smsTemplateModel == null ? "" : smsTemplateModel.getBody())
                .setAppBody(appTemplateModel == null ? "" : appTemplateModel.getBody())
                .build();
    }

    public AppointmentAutoMsgConfigModel updateAppointmentAutoMsgConfig(
            Long companyId, UpdateAppointmentAutoMessageParams request) {
        var existConfig = getAppointmentAutoMsgConfigDetail(companyId, request.getId());
        UpdateAppointmentAutoMsgConfigRequest.Builder updateRequest = UpdateAppointmentAutoMsgConfigRequest.newBuilder()
                .setId(request.getId())
                .setCompanyId(companyId);
        if (request.hasIsEnabled()) {
            updateRequest.setIsEnabled(request.getIsEnabled());
        }
        if (getUserCaseForOBAutoMsg().contains(existConfig.getUseCase()) && request.hasClientReceive()) {
            updateRequest.setIsEmailEnabled(
                    isEmailEnabled(request.getClientReceive().getMessageTypesList()));
            updateRequest.setIsSmsEnabled(
                    isSMSEnabled(request.getClientReceive().getMessageTypesList()));
            updateRequest.setIsAppEnabled(
                    isAppEnabled(request.getClientReceive().getMessageTypesList()));
        }
        if (request.hasTemplate()) {
            updateRequest.setTemplates(ServiceTypeConfigDefList.newBuilder()
                    .addAllValues(
                            getNewServiceTypeConfigDef(request.getTemplate().getValuesList()))
                    .build());
        }
        return autoMessageConfigClient
                .updateAppointmentAutoMsgConfig(updateRequest.build())
                .getAutoMessage();
    }

    // 目前只用于更新 RECEIPT. RECEIPT 的模版目前只有 SMS。
    public AutoMessageConfigModel updatePayAutoMessageConfig(Long companyId, UpdatePayAutoMessageParams request) {
        return updateAutoMessageConfig(
                companyId,
                request.getId(),
                request.hasIsEnabled() ? request.getIsEnabled() : null,
                null,
                null,
                null,
                null,
                request.hasSmsBody() ? request.getSmsBody() : null, // receipt 使用 sms 模版内容填充 email 模版
                request.hasSmsBody() ? request.getSmsBody() : null,
                request.hasAppBody() ? request.getAppBody() : null);
    }

    public List<AppointmentReminderListView> buildAppointmentReminderListView(
            List<AppointmentAutoMsgConfigModel> configModelList) {
        return configModelList.stream()
                .map(k -> AppointmentReminderListView.newBuilder()
                        .setId(k.getId())
                        .setIsEnabled(k.getIsEnabled())
                        .setUseCase(k.getUseCase())
                        .addAllClientReceive(getClientReceiveTypes(k))
                        .addAllServiceItemTypes(getAllServiceItemTypes(k.getTemplatesList()))
                        .setDaysBefore(-k.getMinutesOffset() / 60 / 24)
                        .setMinutesAt(k.getMinutesAt())
                        .build())
                .collect(Collectors.toList());
    }

    public AppointmentReminderDetailView buildAppointmentReminderDetailView(
            AppointmentAutoMsgConfigModel config, Map<Long, TemplateModel> templatesMap) {
        return AppointmentReminderDetailView.newBuilder()
                .setId(config.getId())
                .setIsEnabled(config.getIsEnabled())
                .setUseCase(config.getUseCase())
                .addAllClientReceive(getClientReceiveTypes(config))
                .setDaysBefore(-config.getMinutesOffset() / 60 / 24)
                .setMinutesAt(config.getMinutesAt())
                .addAllTemplate(buildServiceTypeTemplateViewList(config.getTemplatesList(), templatesMap))
                .build();
    }

    public AppointmentAutoMsgConfigModel updateAppointmentReminderConfig(
            Long companyId, UpdateAppointmentReminderParams request) {
        var existConfig = getAppointmentAutoMsgConfigDetail(companyId, request.getId());

        UpdateAppointmentAutoMsgConfigRequest.Builder updateRequest = UpdateAppointmentAutoMsgConfigRequest.newBuilder()
                .setId(request.getId())
                .setCompanyId(companyId);
        if (request.hasIsEnabled()) {
            updateRequest.setIsEnabled(request.getIsEnabled());
        }
        if (request.hasDaysBefore()) {
            updateRequest.setMinutesOffset(-request.getDaysBefore() * 24 * 60);
        }
        if (request.hasMinutesAt()) {
            updateRequest.setMinutesAt(request.getMinutesAt());
        }
        if (request.hasTemplate()) {
            updateRequest.setTemplates(ServiceTypeConfigDefList.newBuilder()
                    .addAllValues(
                            getNewServiceTypeConfigDef(request.getTemplate().getValuesList()))
                    .build());
        }

        switch (existConfig.getUseCase()) {
            case USE_CASE_FIRST_REMINDER -> doCheckAndRelateUpdateFor1stReminder(request, existConfig);
            case USE_CASE_SECOND_REMINDER -> doCheckFor2ndReminder(request, existConfig);
            default -> {}
        }

        return autoMessageConfigClient
                .updateAppointmentAutoMsgConfig(updateRequest.build())
                .getAutoMessage();
    }

    // 1st reminder 和 2nd reminder 状态有部分耦合，需要同时更新
    void doCheckAndRelateUpdateFor1stReminder(
            UpdateAppointmentReminderParams request, AppointmentAutoMsgConfigModel existConfig) {
        AppointmentAutoMsgConfigModel secondReminder = getAppointmentAutoMsgConfigList(
                        existConfig.getCompanyId(), existConfig.getBusinessId(), List.of(USE_CASE_SECOND_REMINDER))
                .get(0);
        UpdateAppointmentAutoMsgConfigRequest.Builder secondReminderUpdateRequest =
                UpdateAppointmentAutoMsgConfigRequest.newBuilder()
                        .setId(secondReminder.getId())
                        .setCompanyId(existConfig.getCompanyId());

        // 1st reminder 关闭时，2nd reminder 也要关闭
        boolean newStatus = request.hasIsEnabled() ? request.getIsEnabled() : existConfig.getIsEnabled();
        if (!newStatus && secondReminder.getIsEnabled()) {
            secondReminderUpdateRequest.setIsEnabled(false);
        }

        // 1st reminder 更新发送时间时，需检查早于 2nd reminder
        int newMinuteOffset =
                request.hasDaysBefore() ? -request.getDaysBefore() * 24 * 60 : existConfig.getMinutesOffset();
        int newMinuteAt = request.hasMinutesAt() ? request.getMinutesAt() : existConfig.getMinutesAt();
        if (newStatus
                && secondReminder.getIsEnabled()
                && !isSendBefore(
                        newMinuteOffset,
                        newMinuteAt,
                        secondReminder.getMinutesOffset(),
                        secondReminder.getMinutesAt())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "The 1st reminder should be sent before the 2nd reminder");
        }

        // 1st reminder 不支持的 service type。2nd reminder 也不支持，需要判断是否需要更新去掉
        if (request.hasTemplate()) {
            Set<ServiceItemType> allServiceTypesFor1st = request.getTemplate().getValuesList().stream()
                    .map(ServiceTypeTemplateDef::getServiceItemTypesList)
                    .flatMap(List::stream)
                    .collect(Collectors.toSet());
            if (!allServiceTypesFor1st.containsAll(getAllServiceItemTypes(secondReminder.getTemplatesList()))) {
                List<ServiceTypeConfigDef> templateDefsFor2nd = new ArrayList<>();
                for (ServiceTypeConfigModel template : secondReminder.getTemplatesList()) {
                    ServiceTypeConfigDef templateDef = ServiceTypeConfigDef.newBuilder()
                            .addAllServiceItemTypes(template.getServiceItemTypesList().stream()
                                    .filter(allServiceTypesFor1st::contains)
                                    .toList())
                            .setEmailTemplateId(template.getEmailTemplateId())
                            .setSmsTemplateId(template.getSmsTemplateId())
                            .setAppTemplateId(template.getAppTemplateId())
                            .build();
                    if (templateDef.getServiceItemTypesList().isEmpty()) {
                        // 兜底保留一个
                        templateDef = ServiceTypeConfigDef.newBuilder(templateDef)
                                .addServiceItemTypes(
                                        template.getServiceItemTypesList().get(0))
                                .build();
                    }
                    if (!CollectionUtils.isEmpty(templateDef.getServiceItemTypesList())) {
                        templateDefsFor2nd.add(templateDef);
                    }
                }
                secondReminderUpdateRequest.setTemplates(ServiceTypeConfigDefList.newBuilder()
                        .addAllValues(templateDefsFor2nd)
                        .build());
            }
        }

        // 需要更新 2nd reminder
        if (secondReminderUpdateRequest.hasTemplates() || secondReminderUpdateRequest.hasIsEnabled()) {
            autoMessageConfigClient.updateAppointmentAutoMsgConfig(secondReminderUpdateRequest.build());
        }
    }

    // 2nd reminder 和 1st reminder 状态有部分耦合，更新前需要检查
    void doCheckFor2ndReminder(UpdateAppointmentReminderParams request, AppointmentAutoMsgConfigModel existConfig) {
        AppointmentAutoMsgConfigModel firstReminder = getAppointmentAutoMsgConfigList(
                        existConfig.getCompanyId(), existConfig.getBusinessId(), List.of(USE_CASE_FIRST_REMINDER))
                .get(0);

        // 1st reminder 关闭时，2nd reminder 不能开启
        boolean newStatus = request.hasIsEnabled() ? request.getIsEnabled() : existConfig.getIsEnabled();
        if (newStatus && !firstReminder.getIsEnabled()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "The 2nd reminder should be closed when the 1st reminder is closed");
        }

        // 2nd reminder 更新发送时间时，需检查晚于 1st reminder
        int newMinuteOffset =
                request.hasDaysBefore() ? -request.getDaysBefore() * 24 * 60 : existConfig.getMinutesOffset();
        int newMinuteAt = request.hasMinutesAt() ? request.getMinutesAt() : existConfig.getMinutesAt();
        if (newStatus
                && !isSendBefore(
                        firstReminder.getMinutesOffset(), firstReminder.getMinutesAt(), newMinuteOffset, newMinuteAt)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "The 2nd reminder should be sent after the 1st reminder");
        }

        // 1st reminder 不支持的 service type。2nd reminder 也不支持
        if (request.hasTemplate()) {
            Set<ServiceItemType> allServiceTypesFor1st =
                    new HashSet<>(getAllServiceItemTypes(firstReminder.getTemplatesList()));
            if (request.getTemplate().getValuesList().stream()
                    .map(ServiceTypeTemplateDef::getServiceItemTypesList)
                    .flatMap(List::stream)
                    .anyMatch(k -> !allServiceTypesFor1st.contains(k))) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR,
                        "The 2nd reminder should have the same service types as the 1st reminder");
            }
        }
    }

    boolean isSendBefore(int minuteOffset1, int minuteAt1, int minuteOffset2, int minuteAt2) {
        return minuteOffset1 < minuteOffset2 || (minuteOffset1 == minuteOffset2 && minuteAt1 < minuteAt2);
    }

    public ReminderListView buildReminderListView(AutoMessageConfigModel config) {
        ReminderListView.Builder view = ReminderListView.newBuilder()
                .setId(config.getId())
                .setIsEnabled(config.getIsEnabled())
                .setUseCase(config.getUseCase())
                .addAllClientReceive(getClientReceiveTypes(config))
                .setMinutesAt(config.getMinutesAt());
        if (config.getMinutesOffset() < 0) {
            view.setDaysBefore(-config.getMinutesOffset() / 60 / 24);
        } else {
            view.setHoursAfter(config.getMinutesOffset() / 60);
        }
        return view.build();
    }

    public ReminderDetailView getReminderDetailView(AutoMessageConfigModel config) {
        Map<Long, TemplateModel> templateModelMap = getTemplates(
                List.of(config.getEmailTemplateId(), config.getSmsTemplateId(), config.getAppTemplateId()));
        TemplateModel emailTemplateModel = templateModelMap.get(config.getEmailTemplateId());
        TemplateModel smsTemplateModel = templateModelMap.get(config.getSmsTemplateId());
        TemplateModel appTemplateModel = templateModelMap.get(config.getAppTemplateId());
        ReminderDetailView.Builder view = ReminderDetailView.newBuilder()
                .setId(config.getId())
                .setIsEnabled(config.getIsEnabled())
                .setUseCase(config.getUseCase())
                .addAllClientReceive(getClientReceiveTypes(config))
                .setMinutesAt(config.getMinutesAt())
                .setEmailSubject(emailTemplateModel == null ? "" : emailTemplateModel.getSubject())
                .setEmailBody(emailTemplateModel == null ? "" : emailTemplateModel.getBody())
                .setSmsBody(smsTemplateModel == null ? "" : smsTemplateModel.getBody())
                .setAppBody(appTemplateModel == null ? "" : appTemplateModel.getBody());
        if (config.getMinutesOffset() < 0) {
            view.setDaysBefore(-config.getMinutesOffset() / 60 / 24);
        } else {
            view.setHoursAfter(config.getMinutesOffset() / 60);
        }
        return view.build();
    }

    public AutoMessageConfigModel updateReminderConfig(Long companyId, UpdateReminderParams request) {
        return updateAutoMessageConfig(
                companyId,
                request.getId(),
                request.hasIsEnabled() ? request.getIsEnabled() : null,
                request.hasDaysBefore() ? request.getDaysBefore() : null,
                request.hasHoursAfter() ? request.getHoursAfter() : null,
                request.hasMinutesAt() ? request.getMinutesAt() : null,
                request.hasEmailSubject() ? request.getEmailSubject() : null,
                request.hasEmailBody() ? request.getEmailBody() : null,
                request.hasSmsBody() ? request.getSmsBody() : null,
                request.hasAppBody() ? request.getAppBody() : null);
    }

    public Map<Long, TemplateModel> getTemplatesByAppointmentConfig(AppointmentAutoMsgConfigModel autoMsgConfig) {
        if (CollectionUtils.isEmpty(autoMsgConfig.getTemplatesList())) {
            return new HashMap<>();
        }
        List<Long> templateIds = new ArrayList<>();
        autoMsgConfig.getTemplatesList().forEach(k -> {
            templateIds.add(k.getEmailTemplateId());
            templateIds.add(k.getSmsTemplateId());
            templateIds.add(k.getAppTemplateId());
        });
        return getTemplates(templateIds);
    }

    List<MessageType> getClientReceiveTypes(AutoMessageConfigModel config) {
        List<MessageType> clientReceiveTypes = new ArrayList<>();
        if (config.getIsEmailEnabled()) {
            clientReceiveTypes.add(MessageType.MESSAGE_TYPE_EMAIL);
        }
        if (config.getIsSmsEnabled()) {
            clientReceiveTypes.add(MessageType.MESSAGE_TYPE_SMS);
        }
        if (config.getIsAppEnabled()) {
            clientReceiveTypes.add(MessageType.MESSAGE_TYPE_APP);
        }
        return clientReceiveTypes;
    }

    List<MessageType> getClientReceiveTypes(AppointmentAutoMsgConfigModel config) {
        List<MessageType> clientReceiveTypes = new ArrayList<>();
        if (config.getIsEmailEnabled()) {
            clientReceiveTypes.add(MessageType.MESSAGE_TYPE_EMAIL);
        }
        if (config.getIsSmsEnabled()) {
            clientReceiveTypes.add(MessageType.MESSAGE_TYPE_SMS);
        }
        if (config.getIsAppEnabled()) {
            clientReceiveTypes.add(MessageType.MESSAGE_TYPE_APP);
        }
        return clientReceiveTypes;
    }

    List<ServiceItemType> getAllServiceItemTypes(List<ServiceTypeConfigModel> serviceTypeConfigs) {
        if (CollectionUtils.isEmpty(serviceTypeConfigs)) {
            return new ArrayList<>();
        }
        return serviceTypeConfigs.stream()
                .map(ServiceTypeConfigModel::getServiceItemTypesList)
                .flatMap(List::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    boolean isEmailEnabled(List<MessageType> clientReceives) {
        return clientReceives.contains(MessageType.MESSAGE_TYPE_EMAIL);
    }

    boolean isSMSEnabled(List<MessageType> clientReceives) {
        return clientReceives.contains(MessageType.MESSAGE_TYPE_SMS);
    }

    boolean isAppEnabled(List<MessageType> clientReceives) {
        return clientReceives.contains(MessageType.MESSAGE_TYPE_APP);
    }

    ServiceTypeTemplateView buildServiceTypeTemplateView(
            ServiceTypeConfigModel config, Map<Long, TemplateModel> templatesMap) {
        TemplateModel emailTemplateModel = templatesMap.get(config.getEmailTemplateId());
        TemplateModel smsTemplateModel = templatesMap.get(config.getSmsTemplateId());
        TemplateModel appTemplateModel = templatesMap.get(config.getAppTemplateId());
        return ServiceTypeTemplateView.newBuilder()
                .setId(config.getId())
                .setEmailSubject(emailTemplateModel == null ? "" : emailTemplateModel.getSubject())
                .setEmailBody(emailTemplateModel == null ? "" : emailTemplateModel.getBody())
                .setSmsBody(smsTemplateModel == null ? "" : smsTemplateModel.getBody())
                .setAppBody(appTemplateModel == null ? "" : appTemplateModel.getBody())
                .addAllServiceItemTypes(config.getServiceItemTypesList())
                .build();
    }

    List<ServiceTypeTemplateView> buildServiceTypeTemplateViewList(
            List<ServiceTypeConfigModel> configs, Map<Long, TemplateModel> templatesMap) {
        if (CollectionUtils.isEmpty(configs)) {
            return new ArrayList<>();
        }
        return configs.stream()
                .sorted(Comparator.comparing(ServiceTypeConfigModel::getId))
                .map(k -> buildServiceTypeTemplateView(k, templatesMap))
                .collect(Collectors.toList());
    }

    public AppointmentAutoMsgConfigModel getAppointmentAutoMsgConfigDetail(Long companyId, Long id) {
        var request = GetAppointmentAutoMsgConfigDetailRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(id)
                .build();
        var resp = autoMessageConfigClient.getAppointmentAutoMsgConfigDetail(request);
        return resp.getAutoMessage();
    }

    public List<AppointmentAutoMsgConfigModel> getAppointmentAutoMsgConfigList(
            Long companyId, Long businessId, List<MessageTemplateUseCase> enumList) {
        if (CollectionUtils.isEmpty(enumList)) {
            return new ArrayList<>();
        }
        var request = GetAppointmentAutoMsgConfigListRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setUseCases(MessageTemplateUseCaseEnumList.newBuilder()
                        .addAllValues(enumList)
                        .build())
                .build();
        var resp = autoMessageConfigClient.getAppointmentAutoMsgConfigList(request);

        Map<MessageTemplateUseCase, AppointmentAutoMsgConfigModel> configMap = resp.getAutoMessagesList().stream()
                .collect(Collectors.toMap(AppointmentAutoMsgConfigModel::getUseCase, config -> config, (a, b) -> a));

        return enumList.stream().map(configMap::get).filter(Objects::nonNull).collect(Collectors.toList());
    }

    AutoMessageConfigModel updateAutoMessageConfig(
            Long companyId,
            Long id,
            Boolean isEnabled,
            Integer daysBefore,
            Integer hoursAfter,
            Integer minutesAt,
            String emailSubject,
            String emailBody,
            String smsBody,
            String appBody) {
        AutoMessageConfigModel configModel = getAutoMessageDetail(companyId, id);
        Map<Long, TemplateModel> templateModelMap = getTemplates(List.of(
                configModel.getEmailTemplateId(), configModel.getSmsTemplateId(), configModel.getAppTemplateId()));
        TemplateModel emailTemplateModel = templateModelMap.get(configModel.getEmailTemplateId());

        UpdateAutoMessageConfigRequest.Builder updateRequest =
                UpdateAutoMessageConfigRequest.newBuilder().setId(id).setCompanyId(companyId);
        if (isEnabled != null) {
            updateRequest.setIsEnabled(isEnabled);
        }

        if (daysBefore != null) {
            updateRequest.setMinutesOffset(-daysBefore * 24 * 60);
        }
        if (hoursAfter != null && hoursAfter > 0) {
            updateRequest.setMinutesOffset(hoursAfter * 60);
        }
        if (minutesAt != null) {
            updateRequest.setMinutesAt(minutesAt);
        }

        List<BatchCreateTemplateItemDef> templatesToCreate = new ArrayList<>();
        if (emailSubject != null || emailBody != null) {
            String eSubject = emailSubject == null ? emailTemplateModel.getSubject() : emailSubject;
            String eBody = emailBody == null ? emailTemplateModel.getBody() : emailBody;
            templatesToCreate.add(BatchCreateTemplateItemDef.newBuilder()
                    .setSeqNum(0)
                    .setSubject(eSubject)
                    .setBody(eBody)
                    .build());
        }
        if (smsBody != null) {
            templatesToCreate.add(BatchCreateTemplateItemDef.newBuilder()
                    .setSeqNum(1)
                    .setBody(smsBody)
                    .build());
        }
        if (appBody != null) {
            templatesToCreate.add(BatchCreateTemplateItemDef.newBuilder()
                    .setSeqNum(2)
                    .setBody(appBody)
                    .build());
        }

        if (!templatesToCreate.isEmpty()) {
            Map<Long, TemplateModel> templatesCreated = templateClient
                    .batchCreateTemplates(BatchCreateTemplatesRequest.newBuilder()
                            .addAllTemplates(templatesToCreate)
                            .build())
                    .getTemplatesMap();
            if (emailSubject != null || emailBody != null) {
                updateRequest.setEmailTemplateId(templatesCreated.get(0L).getId());
            }
            if (smsBody != null) {
                updateRequest.setSmsTemplateId(templatesCreated.get(1L).getId());
            }
            if (appBody != null) {
                updateRequest.setAppTemplateId(templatesCreated.get(2L).getId());
            }
        }

        return autoMessageConfigClient
                .updateAutoMessageConfig(updateRequest.build())
                .getAutoMessage();
    }

    public List<AutoMessageConfigModel> getAutoMessageList(
            Long companyId, Long businessId, List<MessageTemplateUseCase> enumList) {
        if (CollectionUtils.isEmpty(enumList)) {
            return new ArrayList<>();
        }
        var request = GetAutoMessageConfigListRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setUseCases(MessageTemplateUseCaseEnumList.newBuilder()
                        .addAllValues(enumList)
                        .build())
                .build();
        var resp = autoMessageConfigClient.getAutoMessageConfigList(request);

        Map<MessageTemplateUseCase, AutoMessageConfigModel> configMap = resp.getAutoMessagesList().stream()
                .collect(Collectors.toMap(AutoMessageConfigModel::getUseCase, config -> config, (a, b) -> a));

        return enumList.stream().map(configMap::get).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public AutoMessageConfigModel getAutoMessageDetail(Long companyId, Long id) {
        var request = GetAutoMessageConfigDetailRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(id)
                .build();
        var resp = autoMessageConfigClient.getAutoMessageConfigDetail(request);
        return resp.getAutoMessage();
    }

    public Map<Long, TemplateModel> getTemplates(List<Long> ids) {
        ids = Optional.ofNullable(ids).orElse(List.of()).stream()
                .filter(k -> k > 0)
                .toList();
        if (CollectionUtils.isEmpty(ids)) {
            return new HashMap<>();
        }
        var request = MGetTemplatesRequest.newBuilder().addAllIds(ids).build();
        var resp = templateClient.mGetTemplates(request);
        return resp.getTemplatesList().stream().collect(Collectors.toMap(TemplateModel::getId, v -> v, (k1, k2) -> k1));
    }

    List<ServiceTypeConfigDef> getNewServiceTypeConfigDef(List<ServiceTypeTemplateDef> newTemplates) {

        List<ServiceItemType> allServiceTypes = newTemplates.stream()
                .map(ServiceTypeTemplateDef::getServiceItemTypesList)
                .flatMap(List::stream)
                .toList();
        if (new HashSet<>(allServiceTypes).size() != allServiceTypes.size()) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "The template should have different service types");
        }
        List<BatchCreateTemplateItemDef> templatesToCreate = new ArrayList<>();
        for (int i = 0; i < newTemplates.size(); i++) {
            var def = newTemplates.get(i);
            templatesToCreate.add(BatchCreateTemplateItemDef.newBuilder()
                    .setSeqNum((long) 2 * i)
                    .setSubject(def.getEmailSubject())
                    .setBody(def.getEmailBody())
                    .build());
            templatesToCreate.add(BatchCreateTemplateItemDef.newBuilder()
                    .setSeqNum((long) 2 * i + 1)
                    .setBody(def.getSmsBody())
                    .build());
            templatesToCreate.add(BatchCreateTemplateItemDef.newBuilder()
                    .setSeqNum((long) 2 * i + 2)
                    .setSubject(def.getEmailSubject())
                    .setBody(def.getAppBody())
                    .build());
        }

        Map<Long, TemplateModel> templatesCreated = new HashMap<>();
        if (!CollectionUtils.isEmpty(newTemplates)) {
            templatesCreated = templateClient
                    .batchCreateTemplates(BatchCreateTemplatesRequest.newBuilder()
                            .addAllTemplates(templatesToCreate)
                            .build())
                    .getTemplatesMap();
        }

        List<ServiceTypeConfigDef> result = new ArrayList<>();
        for (int i = 0; i < newTemplates.size(); i++) {
            var def = newTemplates.get(i);
            ServiceTypeConfigDef.Builder config =
                    ServiceTypeConfigDef.newBuilder().addAllServiceItemTypes(def.getServiceItemTypesList());
            config.setEmailTemplateId(templatesCreated.get((long) 2 * i).getId());
            config.setSmsTemplateId(templatesCreated.get((long) 2 * i + 1).getId());
            config.setAppTemplateId(templatesCreated.get((long) 2 * i + 2).getId());
            result.add(config.build());
        }
        return result;
    }

    public void transAllToNewAutoMsg() {
        List<Integer> allBusinessIds = iBusinessBusinessClient.getAllBusinessIds2();
        // 拆分为子任务
        List<List<Integer>> splitBusinessIdList = CommonUtil.splitList(allBusinessIds, 20);
        // 分发子任务
        for (List<Integer> businessIds : splitBusinessIdList) {
            ThreadPool.execute(() -> batchTransToNewAutoMsg(businessIds));
        }
    }

    void batchTransToNewAutoMsg(List<Integer> allBusinessIds) {
        int batchSize = 10;
        for (int i = 0; i < allBusinessIds.size(); i += batchSize) {
            List<Long> businessIds = allBusinessIds.subList(i, Math.min(i + batchSize, allBusinessIds.size())).stream()
                    .map(Integer::longValue)
                    .collect(Collectors.toList());
            transToNewAutoMsg(businessIds);
        }
    }

    public void transToNewAutoMsg(List<Long> businessIds) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return;
        }
        log.info("business ids to trans: [{}]", businessIds);
        autoMessageConfigClient.messageTransferByBusiness(MessageTransferByBusinessRequest.newBuilder()
                .addAllBusinessIds(businessIds)
                .build());
        messageTemplateClient.syncSystemMessageTemplates(SyncSystemMessageTemplatesRequest.newBuilder()
                .addAllBusinessIds(businessIds)
                .build());
    }
}
