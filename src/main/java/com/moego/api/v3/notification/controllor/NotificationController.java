package com.moego.api.v3.notification.controllor;

import com.moego.api.v3.notification.convertor.NotificationConverter;
import com.moego.api.v3.notification.helper.NotificationHelper;
import com.moego.idl.api.notification.v1.GetNotificationUnreadCountParams;
import com.moego.idl.api.notification.v1.GetNotificationUnreadCountResult;
import com.moego.idl.api.notification.v1.GetNotificationsParams;
import com.moego.idl.api.notification.v1.GetNotificationsResult;
import com.moego.idl.api.notification.v1.NotificationDismissParams;
import com.moego.idl.api.notification.v1.NotificationDismissResult;
import com.moego.idl.api.notification.v1.NotificationReadAllParams;
import com.moego.idl.api.notification.v1.NotificationReadAllResult;
import com.moego.idl.api.notification.v1.NotificationReadParams;
import com.moego.idl.api.notification.v1.NotificationReadResult;
import com.moego.idl.api.notification.v1.NotificationServiceGrpc;
import com.moego.idl.models.message.v1.NotificationUnreadCountInfoDef;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.service.organization.v1.GetStaffDetailRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class NotificationController extends NotificationServiceGrpc.NotificationServiceImplBase {
    private final NotificationHelper notificationHelper;
    private final NotificationConverter notificationConverter;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffService;

    @Override
    @Auth(AuthType.COMPANY)
    public void getNotifications(
            GetNotificationsParams request, StreamObserver<GetNotificationsResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        Integer staffId = AuthContext.get().getStaffId();
        var businessIds = request.getBusinessIdsList();
        if (request.getBusinessIdsList().isEmpty()) {
            var workingLocationList = staffService
                    .getStaffDetail(GetStaffDetailRequest.newBuilder()
                            .setCompanyId(companyId)
                            .setId(staffId)
                            .build())
                    .getStaff()
                    .getWorkingLocationListList();
            businessIds =
                    workingLocationList.stream().map(LocationBriefView::getId).collect(Collectors.toList());
        }
        var notificationPageDTO = notificationHelper.getNotificationPageDTO(
                companyId,
                staffId,
                businessIds,
                request.getTabType(),
                request.hasStartingAfter() ? request.getStartingAfter() : null,
                request.getLimit());

        responseObserver.onNext(notificationConverter.toGetNotificationsResult(notificationPageDTO));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void notificationRead(
            NotificationReadParams request, StreamObserver<NotificationReadResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        Integer staffId = AuthContext.get().getStaffId();
        notificationHelper.readNotification(companyId, staffId, (int) request.getNotificationId(), request.getSource());
        responseObserver.onNext(NotificationReadResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void notificationReadAll(
            NotificationReadAllParams request, StreamObserver<NotificationReadAllResult> responseObserver) {
        Integer staffId = AuthContext.get().getStaffId();
        Long companyId = AuthContext.get().companyId();

        Set<Long> businessIds;
        if (request.getBusinessIdsList().isEmpty()) {
            var workingLocationList = staffService
                    .getStaffDetail(GetStaffDetailRequest.newBuilder()
                            .setCompanyId(companyId)
                            .setId(staffId)
                            .build())
                    .getStaff()
                    .getWorkingLocationListList();
            businessIds =
                    workingLocationList.stream().map(LocationBriefView::getId).collect(Collectors.toSet());
        } else {
            businessIds = new HashSet<>(request.getBusinessIdsList());
        }

        var count = notificationHelper.readAllNotification(
                businessIds, staffId, request.getTabType(), request.getTypeList(), request.getSource());
        responseObserver.onNext(
                NotificationReadAllResult.newBuilder().setReadCount(count).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getNotificationUnreadCount(
            GetNotificationUnreadCountParams request,
            StreamObserver<GetNotificationUnreadCountResult> responseObserver) {
        Integer staffId = AuthContext.get().getStaffId();
        Long companyId = AuthContext.get().companyId();
        List<Long> businessIds = request.getBusinessIdsList();
        if (businessIds.isEmpty()) {
            businessIds = staffService
                    .getStaffDetail(GetStaffDetailRequest.newBuilder()
                            .setId(staffId)
                            .setCompanyId(companyId)
                            .build())
                    .getStaff()
                    .getWorkingLocationListList()
                    .stream()
                    .map(LocationBriefView::getId)
                    .collect(Collectors.toList());
        }
        var staffNotificationTotalDTOV2s = notificationHelper.getNotificationUnreadCount(businessIds, staffId);

        Map<Long, NotificationUnreadCountInfoDef> businessIdToUnreadCountMap = staffNotificationTotalDTOV2s.stream()
                .collect(Collectors.toMap(
                        dto -> dto.getBusinessId().longValue(), dto -> NotificationUnreadCountInfoDef.newBuilder()
                                .setActivityCount(
                                        Optional.ofNullable(dto.getActivity()).orElse(0))
                                .setSystemCount(
                                        Optional.ofNullable(dto.getSystem()).orElse(0))
                                .setPendingReviewCount(Optional.ofNullable(dto.getPendingReview())
                                        .orElse(0))
                                .build()));
        responseObserver.onNext(GetNotificationUnreadCountResult.newBuilder()
                .putAllBusinessIdToUnreadCount(businessIdToUnreadCountMap)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void notificationDismiss(
            NotificationDismissParams request, StreamObserver<NotificationDismissResult> responseObserver) {
        Integer staffId = AuthContext.get().getStaffId();
        Long companyId = AuthContext.get().companyId();
        notificationHelper.dismissNotification(companyId, staffId, (int) request.getNotificationId());
        responseObserver.onNext(NotificationDismissResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
