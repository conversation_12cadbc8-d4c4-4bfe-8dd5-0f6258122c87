package com.moego.api.v3.notification.helper;

import com.moego.common.dto.notificationDto.StaffNotificationTotalDTOV2;
import com.moego.idl.models.message.v1.NotificationTabType;
import com.moego.server.message.client.INotificationClient;
import com.moego.server.message.dto.NotificationPageDto;
import com.moego.server.message.params.notification.GetNotificationsParams;
import com.moego.server.message.params.notification.ReadAllNotificationParams;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
@Slf4j
public class NotificationHelper {
    private final INotificationClient iNotificationClient;
    private final Map<NotificationTabType, String> notificationTabType = Map.of(
            NotificationTabType.NOTIFICATION_TAB_TYPE_ALL, "all",
            NotificationTabType.NOTIFICATION_TAB_TYPE_SYSTEM, "system",
            NotificationTabType.NOTIFICATION_TAB_TYPE_ACTIVITY, "activity",
            NotificationTabType.NOTIFICATION_TAB_TYPE_PENDING_REVIEW, "pending review");

    public NotificationPageDto getNotificationPageDTO(
            Long companyId,
            Integer staffId,
            List<Long> businessIds,
            NotificationTabType tabType,
            Integer startingAfter,
            Integer limit) {
        String tab = notificationTabType.get(tabType);
        if (tab == null) {
            log.error("tabType {} is not valid", tabType);
            return null;
        }
        return iNotificationClient.getNotifications(new GetNotificationsParams(
                companyId,
                staffId,
                businessIds.stream()
                        .filter(Objects::nonNull)
                        .map(Long::intValue)
                        .collect(Collectors.toList()),
                tab,
                startingAfter,
                limit));
    }

    public Boolean readNotification(Long companyId, Integer staffId, Integer notificationId, String source) {
        return iNotificationClient.readNotification(companyId, staffId, notificationId, source);
    }

    public Boolean dismissNotification(Long companyId, Integer staffId, Integer notificationId) {
        return iNotificationClient.dismissNotification(companyId, staffId, notificationId);
    }

    public int readAllNotification(
            Set<Long> businessIds, Integer staffId, NotificationTabType tabType, List<String> type, String source) {
        String tab = notificationTabType.get(tabType);
        if (tab == null) {
            log.error("tabType {} is not valid", tabType);
            return 0;
        }
        var count = iNotificationClient.readAllNotification(new ReadAllNotificationParams(
                businessIds.stream()
                        .filter(Objects::nonNull)
                        .map(Long::intValue)
                        .collect(Collectors.toList()),
                staffId,
                tab,
                type,
                source));
        return count != null ? count : 0;
    }

    public List<StaffNotificationTotalDTOV2> getNotificationUnreadCount(List<Long> businessIds, Integer staffId) {
        return iNotificationClient.getUnreadCount(
                businessIds.stream()
                        .filter(Objects::nonNull)
                        .map(Long::intValue)
                        .collect(Collectors.toList()),
                staffId);
    }
}
