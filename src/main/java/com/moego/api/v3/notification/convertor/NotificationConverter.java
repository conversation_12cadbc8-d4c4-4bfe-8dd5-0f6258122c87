/*
 * @since 2023-04-12 10:55:30
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.notification.convertor;

import com.google.protobuf.Struct;
import com.moego.common.dto.notificationDto.NotificationBeanDto;
import com.moego.idl.api.notification.v1.GetNotificationsResult;
import com.moego.lib.common.proto.ProtoUtils;
import com.moego.server.message.dto.NotificationPageDto;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedSourcePolicy = ReportingPolicy.WARN,
        unmappedTargetPolicy = ReportingPolicy.WARN)
public interface NotificationConverter {

    @Mapping(target = "notificationListData", source = "notificationList")
    GetNotificationsResult toGetNotificationsResult(NotificationPageDto input);

    GetNotificationsResult.NotificationListData toNotificationListDataList(NotificationBeanDto input);

    List<GetNotificationsResult.NotificationListData> toNotificationListDataList(List<NotificationBeanDto> input);

    static Struct toStruct(Object obj) {
        if (obj instanceof Struct v) {
            return v;
        }
        return ProtoUtils.toValue(obj).getStructValue();
    }
}
