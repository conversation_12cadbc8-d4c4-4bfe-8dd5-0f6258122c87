package com.moego.api.v3.enterprise;

import com.moego.idl.api.enterprise.v1.GetTerritoryAssignedInfoParams;
import com.moego.idl.api.enterprise.v1.GetTerritoryAssignedInfoResult;
import com.moego.idl.api.enterprise.v1.TerritoryApiGrpc;
import com.moego.idl.models.enterprise.v1.AreaType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.enterprise.v1.GetTerritoriesInfoByEnterpriseIdRequest;
import com.moego.idl.service.enterprise.v1.TerritoryServiceGrpc;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.QueryCompaniesByIdsRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@AllArgsConstructor
public class TerritoryController extends TerritoryApiGrpc.TerritoryApiImplBase {
    private final TerritoryServiceGrpc.TerritoryServiceBlockingStub territoryServiceBlockingStub;
    private final CompanyServiceGrpc.CompanyServiceBlockingStub companyServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void getTerritoryAssignedInfo(
            GetTerritoryAssignedInfoParams request, StreamObserver<GetTerritoryAssignedInfoResult> responseObserver) {
        var company = companyServiceBlockingStub
                .queryCompaniesByIds(QueryCompaniesByIdsRequest.newBuilder()
                        .addCompanyIds(AuthContext.get().companyId())
                        .build())
                .getCompanyIdToCompanyMap()
                .get(AuthContext.get().companyId());
        if (company == null) {
            throw ExceptionUtil.bizException(Code.CODE_COMPANY_NOT_FOUND);
        }
        if (company.getEnterpriseId() == 0) {
            responseObserver.onNext(GetTerritoryAssignedInfoResult.getDefaultInstance());
            responseObserver.onCompleted();
        }
        var res = territoryServiceBlockingStub.getTerritoriesInfoByEnterpriseId(
                GetTerritoriesInfoByEnterpriseIdRequest.newBuilder()
                        .setEnterpriseId(company.getEnterpriseId())
                        .build());

        Set<String> allZipcodes = res.getTerritoriesList().stream()
                .flatMap(territory -> territory.getZipCodesList().stream())
                .collect(Collectors.toSet());
        allZipcodes.addAll(res.getGreyAreaZipCodesList());

        var result = GetTerritoryAssignedInfoResult.newBuilder()
                .addAllNotAssigned(List.of()) // todo
                .addAllAllZipcodes(allZipcodes);

        res.getTenantTerritoriesBindingInfosList().forEach(info -> {
            if (info.getCompanyId() == AuthContext.get().companyId()) {
                info.getTerritoriesList().stream().findFirst().ifPresent(territory -> {
                    result.addAssignedToCurrent(GetTerritoryAssignedInfoResult.ZipcodesAssignedInfo.newBuilder()
                            .setAreaName(territory.getName())
                            .setAreaType(AreaType.TERRITORY)
                            .addAllZipcodes(territory.getZipCodesList())
                            .setAssignedCompanyName(info.getTenantName())
                            .setAssignedCompanyId(info.getCompanyId())
                            .build());
                });
            } else {
                info.getTerritoriesList().stream().findFirst().ifPresent(territory -> {
                    result.addAssignedToOthers(GetTerritoryAssignedInfoResult.ZipcodesAssignedInfo.newBuilder()
                            .setAreaName(territory.getName())
                            .setAreaType(AreaType.TERRITORY)
                            .addAllZipcodes(territory.getZipCodesList())
                            .setAssignedCompanyName(info.getTenantName())
                            .setAssignedCompanyId(info.getCompanyId())
                            .build());
                });
            }
        });

        responseObserver.onNext(result.build());
        responseObserver.onCompleted();
    }
}
