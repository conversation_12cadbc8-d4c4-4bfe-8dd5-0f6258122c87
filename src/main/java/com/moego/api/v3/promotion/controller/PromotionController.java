package com.moego.api.v3.promotion.controller;

import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;
import com.moego.api.v3.promotion.service.coupon.RecommendService;
import com.moego.idl.api.promotion.v1.ListPromotionsRequest;
import com.moego.idl.api.promotion.v1.ListPromotionsResponse;
import com.moego.idl.api.promotion.v1.PromotionServiceGrpc;
import com.moego.idl.api.promotion.v1.SearchCouponsRequest;
import com.moego.idl.api.promotion.v1.SearchCouponsResponse;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.metadata.v1.OwnerType;
import com.moego.idl.models.promotion.v1.ResourceModel;
import com.moego.idl.models.promotion.v1.SlotModel;
import com.moego.idl.service.metadata.v1.ExtractValuesRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.core.TypeRef;
import com.moego.lib.common.exception.BizException;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.ob.OBBusinessDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import io.grpc.stub.StreamObserver;
import jakarta.validation.constraints.NotNull;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class PromotionController extends PromotionServiceGrpc.PromotionServiceImplBase {
    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataService;
    private final IGroomingOnlineBookingService onlineBookingApi;
    private final RecommendService recommendService;

    private final LoadingCache<getSlotResourceParams, List<SlotModel>> getSlotResourceCache = Caffeine.newBuilder()
            .maximumSize(10_000)
            .expireAfterWrite(Duration.ofSeconds(60 * 5))
            .build(this::getSlotResourcesRemote);

    private static final String PROMOTION_PAGE_SLOT_METADATA_KEY = "promotion_page_slot_config";
    private static final String PROMOTION_SLOT_RESOURCE_KEY_PREFIX = "promotion_resource_";

    @AllArgsConstructor
    static class getSlotResourceParams {
        @NotNull
        final String page;

        @NotNull
        final Set<String> slots;

        @NotNull
        final Map<String, String> filter;

        @Override
        public boolean equals(Object obj) {
            if (this == obj) return true;
            if (obj == null || getClass() != obj.getClass()) return false;
            var o = (getSlotResourceParams) obj;
            return page.equals(o.page)
                    && slots.containsAll(o.slots)
                    && o.slots.containsAll(slots)
                    && filter.equals(o.filter);
        }

        @Override
        public int hashCode() {
            return Objects.hash(page, slots, filter);
        }
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void listPromotions(ListPromotionsRequest request, StreamObserver<ListPromotionsResponse> responseObserver) {
        var pageSlots = getPageSlots();
        if (pageSlots.isEmpty() || !pageSlots.containsKey(request.getPage())) {
            responseObserver.onNext(ListPromotionsResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        responseObserver.onNext(ListPromotionsResponse.newBuilder()
                .addAllPromotions(getSlotResources(
                        request.getPage(), new HashSet<>(pageSlots.get(request.getPage())), request.getFilterMap()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void searchCoupons(SearchCouponsRequest request, StreamObserver<SearchCouponsResponse> responseObserver) {
        responseObserver.onNext(recommendService.searchCoupons(request));
        responseObserver.onCompleted();
    }

    private Map<String, List<String>> getPageSlots() {
        try {
            var value = metadataService
                    .extractValues(ExtractValuesRequest.newBuilder()
                            .setKeyName(PROMOTION_PAGE_SLOT_METADATA_KEY)
                            .putOwners(OwnerType.OWNER_TYPE_SYSTEM_VALUE, 1)
                            .build())
                    .getValues()
                    .getOrDefault(PROMOTION_PAGE_SLOT_METADATA_KEY, "{}");
            return JsonUtil.toBean(value, new TypeRef<>() {});
        } catch (Exception e) {
            log.error("get promotion page slot config error", e);
            return Map.of();
        }
    }

    private List<SlotModel> getSlotResources(String page, Set<String> slots, Map<String, String> filter) {
        var validFilter = validateFilter(page, filter);
        return Optional.ofNullable(getSlotResourceCache.get(new getSlotResourceParams(page, slots, validFilter)))
                .orElse(new ArrayList<>());
    }

    private List<SlotModel> getSlotResourcesRemote(getSlotResourceParams params) {
        List<SlotModel> result = new ArrayList<>();

        // FIXME: 先简单串行
        for (var slot : params.slots) {
            String value;
            try {
                var getMetadataReqBuilder =
                        ExtractValuesRequest.newBuilder().setKeyName(PROMOTION_SLOT_RESOURCE_KEY_PREFIX + slot);
                switch (params.page) {
                    case "ob_landing":
                    case "ob_booking_submit":
                        OBBusinessDTO dto;
                        try {
                            dto = onlineBookingApi.getBusinessDTOByOBNameOrDomain(
                                    new OBAnonymousParams().setName(params.filter.get("ob_name")));
                            if (dto == null) {
                                return new ArrayList<>();
                            }
                        } catch (Exception e) {
                            BizException biz = ExceptionUtil.extractBizException(e);
                            if (!isBizException(biz)
                                    || !biz.getCode().equals(Code.CODE_BOOK_ONLINE_NAME_INVALID.getNumber())) {
                                throw e;
                            }
                            // invalid ob site name should cache an empty value
                            return new ArrayList<>();
                        }
                        getMetadataReqBuilder.putOwners(OwnerType.OWNER_TYPE_COMPANY_VALUE, dto.getCompanyId());
                        break;
                    default:
                        // default type is company,and any company id is ok
                        getMetadataReqBuilder.putOwners(OwnerType.OWNER_TYPE_COMPANY_VALUE, 1);
                }
                value = metadataService
                        .extractValues(getMetadataReqBuilder.build())
                        .getValues()
                        .getOrDefault(PROMOTION_SLOT_RESOURCE_KEY_PREFIX + slot, "");
            } catch (Exception e) {
                log.error("get promotion slot resource param: {} error:{}", params, e.toString());
                // null for no cache
                return null;
            }
            if (!StringUtils.hasText(value) || value.equals("{}")) {
                continue;
            }
            ResourceModel resource = JsonUtil.toBean(value, ResourceModel.class);
            result.add(SlotModel.newBuilder().setId(slot).setResource(resource).build());
        }
        return result;
    }

    private static boolean isBizException(Throwable e) {
        if (e == null) {
            return false;
        }

        if (e instanceof BizException) {
            return true;
        }

        Throwable cause = e.getCause();
        return isBizException(cause);
    }

    private static final Map<String, List<String>> pageValidFilter = Map.of(
            "ob_landing", List.of("ob_name"),
            "ob_booking_submit", List.of("ob_name"));

    private Map<String, String> validateFilter(String page, Map<String, String> filter) {
        return filter.entrySet().stream()
                .filter(e -> pageValidFilter.getOrDefault(page, List.of()).contains(e.getKey()))
                .filter(e -> StringUtils.hasText(e.getValue()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }
}
