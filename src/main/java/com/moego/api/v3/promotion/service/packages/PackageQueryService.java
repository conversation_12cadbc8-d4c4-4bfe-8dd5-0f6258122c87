package com.moego.api.v3.promotion.service.packages;

import com.moego.api.v3.appointment.converter.PackageConverter;
import com.moego.idl.models.pkg.v1.PackageModel;
import com.moego.server.retail.client.IPackageClient;
import com.moego.server.retail.dto.ListPackagesResult;
import com.moego.server.retail.param.ListPackagesParams;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@AllArgsConstructor
public class PackageQueryService {
    private final IPackageClient packageClient;

    public List<PackageModel> query(Long companyID, List<Long> businessIDs) {

        List<Integer> integerBusinessIDs =
                businessIDs.stream().map(Long::intValue).collect(Collectors.toList());

        ListPackagesResult resp = packageClient.listPackages(new ListPackagesParams(companyID, integerBusinessIDs));

        return resp.packages().stream()
                .filter(pkg -> Objects.nonNull(pkg.getIsActive()) && pkg.getIsActive())
                .map(PackageConverter.INSTANCE::toModel)
                .toList();
    }
}
