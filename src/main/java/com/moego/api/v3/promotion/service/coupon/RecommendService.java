package com.moego.api.v3.promotion.service.coupon;

import com.moego.api.v3.appointment.utils.PackageHelper;
import com.moego.idl.api.appointment.v1.CustomerPackageView;
import com.moego.idl.api.promotion.v1.SearchCouponsRequest;
import com.moego.idl.api.promotion.v1.SearchCouponsResponse;
import com.moego.idl.models.marketing.v1.DiscountCodeCompositeView;
import com.moego.idl.models.membership.v1.MembershipModel;
import com.moego.idl.models.promotion.v1.Coupon;
import com.moego.idl.models.promotion.v1.CouponApplicationTarget;
import com.moego.idl.models.promotion.v1.CouponSearchCondition;
import com.moego.idl.models.promotion.v1.Restrictions;
import com.moego.idl.models.promotion.v1.Source;
import com.moego.idl.models.promotion.v1.TargetType;
import com.moego.idl.models.promotion.v1.Targets;
import com.moego.idl.service.marketing.v1.DiscountCodeServiceGrpc;
import com.moego.idl.service.marketing.v1.GetAvailableDiscountListInput;
import com.moego.idl.service.marketing.v1.GetAvailableDiscountListOutput;
import com.moego.idl.service.membership.v1.ListMembershipsForCustomerRequest;
import com.moego.idl.service.membership.v1.ListMembershipsForCustomerResponse;
import com.moego.idl.service.membership.v1.MembershipServiceGrpc;
import com.moego.idl.service.promotion.v1.PromotionServiceGrpc;
import com.moego.idl.service.promotion.v1.RecommendCouponsRequest;
import com.moego.idl.service.promotion.v1.RecommendCouponsResponse;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.auth.AuthContext;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class RecommendService {

    private final PromotionServiceGrpc.PromotionServiceBlockingStub promotionService;
    private final DiscountCodeServiceGrpc.DiscountCodeServiceBlockingStub discountCodeService;
    private final MembershipServiceGrpc.MembershipServiceBlockingStub membershipService;
    private final PackageHelper packageHelper;

    public SearchCouponsResponse searchCoupons(SearchCouponsRequest request) {
        RecommendCouponsRequest innerRequest = RecommendCouponsRequest.newBuilder()
                .setCustomerId(request.getSearchCondition().getCustomerId())
                .addAllTargets(fetchTargets(request))
                .setSearchCondition(request.getSearchCondition())
                .build();
        RecommendCouponsResponse innerResponse = promotionService.recommendCoupons(innerRequest);
        List<Coupon> availableCouponList = injectShit(innerResponse.getAvailableCouponsList());
        List<Coupon> unavailableCouponList = injectShit(innerResponse.getUnavailableCouponsList());
        List<Coupon> allCoupons = new ArrayList<>();
        allCoupons.addAll(availableCouponList);
        allCoupons.addAll(unavailableCouponList);

        return SearchCouponsResponse.newBuilder()
                .addAllAvailableCoupons(availableCouponList)
                .addAllUnavailableCoupons(unavailableCouponList)
                .addAllPackageSubjects(buildPackageSubjects(request, allCoupons))
                .addAllDiscountSubjects(buildDiscountSubjects(request, allCoupons))
                .addAllMembershipSubjects(buildMembershipSubjects(request, allCoupons))
                .build();
    }

    private List<CouponApplicationTarget> fetchTargets(SearchCouponsRequest request) {
        return request.getSearchCondition().getTargetsList().stream()
                .map(RecommendService::getApplicationTargetFromInputTarget)
                .collect(Collectors.toList());
    }

    private List<CustomerPackageView> buildPackageSubjects(SearchCouponsRequest request, List<Coupon> allCoupons) {
        AuthContext authContext = AuthContext.get();
        var packageFuture = packageHelper.listCustomerPackages(
                authContext.getCompanyId().longValue(),
                authContext.getBusinessId().longValue(),
                Collections.singletonList(request.getSearchCondition().getCustomerId()));
        var customerPackageFuture = packageHelper.listCustomerPackages(
                authContext.getCompanyId().longValue(),
                authContext.getBusinessId().longValue(),
                Collections.singletonList(request.getSearchCondition().getCustomerId()));
        var customerPackageDetailFuture = packageHelper.listCustomerPackageDetails(customerPackageFuture);
        List<CustomerPackageView> packages = PackageHelper.getPackageViews(
                packageFuture.join(),
                customerPackageDetailFuture.join(),
                request.getSearchCondition().getCustomerId(),
                true);
        List<CustomerPackageView> result = new ArrayList<>();
        Set<Long> dedupBuf = new HashSet<>();
        for (CustomerPackageView customerPackageView : packages) {
            if (dedupBuf.contains(customerPackageView.getPackageId())) {
                continue;
            }
            result.add(customerPackageView);
            dedupBuf.add(customerPackageView.getPackageId());
        }

        return result;
    }

    private List<DiscountCodeCompositeView> buildDiscountSubjects(
            SearchCouponsRequest request, List<Coupon> allCoupons) {
        Set<Long> discountIDs = new HashSet<>();
        for (Coupon coupon : allCoupons) {
            if (coupon.getSource().getType() == Source.Type.DISCOUNT) {
                discountIDs.add(coupon.getSource().getDiscount().getId());
            }
        }
        AuthContext authContext = AuthContext.get();
        GetAvailableDiscountListOutput resp =
                discountCodeService.getAvailableDiscountList(GetAvailableDiscountListInput.newBuilder()
                        .setCompanyId(authContext.getCompanyId())
                        .setBusinessId(authContext.getBusinessId())
                        .setCustomerId(request.getSearchCondition().getCustomerId())
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1000)
                                .build())
                        .build());
        return resp.getDiscountCodeCompositeViewsList().stream()
                .filter(discount -> discountIDs.contains(discount.getId()))
                .toList();
    }

    private List<MembershipModel> buildMembershipSubjects(SearchCouponsRequest request, List<Coupon> allCoupons) {
        Set<Long> membershipIDs = new HashSet<>();
        for (Coupon coupon : allCoupons) {
            if (coupon.getSource().getType() == Source.Type.MEMBERSHIP_DISCOUNT
                    || coupon.getSource().getType() == Source.Type.MEMBERSHIP_QUANTITY) {
                membershipIDs.add(coupon.getSource().getMembership().getId());
            }
        }
        AuthContext authContext = AuthContext.get();
        ListMembershipsForCustomerResponse resp =
                membershipService.listMembershipsForCustomer(ListMembershipsForCustomerRequest.newBuilder()
                        .setCompanyId(authContext.getCompanyId())
                        .setBusinessId(authContext.getBusinessId())
                        .setCustomerId(request.getSearchCondition().getCustomerId())
                        .build());

        return resp.getMembershipsList().stream()
                .filter(membership -> membershipIDs.contains(membership.getId()))
                .toList();
    }

    private static CouponApplicationTarget getApplicationTargetFromInputTarget(CouponSearchCondition.Target input) {
        return CouponApplicationTarget.newBuilder()
                .setTargetId(input.getTargetId())
                .setTargetType(input.getTargetType())
                .build();
    }

    private List<Coupon> injectShit(List<Coupon> allCoupons) {
        List<Coupon> newCoupons = new ArrayList<>();
        for (Coupon coupon : allCoupons) {
            Coupon.Builder couponBuilder = Coupon.newBuilder().mergeFrom(coupon);
            if (couponSupportChargeService(coupon)) {
                List<Targets> targets = new ArrayList<>();
                if (!CollectionUtils.isEmpty(coupon.getRestrictions().getTargetsList())) {
                    targets.addAll(coupon.getRestrictions().getTargetsList());
                }
                targets.add(Targets.newBuilder()
                        .setAll(true)
                        .setType(TargetType.SERVICE_CHARGE)
                        .build());

                Restrictions restrictions = coupon.getRestrictions().toBuilder()
                        .clearTargets()
                        .addAllTargets(targets)
                        .build();
                couponBuilder.setRestrictions(restrictions);
            }
            Coupon newCoupon = couponBuilder.build();
            newCoupons.add(newCoupon);
        }
        return newCoupons;
    }

    private boolean couponSupportChargeService(Coupon coupon) {
        if (coupon.getSource().getType() != Source.Type.DISCOUNT) {
            return false;
        }
        return coupon.getRestrictions().getTargetsList().stream()
                .filter(target -> !target.getType().equals(TargetType.SERVICE) && target.getAll())
                .findFirst()
                .isPresent();
    }
}
