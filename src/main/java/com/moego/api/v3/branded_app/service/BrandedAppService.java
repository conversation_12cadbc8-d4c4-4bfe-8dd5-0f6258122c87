package com.moego.api.v3.branded_app.service;

import com.moego.idl.models.account.v1.AccountNamespaceType;
import com.moego.idl.service.branded_app.v1.BrandedAppConfigServiceGrpc;
import com.moego.idl.service.branded_app.v1.GetBrandedAppConfigRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
@Slf4j
public class BrandedAppService {
    private final BrandedAppConfigServiceGrpc.BrandedAppConfigServiceBlockingStub brandedAppConfigService;

    public boolean isBrandedAppUser(long companyId) {
        return !brandedAppConfigService
                .getBrandedAppConfig(GetBrandedAppConfigRequest.newBuilder()
                        .setBrandedEntity(GetBrandedAppConfigRequest.BrandedEntity.newBuilder()
                                .setBrandedType(AccountNamespaceType.COMPANY)
                                .setBrandedId(companyId)
                                .build())
                        .build())
                .getConfig()
                .getBrandedAppId()
                .isBlank();
    }
}
