package com.moego.api.v3.branded_app;

import com.moego.idl.api.branded_app.v1.BrandedAppConfigView;
import com.moego.idl.api.branded_app.v1.BrandedAppServiceGrpc;
import com.moego.idl.api.branded_app.v1.GetBrandedAppConfigParams;
import com.moego.idl.api.branded_app.v1.GetBrandedAppConfigResult;
import com.moego.idl.models.account.v1.AccountNamespaceType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.CompanyModel;
import com.moego.idl.service.branded_app.v1.BrandedAppConfigServiceGrpc.BrandedAppConfigServiceBlockingStub;
import com.moego.idl.service.branded_app.v1.BrandedLocationServiceGrpc;
import com.moego.idl.service.branded_app.v1.GetBrandedAppConfigRequest;
import com.moego.idl.service.branded_app.v1.ListBrandedLocationsRequest;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc.CompanyServiceBlockingStub;
import com.moego.idl.service.organization.v1.QueryCompaniesByIdsRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/7/10
 */
@GrpcService
@RequiredArgsConstructor
public class BrandedAppController extends BrandedAppServiceGrpc.BrandedAppServiceImplBase {

    private final CompanyServiceBlockingStub companyService;
    private final BrandedAppConfigServiceBlockingStub brandedAppConfigService;
    private final BrandedLocationServiceGrpc.BrandedLocationServiceBlockingStub brandedLocationService;

    @Override
    @Auth(AuthType.COMPANY)
    public void getBrandedAppConfig(
            GetBrandedAppConfigParams request, StreamObserver<GetBrandedAppConfigResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        var companyMap = companyService
                .queryCompaniesByIds(QueryCompaniesByIdsRequest.newBuilder()
                        .addCompanyIds(companyId)
                        .build())
                .getCompanyIdToCompanyMap();
        var company = Optional.ofNullable(companyMap.get(companyId))
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_COMPANY_NOT_FOUND));

        var response = brandedAppConfigService.getBrandedAppConfig(GetBrandedAppConfigRequest.newBuilder()
                .setBrandedEntity(buildBrandedEntity(company))
                .build());
        if (response.hasConfig()) {
            var locations = brandedLocationService
                    .listBrandedLocations(ListBrandedLocationsRequest.newBuilder()
                            .setBrandedAppId(response.getConfig().getBrandedAppId())
                            .build())
                    .getLocationsList();
            // find the location that matches the request
            var location = locations.stream()
                    .filter(l -> companyId.equals(l.getCompanyId()))
                    .findFirst()
                    .orElse(null);
            if (location == null) {
                responseObserver.onNext(GetBrandedAppConfigResult.getDefaultInstance());
            } else {
                var config = response.getConfig();
                responseObserver.onNext(GetBrandedAppConfigResult.newBuilder()
                        .setConfig(BrandedAppConfigView.newBuilder()
                                .setBrandedAppId(config.getBrandedAppId())
                                .setAppIconUrl(config.getAppIconUrl())
                                .setAppName(config.getAppName())
                                .build())
                        .build());
            }
        } else {
            responseObserver.onNext(GetBrandedAppConfigResult.getDefaultInstance());
        }

        responseObserver.onCompleted();
    }

    private GetBrandedAppConfigRequest.BrandedEntity buildBrandedEntity(CompanyModel company) {
        if (Objects.equals(company.getEnterpriseId(), 0L)) {
            return GetBrandedAppConfigRequest.BrandedEntity.newBuilder()
                    .setBrandedId(company.getId())
                    .setBrandedType(AccountNamespaceType.COMPANY)
                    .build();
        } else {
            return GetBrandedAppConfigRequest.BrandedEntity.newBuilder()
                    .setBrandedId(company.getEnterpriseId())
                    .setBrandedType(AccountNamespaceType.ENTERPRISE)
                    .build();
        }
    }
}
