package com.moego.api.v3.business_customer.controller;

import com.moego.api.v3.business_customer.dto.PetVaccineRequestContext;
import com.moego.idl.api.business_customer.v1.ApprovePetVaccineRequestParams;
import com.moego.idl.api.business_customer.v1.ApprovePetVaccineRequestResult;
import com.moego.idl.api.business_customer.v1.BusinessPetVaccineRequestServiceGrpc.BusinessPetVaccineRequestServiceImplBase;
import com.moego.idl.api.business_customer.v1.DeclinePetVaccineRequestParams;
import com.moego.idl.api.business_customer.v1.DeclinePetVaccineRequestResult;
import com.moego.idl.api.business_customer.v1.ReviewPetVaccineRequestParams;
import com.moego.idl.api.business_customer.v1.ReviewPetVaccineRequestResult;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRecordModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRequestModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRequestUpdateDef;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessPetVaccineRecordServiceGrpc.BusinessPetVaccineRecordServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessPetVaccineRequestServiceGrpc.BusinessPetVaccineRequestServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessPetVaccineServiceGrpc.BusinessPetVaccineServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.GetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.GetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.GetPetVaccineRecordRequest;
import com.moego.idl.service.business_customer.v1.GetPetVaccineRequestRequest;
import com.moego.idl.service.business_customer.v1.ListPetVaccineRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetVaccineRequestRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.message.api.INotificationService;
import io.grpc.stub.StreamObserver;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@GrpcService
@AllArgsConstructor
@Slf4j
public class BusinessPetVaccineRequestController extends BusinessPetVaccineRequestServiceImplBase {

    private final BusinessPetVaccineRequestServiceBlockingStub petVaccineRequestService;
    private final BusinessPetVaccineRecordServiceBlockingStub petVaccineRecordService;
    private final BusinessPetVaccineServiceBlockingStub petVaccineService;
    private final BusinessCustomerPetServiceBlockingStub petService;
    private final BusinessCustomerServiceBlockingStub customerService;

    private final INotificationService notificationService;

    @Override
    @Auth(AuthType.COMPANY)
    public void reviewPetVaccineRequest(
            ReviewPetVaccineRequestParams request, StreamObserver<ReviewPetVaccineRequestResult> responseObserver) {

        var context = AuthContext.get();
        var tenant = Tenant.newBuilder().setCompanyId(context.companyId()).build();

        // get pet vaccine request
        var petVaccineRequest = petVaccineRequestService
                .getPetVaccineRequest(GetPetVaccineRequestRequest.newBuilder()
                        .setId(request.getId())
                        .build())
                .getPetVaccineRequest();

        // 已经处理过，标记为失效
        if (petVaccineRequest.getStatus() != BusinessPetVaccineRequestModel.Status.PENDING) {
            log.info("pet vaccine request {} has been {}", petVaccineRequest.getId(), petVaccineRequest.getStatus());
            responseObserver.onNext(
                    ReviewPetVaccineRequestResult.newBuilder().setExpired(true).build());
            responseObserver.onCompleted();
            return;
        }

        var petVaccineRequestCtx = buildPetVaccineRequestCtx(tenant, petVaccineRequest);
        // 相关数据获取异常，标记为失效
        if (petVaccineRequestCtx.getException() != null) {
            responseObserver.onNext(
                    ReviewPetVaccineRequestResult.newBuilder().setExpired(true).build());
            responseObserver.onCompleted();
            return;
        }

        var customer = petVaccineRequestCtx.getCustomer();
        var pet = petVaccineRequestCtx.getPet();
        var petVaccineRecord = petVaccineRequestCtx.getPetVaccineRecord();
        var vaccineNames = petVaccineRequestCtx.getVaccineNames();

        var result = ReviewPetVaccineRequestResult.newBuilder()
                .setCreateTime(petVaccineRequest.getCreateTime())
                .setClient(ReviewPetVaccineRequestResult.Client.newBuilder()
                        .setFirstName(customer.getFirstName())
                        .setLastName(customer.getLastName())
                        .setPhoneNumber(customer.getPhoneNumber())
                        .build())
                .setPet(ReviewPetVaccineRequestResult.Pet.newBuilder()
                        .setPetName(pet.getPetName())
                        .setPetType(pet.getPetType())
                        .setBreed(pet.getBreed())
                        .setAvatarPath(pet.getAvatarPath()));

        var vaccineName = vaccineNames.get(petVaccineRequest.getVaccineId());
        result.setPetVaccineAfter(buildVaccineAfter(petVaccineRequest, vaccineName));

        if (petVaccineRecord != null) {
            vaccineName = vaccineNames.get(petVaccineRecord.getVaccineId());
            result.setPetVaccineBefore(buildVaccineBefore(petVaccineRecord, vaccineName));
        }

        responseObserver.onNext(result.build());
        responseObserver.onCompleted();
    }

    private ReviewPetVaccineRequestResult.Vaccine buildVaccineBefore(
            BusinessPetVaccineRecordModel model, String vaccineName) {
        var builder = ReviewPetVaccineRequestResult.Vaccine.newBuilder()
                .setRecordId(model.getId())
                .setVaccineId(model.getVaccineId())
                .setVaccineName(vaccineName)
                .addAllDocumentUrls(model.getDocumentUrlsList());
        if (model.hasExpirationDate()) {
            builder.setExpirationDate(model.getExpirationDate());
        }
        return builder.build();
    }

    private ReviewPetVaccineRequestResult.Vaccine buildVaccineAfter(
            BusinessPetVaccineRequestModel model, String vaccineName) {
        var builder = ReviewPetVaccineRequestResult.Vaccine.newBuilder()
                .setRecordId(model.getId())
                .setVaccineId(model.getVaccineId())
                .setVaccineName(vaccineName)
                .addAllDocumentUrls(model.getDocumentUrlsList());
        if (model.hasExpirationDate()) {
            builder.setExpirationDate(model.getExpirationDate());
        }
        return builder.build();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void approvePetVaccineRequest(
            ApprovePetVaccineRequestParams request, StreamObserver<ApprovePetVaccineRequestResult> responseObserver) {
        var context = AuthContext.get();
        var tenant = Tenant.newBuilder().setCompanyId(context.companyId()).build();

        var petVaccineRequest = petVaccineRequestService
                .getPetVaccineRequest(GetPetVaccineRequestRequest.newBuilder()
                        .setId(request.getId())
                        .build())
                .getPetVaccineRequest();

        // 检查 pet vaccine request 状态
        if (BusinessPetVaccineRequestModel.Status.DECLINED.equals(petVaccineRequest.getStatus())) {
            // 清除通知，提示报错引导用户刷新页面，刷新后应当不会再出现这个通知，避免重复进行 review
            notificationService.dismissSameNotification(context.companyId(), (int) request.getNotificationId());
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    "This pet vaccine request has already been declined. Please refresh your page.");
        }
        if (BusinessPetVaccineRequestModel.Status.APPROVED.equals(petVaccineRequest.getStatus())) {
            // 清除通知，提示报错引导用户刷新页面，刷新后应当不会再出现这个通知，避免重复进行 review
            notificationService.dismissSameNotification(context.companyId(), (int) request.getNotificationId());
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    "This pet vaccine request has already been approved. Please refresh your page.");
        }

        // 检查 client & pet & vaccine 数据
        var petVaccineRequestCtx = buildPetVaccineRequestCtx(tenant, petVaccineRequest);
        if (petVaccineRequestCtx.getException() != null) {
            // 存在失效数据，导致 pet vaccine request 失效，需要清除通知，避免重复进行 review
            notificationService.dismissSameNotification(context.companyId(), (int) request.getNotificationId());
            throw petVaccineRequestCtx.getException();
        }

        var updateDef = BusinessPetVaccineRequestUpdateDef.newBuilder()
                .setStatus(BusinessPetVaccineRequestModel.Status.APPROVED);
        if (request.hasExpirationDate()) {
            updateDef.setExpirationDate(request.getExpirationDate());
        }

        petVaccineRequestService.updatePetVaccineRequest(UpdatePetVaccineRequestRequest.newBuilder()
                .setId(request.getId())
                .setPetVaccineRequest(updateDef)
                .build());

        // 清除通知，避免重复进行 review
        notificationService.dismissSameNotification(context.companyId(), (int) request.getNotificationId());

        responseObserver.onNext(ApprovePetVaccineRequestResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void declinePetVaccineRequest(
            DeclinePetVaccineRequestParams request, StreamObserver<DeclinePetVaccineRequestResult> responseObserver) {
        var context = AuthContext.get();

        var petVaccineRequest = petVaccineRequestService
                .getPetVaccineRequest(GetPetVaccineRequestRequest.newBuilder()
                        .setId(request.getId())
                        .build())
                .getPetVaccineRequest();

        // 如果已经是 declined，不做任何操作(幂等)
        if (BusinessPetVaccineRequestModel.Status.DECLINED.equals(petVaccineRequest.getStatus())) {
            // 清除通知，避免重复进行 review
            notificationService.dismissSameNotification(context.companyId(), (int) request.getNotificationId());
            responseObserver.onNext(DeclinePetVaccineRequestResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // 如果已经是 approved
        if (BusinessPetVaccineRequestModel.Status.APPROVED.equals(petVaccineRequest.getStatus())) {
            // 清除通知，避免重复进行 review
            notificationService.dismissSameNotification(context.companyId(), (int) request.getNotificationId());
            // force 情况下不抛出异常，直接返回，主要是为了触发前面的 notification 清除
            if (request.getForce()) {
                responseObserver.onNext(DeclinePetVaccineRequestResult.getDefaultInstance());
                responseObserver.onCompleted();
                return;
            }
            // 否则抛出异常，提示报错引导用户刷新页面，刷新后应当不会再出现这个通知，避免重复进行 review
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    "This pet vaccine request has already been approved. Please refresh your page.");
        }

        var updateDef = BusinessPetVaccineRequestUpdateDef.newBuilder()
                .setStatus(BusinessPetVaccineRequestModel.Status.DECLINED);
        petVaccineRequestService.updatePetVaccineRequest(UpdatePetVaccineRequestRequest.newBuilder()
                .setId(request.getId())
                .setPetVaccineRequest(updateDef)
                .build());

        // 清除通知，避免重复进行 review
        notificationService.dismissSameNotification(context.companyId(), (int) request.getNotificationId());

        responseObserver.onNext(DeclinePetVaccineRequestResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private PetVaccineRequestContext buildPetVaccineRequestCtx(
            Tenant tenant, BusinessPetVaccineRequestModel petVaccineRequest) {
        // get pet info
        var pet = petService
                .getPetInfo(GetPetInfoRequest.newBuilder()
                        .setId(petVaccineRequest.getPetId())
                        .setTenant(tenant)
                        .build())
                .getPet();

        if (pet.getDeleted()) {
            log.warn("pet {} has been deleted", pet.getId());
            return PetVaccineRequestContext.builder()
                    .exception(ExceptionUtil.bizException(Code.CODE_PET_NOT_FOUND, "Pet has been deleted."))
                    .build();
        }

        // get customer info
        var customer = customerService
                .getCustomerInfo(GetCustomerInfoRequest.newBuilder()
                        .setId(pet.getCustomerId())
                        .setTenant(tenant)
                        .build())
                .getCustomer();
        if (customer.getDeleted()) {
            log.warn("customer {} has been deleted", customer.getId());
            return PetVaccineRequestContext.builder()
                    .exception(ExceptionUtil.bizException(Code.CODE_CUSTOMER_NOT_FOUND, "Customer has been deleted."))
                    .build();
        }

        // get related pet vaccine record
        BusinessPetVaccineRecordModel petVaccineRecord = null;
        if (petVaccineRequest.getVaccineRecordId() > 0) {
            petVaccineRecord = petVaccineRecordService
                    .getPetVaccineRecord(GetPetVaccineRecordRequest.newBuilder()
                            .setId(petVaccineRequest.getVaccineRecordId())
                            .build())
                    .getVaccineRecord();
            if (petVaccineRecord.getDeleted()) {
                log.warn("pet vaccine record {} has been deleted", petVaccineRecord.getId());
                return PetVaccineRequestContext.builder()
                        .exception(ExceptionUtil.bizException(
                                Code.CODE_PARAMS_ERROR, "Pet vaccine record has been deleted."))
                        .build();
            }
        }

        // get vaccine names
        var vaccineNames = petVaccineService
                .listPetVaccine(ListPetVaccineRequest.newBuilder()
                        .setCompanyId(tenant.getCompanyId())
                        .build())
                .getVaccinesList()
                .stream()
                .collect(Collectors.toMap(BusinessPetVaccineModel::getId, BusinessPetVaccineModel::getName));

        // 检查 vaccine 是否存在
        if (!vaccineNames.containsKey(petVaccineRequest.getVaccineId())) {
            log.warn(
                    "vaccine {} not found for pet vaccine request {}",
                    petVaccineRequest.getVaccineId(),
                    petVaccineRequest.getId());

            return PetVaccineRequestContext.builder()
                    .exception(ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Vaccine not found."))
                    .build();
        }

        if (petVaccineRecord != null && !vaccineNames.containsKey(petVaccineRecord.getVaccineId())) {
            log.warn(
                    "vaccine {} not found for pet vaccine record {}",
                    petVaccineRecord.getVaccineId(),
                    petVaccineRecord.getId());

            return PetVaccineRequestContext.builder()
                    .exception(ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Vaccine not found."))
                    .build();
        }

        return PetVaccineRequestContext.builder()
                .pet(pet)
                .customer(customer)
                .petVaccineRecord(petVaccineRecord)
                .vaccineNames(vaccineNames)
                .build();
    }
}
