package com.moego.api.v3.business_customer.service;

import com.moego.idl.models.business_customer.v1.BusinessPetVaccineModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRecordBindingModel;
import com.moego.idl.service.business_customer.v1.BatchListVaccineRecordRequest;
import com.moego.idl.service.business_customer.v1.BusinessPetVaccineRecordServiceGrpc;
import com.moego.lib.utils.model.Pair;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024/9/27
 */
@Service
@RequiredArgsConstructor
public class BusinessPetVaccineService {

    private final BusinessPetVaccineRecordServiceGrpc.BusinessPetVaccineRecordServiceBlockingStub vaccineRecordStub;

    public Pair<List<BusinessPetVaccineRecordBindingModel>, List<BusinessPetVaccineModel>> listPetVaccines(
            Long companyId, List<Long> petIds) {
        if (companyId == null || ObjectUtils.isEmpty(petIds)) {
            return Pair.of(List.of(), List.of());
        }
        var response = vaccineRecordStub.batchListVaccineRecord(BatchListVaccineRecordRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllPetIds(petIds)
                .setIncludeVaccine(true)
                .build());
        return Pair.of(response.getBindingsList(), response.getVaccinesList());
    }
}
