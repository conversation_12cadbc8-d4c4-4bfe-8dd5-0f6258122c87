package com.moego.api.v3.business_customer.controller;

import com.moego.api.v3.business_customer.converter.PetVaccineConverter;
import com.moego.api.v3.business_customer.service.OfferingService;
import com.moego.idl.api.business_customer.v1.BusinessPetVaccineServiceGrpc;
import com.moego.idl.api.business_customer.v1.CreatePetVaccineParams;
import com.moego.idl.api.business_customer.v1.CreatePetVaccineResult;
import com.moego.idl.api.business_customer.v1.DeletePetVaccineParams;
import com.moego.idl.api.business_customer.v1.DeletePetVaccineResult;
import com.moego.idl.api.business_customer.v1.ListPetVaccineParams;
import com.moego.idl.api.business_customer.v1.ListPetVaccineResult;
import com.moego.idl.api.business_customer.v1.ListPetVaccineTemplateParams;
import com.moego.idl.api.business_customer.v1.ListPetVaccineTemplateResult;
import com.moego.idl.api.business_customer.v1.SortPetVaccineParams;
import com.moego.idl.api.business_customer.v1.SortPetVaccineResult;
import com.moego.idl.api.business_customer.v1.UpdatePetVaccineParams;
import com.moego.idl.api.business_customer.v1.UpdatePetVaccineResult;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineModel;
import com.moego.idl.service.business_customer.v1.BusinessPetVaccineServiceGrpc.BusinessPetVaccineServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.CreatePetVaccineRequest;
import com.moego.idl.service.business_customer.v1.DeletePetVaccineRequest;
import com.moego.idl.service.business_customer.v1.ListPetVaccineRequest;
import com.moego.idl.service.business_customer.v1.ListPetVaccineTemplateRequest;
import com.moego.idl.service.business_customer.v1.SortPetVaccineRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetVaccineRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessPetVaccineController extends BusinessPetVaccineServiceGrpc.BusinessPetVaccineServiceImplBase {

    private final BusinessPetVaccineServiceBlockingStub petVaccineServiceBlockingStub;
    private final OfferingService offeringService;

    private final PetVaccineConverter petVaccineConverter;

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetVaccineTemplate(
            ListPetVaccineTemplateParams request, StreamObserver<ListPetVaccineTemplateResult> responseObserver) {

        var vaccines =
                petVaccineServiceBlockingStub
                        .listPetVaccineTemplate(ListPetVaccineTemplateRequest.getDefaultInstance())
                        .getVaccinesList()
                        .stream()
                        .map(petVaccineConverter::toNameView)
                        .toList();
        var result = ListPetVaccineTemplateResult.newBuilder()
                .addAllVaccines(vaccines)
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetVaccine(ListPetVaccineParams request, StreamObserver<ListPetVaccineResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var listRequest =
                ListPetVaccineRequest.newBuilder().setCompanyId(companyId).build();
        var listResponse = petVaccineServiceBlockingStub.listPetVaccine(listRequest);

        final var vaccineRequirementByService =
                offeringService.listVaccineRequirementsByService(listResponse.getVaccinesList().stream()
                        .map(BusinessPetVaccineModel::getId)
                        .toList());

        var result = ListPetVaccineResult.newBuilder()
                .addAllVaccines(listResponse.getVaccinesList())
                .addAllVaccineRequirementByService(vaccineRequirementByService)
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void createPetVaccine(
            CreatePetVaccineParams request, StreamObserver<CreatePetVaccineResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var createRequest = CreatePetVaccineRequest.newBuilder()
                .setCompanyId(companyId)
                .setVaccine(request.getVaccine())
                .build();
        var createResponse = petVaccineServiceBlockingStub.createPetVaccine(createRequest);

        if (!request.getRequiredByServiceItemTypesList().isEmpty()) {
            offeringService.setVaccineServiceRequirement(
                    createResponse.getVaccine().getId(), request.getRequiredByServiceItemTypesList());
        }

        var result = CreatePetVaccineResult.newBuilder()
                .setVaccine(createResponse.getVaccine())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void updatePetVaccine(
            UpdatePetVaccineParams request, StreamObserver<UpdatePetVaccineResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var updateRequest = UpdatePetVaccineRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .setVaccine(request.getVaccine())
                .build();
        petVaccineServiceBlockingStub.updatePetVaccine(updateRequest);

        if (request.hasRequiredByServiceItemTypes()) {
            offeringService.setVaccineServiceRequirement(
                    request.getId(), request.getRequiredByServiceItemTypes().getTypesList());
        }

        responseObserver.onNext(UpdatePetVaccineResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void sortPetVaccine(SortPetVaccineParams request, StreamObserver<SortPetVaccineResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var sortRequest = SortPetVaccineRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllIds(request.getIdsList())
                .build();
        petVaccineServiceBlockingStub.sortPetVaccine(sortRequest);

        responseObserver.onNext(SortPetVaccineResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void deletePetVaccine(
            DeletePetVaccineParams request, StreamObserver<DeletePetVaccineResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var deleteRequest = DeletePetVaccineRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .build();
        petVaccineServiceBlockingStub.deletePetVaccine(deleteRequest);

        responseObserver.onNext(DeletePetVaccineResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
