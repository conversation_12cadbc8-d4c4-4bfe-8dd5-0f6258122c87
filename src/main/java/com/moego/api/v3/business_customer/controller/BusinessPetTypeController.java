package com.moego.api.v3.business_customer.controller;

import com.moego.idl.api.business_customer.v1.BusinessPetTypeServiceGrpc;
import com.moego.idl.api.business_customer.v1.ListPetTypeParams;
import com.moego.idl.api.business_customer.v1.ListPetTypeResult;
import com.moego.idl.api.business_customer.v1.SortPetTypeParams;
import com.moego.idl.api.business_customer.v1.SortPetTypeResult;
import com.moego.idl.api.business_customer.v1.UpdatePetTypeParams;
import com.moego.idl.api.business_customer.v1.UpdatePetTypeResult;
import com.moego.idl.service.business_customer.v1.BusinessPetBreedServiceGrpc.BusinessPetBreedServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessPetTypeServiceGrpc.BusinessPetTypeServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.GetPetTypeRequest;
import com.moego.idl.service.business_customer.v1.ListPetBreedRequest;
import com.moego.idl.service.business_customer.v1.ListPetTypeRequest;
import com.moego.idl.service.business_customer.v1.SortPetTypeRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetTypeRequest;
import com.moego.idl.service.offering.v1.RemoveServiceFilterRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import java.util.HashMap;
import java.util.Map;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessPetTypeController extends BusinessPetTypeServiceGrpc.BusinessPetTypeServiceImplBase {

    private final BusinessPetTypeServiceBlockingStub petTypeServiceBlockingStub;

    private final BusinessPetBreedServiceBlockingStub petBreedServiceBlockingStub;

    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub
            serviceManagementServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetType(ListPetTypeParams request, StreamObserver<ListPetTypeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var listRequest = ListPetTypeRequest.newBuilder()
                .setCompanyId(companyId)
                .setIncludeUnavailable(request.getIncludeUnavailable())
                .build();
        var listResponse = petTypeServiceBlockingStub.listPetType(listRequest);

        Map<Long, Integer> countMap = new HashMap<>();
        if (request.getIncludeBreedCount()) {
            listResponse.getTypesList().forEach(petType -> {
                var breedList = petBreedServiceBlockingStub.listPetBreed(ListPetBreedRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setPetTypeId(petType.getPetTypeId())
                        .build());
                countMap.put(petType.getId(), breedList.getBreedsCount());
            });
        }

        var result = ListPetTypeResult.newBuilder()
                .addAllTypes(listResponse.getTypesList())
                .putAllBreedCount(countMap)
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void updatePetType(UpdatePetTypeParams request, StreamObserver<UpdatePetTypeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var updateRequest = UpdatePetTypeRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .setType(request.getType())
                .build();
        petTypeServiceBlockingStub.updatePetType(updateRequest);

        // 标记 Pet type 为 unavailable 时，需要从 ServiceFilterByPet 中移除（TODO：通过异步时间通知完成）
        if (!request.getType().getIsAvailable()) {
            var petTypeModel = petTypeServiceBlockingStub.getPetType(GetPetTypeRequest.newBuilder()
                    .setId(request.getId())
                    .setCompanyId(AuthContext.get().companyId())
                    .build());
            serviceManagementServiceBlockingStub.removeServiceFilter(RemoveServiceFilterRequest.newBuilder()
                    .setCompanyId(companyId)
                    .setPetType(petTypeModel.getType().getPetTypeId())
                    .build());
        }

        responseObserver.onNext(UpdatePetTypeResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void sortPetType(SortPetTypeParams request, StreamObserver<SortPetTypeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var sortRequest = SortPetTypeRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllIds(request.getIdsList())
                .build();
        petTypeServiceBlockingStub.sortPetType(sortRequest);

        responseObserver.onNext(SortPetTypeResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
