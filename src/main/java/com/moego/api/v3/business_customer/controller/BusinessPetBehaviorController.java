package com.moego.api.v3.business_customer.controller;

import com.moego.idl.api.business_customer.v1.BusinessPetBehaviorServiceGrpc;
import com.moego.idl.api.business_customer.v1.CreatePetBehaviorParams;
import com.moego.idl.api.business_customer.v1.CreatePetBehaviorResult;
import com.moego.idl.api.business_customer.v1.DeletePetBehaviorParams;
import com.moego.idl.api.business_customer.v1.DeletePetBehaviorResult;
import com.moego.idl.api.business_customer.v1.ListPetBehaviorParams;
import com.moego.idl.api.business_customer.v1.ListPetBehaviorResult;
import com.moego.idl.api.business_customer.v1.SortPetBehaviorParams;
import com.moego.idl.api.business_customer.v1.SortPetBehaviorResult;
import com.moego.idl.api.business_customer.v1.UpdatePetBehaviorParams;
import com.moego.idl.api.business_customer.v1.UpdatePetBehaviorResult;
import com.moego.idl.service.business_customer.v1.BusinessPetBehaviorServiceGrpc.BusinessPetBehaviorServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.CreatePetBehaviorRequest;
import com.moego.idl.service.business_customer.v1.DeletePetBehaviorRequest;
import com.moego.idl.service.business_customer.v1.ListPetBehaviorRequest;
import com.moego.idl.service.business_customer.v1.SortPetBehaviorRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetBehaviorRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessPetBehaviorController extends BusinessPetBehaviorServiceGrpc.BusinessPetBehaviorServiceImplBase {

    private final BusinessPetBehaviorServiceBlockingStub petBehaviorServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetBehavior(ListPetBehaviorParams request, StreamObserver<ListPetBehaviorResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var listRequest =
                ListPetBehaviorRequest.newBuilder().setCompanyId(companyId).build();
        var listResponse = petBehaviorServiceBlockingStub.listPetBehavior(listRequest);

        var result = ListPetBehaviorResult.newBuilder()
                .addAllBehaviors(listResponse.getBehaviorsList())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void createPetBehavior(
            CreatePetBehaviorParams request, StreamObserver<CreatePetBehaviorResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var createRequest = CreatePetBehaviorRequest.newBuilder()
                .setCompanyId(companyId)
                .setBehavior(request.getBehavior())
                .build();
        var createResponse = petBehaviorServiceBlockingStub.createPetBehavior(createRequest);

        var result = CreatePetBehaviorResult.newBuilder()
                .setBehavior(createResponse.getBehavior())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void updatePetBehavior(
            UpdatePetBehaviorParams request, StreamObserver<UpdatePetBehaviorResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var updateRequest = UpdatePetBehaviorRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .setBehavior(request.getBehavior())
                .build();
        petBehaviorServiceBlockingStub.updatePetBehavior(updateRequest);

        responseObserver.onNext(UpdatePetBehaviorResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void sortPetBehavior(SortPetBehaviorParams request, StreamObserver<SortPetBehaviorResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var sortRequest = SortPetBehaviorRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllIds(request.getIdsList())
                .build();
        petBehaviorServiceBlockingStub.sortPetBehavior(sortRequest);

        responseObserver.onNext(SortPetBehaviorResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void deletePetBehavior(
            DeletePetBehaviorParams request, StreamObserver<DeletePetBehaviorResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var deleteRequest = DeletePetBehaviorRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .build();
        petBehaviorServiceBlockingStub.deletePetBehavior(deleteRequest);

        responseObserver.onNext(DeletePetBehaviorResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
