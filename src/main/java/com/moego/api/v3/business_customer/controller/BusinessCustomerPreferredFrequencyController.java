package com.moego.api.v3.business_customer.controller;

import com.moego.idl.api.business_customer.v1.BusinessCustomerPreferredFrequencyServiceGrpc.BusinessCustomerPreferredFrequencyServiceImplBase;
import com.moego.idl.api.business_customer.v1.GetCustomerGroomingFrequencyParams;
import com.moego.idl.api.business_customer.v1.GetCustomerGroomingFrequencyResult;
import com.moego.idl.api.business_customer.v1.UpsertCustomerGroomingFrequencyParams;
import com.moego.idl.api.business_customer.v1.UpsertCustomerGroomingFrequencyResult;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPreferredFrequencyServiceGrpc.BusinessCustomerPreferredFrequencyServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.GetCustomerGroomingFrequencyRequest;
import com.moego.idl.service.business_customer.v1.UpsertCustomerGroomingFrequencyRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessCustomerPreferredFrequencyController extends BusinessCustomerPreferredFrequencyServiceImplBase {

    private final BusinessCustomerPreferredFrequencyServiceBlockingStub customerPreferredFrequencyServiceBlockingStub;

    @Auth(AuthType.COMPANY)
    @Override
    public void getCustomerGroomingFrequency(
            GetCustomerGroomingFrequencyParams request,
            StreamObserver<GetCustomerGroomingFrequencyResult> responseObserver) {

        var getCustomerGroomingFrequencyRequest = GetCustomerGroomingFrequencyRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .build();

        var result = customerPreferredFrequencyServiceBlockingStub.getCustomerGroomingFrequency(
                getCustomerGroomingFrequencyRequest);

        var response = GetCustomerGroomingFrequencyResult.newBuilder()
                .setGroomingFrequency(result.getGroomingFrequency())
                .build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void upsertCustomerGroomingFrequency(
            UpsertCustomerGroomingFrequencyParams request,
            StreamObserver<UpsertCustomerGroomingFrequencyResult> responseObserver) {

        var upsertRequest = UpsertCustomerGroomingFrequencyRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setGroomingFrequency(request.getGroomingFrequency())
                .setApplyToAllCustomers(request.getApplyToAllCustomers())
                .build();

        customerPreferredFrequencyServiceBlockingStub.upsertCustomerGroomingFrequency(upsertRequest);

        responseObserver.onNext(UpsertCustomerGroomingFrequencyResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
