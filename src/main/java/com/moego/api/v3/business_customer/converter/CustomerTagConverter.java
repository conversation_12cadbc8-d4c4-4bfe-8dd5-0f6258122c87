package com.moego.api.v3.business_customer.converter;

import com.moego.idl.models.business_customer.v1.BusinessCustomerTagModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerTagNameView;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CustomerTagConverter {

    BusinessCustomerTagNameView toNameView(BusinessCustomerTagModel model);
}
