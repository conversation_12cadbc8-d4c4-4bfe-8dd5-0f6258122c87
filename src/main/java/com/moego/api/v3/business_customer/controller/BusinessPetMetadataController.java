package com.moego.api.v3.business_customer.controller;

import com.moego.api.v3.business_customer.converter.PetMetadataConverter;
import com.moego.idl.api.business_customer.v1.BusinessPetMetadataServiceGrpc;
import com.moego.idl.api.business_customer.v1.CreatePetMetadataParams;
import com.moego.idl.api.business_customer.v1.CreatePetMetadataResult;
import com.moego.idl.api.business_customer.v1.DeletePetMetadataParams;
import com.moego.idl.api.business_customer.v1.DeletePetMetadataResult;
import com.moego.idl.api.business_customer.v1.ListPetMetadataParams;
import com.moego.idl.api.business_customer.v1.ListPetMetadataResult;
import com.moego.idl.api.business_customer.v1.SortPetMetadataParams;
import com.moego.idl.api.business_customer.v1.SortPetMetadataResult;
import com.moego.idl.api.business_customer.v1.UpdatePetMetadataParams;
import com.moego.idl.api.business_customer.v1.UpdatePetMetadataResult;
import com.moego.idl.models.business_customer.v1.BusinessPetMetadataView;
import com.moego.idl.service.business_customer.v1.BusinessPetMetadataServiceGrpc.BusinessPetMetadataServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.CreatePetMetadataRequest;
import com.moego.idl.service.business_customer.v1.CreatePetMetadataResponse;
import com.moego.idl.service.business_customer.v1.DeletePetMetadataRequest;
import com.moego.idl.service.business_customer.v1.ListPetMetadataRequest;
import com.moego.idl.service.business_customer.v1.ListPetMetadataResponse;
import com.moego.idl.service.business_customer.v1.SortPetMetadataRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetMetadataRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/1/15
 */
@GrpcService
@RequiredArgsConstructor
public class BusinessPetMetadataController extends BusinessPetMetadataServiceGrpc.BusinessPetMetadataServiceImplBase {

    private final BusinessPetMetadataServiceBlockingStub blockingStub;
    private final PetMetadataConverter petMetadataConverter;

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void createPetMetadata(
            CreatePetMetadataParams request, StreamObserver<CreatePetMetadataResult> responseObserver) {
        CreatePetMetadataRequest createPetMetadataRequest = CreatePetMetadataRequest.newBuilder()
                .setMetadata(request.getMetadata())
                .setCompanyId(AuthContext.get().companyId())
                .build();
        CreatePetMetadataResponse response = blockingStub.createPetMetadata(createPetMetadataRequest);

        responseObserver.onNext(
                CreatePetMetadataResult.newBuilder().setId(response.getId()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void updatePetMetadata(
            UpdatePetMetadataParams request, StreamObserver<UpdatePetMetadataResult> responseObserver) {
        UpdatePetMetadataRequest updatePetMetadataRequest = UpdatePetMetadataRequest.newBuilder()
                .setId(request.getId())
                .setMetadataValue(request.getMetadataValue())
                .putAllExtraJson(request.getExtraJsonMap())
                .setCompanyId(AuthContext.get().companyId())
                .build();
        blockingStub.updatePetMetadata(updatePetMetadataRequest);

        responseObserver.onNext(UpdatePetMetadataResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void deletePetMetadata(
            DeletePetMetadataParams request, StreamObserver<DeletePetMetadataResult> responseObserver) {
        DeletePetMetadataRequest deletePetMetadataRequest = DeletePetMetadataRequest.newBuilder()
                .setId(request.getId())
                .setCompanyId(AuthContext.get().companyId())
                .build();
        blockingStub.deletePetMetadata(deletePetMetadataRequest);

        responseObserver.onNext(DeletePetMetadataResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetMetadata(ListPetMetadataParams request, StreamObserver<ListPetMetadataResult> responseObserver) {
        ListPetMetadataRequest listPetMetadataRequest = ListPetMetadataRequest.newBuilder()
                .addAllMetadataNames(request.getMetadataNamesList())
                .setCompanyId(AuthContext.get().companyId())
                .build();
        ListPetMetadataResponse response = blockingStub.listPetMetadata(listPetMetadataRequest);
        List<BusinessPetMetadataView> views = petMetadataConverter.modelToView(response.getMetadataList());

        // 先按 metadataName 从小到大，再按 sort 从大到小排序
        responseObserver.onNext(ListPetMetadataResult.newBuilder()
                .addAllMetadata(views.stream()
                        .sorted((o1, o2) -> {
                            if (o1.getMetadataName().equals(o2.getMetadataName())) {
                                return o2.getSort() - o1.getSort();
                            }
                            return o1.getMetadataName().compareTo(o2.getMetadataName());
                        })
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void sortPetMetadata(SortPetMetadataParams request, StreamObserver<SortPetMetadataResult> responseObserver) {
        SortPetMetadataRequest sortPetMetadataRequest = SortPetMetadataRequest.newBuilder()
                .setMetadataName(request.getMetadataName())
                .addAllIds(request.getIdsList())
                .setCompanyId(AuthContext.get().companyId())
                .build();
        blockingStub.sortPetMetadata(sortPetMetadataRequest);

        responseObserver.onNext(SortPetMetadataResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
