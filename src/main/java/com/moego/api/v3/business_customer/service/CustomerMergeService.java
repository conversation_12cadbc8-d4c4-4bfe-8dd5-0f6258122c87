package com.moego.api.v3.business_customer.service;

import com.google.common.collect.Lists;
import com.moego.api.v3.business_customer.comparator.DuplicateCustomerComparableDTO;
import com.moego.api.v3.business_customer.comparator.PetViewComparator;
import com.moego.api.v3.business_customer.converter.CustomerConverter;
import com.moego.api.v3.membership.converter.MembershipConverter;
import com.moego.api.v3.membership.service.MembershipService;
import com.moego.idl.api.business_customer.v1.ListDuplicateCustomerGroupsResult;
import com.moego.idl.api.business_customer.v1.PreviewCustomerMergeResult;
import com.moego.idl.models.business_customer.v1.BusinessCustomerContactModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.CustomerDuplicationCheckView;
import com.moego.idl.models.business_customer.v1.CustomerMergeStatus;
import com.moego.idl.models.business_customer.v1.MergeRelationDef;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.membership.v1.MembershipSubscriptionListModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.agreement.v1.AgreementRecordServiceGrpc.AgreementRecordServiceBlockingStub;
import com.moego.idl.service.agreement.v1.GetRecentSignedAgreementListByCompanyRequest;
import com.moego.idl.service.appointment.v1.AppointmentCountServiceGrpc.AppointmentCountServiceBlockingStub;
import com.moego.idl.service.appointment.v1.BatchGetTotalAppointmentCountRequest;
import com.moego.idl.service.appointment.v1.BatchGetUpcomingAppointmentCountRequest;
import com.moego.idl.service.business_customer.v1.BatchCheckCustomerMergeStatusRequest;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerContactServiceGrpc.BusinessCustomerContactServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessCustomerMergeServiceGrpc.BusinessCustomerMergeServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.CheckCustomerDuplicationRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.ListCustomerContactRequest;
import com.moego.idl.service.business_customer.v1.ListDuplicateCustomerGroupsRequest;
import com.moego.idl.service.business_customer.v1.ListPetInfoRequest;
import com.moego.idl.service.business_customer.v1.MergeCustomersRequest;
import com.moego.idl.service.business_customer.v1.PreviewPetMergeRelationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.payment.api.IPaymentCreditCardService;
import com.moego.server.payment.dto.CardDTO;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class CustomerMergeService {

    private final BusinessCustomerServiceBlockingStub customerServiceBlockingStub;
    private final BusinessCustomerPetServiceBlockingStub petServiceBlockingStub;
    private final BusinessCustomerContactServiceBlockingStub contactServiceBlockingStub;
    private final BusinessCustomerMergeServiceBlockingStub customerMergeServiceBlockingStub;
    private final AppointmentCountServiceBlockingStub appointmentCountServiceBlockingStub;
    private final AgreementRecordServiceBlockingStub agreementRecordServiceBlockingStub;

    private final IPaymentCreditCardService creditCardService;

    private final MembershipService membershipService;

    private final MembershipConverter membershipConverter;

    public List<ListDuplicateCustomerGroupsResult.Group> listDuplicateCustomerGroups(
            Tenant tenant, int maxGroupCount, Set<Long> workingLocationIds, boolean mask) {

        var groupRequest = ListDuplicateCustomerGroupsRequest.newBuilder()
                .setTenant(tenant)
                .setMaxGroupCount(maxGroupCount)
                .addAllPreferredBusinessIds(workingLocationIds)
                .build();
        var groups = customerMergeServiceBlockingStub
                .listDuplicateCustomerGroups(groupRequest)
                .getGroupsList();

        if (CollectionUtils.isEmpty(groups)) {
            return List.of();
        }

        var customerIds = groups.stream()
                .flatMap(g -> g.getCustomersList().stream())
                .map(CustomerDuplicationCheckView::getId)
                .toList();

        var totalAppointmentCountMap = listTotalAppointmentCount(tenant, customerIds);

        return groups.stream()
                .map(group -> {
                    var customers = group.getCustomersList().stream()
                            .map(customer -> {
                                var customerId = customer.getId();
                                var totalAppointmentCount = totalAppointmentCountMap.getOrDefault(customerId, 0);
                                return CustomerConverter.INSTANCE.toCustomerView(customer, totalAppointmentCount, mask);
                            })
                            .toList();
                    return ListDuplicateCustomerGroupsResult.Group.newBuilder()
                            .addAllCustomers(customers)
                            .build();
                })
                .toList();
    }

    public PreviewCustomerMergeResult previewCustomerMerge(
            Tenant tenant, List<Long> customerIds, Long primaryCustomerId, Set<Long> workingLocationIds, boolean mask) {

        if (primaryCustomerId != null) {
            checkPrimaryCustomerId(customerIds, primaryCustomerId);
        }
        checkCustomerDuplication(tenant, customerIds);

        var customers = listCustomers(tenant, customerIds);

        if (!CollectionUtils.isEmpty(workingLocationIds)) {
            var hasPermission =
                    customers.stream().allMatch(c -> workingLocationIds.contains(c.getPreferredBusinessId()));
            if (!hasPermission) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PERMISSION_NOT_ENOUGH, "Permission not enough to merge these customers.");
            }
        }

        var subscriptions = membershipService.getAllSubscriptions(customerIds);
        var cards = listCustomerCreditCards(customers);
        var totalAppointmentCountMap = listTotalAppointmentCount(tenant, customerIds);
        var upcomingAppointmentCountMap = listUpcomingAppointmentCount(tenant, customerIds);

        // sort customer
        var sortedCustomerIds = sortCustomers(customerIds, cards, subscriptions, totalAppointmentCountMap);
        var customerMap = customers.stream().collect(Collectors.toMap(BusinessCustomerInfoModel::getId, c -> c));
        var sortedCustomers = sortedCustomerIds.stream()
                .map(customerMap::get)
                .map(customer -> CustomerConverter.INSTANCE.toCustomerView(
                        customer,
                        Optional.ofNullable(subscriptions.get(customer.getId())).isPresent(),
                        mask))
                .toList();

        var targetCustomerId = primaryCustomerId != null ? primaryCustomerId : sortedCustomerIds.get(0);
        var sourceCustomerIds = sortedCustomerIds.stream()
                .filter(id -> !Objects.equals(id, targetCustomerId))
                .toList();

        var pets = listAlivePets(tenant, targetCustomerId, sourceCustomerIds);
        var contacts = listCustomerContacts(targetCustomerId, sourceCustomerIds, mask);
        var agreements = listCustomerSignedAgreements(tenant, targetCustomerId);
        var targetCards = Optional.ofNullable(cards.get(targetCustomerId)).orElse(new ArrayList<>()).stream()
                .map(membershipConverter::creditCardPublicView)
                .toList();
        var membershipSubscriptions = Optional.ofNullable(subscriptions.get(targetCustomerId))
                .map(MembershipSubscriptionListModel::getMembershipSubscriptionsList)
                .orElse(new ArrayList<>());
        var totalAppointmentCount = totalAppointmentCountMap.values().stream()
                .mapToInt(Integer::intValue)
                .sum();
        var upcomingAppointmentCount = upcomingAppointmentCountMap.values().stream()
                .mapToInt(Integer::intValue)
                .sum();

        return PreviewCustomerMergeResult.newBuilder()
                .setPrimaryCustomerId(targetCustomerId)
                .addAllCustomers(sortedCustomers)
                .addAllPets(pets)
                .addAllContacts(contacts)
                .addAllCreditCards(targetCards)
                .addAllMembershipSubscriptions(membershipSubscriptions)
                .addAllSignedAgreements(agreements)
                .setTotalAppointmentCount(totalAppointmentCount)
                .setUpcomingAppointmentCount(upcomingAppointmentCount)
                .build();
    }

    public void mergeCustomers(
            Tenant tenant, List<Long> customerIds, long primaryCustomerId, Set<Long> workingLocationIds) {

        checkPrimaryCustomerId(customerIds, primaryCustomerId);

        var customers = listCustomers(tenant, customerIds);

        if (!CollectionUtils.isEmpty(workingLocationIds)) {
            var hasPermission =
                    customers.stream().allMatch(c -> workingLocationIds.contains(c.getPreferredBusinessId()));
            if (!hasPermission) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PERMISSION_NOT_ENOUGH, "Permission not enough to merge these customers.");
            }
        }

        var sourceCustomerIds =
                customerIds.stream().filter(id -> !id.equals(primaryCustomerId)).toList();

        var mergeRelation =
                MergeRelationDef.newBuilder().setTargetId(primaryCustomerId).addAllSourceIds(sourceCustomerIds);

        var request = MergeCustomersRequest.newBuilder()
                .setTenant(tenant)
                .setCustomerMergeRelation(mergeRelation)
                .build();

        customerMergeServiceBlockingStub.mergeCustomers(request);
    }

    public CustomerMergeStatus checkCustomerMergeStatus(Tenant tenant, long customerId) {
        var getRequest = GetCustomerInfoRequest.newBuilder()
                .setTenant(tenant)
                .setId(customerId)
                .build();
        var customer = customerServiceBlockingStub.getCustomerInfo(getRequest).getCustomer();
        if (customer.getDeleted()) {
            throw ExceptionUtil.bizException(Code.CODE_CUSTOMER_NOT_FOUND);
        }

        var checkRequest = BatchCheckCustomerMergeStatusRequest.newBuilder()
                .addCustomerIds(customerId)
                .build();

        return customerMergeServiceBlockingStub
                .batchCheckCustomerMergeStatus(checkRequest)
                .getStatusMap()
                .getOrDefault(customerId, CustomerMergeStatus.NO_MERGE_RECORD);
    }

    private void checkPrimaryCustomerId(List<Long> customerIds, long primaryCustomerId) {
        // primaryCustomerId 必须在 customerIds 中
        if (!customerIds.contains(primaryCustomerId)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
    }

    private List<BusinessCustomerInfoModel> listCustomers(Tenant tenant, List<Long> customerIds) {
        var request = BatchGetCustomerInfoRequest.newBuilder()
                .setTenant(tenant)
                .addAllIds(customerIds)
                .build();

        var customers =
                customerServiceBlockingStub.batchGetCustomerInfo(request).getCustomersList();

        // check if all customer ids exist and not deleted
        var existCustomerIds = customers.stream()
                .filter(c -> !c.getDeleted())
                .map(BusinessCustomerInfoModel::getId)
                .collect(Collectors.toSet());
        var notExist = customerIds.stream().anyMatch(id -> !existCustomerIds.contains(id));
        if (notExist) {
            throw ExceptionUtil.bizException(Code.CODE_CUSTOMER_NOT_FOUND);
        }

        return customers;
    }

    private void checkCustomerDuplication(Tenant tenant, List<Long> customerIds) {
        // check if all customers are duplicate
        var duplicate = customerMergeServiceBlockingStub
                .checkCustomerDuplication(CheckCustomerDuplicationRequest.newBuilder()
                        .setTenant(tenant)
                        .addAllCustomerIds(customerIds)
                        .build())
                .getDuplicate();
        if (!duplicate) {
            throw ExceptionUtil.bizException(
                    Code.CODE_CUSTOMER_NOT_DUPLICATE, "This duplicate group is out of date. Please refresh the page.");
        }
    }

    private List<Long> sortCustomers(
            List<Long> customerIds,
            Map<Long, List<CardDTO>> cards,
            Map<Long, MembershipSubscriptionListModel> subscriptions,
            Map<Long, Integer> totalAppointmentCountMap) {
        return customerIds.stream()
                .map(id -> {
                    var creditCardCount =
                            Optional.ofNullable(cards.get(id)).map(List::size).orElse(0);

                    var membershipCount = Optional.ofNullable(subscriptions.get(id))
                            .map(s -> s.getMembershipSubscriptionsList().size())
                            .orElse(0);

                    var totalAppointmentCount = totalAppointmentCountMap.getOrDefault(id, 0);

                    return DuplicateCustomerComparableDTO.builder()
                            .id(id)
                            .creditCardCount(creditCardCount)
                            .membershipCount(membershipCount)
                            .totalAppointmentCount(totalAppointmentCount)
                            .build();
                })
                .sorted()
                .map(DuplicateCustomerComparableDTO::getId)
                .toList();
    }

    private Map<Long, List<CardDTO>> listCustomerCreditCards(List<BusinessCustomerInfoModel> customers) {
        var creditCardMap = new HashMap<Long, List<CardDTO>>();
        if (CollectionUtils.isEmpty(customers)) {
            return creditCardMap;
        }
        for (var customer : customers) {
            var customerId = customer.getId();
            var businessId = customer.getPreferredBusinessId();
            var creditCards = creditCardService.getCreditCardList((int) businessId, (int) customerId);
            creditCardMap.put(customerId, creditCards);
        }
        return creditCardMap;
    }

    private Map<Long, Integer> listTotalAppointmentCount(Tenant tenant, List<Long> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return Map.of();
        }

        var resultMap = new HashMap<Long, Integer>();
        Lists.partition(customerIds, 100).forEach(ids -> {
            var request = BatchGetTotalAppointmentCountRequest.newBuilder()
                    .setCompanyId(tenant.getCompanyId())
                    .addAllCustomerIds(ids)
                    .build();

            var countMap = appointmentCountServiceBlockingStub
                    .batchGetTotalAppointmentCount(request)
                    .getCountMap();

            resultMap.putAll(countMap);
        });

        return resultMap;
    }

    private Map<Long, Integer> listUpcomingAppointmentCount(Tenant tenant, List<Long> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return Map.of();
        }

        var resultMap = new HashMap<Long, Integer>();
        Lists.partition(customerIds, 100).forEach(ids -> {
            var request = BatchGetUpcomingAppointmentCountRequest.newBuilder()
                    .setCompanyId(tenant.getCompanyId())
                    .addAllCustomerIds(ids)
                    .build();

            var countMap = appointmentCountServiceBlockingStub
                    .batchGetUpcomingAppointmentCount(request)
                    .getCountMap();

            resultMap.putAll(countMap);
        });

        return resultMap;
    }

    private List<PreviewCustomerMergeResult.PetView> listAlivePets(
            Tenant tenant, long targetCustomerId, List<Long> sourceCustomerIds) {
        var customerMergeRelation = MergeRelationDef.newBuilder()
                .setTargetId(targetCustomerId)
                .addAllSourceIds(sourceCustomerIds)
                .build();
        var previewRequest = PreviewPetMergeRelationRequest.newBuilder()
                .setTenant(tenant)
                .setCustomerMergeRelation(customerMergeRelation)
                .build();
        var petMergeRelations = customerMergeServiceBlockingStub
                .previewPetMergeRelation(previewRequest)
                .getPetMergeRelationsList();

        var targetPetIds =
                petMergeRelations.stream().map(MergeRelationDef::getTargetId).collect(Collectors.toSet());
        var sourcePetIds = petMergeRelations.stream()
                .flatMap(r -> r.getSourceIdsList().stream())
                .collect(Collectors.toSet());

        var filter = ListPetInfoRequest.Filter.newBuilder()
                .addCompanyIds(tenant.getCompanyId())
                .addCustomerIds(targetCustomerId)
                .addAllCustomerIds(sourceCustomerIds)
                .setIncludePassedAway(false)
                .build();
        var listPetRequest = ListPetInfoRequest.newBuilder().setFilter(filter).build();
        return petServiceBlockingStub.listPetInfo(listPetRequest).getPetsList().stream()
                // 在 sourcePetIds 里的是被合并的 pet, 不展示
                .filter(p -> !sourcePetIds.contains(p.getId()))
                // 在 targetPetIds 里的是有冲突的 pet, 不在 targetPetIds 里的是没冲突的 pet
                .map(pet -> CustomerConverter.INSTANCE.toPetView(pet, targetPetIds.contains(pet.getId())))
                .sorted(new PetViewComparator())
                .toList();
    }

    private List<PreviewCustomerMergeResult.ContactView> listCustomerContacts(
            long targetCustomerId, List<Long> sourceCustomerIds, boolean mask) {
        var allContacts = new ArrayList<BusinessCustomerContactModel>();
        // 根据产品需求, sourceCustomerIds 的 contact 优先展示, targetCustomerId 的 contact 放在后面
        var customerIds = new ArrayList<>(sourceCustomerIds);
        customerIds.add(targetCustomerId);
        for (var customerId : customerIds) {
            var contactType = ListCustomerContactRequest.ContactType.newBuilder()
                    // targetCustomerId 不需要列出 main contact
                    .setMain(!Objects.equals(targetCustomerId, customerId))
                    .setAdditional(true);

            var request = ListCustomerContactRequest.newBuilder()
                    .setCustomerId(customerId)
                    .setContactType(contactType)
                    .build();
            var contacts =
                    contactServiceBlockingStub.listCustomerContact(request).getContactsList();
            allContacts.addAll(contacts);
        }

        return allContacts.stream()
                .map(contact -> CustomerConverter.INSTANCE.toContactView(contact, mask))
                .toList();
    }

    private List<PreviewCustomerMergeResult.AgreementView> listCustomerSignedAgreements(
            Tenant tenant, long customerId) {
        var request = GetRecentSignedAgreementListByCompanyRequest.newBuilder()
                .setCompanyId(tenant.getCompanyId())
                .setCustomerId(customerId)
                .build();
        return agreementRecordServiceBlockingStub
                .getRecentSignedAgreementListByCompany(request)
                .getAgreementRecentViewList()
                .stream()
                .map(CustomerConverter.INSTANCE::toAgreementView)
                .toList();
    }
}
