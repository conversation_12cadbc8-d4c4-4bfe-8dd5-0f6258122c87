package com.moego.api.v3.business_customer.controller;

import com.moego.api.v3.business_customer.converter.CustomerTagConverter;
import com.moego.idl.api.business_customer.v1.BusinessCustomerTagServiceGrpc;
import com.moego.idl.api.business_customer.v1.CreateCustomerTagParams;
import com.moego.idl.api.business_customer.v1.CreateCustomerTagResult;
import com.moego.idl.api.business_customer.v1.DeleteCustomerTagParams;
import com.moego.idl.api.business_customer.v1.DeleteCustomerTagResult;
import com.moego.idl.api.business_customer.v1.ListCustomerTagParams;
import com.moego.idl.api.business_customer.v1.ListCustomerTagResult;
import com.moego.idl.api.business_customer.v1.ListCustomerTagTemplateParams;
import com.moego.idl.api.business_customer.v1.ListCustomerTagTemplateResult;
import com.moego.idl.api.business_customer.v1.SortCustomerTagParams;
import com.moego.idl.api.business_customer.v1.SortCustomerTagResult;
import com.moego.idl.api.business_customer.v1.UpdateCustomerTagParams;
import com.moego.idl.api.business_customer.v1.UpdateCustomerTagResult;
import com.moego.idl.service.business_customer.v1.BusinessCustomerTagServiceGrpc.BusinessCustomerTagServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.CreateCustomerTagRequest;
import com.moego.idl.service.business_customer.v1.DeleteCustomerTagRequest;
import com.moego.idl.service.business_customer.v1.ListCustomerTagRequest;
import com.moego.idl.service.business_customer.v1.ListCustomerTagTemplateRequest;
import com.moego.idl.service.business_customer.v1.SortCustomerTagRequest;
import com.moego.idl.service.business_customer.v1.UpdateCustomerTagRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessCustomerTagController extends BusinessCustomerTagServiceGrpc.BusinessCustomerTagServiceImplBase {

    private final BusinessCustomerTagServiceBlockingStub customerTagServiceBlockingStub;

    private final CustomerTagConverter customerTagConverter;

    @Override
    @Auth(AuthType.COMPANY)
    public void listCustomerTag(ListCustomerTagParams request, StreamObserver<ListCustomerTagResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var listRequest =
                ListCustomerTagRequest.newBuilder().setCompanyId(companyId).build();
        var listResponse = customerTagServiceBlockingStub.listCustomerTag(listRequest);

        var result = ListCustomerTagResult.newBuilder()
                .addAllTags(listResponse.getTagsList())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listCustomerTagTemplate(
            ListCustomerTagTemplateParams request, StreamObserver<ListCustomerTagTemplateResult> responseObserver) {

        var listResponse =
                customerTagServiceBlockingStub
                        .listCustomerTagTemplate(ListCustomerTagTemplateRequest.getDefaultInstance())
                        .getTagsList()
                        .stream()
                        .map(customerTagConverter::toNameView)
                        .toList();

        var result = ListCustomerTagTemplateResult.newBuilder()
                .addAllTags(listResponse)
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void createCustomerTag(
            CreateCustomerTagParams request, StreamObserver<CreateCustomerTagResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var createRequest = CreateCustomerTagRequest.newBuilder()
                .setCompanyId(companyId)
                .setTag(request.getTag())
                .build();
        var createResponse = customerTagServiceBlockingStub.createCustomerTag(createRequest);

        var result = CreateCustomerTagResult.newBuilder()
                .setTag(createResponse.getTag())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void updateCustomerTag(
            UpdateCustomerTagParams request, StreamObserver<UpdateCustomerTagResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var updateRequest = UpdateCustomerTagRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .setTag(request.getTag())
                .build();
        customerTagServiceBlockingStub.updateCustomerTag(updateRequest);

        responseObserver.onNext(UpdateCustomerTagResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void sortCustomerTag(SortCustomerTagParams request, StreamObserver<SortCustomerTagResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var sortRequest = SortCustomerTagRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllIds(request.getIdsList())
                .build();
        customerTagServiceBlockingStub.sortCustomerTag(sortRequest);

        responseObserver.onNext(SortCustomerTagResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void deleteCustomerTag(
            DeleteCustomerTagParams request, StreamObserver<DeleteCustomerTagResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var deleteRequest = DeleteCustomerTagRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .build();
        customerTagServiceBlockingStub.deleteCustomerTag(deleteRequest);

        responseObserver.onNext(DeleteCustomerTagResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
