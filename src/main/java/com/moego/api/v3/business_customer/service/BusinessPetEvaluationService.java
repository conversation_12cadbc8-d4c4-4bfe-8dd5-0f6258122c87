package com.moego.api.v3.business_customer.service;

import com.moego.api.v3.shared.helper.EvaluationHelper;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.api.business_customer.v1.ListEvaluationStatusByPetServiceParams;
import com.moego.idl.api.business_customer.v1.ListEvaluationStatusByPetServiceResult;
import com.moego.idl.models.business_customer.v1.PetEvaluationModel;
import com.moego.idl.models.customer.v1.EvaluationStatus;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.ListPetEvaluationRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class BusinessPetEvaluationService {

    private final com.moego.idl.service.business_customer.v1.BusinessPetEvaluationServiceGrpc
                    .BusinessPetEvaluationServiceBlockingStub
            petEvaluationClient;

    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceClient;
    private final EvaluationHelper evaluationHelper;

    /**
     * 根据 pet 和 service 信息，计算是否需要 evaluation
     *
     * @return 每个 pet-service 组合的 evaluation status 结果列表
     */
    public List<ListEvaluationStatusByPetServiceResult.EvaluationStatusResult> listEvaluationStatusByPetService(
            Long companyId, List<ListEvaluationStatusByPetServiceParams.PetServiceIdParams> petServiceIdList) {

        List<Long> petIds = petServiceIdList.stream()
                .map(ListEvaluationStatusByPetServiceParams.PetServiceIdParams::getPetId)
                .distinct()
                .toList();
        List<Long> serviceIds = petServiceIdList.stream()
                .map(ListEvaluationStatusByPetServiceParams.PetServiceIdParams::getServiceId)
                .distinct()
                .toList();

        // 获取pet evaluation和service信息
        Map<Long, List<PetEvaluationModel>> petEvaluationsMap = getPetEvaluationsMap(companyId, petIds);
        Map<Long, ServiceBriefView> serviceMap = getServiceMap(serviceIds);

        var evaluationIds = petEvaluationsMap.values().stream()
                .flatMap(Collection::stream)
                .map(PetEvaluationModel::getEvaluationId)
                .filter(CommonUtil::isNormal)
                .collect(Collectors.toSet());
        var evaluationIdToEvaluation = evaluationHelper.listEvaluation(evaluationIds);

        List<ListEvaluationStatusByPetServiceResult.EvaluationStatusResult> results = new ArrayList<>();

        // 为每个 pet-service 组合生成结果
        for (var petServiceIdParams : petServiceIdList) {
            var petId = petServiceIdParams.getPetId();
            var service = serviceMap.get(petServiceIdParams.getServiceId());
            var petEvaluations = petEvaluationsMap.getOrDefault(petId, Collections.emptyList());
            ListEvaluationStatusByPetServiceResult.EvaluationStatusResult result;
            if (service == null) {
                result = ListEvaluationStatusByPetServiceResult.EvaluationStatusResult.newBuilder()
                        .setPetId(petId)
                        .setServiceId(petServiceIdParams.getServiceId())
                        .setFinalEvaluationStatus(EvaluationStatus.PASS)
                        .setIsEvaluationRequired(false)
                        .setIsEvaluationRequiredForOb(false)
                        .setEvaluationId(0)
                        .build();
            } else {
                result = buildEvaluationStatusResult(petId, service, petEvaluations);
            }

            var evaluationBriefView = evaluationIdToEvaluation.get(result.getEvaluationId());

            if (result.getEvaluationId() == 0
                    || !result.getIsEvaluationRequired()
                    || (evaluationBriefView != null && !evaluationBriefView.getIsActive())) {
                result = result.toBuilder()
                        .setFinalEvaluationStatus(EvaluationStatus.PASS)
                        .build();
            }
            results.add(result);
        }

        return results;
    }

    /**
     * 获取 pet evaluation 并按 pet ID返回 map
     */
    private Map<Long, List<PetEvaluationModel>> getPetEvaluationsMap(Long companyId, List<Long> petIds) {
        var petEvaluationList = petEvaluationClient
                .listPetEvaluation(ListPetEvaluationRequest.newBuilder()
                        .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                        .addAllPetIds(petIds)
                        .build())
                .getPetEvaluationsList();

        Map<Long, List<PetEvaluationModel>> petEvaluationsMap = new HashMap<>();
        for (PetEvaluationModel petEvaluation : petEvaluationList) {
            petEvaluationsMap
                    .computeIfAbsent(petEvaluation.getPetId(), k -> new ArrayList<>())
                    .add(petEvaluation);
        }

        return petEvaluationsMap;
    }

    /**
     * 获取 service 并按 service ID 组织
     */
    private Map<Long, ServiceBriefView> getServiceMap(List<Long> serviceIds) {
        var serviceList = serviceClient
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .addAllServiceIds(serviceIds)
                        .build())
                .getServicesList();

        return serviceList.stream().collect(Collectors.toMap(ServiceBriefView::getId, s -> s));
    }

    /**
     * 为pet-service组合构建evaluation status结果
     */
    private static ListEvaluationStatusByPetServiceResult.EvaluationStatusResult buildEvaluationStatusResult(
            Long petId, ServiceBriefView service, List<PetEvaluationModel> petEvaluations) {
        // service id 一定有值 ，petEvaluations 也有值
        // 提取service evaluation要求
        boolean isEvaluationRequired = service.getIsEvaluationRequired();
        boolean isEvaluationRequiredForOb = service.getIsEvaluationRequiredForOb();
        long evaluationId = service.getEvaluationId();
        long serviceId = service.getId();

        // 构建evaluation result
        var resultBuilder = ListEvaluationStatusByPetServiceResult.EvaluationStatusResult.newBuilder()
                .setPetId(petId)
                .setServiceId(serviceId)
                .setIsEvaluationRequired(isEvaluationRequired)
                .setIsEvaluationRequiredForOb(isEvaluationRequiredForOb)
                .setFinalEvaluationStatus(EvaluationStatus.PASS)
                .setEvaluationId(evaluationId);
        if (!service.getInactive() && service.getIsEvaluationRequired() && service.getEvaluationId() > 0) {
            // 查找匹配的evaluation
            PetEvaluationModel matchingEvaluation = findMatchingEvaluation(petEvaluations, evaluationId);
            if (matchingEvaluation != null) {
                resultBuilder.setFinalEvaluationStatus(matchingEvaluation.getEvaluationStatus());
            }
        }
        return resultBuilder.build();
    }

    /**
     * 查找特定 evaluation ID 的匹配 evaluation
     */
    private static PetEvaluationModel findMatchingEvaluation(List<PetEvaluationModel> evaluations, long evaluationId) {
        if (CollectionUtils.isEmpty(evaluations)) {
            return null;
        }
        return evaluations.stream()
                .filter(eval -> eval.getEvaluationId() == evaluationId)
                .findFirst()
                .orElse(null);
    }
}
