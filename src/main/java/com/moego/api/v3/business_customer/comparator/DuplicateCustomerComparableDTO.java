package com.moego.api.v3.business_customer.comparator;

import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class DuplicateCustomerComparableDTO implements Comparable<DuplicateCustomerComparableDTO> {

    private long id;
    private int creditCardCount;
    private int membershipCount;
    private int totalAppointmentCount;

    @Override
    public int compareTo(DuplicateCustomerComparableDTO o) {
        // sort by credit card count, greater first
        if (o.creditCardCount != this.creditCardCount) {
            return o.creditCardCount - this.creditCardCount;
        }

        // sort by membership count, greater first
        if (o.membershipCount != this.membershipCount) {
            return o.membershipCount - this.membershipCount;
        }

        // sort by total appointment count, greater first
        if (o.totalAppointmentCount != this.totalAppointmentCount) {
            return o.totalAppointmentCount - this.totalAppointmentCount;
        }

        // sort by id, smaller first
        return (int) (this.id - o.id);
    }

    @Override
    public boolean equals(Object obj) {
        if (!(obj instanceof DuplicateCustomerComparableDTO other)) {
            return false;
        }
        return this.id == other.id;
    }

    @Override
    public int hashCode() {
        return Long.hashCode(this.id);
    }
}
