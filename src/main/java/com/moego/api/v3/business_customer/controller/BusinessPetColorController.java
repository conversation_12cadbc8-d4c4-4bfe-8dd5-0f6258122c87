package com.moego.api.v3.business_customer.controller;

import com.moego.api.v3.business_customer.service.BusinessColorService;
import com.moego.idl.api.business_customer.v1.BindingColorParams;
import com.moego.idl.api.business_customer.v1.BindingColorResult;
import com.moego.idl.api.business_customer.v1.BusinessPetColorServiceGrpc;
import com.moego.idl.api.business_customer.v1.CreatePetColorParams;
import com.moego.idl.api.business_customer.v1.CreatePetColorResult;
import com.moego.idl.api.business_customer.v1.DeletePetColorParams;
import com.moego.idl.api.business_customer.v1.DeletePetColorResult;
import com.moego.idl.api.business_customer.v1.ListPetBindingParams;
import com.moego.idl.api.business_customer.v1.ListPetBindingResult;
import com.moego.idl.api.business_customer.v1.ListPetColorParams;
import com.moego.idl.api.business_customer.v1.ListPetColorResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessPetColorController extends BusinessPetColorServiceGrpc.BusinessPetColorServiceImplBase {
    private final BusinessColorService colorService;

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetColor(ListPetColorParams request, StreamObserver<ListPetColorResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var resp = colorService.listPetColor(companyId);
        responseObserver.onNext(resp);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void createPetColor(CreatePetColorParams request, StreamObserver<CreatePetColorResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var resp = colorService.createColor(companyId, request.getName());
        responseObserver.onNext(resp);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deletePetColor(DeletePetColorParams request, StreamObserver<DeletePetColorResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var resp = colorService.deleteColor(companyId, request.getColorId());
        responseObserver.onNext(
                DeletePetColorResult.newBuilder().setResult(resp).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetBinding(ListPetBindingParams request, StreamObserver<ListPetBindingResult> responseObserver) {
        // 目前没有color id查找binding 的需求, 先留个口子
        var result = colorService.listPetBindingColor(null, request.getPetId());
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void bindingColor(BindingColorParams request, StreamObserver<BindingColorResult> responseObserver) {
        colorService.bindingColor(request.getPetId(), request.getColorId());
        responseObserver.onNext(BindingColorResult.newBuilder().setResult(true).build());
        responseObserver.onCompleted();
    }
}
