package com.moego.api.v3.business_customer.dto;

import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRecordModel;
import com.moego.lib.common.exception.BizException;
import java.util.Map;
import lombok.Builder;
import lombok.Getter;

@Builder
@Getter
public class PetVaccineRequestContext {
    private BizException exception;
    private BusinessCustomerPetInfoModel pet;
    private BusinessCustomerInfoModel customer;
    private BusinessPetVaccineRecordModel petVaccineRecord;
    private Map<Long, String> vaccineNames;
}
