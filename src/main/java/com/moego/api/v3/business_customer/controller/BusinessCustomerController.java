package com.moego.api.v3.business_customer.controller;

import com.moego.api.v3.business_customer.service.CustomerMergeService;
import com.moego.idl.api.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.api.business_customer.v1.CheckCustomerMergeStatusParams;
import com.moego.idl.api.business_customer.v1.CheckCustomerMergeStatusResult;
import com.moego.idl.api.business_customer.v1.ListDuplicateCustomerGroupsParams;
import com.moego.idl.api.business_customer.v1.ListDuplicateCustomerGroupsResult;
import com.moego.idl.api.business_customer.v1.MergeCustomersParams;
import com.moego.idl.api.business_customer.v1.MergeCustomersResult;
import com.moego.idl.api.business_customer.v1.PreviewCustomerMergeParams;
import com.moego.idl.api.business_customer.v1.PreviewCustomerMergeResult;
import com.moego.idl.api.business_customer.v1.UpdateCustomerPreferredBusinessParams;
import com.moego.idl.api.business_customer.v1.UpdateCustomerPreferredBusinessResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.metadata.v1.OwnerType;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.UpdateCustomerPreferredBusinessRequest;
import com.moego.idl.service.metadata.v1.ExtractValuesRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.idl.service.organization.v1.GetStaffDetailRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import io.grpc.stub.StreamObserver;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessCustomerController extends BusinessCustomerServiceGrpc.BusinessCustomerServiceImplBase {

    private final BusinessCustomerServiceBlockingStub customerServiceBlockingStub;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub;
    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;

    private final CustomerMergeService customerMergeService;

    private final PermissionHelper permissionHelper;

    @Override
    @Auth(AuthType.COMPANY)
    public void updateCustomerPreferredBusiness(
            UpdateCustomerPreferredBusinessParams request,
            StreamObserver<UpdateCustomerPreferredBusinessResult> responseObserver) {

        var context = AuthContext.get();
        var companyId = context.companyId();
        var staffId = context.staffId();
        var targetBusinessId = request.getPreferredBusinessId();

        // check working location
        var workingLocationIds = getStaffWorkingLocationIds(companyId, staffId);
        boolean hasPermission = workingLocationIds.contains(targetBusinessId);

        if (!hasPermission) {
            throw ExceptionUtil.bizException(Code.CODE_PERMISSION_NOT_ENOUGH);
        }

        var updateRequest = UpdateCustomerPreferredBusinessRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                .setCustomerId(request.getCustomerId())
                .setPreferredBusinessId(request.getPreferredBusinessId())
                .setUpdatedBy(AuthContext.get().staffId())
                .build();
        customerServiceBlockingStub.updateCustomerPreferredBusiness(updateRequest);

        responseObserver.onNext(UpdateCustomerPreferredBusinessResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listDuplicateCustomerGroups(
            ListDuplicateCustomerGroupsParams request,
            StreamObserver<ListDuplicateCustomerGroupsResult> responseObserver) {

        var companyId = AuthContext.get().companyId();
        var staffId = AuthContext.get().staffId();

        // check feature flag
        if (!enableMergeClient(companyId)) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN);
        }

        // check permission and get working locations
        var workingLocationIds = getStaffWorkingLocationIds(companyId, staffId, PermissionEnums.MERGE_CLIENT);
        var mask = !permissionHelper.hasPermission(
                companyId, staffId, PermissionEnums.ACCESS_CLIENT_EMAIL_AND_PHONE_NUMBER);

        var tenant = Tenant.newBuilder().setCompanyId(companyId).build();
        var maxGroupCount = request.getMaxGroupCount();

        var groups = customerMergeService.listDuplicateCustomerGroups(tenant, maxGroupCount, workingLocationIds, mask);

        var result = ListDuplicateCustomerGroupsResult.newBuilder()
                .addAllGroups(groups)
                .build();
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void previewCustomerMerge(
            PreviewCustomerMergeParams request, StreamObserver<PreviewCustomerMergeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();
        var staffId = AuthContext.get().staffId();

        // check feature flag
        if (!enableMergeClient(companyId)) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN);
        }

        // check permission and get working locations
        var workingLocationIds = getStaffWorkingLocationIds(companyId, staffId, PermissionEnums.MERGE_CLIENT);
        var mask = !permissionHelper.hasPermission(
                companyId, staffId, PermissionEnums.ACCESS_CLIENT_EMAIL_AND_PHONE_NUMBER);

        var tenant = Tenant.newBuilder().setCompanyId(companyId).build();
        var customerIds = request.getCustomerIdsList();
        var primaryCustomerId = request.hasPrimaryCustomerId() ? request.getPrimaryCustomerId() : null;

        var result = customerMergeService.previewCustomerMerge(
                tenant, customerIds, primaryCustomerId, workingLocationIds, mask);

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void mergeCustomers(MergeCustomersParams request, StreamObserver<MergeCustomersResult> responseObserver) {

        var companyId = AuthContext.get().companyId();
        var staffId = AuthContext.get().staffId();

        // check feature flag
        if (!enableMergeClient(companyId)) {
            throw ExceptionUtil.bizException(Code.CODE_FORBIDDEN);
        }

        // check permission and get working locations
        var workingLocationIds = getStaffWorkingLocationIds(companyId, staffId, PermissionEnums.MERGE_CLIENT);

        var tenant = Tenant.newBuilder().setCompanyId(companyId).build();
        var customerIds = request.getCustomerIdsList();
        var primaryCustomerId = request.getPrimaryCustomerId();

        customerMergeService.mergeCustomers(tenant, customerIds, primaryCustomerId, workingLocationIds);

        responseObserver.onNext(MergeCustomersResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void checkCustomerMergeStatus(
            CheckCustomerMergeStatusParams request, StreamObserver<CheckCustomerMergeStatusResult> responseObserver) {

        var companyId = AuthContext.get().companyId();
        var tenant = Tenant.newBuilder().setCompanyId(companyId).build();
        var status = customerMergeService.checkCustomerMergeStatus(tenant, request.getCustomerId());

        var result =
                CheckCustomerMergeStatusResult.newBuilder().setStatus(status).build();
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    private static final Long ACCESS_CLIENT_SCOPE_DEFAULT = 0L;
    private static final Long ACCESS_CLIENT_SCOPE_WORKING_LOCATIONS = 1L;
    private static final Long ACCESS_CLIENT_SCOPE_ALL_LOCATIONS = 2L;
    private static final Long ACCESS_CLIENT_SCOPE_INDIVIDUAL_CLIENT_PROFILE = 3L;

    /**
     * Get staff's working locations with permission check.
     * Return empty set if staff has all location's permission.
     * Throw exception if staff has no permission.
     */
    private Set<Long> getStaffWorkingLocationIds(long companyId, long staffId, PermissionEnums permissionEnums) {
        var scope = permissionHelper.getPermissionScopeIndex(companyId, staffId, permissionEnums);
        if (scope == null) {
            throw ExceptionUtil.bizException(Code.CODE_PERMISSION_NOT_ENOUGH);
        }

        // scope = 0: only single location use this scope;
        // scope = 2: all locations.
        // both of them no need to get working locations
        if (scope.equals(ACCESS_CLIENT_SCOPE_DEFAULT)
                || scope.equals(ACCESS_CLIENT_SCOPE_ALL_LOCATIONS)
                || scope.equals(ACCESS_CLIENT_SCOPE_INDIVIDUAL_CLIENT_PROFILE)) {
            return Set.of();
        }

        // scope = 1: staff's working locations
        if (scope.equals(ACCESS_CLIENT_SCOPE_WORKING_LOCATIONS)) {
            return getStaffWorkingLocationIds(companyId, staffId);
        }

        throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "scope not supported");
    }

    private Set<Long> getStaffWorkingLocationIds(long companyId, long staffId) {
        var resp = staffServiceBlockingStub.getStaffDetail(GetStaffDetailRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(staffId)
                .build());
        return resp.getStaff().getWorkingLocationListList().stream()
                .map(LocationBriefView::getId)
                .collect(Collectors.toSet());
    }

    public boolean enableMergeClient(long companyId) {
        try {
            String keyName = "merge_client_feature_flag";

            var request = ExtractValuesRequest.newBuilder()
                    .setKeyName(keyName)
                    .putOwners(OwnerType.OWNER_TYPE_COMPANY_VALUE, companyId)
                    .build();

            return metadataServiceBlockingStub
                    .extractValues(request)
                    .getValuesMap()
                    .getOrDefault(keyName, "false")
                    .equals("true");
        } catch (Exception e) {
            return false;
        }
    }
}
