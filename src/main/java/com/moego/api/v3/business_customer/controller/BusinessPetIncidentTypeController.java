package com.moego.api.v3.business_customer.controller;

import com.moego.idl.api.business_customer.v1.BusinessPetIncidentTypeServiceGrpc;
import com.moego.idl.api.business_customer.v1.CreatePetIncidentTypeParams;
import com.moego.idl.api.business_customer.v1.CreatePetIncidentTypeResult;
import com.moego.idl.api.business_customer.v1.DeletePetIncidentTypeParams;
import com.moego.idl.api.business_customer.v1.DeletePetIncidentTypeResult;
import com.moego.idl.api.business_customer.v1.ListPetIncidentTypeParams;
import com.moego.idl.api.business_customer.v1.ListPetIncidentTypeResult;
import com.moego.idl.api.business_customer.v1.SortPetIncidentTypeParams;
import com.moego.idl.api.business_customer.v1.SortPetIncidentTypeResult;
import com.moego.idl.api.business_customer.v1.UpdatePetIncidentTypeParams;
import com.moego.idl.api.business_customer.v1.UpdatePetIncidentTypeResult;
import com.moego.idl.service.business_customer.v1.BusinessPetIncidentTypeServiceGrpc.BusinessPetIncidentTypeServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.CreatePetIncidentTypeRequest;
import com.moego.idl.service.business_customer.v1.DeletePetIncidentTypeRequest;
import com.moego.idl.service.business_customer.v1.ListPetIncidentTypeRequest;
import com.moego.idl.service.business_customer.v1.SortPetIncidentTypeRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetIncidentTypeRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessPetIncidentTypeController
        extends BusinessPetIncidentTypeServiceGrpc.BusinessPetIncidentTypeServiceImplBase {

    private final BusinessPetIncidentTypeServiceBlockingStub petIncidentTypeServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetIncidentType(
            ListPetIncidentTypeParams request, StreamObserver<ListPetIncidentTypeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var listRequest = ListPetIncidentTypeRequest.newBuilder()
                .setCompanyId(companyId)
                .setIsIncludeDeleted(request.getIsIncludeDeleted())
                .build();
        var listResponse = petIncidentTypeServiceBlockingStub.listPetIncidentType(listRequest);

        var result = ListPetIncidentTypeResult.newBuilder()
                .addAllIncidentTypes(listResponse.getIncidentTypesList())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void createPetIncidentType(
            CreatePetIncidentTypeParams request, StreamObserver<CreatePetIncidentTypeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var createRequest = CreatePetIncidentTypeRequest.newBuilder()
                .setCompanyId(companyId)
                .setIncidentType(request.getIncidentType())
                .build();
        var createResponse = petIncidentTypeServiceBlockingStub.createPetIncidentType(createRequest);

        var result = CreatePetIncidentTypeResult.newBuilder()
                .setIncidentType(createResponse.getIncidentType())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void updatePetIncidentType(
            UpdatePetIncidentTypeParams request, StreamObserver<UpdatePetIncidentTypeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var updateRequest = UpdatePetIncidentTypeRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .setIncidentType(request.getIncidentType())
                .build();
        petIncidentTypeServiceBlockingStub.updatePetIncidentType(updateRequest);

        responseObserver.onNext(UpdatePetIncidentTypeResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void sortPetIncidentType(
            SortPetIncidentTypeParams request, StreamObserver<SortPetIncidentTypeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var sortRequest = SortPetIncidentTypeRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllIds(request.getIdsList())
                .build();
        petIncidentTypeServiceBlockingStub.sortPetIncidentType(sortRequest);

        responseObserver.onNext(SortPetIncidentTypeResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void deletePetIncidentType(
            DeletePetIncidentTypeParams request, StreamObserver<DeletePetIncidentTypeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var deleteRequest = DeletePetIncidentTypeRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .build();
        petIncidentTypeServiceBlockingStub.deletePetIncidentType(deleteRequest);

        responseObserver.onNext(DeletePetIncidentTypeResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
