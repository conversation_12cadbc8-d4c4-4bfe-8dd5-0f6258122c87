package com.moego.api.v3.business_customer.service;

import com.moego.idl.models.business_customer.v1.BusinessPetIncidentReportModel;
import com.moego.idl.service.business_customer.v1.BatchGetPetIncidentReportRequest;
import com.moego.idl.service.business_customer.v1.BusinessPetIncidentReportServiceGrpc;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Service
@RequiredArgsConstructor
public class BusinessPetIncidentService {

    private final BusinessPetIncidentReportServiceGrpc.BusinessPetIncidentReportServiceBlockingStub petIncidentStub;

    public Map<Long, List<BusinessPetIncidentReportModel>> listPetIncidents(Long companyId, List<Long> petIds) {
        if (companyId == null || ObjectUtils.isEmpty(petIds)) {
            return Map.of();
        }
        var response = petIncidentStub.batchGetPetIncidentReport(BatchGetPetIncidentReportRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllPetIds(petIds)
                .build());
        return response.getIncidentReportsMapMap().entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().getIncidentReportsList()));
    }
}
