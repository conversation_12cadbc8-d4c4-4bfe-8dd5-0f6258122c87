package com.moego.api.v3.business_customer.controller;

import com.moego.idl.api.business_customer.v1.BatchUpsertPetSizeParams;
import com.moego.idl.api.business_customer.v1.BatchUpsertPetSizeResult;
import com.moego.idl.api.business_customer.v1.BusinessPetSizeServiceGrpc;
import com.moego.idl.api.business_customer.v1.ListPetSizeParams;
import com.moego.idl.api.business_customer.v1.ListPetSizeResult;
import com.moego.idl.service.business_customer.v1.BatchUpsertPetSizeRequest;
import com.moego.idl.service.business_customer.v1.BusinessPetSizeServiceGrpc.BusinessPetSizeServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.ListPetSizeRequest;
import com.moego.idl.service.offering.v1.RemoveServiceFilterRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessPetSizeController extends BusinessPetSizeServiceGrpc.BusinessPetSizeServiceImplBase {

    private final BusinessPetSizeServiceBlockingStub petSizeServiceBlockingStub;

    private final ServiceManagementServiceBlockingStub serviceManagementServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetSize(ListPetSizeParams request, StreamObserver<ListPetSizeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var listRequest =
                ListPetSizeRequest.newBuilder().setCompanyId(companyId).build();
        var listResponse = petSizeServiceBlockingStub.listPetSize(listRequest);

        var result = ListPetSizeResult.newBuilder()
                .addAllSizes(listResponse.getSizesList())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.MANAGE_PET_WEIGHT_RANGE})
    public void batchUpsertPetSize(
            BatchUpsertPetSizeParams request, StreamObserver<BatchUpsertPetSizeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var upsertRequest = BatchUpsertPetSizeRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllSizesToCreate(request.getSizesToCreateList())
                .putAllSizesToUpdate(request.getSizesToUpdateMap())
                .build();
        var upsertResponse = petSizeServiceBlockingStub.batchUpsertPetSize(upsertRequest);
        if (!upsertResponse.getDeletedSizeIdsList().isEmpty()) {
            for (var petSizeId : upsertResponse.getDeletedSizeIdsList()) {
                var deleteRequest = RemoveServiceFilterRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setPetSizeId(petSizeId)
                        .build();
                serviceManagementServiceBlockingStub.removeServiceFilter(deleteRequest);
            }
        }

        responseObserver.onNext(BatchUpsertPetSizeResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
