package com.moego.api.v3.business_customer.service;

import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetCustomerInfoRequest;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024/9/27
 */
@Service
@RequiredArgsConstructor
public class BusinessCustomerService {

    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub customerStub;

    public BusinessCustomerInfoModel getBusinessCustomerInfo(Long customerId) {
        if (customerId == null || customerId == 0) {
            return BusinessCustomerInfoModel.getDefaultInstance();
        }

        return customerStub
                .getCustomerInfo(
                        GetCustomerInfoRequest.newBuilder().setId(customerId).build())
                .getCustomer();
    }

    public List<BusinessCustomerInfoModel> listBusinessCustomerInfos(List<Long> customerIds) {
        if (ObjectUtils.isEmpty(customerIds)) {
            return List.of();
        }
        return customerStub
                .batchGetCustomerInfo(BatchGetCustomerInfoRequest.newBuilder()
                        .addAllIds(customerIds)
                        .build())
                .getCustomersList();
    }
}
