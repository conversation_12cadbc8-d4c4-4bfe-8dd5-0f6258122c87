package com.moego.api.v3.business_customer.comparator;

import com.moego.idl.api.business_customer.v1.PreviewCustomerMergeResult;
import java.io.Serializable;
import java.util.Comparator;

public class PetViewComparator implements Comparator<PreviewCustomerMergeResult.PetView>, Serializable {

    private static final long serialVersionUID = -8560889619077986205L;

    @Override
    public int compare(PreviewCustomerMergeResult.PetView o1, PreviewCustomerMergeResult.PetView o2) {
        var o1Duplicate = o1.getDuplicate() ? 1 : 0;
        var o2Duplicate = o2.getDuplicate() ? 1 : 0;
        // sort by duplicate, true first
        if (o1Duplicate != o2Duplicate) {
            return o2Duplicate - o1Duplicate;
        }

        // sort by pet id, smaller first
        return (int) (o1.getId() - o2.getId());
    }
}
