package com.moego.api.v3.business_customer.service;

import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.server.business.client.IBusinessServiceAreaClient;
import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.business.params.BatchGetAreasByLocationParams;
import com.moego.server.business.params.GetAreasByLocationParams;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/9/27
 */
@Service
@RequiredArgsConstructor
public class CustomerCertainAreaService {

    private final IBusinessServiceAreaClient serviceAreaClient;

    /**
     * List customer certain areas in business
     *
     * @param businessId        business id
     * @param customerAddresses customer addresses
     * @return customer_id -> certain areas
     */
    public Map<Long, List<CertainAreaDTO>> listCertainAreas(
            Long businessId, List<BusinessCustomerAddressModel> customerAddresses) {
        var params = customerAddresses.stream()
                .map(address -> new GetAreasByLocationParams(
                        address.getCustomerId(),
                        String.valueOf(address.getCoordinate().getLatitude()),
                        String.valueOf(address.getCoordinate().getLongitude()),
                        address.getZipcode()))
                .toList();

        return serviceAreaClient.getAreasByLocation(new BatchGetAreasByLocationParams(businessId, null, params));
    }
}
