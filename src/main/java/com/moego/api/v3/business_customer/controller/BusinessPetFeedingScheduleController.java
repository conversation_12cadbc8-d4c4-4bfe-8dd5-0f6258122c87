package com.moego.api.v3.business_customer.controller;

import com.moego.api.v3.business_customer.converter.PetScheduleConverter;
import com.moego.idl.api.business_customer.v1.BusinessPetFeedingScheduleServiceGrpc;
import com.moego.idl.api.business_customer.v1.CreateFeedingScheduleParams;
import com.moego.idl.api.business_customer.v1.CreateFeedingScheduleResult;
import com.moego.idl.api.business_customer.v1.DeleteFeedingScheduleParams;
import com.moego.idl.api.business_customer.v1.DeleteFeedingScheduleResult;
import com.moego.idl.api.business_customer.v1.ListPetFeedingScheduleParams;
import com.moego.idl.api.business_customer.v1.ListPetFeedingScheduleResult;
import com.moego.idl.api.business_customer.v1.UpdateFeedingScheduleParams;
import com.moego.idl.api.business_customer.v1.UpdateFeedingScheduleResult;
import com.moego.idl.models.business_customer.v1.BusinessPetFeedingScheduleView;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleSettingModel;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleTimeDef;
import com.moego.idl.service.business_customer.v1.BusinessPetFeedingScheduleServiceGrpc.BusinessPetFeedingScheduleServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.CreateFeedingScheduleRequest;
import com.moego.idl.service.business_customer.v1.CreateFeedingScheduleResponse;
import com.moego.idl.service.business_customer.v1.DeleteFeedingScheduleRequest;
import com.moego.idl.service.business_customer.v1.ListPetFeedingScheduleRequest;
import com.moego.idl.service.business_customer.v1.ListPetFeedingScheduleResponse;
import com.moego.idl.service.business_customer.v1.UpdateFeedingScheduleRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/1/18
 */
@GrpcService
@RequiredArgsConstructor
public class BusinessPetFeedingScheduleController
        extends BusinessPetFeedingScheduleServiceGrpc.BusinessPetFeedingScheduleServiceImplBase {

    private final BusinessPetFeedingScheduleServiceBlockingStub blockingStub;
    private final PetScheduleConverter petScheduleConverter;

    @Override
    @Auth(AuthType.COMPANY)
    public void createFeedingSchedule(
            CreateFeedingScheduleParams request, StreamObserver<CreateFeedingScheduleResult> responseObserver) {
        CreateFeedingScheduleRequest createFeedingScheduleRequest = CreateFeedingScheduleRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setFeedingSchedule(request.getFeedingSchedule())
                .build();
        CreateFeedingScheduleResponse response = blockingStub.createFeedingSchedule(createFeedingScheduleRequest);

        responseObserver.onNext(
                CreateFeedingScheduleResult.newBuilder().setId(response.getId()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateFeedingSchedule(
            UpdateFeedingScheduleParams request, StreamObserver<UpdateFeedingScheduleResult> responseObserver) {
        UpdateFeedingScheduleRequest updateFeedingScheduleRequest = UpdateFeedingScheduleRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setId(request.getId())
                .setFeedingSchedule(request.getFeedingSchedule())
                .build();
        blockingStub.updateFeedingSchedule(updateFeedingScheduleRequest);

        responseObserver.onNext(UpdateFeedingScheduleResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deleteFeedingSchedule(
            DeleteFeedingScheduleParams request, StreamObserver<DeleteFeedingScheduleResult> responseObserver) {
        DeleteFeedingScheduleRequest deleteFeedingScheduleRequest = DeleteFeedingScheduleRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setId(request.getId())
                .build();
        blockingStub.deleteFeedingSchedule(deleteFeedingScheduleRequest);

        responseObserver.onNext(DeleteFeedingScheduleResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetFeedingSchedule(
            ListPetFeedingScheduleParams request, StreamObserver<ListPetFeedingScheduleResult> responseObserver) {
        ListPetFeedingScheduleRequest listPetFeedingScheduleRequest = ListPetFeedingScheduleRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setPetId(request.getPetId())
                .build();
        ListPetFeedingScheduleResponse response = blockingStub.listPetFeedingSchedule(listPetFeedingScheduleRequest);
        Map<Long, List<BusinessPetScheduleSettingModel>> feedingSchedulesMap = response.getSchedulesList().stream()
                .collect(Collectors.groupingBy(BusinessPetScheduleSettingModel::getScheduleId));
        List<BusinessPetFeedingScheduleView> views = response.getFeedingsList().stream()
                .map(feeding -> petScheduleConverter.modelToView(feeding).toBuilder()
                        .addAllFeedingTimes(feedingSchedulesMap.getOrDefault(feeding.getId(), List.of()).stream()
                                .map(time -> BusinessPetScheduleTimeDef.newBuilder()
                                        .setScheduleTime(time.getScheduleTime())
                                        .putAllExtraJson(time.getScheduleExtraJsonMap())
                                        .build())
                                .toList())
                        .build())
                .toList();

        responseObserver.onNext(ListPetFeedingScheduleResult.newBuilder()
                .addAllFeedingSchedules(views.stream()
                        .sorted((o1, o2) -> (int) (o1.getId() - o2.getId()))
                        .toList())
                .build());
        responseObserver.onCompleted();
    }
}
