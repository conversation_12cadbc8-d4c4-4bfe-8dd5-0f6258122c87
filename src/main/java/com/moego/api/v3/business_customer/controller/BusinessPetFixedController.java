package com.moego.api.v3.business_customer.controller;

import com.moego.idl.api.business_customer.v1.BusinessPetFixedServiceGrpc;
import com.moego.idl.api.business_customer.v1.CreatePetFixedParams;
import com.moego.idl.api.business_customer.v1.CreatePetFixedResult;
import com.moego.idl.api.business_customer.v1.DeletePetFixedParams;
import com.moego.idl.api.business_customer.v1.DeletePetFixedResult;
import com.moego.idl.api.business_customer.v1.ListPetFixedParams;
import com.moego.idl.api.business_customer.v1.ListPetFixedResult;
import com.moego.idl.api.business_customer.v1.ListPetFixedTemplateParams;
import com.moego.idl.api.business_customer.v1.ListPetFixedTemplateResult;
import com.moego.idl.api.business_customer.v1.SortPetFixedParams;
import com.moego.idl.api.business_customer.v1.SortPetFixedResult;
import com.moego.idl.api.business_customer.v1.UpdatePetFixedParams;
import com.moego.idl.api.business_customer.v1.UpdatePetFixedResult;
import com.moego.idl.models.business_customer.v1.BusinessPetFixedNameView;
import com.moego.idl.service.business_customer.v1.BusinessPetFixedServiceGrpc.BusinessPetFixedServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.CreatePetFixedRequest;
import com.moego.idl.service.business_customer.v1.DeletePetFixedRequest;
import com.moego.idl.service.business_customer.v1.ListPetFixedRequest;
import com.moego.idl.service.business_customer.v1.SortPetFixedRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetFixedRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessPetFixedController extends BusinessPetFixedServiceGrpc.BusinessPetFixedServiceImplBase {

    private final BusinessPetFixedServiceBlockingStub petFixedServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetFixedTemplate(
            ListPetFixedTemplateParams request, StreamObserver<ListPetFixedTemplateResult> responseObserver) {
        // FIXME: store and read from database
        var result = ListPetFixedTemplateResult.newBuilder()
                .addFixeds(
                        BusinessPetFixedNameView.newBuilder().setName("Spayed").build())
                .addFixeds(BusinessPetFixedNameView.newBuilder()
                        .setName("Neutered")
                        .build())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetFixed(ListPetFixedParams request, StreamObserver<ListPetFixedResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var listRequest =
                ListPetFixedRequest.newBuilder().setCompanyId(companyId).build();
        var listResponse = petFixedServiceBlockingStub.listPetFixed(listRequest);

        var result = ListPetFixedResult.newBuilder()
                .addAllFixeds(listResponse.getFixedsList())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void createPetFixed(CreatePetFixedParams request, StreamObserver<CreatePetFixedResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var createRequest = CreatePetFixedRequest.newBuilder()
                .setCompanyId(companyId)
                .setFixed(request.getFixed())
                .build();
        var createResponse = petFixedServiceBlockingStub.createPetFixed(createRequest);

        var result = CreatePetFixedResult.newBuilder()
                .setFixed(createResponse.getFixed())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void updatePetFixed(UpdatePetFixedParams request, StreamObserver<UpdatePetFixedResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var updateRequest = UpdatePetFixedRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .setFixed(request.getFixed())
                .build();
        petFixedServiceBlockingStub.updatePetFixed(updateRequest);

        responseObserver.onNext(UpdatePetFixedResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void sortPetFixed(SortPetFixedParams request, StreamObserver<SortPetFixedResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var sortRequest = SortPetFixedRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllIds(request.getIdsList())
                .build();
        petFixedServiceBlockingStub.sortPetFixed(sortRequest);

        responseObserver.onNext(SortPetFixedResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void deletePetFixed(DeletePetFixedParams request, StreamObserver<DeletePetFixedResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var deleteRequest = DeletePetFixedRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .build();
        petFixedServiceBlockingStub.deletePetFixed(deleteRequest);

        responseObserver.onNext(DeletePetFixedResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
