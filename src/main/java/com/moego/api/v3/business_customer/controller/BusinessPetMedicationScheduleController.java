package com.moego.api.v3.business_customer.controller;

import com.moego.api.v3.business_customer.converter.PetScheduleConverter;
import com.moego.idl.api.business_customer.v1.BusinessPetMedicationScheduleServiceGrpc;
import com.moego.idl.api.business_customer.v1.CreateMedicationScheduleParams;
import com.moego.idl.api.business_customer.v1.CreateMedicationScheduleResult;
import com.moego.idl.api.business_customer.v1.DeleteMedicationScheduleParams;
import com.moego.idl.api.business_customer.v1.DeleteMedicationScheduleResult;
import com.moego.idl.api.business_customer.v1.ListPetMedicationScheduleParams;
import com.moego.idl.api.business_customer.v1.ListPetMedicationScheduleResult;
import com.moego.idl.api.business_customer.v1.UpdateMedicationScheduleParams;
import com.moego.idl.api.business_customer.v1.UpdateMedicationScheduleResult;
import com.moego.idl.models.business_customer.v1.BusinessPetMedicationScheduleView;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleSettingModel;
import com.moego.idl.models.business_customer.v1.BusinessPetScheduleTimeDef;
import com.moego.idl.service.business_customer.v1.BusinessPetMedicationScheduleServiceGrpc.BusinessPetMedicationScheduleServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.CreateMedicationScheduleRequest;
import com.moego.idl.service.business_customer.v1.CreateMedicationScheduleResponse;
import com.moego.idl.service.business_customer.v1.DeleteMedicationScheduleRequest;
import com.moego.idl.service.business_customer.v1.ListPetMedicationScheduleRequest;
import com.moego.idl.service.business_customer.v1.ListPetMedicationScheduleResponse;
import com.moego.idl.service.business_customer.v1.UpdateMedicationScheduleRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/1/18
 */
@GrpcService
@RequiredArgsConstructor
public class BusinessPetMedicationScheduleController
        extends BusinessPetMedicationScheduleServiceGrpc.BusinessPetMedicationScheduleServiceImplBase {

    private final BusinessPetMedicationScheduleServiceBlockingStub blockingStub;
    private final PetScheduleConverter petScheduleConverter;

    @Override
    @Auth(AuthType.COMPANY)
    public void createMedicationSchedule(
            CreateMedicationScheduleParams request, StreamObserver<CreateMedicationScheduleResult> responseObserver) {
        CreateMedicationScheduleRequest createMedicationScheduleRequest = CreateMedicationScheduleRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setMedicationSchedule(request.getMedicationSchedule())
                .build();
        CreateMedicationScheduleResponse response =
                blockingStub.createMedicationSchedule(createMedicationScheduleRequest);

        responseObserver.onNext(CreateMedicationScheduleResult.newBuilder()
                .setId(response.getId())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateMedicationSchedule(
            UpdateMedicationScheduleParams request, StreamObserver<UpdateMedicationScheduleResult> responseObserver) {
        UpdateMedicationScheduleRequest updateMedicationScheduleRequest = UpdateMedicationScheduleRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setId(request.getId())
                .setMedicationSchedule(request.getMedicationSchedule())
                .build();
        blockingStub.updateMedicationSchedule(updateMedicationScheduleRequest);

        responseObserver.onNext(UpdateMedicationScheduleResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deleteMedicationSchedule(
            DeleteMedicationScheduleParams request, StreamObserver<DeleteMedicationScheduleResult> responseObserver) {
        DeleteMedicationScheduleRequest deleteMedicationScheduleRequest = DeleteMedicationScheduleRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setId(request.getId())
                .build();
        blockingStub.deleteMedicationSchedule(deleteMedicationScheduleRequest);

        responseObserver.onNext(DeleteMedicationScheduleResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetMedicationSchedule(
            ListPetMedicationScheduleParams request, StreamObserver<ListPetMedicationScheduleResult> responseObserver) {
        ListPetMedicationScheduleRequest listPetMedicationScheduleRequest =
                ListPetMedicationScheduleRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setPetId(request.getPetId())
                        .build();
        ListPetMedicationScheduleResponse response =
                blockingStub.listPetMedicationSchedule(listPetMedicationScheduleRequest);
        Map<Long, List<BusinessPetScheduleSettingModel>> medicationSchedulesMap = response.getSchedulesList().stream()
                .collect(Collectors.groupingBy(BusinessPetScheduleSettingModel::getScheduleId));
        List<BusinessPetMedicationScheduleView> views = response.getMedicationsList().stream()
                .map(medication -> petScheduleConverter.modelToView(medication).toBuilder()
                        .addAllMedicationTimes(
                                medicationSchedulesMap.getOrDefault(medication.getId(), List.of()).stream()
                                        .map(time -> BusinessPetScheduleTimeDef.newBuilder()
                                                .setScheduleTime(time.getScheduleTime())
                                                .putAllExtraJson(time.getScheduleExtraJsonMap())
                                                .build())
                                        .toList())
                        .build())
                .toList();

        responseObserver.onNext(ListPetMedicationScheduleResult.newBuilder()
                .addAllMedicationSchedules(views.stream()
                        .sorted((o1, o2) -> (int) (o1.getId() - o2.getId()))
                        .toList())
                .build());
        responseObserver.onCompleted();
    }
}
