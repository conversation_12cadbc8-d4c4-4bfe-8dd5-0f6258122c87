package com.moego.api.v3.business_customer.controller;

import com.moego.idl.api.business_customer.v1.BusinessPetNoteServiceGrpc;
import com.moego.idl.api.business_customer.v1.PinPetNoteParams;
import com.moego.idl.api.business_customer.v1.PinPetNoteResult;
import com.moego.idl.service.business_customer.v1.BusinessPetNoteServiceGrpc.BusinessPetNoteServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.PinPetNoteRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

/**
 * Controller for pet note operations
 */
@GrpcService
@RequiredArgsConstructor
public class BusinessPetNoteController extends BusinessPetNoteServiceGrpc.BusinessPetNoteServiceImplBase {

    private final BusinessPetNoteServiceBlockingStub petNoteServiceBlockingStub;

    /**
     * Pin or unpin a pet note
     *
     * @param request the request containing the pet note ID and pin status
     * @param responseObserver the response observer
     */
    @Override
    @Auth(AuthType.COMPANY)
    public void pinPetNote(PinPetNoteParams request, StreamObserver<PinPetNoteResult> responseObserver) {
        Long staffId = AuthContext.get().staffId();
        Long companyId = AuthContext.get().companyId();

        var pinPetNoteRequest = PinPetNoteRequest.newBuilder()
                .setId(request.getId())
                .setIsPinned(request.getIsPinned())
                .setUpdatedBy(staffId)
                .setCompanyId(companyId)
                .build();

        var response = petNoteServiceBlockingStub.pinPetNote(pinPetNoteRequest);

        var result = PinPetNoteResult.newBuilder().setNote(response.getNote()).build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }
}
