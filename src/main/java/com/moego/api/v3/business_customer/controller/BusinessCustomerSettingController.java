package com.moego.api.v3.business_customer.controller;

import com.moego.idl.api.business_customer.v1.BusinessCustomerSettingServiceGrpc;
import com.moego.idl.api.business_customer.v1.GetCustomerSettingParams;
import com.moego.idl.api.business_customer.v1.GetCustomerSettingResult;
import com.moego.idl.api.business_customer.v1.UpdateCustomerCreationSettingParams;
import com.moego.idl.api.business_customer.v1.UpdateCustomerCreationSettingResult;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.BusinessCustomerSettingServiceGrpc.BusinessCustomerSettingServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.GetCustomerSettingRequest;
import com.moego.idl.service.business_customer.v1.UpdateCustomerCreationSettingRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessCustomerSettingController
        extends BusinessCustomerSettingServiceGrpc.BusinessCustomerSettingServiceImplBase {

    private final BusinessCustomerSettingServiceBlockingStub businessCustomerSettingServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void getCustomerSetting(
            GetCustomerSettingParams request, StreamObserver<GetCustomerSettingResult> responseObserver) {

        var companyId = AuthContext.get().companyId();
        var tenant = Tenant.newBuilder().setCompanyId(companyId).build();
        var getRequest =
                GetCustomerSettingRequest.newBuilder().setTenant(tenant).build();
        var setting = businessCustomerSettingServiceBlockingStub
                .getCustomerSetting(getRequest)
                .getSetting();

        var result = GetCustomerSettingResult.newBuilder().setSetting(setting).build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateCustomerCreationSetting(
            UpdateCustomerCreationSettingParams request,
            StreamObserver<UpdateCustomerCreationSettingResult> responseObserver) {

        var companyId = AuthContext.get().companyId();
        var tenant = Tenant.newBuilder().setCompanyId(companyId).build();

        var creationSetting = UpdateCustomerCreationSettingRequest.newBuilder()
                .setTenant(tenant)
                .setCreationSetting(request.getCreationSetting())
                .build();
        businessCustomerSettingServiceBlockingStub.updateCustomerCreationSetting(creationSetting);

        responseObserver.onNext(UpdateCustomerCreationSettingResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
