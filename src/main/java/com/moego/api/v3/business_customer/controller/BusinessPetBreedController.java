package com.moego.api.v3.business_customer.controller;

import com.moego.idl.api.business_customer.v1.BatchUpsertPetBreedParams;
import com.moego.idl.api.business_customer.v1.BatchUpsertPetBreedResult;
import com.moego.idl.api.business_customer.v1.BusinessPetBreedServiceGrpc;
import com.moego.idl.api.business_customer.v1.CreatePetBreedParams;
import com.moego.idl.api.business_customer.v1.CreatePetBreedResult;
import com.moego.idl.api.business_customer.v1.DeletePetBreedParams;
import com.moego.idl.api.business_customer.v1.DeletePetBreedResult;
import com.moego.idl.api.business_customer.v1.ListPetBreedParams;
import com.moego.idl.api.business_customer.v1.ListPetBreedResult;
import com.moego.idl.api.business_customer.v1.UpdatePetBreedParams;
import com.moego.idl.api.business_customer.v1.UpdatePetBreedResult;
import com.moego.idl.service.business_customer.v1.BatchUpsertPetBreedRequest;
import com.moego.idl.service.business_customer.v1.BusinessPetBreedServiceGrpc.BusinessPetBreedServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.CreatePetBreedRequest;
import com.moego.idl.service.business_customer.v1.DeletePetBreedRequest;
import com.moego.idl.service.business_customer.v1.GetPetBreedRequest;
import com.moego.idl.service.business_customer.v1.ListPetBreedRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetBreedRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import com.moego.server.grooming.api.IGroomingServiceService;
import io.grpc.stub.StreamObserver;
import java.util.Map.Entry;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessPetBreedController extends BusinessPetBreedServiceGrpc.BusinessPetBreedServiceImplBase {

    private final BusinessPetBreedServiceBlockingStub petBreedServiceBlockingStub;

    private final IGroomingServiceService groomingServiceService;

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetBreed(ListPetBreedParams request, StreamObserver<ListPetBreedResult> responseObserver) {

        var companyId = AuthContext.get().companyId();
        var petTypeId = request.getPetTypeId();

        var listRequest = ListPetBreedRequest.newBuilder()
                .setCompanyId(companyId)
                .setPetTypeId(petTypeId)
                .build();
        var listResponse = petBreedServiceBlockingStub.listPetBreed(listRequest);

        var result = ListPetBreedResult.newBuilder()
                .addAllBreeds(listResponse.getBreedsList())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.MANAGE_PET_TYPE_AND_BREED})
    public void batchUpsertPetBreed(
            BatchUpsertPetBreedParams request, StreamObserver<BatchUpsertPetBreedResult> responseObserver) {

        var companyId = AuthContext.get().companyId();
        var businessId = AuthContext.get().businessId();

        var trimmedCreateList = request.getBreedsToCreateList().stream()
                .map(b -> b.toBuilder().setName(b.getName().trim()).build())
                .toList();
        var trimmedUpdateMap = request.getBreedsToUpdateMap().entrySet().stream()
                .collect(Collectors.toMap(Entry::getKey, entry -> entry.getValue().toBuilder()
                        .setName(entry.getValue().getName().trim())
                        .build()));

        var upsertRequest = BatchUpsertPetBreedRequest.newBuilder()
                .setCompanyId(companyId)
                .setPetTypeId(request.getPetTypeId())
                .addAllBreedsToCreate(trimmedCreateList)
                .putAllBreedsToUpdate(trimmedUpdateMap)
                .build();

        var upsertResponse = petBreedServiceBlockingStub.batchUpsertPetBreed(upsertRequest);
        var deletedBreedNames = upsertResponse.getDeletedBreedNamesList();

        // TODO: 虽然传 business id，但是需要支持 company 维度 update
        for (var deletedBreedName : deletedBreedNames) {
            groomingServiceService.updateBreedBinding(businessId.intValue(), deletedBreedName);
        }

        responseObserver.onNext(BatchUpsertPetBreedResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.MANAGE_PET_TYPE_AND_BREED})
    public void createPetBreed(CreatePetBreedParams request, StreamObserver<CreatePetBreedResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var createRequest = CreatePetBreedRequest.newBuilder()
                .setCompanyId(companyId)
                .setBreed(request.getBreed().toBuilder()
                        .setName(request.getBreed().getName().trim())
                        .build())
                .setPetTypeId(request.getPetTypeId())
                .build();

        var breed = petBreedServiceBlockingStub.createPetBreed(createRequest).getBreed();

        var result = CreatePetBreedResult.newBuilder().setBreed(breed).build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.MANAGE_PET_TYPE_AND_BREED})
    public void updatePetBreed(UpdatePetBreedParams request, StreamObserver<UpdatePetBreedResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var breed = request.getBreed();
        if (breed.hasName()) {
            breed = breed.toBuilder().setName(breed.getName().trim()).build();
        }

        var updateRequest = UpdatePetBreedRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .setBreed(breed)
                .build();

        petBreedServiceBlockingStub.updatePetBreed(updateRequest);

        responseObserver.onNext(UpdatePetBreedResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.MANAGE_PET_TYPE_AND_BREED})
    public void deletePetBreed(DeletePetBreedParams request, StreamObserver<DeletePetBreedResult> responseObserver) {

        var companyId = AuthContext.get().companyId();
        var businessId = AuthContext.get().businessId();

        var id = request.getId();
        var getBreedRequest = GetPetBreedRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(id)
                .build();
        var breedName = petBreedServiceBlockingStub
                .getPetBreed(getBreedRequest)
                .getBreed()
                .getName();

        var deleteRequest = DeletePetBreedRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(id)
                .build();
        petBreedServiceBlockingStub.deletePetBreed(deleteRequest);

        // TODO: 虽然传 business id，但是需要支持 company 维度 update
        groomingServiceService.updateBreedBinding(businessId.intValue(), breedName);

        responseObserver.onNext(DeletePetBreedResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
