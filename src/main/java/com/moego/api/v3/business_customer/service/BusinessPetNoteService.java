package com.moego.api.v3.business_customer.service;

import com.moego.idl.models.business_customer.v1.BusinessPetNoteModel;
import com.moego.idl.service.business_customer.v1.BatchListPetNoteRequest;
import com.moego.idl.service.business_customer.v1.BusinessPetNoteServiceGrpc;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

@Service
@RequiredArgsConstructor
public class BusinessPetNoteService {

    private final BusinessPetNoteServiceGrpc.BusinessPetNoteServiceBlockingStub petNoteStub;

    public Map<Long, List<BusinessPetNoteModel>> listPetNotes(List<Long> petIds) {
        if (ObjectUtils.isEmpty(petIds)) {
            return Map.of();
        }
        var response = petNoteStub.batchListPetNote(
                BatchListPetNoteRequest.newBuilder().addAllPetIds(petIds).build());
        return response.getPetNotesMapMap().entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().getNotesList()));
    }
}
