package com.moego.api.v3.business_customer.controller;

import com.moego.api.v3.business_customer.converter.PetEvaluationConverter;
import com.moego.api.v3.business_customer.service.BusinessPetEvaluationService;
import com.moego.api.v3.shared.util.PrintUtil;
import com.moego.idl.api.business_customer.v1.BusinessPetEvaluationServiceGrpc;
import com.moego.idl.api.business_customer.v1.EvaluationExtra;
import com.moego.idl.api.business_customer.v1.EvaluationHistoryView;
import com.moego.idl.api.business_customer.v1.ListEvaluationStatusByPetServiceParams;
import com.moego.idl.api.business_customer.v1.ListEvaluationStatusByPetServiceResult;
import com.moego.idl.api.business_customer.v1.ListPetEvaluationHistoryParams;
import com.moego.idl.api.business_customer.v1.ListPetEvaluationHistoryResult;
import com.moego.idl.api.business_customer.v1.ListPetEvaluationParams;
import com.moego.idl.api.business_customer.v1.ListPetEvaluationResult;
import com.moego.idl.api.business_customer.v1.Operator;
import com.moego.idl.api.business_customer.v1.UpdatePetEvaluationParams;
import com.moego.idl.api.business_customer.v1.UpdatePetEvaluationResult;
import com.moego.idl.models.business_customer.v1.PetEvaluationHistoryModel;
import com.moego.idl.models.business_customer.v1.PetEvaluationModel;
import com.moego.idl.models.offering.v1.EvaluationView;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.CreatePetEvaluationHistoryRequest;
import com.moego.idl.service.business_customer.v1.ListPetEvaluationHistoryRequest;
import com.moego.idl.service.business_customer.v1.ListPetEvaluationRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetEvaluationRequest;
import com.moego.idl.service.offering.v1.ListEvaluationRequest;
import com.moego.idl.service.organization.v1.QueryStaffByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.AllArgsConstructor;
import org.springframework.util.CollectionUtils;

@GrpcService
@AllArgsConstructor
public class BusinessPetEvaluationController
        extends BusinessPetEvaluationServiceGrpc.BusinessPetEvaluationServiceImplBase {

    private static final String OPERATOR_NAME_SYSTEM = "system";

    private final com.moego.idl.service.business_customer.v1.BusinessPetEvaluationServiceGrpc
                    .BusinessPetEvaluationServiceBlockingStub
            petEvaluationClient;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffService;
    private final PetEvaluationConverter petEvaluationConverter;
    private final BusinessPetEvaluationService businessPetEvaluationService;

    private final com.moego.idl.service.offering.v1.EvaluationServiceGrpc.EvaluationServiceBlockingStub
            evaluationServiceClient;

    @Override
    @Auth(AuthType.COMPANY)
    public void updatePetEvaluation(
            UpdatePetEvaluationParams request, StreamObserver<UpdatePetEvaluationResult> responseObserver) {
        var companyId = AuthContext.get().companyId();

        petEvaluationClient.createPetEvaluationHistory(CreatePetEvaluationHistoryRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                .setPetId(request.getPetId())
                .setEvaluationStatus(request.getEvaluationStatus())
                .setActionType(request.getActionType())
                .setOperatorStaffId(AuthContext.get().staffId())
                .setEvaluationId(request.getEvaluationId())
                .build());

        petEvaluationClient.updatePetEvaluation(UpdatePetEvaluationRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                .addPetIds(request.getPetId())
                .setEvaluationId(request.getEvaluationId())
                .setEvaluationStatus(request.getEvaluationStatus())
                .build());
        responseObserver.onNext(UpdatePetEvaluationResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    private List<EvaluationView> queryEvaluationViewByIds(Set<Long> evaluationIds) {
        evaluationIds =
                evaluationIds.stream().filter(evaluationId -> evaluationId > 0).collect(Collectors.toSet());
        if (CollectionUtils.isEmpty(evaluationIds)) {
            return List.of();
        }
        var evaluationModels = evaluationServiceClient
                .listEvaluation(ListEvaluationRequest.newBuilder()
                        .setFilter(ListEvaluationRequest.Filter.newBuilder()
                                .addAllIds(evaluationIds)
                                .build())
                        .build())
                .getEvaluationsList();
        return petEvaluationConverter.convertModelToView(evaluationModels);
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetEvaluation(
            ListPetEvaluationParams request, StreamObserver<ListPetEvaluationResult> responseObserver) {
        var petEvaluationList = petEvaluationClient
                .listPetEvaluation(ListPetEvaluationRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(AuthContext.get().companyId())
                                .build())
                        .addPetIds(request.getPetId())
                        .build())
                .getPetEvaluationsList();
        responseObserver.onNext(ListPetEvaluationResult.newBuilder()
                .addAllPetEvaluations(petEvaluationList)
                .addAllEvaluations(queryEvaluationViewByIds(petEvaluationList.stream()
                        .map(PetEvaluationModel::getEvaluationId)
                        .collect(Collectors.toSet())))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetEvaluationHistory(
            ListPetEvaluationHistoryParams request, StreamObserver<ListPetEvaluationHistoryResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var listResponse = petEvaluationClient.listPetEvaluationHistory(ListPetEvaluationHistoryRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                .setPetId(request.getPetId())
                .build());
        responseObserver.onNext(ListPetEvaluationHistoryResult.newBuilder()
                .addAllEvaluationHistories(listToView(companyId, listResponse.getEvaluationHistoriesList()))
                .addAllEvaluations(queryEvaluationViewByIds(listResponse.getEvaluationHistoriesList().stream()
                        .map(PetEvaluationHistoryModel::getEvaluationId)
                        .collect(Collectors.toSet())))
                .build());
        responseObserver.onCompleted();
    }

    private List<EvaluationHistoryView> listToView(Long companyId, List<PetEvaluationHistoryModel> modelList) {
        if (CollectionUtils.isEmpty(modelList)) {
            return List.of();
        }

        var staffIds = modelList.stream()
                .map(PetEvaluationHistoryModel::getOperatorStaffId)
                .filter(operatorStaffId -> operatorStaffId > 0)
                .collect(Collectors.toSet());

        // 获取 staff
        Map<Long, String> staffNameMap = getStaffNameMap(staffIds);

        return modelList.stream()
                .map(historyModel -> buildEvaluationHistoryView(historyModel, staffNameMap))
                .collect(Collectors.toList());
    }

    private EvaluationHistoryView buildEvaluationHistoryView(
            PetEvaluationHistoryModel historyModel, Map<Long, String> staffNameMap) {
        var builder = EvaluationHistoryView.newBuilder()
                .setActionType(historyModel.getActionType())
                .setOperateDate(historyModel.getCreatedAt())
                .setEvaluationId(historyModel.getEvaluationId())
                .setEvaluationExtra(EvaluationExtra.newBuilder()
                        .setResetIntervalDays(historyModel.getResetIntervalDays())
                        .setOriginalStatus(historyModel.getOriginalStatus())
                        .setNewStatus(historyModel.getNewStatus())
                        .build());

        if (historyModel.getActionType() != PetEvaluationHistoryModel.ActionType.UPDATE_BY_SYSTEM) {
            builder.setOperator(Operator.newBuilder()
                    .setId(historyModel.getOperatorStaffId())
                    .setName(staffNameMap.getOrDefault(historyModel.getOperatorStaffId(), ""))
                    .build());
        } else {
            builder.setOperator(
                    Operator.newBuilder().setName(OPERATOR_NAME_SYSTEM).build());
        }
        return builder.build();
    }

    private Map<Long, String> getStaffNameMap(Set<Long> staffIds) {
        if (CollectionUtils.isEmpty(staffIds)) {
            return Map.of();
        }
        var staffList = staffService
                .queryStaffByIds(QueryStaffByIdsRequest.newBuilder()
                        .addAllStaffIds(staffIds)
                        .build())
                .getStaffsList();
        return staffList.stream()
                .collect(Collectors.toMap(
                        StaffModel::getId,
                        staff -> PrintUtil.printName(staff.getFirstName(), staff.getLastName()),
                        (a, b) -> b));
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listEvaluationStatusByPetService(
            ListEvaluationStatusByPetServiceParams request,
            StreamObserver<ListEvaluationStatusByPetServiceResult> responseObserver) {

        responseObserver.onNext(ListEvaluationStatusByPetServiceResult.newBuilder()
                .addAllEvaluationStatusResults(businessPetEvaluationService.listEvaluationStatusByPetService(
                        AuthContext.get().companyId(), request.getPetServiceIdsList()))
                .build());
        responseObserver.onCompleted();
    }
}
