package com.moego.api.v3.business_customer.service;

import com.moego.idl.api.business_customer.v1.ListPetVaccineResult;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceVaccineRequirementModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.offering.v1.ListServiceVaccineRequirementsRequest;
import com.moego.idl.service.offering.v1.ServiceVaccineRequirementServiceGrpc;
import com.moego.idl.service.offering.v1.UpdateServiceRequirementForVaccineRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.auth.AuthContext;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class OfferingService {
    private final ServiceVaccineRequirementServiceGrpc.ServiceVaccineRequirementServiceBlockingStub
            serviceVaccineClient;

    public List<ListPetVaccineResult.VaccineRequirementByService> listVaccineRequirementsByService(
            List<Long> vaccineId) {
        if (vaccineId.isEmpty()) {
            return List.of();
        }

        List<ServiceVaccineRequirementModel> vaccineRequirements = new ArrayList<>();
        int pageNum = 1;
        final int pageSize = 1000;
        while (true) {
            final var input = ListServiceVaccineRequirementsRequest.newBuilder()
                    .setTenant(Tenant.newBuilder()
                            .setCompanyId(AuthContext.get().companyId())
                            .build())
                    .setPagination(PaginationRequest.newBuilder()
                            .setPageNum(pageNum)
                            .setPageSize(pageSize)
                            .build())
                    .setFilter(ListServiceVaccineRequirementsRequest.Filters.newBuilder()
                            .addAllVaccineIds(vaccineId)
                            .build())
                    .build();
            final var output = serviceVaccineClient.listServiceVaccineRequirements(input);
            vaccineRequirements.addAll(output.getServiceVaccineRequirementsList());

            if (output.getPagination().getTotal() <= pageNum * pageSize) {
                break;
            }
            pageNum++;
        }

        // 按照 vaccine id 分组
        return vaccineRequirements.stream()
                .collect(Collectors.groupingBy(ServiceVaccineRequirementModel::getVaccineId))
                .entrySet()
                .stream()
                .map(entry -> ListPetVaccineResult.VaccineRequirementByService.newBuilder()
                        .setVaccineId(entry.getKey())
                        .addAllRequiredByServiceItemTypes(entry.getValue().stream()
                                .map(ServiceVaccineRequirementModel::getServiceItemType)
                                .collect(Collectors.toList()))
                        .build())
                .toList();
    }

    public void setVaccineServiceRequirement(Long vaccineId, List<ServiceItemType> serviceItemTypes) {
        final var input = UpdateServiceRequirementForVaccineRequest.newBuilder()
                .setTenant(Tenant.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .build())
                .setVaccineId(vaccineId)
                .addAllServiceItemTypes(serviceItemTypes)
                .build();
        serviceVaccineClient.updateServiceRequirementForVaccine(input);
    }
}
