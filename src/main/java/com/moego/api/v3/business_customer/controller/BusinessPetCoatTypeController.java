package com.moego.api.v3.business_customer.controller;

import com.moego.api.v3.business_customer.converter.PetCoatTypeConverter;
import com.moego.idl.api.business_customer.v1.BusinessPetCoatTypeServiceGrpc;
import com.moego.idl.api.business_customer.v1.CreatePetCoatTypeParams;
import com.moego.idl.api.business_customer.v1.CreatePetCoatTypeResult;
import com.moego.idl.api.business_customer.v1.DeletePetCoatTypeParams;
import com.moego.idl.api.business_customer.v1.DeletePetCoatTypeResult;
import com.moego.idl.api.business_customer.v1.ListPetCoatTypeParams;
import com.moego.idl.api.business_customer.v1.ListPetCoatTypeResult;
import com.moego.idl.api.business_customer.v1.ListPetCoatTypeTemplateParams;
import com.moego.idl.api.business_customer.v1.ListPetCoatTypeTemplateResult;
import com.moego.idl.api.business_customer.v1.SortPetCoatTypeParams;
import com.moego.idl.api.business_customer.v1.SortPetCoatTypeResult;
import com.moego.idl.api.business_customer.v1.UpdatePetCoatTypeParams;
import com.moego.idl.api.business_customer.v1.UpdatePetCoatTypeResult;
import com.moego.idl.service.business_customer.v1.BusinessPetCoatTypeServiceGrpc.BusinessPetCoatTypeServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.CreatePetCoatTypeRequest;
import com.moego.idl.service.business_customer.v1.DeletePetCoatTypeRequest;
import com.moego.idl.service.business_customer.v1.ListPetCoatTypeRequest;
import com.moego.idl.service.business_customer.v1.ListPetCoatTypeTemplateRequest;
import com.moego.idl.service.business_customer.v1.SortPetCoatTypeRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetCoatTypeRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import com.moego.server.grooming.api.IGroomingServiceService;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessPetCoatTypeController extends BusinessPetCoatTypeServiceGrpc.BusinessPetCoatTypeServiceImplBase {

    private final BusinessPetCoatTypeServiceBlockingStub petCoatTypeServiceBlockingStub;

    private final IGroomingServiceService groomingServiceService;

    private final PetCoatTypeConverter petCoatTypeConverter;

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetCoatTypeTemplate(
            ListPetCoatTypeTemplateParams request, StreamObserver<ListPetCoatTypeTemplateResult> responseObserver) {

        var coatTypes = petCoatTypeServiceBlockingStub
                .listPetCoatTypeTemplate(ListPetCoatTypeTemplateRequest.getDefaultInstance())
                .getCoatTypesList()
                .stream()
                .map(petCoatTypeConverter::toNameView)
                .toList();

        var result = ListPetCoatTypeTemplateResult.newBuilder()
                .addAllCoatTypes(coatTypes)
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetCoatType(ListPetCoatTypeParams request, StreamObserver<ListPetCoatTypeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var listRequest =
                ListPetCoatTypeRequest.newBuilder().setCompanyId(companyId).build();
        var listResponse = petCoatTypeServiceBlockingStub.listPetCoatType(listRequest);

        var result = ListPetCoatTypeResult.newBuilder()
                .addAllCoatTypes(listResponse.getCoatTypesList())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void createPetCoatType(
            CreatePetCoatTypeParams request, StreamObserver<CreatePetCoatTypeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var createRequest = CreatePetCoatTypeRequest.newBuilder()
                .setCompanyId(companyId)
                .setCoatType(request.getCoatType())
                .build();
        var createResponse = petCoatTypeServiceBlockingStub.createPetCoatType(createRequest);

        var result = CreatePetCoatTypeResult.newBuilder()
                .setCoatType(createResponse.getCoatType())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void updatePetCoatType(
            UpdatePetCoatTypeParams request, StreamObserver<UpdatePetCoatTypeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var updateRequest = UpdatePetCoatTypeRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .setCoatType(request.getCoatType())
                .build();
        petCoatTypeServiceBlockingStub.updatePetCoatType(updateRequest);

        responseObserver.onNext(UpdatePetCoatTypeResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void sortPetCoatType(SortPetCoatTypeParams request, StreamObserver<SortPetCoatTypeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var sortRequest = SortPetCoatTypeRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllIds(request.getIdsList())
                .build();
        petCoatTypeServiceBlockingStub.sortPetCoatType(sortRequest);

        responseObserver.onNext(SortPetCoatTypeResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void deletePetCoatType(
            DeletePetCoatTypeParams request, StreamObserver<DeletePetCoatTypeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();
        var businessId = AuthContext.get().businessId();
        var id = request.getId();

        var deleteRequest = DeletePetCoatTypeRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(id)
                .build();
        petCoatTypeServiceBlockingStub.deletePetCoatType(deleteRequest);

        // TODO: 虽然传 business id，但是需要支持 company 维度 update
        groomingServiceService.updateCoatBinding(businessId.intValue(), (int) id);

        responseObserver.onNext(DeletePetCoatTypeResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
