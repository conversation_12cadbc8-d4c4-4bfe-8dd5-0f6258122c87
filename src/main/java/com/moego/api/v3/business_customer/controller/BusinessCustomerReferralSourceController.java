package com.moego.api.v3.business_customer.controller;

import com.moego.api.v3.business_customer.converter.CustomerReferralSourceConverter;
import com.moego.idl.api.business_customer.v1.BusinessCustomerReferralSourceServiceGrpc;
import com.moego.idl.api.business_customer.v1.CreateCustomerReferralSourceParams;
import com.moego.idl.api.business_customer.v1.CreateCustomerReferralSourceResult;
import com.moego.idl.api.business_customer.v1.DeleteCustomerReferralSourceParams;
import com.moego.idl.api.business_customer.v1.DeleteCustomerReferralSourceResult;
import com.moego.idl.api.business_customer.v1.ListCustomerReferralSourceParams;
import com.moego.idl.api.business_customer.v1.ListCustomerReferralSourceResult;
import com.moego.idl.api.business_customer.v1.ListCustomerReferralSourceTemplateParams;
import com.moego.idl.api.business_customer.v1.ListCustomerReferralSourceTemplateResult;
import com.moego.idl.api.business_customer.v1.SortCustomerReferralSourceParams;
import com.moego.idl.api.business_customer.v1.SortCustomerReferralSourceResult;
import com.moego.idl.api.business_customer.v1.UpdateCustomerReferralSourceParams;
import com.moego.idl.api.business_customer.v1.UpdateCustomerReferralSourceResult;
import com.moego.idl.service.business_customer.v1.BusinessCustomerReferralSourceServiceGrpc.BusinessCustomerReferralSourceServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.CreateCustomerReferralSourceRequest;
import com.moego.idl.service.business_customer.v1.DeleteCustomerReferralSourceRequest;
import com.moego.idl.service.business_customer.v1.ListCustomerReferralSourceRequest;
import com.moego.idl.service.business_customer.v1.ListCustomerReferralSourceTemplateRequest;
import com.moego.idl.service.business_customer.v1.SortCustomerReferralSourceRequest;
import com.moego.idl.service.business_customer.v1.UpdateCustomerReferralSourceRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessCustomerReferralSourceController
        extends BusinessCustomerReferralSourceServiceGrpc.BusinessCustomerReferralSourceServiceImplBase {

    private final BusinessCustomerReferralSourceServiceBlockingStub customerReferralSourceServiceBlockingStub;

    private final CustomerReferralSourceConverter customerReferralSourceConverter;

    @Override
    @Auth(AuthType.COMPANY)
    public void listCustomerReferralSourceTemplate(
            ListCustomerReferralSourceTemplateParams request,
            StreamObserver<ListCustomerReferralSourceTemplateResult> responseObserver) {

        var referralSources = customerReferralSourceServiceBlockingStub
                .listCustomerReferralSourceTemplate(ListCustomerReferralSourceTemplateRequest.getDefaultInstance())
                .getReferralSourcesList()
                .stream()
                .map(customerReferralSourceConverter::toNameView)
                .toList();

        var result = ListCustomerReferralSourceTemplateResult.newBuilder()
                .addAllReferralSources(referralSources)
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listCustomerReferralSource(
            ListCustomerReferralSourceParams request,
            StreamObserver<ListCustomerReferralSourceResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var listRequest = ListCustomerReferralSourceRequest.newBuilder()
                .setCompanyId(companyId)
                .build();
        var listResponse = customerReferralSourceServiceBlockingStub.listCustomerReferralSource(listRequest);

        var result = ListCustomerReferralSourceResult.newBuilder()
                .addAllReferralSources(listResponse.getReferralSourcesList())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void createCustomerReferralSource(
            CreateCustomerReferralSourceParams request,
            StreamObserver<CreateCustomerReferralSourceResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var createRequest = CreateCustomerReferralSourceRequest.newBuilder()
                .setCompanyId(companyId)
                .setReferralSource(request.getReferralSource())
                .build();
        var createResponse = customerReferralSourceServiceBlockingStub.createCustomerReferralSource(createRequest);

        var result = CreateCustomerReferralSourceResult.newBuilder()
                .setReferralSource(createResponse.getReferralSource())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void updateCustomerReferralSource(
            UpdateCustomerReferralSourceParams request,
            StreamObserver<UpdateCustomerReferralSourceResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var updateRequest = UpdateCustomerReferralSourceRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .setReferralSource(request.getReferralSource())
                .build();
        customerReferralSourceServiceBlockingStub.updateCustomerReferralSource(updateRequest);

        responseObserver.onNext(UpdateCustomerReferralSourceResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void sortCustomerReferralSource(
            SortCustomerReferralSourceParams request,
            StreamObserver<SortCustomerReferralSourceResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var sortRequest = SortCustomerReferralSourceRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllIds(request.getIdsList())
                .build();
        customerReferralSourceServiceBlockingStub.sortCustomerReferralSource(sortRequest);

        responseObserver.onNext(SortCustomerReferralSourceResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void deleteCustomerReferralSource(
            DeleteCustomerReferralSourceParams request,
            StreamObserver<DeleteCustomerReferralSourceResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var deleteRequest = DeleteCustomerReferralSourceRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .build();
        customerReferralSourceServiceBlockingStub.deleteCustomerReferralSource(deleteRequest);

        responseObserver.onNext(DeleteCustomerReferralSourceResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
