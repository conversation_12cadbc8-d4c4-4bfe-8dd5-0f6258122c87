package com.moego.api.v3.business_customer.service;

import com.moego.idl.models.business_customer.v1.BusinessPetCodeBindingModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeModel;
import com.moego.idl.service.business_customer.v1.BatchListBindingPetCodeRequest;
import com.moego.idl.service.business_customer.v1.BatchListBindingPetCodeResponse;
import com.moego.idl.service.business_customer.v1.BusinessPetCodeServiceGrpc;
import com.moego.lib.utils.model.Pair;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024/9/27
 */
@Service
@RequiredArgsConstructor
public class BusinessPetCodeService {

    private final BusinessPetCodeServiceGrpc.BusinessPetCodeServiceBlockingStub petCodeStub;

    public Pair<List<BusinessPetCodeBindingModel>, List<BusinessPetCodeModel>> listPetCodes(
            Long companyId, List<Long> petIds) {
        if (companyId == null || ObjectUtils.isEmpty(petIds)) {
            return Pair.of(List.of(), List.of());
        }
        var response = petCodeStub.batchListBindingPetCode(BatchListBindingPetCodeRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllPetIds(petIds)
                .build());
        return Pair.of(response.getBindingsList(), response.getPetCodesList());
    }

    public Pair<List<BatchListBindingPetCodeResponse.Binding>, List<BusinessPetCodeModel>> listPetCodeBindings(
            Long companyId, List<Long> petIds) {
        if (companyId == null || ObjectUtils.isEmpty(petIds)) {
            return Pair.of(List.of(), List.of());
        }
        var response = petCodeStub.batchListBindingPetCode(BatchListBindingPetCodeRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllPetIds(petIds)
                .build());
        return Pair.of(response.getPetCodeBindingsList(), response.getPetCodesList());
    }

    public Map<Long, List<BusinessPetCodeModel>> getPetCodeMap(long companyId, Collection<Long> petIdList) {
        if (CollectionUtils.isEmpty(petIdList)) {
            return Map.of();
        }
        BatchListBindingPetCodeResponse batchListBindingPetCodeResponse =
                petCodeStub.batchListBindingPetCode(BatchListBindingPetCodeRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllPetIds(petIdList)
                        .build());
        Map<Long, BusinessPetCodeBindingModel> petCodeBindingModelMap =
                batchListBindingPetCodeResponse.getBindingsList().stream()
                        .collect(Collectors.toMap(
                                BusinessPetCodeBindingModel::getPetId, Function.identity(), (k1, k2) -> k1));
        Map<Long, BusinessPetCodeModel> petCodeModelMap = batchListBindingPetCodeResponse.getPetCodesList().stream()
                .collect(Collectors.toMap(BusinessPetCodeModel::getId, Function.identity()));
        return petIdList.stream().collect(Collectors.toMap(Function.identity(), petId -> {
            BusinessPetCodeBindingModel businessPetCodeBindingModel = petCodeBindingModelMap.get(petId);
            if (Objects.isNull(businessPetCodeBindingModel)) {
                return List.of();
            }
            return businessPetCodeBindingModel.getPetCodeIdsList().stream()
                    .map(petCodeModelMap::get)
                    .toList();
        }));
    }

    public Map<Long, List<Long>> getPetCodeIdsMap(long companyId, Collection<Long> petIdList) {
        var petCods = getPetCodeMap(companyId, petIdList);
        return petCods.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                        .map(BusinessPetCodeModel::getId)
                        .toList()));
    }
}
