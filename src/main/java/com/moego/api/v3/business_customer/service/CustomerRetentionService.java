package com.moego.api.v3.business_customer.service;

import com.google.common.collect.ImmutableMap;
import com.google.protobuf.Timestamp;
import com.google.type.Interval;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.PropertyEnum;
import com.moego.common.params.PageQuery;
import com.moego.common.params.SortParams;
import com.moego.idl.api.business_customer.v1.FetchRetentionDataParams;
import com.moego.idl.api.business_customer.v1.FetchRetentionDataResult;
import com.moego.idl.models.business_customer.v1.CustomerRetentionData;
import com.moego.idl.models.business_customer.v1.CustomerRetentionData.TableColumn;
import com.moego.idl.models.business_customer.v1.RetentionFilter;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.models.reporting.v2.Field;
import com.moego.idl.models.reporting.v2.FilterRequest;
import com.moego.idl.models.reporting.v2.NumberData;
import com.moego.idl.models.reporting.v2.Operator;
import com.moego.idl.models.reporting.v2.ReportingScene;
import com.moego.idl.models.reporting.v2.TableRowData;
import com.moego.idl.models.reporting.v2.TokenInfo;
import com.moego.idl.models.reporting.v2.Value;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetLocationListRequest;
import com.moego.idl.service.reporting.v2.FetchReportDataParams;
import com.moego.idl.service.reporting.v2.FetchReportDataResult;
import com.moego.idl.service.reporting.v2.ReportServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.dto.PhoneNumberEmailDto;
import com.moego.server.customer.params.ClientListParams;
import com.moego.server.customer.params.ClientListRequest;
import java.time.Duration;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class CustomerRetentionService {

    private final String CUSTOMER_RETENTION_TABLE_DIAGRAM_ID = "customer_retention_table";
    private final String CUSTOMER_ID_FIELD_KEY = "customer_id";
    private final String CARE_TYPE_FIELD_KEY = "care_type";
    private static final ImmutableMap<TableColumn, List<String>> columnToFieldKeys = new ImmutableMap.Builder<
                    TableColumn, List<String>>()
            .put(TableColumn.AVERAGE, List.of("avg_spent", "avg_visits"))
            .put(TableColumn.JANUARY, List.of("january_spent", "january_visits"))
            .put(TableColumn.FEBRUARY, List.of("february_spent", "february_visits"))
            .put(TableColumn.MARCH, List.of("march_spent", "march_visits"))
            .put(TableColumn.APRIL, List.of("april_spent", "april_visits"))
            .put(TableColumn.MAY, List.of("may_spent", "may_visits"))
            .put(TableColumn.JUNE, List.of("june_spent", "june_visits"))
            .put(TableColumn.JULY, List.of("july_spent", "july_visits"))
            .put(TableColumn.AUGUST, List.of("august_spent", "august_visits"))
            .put(TableColumn.SEPTEMBER, List.of("september_spent", "september_visits"))
            .put(TableColumn.OCTOBER, List.of("october_spent", "october_visits"))
            .put(TableColumn.NOVEMBER, List.of("november_spent", "november_visits"))
            .put(TableColumn.DECEMBER, List.of("december_spent", "december_visits"))
            .build();

    private static final Map<RetentionFilter.CareType, String> careTypeToValue = Map.of(
            RetentionFilter.CareType.GROOMING, "Grooming",
            RetentionFilter.CareType.BOARDING, "Boarding",
            RetentionFilter.CareType.DAYCARE, "Daycare",
            RetentionFilter.CareType.EVALUATION, "Evaluation",
            RetentionFilter.CareType.NON_SERVICE_SALES, "Non-service sales",
            RetentionFilter.CareType.CARE_TYPE_UNSPECIFIED, "Other");

    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessService;
    private final ICustomerCustomerService customerApi;
    private final ReportServiceGrpc.ReportServiceBlockingStub reportService;

    public FetchRetentionDataResult fetchRetentionData(FetchRetentionDataParams params) {
        long companyId = AuthContext.get().companyId();
        List<Long> businessIds = new ArrayList<>();
        var locations = businessService.getLocationList(
                GetLocationListRequest.newBuilder().setTokenCompanyId(companyId).build());
        if (!CollectionUtils.isEmpty(locations.getLocationList())) {
            businessIds.addAll(locations.getLocationList().stream()
                    .map(LocationBriefView::getId)
                    .toList());
        } else {
            businessIds.add(AuthContext.get().businessId());
        }

        var customerResult = customerApi.getSmartClientListV2(new ClientListRequest(
                companyId,
                AuthContext.get().businessId(),
                null,
                buildClientFiltersParams(params.getClientFilter(), params.getPagination())));
        log.info("Debug#fetchRetentionData, customerResult: {}", JsonUtil.toJson(customerResult));

        if (CollectionUtils.isEmpty(customerResult.getCustomerList())) {
            return FetchRetentionDataResult.getDefaultInstance();
        }
        List<FilterRequest> filters = new ArrayList<>();
        // customer_id filter
        filters.add(buildCustomerIdsFilter(customerResult.getCustomerList().stream()
                .map(PhoneNumberEmailDto::getCustomerId)
                .collect(Collectors.toSet())));
        if (!CollectionUtils.isEmpty(params.getRetentionFilter().getCareTypesList())) {
            // care_type filter
            filters.add(buildCareTypeFilter(params.getRetentionFilter().getCareTypesList()));
        }

        var tableData = reportService.fetchReportData(FetchReportDataParams.newBuilder()
                .setDiagramId(CUSTOMER_RETENTION_TABLE_DIAGRAM_ID)
                .addAllBusinessIds(businessIds)
                .setCurrentPeriod(params.getRetentionFilter().getPeriod())
                .setPreviousPeriod(
                        buildLastPeriodInterval(params.getRetentionFilter().getPeriod()))
                .addAllFilters(filters)
                .addGroupByFieldKeys(CUSTOMER_ID_FIELD_KEY)
                .setPagination(getReportPagination())
                .setTokenInfo(TokenInfo.newBuilder()
                        .setCompanyId(companyId)
                        .setStaffId(AuthContext.get().staffId())
                        .build())
                .addAllOrderBys(params.getOrderBysList())
                .setReportingType(ReportingScene.COMMON)
                .build());

        return FetchRetentionDataResult.newBuilder()
                .addAllData(buildRetentionData(tableData, customerResult.getCustomerList()))
                .setPagination(PaginationResponse.newBuilder()
                        .setTotal(customerResult.getCustomerTotal())
                        .setPageNum(params.getPagination().getPageNum())
                        .setPageSize(params.getPagination().getPageSize())
                        .build())
                .build();
    }

    /**
     * 构建前一个查询周期
     */
    private Interval buildLastPeriodInterval(Interval currentPeriod) {
        Instant start = Instant.ofEpochSecond(
                currentPeriod.getStartTime().getSeconds(),
                currentPeriod.getStartTime().getNanos());
        Instant end = Instant.ofEpochSecond(
                currentPeriod.getEndTime().getSeconds(),
                currentPeriod.getEndTime().getNanos());
        var diff = ChronoUnit.DAYS.between(start, end);
        Instant lastStart = start.minus(Duration.ofDays(diff));
        Instant lastEnd = end.minus(Duration.ofDays(diff));
        return Interval.newBuilder()
                .setStartTime(Timestamp.newBuilder()
                        .setSeconds(lastStart.getEpochSecond())
                        .setNanos(lastStart.getNano())
                        .build())
                .setEndTime(Timestamp.newBuilder()
                        .setSeconds(lastEnd.getEpochSecond())
                        .setNanos(lastEnd.getNano())
                        .build())
                .build();
    }

    private FilterRequest buildCustomerIdsFilter(Collection<Integer> customerIds) {
        return FilterRequest.newBuilder()
                .setFieldKey(CUSTOMER_ID_FIELD_KEY)
                .setOperator(Operator.IN)
                .addAllValues(customerIds.stream()
                        .map(customerId ->
                                Value.newBuilder().setInt64(customerId).build())
                        .toList())
                .build();
    }

    private FilterRequest buildCareTypeFilter(List<RetentionFilter.CareType> careTypes) {
        return FilterRequest.newBuilder()
                .setFieldKey(CARE_TYPE_FIELD_KEY)
                .setOperator(Operator.IN)
                .addAllValues(careTypes.stream()
                        .map(careType -> Value.newBuilder()
                                .setString(careTypeToValue.get(careType))
                                .build())
                        .toList())
                .build();
    }

    private PaginationRequest getReportPagination() {
        return PaginationRequest.newBuilder()
                .setPageNum(1) // 默认拉取第一页，分页逻辑已经在 smart client list 中处理
                .setPageSize(1000) // 默认拉取 1000 条
                .build();
    }

    private List<CustomerRetentionData> buildRetentionData(
            FetchReportDataResult tableData, List<PhoneNumberEmailDto> customerList) {
        Map<String, TableRowData> customerIdToData = tableData.getTableData().getRowsList().stream()
                .collect(Collectors.toMap(
                        row -> row.getDataMap()
                                .get(CUSTOMER_ID_FIELD_KEY)
                                .getValue()
                                .getString(),
                        Function.identity()));

        return customerList.stream()
                .map(customer -> {
                    var customerView = CustomerRetentionData.CustomerRetentionView.newBuilder()
                            .setId(customer.getCustomerId())
                            .setFirstName(customer.getFirstName())
                            .setLastName(customer.getLastName())
                            .setAvatarPath(customer.getAvatarPath())
                            .setIsNew(customer.getIsNewCustomer())
                            .setIsProspect(customer.getIsProspectCustomer())
                            .setPreferredFrequencyDay(customer.getPreferredFrequencyDay())
                            .setPreferredFrequencyType(customer.getPreferredFrequencyType())
                            .setHasPetParentAppAccount(customer.getHasPetParentAppAccount())
                            .setInactive(BooleanEnum.INACTIVE_TRUE.equals(customer.getInactive()))
                            .build();
                    if (!customerIdToData.containsKey(String.valueOf(customer.getCustomerId()))) {
                        return CustomerRetentionData.newBuilder()
                                .setCustomer(customerView)
                                .addAllRetentionData(buildDefaultRetentionDataList())
                                .build();
                    }
                    var row = customerIdToData.get(String.valueOf(customer.getCustomerId()));
                    List<CustomerRetentionData.RetentionData> retentionDataList = new ArrayList<>();
                    Value lastMonthTotalSpent = Value.newBuilder().setDouble(0d).build();
                    Value lastMonthTotalVisit = Value.newBuilder().setInt64(0).build();
                    for (TableColumn column : TableColumn.values()) {
                        var fieldKeys = columnToFieldKeys.get(column);
                        if (fieldKeys == null) {
                            continue;
                        }
                        var totalSpent = row.getDataMap().get(fieldKeys.get(0));
                        var totalVisits = row.getDataMap().get(fieldKeys.get(1));
                        // 如果是 1 月份，取 12 月份的 previous value, 否则取上一个月的 value，作为环比展示
                        if (column == TableColumn.JANUARY) {
                            // 取12月的 previous value
                            var decFieldKeys = columnToFieldKeys.get(TableColumn.DECEMBER);
                            assert decFieldKeys != null;
                            totalSpent = totalSpent.toBuilder()
                                    .setPreviousValue(row.getDataMap()
                                            .getOrDefault(
                                                    decFieldKeys.get(0), getDefaultTotalSpent(decFieldKeys.get(0)))
                                            .getPreviousValue())
                                    .build();
                            totalVisits = totalVisits.toBuilder()
                                    .setPreviousValue(row.getDataMap()
                                            .getOrDefault(
                                                    decFieldKeys.get(1), getDefaultTotalVisits(decFieldKeys.get(1)))
                                            .getPreviousValue())
                                    .build();
                        } else {
                            totalSpent = totalSpent.toBuilder()
                                    .setPreviousValue(lastMonthTotalSpent)
                                    .build();
                            totalVisits = totalVisits.toBuilder()
                                    .setPreviousValue(lastMonthTotalVisit)
                                    .build();
                        }

                        retentionDataList.add(CustomerRetentionData.RetentionData.newBuilder()
                                .setColumn(column)
                                .setTotalSpent(totalSpent)
                                .setTotalVisits(totalVisits)
                                .build());
                        lastMonthTotalSpent = totalSpent.getValue();
                        lastMonthTotalVisit = totalVisits.getValue();
                    }
                    return CustomerRetentionData.newBuilder()
                            .setCustomer(customerView)
                            .addAllRetentionData(retentionDataList)
                            .build();
                })
                .toList();
    }

    private ClientListParams buildClientFiltersParams(String clientFilter, PaginationRequest paginationParams) {
        if (StringUtils.hasText(clientFilter)) {
            var clientListParams = JsonUtil.toBean(clientFilter, ClientListParams.class);
            var clientListParamsBuilder = JsonUtil.toBean(clientFilter, ClientListParams.class).toBuilder();
            if (clientListParams.sort() == null) {
                clientListParamsBuilder.sort(new SortParams(PropertyEnum.first_name, PageQuery.OrderEnum.asc));
            }
            return clientListParamsBuilder
                    .pageNum(paginationParams.getPageNum())
                    .pageSize(paginationParams.getPageSize())
                    .build();
        }
        return ClientListParams.builder()
                .sort(new SortParams(PropertyEnum.first_name, PageQuery.OrderEnum.asc))
                .pageNum(paginationParams.getPageNum())
                .pageSize(paginationParams.getPageSize())
                .build();
    }

    private List<CustomerRetentionData.RetentionData> buildDefaultRetentionDataList() {
        return columnToFieldKeys.entrySet().stream()
                .map(entry -> CustomerRetentionData.RetentionData.newBuilder()
                        .setColumn(entry.getKey())
                        .setTotalSpent(getDefaultTotalSpent(entry.getValue().get(0)))
                        .setTotalVisits(getDefaultTotalVisits(entry.getValue().get(1)))
                        .build())
                .toList();
    }

    private NumberData getDefaultTotalVisits(String fieldKey) {
        return NumberData.newBuilder()
                .setFieldKey(fieldKey)
                .setFieldType(Field.Type.NUMBER)
                .setValue(Value.newBuilder().setInt64(0).build())
                .setPreviousValue(Value.newBuilder().setInt64(0).build())
                .build();
    }

    private NumberData getDefaultTotalSpent(String fieldKey) {
        return NumberData.newBuilder()
                .setFieldKey(fieldKey)
                .setFieldType(Field.Type.MONEY)
                .setValue(Value.newBuilder().setDouble(0d).build())
                .setPreviousValue(Value.newBuilder().setDouble(0d).build())
                .build();
    }
}
