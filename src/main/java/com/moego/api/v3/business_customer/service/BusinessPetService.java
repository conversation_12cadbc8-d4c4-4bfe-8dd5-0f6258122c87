package com.moego.api.v3.business_customer.service;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.business_customer.v1.BusinessPetNoteModel;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BatchGetPetRequest;
import com.moego.idl.service.business_customer.v1.BatchListPetNoteRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetNoteServiceGrpc;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024/9/27
 */
@Service
@RequiredArgsConstructor
public class BusinessPetService {

    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub customerPetStub;
    private final BusinessPetNoteServiceGrpc.BusinessPetNoteServiceBlockingStub petNoteClient;

    public List<BusinessCustomerPetModel> listPets(List<Long> petIds) {
        if (ObjectUtils.isEmpty(petIds)) {
            return List.of();
        }
        return customerPetStub
                .batchGetPet(BatchGetPetRequest.newBuilder().addAllIds(petIds).build())
                .getPetsList();
    }

    public Map<Long, List<BusinessPetNoteModel>> getPetNoteDTOs(List<Long> petIds) {
        petIds = petIds.stream().filter(k -> k != null && k > 0).distinct().toList();
        if (CollectionUtils.isEmpty(petIds)) {
            return new HashMap<>();
        }
        List<Set<Long>> sets = CommonUtil.splitListByItemNum(petIds, 100);
        Map<Long, List<BusinessPetNoteModel>> petNoteMap = new HashMap<>();
        sets.forEach(set -> {
            var petNoteResponse = petNoteClient.batchListPetNote(
                    BatchListPetNoteRequest.newBuilder().addAllPetIds(set).build());
            petNoteResponse.getPetNotesMapMap().forEach((k, v) -> petNoteMap.put(k, v.getNotesList()));
        });

        return petNoteMap;
    }

    public Map<Long, BusinessCustomerPetInfoModel> listPetsInfo(List<Long> petIds) {
        if (ObjectUtils.isEmpty(petIds)) {
            return Map.of();
        }
        return customerPetStub
                .batchGetPetInfo(
                        BatchGetPetInfoRequest.newBuilder().addAllIds(petIds).build())
                .getPetsList()
                .stream()
                .collect(Collectors.toMap(BusinessCustomerPetInfoModel::getId, Function.identity()));
    }
}
