package com.moego.api.v3.business_customer.controller;

import com.moego.api.v3.business_customer.service.CustomerRetentionService;
import com.moego.idl.api.business_customer.v1.BusinessCustomerRetentionServiceGrpc;
import com.moego.idl.api.business_customer.v1.FetchRetentionDataParams;
import com.moego.idl.api.business_customer.v1.FetchRetentionDataResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class BusinessCustomerRetentionController
        extends BusinessCustomerRetentionServiceGrpc.BusinessCustomerRetentionServiceImplBase {

    private final CustomerRetentionService customerRetentionService;

    @Override
    @Auth(AuthType.COMPANY)
    public void fetchRetentionData(
            FetchRetentionDataParams request, StreamObserver<FetchRetentionDataResult> responseObserver) {
        responseObserver.onNext(customerRetentionService.fetchRetentionData(request));
        responseObserver.onCompleted();
    }
}
