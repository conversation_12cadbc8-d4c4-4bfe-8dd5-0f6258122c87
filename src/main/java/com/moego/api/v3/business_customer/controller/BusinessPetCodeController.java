package com.moego.api.v3.business_customer.controller;

import com.moego.idl.api.business_customer.v1.BusinessPetCodeServiceGrpc;
import com.moego.idl.api.business_customer.v1.CreatePetCodeAndBindingParams;
import com.moego.idl.api.business_customer.v1.CreatePetCodeAndBindingResult;
import com.moego.idl.api.business_customer.v1.CreatePetCodeParams;
import com.moego.idl.api.business_customer.v1.CreatePetCodeResult;
import com.moego.idl.api.business_customer.v1.DeletePetCodeParams;
import com.moego.idl.api.business_customer.v1.DeletePetCodeResult;
import com.moego.idl.api.business_customer.v1.ListPetCodeParams;
import com.moego.idl.api.business_customer.v1.ListPetCodeResult;
import com.moego.idl.api.business_customer.v1.SortPetCodeParams;
import com.moego.idl.api.business_customer.v1.SortPetCodeResult;
import com.moego.idl.api.business_customer.v1.UpdateBindingParams;
import com.moego.idl.api.business_customer.v1.UpdateBindingResult;
import com.moego.idl.api.business_customer.v1.UpdatePetCodeParams;
import com.moego.idl.api.business_customer.v1.UpdatePetCodeResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.business_customer.v1.BindingPetCodeRequest;
import com.moego.idl.service.business_customer.v1.BusinessPetCodeServiceGrpc.BusinessPetCodeServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.CreatePetCodeRequest;
import com.moego.idl.service.business_customer.v1.DeletePetCodeRequest;
import com.moego.idl.service.business_customer.v1.ListPetCodeRequest;
import com.moego.idl.service.business_customer.v1.SortPetCodeRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetCodeRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import java.util.Objects;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class BusinessPetCodeController extends BusinessPetCodeServiceGrpc.BusinessPetCodeServiceImplBase {

    private final BusinessPetCodeServiceBlockingStub petCodeServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void createPetCodeAndBinding(
            CreatePetCodeAndBindingParams request, StreamObserver<CreatePetCodeAndBindingResult> responseObserver) {
        // 1. create a code
        // 2. binding the code to the pet
        var companyId = AuthContext.get().companyId();
        var createRequest = CreatePetCodeRequest.newBuilder()
                .setCompanyId(companyId)
                .setPetCode(request.getPetCode())
                .build();

        var createResponse = petCodeServiceBlockingStub.createPetCode(createRequest);
        var code = createResponse.getPetCode();

        var petId = request.getPetId();
        // binding
        var resp = petCodeServiceBlockingStub.bindingPetCode(BindingPetCodeRequest.newBuilder()
                .setPetCodeId(code.getId())
                .setPetId(petId)
                .setComment(request.getComment())
                .setStatus(BindingPetCodeRequest.Status.BINDING)
                .build());
        // 应该不可能出现
        if (Objects.isNull(resp)) throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);

        responseObserver.onNext(
                CreatePetCodeAndBindingResult.newBuilder().setPetCode(code).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateBinding(UpdateBindingParams request, StreamObserver<UpdateBindingResult> responseObserver) {
        // update the binding
        var petId = request.getPetId();
        var petCodeId = request.getPetCodeId();
        var status = BindingPetCodeRequest.Status.forNumber(request.getStatus().getNumber());

        var resp = petCodeServiceBlockingStub.bindingPetCode(BindingPetCodeRequest.newBuilder()
                .setPetCodeId(petCodeId)
                .setPetId(petId)
                .setComment(request.getComment())
                .setStatus(status)
                .build());

        // 应该也不可能出现
        if (Objects.isNull(resp)) throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        responseObserver.onNext(UpdateBindingResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listPetCode(ListPetCodeParams request, StreamObserver<ListPetCodeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var listRequest =
                ListPetCodeRequest.newBuilder().setCompanyId(companyId).build();
        var listResponse = petCodeServiceBlockingStub.listPetCode(listRequest);

        var result = ListPetCodeResult.newBuilder()
                .addAllPetCodes(listResponse.getPetCodesList())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void createPetCode(CreatePetCodeParams request, StreamObserver<CreatePetCodeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var createRequest = CreatePetCodeRequest.newBuilder()
                .setCompanyId(companyId)
                .setPetCode(request.getPetCode())
                .build();
        var createResponse = petCodeServiceBlockingStub.createPetCode(createRequest);

        var result = CreatePetCodeResult.newBuilder()
                .setPetCode(createResponse.getPetCode())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void updatePetCode(UpdatePetCodeParams request, StreamObserver<UpdatePetCodeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var updateRequest = UpdatePetCodeRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .setPetCode(request.getPetCode())
                .build();
        petCodeServiceBlockingStub.updatePetCode(updateRequest);

        responseObserver.onNext(UpdatePetCodeResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void sortPetCode(SortPetCodeParams request, StreamObserver<SortPetCodeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var sortRequest = SortPetCodeRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllIds(request.getIdsList())
                .build();
        petCodeServiceBlockingStub.sortPetCode(sortRequest);

        responseObserver.onNext(SortPetCodeResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_CLIENT_PET_SETTING})
    public void deletePetCode(DeletePetCodeParams request, StreamObserver<DeletePetCodeResult> responseObserver) {

        var companyId = AuthContext.get().companyId();

        var deleteRequest = DeletePetCodeRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(request.getId())
                .build();
        petCodeServiceBlockingStub.deletePetCode(deleteRequest);

        responseObserver.onNext(DeletePetCodeResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
