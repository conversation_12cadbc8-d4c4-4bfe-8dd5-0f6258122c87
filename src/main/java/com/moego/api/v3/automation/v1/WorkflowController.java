package com.moego.api.v3.automation.v1;

import com.moego.idl.api.automation.v1.CreateWorkflowParams;
import com.moego.idl.api.automation.v1.CreateWorkflowResult;
import com.moego.idl.api.automation.v1.CreateWorkflowTemplateParams;
import com.moego.idl.api.automation.v1.CreateWorkflowTemplateResult;
import com.moego.idl.api.automation.v1.FilterCustomerParams;
import com.moego.idl.api.automation.v1.FilterCustomerResult;
import com.moego.idl.api.automation.v1.GetWorkflowConfigParams;
import com.moego.idl.api.automation.v1.GetWorkflowConfigResult;
import com.moego.idl.api.automation.v1.GetWorkflowInfoParams;
import com.moego.idl.api.automation.v1.GetWorkflowInfoResult;
import com.moego.idl.api.automation.v1.GetWorkflowSettingParams;
import com.moego.idl.api.automation.v1.GetWorkflowSettingResult;
import com.moego.idl.api.automation.v1.GetWorkflowTemplateInfoParams;
import com.moego.idl.api.automation.v1.GetWorkflowTemplateInfoResult;
import com.moego.idl.api.automation.v1.ListEnterpriseWorkflowsParams;
import com.moego.idl.api.automation.v1.ListEnterpriseWorkflowsResult;
import com.moego.idl.api.automation.v1.ListWorkflowCategoriesParams;
import com.moego.idl.api.automation.v1.ListWorkflowCategoriesResult;
import com.moego.idl.api.automation.v1.ListWorkflowRecordsParams;
import com.moego.idl.api.automation.v1.ListWorkflowRecordsResult;
import com.moego.idl.api.automation.v1.ListWorkflowTemplatesParams;
import com.moego.idl.api.automation.v1.ListWorkflowTemplatesResult;
import com.moego.idl.api.automation.v1.ListWorkflowsParams;
import com.moego.idl.api.automation.v1.ListWorkflowsResult;
import com.moego.idl.api.automation.v1.UpdateWorkflowContentParams;
import com.moego.idl.api.automation.v1.UpdateWorkflowContentResult;
import com.moego.idl.api.automation.v1.UpdateWorkflowInfoParams;
import com.moego.idl.api.automation.v1.UpdateWorkflowInfoResult;
import com.moego.idl.api.automation.v1.UpdateWorkflowSettingParams;
import com.moego.idl.api.automation.v1.UpdateWorkflowSettingResult;
import com.moego.idl.api.automation.v1.WorkflowServiceGrpc;
import com.moego.idl.service.automation.v1.CreateWorkflowRequest;
import com.moego.idl.service.automation.v1.CreateWorkflowResponse;
import com.moego.idl.service.automation.v1.CreateWorkflowTemplateRequest;
import com.moego.idl.service.automation.v1.CreateWorkflowTemplateResponse;
import com.moego.idl.service.automation.v1.FilterCustomerRequest;
import com.moego.idl.service.automation.v1.FilterCustomerResponse;
import com.moego.idl.service.automation.v1.GetWorkflowConfigRequest;
import com.moego.idl.service.automation.v1.GetWorkflowConfigResponse;
import com.moego.idl.service.automation.v1.GetWorkflowInfoRequest;
import com.moego.idl.service.automation.v1.GetWorkflowInfoResponse;
import com.moego.idl.service.automation.v1.GetWorkflowSettingRequest;
import com.moego.idl.service.automation.v1.GetWorkflowSettingResponse;
import com.moego.idl.service.automation.v1.GetWorkflowTemplateInfoRequest;
import com.moego.idl.service.automation.v1.GetWorkflowTemplateInfoResponse;
import com.moego.idl.service.automation.v1.ListEnterpriseWorkflowsRequest;
import com.moego.idl.service.automation.v1.ListEnterpriseWorkflowsResponse;
import com.moego.idl.service.automation.v1.ListWorkflowCategoriesRequest;
import com.moego.idl.service.automation.v1.ListWorkflowCategoriesResponse;
import com.moego.idl.service.automation.v1.ListWorkflowTemplatesRequest;
import com.moego.idl.service.automation.v1.ListWorkflowTemplatesResponse;
import com.moego.idl.service.automation.v1.ListWorkflowsRequest;
import com.moego.idl.service.automation.v1.ListWorkflowsResponse;
import com.moego.idl.service.automation.v1.UpdateWorkflowContentRequest;
import com.moego.idl.service.automation.v1.UpdateWorkflowContentResponse;
import com.moego.idl.service.automation.v1.UpdateWorkflowInfoRequest;
import com.moego.idl.service.automation.v1.UpdateWorkflowInfoResponse;
import com.moego.idl.service.automation.v1.UpdateWorkflowSettingRequest;
import com.moego.idl.service.automation.v1.UpdateWorkflowSettingResponse;
import com.moego.idl.service.automation.v1.WorkflowServiceGrpc.WorkflowServiceBlockingStub;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@AllArgsConstructor
public class WorkflowController extends WorkflowServiceGrpc.WorkflowServiceImplBase {

    private final WorkflowServiceBlockingStub workflowServiceBlockingStub;

    @Auth(AuthType.COMPANY)
    @Override
    public void getWorkflowConfig(
            GetWorkflowConfigParams request, StreamObserver<GetWorkflowConfigResult> responseObserver) {
        GetWorkflowConfigResponse resp = workflowServiceBlockingStub.getWorkflowConfig(
                GetWorkflowConfigRequest.newBuilder().build());
        responseObserver.onNext(GetWorkflowConfigResult.newBuilder()
                .addAllWorkflowConfigs(resp.getWorkflowConfigsList())
                .addAllFilterGroups(resp.getFilterGroupsList())
                .addAllEventFilterGroups(resp.getEventFilterGroupsList())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void createWorkflow(CreateWorkflowParams request, StreamObserver<CreateWorkflowResult> responseObserver) {
        CreateWorkflowResponse resp = workflowServiceBlockingStub.createWorkflow(CreateWorkflowRequest.newBuilder()
                .setWorkflow(request.getWorkflow())
                .build());
        responseObserver.onNext(CreateWorkflowResult.newBuilder()
                .setWorkflow(resp.getWorkflow())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void updateWorkflowContent(
            UpdateWorkflowContentParams request, StreamObserver<UpdateWorkflowContentResult> responseObserver) {
        UpdateWorkflowContentResponse resp =
                workflowServiceBlockingStub.updateWorkflowContent(UpdateWorkflowContentRequest.newBuilder()
                        .setWorkflowId(request.getWorkflowId())
                        .addAllSteps(request.getStepsList())
                        .setConsumerData(request.getConsumerData())
                        .build());

        responseObserver.onNext(UpdateWorkflowContentResult.newBuilder()
                .setWorkflow(resp.getWorkflow())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void updateWorkflowInfo(
            UpdateWorkflowInfoParams request, StreamObserver<UpdateWorkflowInfoResult> responseObserver) {
        UpdateWorkflowInfoRequest.Builder builder =
                UpdateWorkflowInfoRequest.newBuilder().setWorkflowId(request.getWorkflowId());
        if (request.hasName()) {
            builder.setName(request.getName());
        }
        if (request.hasDesc()) {
            builder.setDesc(request.getDesc());
        }
        if (request.hasStatus()) {
            builder.setStatus(request.getStatus());
        }
        if (request.hasShutDownPendingSteps()) {
            builder.setShutDownPendingSteps(request.getShutDownPendingSteps());
        }

        UpdateWorkflowInfoResponse resp = workflowServiceBlockingStub.updateWorkflowInfo(builder.build());
        responseObserver.onNext(UpdateWorkflowInfoResult.newBuilder()
                .setWorkflow(resp.getWorkflow())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void listWorkflowCategories(
            ListWorkflowCategoriesParams request, StreamObserver<ListWorkflowCategoriesResult> responseObserver) {
        ListWorkflowCategoriesResponse resp = workflowServiceBlockingStub.listWorkflowCategories(
                ListWorkflowCategoriesRequest.newBuilder().build());
        responseObserver.onNext(ListWorkflowCategoriesResult.newBuilder()
                .addAllCategories(resp.getCategoriesList())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void listWorkflows(ListWorkflowsParams request, StreamObserver<ListWorkflowsResult> responseObserver) {
        ListWorkflowsRequest.Builder builder = ListWorkflowsRequest.newBuilder();
        if (request.hasPagination()) {
            builder.setPagination(request.getPagination());
        }
        if (request.hasFilter()) {
            ListWorkflowsRequest.Filter.Builder filterBuilder = ListWorkflowsRequest.Filter.newBuilder();
            if (request.getFilter().hasName()) {
                filterBuilder.setName(request.getFilter().getName());
            }
            if (request.getFilter().hasCategoryId()) {
                filterBuilder.setCategoryId(request.getFilter().getCategoryId());
            }
            filterBuilder.addAllStatus(request.getFilter().getStatusList());
            builder.setFilter(filterBuilder.build());
        }

        ListWorkflowsResponse resp = workflowServiceBlockingStub.listWorkflows(builder.build());
        responseObserver.onNext(ListWorkflowsResult.newBuilder()
                .addAllWorkflows(resp.getWorkflowsList())
                .setPagination(resp.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void listEnterpriseWorkflows(
            ListEnterpriseWorkflowsParams request, StreamObserver<ListEnterpriseWorkflowsResult> responseObserver) {
        ListEnterpriseWorkflowsRequest.Builder builder = ListEnterpriseWorkflowsRequest.newBuilder();
        if (request.hasPagination()) {
            builder.setPagination(request.getPagination());
        }
        if (request.hasFilter()) {
            ListEnterpriseWorkflowsRequest.Filter.Builder filterBuilder =
                    ListEnterpriseWorkflowsRequest.Filter.newBuilder();
            if (request.getFilter().hasName()) {
                filterBuilder.setName(request.getFilter().getName());
            }
            filterBuilder.addAllStatus(request.getFilter().getStatusList());
            builder.setFilter(filterBuilder.build());
        }

        ListEnterpriseWorkflowsResponse resp = workflowServiceBlockingStub.listEnterpriseWorkflows(builder.build());
        responseObserver.onNext(ListEnterpriseWorkflowsResult.newBuilder()
                .addAllWorkflows(resp.getWorkflowsList())
                .setPagination(resp.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void listWorkflowRecords(
            ListWorkflowRecordsParams request, StreamObserver<ListWorkflowRecordsResult> responseObserver) {
        super.listWorkflowRecords(request, responseObserver);
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void listWorkflowTemplates(
            ListWorkflowTemplatesParams request, StreamObserver<ListWorkflowTemplatesResult> responseObserver) {
        ListWorkflowTemplatesRequest.Builder builder =
                ListWorkflowTemplatesRequest.newBuilder().setPagination(request.getPagination());
        if (request.hasFilter()) {
            ListWorkflowTemplatesRequest.Filter.Builder filterBuilder =
                    ListWorkflowTemplatesRequest.Filter.newBuilder();
            if (request.getFilter().hasName()) {
                filterBuilder.setName(request.getFilter().getName());
            }
            if (request.getFilter().hasCategoryId()) {
                filterBuilder.setCategoryId(request.getFilter().getCategoryId());
            }
            if (request.getFilter().hasRecommendType()) {
                filterBuilder.setRecommendType(request.getFilter().getRecommendType());
            }
            builder.setFilter(filterBuilder.build());
        }

        ListWorkflowTemplatesResponse resp = workflowServiceBlockingStub.listWorkflowTemplates(builder.build());
        responseObserver.onNext(ListWorkflowTemplatesResult.newBuilder()
                .addAllWorkflows(resp.getWorkflowsList())
                .setPagination(resp.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void getWorkflowInfo(GetWorkflowInfoParams request, StreamObserver<GetWorkflowInfoResult> responseObserver) {
        GetWorkflowInfoResponse resp = workflowServiceBlockingStub.getWorkflowInfo(GetWorkflowInfoRequest.newBuilder()
                .setWorkflowId(request.getWorkflowId())
                .build());
        responseObserver.onNext(GetWorkflowInfoResult.newBuilder()
                .setWorkflow(resp.getWorkflow())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void getWorkflowTemplateInfo(
            GetWorkflowTemplateInfoParams request, StreamObserver<GetWorkflowTemplateInfoResult> responseObserver) {
        GetWorkflowTemplateInfoResponse resp =
                workflowServiceBlockingStub.getWorkflowTemplateInfo(GetWorkflowTemplateInfoRequest.newBuilder()
                        .setWorkflowId(request.getWorkflowId())
                        .build());
        responseObserver.onNext(GetWorkflowTemplateInfoResult.newBuilder()
                .setWorkflow(resp.getWorkflow())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void updateWorkflowSetting(
            UpdateWorkflowSettingParams request, StreamObserver<UpdateWorkflowSettingResult> responseObserver) {
        UpdateWorkflowSettingRequest build = UpdateWorkflowSettingRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setSetting(request.getSetting())
                .build();
        UpdateWorkflowSettingResponse resp = workflowServiceBlockingStub.updateWorkflowSetting(build);

        responseObserver.onNext(UpdateWorkflowSettingResult.newBuilder()
                .setSetting(resp.getSetting())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void getWorkflowSetting(
            GetWorkflowSettingParams request, StreamObserver<GetWorkflowSettingResult> responseObserver) {
        GetWorkflowSettingResponse resp =
                workflowServiceBlockingStub.getWorkflowSetting(GetWorkflowSettingRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .build());
        responseObserver.onNext(GetWorkflowSettingResult.newBuilder()
                .setSetting(resp.getSetting())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void createWorkflowTemplate(
            CreateWorkflowTemplateParams request, StreamObserver<CreateWorkflowTemplateResult> responseObserver) {
        CreateWorkflowTemplateResponse resp =
                workflowServiceBlockingStub.createWorkflowTemplate(CreateWorkflowTemplateRequest.newBuilder()
                        .setWorkflow(request.getWorkflow())
                        .build());
        responseObserver.onNext(CreateWorkflowTemplateResult.newBuilder()
                .setWorkflow(resp.getWorkflow())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void filterCustomer(FilterCustomerParams request, StreamObserver<FilterCustomerResult> responseObserver) {
        FilterCustomerRequest.Builder reqBuilder = FilterCustomerRequest.newBuilder()
                .addAllFilters(request.getFiltersList())
                .setPagination(request.getPagination());
        if (request.hasCustomerId()) {
            reqBuilder.setCustomerId(request.getCustomerId());
        }
        Integer companyId = AuthContext.get().getCompanyId();
        if (companyId != null) {
            reqBuilder.setCompanyId(companyId);
        }

        FilterCustomerResponse resp = workflowServiceBlockingStub.filterCustomer(reqBuilder.build());
        responseObserver.onNext(FilterCustomerResult.newBuilder()
                .addAllCustomer(resp.getCustomerList())
                .setPagination(resp.getPagination())
                .build());
        responseObserver.onCompleted();
    }
}
