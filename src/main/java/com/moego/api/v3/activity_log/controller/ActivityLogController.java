package com.moego.api.v3.activity_log.controller;

import static com.moego.idl.service.activity_log.v1.ActivityLogServiceGrpc.ActivityLogServiceBlockingStub;

import com.moego.idl.api.activity_log.v1.ActivityLogServiceGrpc;
import com.moego.idl.api.activity_log.v1.GetActivityLogDetailsRequest;
import com.moego.idl.api.activity_log.v1.GetActivityLogDetailsResponse;
import com.moego.idl.api.activity_log.v1.SearchActionPageRequest;
import com.moego.idl.api.activity_log.v1.SearchActionPageResponse;
import com.moego.idl.api.activity_log.v1.SearchActivityLogPageRequest;
import com.moego.idl.api.activity_log.v1.SearchActivityLogPageResponse;
import com.moego.idl.api.activity_log.v1.SearchOperatorPageRequest;
import com.moego.idl.api.activity_log.v1.SearchOperatorPageResponse;
import com.moego.idl.api.activity_log.v1.SearchOwnerPageRequest;
import com.moego.idl.api.activity_log.v1.SearchOwnerPageResponse;
import com.moego.idl.api.activity_log.v1.SearchResourceTypePageRequest;
import com.moego.idl.api.activity_log.v1.SearchResourceTypePageResponse;
import com.moego.idl.service.activity_log.v1.GetActivityLogDetailsInput;
import com.moego.idl.service.activity_log.v1.GetActivityLogDetailsOutput;
import com.moego.idl.service.activity_log.v1.SearchActionPageInput;
import com.moego.idl.service.activity_log.v1.SearchActionPageOutput;
import com.moego.idl.service.activity_log.v1.SearchActivityLogPageInput;
import com.moego.idl.service.activity_log.v1.SearchActivityLogPageOutput;
import com.moego.idl.service.activity_log.v1.SearchOperatorPageInput;
import com.moego.idl.service.activity_log.v1.SearchOperatorPageOutput;
import com.moego.idl.service.activity_log.v1.SearchOwnerPageInput;
import com.moego.idl.service.activity_log.v1.SearchOwnerPageOutput;
import com.moego.idl.service.activity_log.v1.SearchResourceTypePageInput;
import com.moego.idl.service.activity_log.v1.SearchResourceTypePageOutput;
import com.moego.idl.utils.v1.PaginationResponse;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/3/20
 */
@GrpcService
@RequiredArgsConstructor
public class ActivityLogController extends ActivityLogServiceGrpc.ActivityLogServiceImplBase {

    private final ActivityLogServiceBlockingStub activityLogClient;

    @Override
    @Auth(AuthType.BUSINESS)
    public void searchActivityLogPage(
            SearchActivityLogPageRequest request, StreamObserver<SearchActivityLogPageResponse> ro) {
        SearchActivityLogPageInput.Builder builder = SearchActivityLogPageInput.newBuilder();

        var pageBuilder = PaginationRequest.newBuilder();
        if (request.hasPagination()) {
            pageBuilder.setPageNum(request.getPagination().getPageNum());
            pageBuilder.setPageSize(request.getPagination().getPageSize());
        } else {
            pageBuilder.setPageNum((int) (request.getPage().getPageNo() + 1));
            pageBuilder.setPageSize((int) request.getPage().getPageSize());
        }
        var page = pageBuilder.build();
        builder.setPagination(page);

        builder.setBusinessId(AuthContext.get().businessId());
        if (request.hasStartTime()) {
            builder.setStartTime(request.getStartTime());
        }
        if (request.hasEndTime()) {
            builder.setEndTime(request.getEndTime());
        }
        builder.addAllResourceType(request.getResourceTypeList());
        builder.addAllResourceId(request.getResourceIdList());
        builder.addAllOperatorId(request.getOperatorIdList());
        builder.addAllAction(request.getActionList());
        builder.addAllOwnerId(request.getOwnerIdList());

        SearchActivityLogPageOutput output = activityLogClient.searchActivityLogPage(builder.build());

        var oldPage = PaginationResponse.newBuilder()
                .setPageNo(page.getPageNum() - 1)
                .setPageSize(page.getPageSize())
                .setTotal(output.getPagination().getTotal())
                .build();

        SearchActivityLogPageResponse result = SearchActivityLogPageResponse.newBuilder()
                .addAllActivityLogs(output.getActivityLogsList())
                .setPage(oldPage)
                .setPagination(output.getPagination())
                .build();

        ro.onNext(result);
        ro.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void getActivityLogDetails(
            GetActivityLogDetailsRequest request, StreamObserver<GetActivityLogDetailsResponse> ro) {
        GetActivityLogDetailsOutput output = activityLogClient.getActivityLogDetails(
                GetActivityLogDetailsInput.newBuilder().setId(request.getId()).build());

        GetActivityLogDetailsResponse result = GetActivityLogDetailsResponse.newBuilder()
                .setActivityLog(output.getActivityLog())
                .addAllEffects(output.getEffectsList())
                .build();

        ro.onNext(result);
        ro.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void searchResourceTypePage(
            SearchResourceTypePageRequest request, StreamObserver<SearchResourceTypePageResponse> ro) {
        SearchResourceTypePageInput.Builder builder = SearchResourceTypePageInput.newBuilder();

        var pageBuilder = PaginationRequest.newBuilder();
        if (request.hasPagination()) {
            pageBuilder.setPageNum(request.getPagination().getPageNum());
            pageBuilder.setPageSize(request.getPagination().getPageSize());
        } else {
            pageBuilder.setPageNum((int) (request.getPage().getPageNo() + 1));
            pageBuilder.setPageSize((int) request.getPage().getPageSize());
        }
        var page = pageBuilder.build();
        builder.setPagination(page);

        builder.setBusinessId(AuthContext.get().businessId());
        if (request.hasResourceType()) {
            builder.setResourceType(request.getResourceType());
        }

        SearchResourceTypePageOutput output = activityLogClient.searchResourceTypePage(builder.build());

        var oldPage = PaginationResponse.newBuilder()
                .setPageNo(page.getPageNum() - 1)
                .setPageSize(page.getPageSize())
                .setTotal(output.getPagination().getTotal())
                .build();

        SearchResourceTypePageResponse result = SearchResourceTypePageResponse.newBuilder()
                .addAllResourceTypes(output.getResourceTypesList())
                .setPage(oldPage)
                .setPagination(output.getPagination())
                .build();

        ro.onNext(result);
        ro.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void searchOperatorPage(SearchOperatorPageRequest request, StreamObserver<SearchOperatorPageResponse> ro) {
        SearchOperatorPageInput.Builder builder = SearchOperatorPageInput.newBuilder();

        var pageBuilder = PaginationRequest.newBuilder();
        if (request.hasPagination()) {
            pageBuilder.setPageNum(request.getPagination().getPageNum());
            pageBuilder.setPageSize(request.getPagination().getPageSize());
        } else {
            pageBuilder.setPageNum((int) (request.getPage().getPageNo() + 1));
            pageBuilder.setPageSize((int) request.getPage().getPageSize());
        }
        var page = pageBuilder.build();
        builder.setPagination(page);

        builder.setBusinessId(AuthContext.get().businessId());
        if (request.hasOperatorName()) {
            builder.setOperatorName(request.getOperatorName());
        }

        SearchOperatorPageOutput output = activityLogClient.searchOperatorPage(builder.build());

        var oldPage = PaginationResponse.newBuilder()
                .setPageNo(page.getPageNum() - 1)
                .setPageSize(page.getPageSize())
                .setTotal(output.getPagination().getTotal())
                .build();

        SearchOperatorPageResponse result = SearchOperatorPageResponse.newBuilder()
                .addAllOperators(output.getOperatorsList())
                .setPage(oldPage)
                .setPagination(output.getPagination())
                .build();

        ro.onNext(result);
        ro.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void searchActionPage(SearchActionPageRequest request, StreamObserver<SearchActionPageResponse> ro) {
        SearchActionPageInput.Builder builder = SearchActionPageInput.newBuilder();

        var pageBuilder = PaginationRequest.newBuilder();
        if (request.hasPagination()) {
            pageBuilder.setPageNum(request.getPagination().getPageNum());
            pageBuilder.setPageSize(request.getPagination().getPageSize());
        } else {
            pageBuilder.setPageNum((int) (request.getPage().getPageNo() + 1));
            pageBuilder.setPageSize((int) request.getPage().getPageSize());
        }
        var page = pageBuilder.build();
        builder.setPagination(page);

        builder.setBusinessId(AuthContext.get().businessId());
        if (request.hasAction()) {
            builder.setAction(request.getAction());
        }

        SearchActionPageOutput output = activityLogClient.searchActionPage(builder.build());

        var oldPage = PaginationResponse.newBuilder()
                .setPageNo(page.getPageNum() - 1)
                .setPageSize(page.getPageSize())
                .setTotal(output.getPagination().getTotal())
                .build();

        SearchActionPageResponse result = SearchActionPageResponse.newBuilder()
                .addAllActions(output.getActionsList())
                .setPage(oldPage)
                .setPagination(output.getPagination())
                .build();

        ro.onNext(result);
        ro.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void searchOwnerPage(SearchOwnerPageRequest request, StreamObserver<SearchOwnerPageResponse> ro) {
        SearchOwnerPageInput.Builder builder = SearchOwnerPageInput.newBuilder();

        var pageBuilder = PaginationRequest.newBuilder();
        if (request.hasPagination()) {
            pageBuilder.setPageNum(request.getPagination().getPageNum());
            pageBuilder.setPageSize(request.getPagination().getPageSize());
        } else {
            pageBuilder.setPageNum((int) (request.getPage().getPageNo() + 1));
            pageBuilder.setPageSize((int) request.getPage().getPageSize());
        }
        var page = pageBuilder.build();
        builder.setPagination(page);

        builder.setBusinessId(AuthContext.get().businessId());
        if (request.hasOwnerName()) {
            builder.setOwnerName(request.getOwnerName());
        }

        SearchOwnerPageOutput output = activityLogClient.searchOwnerPage(builder.build());

        var oldPage = PaginationResponse.newBuilder()
                .setPageNo(page.getPageNum() - 1)
                .setPageSize(page.getPageSize())
                .setTotal(output.getPagination().getTotal())
                .build();

        SearchOwnerPageResponse result = SearchOwnerPageResponse.newBuilder()
                .addAllOwners(output.getOwnersList())
                .setPage(oldPage)
                .setPagination(output.getPagination())
                .build();

        ro.onNext(result);
        ro.onCompleted();
    }
}
