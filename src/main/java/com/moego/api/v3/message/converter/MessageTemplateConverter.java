package com.moego.api.v3.message.converter;

import com.moego.idl.api.message.v1.CheckMessageTemplateNameExistParams;
import com.moego.idl.api.message.v1.CheckMessageTemplateNameExistResult;
import com.moego.idl.api.message.v1.CreateMessageTemplateParams;
import com.moego.idl.api.message.v1.CreateMessageTemplateResult;
import com.moego.idl.api.message.v1.DeleteMessageTemplateParams;
import com.moego.idl.api.message.v1.DeleteMessageTemplateResult;
import com.moego.idl.api.message.v1.GetMessageTemplateParams;
import com.moego.idl.api.message.v1.GetMessageTemplatePlaceholdersParams;
import com.moego.idl.api.message.v1.GetMessageTemplatePlaceholdersResult;
import com.moego.idl.api.message.v1.GetMessageTemplateResult;
import com.moego.idl.api.message.v1.GetMessageTemplatesParams;
import com.moego.idl.api.message.v1.GetMessageTemplatesResult;
import com.moego.idl.api.message.v1.GetRenderedMessageParams;
import com.moego.idl.api.message.v1.GetRenderedMessageResult;
import com.moego.idl.api.message.v1.UpdateMessageTemplateParams;
import com.moego.idl.api.message.v1.UpdateMessageTemplateResult;
import com.moego.idl.service.message.v1.CheckMessageTemplateNameExistRequest;
import com.moego.idl.service.message.v1.CheckMessageTemplateNameExistResponse;
import com.moego.idl.service.message.v1.CreateMessageTemplateRequest;
import com.moego.idl.service.message.v1.CreateMessageTemplateResponse;
import com.moego.idl.service.message.v1.DeleteMessageTemplateRequest;
import com.moego.idl.service.message.v1.DeleteMessageTemplateResponse;
import com.moego.idl.service.message.v1.GetMessageTemplatePlaceholdersRequest;
import com.moego.idl.service.message.v1.GetMessageTemplatePlaceholdersResponse;
import com.moego.idl.service.message.v1.GetMessageTemplateRequest;
import com.moego.idl.service.message.v1.GetMessageTemplateResponse;
import com.moego.idl.service.message.v1.GetMessageTemplatesRequest;
import com.moego.idl.service.message.v1.GetMessageTemplatesResponse;
import com.moego.idl.service.message.v1.GetRenderedMessageRequest;
import com.moego.idl.service.message.v1.GetRenderedMessageResponse;
import com.moego.idl.service.message.v1.UpdateMessageTemplateRequest;
import com.moego.idl.service.message.v1.UpdateMessageTemplateResponse;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface MessageTemplateConverter {

    MessageTemplateConverter INSTANCE = Mappers.getMapper(MessageTemplateConverter.class);

    CheckMessageTemplateNameExistRequest toRequest(CheckMessageTemplateNameExistParams params, Long businessId);

    CheckMessageTemplateNameExistResult toResult(CheckMessageTemplateNameExistResponse response);

    CreateMessageTemplateRequest toRequest(
            CreateMessageTemplateParams params, Long businessId, Long companyId, Long staffId);

    CreateMessageTemplateResult toResult(CreateMessageTemplateResponse response);

    GetMessageTemplateRequest toRequest(GetMessageTemplateParams params, Long businessId);

    GetMessageTemplateResult toResult(GetMessageTemplateResponse response);

    UpdateMessageTemplateRequest toRequest(UpdateMessageTemplateParams params, Long businessId, Long staffId);

    UpdateMessageTemplateResult toResult(UpdateMessageTemplateResponse response);

    DeleteMessageTemplateRequest toRequest(DeleteMessageTemplateParams params, Long businessId, Long staffId);

    DeleteMessageTemplateResult toResult(DeleteMessageTemplateResponse response);

    GetMessageTemplatesRequest toRequest(GetMessageTemplatesParams params, Long businessId);

    GetMessageTemplatesResult toResult(GetMessageTemplatesResponse response);

    GetMessageTemplatePlaceholdersRequest toRequest(GetMessageTemplatePlaceholdersParams params, Long businessId);

    GetMessageTemplatePlaceholdersResult toResult(GetMessageTemplatePlaceholdersResponse response);

    GetRenderedMessageRequest toRequest(GetRenderedMessageParams params, Long businessId);

    GetRenderedMessageResult toResult(GetRenderedMessageResponse response);
}
