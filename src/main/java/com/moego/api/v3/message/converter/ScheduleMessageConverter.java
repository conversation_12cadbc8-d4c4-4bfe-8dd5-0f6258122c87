package com.moego.api.v3.message.converter;

import com.moego.idl.api.message.v1.CreateScheduleMessageParams;
import com.moego.idl.api.message.v1.UpdateScheduleMessageParams;
import com.moego.idl.models.message.v1.ScheduleMessageModel;
import com.moego.idl.models.message.v1.ScheduleMessagePublicView;
import com.moego.idl.service.message.v1.CreateScheduleMessageRequest;
import com.moego.idl.service.message.v1.UpdateScheduleMessageRequest;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

/**
 * <AUTHOR>
 * @since 2023/11/19
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface ScheduleMessageConverter {

    CreateScheduleMessageRequest paramsToRequest(CreateScheduleMessageParams params);

    @Mapping(target = "content", source = "byCustomContent.content")
    @Mapping(target = "receiptContactId", source = "byCustomContent.receiptContactId")
    UpdateScheduleMessageRequest paramsToRequest(UpdateScheduleMessageParams params);

    ScheduleMessagePublicView modelToView(ScheduleMessageModel model);

    List<ScheduleMessagePublicView> modelToView(List<ScheduleMessageModel> models);
}
