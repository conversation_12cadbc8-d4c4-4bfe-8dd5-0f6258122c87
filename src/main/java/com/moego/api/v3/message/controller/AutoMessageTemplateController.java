package com.moego.api.v3.message.controller;

import com.moego.idl.api.message.v1.AutoMessageTemplateServiceGrpc;
import com.moego.idl.api.message.v1.GetPreviewTemplateParams;
import com.moego.idl.api.message.v1.GetPreviewTemplateResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.message.client.IMessageAutoClient;
import com.moego.server.message.dto.AutoMessageTemplatePreviewDTO;
import com.moego.server.message.enums.AutoMessageTemplateEnum;
import com.moego.server.message.params.AutoMessageTemplatePreviewParams;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/11/22
 */
@GrpcService
@RequiredArgsConstructor
public class AutoMessageTemplateController extends AutoMessageTemplateServiceGrpc.AutoMessageTemplateServiceImplBase {

    private final IMessageAutoClient messageAutoClient;

    @Override
    @Auth(AuthType.BUSINESS)
    public void getPreviewTemplate(
            GetPreviewTemplateParams request, StreamObserver<GetPreviewTemplateResult> responseObserver) {
        AutoMessageTemplatePreviewParams params = new AutoMessageTemplatePreviewParams()
                .setBusinessId(AuthContext.get().getBusinessId())
                .setAppointmentId((int) request.getForAppointment().getAppointmentId())
                .setTemplateType(AutoMessageTemplateEnum.fromValue(
                        request.getForAppointment().getTypeValue()));
        AutoMessageTemplatePreviewDTO previewDTO = messageAutoClient.getPreviewAutoMessageTemplate(params);

        responseObserver.onNext(GetPreviewTemplateResult.newBuilder()
                .setTemplateContent(previewDTO.getTemplate().getBody())
                .setPreviewContent(previewDTO.getPreviewContent())
                .build());
        responseObserver.onCompleted();
    }
}
