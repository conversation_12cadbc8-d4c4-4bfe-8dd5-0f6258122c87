package com.moego.api.v3.message.controller;

import com.moego.api.v3.message.converter.MessageTemplateConverter;
import com.moego.api.v3.permission.service.CompanyService;
import com.moego.idl.api.message.v1.CheckMessageTemplateNameExistParams;
import com.moego.idl.api.message.v1.CheckMessageTemplateNameExistResult;
import com.moego.idl.api.message.v1.CreateMessageTemplateParams;
import com.moego.idl.api.message.v1.CreateMessageTemplateResult;
import com.moego.idl.api.message.v1.DeleteMessageTemplateParams;
import com.moego.idl.api.message.v1.DeleteMessageTemplateResult;
import com.moego.idl.api.message.v1.GetMessageTemplateParams;
import com.moego.idl.api.message.v1.GetMessageTemplatePlaceholdersResult;
import com.moego.idl.api.message.v1.GetMessageTemplateResult;
import com.moego.idl.api.message.v1.GetMessageTemplatesParams;
import com.moego.idl.api.message.v1.GetMessageTemplatesResult;
import com.moego.idl.api.message.v1.GetRenderedMessageParams;
import com.moego.idl.api.message.v1.GetRenderedMessageResult;
import com.moego.idl.api.message.v1.MessageTemplateServiceGrpc;
import com.moego.idl.api.message.v1.UpdateMessageTemplateParams;
import com.moego.idl.api.message.v1.UpdateMessageTemplateResult;
import com.moego.idl.models.message.v1.MessageTemplatePlaceholderSimpleView;
import com.moego.idl.models.message.v1.MessageTemplateSimpleView;
import com.moego.idl.models.message.v1.MessageTemplateUseCase;
import com.moego.idl.service.message.v1.CheckMessageTemplateNameExistRequest;
import com.moego.idl.service.message.v1.CheckMessageTemplateNameExistResponse;
import com.moego.idl.service.message.v1.CreateMessageTemplateRequest;
import com.moego.idl.service.message.v1.CreateMessageTemplateResponse;
import com.moego.idl.service.message.v1.DeleteMessageTemplateRequest;
import com.moego.idl.service.message.v1.DeleteMessageTemplateResponse;
import com.moego.idl.service.message.v1.GetMessageTemplatePlaceholdersRequest;
import com.moego.idl.service.message.v1.GetMessageTemplatePlaceholdersResponse;
import com.moego.idl.service.message.v1.GetMessageTemplateRequest;
import com.moego.idl.service.message.v1.GetMessageTemplateResponse;
import com.moego.idl.service.message.v1.GetMessageTemplatesRequest;
import com.moego.idl.service.message.v1.GetMessageTemplatesResponse;
import com.moego.idl.service.message.v1.GetRenderedMessageRequest;
import com.moego.idl.service.message.v1.GetRenderedMessageResponse;
import com.moego.idl.service.message.v1.UpdateMessageTemplateRequest;
import com.moego.idl.service.message.v1.UpdateMessageTemplateResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.customer.client.IIntakeFormClient;
import com.moego.server.customer.dto.IntakeFormDTO;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class MessageTemplateController extends MessageTemplateServiceGrpc.MessageTemplateServiceImplBase {

    private final com.moego.idl.service.message.v1.MessageTemplateServiceGrpc.MessageTemplateServiceBlockingStub
            messageTemplateServiceBlockingStub;

    private final CompanyService companyService;

    private final IIntakeFormClient intakeFormClient;

    @Override
    @Auth(AuthType.BUSINESS)
    public void checkMessageTemplateNameExist(
            CheckMessageTemplateNameExistParams request,
            StreamObserver<CheckMessageTemplateNameExistResult> responseObserver) {
        CheckMessageTemplateNameExistRequest req = MessageTemplateConverter.INSTANCE.toRequest(
                request, AuthContext.get().businessId());
        CheckMessageTemplateNameExistResponse res =
                messageTemplateServiceBlockingStub.checkMessageTemplateNameExist(req);
        responseObserver.onNext(MessageTemplateConverter.INSTANCE.toResult(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void createMessageTemplate(
            CreateMessageTemplateParams request, StreamObserver<CreateMessageTemplateResult> responseObserver) {
        CreateMessageTemplateRequest req = MessageTemplateConverter.INSTANCE.toRequest(
                request,
                AuthContext.get().businessId(),
                AuthContext.get().companyId(),
                AuthContext.get().staffId());
        CreateMessageTemplateResponse res = messageTemplateServiceBlockingStub.createMessageTemplate(req);
        responseObserver.onNext(MessageTemplateConverter.INSTANCE.toResult(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void getMessageTemplate(
            GetMessageTemplateParams request, StreamObserver<GetMessageTemplateResult> responseObserver) {
        GetMessageTemplateRequest req = MessageTemplateConverter.INSTANCE.toRequest(
                request, AuthContext.get().businessId());
        GetMessageTemplateResponse res = messageTemplateServiceBlockingStub.getMessageTemplate(req);
        responseObserver.onNext(MessageTemplateConverter.INSTANCE.toResult(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void updateMessageTemplate(
            UpdateMessageTemplateParams request, StreamObserver<UpdateMessageTemplateResult> responseObserver) {
        UpdateMessageTemplateRequest req = MessageTemplateConverter.INSTANCE.toRequest(
                request, AuthContext.get().businessId(), AuthContext.get().staffId());
        UpdateMessageTemplateResponse res = messageTemplateServiceBlockingStub.updateMessageTemplate(req);
        responseObserver.onNext(MessageTemplateConverter.INSTANCE.toResult(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void deleteMessageTemplate(
            DeleteMessageTemplateParams request, StreamObserver<DeleteMessageTemplateResult> responseObserver) {
        DeleteMessageTemplateRequest req = MessageTemplateConverter.INSTANCE.toRequest(
                request, AuthContext.get().businessId(), AuthContext.get().staffId());
        DeleteMessageTemplateResponse res = messageTemplateServiceBlockingStub.deleteMessageTemplate(req);
        responseObserver.onNext(MessageTemplateConverter.INSTANCE.toResult(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void getMessageTemplates(
            GetMessageTemplatesParams request, StreamObserver<GetMessageTemplatesResult> responseObserver) {
        GetMessageTemplatesRequest req = MessageTemplateConverter.INSTANCE.toRequest(
                request, AuthContext.get().businessId());
        GetMessageTemplatesResponse res = messageTemplateServiceBlockingStub.getMessageTemplates(req);
        // 排序，system template 排前面
        List<MessageTemplateSimpleView> viewList = new ArrayList<>(res.getMessageTemplateSimpleViewsList());
        viewList.sort(Comparator.comparing(MessageTemplateSimpleView::getIsSystem)
                .reversed()
                .thenComparing(MessageTemplateSimpleView::getId));
        responseObserver.onNext(MessageTemplateConverter.INSTANCE.toResult(GetMessageTemplatesResponse.newBuilder()
                .addAllMessageTemplateSimpleViews(viewList)
                .build()));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void getMessageTemplatePlaceholders(
            com.moego.idl.api.message.v1.GetMessageTemplatePlaceholdersParams request,
            StreamObserver<com.moego.idl.api.message.v1.GetMessageTemplatePlaceholdersResult> responseObserver) {
        GetMessageTemplatePlaceholdersRequest req = MessageTemplateConverter.INSTANCE.toRequest(
                request, AuthContext.get().businessId());
        GetMessageTemplatePlaceholdersResponse res =
                messageTemplateServiceBlockingStub.getMessageTemplatePlaceholders(req);

        responseObserver.onNext(buildPlaceholderResult(request.getUseCase(), res));
        responseObserver.onCompleted();
    }

    /**
     * 临时白名单方案
     */
    private GetMessageTemplatePlaceholdersResult buildPlaceholderResult(
            MessageTemplateUseCase useCase, GetMessageTemplatePlaceholdersResponse res) {
        List<MessageTemplatePlaceholderSimpleView> views;
        if (companyService.isInBrandedAppWhiteList(AuthContext.get().companyId())) {
            views = res.getMessageTemplatePlaceholderSimpleViewsList().stream()
                    .filter(view -> !Objects.equals(view.getPlaceholderText(), "{petParentPortalLink}"))
                    .toList();
        } else {
            views = res.getMessageTemplatePlaceholderSimpleViewsList().stream()
                    .filter(view -> !Objects.equals(view.getPlaceholderText(), "{petParentAppDownloadLink}"))
                    .toList();
        }

        // add for sb channon, engagement follow up message placeholder 添加 intake form link。不加他说需求做不了。
        if (useCase == MessageTemplateUseCase.USE_CASE_ENGAGEMENT_FOLLOWUP) {
            views = addIntakeFormLink(views);
        }

        return GetMessageTemplatePlaceholdersResult.newBuilder()
                .addAllMessageTemplatePlaceholderSimpleViews(views)
                .build();
    }

    private List<MessageTemplatePlaceholderSimpleView> addIntakeFormLink(
            List<MessageTemplatePlaceholderSimpleView> views) {
        List<MessageTemplatePlaceholderSimpleView> result = new ArrayList<>(views);
        List<IntakeFormDTO> forms = intakeFormClient.getIntakeForms(
                AuthContext.get().companyId(), AuthContext.get().businessId());
        if (CollectionUtils.isEmpty(forms)) {
            return result;
        }
        for (IntakeFormDTO form : forms) {
            result.add(formDTOToView(form));
        }
        return result; // and fuck u, channon.
    }

    private MessageTemplatePlaceholderSimpleView formDTOToView(IntakeFormDTO dto) {
        return MessageTemplatePlaceholderSimpleView.newBuilder()
                .setPlaceholderText(String.format("{intakeFormLink:%d:%s}", dto.getFormId(), dto.getTitle()))
                .setPlaceholderName(dto.getTitle())
                .setPlaceholderGroup("Submit intake forms")
                .build(); // 日内瓦，越写越气。
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void getRenderedMessage(
            GetRenderedMessageParams request, StreamObserver<GetRenderedMessageResult> responseObserver) {
        GetRenderedMessageRequest req = MessageTemplateConverter.INSTANCE.toRequest(
                request, AuthContext.get().businessId());
        GetRenderedMessageResponse res = messageTemplateServiceBlockingStub.getRenderedMessage(req);
        responseObserver.onNext(MessageTemplateConverter.INSTANCE.toResult(res));
        responseObserver.onCompleted();
    }
}
