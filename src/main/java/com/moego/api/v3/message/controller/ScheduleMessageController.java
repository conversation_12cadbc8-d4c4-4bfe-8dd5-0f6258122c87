package com.moego.api.v3.message.controller;

import com.moego.api.v3.message.converter.BusinessCustomerConverter;
import com.moego.api.v3.message.converter.ScheduleMessageConverter;
import com.moego.api.v3.message.service.ScheduleMessageService;
import com.moego.common.dto.StaffPermissions;
import com.moego.common.enums.FeatureConst;
import com.moego.common.utils.PermissionUtil;
import com.moego.idl.api.message.v1.CreateScheduleMessageParams;
import com.moego.idl.api.message.v1.CreateScheduleMessageResult;
import com.moego.idl.api.message.v1.DeleteScheduleMessageParams;
import com.moego.idl.api.message.v1.DeleteScheduleMessageResult;
import com.moego.idl.api.message.v1.GetScheduleMessageParams;
import com.moego.idl.api.message.v1.GetScheduleMessageResult;
import com.moego.idl.api.message.v1.GetScheduleMessagesParams;
import com.moego.idl.api.message.v1.GetScheduleMessagesResult;
import com.moego.idl.api.message.v1.ScheduleMessageServiceGrpc;
import com.moego.idl.api.message.v1.SendScheduleMessageParams;
import com.moego.idl.api.message.v1.SendScheduleMessageResult;
import com.moego.idl.api.message.v1.UpdateScheduleMessageParams;
import com.moego.idl.api.message.v1.UpdateScheduleMessageResult;
import com.moego.idl.models.business_customer.v1.BusinessCustomerContactPublicView;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.message.v1.AutoMessageAppointmentDef;
import com.moego.idl.models.message.v1.Method;
import com.moego.idl.models.message.v1.ScheduleMessageCustomDef;
import com.moego.idl.models.message.v1.ScheduleMessageModel;
import com.moego.idl.models.message.v1.ScheduleMessageStatus;
import com.moego.idl.service.message.v1.CreateScheduleMessageRequest;
import com.moego.idl.service.message.v1.CreateScheduleMessageResponse;
import com.moego.idl.service.message.v1.DeleteAppointmentScheduleMessageRequest;
import com.moego.idl.service.message.v1.DeleteAppointmentScheduleMessageResponse;
import com.moego.idl.service.message.v1.DeleteScheduleMessageRequest;
import com.moego.idl.service.message.v1.GetScheduleMessagesRequest;
import com.moego.idl.service.message.v1.GetScheduleMessagesResponse;
import com.moego.idl.service.message.v1.ScheduleMessageServiceGrpc.ScheduleMessageServiceBlockingStub;
import com.moego.idl.service.message.v1.SendScheduleMessageRequest;
import com.moego.idl.service.message.v1.UpdateScheduleMessageRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.customer.client.ICustomerContactClient;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.CustomerContactDto;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.params.CustomerIdListParams;
import com.moego.server.message.client.IMessageAutoClient;
import com.moego.server.message.client.IMessageThreadClient;
import com.moego.server.message.dto.AutoMessageTemplatePreviewDTO;
import com.moego.server.message.dto.VisibleThreadCustomerDTO;
import com.moego.server.message.enums.AutoMessageTemplateEnum;
import com.moego.server.message.params.AutoMessageTemplatePreviewParams;
import com.moego.server.payment.client.IPaymentPlanClient;
import io.grpc.stub.StreamObserver;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2023/11/19
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class ScheduleMessageController extends ScheduleMessageServiceGrpc.ScheduleMessageServiceImplBase {

    private final ScheduleMessageServiceBlockingStub scheduleMessageServiceBlockingStub;
    private final ScheduleMessageService scheduleMessageService;
    private final ICustomerCustomerClient customerClient;
    private final ICustomerContactClient customerContactClient;
    private final IMessageAutoClient messageAutoClient;
    private final IPaymentPlanClient planClient;
    private final IBusinessStaffClient staffClient;
    private final IMessageThreadClient messageThreadClient;
    private final ScheduleMessageConverter scheduleMessageConverter;
    private final BusinessCustomerConverter businessCustomerConverter;

    @Override
    @Auth(AuthType.BUSINESS)
    public void createScheduleMessage(
            CreateScheduleMessageParams request, StreamObserver<CreateScheduleMessageResult> responseObserver) {
        planClient.checkFeatureCodeIsEnableByBid(
                Math.toIntExact(AuthContext.get().businessId()), FeatureConst.FC_SCHEDULED_MESSAGE);
        boolean isBefore = Instant.ofEpochSecond(request.getSendOutAt().getSeconds())
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime()
                .isBefore(LocalDateTime.now());
        if (isBefore) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Send out time must be after now");
        }

        CreateScheduleMessageRequest.Builder builder = CreateScheduleMessageRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .setCustomerId(request.getCustomerId())
                .setStaffId(AuthContext.get().staffId())
                .setSendOutAt(request.getSendOutAt())
                .setMethod(request.getMethod());
        if (request.getMethod().equals(Method.METHOD_UNSPECIFIED)) {
            builder.setMethod(Method.METHOD_SMS);
        }

        switch (request.getSendMethodCase()) {
            case BY_CUSTOM_CONTENT -> {
                ScheduleMessageCustomDef byCustomContent = request.getByCustomContent();
                if (byCustomContent.getReceiptContactId() == 0) {
                    CustomerContactDto contact = customerContactClient.getCustomerPrimaryPhoneNumberEmail(
                            Math.toIntExact(AuthContext.get().businessId()), Math.toIntExact(request.getCustomerId()));
                    builder.setReceiptContactId(contact.getContactId());
                } else {
                    builder.setReceiptContactId(byCustomContent.getReceiptContactId());
                }
                builder.setContent(byCustomContent.getContent());
            }
            case BY_AUTO_MESSAGE -> {
                AutoMessageAppointmentDef byAutoMessage = request.getByAutoMessage();
                DeleteAppointmentScheduleMessageResponse response =
                        scheduleMessageServiceBlockingStub.deleteAppointmentScheduleMessages(
                                DeleteAppointmentScheduleMessageRequest.newBuilder()
                                        .setAppointmentId(byAutoMessage.getAppointmentId())
                                        .setStaffId(AuthContext.get().staffId())
                                        .build());
                if (response.getDeletedCount() != 0) {
                    log.info(
                            "Delete existing schedule message success, appointmentId: {}",
                            byAutoMessage.getAppointmentId());
                }
                AutoMessageTemplatePreviewDTO previewDTO =
                        messageAutoClient.getPreviewAutoMessageTemplate(new AutoMessageTemplatePreviewParams()
                                .setBusinessId(Math.toIntExact(AuthContext.get().businessId()))
                                .setAppointmentId(Math.toIntExact(byAutoMessage.getAppointmentId()))
                                .setTemplateType(AutoMessageTemplateEnum.fromValue(byAutoMessage.getTypeValue())));
                builder.setType(byAutoMessage.getType())
                        .setContent(previewDTO.getPreviewContent())
                        .setAppointmentId(byAutoMessage.getAppointmentId());
            }
            default -> {}
        }

        CreateScheduleMessageResponse response =
                scheduleMessageServiceBlockingStub.createScheduleMessage(builder.build());
        responseObserver.onNext(CreateScheduleMessageResult.newBuilder()
                .setScheduleMessageId(response.getScheduleMessageId())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void updateScheduleMessage(
            UpdateScheduleMessageParams request, StreamObserver<UpdateScheduleMessageResult> responseObserver) {
        ScheduleMessageModel scheduleMessage =
                scheduleMessageService.getScheduleMessage(request.getScheduleMessageId());
        UpdateScheduleMessageRequest.Builder builder = scheduleMessageConverter.paramsToRequest(request).toBuilder()
                .setStaffId(AuthContext.get().staffId());
        if (scheduleMessage.getAutoMessageTypeValue() != 0) {
            builder.clearContent();
        }
        scheduleMessageServiceBlockingStub.updateScheduledMessage(builder.build());
        responseObserver.onNext(UpdateScheduleMessageResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void deleteScheduleMessage(
            DeleteScheduleMessageParams request, StreamObserver<DeleteScheduleMessageResult> responseObserver) {
        scheduleMessageService.getScheduleMessage(request.getScheduleMessageId());
        DeleteScheduleMessageRequest deleteScheduleMessageRequest = DeleteScheduleMessageRequest.newBuilder()
                .setScheduleMessageId(request.getScheduleMessageId())
                .setStaffId(AuthContext.get().staffId())
                .build();
        scheduleMessageServiceBlockingStub.deleteScheduleMessage(deleteScheduleMessageRequest);
        responseObserver.onNext(DeleteScheduleMessageResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void getScheduleMessage(
            GetScheduleMessageParams request, StreamObserver<GetScheduleMessageResult> responseObserver) {
        ScheduleMessageModel scheduleMessage =
                scheduleMessageService.getScheduleMessage(request.getScheduleMessageId());
        int customerId = (int) scheduleMessage.getCustomerId();
        MoeBusinessCustomerDTO customer = customerClient.getCustomerWithDeleted(customerId);

        responseObserver.onNext(GetScheduleMessageResult.newBuilder()
                .setScheduleMessage(scheduleMessageConverter.modelToView(scheduleMessage))
                .setCustomer(businessCustomerConverter.dtoToNameView(customer))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void getScheduleMessages(
            GetScheduleMessagesParams request, StreamObserver<GetScheduleMessagesResult> responseObserver) {
        GetScheduleMessagesRequest.Builder builder = GetScheduleMessagesRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .addAllStatuses(List.of(ScheduleMessageStatus.SCHEDULED))
                .addAllMethods(request.getMethodsList());
        VisibleThreadCustomerDTO visibleThreadCustomerDTO = messageThreadClient.getVisibleThreadCustomerList(
                AuthContext.get().businessId().intValue(),
                AuthContext.get().staffId().intValue());
        boolean isVisibleAll =
                visibleThreadCustomerDTO.getIsVisibleAll() != null && visibleThreadCustomerDTO.getIsVisibleAll();
        List<Long> visibleCustomerIds = visibleThreadCustomerDTO.getVisibleCustomerIds().stream()
                .map(Long::valueOf)
                .toList();
        boolean isNoPermission = !isVisibleAll
                && (CollectionUtils.isEmpty(visibleCustomerIds)
                        || (request.hasCustomerId() && !visibleCustomerIds.contains(request.getCustomerId())));
        if (isNoPermission) {
            responseObserver.onNext(GetScheduleMessagesResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        if (request.hasCustomerId()) {
            builder.addCustomerIds(request.getCustomerId());
        } else {
            builder.addAllCustomerIds(visibleCustomerIds);
        }

        GetScheduleMessagesResponse response = scheduleMessageServiceBlockingStub.getScheduleMessages(builder.build());
        if (response.getScheduleMessagesCount() == 0) {
            responseObserver.onNext(GetScheduleMessagesResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        // Get customers
        List<Integer> customerIds = response.getScheduleMessagesList().stream()
                .map(ScheduleMessageModel::getCustomerId)
                .map(Long::intValue)
                .distinct()
                .toList();
        CustomerIdListParams params = new CustomerIdListParams();
        params.setIdList(customerIds);
        List<MoeBusinessCustomerDTO> customers = customerClient.queryCustomerList(params);
        GetScheduleMessagesResult.Builder resultBuilder = GetScheduleMessagesResult.newBuilder()
                .addAllScheduleMessages(scheduleMessageConverter.modelToView(response.getScheduleMessagesList()))
                .addAllCustomers(businessCustomerConverter.dtoToNameView(customers));
        // Get contacts
        Map<Integer, List<CustomerContactDto>> contacts = customerContactClient.getCustomerContacts(customerIds);
        StaffPermissions permissions = staffClient.getBusinessRoleByStaffId(
                Math.toIntExact(AuthContext.get().staffId()));
        boolean isHidePhoneNumber =
                !PermissionUtil.checkStaffPermissionsInfo(permissions, PermissionUtil.VIEW_CLIENT_PHONE);
        contacts.values().stream().flatMap(List::stream).forEach(contact -> {
            BusinessCustomerContactPublicView view = businessCustomerConverter.dtoToView(contact);
            if (isHidePhoneNumber) {
                view = view.toBuilder()
                        .setPhoneNumber(PermissionUtil.phoneNumberConfusion(view.getPhoneNumber()))
                        .build();
            }
            resultBuilder.addContacts(view);
        });

        responseObserver.onNext(resultBuilder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void sendScheduleMessage(
            SendScheduleMessageParams request, StreamObserver<SendScheduleMessageResult> responseObserver) {
        ScheduleMessageModel scheduleMessage =
                scheduleMessageService.getScheduleMessage(request.getScheduleMessageId());
        scheduleMessageServiceBlockingStub.sendScheduleMessage(SendScheduleMessageRequest.newBuilder()
                .setScheduleMessage(scheduleMessage)
                .setStaffId(AuthContext.get().staffId())
                .build());

        responseObserver.onNext(SendScheduleMessageResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
