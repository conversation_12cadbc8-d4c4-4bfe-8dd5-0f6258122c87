package com.moego.api.v3.message.service;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.message.v1.ScheduleMessageModel;
import com.moego.idl.models.message.v1.ScheduleMessageStatus;
import com.moego.idl.service.message.v1.GetScheduleMessageRequest;
import com.moego.idl.service.message.v1.GetScheduleMessageResponse;
import com.moego.idl.service.message.v1.ScheduleMessageServiceGrpc;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/11/19
 */
@Service
@RequiredArgsConstructor
public class ScheduleMessageService {

    private final ScheduleMessageServiceGrpc.ScheduleMessageServiceBlockingStub scheduleMessageServiceBlockingStub;

    public ScheduleMessageModel getScheduleMessage(long scheduleMessageId) {
        GetScheduleMessageResponse response =
                scheduleMessageServiceBlockingStub.getScheduleMessage(GetScheduleMessageRequest.newBuilder()
                        .setScheduleMessageId(scheduleMessageId)
                        .build());
        if (!response.hasScheduleMessage()
                || Objects.equals(response.getScheduleMessage().getStatus(), ScheduleMessageStatus.DELETED)
                || !Objects.equals(
                        response.getScheduleMessage().getCompanyId(),
                        AuthContext.get().companyId())
                || !Objects.equals(
                        response.getScheduleMessage().getBusinessId(),
                        AuthContext.get().businessId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Schedule message not found");
        }
        return response.getScheduleMessage();
    }
}
