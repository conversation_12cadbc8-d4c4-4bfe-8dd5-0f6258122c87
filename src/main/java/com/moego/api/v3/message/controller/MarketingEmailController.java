package com.moego.api.v3.message.controller;

import static com.moego.lib.common.auth.AuthType.BUSINESS;

import com.google.protobuf.Empty;
import com.moego.api.v3.permission.service.CompanyService;
import com.moego.idl.api.message.v1.CalculateCreditCostRequest;
import com.moego.idl.api.message.v1.CalculateCreditCostResponse;
import com.moego.idl.api.message.v1.CancelScheduleEmailRequest;
import com.moego.idl.api.message.v1.DeleteEmailRequest;
import com.moego.idl.api.message.v1.GetAppointmentsAfterEmailRequest;
import com.moego.idl.api.message.v1.GetAppointmentsAfterEmailResponse;
import com.moego.idl.api.message.v1.GetAvailableEmailsCountResponse;
import com.moego.idl.api.message.v1.GetEmailDetailRequest;
import com.moego.idl.api.message.v1.GetEmailListRequest;
import com.moego.idl.api.message.v1.GetEmailListResponse;
import com.moego.idl.api.message.v1.GetMarketingEmailTemplateDetailRequest;
import com.moego.idl.api.message.v1.GetMarketingEmailTemplateListRequest;
import com.moego.idl.api.message.v1.GetMarketingEmailTemplateListResponse;
import com.moego.idl.api.message.v1.GetRecipientListRequest;
import com.moego.idl.api.message.v1.GetRecipientListResponse;
import com.moego.idl.api.message.v1.MarketingEmailServiceGrpc;
import com.moego.idl.api.message.v1.MassEmailSendRequest;
import com.moego.idl.api.message.v1.MassEmailSendResponse;
import com.moego.idl.api.message.v1.RescheduleEmailRequest;
import com.moego.idl.api.message.v1.SaveEmailDraftRequest;
import com.moego.idl.api.message.v1.SaveEmailDraftResponse;
import com.moego.idl.api.message.v1.SendNowRequest;
import com.moego.idl.api.message.v1.SendTestEmailRequest;
import com.moego.idl.api.message.v1.ViewEmailReplyRequest;
import com.moego.idl.api.message.v1.ViewEmailReplyResponse;
import com.moego.idl.models.message.v1.MarketingEmailModel;
import com.moego.idl.models.message.v1.MarketingEmailTemplateModel;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import java.util.Objects;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class MarketingEmailController extends MarketingEmailServiceGrpc.MarketingEmailServiceImplBase {

    private final com.moego.idl.service.message.v1.MarketingEmailServiceGrpc.MarketingEmailServiceBlockingStub
            marketingEmailServiceBlockingStub;
    private final CompanyService companyService;

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void massEmailSend(MassEmailSendRequest request, StreamObserver<MassEmailSendResponse> responseObserver) {
        var businessId = AuthContext.get().businessId();
        var companyId = AuthContext.get().companyId();
        var staffId = AuthContext.get().staffId();
        com.moego.idl.service.message.v1.MassEmailSendResponse resp = marketingEmailServiceBlockingStub.massEmailSend(
                com.moego.idl.service.message.v1.MassEmailSendRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setEmail(request.getEmail())
                        .setDraftId(request.getDraftId())
                        .setRecipientFilter(request.getRecipientFilter())
                        .setCompanyId(companyId)
                        .setStaffId(staffId)
                        .build());
        responseObserver.onNext(
                MassEmailSendResponse.newBuilder().setId(resp.getId()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void getEmailList(GetEmailListRequest request, StreamObserver<GetEmailListResponse> responseObserver) {
        var businessId = AuthContext.get().businessId();
        var companyId = AuthContext.get().companyId();
        com.moego.idl.service.message.v1.GetEmailListResponse resp = marketingEmailServiceBlockingStub.getEmailList(
                com.moego.idl.service.message.v1.GetEmailListRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setPagination(request.getPagination())
                        .setStatus(request.getStatus())
                        .setSubject(request.getSubject())
                        .setCompanyId(companyId)
                        .build());
        responseObserver.onNext(GetEmailListResponse.newBuilder()
                .setPagination(resp.getPagination())
                .addAllList(resp.getListList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void getEmailDetail(GetEmailDetailRequest request, StreamObserver<MarketingEmailModel> responseObserver) {
        var businessId = AuthContext.get().businessId();
        var companyId = AuthContext.get().companyId();
        MarketingEmailModel detail = marketingEmailServiceBlockingStub.getEmailDetail(
                com.moego.idl.service.message.v1.GetEmailDetailRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setEmailId(request.getId())
                        .setCompanyId(companyId)
                        .build());
        responseObserver.onNext(detail);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void sendNow(SendNowRequest request, StreamObserver<Empty> responseObserver) {
        var businessId = AuthContext.get().businessId();
        var companyId = AuthContext.get().companyId();
        var staffId = AuthContext.get().staffId();
        marketingEmailServiceBlockingStub.sendNow(com.moego.idl.service.message.v1.SendNowRequest.newBuilder()
                .setBusinessId(businessId)
                .setId(request.getId())
                .setCompanyId(companyId)
                .setStaffId(staffId)
                .build());
        responseObserver.onNext(Empty.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void sendTestEmail(SendTestEmailRequest request, StreamObserver<Empty> responseObserver) {
        var businessId = AuthContext.get().getBusinessId();
        var staffId = AuthContext.get().staffId();
        var companyId = AuthContext.get().companyId();
        marketingEmailServiceBlockingStub.sendTestEmail(
                com.moego.idl.service.message.v1.SendTestEmailRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setSubject(request.getSubject())
                        .setContent(request.getContent())
                        .setRecipientEmail(request.getRecipientEmail())
                        .setStaffId(staffId)
                        .addAllAttachments(request.getAttachmentsList())
                        .setCompanyId(companyId)
                        .build());
        responseObserver.onNext(Empty.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void getAvailableEmailsCount(
            Empty request, StreamObserver<GetAvailableEmailsCountResponse> responseObserver) {
        var businessId = AuthContext.get().businessId();
        var companyId = AuthContext.get().companyId();
        com.moego.idl.service.message.v1.GetAvailableEmailsCountResponse resp =
                marketingEmailServiceBlockingStub.getAvailableEmailsCount(
                        com.moego.idl.service.message.v1.GetAvailableEmailsCountRequest.newBuilder()
                                .setBusinessId(businessId)
                                .setCompanyId(companyId)
                                .build());
        responseObserver.onNext(GetAvailableEmailsCountResponse.newBuilder()
                .setAvailableEmails(resp.getAvailableEmails())
                .setUsedEmails(resp.getUsedEmails())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void cancelScheduleEmail(CancelScheduleEmailRequest request, StreamObserver<Empty> responseObserver) {
        var businessId = AuthContext.get().businessId();
        var companyId = AuthContext.get().companyId();
        var staffId = AuthContext.get().staffId();
        marketingEmailServiceBlockingStub.cancelScheduleEmail(
                com.moego.idl.service.message.v1.CancelScheduleEmailRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setId(request.getId())
                        .setCompanyId(companyId)
                        .setStaffId(staffId)
                        .build());
        responseObserver.onNext(Empty.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void saveEmailDraft(SaveEmailDraftRequest request, StreamObserver<SaveEmailDraftResponse> responseObserver) {
        var businessId = AuthContext.get().businessId();
        Long companyId = AuthContext.get().companyId();
        var staffId = AuthContext.get().staffId();
        com.moego.idl.service.message.v1.SaveEmailDraftResponse resp = marketingEmailServiceBlockingStub.saveEmailDraft(
                com.moego.idl.service.message.v1.SaveEmailDraftRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setCompanyId(companyId)
                        .setStaffId(staffId)
                        .setEmail(request.getEmail())
                        .setDraftId(request.getDraftId())
                        .setRecipientFilter(request.getRecipientFilter())
                        .build());
        responseObserver.onNext(
                SaveEmailDraftResponse.newBuilder().setId(resp.getId()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void rescheduleEmail(RescheduleEmailRequest request, StreamObserver<Empty> responseObserver) {
        var businessId = AuthContext.get().businessId();
        var companyId = AuthContext.get().companyId();
        var staffId = AuthContext.get().staffId();
        marketingEmailServiceBlockingStub.rescheduleEmail(
                com.moego.idl.service.message.v1.RescheduleEmailRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setCompanyId(companyId)
                        .setStaffId(staffId)
                        .setId(request.getId())
                        .setSendTime(request.getSendTime())
                        .build());
        responseObserver.onNext(Empty.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void getRecipientList(
            GetRecipientListRequest request, StreamObserver<GetRecipientListResponse> responseObserver) {
        var businessId = AuthContext.get().businessId();
        var companyId = AuthContext.get().companyId();
        com.moego.idl.service.message.v1.GetRecipientListResponse resp =
                marketingEmailServiceBlockingStub.getRecipientList(
                        com.moego.idl.service.message.v1.GetRecipientListRequest.newBuilder()
                                .setBusinessId(businessId)
                                .setCompanyId(companyId)
                                .setPagination(request.getPagination())
                                .setId(request.getId())
                                .setStatus(request.getStatus())
                                .build());
        responseObserver.onNext(GetRecipientListResponse.newBuilder()
                .setPagination(resp.getPagination())
                .addAllRecipients(resp.getRecipientsList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void getMarketingEmailTemplateList(
            GetMarketingEmailTemplateListRequest request,
            StreamObserver<GetMarketingEmailTemplateListResponse> responseObserver) {
        var businessId = AuthContext.get().businessId();
        var companyId = AuthContext.get().companyId();
        var resp = marketingEmailServiceBlockingStub.getMarketingEmailTemplateList(
                com.moego.idl.service.message.v1.GetMarketingEmailTemplateListRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setCompanyId(companyId)
                        .setPagination(request.getPagination())
                        .build());

        var templates = resp.getTemplatesList();
        if (!companyService.isInBrandedAppWhiteList(companyId)) {
            templates = templates.stream()
                    .filter(template -> !Objects.equals(
                            template.getType(),
                            MarketingEmailTemplateModel.MarketingEmailTemplateType.INTRODUCE_PET_PARENT_APP))
                    .toList();
        }

        responseObserver.onNext(GetMarketingEmailTemplateListResponse.newBuilder()
                .setPagination(resp.getPagination())
                .addAllTemplates(templates)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void getMarketingEmailTemplateDetail(
            GetMarketingEmailTemplateDetailRequest request,
            StreamObserver<MarketingEmailTemplateModel> responseObserver) {
        var businessId = AuthContext.get().businessId();
        var companyId = AuthContext.get().companyId();
        MarketingEmailTemplateModel model = marketingEmailServiceBlockingStub.getMarketingEmailTemplateDetail(
                com.moego.idl.service.message.v1.GetMarketingEmailTemplateDetailRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setCompanyId(companyId)
                        .setId(request.getId())
                        .build());
        responseObserver.onNext(model);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void viewEmailReply(ViewEmailReplyRequest request, StreamObserver<ViewEmailReplyResponse> responseObserver) {
        var businessId = AuthContext.get().businessId();
        var companyId = AuthContext.get().companyId();
        com.moego.idl.service.message.v1.ViewEmailReplyResponse resp = marketingEmailServiceBlockingStub.viewEmailReply(
                com.moego.idl.service.message.v1.ViewEmailReplyRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setCompanyId(companyId)
                        .setId(request.getId())
                        .build());
        responseObserver.onNext(ViewEmailReplyResponse.newBuilder()
                .setContent(resp.getContent())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void deleteEmail(DeleteEmailRequest request, StreamObserver<Empty> responseObserver) {
        var businessId = AuthContext.get().businessId();
        var companyId = AuthContext.get().companyId();
        marketingEmailServiceBlockingStub.deleteEmail(com.moego.idl.service.message.v1.DeleteEmailRequest.newBuilder()
                .setBusinessId(businessId)
                .setId(request.getId())
                .setCompanyId(companyId)
                .build());
        responseObserver.onNext(Empty.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void calculateCreditCost(
            CalculateCreditCostRequest request, StreamObserver<CalculateCreditCostResponse> responseObserver) {
        var businessId = AuthContext.get().businessId();
        var companyId = AuthContext.get().companyId();
        com.moego.idl.service.message.v1.CalculateCreditCostResponse resp =
                marketingEmailServiceBlockingStub.calculateCreditCost(
                        com.moego.idl.service.message.v1.CalculateCreditCostRequest.newBuilder()
                                .setBusinessId(businessId)
                                .setCompanyId(companyId)
                                .setRecipientCount(request.getRecipientCount())
                                .build());
        responseObserver.onNext(CalculateCreditCostResponse.newBuilder()
                .setCreditCost(resp.getCreditCost())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MARKETING_CAMPAIGN})
    public void getAppointmentsAfterEmail(
            GetAppointmentsAfterEmailRequest request,
            StreamObserver<GetAppointmentsAfterEmailResponse> responseObserver) {
        var businessId = AuthContext.get().businessId();
        var companyId = AuthContext.get().companyId();
        com.moego.idl.service.message.v1.GetAppointmentsAfterEmailResponse resp =
                marketingEmailServiceBlockingStub.getAppointmentsAfterEmail(
                        com.moego.idl.service.message.v1.GetAppointmentsAfterEmailRequest.newBuilder()
                                .setBusinessId(businessId)
                                .setCompanyId(companyId)
                                .setId(request.getId())
                                .setPagination(request.getPagination())
                                .build());
        responseObserver.onNext(GetAppointmentsAfterEmailResponse.newBuilder()
                .setPagination(resp.getPagination())
                .addAllAppointments(resp.getAppointmentsList())
                .setTotalExpected(resp.getTotalExpected())
                .build());
        responseObserver.onCompleted();
    }
}
