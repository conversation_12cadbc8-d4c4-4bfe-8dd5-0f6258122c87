/*
 * @since 2023-10-23 17:27:21
 * <AUTHOR> <<EMAIL>>
 */
package com.moego.api.v3.agreement.controller;

import com.moego.api.v3.agreement.converter.AgreementConverter;
import com.moego.api.v3.agreement.service.AgreementService;
import com.moego.idl.api.agreement.v1.AgreementServiceGrpc;
import com.moego.idl.api.agreement.v1.GetAgreementContentListByCompanyParams;
import com.moego.idl.api.agreement.v1.GetAgreementContentListByCompanyResult;
import com.moego.idl.api.agreement.v1.QueryCompanyAgreementInfoListParams;
import com.moego.idl.api.agreement.v1.QueryCompanyAgreementInfoListResult;
import com.moego.idl.models.agreement.v1.AgreementModel;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.service.organization.v1.GetStaffDetailRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.idl.utils.v1.Id;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023/10/27
 */
@GrpcService
@RequiredArgsConstructor
@Slf4j
public class AgreementController extends AgreementServiceGrpc.AgreementServiceImplBase {

    private final AgreementService agreementService;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub;

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getAgreement(Id request, StreamObserver<AgreementModel> responseObserver) {
        var response = agreementService.getAgreementById(request.getId());
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getAgreementContentListByCompany(
            GetAgreementContentListByCompanyParams request,
            StreamObserver<GetAgreementContentListByCompanyResult> responseObserver) {
        var agreementList = agreementService.getAgreementContentListByCompany(
                AuthContext.get().companyId(), request);
        responseObserver.onNext(GetAgreementContentListByCompanyResult.newBuilder()
                .addAllAgreementContentView(agreementList)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void queryCompanyAgreementInfoList(
            QueryCompanyAgreementInfoListParams request,
            StreamObserver<QueryCompanyAgreementInfoListResult> responseObserver) {
        var companyId = AuthContext.get().companyId();

        var workingLocationIds = staffServiceBlockingStub
                .getStaffDetail(GetStaffDetailRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setId(AuthContext.get().staffId())
                        .build())
                .getStaff()
                .getWorkingLocationListList()
                .stream()
                .map(LocationBriefView::getId)
                .toList();
        if (workingLocationIds.isEmpty()) {
            responseObserver.onNext(
                    QueryCompanyAgreementInfoListResult.newBuilder().build());
            responseObserver.onCompleted();
            return;
        }
        var agreementList = agreementService.getAgreementContentListByCompany(companyId, workingLocationIds, request);
        responseObserver.onNext(QueryCompanyAgreementInfoListResult.newBuilder()
                .addAllAgreementInfoList(
                        // 注意！这个mapstruct的转换方法是手动实现的，加字段不要漏了
                        AgreementConverter.INSTANCE.toAgreementInfoList(agreementList))
                .build());
        responseObserver.onCompleted();
    }
}
