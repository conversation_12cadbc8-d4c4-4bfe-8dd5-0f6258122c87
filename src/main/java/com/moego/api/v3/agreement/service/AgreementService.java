package com.moego.api.v3.agreement.service;

import com.moego.api.v3.agreement.converter.AgreementConverter;
import com.moego.idl.api.agreement.v1.GetAgreementContentListByCompanyParams;
import com.moego.idl.api.agreement.v1.QueryCompanyAgreementInfoListParams;
import com.moego.idl.models.agreement.v1.AgreementModel;
import com.moego.idl.models.agreement.v1.AgreementModelContentView;
import com.moego.idl.models.agreement.v1.ServiceType;
import com.moego.idl.service.agreement.v1.AgreementServiceGrpc;
import com.moego.idl.service.agreement.v1.GetAgreementListByCompanyRequest;
import com.moego.idl.service.agreement.v1.GetAgreementRequest;
import com.moego.idl.utils.v1.Status;
import io.micrometer.common.util.StringUtils;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/10/27
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AgreementService {

    private final AgreementServiceGrpc.AgreementServiceBlockingStub agreementServiceBlockingStub;

    public AgreementModel getAgreementById(Long id) {
        return agreementServiceBlockingStub.getAgreement(
                GetAgreementRequest.newBuilder().setId(id).build());
    }

    public List<AgreementModelContentView> getAgreementContentListByCompany(
            Long companyId, GetAgreementContentListByCompanyParams params) {

        return agreementServiceBlockingStub
                .getAgreementContentListByCompany(
                        AgreementConverter.INSTANCE.toGetAgreementListByCompanyRequest(companyId, params))
                .getAgreementContentViewList();
    }

    public List<AgreementModelContentView> getAgreementContentListByCompany(
            Long companyId, List<Long> filterBusinessIds, QueryCompanyAgreementInfoListParams params) {

        var serviceType = toServiceType(params.getServiceType(), null);
        var reqBuilder = GetAgreementListByCompanyRequest.newBuilder()
                .setCompanyId(companyId)
                .setStatus(Status.STATUS_NORMAL);
        if (serviceType > 0) {
            reqBuilder.setServiceTypes(serviceType);
        }
        if (filterBusinessIds != null) {
            reqBuilder.addAllBusinessIds(filterBusinessIds);
        }
        return agreementServiceBlockingStub
                .getAgreementContentListByCompany(reqBuilder.build())
                .getAgreementContentViewList();
    }

    private int toServiceType(String type, Boolean ob) {
        return toServiceType(type) | ((ob != null && ob) ? ServiceType.SERVICE_TYPE_ONLINE_BOOKING_VALUE : 0);
    }

    private static int toServiceType(String type) {
        int v = ServiceType.SERVICE_TYPE_UNSPECIFIED_VALUE;
        if (StringUtils.isBlank(type)) {
            return v;
        }
        String[] types = type.split(",");
        for (String t : types) {
            if (!t.isEmpty()) {
                switch (Integer.parseInt(t)) {
                    case 1:
                        v |= ServiceType.SERVICE_TYPE_BOARDING_VALUE;
                        break;
                    case 2:
                        v |= ServiceType.SERVICE_TYPE_DAYCARE_VALUE;
                        break;
                    case 3:
                        v |= ServiceType.SERVICE_TYPE_GROOMING_VALUE;
                        break;
                    case 4:
                        v |= ServiceType.SERVICE_TYPE_TRAINING_VALUE;
                        break;
                    default:
                        break;
                }
            }
        }

        return v;
    }
}
