package com.moego.api.v3.agreement.service;

import com.moego.api.v3.agreement.converter.AgreementRecordConverter;
import com.moego.idl.api.agreement.v1.SignAgreementParams;
import com.moego.idl.models.agreement.v1.AgreementRecordSimpleView;
import com.moego.idl.service.agreement.v1.AgreementRecordServiceGrpc;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/10/27
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class AgreementRecordService {

    private final AgreementRecordServiceGrpc.AgreementRecordServiceBlockingStub agreementRecordServiceBlockingStub;
    private final AgreementRecordConverter agreementRecordConverter;

    public AgreementRecordSimpleView signAgreement(SignAgreementParams request) {
        return agreementRecordServiceBlockingStub.signAgreement(agreementRecordConverter.toRequest(request));
    }
}
