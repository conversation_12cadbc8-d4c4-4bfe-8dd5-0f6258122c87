package com.moego.api.v3.agreement.converter;

import static org.mapstruct.ReportingPolicy.WARN;

import com.moego.idl.api.agreement.v1.GetAgreementContentListByCompanyParams;
import com.moego.idl.api.agreement.v1.QueryCompanyAgreementInfoListResult;
import com.moego.idl.models.agreement.v1.AgreementModelContentView;
import com.moego.idl.models.agreement.v1.ServiceType;
import com.moego.idl.service.agreement.v1.GetAgreementListByCompanyRequest;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.factory.Mappers;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        unmappedTargetPolicy = WARN,
        unmappedSourcePolicy = WARN)
public interface AgreementConverter {
    AgreementConverter INSTANCE = Mappers.getMapper(AgreementConverter.class);

    GetAgreementListByCompanyRequest toGetAgreementListByCompanyRequest(
            Long companyId, GetAgreementContentListByCompanyParams params);

    List<QueryCompanyAgreementInfoListResult.AgreementInfo> toAgreementInfoList(List<AgreementModelContentView> input);

    // 注意！这个mapstruct的转换方法是手动实现的，加字段不要漏了
    default QueryCompanyAgreementInfoListResult.AgreementInfo toAgreementInfo(AgreementModelContentView element) {
        var builder = QueryCompanyAgreementInfoListResult.AgreementInfo.newBuilder();
        builder.setId(element.getId());
        builder.setAgreementHeader(element.getAgreementTitle());
        builder.setAgreementContent(element.getAgreementContent());

        // if serviceTypes(an bit map) contains SERVICE_TYPE_ONLINE_BOOKING, then set bookOnlineAvailable to 1, else 0
        var obServiceTypeBit = ServiceType.SERVICE_TYPE_ONLINE_BOOKING.getNumber();
        boolean checkServiceType = obServiceTypeBit == (obServiceTypeBit & element.getServiceTypes());
        byte hasBookOnline = checkServiceType ? (byte) 1 : 0;
        builder.setBookOnlineAvailable(hasBookOnline);

        builder.setAgreementRequiredType((byte) (element.getSignedPolicy().getNumber()));
        builder.setCreateTime((int) (element.getCreateTime() / 1000));
        builder.setUpdateTime((int) (element.getLastEditTime() / 1000));
        builder.setBusinessId(element.getBusinessId());
        return builder.build();
    }
}
