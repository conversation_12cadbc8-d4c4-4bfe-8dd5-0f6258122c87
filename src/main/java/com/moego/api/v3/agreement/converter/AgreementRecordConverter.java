package com.moego.api.v3.agreement.converter;

import static org.mapstruct.ReportingPolicy.WARN;

import com.moego.idl.api.agreement.v1.SignAgreementParams;
import com.moego.idl.service.agreement.v1.SignAgreementRequest;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;

/**
 * <AUTHOR>
 * @since 2023/10/27
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        unmappedTargetPolicy = WARN,
        unmappedSourcePolicy = WARN)
public interface AgreementRecordConverter {
    SignAgreementRequest toRequest(SignAgreementParams params);
}
