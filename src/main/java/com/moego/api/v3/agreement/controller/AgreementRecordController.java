/*
 * @since 2023-10-23 17:27:21
 * <AUTHOR> <<EMAIL>>
 */
package com.moego.api.v3.agreement.controller;

import com.moego.api.v3.agreement.service.AgreementRecordService;
import com.moego.idl.api.agreement.v1.AgreementRecordServiceGrpc;
import com.moego.idl.api.agreement.v1.SignAgreementParams;
import com.moego.idl.models.agreement.v1.AgreementRecordSimpleView;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @since 2023/10/27
 */
@GrpcService
@RequiredArgsConstructor
@Slf4j
public class AgreementRecordController extends AgreementRecordServiceGrpc.AgreementRecordServiceImplBase {
    private final AgreementRecordService agreementRecordService;

    @Override
    @Auth(AuthType.ACCOUNT)
    public void signAgreement(SignAgreementParams request, StreamObserver<AgreementRecordSimpleView> responseObserver) {
        var response = agreementRecordService.signAgreement(request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }
}
