/*
 * @since 2024-07-29 16:43:55
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.ai_assistant.controller;

import static com.moego.lib.common.auth.AuthType.BUSINESS;

import com.moego.api.v3.ai_assistant.converter.NaturalLanguageConverter;
import com.moego.idl.api.ai_assistant.v1.DetectLanguageParams;
import com.moego.idl.api.ai_assistant.v1.DetectLanguageResult;
import com.moego.idl.api.ai_assistant.v1.NaturalLanguageServiceGrpc;
import com.moego.idl.service.ai_assistant.v1.NaturalLanguageServiceGrpc.NaturalLanguageServiceBlockingStub;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class NaturalLanguageController extends NaturalLanguageServiceGrpc.NaturalLanguageServiceImplBase {
    private final NaturalLanguageServiceBlockingStub naturalLanguageServiceBlockingStub;
    private final NaturalLanguageConverter naturalLanguageConverter;

    @Override
    @Auth(BUSINESS)
    public void detectLanguage(DetectLanguageParams request, StreamObserver<DetectLanguageResult> responseObserver) {
        final var req = naturalLanguageConverter.detectLanguageRequest(request);
        final var res = naturalLanguageServiceBlockingStub.detectLanguage(req);
        final var result = naturalLanguageConverter.detectLanguageResult(res);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }
}
