/*
 * @since 2023-07-03 10:38:40
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.ai_assistant.controller;

import static com.moego.api.v3.shared.util.PrintUtil.printFieldList;
import static com.moego.api.v3.shared.util.PrintUtil.printName;
import static com.moego.api.v3.shared.util.PrintUtil.printString;
import static com.moego.idl.service.ai_assistant.v1.BusinessConversationServiceGrpc.BusinessConversationServiceBlockingStub;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.server.business.dto.MoeBusinessDto.getBusinessAddress;

import com.moego.api.v3.ai_assistant.converter.BusinessConversationConverter;
import com.moego.idl.api.ai_assistant.v1.AdoptAnswerParams;
import com.moego.idl.api.ai_assistant.v1.AdoptAnswerResult;
import com.moego.idl.api.ai_assistant.v1.AskParams;
import com.moego.idl.api.ai_assistant.v1.AskResult;
import com.moego.idl.api.ai_assistant.v1.BusinessConversationServiceGrpc.BusinessConversationServiceImplBase;
import com.moego.idl.api.ai_assistant.v1.CloseBusinessConversationParams;
import com.moego.idl.api.ai_assistant.v1.CloseBusinessConversationResult;
import com.moego.idl.api.ai_assistant.v1.CreateBusinessConversationParams;
import com.moego.idl.api.ai_assistant.v1.CreateBusinessConversationResult;
import com.moego.idl.api.ai_assistant.v1.RephraseAnswerParams;
import com.moego.idl.api.ai_assistant.v1.RephraseAnswerResult;
import com.moego.idl.api.ai_assistant.v1.SendAnswerParams;
import com.moego.idl.api.ai_assistant.v1.SendAnswerResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.ai_assistant.v1.AdoptAnswerRequest;
import com.moego.idl.service.ai_assistant.v1.AskRequest;
import com.moego.idl.service.ai_assistant.v1.CloseBusinessConversationRequest;
import com.moego.idl.service.ai_assistant.v1.CreateBusinessConversationRequest;
import com.moego.idl.service.ai_assistant.v1.RephraseAnswerRequest;
import com.moego.idl.service.ai_assistant.v1.SendAnswerRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.params.InfoIdParams;
import com.moego.server.business.params.StaffIdParams;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.GroomingCalenderPetInfo;
import io.grpc.stub.StreamObserver;
import java.util.HashMap;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class BusinessConversationController extends BusinessConversationServiceImplBase {

    private final BusinessConversationConverter businessConversationConverter;
    private final BusinessConversationServiceBlockingStub businessConversationServiceBlockingStub;
    private final IBusinessBusinessClient iBusinessBusinessClient;
    private final IBusinessStaffClient iBusinessStaffClient;
    private final ICustomerCustomerClient iCustomerCustomerClient;
    private final IPetClient iPetClient;

    @Override
    @Auth(AuthType.BUSINESS)
    public void createConversation(
            CreateBusinessConversationParams request,
            StreamObserver<CreateBusinessConversationResult> responseObserver) {
        var auth = AuthContext.get();
        var variables = new HashMap<String, String>();
        var business = iBusinessBusinessClient.getBusinessInfo(new InfoIdParams(auth.getBusinessId()));
        var staff = iBusinessStaffClient.getStaff(new StaffIdParams(auth.getBusinessId(), auth.getStaffId()));
        variables.put("businessName", business.getBusinessName());
        variables.put("businessAddress", getBusinessAddress(business));
        variables.put("businessWebsite", printString(business.getWebsite()));
        variables.put("staffName", printName(staff.getFirstName(), staff.getLastName()));
        if (request.getScenarioCase() == CreateBusinessConversationParams.ScenarioCase.TWO_WAY_MESSAGE) {
            var clientId = (int) request.getTwoWayMessage().getCustomerId();
            var client = iCustomerCustomerClient.getCustomerWithDeleted(clientId);
            if (client == null || !client.getCompanyId().equals(auth.companyId())) {
                throw bizException(Code.CODE_PARAMS_ERROR, "Client not found");
            }
            variables.put("clientName", printName(client.getFirstName(), client.getLastName()));
            var pets = iPetClient.getPetNameBreedByCustomerId(clientId, auth.getBusinessId());
            variables.put("petNames", printFieldList(pets, GroomingCalenderPetInfo::getPetName));
            variables.put("petCount", String.valueOf(pets.size()));
        }

        var vo = CreateBusinessConversationRequest.newBuilder()
                .setBusinessId(auth.businessId())
                .setStaffId(auth.staffId())
                .setScenario(request.getScenarioCase().name())
                .setPrompt(request.getPrompt())
                .putAllVariables(variables)
                .build();
        var dto = businessConversationServiceBlockingStub.createConversation(vo);
        var result = CreateBusinessConversationResult.newBuilder()
                .setConversationId(dto.getConversationId())
                .build();
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void closeConversation(
            CloseBusinessConversationParams request, StreamObserver<CloseBusinessConversationResult> responseObserver) {
        var vo = CloseBusinessConversationRequest.newBuilder()
                .setBusinessId(AuthContext.get().businessId())
                .setConversationId(request.getConversationId())
                .build();
        businessConversationServiceBlockingStub.closeConversation(vo);
        responseObserver.onNext(CloseBusinessConversationResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void ask(AskParams request, StreamObserver<AskResult> responseObserver) {
        var vo = AskRequest.newBuilder()
                .setBusinessId(AuthContext.get().businessId())
                .setSenderId(AuthContext.get().staffId())
                .setConversationId(request.getConversationId())
                .setQuestion(request.getQuestion())
                .build();
        var dto = businessConversationServiceBlockingStub.ask(vo);
        var result = AskResult.newBuilder()
                .setQuestionId(dto.getQuestionId())
                .setAnswer(dto.getAnswer())
                .build();
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void rephraseAnswer(RephraseAnswerParams request, StreamObserver<RephraseAnswerResult> responseObserver) {
        var vo = RephraseAnswerRequest.newBuilder()
                .setBusinessId(AuthContext.get().businessId())
                .setConversationId(request.getConversationId())
                .setQuestionId(request.getQuestionId())
                .build();
        var dto = businessConversationServiceBlockingStub.rephraseAnswer(vo);
        var result = RephraseAnswerResult.newBuilder()
                .setQuestionId(dto.getQuestionId())
                .setAnswer(dto.getAnswer())
                .build();
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void adoptAnswer(AdoptAnswerParams request, StreamObserver<AdoptAnswerResult> responseObserver) {
        var vo = AdoptAnswerRequest.newBuilder()
                .setBusinessId(AuthContext.get().businessId())
                .setConversationId(request.getConversationId())
                .setQuestionId(request.getQuestionId())
                .build();
        businessConversationServiceBlockingStub.adoptAnswer(vo);
        responseObserver.onNext(AdoptAnswerResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.BUSINESS)
    public void sendAnswer(SendAnswerParams request, StreamObserver<SendAnswerResult> responseObserver) {
        var vo = SendAnswerRequest.newBuilder()
                .setBusinessId(AuthContext.get().businessId())
                .setConversationId(request.getConversationId())
                .setQuestionId(request.getQuestionId())
                .setMessageId(request.getMessageId())
                .build();
        businessConversationServiceBlockingStub.sendAnswer(vo);
        responseObserver.onNext(SendAnswerResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
