/*
 * @since 2024-07-29 16:45:12
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.ai_assistant.converter;

import com.moego.idl.api.ai_assistant.v1.DetectLanguageParams;
import com.moego.idl.api.ai_assistant.v1.DetectLanguageResult;
import com.moego.idl.service.ai_assistant.v1.DetectLanguageRequest;
import com.moego.idl.service.ai_assistant.v1.DetectLanguageResponse;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public abstract class NaturalLanguageConverter {
    public abstract DetectLanguageRequest detectLanguageRequest(DetectLanguageParams detectLanguageParams);

    public abstract DetectLanguageResult detectLanguageResult(DetectLanguageResponse detectLanguageResponse);
}
