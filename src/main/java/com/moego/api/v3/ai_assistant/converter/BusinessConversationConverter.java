/*
 * @since 2023-07-03 16:58:41
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.ai_assistant.converter;

import com.moego.idl.api.ai_assistant.v1.CreateBusinessConversationParams;
import com.moego.idl.service.ai_assistant.v1.CreateBusinessConversationRequest;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface BusinessConversationConverter {
    CreateBusinessConversationRequest toRequest(CreateBusinessConversationParams params);
}
