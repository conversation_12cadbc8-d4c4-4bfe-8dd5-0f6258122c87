package com.moego.api.v3.map.v1;

import com.google.type.LatLng;
import com.google.type.PostalAddress;
import com.moego.idl.api.map.v1.AutocompleteAddressParams;
import com.moego.idl.api.map.v1.AutocompleteAddressResult;
import com.moego.idl.api.map.v1.GetAddressParams;
import com.moego.idl.api.map.v1.GetAddressResult;
import com.moego.idl.api.map.v1.GetAddressV2Result;
import com.moego.idl.api.map.v1.GetCountriesParams;
import com.moego.idl.api.map.v1.GetCountriesResult;
import com.moego.idl.api.map.v1.GetPostalCodeParams;
import com.moego.idl.api.map.v1.GetPostalCodeResult;
import com.moego.idl.api.map.v1.MapServiceGrpc;
import com.moego.idl.api.map.v1.SearchPostalCodesParams;
import com.moego.idl.api.map.v1.SearchPostalCodesResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.map.v1.Circle;
import com.moego.idl.models.map.v1.LocationBias;
import com.moego.idl.models.map.v1.PostalAddressModel;
import com.moego.idl.service.map.v1.AutocompleteAddressRequest;
import com.moego.idl.service.map.v1.GetAddressRequest;
import com.moego.idl.service.map.v1.GetCountriesRequest;
import com.moego.idl.service.map.v1.GetPostalCodeRequest;
import com.moego.idl.service.map.v1.SearchPostalCodesRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.params.InfoIdParams;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

@Slf4j
@GrpcService
public class MapController extends MapServiceGrpc.MapServiceImplBase {

    private final IBusinessBusinessClient businessClient;
    private final com.moego.idl.service.map.v1.MapServiceGrpc.MapServiceBlockingStub mapClient;

    private final double autocompleteRadius;

    public MapController(
            @Value("${moego.map.autocomplete.radius}") Double radius,
            @Autowired IBusinessBusinessClient businessClient,
            @Autowired com.moego.idl.service.map.v1.MapServiceGrpc.MapServiceBlockingStub mapClient) {
        this.businessClient = businessClient;
        this.mapClient = mapClient;
        this.autocompleteRadius = radius;
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getCountries(GetCountriesParams request, StreamObserver<GetCountriesResult> responseObserver) {
        var response = mapClient.getCountries(to(request));
        var builder = GetCountriesResult.newBuilder();
        if (0 < response.getCountriesCount()) {
            builder.addAllCountries(response.getCountriesList());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getAddress(GetAddressParams request, StreamObserver<GetAddressResult> responseObserver) {
        var response = mapClient.getAddress(to(request));
        if (!response.hasAddress()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "address not found");
        }
        responseObserver.onNext(
                GetAddressResult.newBuilder().setAddress(response.getAddress()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void autocompleteAddress(
            AutocompleteAddressParams request, StreamObserver<AutocompleteAddressResult> responseObserver) {
        var response = mapClient.autocompleteAddress(buildAutocompleteRequest(request));
        var builder = AutocompleteAddressResult.newBuilder();
        if (0 < response.getSuggestionsCount()) {
            builder.addAllSuggestions(response.getSuggestionsList());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getPostalCode(GetPostalCodeParams request, StreamObserver<GetPostalCodeResult> responseObserver) {
        var params = GetPostalCodeRequest.newBuilder()
                .setCountry(request.getCountry())
                .setPostalCode(request.getPostalCode())
                .build();
        var response = mapClient.getPostalCode(params);
        var builder = GetPostalCodeResult.newBuilder();
        if (response.hasRegion()) {
            builder.setRegion(response.getRegion());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void searchPostalCodes(
            SearchPostalCodesParams request, StreamObserver<SearchPostalCodesResult> responseObserver) {
        var params = SearchPostalCodesRequest.newBuilder()
                .setCountry(request.getCountry())
                .setPostalCode(request.getPostalCode())
                .setLimit(request.hasLimit() ? request.getLimit() : 20)
                .build();
        var response = mapClient.searchPostalCodes(params);
        var builder = SearchPostalCodesResult.newBuilder();
        if (0 < response.getRegionsCount()) {
            builder.addAllRegions(response.getRegionsList());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getAddressV2(GetAddressParams request, StreamObserver<GetAddressV2Result> responseObserver) {
        var response = mapClient.getAddress(to(request));
        if (!response.hasAddress()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "address not found");
        }
        var addr = response.getAddress();
        var addressLines = new ArrayList<String>();
        // 从 additional 字段中提取街道地址
        if (addr.hasAdditional() && addr.getAdditional().getFieldsMap().containsKey("displayName")) {
            String streetAddress =
                    addr.getAdditional().getFieldsMap().get("displayName").getStringValue();

            if (!streetAddress.isEmpty()) {
                addressLines.add(streetAddress);
            }
        }

        var postalAddress = PostalAddress.newBuilder()
                .setPostalCode(addr.getPostalCode())
                .setRegionCode(addr.getCountry())
                .addAllAddressLines(addressLines);
        if (addr.hasLevel1()) postalAddress.setAdministrativeArea(addr.getLevel1());
        if (addr.hasLevel2()) postalAddress.setSublocality(addr.getLevel2());
        if (addr.hasLevel3()) postalAddress.setLocality(addr.getLevel3());
        var result = GetAddressV2Result.newBuilder()
                .setAddress(PostalAddressModel.newBuilder()
                        .setId(addr.getId())
                        .setFormattedAddress(addr.getFormattedAddress())
                        .setFormattedLocal(addr.getFormattedLocal())
                        .setBounds(addr.getBounds())
                        .setLatLng(addr.getCoordinate())
                        .addAllLabels(addr.getLabelsList())
                        .setSourceId(addr.getSourceId())
                        .setSourceType(addr.getSourceType())
                        .setCreatedAt(addr.getCreatedAt())
                        .setUpdatedAt(addr.getUpdatedAt())
                        .setAdditional(addr.getAdditional())
                        .setPostalAddress(postalAddress)
                        .build())
                .build();
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    static GetCountriesRequest to(GetCountriesParams params) {
        var builder = GetCountriesRequest.newBuilder();
        if (params.hasCode()) {
            builder.setCode(params.getCode());
        }
        if (params.hasName()) {
            builder.setName(params.getName());
        }
        if (params.hasIndependent()) {
            builder.setIndependent(params.getIndependent());
        }

        return builder.build();
    }

    private AutocompleteAddressRequest buildAutocompleteRequest(AutocompleteAddressParams params) {
        List<String> countries = new ArrayList<>();
        if (0 < params.getCountriesCount()) {
            params.getCountriesList().forEach(country -> {
                if (!country.isBlank()) {
                    countries.add(country);
                }
            });
        }

        var builder = AutocompleteAddressRequest.newBuilder();
        builder.setTerm(params.getTerm());
        if (params.hasOrigin()) {
            builder.setOrigin(params.getOrigin());
        }
        if (params.hasState()) {
            builder.setState(params.getState());
        }
        if (params.hasCounty()) {
            builder.setCounty(params.getCounty());
        }
        if (params.hasDistrict()) {
            builder.setDistrict(params.getDistrict());
        }
        if (params.hasCity()) {
            builder.setCity(params.getCity());
        }
        if (params.hasPostalCode()) {
            builder.setPostalCode(params.getPostalCode());
        }
        if (0 < params.getAddressTypesCount()) {
            builder.addAllAddressTypes(params.getAddressTypesList());
        }
        if (params.hasLocationBias()) {
            builder.setLocationBias(params.getLocationBias());
        }
        if (params.hasLocationRestriction()) {
            builder.setLocationRestriction(params.getLocationRestriction());
        }
        if (params.hasLanguageCode()) {
            builder.setLanguageCode(params.getLanguageCode());
        }
        if (params.hasMaxResults()) {
            builder.setMaxResults(params.getMaxResults());
        }

        if (countries.isEmpty()) {
            if (!params.hasLocationRestriction() && !params.hasLocationBias()) {
                fillRestriction(builder, AuthContext.get().getBusinessId());
            }
        } else {
            builder.addAllCountries(countries);
        }

        return builder.build();
    }

    private void fillRestriction(AutocompleteAddressRequest.Builder builder, Integer businessId) {
        if (businessId == null) {
            return;
        }
        try {
            var infoParams = InfoIdParams.builder().infoId(businessId).build();
            var info = businessClient.getBusinessInfo(infoParams);
            if (info == null) {
                log.warn("business NOT FOUND: " + businessId);
                return;
            }
            var cb = GetCountriesRequest.newBuilder();
            if (StringUtils.hasText(info.getCountryAlpha2Code())) {
                cb.setCode(info.getCountryAlpha2Code());
            } else {
                if (StringUtils.hasText(info.getCountry())) {
                    cb.setName(info.getCountry());
                }
            }
            var countries = mapClient.getCountries(cb.build());
            if (1 == countries.getCountriesCount()
                    && "GB".equalsIgnoreCase(countries.getCountries(0).getAlpha2Code())) {
                builder.addCountries("GB");
            } else {
                if (StringUtils.hasText(info.getAddressLat()) && StringUtils.hasText(info.getAddressLng())) {
                    var lat = Double.parseDouble(info.getAddressLat());
                    var lng = Double.parseDouble(info.getAddressLng());
                    var center = LatLng.newBuilder()
                            .setLatitude(lat)
                            .setLongitude(lng)
                            .build();
                    var circle = Circle.newBuilder()
                            .setCenter(center)
                            .setRadius(autocompleteRadius)
                            .build();
                    var locationBias =
                            LocationBias.newBuilder().setCircle(circle).build();
                    builder.setLocationBias(locationBias);
                }
            }
        } catch (Exception e) {
            log.error("auto fill location for business {} occur EXCEPTION: ", businessId, e);
        }
    }

    static GetAddressRequest to(GetAddressParams params) {
        var builder = GetAddressRequest.newBuilder();
        if (params.hasAddressSource()) {
            builder.setAddressSource(params.getAddressSource());
        }
        if (params.hasCoordinate()) {
            builder.setCoordinate(params.getCoordinate());
        }
        if (params.hasCondition()) {
            builder.setCondition(to(params.getCondition()));
        }
        if (params.hasLanguageCode()) {
            builder.setLanguageCode(params.getLanguageCode());
        }

        return builder.build();
    }

    static GetAddressRequest.CombinedConditions to(GetAddressParams.CombinedConditions params) {
        var builder = GetAddressRequest.CombinedConditions.newBuilder();
        builder.setCountry(params.getCountry());
        if (params.hasAddress()) {
            builder.setAddress(params.getAddress());
        }
        if (params.hasPostalCode()) {
            builder.setPostalCode(params.getPostalCode());
        }
        if (params.hasLabel()) {
            builder.setLabel(params.getLabel());
        }
        if (params.hasBounds()) {
            builder.setBounds(params.getBounds());
        }

        return builder.build();
    }
}
