package com.moego.api.v3.todo.controller;

import static com.moego.idl.service.todo.v1.TodoServiceGrpc.TodoServiceBlockingStub;
import static com.moego.lib.common.auth.AuthType.ACCOUNT;
import static com.moego.lib.common.auth.AuthType.ANONYMOUS;

import com.google.protobuf.Empty;
import com.moego.idl.api.todo.v1.AddTodoRequest;
import com.moego.idl.api.todo.v1.EchoHzParams;
import com.moego.idl.api.todo.v1.EchoHzResult;
import com.moego.idl.api.todo.v1.HelloArkParams;
import com.moego.idl.api.todo.v1.HelloArkResult;
import com.moego.idl.api.todo.v1.HelloBetterParams;
import com.moego.idl.api.todo.v1.HelloBetterResult;
import com.moego.idl.api.todo.v1.HelloBrysonParams;
import com.moego.idl.api.todo.v1.HelloBrysonResult;
import com.moego.idl.api.todo.v1.HelloHarvieParams;
import com.moego.idl.api.todo.v1.HelloHarvieResult;
import com.moego.idl.api.todo.v1.HelloJettServiceResponse;
import com.moego.idl.api.todo.v1.HelloKaiParams;
import com.moego.idl.api.todo.v1.HelloKaiResult;
import com.moego.idl.api.todo.v1.HelloKurokoParams;
import com.moego.idl.api.todo.v1.HelloKurokoResult;
import com.moego.idl.api.todo.v1.HelloPerqinParams;
import com.moego.idl.api.todo.v1.HelloPerqinResult;
import com.moego.idl.api.todo.v1.HelloYueyueParams;
import com.moego.idl.api.todo.v1.HelloYueyueResult;
import com.moego.idl.api.todo.v1.TodoServiceGrpc;
import com.moego.idl.api.todo.v1.UpdateTodoRequest;
import com.moego.idl.models.todo.v1.TodoModel;
import com.moego.idl.models.universal.v1.EntityListModel;
import com.moego.idl.service.todo.v1.EchoHzRequest;
import com.moego.idl.service.todo.v1.EchoHzResponse;
import com.moego.idl.service.todo.v1.HelloArkRequest;
import com.moego.idl.service.todo.v1.HelloArkResponse;
import com.moego.idl.service.todo.v1.HelloBetterRequest;
import com.moego.idl.service.todo.v1.HelloBetterResponse;
import com.moego.idl.service.todo.v1.HelloBrysonRequest;
import com.moego.idl.service.todo.v1.HelloBrysonResponse;
import com.moego.idl.service.todo.v1.HelloHarvieRequest;
import com.moego.idl.service.todo.v1.HelloHarvieResponse;
import com.moego.idl.service.todo.v1.HelloJettResponse;
import com.moego.idl.service.todo.v1.HelloKaiRequest;
import com.moego.idl.service.todo.v1.HelloKaiResponse;
import com.moego.idl.service.todo.v1.HelloKurokoRequest;
import com.moego.idl.service.todo.v1.HelloKurokoResponse;
import com.moego.idl.service.todo.v1.HelloPerqinRequest;
import com.moego.idl.service.todo.v1.HelloPerqinResponse;
import com.moego.idl.service.todo.v1.HelloYueyueRequest;
import com.moego.idl.service.todo.v1.HelloYueyueResponse;
import com.moego.idl.service.todo.v1.ListTodoRequest;
import com.moego.idl.utils.v1.Id;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.proto.ShapeUtils;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class TodoController extends TodoServiceGrpc.TodoServiceImplBase {

    private final TodoServiceBlockingStub todoServiceBlockingStub;

    @Override
    @Auth(ACCOUNT)
    public void addTodo(AddTodoRequest request, StreamObserver<TodoModel> responseObserver) {
        TodoModel todo = todoServiceBlockingStub.addTodo(com.moego.idl.service.todo.v1.AddTodoRequest.newBuilder()
                .setTitle(request.getTitle())
                .setUserId(AuthContext.get().accountId())
                .build());
        responseObserver.onNext(todo);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ACCOUNT)
    public void getTodo(Id request, StreamObserver<TodoModel> responseObserver) {
        var accountId = AuthContext.get().accountId();
        TodoModel todoModel = todoServiceBlockingStub.getTodo(ShapeUtils.ownId(request.getId(), accountId));
        responseObserver.onNext(todoModel);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ACCOUNT)
    public void listTodo(Empty request, StreamObserver<EntityListModel> responseObserver) {
        var accountId = AuthContext.get().accountId();
        EntityListModel entityListModel = todoServiceBlockingStub.listTodo(
                ListTodoRequest.newBuilder().setUserId(accountId).build());
        responseObserver.onNext(entityListModel);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ACCOUNT)
    public void updateTodo(UpdateTodoRequest request, StreamObserver<TodoModel> responseObserver) {
        var accountId = AuthContext.get().accountId();
        TodoModel todoModel =
                todoServiceBlockingStub.updateTodo(com.moego.idl.service.todo.v1.UpdateTodoRequest.newBuilder()
                        .setStatus(request.getStatus())
                        .setId(request.getId())
                        .setTitle(request.getTitle())
                        .setUserId(accountId)
                        .build());
        responseObserver.onNext(todoModel);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ACCOUNT)
    public void deleteTodo(Id request, StreamObserver<Empty> responseObserver) {
        var accountId = AuthContext.get().accountId();
        Empty empty = todoServiceBlockingStub.deleteTodo(ShapeUtils.ownId(request.getId(), accountId));
        responseObserver.onNext(empty);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void helloJett(Empty request, StreamObserver<HelloJettServiceResponse> responseObserver) {
        HelloJettResponse jett =
                todoServiceBlockingStub.helloJett(Empty.newBuilder().build());
        HelloJettServiceResponse jettServiceResponse = HelloJettServiceResponse.newBuilder()
                .setHelloJett(jett.getHelloJett())
                .build();
        responseObserver.onNext(jettServiceResponse);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void echoHz(EchoHzParams request, StreamObserver<EchoHzResult> responseObserver) {
        EchoHzResponse bkRes = todoServiceBlockingStub.echoHz(EchoHzRequest.newBuilder()
                .setId(request.getId())
                .setMsg(request.getMsg())
                .build());
        EchoHzResult echoHzResult = EchoHzResult.newBuilder()
                .setId(bkRes.getId())
                .setMsg(bkRes.getMsg())
                .build();
        responseObserver.onNext(echoHzResult);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void helloArk(HelloArkParams params, StreamObserver<HelloArkResult> resultObServer) {
        HelloArkResponse resp = todoServiceBlockingStub.helloArk(
                HelloArkRequest.newBuilder().setMessage(params.getMessage()).build());
        HelloArkResult.Builder resultBuilder = HelloArkResult.newBuilder();
        resultBuilder.addHistoryMessages(resp.getReply());
        resultObServer.onNext(resultBuilder.build());
        resultObServer.onCompleted();
    }

    // Hello world api by better
    @Override
    @Auth(ANONYMOUS)
    public void helloBetter(HelloBetterParams params, StreamObserver<HelloBetterResult> resultObServer) {
        HelloBetterResponse better = todoServiceBlockingStub.helloBetter(
                HelloBetterRequest.newBuilder().setMessage(params.getMessage()).build());

        HelloBetterResult betterServiceResponse =
                HelloBetterResult.newBuilder().setReply(better.getReply()).build();
        resultObServer.onNext(betterServiceResponse);
        resultObServer.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void helloPerqin(HelloPerqinParams params, StreamObserver<HelloPerqinResult> resultObserver) {
        HelloPerqinRequest request =
                HelloPerqinRequest.newBuilder().setMessage(params.getMessage()).build();
        HelloPerqinResponse response = todoServiceBlockingStub.helloPerqin(request);

        HelloPerqinResult perqinResult =
                HelloPerqinResult.newBuilder().setReply(response.getReply()).build();
        resultObserver.onNext(perqinResult);
        resultObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void helloYueyue(HelloYueyueParams params, StreamObserver<HelloYueyueResult> resultObserver) {
        HelloYueyueRequest request =
                HelloYueyueRequest.newBuilder().setMessage(params.getMessage()).build();
        HelloYueyueResponse response = todoServiceBlockingStub.helloYueyue(request);

        HelloYueyueResult result =
                HelloYueyueResult.newBuilder().setReply(response.getReply()).build();
        resultObserver.onNext(result);
        resultObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void helloKai(HelloKaiParams params, StreamObserver<HelloKaiResult> resultObserver) {
        HelloKaiRequest request =
                HelloKaiRequest.newBuilder().setMessage(params.getMessage()).build();
        HelloKaiResponse response = todoServiceBlockingStub.helloKai(request);

        HelloKaiResult result =
                HelloKaiResult.newBuilder().setReply(response.getReply()).build();
        resultObserver.onNext(result);
        resultObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void helloKuroko(HelloKurokoParams params, StreamObserver<HelloKurokoResult> resultObserver) {

        HelloKurokoRequest request =
                HelloKurokoRequest.newBuilder().setMessage(params.getMessage()).build();
        HelloKurokoResponse response = todoServiceBlockingStub.helloKuroko(request);

        HelloKurokoResult result =
                HelloKurokoResult.newBuilder().setReply(response.getReply()).build();
        resultObserver.onNext(result);
        resultObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void helloBryson(HelloBrysonParams params, StreamObserver<HelloBrysonResult> resultObserver) {

        HelloBrysonRequest request =
                HelloBrysonRequest.newBuilder().setMessage(params.getMessage()).build();
        HelloBrysonResponse response = todoServiceBlockingStub.helloBryson(request);

        HelloBrysonResult result =
                HelloBrysonResult.newBuilder().setReply(response.getReply()).build();
        resultObserver.onNext(result);
        resultObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void helloHarvie(HelloHarvieParams params, StreamObserver<HelloHarvieResult> resultObServer) {
        // parse params
        long id = params.getId();
        String msg = params.getMessage();

        // build rpc req
        HelloHarvieRequest rpcReq =
                HelloHarvieRequest.newBuilder().setMessage(msg).build();

        // send rpc
        HelloHarvieResponse rpcResp = todoServiceBlockingStub.helloHarvie(rpcReq);

        // parse rpc resp
        String reply = rpcResp.getReply();

        // build res and return
        HelloHarvieResult res =
                HelloHarvieResult.newBuilder().setId(id).setReply(reply).build();
        resultObServer.onNext(res);
        resultObServer.onCompleted();
    }
}
