package com.moego.api.v3.enagement.v1;

import com.moego.idl.api.engagement.v1.ConfirmSenderEmailParams;
import com.moego.idl.api.engagement.v1.ConfirmSenderEmailResult;
import com.moego.idl.api.engagement.v1.EmailServiceGrpc;
import com.moego.idl.api.engagement.v1.FetchDNSConfigsParams;
import com.moego.idl.api.engagement.v1.FetchDNSConfigsResult;
import com.moego.idl.api.engagement.v1.GetSenderEmailParams;
import com.moego.idl.api.engagement.v1.GetSenderEmailResult;
import com.moego.idl.api.engagement.v1.SaveSenderEmailParams;
import com.moego.idl.api.engagement.v1.SaveSenderEmailResult;
import com.moego.idl.api.engagement.v1.SendConfirmEmailParams;
import com.moego.idl.api.engagement.v1.SendConfirmEmailResult;
import com.moego.idl.api.engagement.v1.VerifySenderEmailParams;
import com.moego.idl.api.engagement.v1.VerifySenderEmailResult;
import com.moego.idl.service.engagement.v1.ConfirmSenderEmailRequest;
import com.moego.idl.service.engagement.v1.ConfirmSenderEmailResponse;
import com.moego.idl.service.engagement.v1.EmailServiceGrpc.EmailServiceBlockingStub;
import com.moego.idl.service.engagement.v1.FetchDNSConfigsRequest;
import com.moego.idl.service.engagement.v1.FetchDNSConfigsResponse;
import com.moego.idl.service.engagement.v1.GetSenderEmailRequest;
import com.moego.idl.service.engagement.v1.GetSenderEmailResponse;
import com.moego.idl.service.engagement.v1.SaveSenderEmailRequest;
import com.moego.idl.service.engagement.v1.SaveSenderEmailResponse;
import com.moego.idl.service.engagement.v1.SendConfirmEmailRequest;
import com.moego.idl.service.engagement.v1.SendConfirmEmailResponse;
import com.moego.idl.service.engagement.v1.VerifySenderEmailRequest;
import com.moego.idl.service.engagement.v1.VerifySenderEmailResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@AllArgsConstructor
public class EmailController extends EmailServiceGrpc.EmailServiceImplBase {

    private final EmailServiceBlockingStub emailServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void saveSenderEmail(SaveSenderEmailParams request, StreamObserver<SaveSenderEmailResult> responseObserver) {
        SaveSenderEmailResponse resp = emailServiceBlockingStub.saveSenderEmail(SaveSenderEmailRequest.newBuilder()
                .setEmail(request.getEmail())
                .setName(request.getName())
                .setBusinessId(AuthContext.get().businessId())
                .setType(request.getType())
                .build());
        responseObserver.onNext(SaveSenderEmailResult.newBuilder()
                .setSuccess(resp.getSuccess())
                .setError(resp.getError())
                .setSenderEmail(resp.getSenderEmail())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getSenderEmail(GetSenderEmailParams request, StreamObserver<GetSenderEmailResult> responseObserver) {
        GetSenderEmailResponse resp = emailServiceBlockingStub.getSenderEmail(GetSenderEmailRequest.newBuilder()
                .setBusinessId(AuthContext.get().businessId())
                .build());
        responseObserver.onNext(GetSenderEmailResult.newBuilder()
                .setSenderEmail(resp.getSenderEmail())
                .setSenderEmailUsageType(resp.getSenderEmailUsageType())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void sendConfirmEmail(
            SendConfirmEmailParams request, StreamObserver<SendConfirmEmailResult> responseObserver) {
        SendConfirmEmailResponse resp = emailServiceBlockingStub.sendConfirmEmail(SendConfirmEmailRequest.newBuilder()
                .setEmail(request.getEmail())
                .setBusinessId(AuthContext.get().businessId())
                .build());
        responseObserver.onNext(SendConfirmEmailResult.newBuilder()
                .setError(resp.getError())
                .setSuccess(resp.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void confirmSenderEmail(
            ConfirmSenderEmailParams request, StreamObserver<ConfirmSenderEmailResult> responseObserver) {

        ConfirmSenderEmailResponse resp =
                emailServiceBlockingStub.confirmSenderEmail(ConfirmSenderEmailRequest.newBuilder()
                        .setEmail(request.getEmail())
                        .setBusinessId(AuthContext.get().businessId())
                        .setConfirmationCode(request.getConfirmationCode())
                        .build());
        responseObserver.onNext(ConfirmSenderEmailResult.newBuilder()
                .setError(resp.getError())
                .setSuccess(resp.getSuccess())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void fetchDNSConfigs(FetchDNSConfigsParams request, StreamObserver<FetchDNSConfigsResult> responseObserver) {
        FetchDNSConfigsResponse resp = emailServiceBlockingStub.fetchDNSConfigs(FetchDNSConfigsRequest.newBuilder()
                .setBusinessId(AuthContext.get().businessId())
                .setEmail(request.getEmail())
                .build());
        responseObserver.onNext(FetchDNSConfigsResult.newBuilder()
                .addAllDnsRecords(resp.getDnsRecordsList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void verifySenderEmail(
            VerifySenderEmailParams request, StreamObserver<VerifySenderEmailResult> responseObserver) {
        VerifySenderEmailResponse resp =
                emailServiceBlockingStub.verifySenderEmail(VerifySenderEmailRequest.newBuilder()
                        .setEmail(request.getEmail())
                        .setBusinessId(AuthContext.get().businessId())
                        .build());
        responseObserver.onNext(VerifySenderEmailResult.newBuilder()
                .setError(resp.getError())
                .setSuccess(resp.getSuccess())
                .build());
        responseObserver.onCompleted();
    }
}
