package com.moego.api.v3.enagement.v1;

import com.moego.idl.api.engagement.v1.GetSettingParams;
import com.moego.idl.api.engagement.v1.GetSettingResult;
import com.moego.idl.api.engagement.v1.SettingServiceGrpc.SettingServiceImplBase;
import com.moego.idl.api.engagement.v1.TmpCallingSeatsParams;
import com.moego.idl.api.engagement.v1.TmpCallingSeatsResult;
import com.moego.idl.api.engagement.v1.UpdateSettingParams;
import com.moego.idl.api.engagement.v1.UpdateSettingResult;
import com.moego.idl.service.engagement.v1.GetSettingRequest;
import com.moego.idl.service.engagement.v1.GetSettingResponse;
import com.moego.idl.service.engagement.v1.SettingServiceGrpc.SettingServiceBlockingStub;
import com.moego.idl.service.engagement.v1.TmpCallingSeatsRequest;
import com.moego.idl.service.engagement.v1.TmpCallingSeatsResponse;
import com.moego.idl.service.engagement.v1.UpdateSettingRequest;
import com.moego.idl.service.engagement.v1.UpdateSettingResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@AllArgsConstructor
public class SettingController extends SettingServiceImplBase {

    private final SettingServiceBlockingStub settingServiceStub;

    @Auth(AuthType.BUSINESS)
    @Override
    public void getSetting(GetSettingParams request, StreamObserver<GetSettingResult> responseObserver) {
        Long businessID = AuthContext.get().businessId();
        Long companyID = AuthContext.get().companyId();
        GetSettingRequest input = GetSettingRequest.newBuilder()
                .setBusinessId(businessID)
                .setCompanyId(companyID)
                .build();
        GetSettingResponse output = settingServiceStub.getSetting(input);
        responseObserver.onNext(GetSettingResult.newBuilder()
                .setSetting(output.getSetting())
                .setSeatsSetting(output.getSeatsSetting())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.BUSINESS)
    @Override
    public void updateSetting(UpdateSettingParams request, StreamObserver<UpdateSettingResult> responseObserver) {
        Long companyID = AuthContext.get().companyId();
        Long businessID = AuthContext.get().businessId();
        Long staffID = AuthContext.get().staffId();

        UpdateSettingRequest.Builder updateSettingBuilder = UpdateSettingRequest.newBuilder()
                .setCompanyId(companyID)
                .setBusinessId(businessID)
                .setStaffId(staffID)
                .setUpdateSetting(request.getUpdateSetting());

        if (request.hasUpdateSeatsSetting()) {
            updateSettingBuilder.setUpdateSeatsSetting(request.getUpdateSeatsSetting());
        }
        UpdateSettingResponse output = settingServiceStub.updateSetting(updateSettingBuilder.build());

        responseObserver.onNext(UpdateSettingResult.newBuilder()
                .setSetting(output.getSetting())
                .setSeatsSetting(output.getSeatsSetting())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.BUSINESS)
    @Override
    public void getTmpCallingSeats(
            TmpCallingSeatsParams request, StreamObserver<TmpCallingSeatsResult> responseObserver) {
        TmpCallingSeatsResponse output = settingServiceStub.tmpCallingSeats(
                TmpCallingSeatsRequest.newBuilder().build());
        TmpCallingSeatsResult resultBuilder = TmpCallingSeatsResult.newBuilder()
                .addAllTmpCallingSeats(output.getTmpCallingSeatsList())
                .setSeatsLimit(output.getSeatsLimit())
                .setIsSeatsLimitReached(output.getIsSeatsLimitReached())
                .build();
        responseObserver.onNext(resultBuilder);
        responseObserver.onCompleted();
    }
}
