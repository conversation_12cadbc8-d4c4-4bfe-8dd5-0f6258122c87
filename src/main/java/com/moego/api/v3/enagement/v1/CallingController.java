package com.moego.api.v3.enagement.v1;

import com.google.protobuf.Empty;
import com.moego.idl.api.engagement.v1.CallFromBAppParams;
import com.moego.idl.api.engagement.v1.CallingServiceGrpc;
import com.moego.idl.api.engagement.v1.ConfirmUnresolvedRangeParams;
import com.moego.idl.api.engagement.v1.ConfirmUnresolvedRangeResult;
import com.moego.idl.api.engagement.v1.GetCallingDetailParams;
import com.moego.idl.api.engagement.v1.GetCallingDetailResult;
import com.moego.idl.api.engagement.v1.GetCallingLogOverviewParams;
import com.moego.idl.api.engagement.v1.GetCallingLogOverviewResult;
import com.moego.idl.api.engagement.v1.GetCallingLogParams;
import com.moego.idl.api.engagement.v1.GetCallingLogResult;
import com.moego.idl.api.engagement.v1.GetCustomerDialMaskParams;
import com.moego.idl.api.engagement.v1.GetCustomerMaskResult;
import com.moego.idl.api.engagement.v1.GetDefaultLocalPhoneNumberResult;
import com.moego.idl.api.engagement.v1.GetTokenParams;
import com.moego.idl.api.engagement.v1.GetTokenResult;
import com.moego.idl.api.engagement.v1.ListCallingLogsParams;
import com.moego.idl.api.engagement.v1.ListCallingLogsResult;
import com.moego.idl.api.engagement.v1.MarkLogResolveStatusParams;
import com.moego.idl.api.engagement.v1.MarkLogResolveStatusResult;
import com.moego.idl.api.engagement.v1.SearchCustomerParams;
import com.moego.idl.api.engagement.v1.SearchCustomerResult;
import com.moego.idl.models.engagement.v1.CallingLogFilter;
import com.moego.idl.models.engagement.v1.Pet;
import com.moego.idl.service.engagement.v1.CallFromBAppRequest;
import com.moego.idl.service.engagement.v1.CallingServiceGrpc.CallingServiceBlockingStub;
import com.moego.idl.service.engagement.v1.ConfirmUnresolvedRangeRequest;
import com.moego.idl.service.engagement.v1.ConfirmUnresolvedRangeResponse;
import com.moego.idl.service.engagement.v1.GetCallingDetailRequest;
import com.moego.idl.service.engagement.v1.GetCallingLogOverviewRequest;
import com.moego.idl.service.engagement.v1.GetCallingLogOverviewResponse;
import com.moego.idl.service.engagement.v1.GetCallingLogRequest;
import com.moego.idl.service.engagement.v1.GetCallingLogResponse;
import com.moego.idl.service.engagement.v1.GetCustomerDialMaskRequest;
import com.moego.idl.service.engagement.v1.GetTokenRequest;
import com.moego.idl.service.engagement.v1.GetTokenResponse;
import com.moego.idl.service.engagement.v1.ListCallingLogsRequest;
import com.moego.idl.service.engagement.v1.ListCallingLogsResponse;
import com.moego.idl.service.engagement.v1.MarkLogResolveStatusRequest;
import com.moego.idl.service.engagement.v1.SearchClientRequest;
import com.moego.idl.service.engagement.v1.SearchClientResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.CustomerSearchListDto;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@AllArgsConstructor
public class CallingController extends CallingServiceGrpc.CallingServiceImplBase {

    private final CallingServiceBlockingStub callingServiceBlockingStub;

    private final ICustomerCustomerClient customerClient;

    @Auth(AuthType.COMPANY)
    @Override
    public void getToken(Empty request, StreamObserver<GetTokenResult> responseObserver) {
        var svcResponse = callingServiceBlockingStub.getToken(request);
        var apiResponse = GetTokenResult.newBuilder()
                .setToken(svcResponse.getToken())
                .addAllPermissions(svcResponse.getStaffPermissionsList())
                .build();
        responseObserver.onNext(apiResponse);
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void getCallingDetail(
            GetCallingDetailParams request, StreamObserver<GetCallingDetailResult> responseObserver) {
        var svrRequestBuilder = GetCallingDetailRequest.newBuilder();
        if (request.hasCustomerId()) {
            svrRequestBuilder.setCustomerId(request.getCustomerId());
        } else {
            svrRequestBuilder.setClientId(request.getClientId());
        }
        var svcRequest = svrRequestBuilder.build();
        var svcResponse = callingServiceBlockingStub.getCallingDetail(svcRequest);
        var apiResponse = GetCallingDetailResult.newBuilder()
                .setCustomer(svcResponse.getCustomer())
                .addAllPets(svcResponse.getPetsList())
                .setClientId(svcResponse.getClientId())
                .setBusinessId(svcResponse.getBusinessId())
                .setBusinessName(svcResponse.getBusinessName())
                .setCompanyId(svcResponse.getCompanyId())
                .setCompanyName(svcResponse.getCompanyName())
                .setClientName(svcResponse.getClientName())
                .setIsRecording(svcResponse.getIsRecording())
                .build();
        responseObserver.onNext(apiResponse);
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void getCustomerDialMask(
            GetCustomerDialMaskParams request, StreamObserver<GetCustomerMaskResult> responseObserver) {
        var svcRequestBuilder = GetCustomerDialMaskRequest.newBuilder();
        if (request.hasCustomerId()) {
            svcRequestBuilder.setCustomerId(request.getCustomerId());
        } else {
            svcRequestBuilder.setClientId(request.getClientId());
        }
        var svcResponse = callingServiceBlockingStub.getCustomerDialMask(svcRequestBuilder.build());
        responseObserver.onNext(GetCustomerMaskResult.newBuilder()
                .setMask(svcResponse.getMask())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void getCallingLog(GetCallingLogParams request, StreamObserver<GetCallingLogResult> responseObserver) {
        GetCallingLogRequest req =
                GetCallingLogRequest.newBuilder().setId(request.getId()).build();
        GetCallingLogResponse resp = callingServiceBlockingStub.getCallingLog(req);
        responseObserver.onNext(GetCallingLogResult.newBuilder()
                .setCallingLog(resp.getCallingLogView())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void listCallingLogs(ListCallingLogsParams request, StreamObserver<ListCallingLogsResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        ListCallingLogsRequest.Builder builder =
                ListCallingLogsRequest.newBuilder().setCompanyId(companyId);
        if (request.hasFilter()) {
            CallingLogFilter.Builder filterBuilder = CallingLogFilter.newBuilder();
            if (!request.getFilter().getBusinessIdsList().isEmpty()) {
                filterBuilder.addAllBusinessIds(request.getFilter().getBusinessIdsList());
            }
            if (request.getFilter().hasInitTimePeriod()) {
                filterBuilder.setInitTimePeriod(request.getFilter().getInitTimePeriod());
            }
            if (!request.getFilter().getDirectionsList().isEmpty()) {
                filterBuilder.addAllDirections(request.getFilter().getDirectionsList());
            }
            if (!request.getFilter().getClientIdsList().isEmpty()) {
                filterBuilder.addAllClientIds(request.getFilter().getClientIdsList());
            }
            if (!request.getFilter().getCustomerIdsList().isEmpty()) {
                filterBuilder.addAllCustomerIds(request.getFilter().getCustomerIdsList());
            }
            if (!request.getFilter().getCategoriesList().isEmpty()) {
                filterBuilder.addAllCategories(request.getFilter().getCategoriesList());
            }
            if (!request.getFilter().getStatusesList().isEmpty()) {
                filterBuilder.addAllStatuses(request.getFilter().getStatusesList());
            }
            if (!request.getFilter().getRecordTypesList().isEmpty()) {
                filterBuilder.addAllRecordTypes(request.getFilter().getRecordTypesList());
            }
            builder.setLogFilter(filterBuilder.build());
        }
        if (request.hasOrderBy()) {
            builder.setOrderBy(request.getOrderBy());
        }
        if (request.hasPagination()) {
            builder.setPagination(request.getPagination());
        }
        if (request.hasLogFilter()) {
            builder.setLogFilter(request.getLogFilter());
        }

        ListCallingLogsResponse response = callingServiceBlockingStub.listCallingLogs(builder.build());
        responseObserver.onNext(ListCallingLogsResult.newBuilder()
                .addAllCallingLogs(response.getCallingLogViewsList())
                .setPagination(response.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void getCallingLogOverview(
            GetCallingLogOverviewParams request, StreamObserver<GetCallingLogOverviewResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        Long businessId = AuthContext.get().businessId();
        Long staffId = AuthContext.get().staffId();
        GetCallingLogOverviewResult.Builder builder = GetCallingLogOverviewResult.newBuilder();

        GetCallingLogOverviewRequest.Builder reqBuilder = GetCallingLogOverviewRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setStaffId(staffId)
                .setFilter(GetCallingLogOverviewRequest.Filter.newBuilder()
                        .setInitTimePeriod(request.getFilter().getInitTimePeriod()));
        GetCallingLogOverviewResponse callingLogOverview =
                callingServiceBlockingStub.getCallingLogOverview(reqBuilder.build());
        if (callingLogOverview != null) {
            GetCallingLogOverviewResponse.CallReceived callReceived = callingLogOverview.getCallReceived();
            GetCallingLogOverviewResult.CallStatusSummary build =
                    GetCallingLogOverviewResult.CallStatusSummary.newBuilder()
                            .setCallAnswered(callReceived.getCallAnswered())
                            .setCallUnanswered(callReceived.getCallUnanswered())
                            .setCallReceived(callReceived.getCallReceived())
                            .build();
            builder.setCallStatusSummary(build);
            builder.setCallInAfterHour(callingLogOverview.getCallInAfterHour());
            builder.setVoicemailReceived(callingLogOverview.getVoicemailReceived());
            builder.setAverageResponseTime(callingLogOverview.getAverageResponseTime());
            builder.setResolved(callingLogOverview.getResolved());
            builder.setUnresolved(callingLogOverview.getUnresolved());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void searchCustomer(
            SearchCustomerParams request, io.grpc.stub.StreamObserver<SearchCustomerResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        String keyword = request.getKeyword();

        SearchCustomerResult.Builder builder = SearchCustomerResult.newBuilder();

        // 第一步 调用search接口获取customer信息
        CustomerSearchListDto searchCustomer = customerClient.search(companyId, keyword);
        if (searchCustomer != null
                && searchCustomer.getCustomerList() != null
                && !searchCustomer.getCustomerList().isEmpty()) {
            searchCustomer.getCustomerList().forEach(customer -> {
                SearchCustomerResult.Customer.Builder cusBuilder = SearchCustomerResult.Customer.newBuilder()
                        .setId(customer.getCustomerId())
                        .setFirstName(customer.getFirstName())
                        .setLastName(customer.getLastName())
                        .setColorCode(customer.getClientColor())
                        .setAvatarPath(customer.getAvatarPath());

                if (customer.getPetNameList() != null
                        && !customer.getPetNameList().isEmpty()) {
                    customer.getPetNameList().forEach(pet -> {
                        Pet.Builder petBuilder = Pet.newBuilder()
                                .setId(pet.getPetId())
                                .setName(pet.getName())
                                .setBreed(pet.getBreed());
                        cusBuilder.addPets(petBuilder.build());
                    });
                }
                builder.addCustomers(cusBuilder.build());
            });
        }

        // 第二步 调用searchLogs接口获取通话记录
        SearchClientResponse searchClientResponse =
                callingServiceBlockingStub.searchClient(SearchClientRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setKeyword(keyword)
                        .build());

        if (searchClientResponse != null
                && !searchClientResponse.getClientsList().isEmpty()) {
            searchClientResponse.getClientsList().stream()
                    .filter(client -> client.getCustomerId() == 0)
                    .forEach(client -> {
                        SearchCustomerResult.CallingClient.Builder clientBuilder =
                                SearchCustomerResult.CallingClient.newBuilder()
                                        .setId(client.getId())
                                        .setName(client.getName());
                        builder.addCallingClients(clientBuilder.build());
                    });
        }

        // 返回结果并完成响应
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getDefaultLocalPhoneNumber(
            Empty request, StreamObserver<GetDefaultLocalPhoneNumberResult> responseObserver) {
        var svcResponse = callingServiceBlockingStub.getDefaultLocalPhoneNumber(request);
        responseObserver.onNext(GetDefaultLocalPhoneNumberResult.newBuilder()
                .setPhoneNumber(svcResponse.getPhoneNumber())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void callFromBApp(CallFromBAppParams request, StreamObserver<Empty> responseObserver) {
        var svcRequestBuilder = CallFromBAppRequest.newBuilder().setLocalPhoneNumber(request.getLocalPhoneNumber());
        if (request.hasClientId()) {
            svcRequestBuilder.setClientId(request.getClientId());
        } else if (request.hasCustomerId()) {
            svcRequestBuilder.setCustomerId(request.getCustomerId());
        } else if (request.hasPhoneNumber()) {
            svcRequestBuilder.setPhoneNumber(request.getPhoneNumber());
        }
        responseObserver.onNext(callingServiceBlockingStub.callFromBApp(svcRequestBuilder.build()));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getVoipToken(GetTokenParams request, StreamObserver<GetTokenResult> responseObserver) {
        final GetTokenRequest getTokenRequest = GetTokenRequest.newBuilder()
                .setCallingSource(request.getCallingSource())
                .build();
        final GetTokenResponse getTokenResponse = callingServiceBlockingStub.getVoipToken(getTokenRequest);
        final GetTokenResult getTokenResult = GetTokenResult.newBuilder()
                .setToken(getTokenResponse.getToken())
                .addAllPermissions(getTokenResponse.getStaffPermissionsList())
                .build();
        responseObserver.onNext(getTokenResult);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void confirmUnresolvedRange(
            ConfirmUnresolvedRangeParams request, StreamObserver<ConfirmUnresolvedRangeResult> responseObserver) {
        AuthContext authContext = AuthContext.get();
        final ConfirmUnresolvedRangeRequest confirmUnresolvedRangeRequest = ConfirmUnresolvedRangeRequest.newBuilder()
                .setFilter(request.getFilter())
                .setCompanyId(authContext.getCompanyId())
                .build();
        final ConfirmUnresolvedRangeResponse confirmUnresolvedRangeResponse =
                callingServiceBlockingStub.confirmUnresolvedRange(confirmUnresolvedRangeRequest);
        final ConfirmUnresolvedRangeResult confirmUnresolvedRangeResult = ConfirmUnresolvedRangeResult.newBuilder()
                .setUnresolvedCount(confirmUnresolvedRangeResponse.getUnresolvedCount())
                .build();
        responseObserver.onNext(confirmUnresolvedRangeResult);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void markLogResolveStatus(
            MarkLogResolveStatusParams request, StreamObserver<MarkLogResolveStatusResult> responseObserver) {
        AuthContext authContext = AuthContext.get();
        final MarkLogResolveStatusRequest markLogResolveStatusRequest = MarkLogResolveStatusRequest.newBuilder()
                .setFilter(request.getFilter())
                .setCompanyId(authContext.getCompanyId())
                .setIsResolved(request.getIsResolved())
                .build();
        callingServiceBlockingStub.markLogResolveStatus(markLogResolveStatusRequest);
        final MarkLogResolveStatusResult markLogAsResolvedResult =
                MarkLogResolveStatusResult.newBuilder().build();
        responseObserver.onNext(markLogAsResolvedResult);
        responseObserver.onCompleted();
    }
}
