package com.moego.api.v3.appointment.converter;

import com.moego.idl.models.appointment.v1.AppointmentPetFeedingScheduleDef;
import com.moego.idl.models.business_customer.v1.BusinessPetFeedingScheduleDef;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/1/26
 */
@Mapper()
public interface PetFeedingConverter {

    PetFeedingConverter INSTANCE = Mappers.getMapper(PetFeedingConverter.class);

    default BusinessPetFeedingScheduleDef toBusinessPetFeedingScheduleDef(
            Long petId, AppointmentPetFeedingScheduleDef def) {
        return BusinessPetFeedingScheduleDef.newBuilder()
                .setPetId(petId)
                .setFeedingAmount(def.getFeedingAmount())
                .setFeedingUnit(def.getFeedingUnit())
                .setFeedingType(def.getFeedingType())
                .setFeedingSource(def.getFeedingSource())
                .setFeedingInstruction(def.getFeedingInstruction())
                .setFeedingNote(def.getFeedingNote())
                .addAllFeedingTimes(def.getFeedingTimesList())
                .build();
    }
}
