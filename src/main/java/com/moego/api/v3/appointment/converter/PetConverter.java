package com.moego.api.v3.appointment.converter;

import com.moego.idl.models.business_customer.v1.BusinessCustomerPetCalendarView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModelOverview;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface PetConverter {

    PetConverter INSTANCE = Mappers.getMapper(PetConverter.class);

    BusinessCustomerPetModelOverview toOverview(BusinessCustomerPetInfoModel pet);

    BusinessCustomerPetModelOverview toOverview(BusinessCustomerPetModel pet);

    BusinessCustomerPetCalendarView toView(BusinessCustomerPetInfoModel pet);
}
