package com.moego.api.v3.appointment.service;

import com.google.type.Date;
import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.api.v3.appointment.dto.CalendarCardFilterDTO;
import com.moego.api.v3.organization.service.StaffService;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentNoteModel;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.WaitListStatus;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.AppointmentNoteServiceGrpc;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.GetAppointmentNoteListRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentNoteListResponse;
import com.moego.idl.service.appointment.v1.ListAppointmentsRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentsResponse;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.auth.AuthContext;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class AppointmentService {

    public static final List<AppointmentStatus> ACTIVE_STATUS_SET = List.of(
            AppointmentStatus.UNCONFIRMED,
            AppointmentStatus.CONFIRMED,
            AppointmentStatus.READY,
            AppointmentStatus.CHECKED_IN,
            AppointmentStatus.FINISHED);

    public static final List<WaitListStatus> WAIT_LIST_STATUSES_FOR_APPOINTMENT =
            List.of(WaitListStatus.APPTONLY, WaitListStatus.APPTANDWAITLIST);

    private final CompanySettingService companySettingService;
    private final StaffService staffService;

    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;

    private final AppointmentNoteServiceGrpc.AppointmentNoteServiceBlockingStub appointmentNoteService;

    public List<Long> getAppointmentByStaffAndDate(long staffId, Date date, Boolean includeFinished) {
        ListAppointmentsRequest.Filter.Builder builder = ListAppointmentsRequest.Filter.newBuilder()
                .addStaffIds(staffId)
                .addAllStatus(List.of(
                        AppointmentStatus.UNCONFIRMED,
                        AppointmentStatus.CONFIRMED,
                        AppointmentStatus.READY,
                        AppointmentStatus.CHECKED_IN))
                // only support grooming/dog_walking only
                .addServiceTypeIncludes(ServiceItemEnum.GROOMING.getBitValue())
                .addServiceTypeIncludes(ServiceItemEnum.DOG_WALKING.getBitValue())
                .setFilterBookingRequest(true) // pending appointment filter
                .setAppointmentDate(date)
                .setIncludeServiceOperation(true);

        if (includeFinished) {
            builder.addStatus(AppointmentStatus.FINISHED);
        }

        ListAppointmentsRequest.Filter appointmentFilter = builder.build();

        ListAppointmentsResponse listAppointmentsResponse =
                appointmentStub.listAppointments(ListAppointmentsRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .addBusinessIds(AuthContext.get().businessId())
                        .setFilter(appointmentFilter)
                        .build());

        return listAppointmentsResponse.getAppointmentsList().stream()
                .map(AppointmentModel::getId)
                .toList();
    }

    /**
     * ACTIVE STATUS 组合的允许展示在 calendar 上
     * Appointment 的时间跨度限制在 60 天
     * 最多查询 2000 条数据
     *
     * @param cardFilter        calendar card filter
     * @param filterNoStartTime filter no start time
     * @return list of appointments
     */
    public List<AppointmentModel> listCalendarAppointments(
            CalendarCardFilterDTO cardFilter, boolean filterNoStartTime) {
        Long companyId = cardFilter.getCompanyId();
        Long businessId = cardFilter.getBusinessId();
        if (companyId == null || businessId == null) {
            return List.of();
        }
        var timeZoneName = companySettingService.mustGetTimeZoneName(companyId);

        int pageNum = 1;
        int pageSize = 1000;
        var filter = buildCalendarFilter(
                cardFilter.getStartDate(), cardFilter.getEndDate(), filterNoStartTime, timeZoneName);
        var response = appointmentStub.listAppointments(ListAppointmentsRequest.newBuilder()
                .setPagination(
                        PaginationRequest.newBuilder().setPageNum(pageNum).setPageSize(pageSize))
                .setCompanyId(companyId)
                .addBusinessIds(businessId)
                .setFilter(filter)
                .build());
        var appointments = new ArrayList<>(response.getAppointmentsList());
        if (response.getPagination().getTotal() > pageSize) {
            var nextResponse = appointmentStub.listAppointments(ListAppointmentsRequest.newBuilder()
                    .setPagination(
                            PaginationRequest.newBuilder().setPageNum(++pageNum).setPageSize(pageSize))
                    .setCompanyId(companyId)
                    .addBusinessIds(businessId)
                    .setFilter(filter)
                    .build());
            appointments.addAll(nextResponse.getAppointmentsList());
        }
        return appointments;
    }

    public List<AppointmentModel> listAppointmentPrintCardList(
            long companyId,
            long businessId,
            LocalDate date,
            List<ServiceItemType> serviceItemTypes,
            List<AppointmentStatus> statuses) {
        String timezone = companySettingService.mustGetTimeZoneName(companyId);

        var startTime = date.atStartOfDay().atZone(ZoneId.of(timezone));
        var endTime = startTime.plusDays(1);

        List<Integer> serviceTypes = ServiceItemEnum.convertServiceItemListToBitValueList(
                serviceItemTypes.stream().map(ServiceItemType::getNumber).toList());

        int pageNum = 1;
        int pageSize = 1000;

        var filter = ListAppointmentsRequest.Filter.newBuilder()
                .setStartTimeRange(DateTimeConverter.INSTANCE.buildInterval(
                        endTime.minusDays(60).toEpochSecond(), endTime.toEpochSecond() - 1))
                .setEndTimeRange(DateTimeConverter.INSTANCE.buildInterval(
                        startTime.toEpochSecond(), startTime.plusDays(60).toEpochSecond() - 1))
                .addAllStatus(statuses)
                .addAllServiceTypeIncludes(serviceTypes)
                .addAllWaitListStatuses(WAIT_LIST_STATUSES_FOR_APPOINTMENT)
                .setFilterBookingRequest(true)
                .build();
        var request = ListAppointmentsRequest.newBuilder()
                .setCompanyId(companyId)
                .addBusinessIds(businessId)
                .setFilter(filter)
                .setPagination(
                        PaginationRequest.newBuilder().setPageNum(pageNum).setPageSize(pageSize))
                .build();

        return appointmentStub.listAppointments(request).getAppointmentsList();
    }

    public List<AppointmentModel> listBoardingDeparutrePrintCardList(
            long companyId, long businessId, LocalDate endDate, List<AppointmentStatus> statuses) {
        String timezone = companySettingService.mustGetTimeZoneName(companyId);

        var startTime = endDate.atStartOfDay().atZone(ZoneId.of(timezone));
        var endTime = startTime.plusDays(1);

        List<Integer> serviceTypes =
                ServiceItemEnum.convertServiceItemListToBitValueList(List.of(ServiceItemType.BOARDING_VALUE));

        int pageNum = 1;
        int pageSize = 1000;

        var filter = ListAppointmentsRequest.Filter.newBuilder()
                .setStartTimeRange(DateTimeConverter.INSTANCE.buildInterval(
                        endTime.minusDays(60).toEpochSecond(), endTime.toEpochSecond() - 1))
                .setEndTimeRange(DateTimeConverter.INSTANCE.buildInterval(
                        startTime.toEpochSecond(), endTime.toEpochSecond() - 1))
                .addAllStatus(statuses)
                .addAllServiceTypeIncludes(serviceTypes)
                .addAllWaitListStatuses(WAIT_LIST_STATUSES_FOR_APPOINTMENT)
                .setFilterBookingRequest(true)
                .build();
        var request = ListAppointmentsRequest.newBuilder()
                .setCompanyId(companyId)
                .addBusinessIds(businessId)
                .setFilter(filter)
                .setPagination(
                        PaginationRequest.newBuilder().setPageNum(pageNum).setPageSize(pageSize))
                .build();

        return appointmentStub.listAppointments(request).getAppointmentsList();
    }

    public List<AppointmentModel> listBoardingArrivalPrintCardList(
            long companyId, long businessId, LocalDate startDate, List<AppointmentStatus> statuses) {
        String timezone = companySettingService.mustGetTimeZoneName(companyId);

        var startTime = startDate.atStartOfDay().atZone(ZoneId.of(timezone));
        var endTime = startTime.plusDays(1);

        List<Integer> serviceTypes =
                ServiceItemEnum.convertServiceItemListToBitValueList(List.of(ServiceItemType.BOARDING_VALUE));

        int pageNum = 1;
        int pageSize = 1000;

        var filter = ListAppointmentsRequest.Filter.newBuilder()
                .setStartTimeRange(DateTimeConverter.INSTANCE.buildInterval(
                        startTime.toEpochSecond(), endTime.toEpochSecond() - 1))
                .addAllStatus(statuses)
                .addAllServiceTypeIncludes(serviceTypes)
                .addAllWaitListStatuses(WAIT_LIST_STATUSES_FOR_APPOINTMENT)
                .setFilterBookingRequest(true)
                .build();
        var request = ListAppointmentsRequest.newBuilder()
                .setCompanyId(companyId)
                .addBusinessIds(businessId)
                .setFilter(filter)
                .setPagination(
                        PaginationRequest.newBuilder().setPageNum(pageNum).setPageSize(pageSize))
                .build();

        return appointmentStub.listAppointments(request).getAppointmentsList();
    }

    private static ListAppointmentsRequest.Filter buildCalendarFilter(
            Date startFilter, Date endFilter, boolean filterNoStartTime, String timezone) {
        var startDate = DateTimeConverter.INSTANCE.toZonedDateTime(startFilter, timezone);
        var endDate =
                DateTimeConverter.INSTANCE.toZonedDateTime(endFilter, timezone).plusDays(1);
        // 限制查询 60 天，避免查询过多数据导致索引失效
        return ListAppointmentsRequest.Filter.newBuilder()
                .setStartTimeRange(DateTimeConverter.INSTANCE.buildInterval(
                        endDate.minusDays(60).toEpochSecond(), endDate.toEpochSecond() - 1))
                .setEndTimeRange(DateTimeConverter.INSTANCE.buildInterval(
                        startDate.toEpochSecond(), startDate.plusDays(60).toEpochSecond() - 1))
                .addAllStatus(ACTIVE_STATUS_SET)
                .addAllWaitListStatuses(WAIT_LIST_STATUSES_FOR_APPOINTMENT)
                .setFilterNoStartTime(filterNoStartTime)
                .setFilterBookingRequest(true)
                .build();
    }

    public Map<Long, List<AppointmentNoteModel>> getAppointmentNotes(
            long companyId, @NotNull Collection<Long> appointmentIdList) {
        appointmentIdList = appointmentIdList.stream()
                .filter(k -> k != null && k > 0)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(appointmentIdList)) {
            return Map.of();
        }
        GetAppointmentNoteListResponse appointmentNoteListResponse =
                appointmentNoteService.getAppointmentNoteList(GetAppointmentNoteListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllAppointmentIds(appointmentIdList)
                        .build());
        List<AppointmentNoteModel> notesList = new ArrayList<>(appointmentNoteListResponse.getNotesList());
        return notesList.stream().collect(Collectors.groupingBy(AppointmentNoteModel::getGroomingId));
    }
}
