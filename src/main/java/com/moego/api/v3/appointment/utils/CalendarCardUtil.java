package com.moego.api.v3.appointment.utils;

import static com.moego.api.v3.appointment.utils.PetDetailUtil.buildDateTime;
import static com.moego.idl.api.appointment.v1.CalendarCardDraggableInfo.NotResizeReason.INCLUDE_MULTIPLE_SERVICES;
import static com.moego.idl.api.appointment.v1.CalendarCardDraggableInfo.NotResizeReason.NOT_RESIZE_REASON_UNSPECIFIED;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.google.protobuf.Timestamp;
import com.moego.api.v3.appointment.converter.AutoAssignConverter;
import com.moego.api.v3.appointment.converter.PetDetailConverter;
import com.moego.api.v3.appointment.converter.ServiceConverter;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.idl.api.appointment.v1.CalendarCardCustomerInfo;
import com.moego.idl.api.appointment.v1.CalendarCardDraggableInfo;
import com.moego.idl.api.appointment.v1.CalendarCardEvaluationInfo;
import com.moego.idl.api.appointment.v1.CalendarCardPetInfo;
import com.moego.idl.api.appointment.v1.CalendarCardServiceInfo;
import com.moego.idl.api.appointment.v1.CalendarCardView;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.BlockTimeModel;
import com.moego.idl.models.appointment.v1.CalendarCardType;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v1.ServiceOperationModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.GroomingAddOnDetailModel;
import com.moego.lib.utils.model.Pair;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024/9/23
 */
public class CalendarCardUtil {

    private static final Set<CalendarCardType> SERVICE_AND_OPERATIONS =
            Set.of(CalendarCardType.SERVICE, CalendarCardType.OPERATION, CalendarCardType.SERVICE_AND_OPERATION);

    private static final Set<AppointmentStatus> NO_DRAGGABLE_STATUSES =
            Set.of(AppointmentStatus.FINISHED, AppointmentStatus.CANCELED);

    public static final int MINUTES_IN_DAY = 24 * 60;

    public static List<CalendarCardView.Builder> buildCalendarCardsFromBlockTimes(List<BlockTimeModel> blockTimes) {
        if (ObjectUtils.isEmpty(blockTimes)) {
            return List.of();
        }
        return blockTimes.stream()
                .map(block -> CalendarCardView.newBuilder()
                        .setCardType(CalendarCardType.BLOCK)
                        .setAppointmentId(block.getId())
                        .setStaffId(block.getStaffId())
                        .setDate(block.getStartDate())
                        .setStartTime(block.getStartTime())
                        .setEndDate(block.getEndDate())
                        .setEndTime(block.getEndTime())
                        .setRepeatId(block.getRepeatId())
                        .setAppointmentColor(block.getColorCode())
                        .setTicketComments(block.getDescription())
                        .setHasRelatedSplitCards(false)
                        .setDraggableInfo(CalendarCardDraggableInfo.newBuilder()
                                .setDraggable(true)
                                .setResize(true)
                                .build()))
                .toList();
    }

    public static List<CalendarCardView.Builder> buildCalendarCardsFromBookingRequests(
            List<BookingRequestModel> bookingRequests) {
        if (ObjectUtils.isEmpty(bookingRequests)) {
            return List.of();
        }
        return bookingRequests.stream()
                .map(CalendarCardUtil::buildOneCalendarCardFromBookingRequests)
                .flatMap(List::stream)
                .toList();
    }

    public static List<CalendarCardView.Builder> buildOneCalendarCardFromBookingRequests(
            BookingRequestModel obRequest) {

        var groomingServices = obRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasGrooming)
                .map(BookingRequestModel.Service::getGrooming)
                .toList();
        var allPetsStartAtSameTime = getBookingRequestAllPetsStartAtSameTime(groomingServices);

        var builderList = buildCalendarCardsFromBookingRequest(obRequest);
        var listMap = builderList.stream()
                // 2. 将卡片按规则分组
                .collect(Collectors.groupingBy(card -> buildMergeCardKey(card, allPetsStartAtSameTime)));
        var cards = listMap.values().stream()
                // 3. 将同一天同一 staff 的相邻卡片合并
                .map(CalendarCardUtil::mergeSameDateStaffCards)
                .flatMap(List::stream)
                // 4. 相同 staff 相同 start time 的卡片按照 duration 从小到大排序
                .sorted(Comparator.comparing(CalendarCardView.Builder::getStaffId)
                        .thenComparing(CalendarCardView.Builder::getStartTime)
                        .thenComparing(card -> card.getEndTime() - card.getStartTime()))
                .toList();

        // 6. grooming only booking request 需要判断每张卡片是否有相关的分裂卡片
        var groomingOnlyBookingRequest =
                obRequest.getServicesList().stream().allMatch(BookingRequestModel.Service::hasGrooming);
        if (groomingOnlyBookingRequest) {
            cards.forEach(card -> card.setHasRelatedSplitCards(determineHasRelatedSplitCards(card, cards)));
        }

        return cards;
    }

    private static List<CalendarCardView.Builder> buildCalendarCardsFromBookingRequest(
            final BookingRequestModel obRequest) {
        return obRequest.getServicesList().stream()
                .map(service -> switch (service.getServiceCase()) {
                    case GROOMING -> buildGroomingServiceCards(
                            obRequest.getAppointmentId(),
                            obRequest.getId(),
                            obRequest.getCustomerId(),
                            service.getGrooming());
                    case EVALUATION -> buildEvaluationServiceCards(
                            obRequest.getAppointmentId(),
                            obRequest.getId(),
                            obRequest.getCustomerId(),
                            service.getEvaluation());
                    case DOG_WALKING -> buildDogWalkingServiceCards(
                            obRequest.getAppointmentId(),
                            obRequest.getId(),
                            obRequest.getCustomerId(),
                            service.getDogWalking());
                    default -> new ArrayList<CalendarCardView.Builder>();
                })
                .flatMap(List::stream)
                .toList();
    }

    private static List<CalendarCardView.Builder> buildDogWalkingServiceCards(
            Long appointmentId,
            Long bookingRequestId,
            Long customerId,
            BookingRequestModel.DogWalkingService dogWalkingService) {
        var service = dogWalkingService.getService();
        var builder = CalendarCardView.newBuilder()
                .setCardType(CalendarCardType.BOOKING_REQUEST)
                .setAppointmentId(appointmentId)
                .setBookingRequestId(bookingRequestId)
                .setDate(service.getStartDate())
                .setStartTime(service.getStartTime())
                .setEndDate(service.getEndDate())
                .setEndTime(service.getEndTime())
                .setCustomerInfo(CalendarCardCustomerInfo.newBuilder()
                        .setCustomerId(customerId)
                        .build())
                .addPets(CalendarCardPetInfo.newBuilder()
                        .setPetId(service.getPetId())
                        .addServices(ServiceConverter.INSTANCE.toCalendarCard(service)))
                .setAppointmentColor("#000000")
                // Booking request 不可拖拽，不可调整大小
                .setDraggableInfo(CalendarCardDraggableInfo.newBuilder()
                        .setDraggable(false)
                        .setResize(false)
                        .setNotResizeReason(CalendarCardDraggableInfo.NotResizeReason.IS_BOOKING_REQUEST)
                        .build());
        return List.of(builder);
    }

    private static List<CalendarCardView.Builder> buildGroomingServiceCards(
            Long appointmentId,
            Long bookingRequestId,
            Long customerId,
            BookingRequestModel.GroomingService groomingService) {
        var service = groomingService.getService();
        List<CalendarCardServiceInfo> services = new ArrayList<>();
        services.add(ServiceConverter.INSTANCE.toCalendarCard(service));
        groomingService.getAddonsList().forEach(addOn -> services.add(ServiceConverter.INSTANCE.toCalendarCard(addOn)));

        var minStartDateTime = convertLocalDateTime(service.getStartDate(), service.getStartTime());
        for (GroomingAddOnDetailModel addOn : groomingService.getAddonsList()) {
            var addonStartDateTime = convertLocalDateTime(addOn.getStartDate(), addOn.getStartTime());
            if (addonStartDateTime.isBefore(minStartDateTime)) {
                minStartDateTime = addonStartDateTime;
            }
        }

        var maxEndDateTime = convertLocalDateTime(service.getEndDate(), service.getEndTime());
        for (GroomingAddOnDetailModel addOn : groomingService.getAddonsList()) {
            var addonEndDateTime = convertLocalDateTime(addOn.getEndDate(), addOn.getEndTime());
            if (addonEndDateTime.isAfter(maxEndDateTime)) {
                maxEndDateTime = addonEndDateTime;
            }
        }

        var builder = CalendarCardView.newBuilder()
                .setCardType(CalendarCardType.BOOKING_REQUEST)
                .setAppointmentId(appointmentId)
                .setBookingRequestId(bookingRequestId)
                .setDate(minStartDateTime.toLocalDate().toString())
                .setStartTime(minStartDateTime.toLocalTime().toSecondOfDay() / 60)
                .setEndDate(maxEndDateTime.toLocalDate().toString())
                .setEndTime(maxEndDateTime.toLocalTime().toSecondOfDay() / 60)
                .setCustomerInfo(CalendarCardCustomerInfo.newBuilder()
                        .setCustomerId(customerId)
                        .build())
                .addPets(CalendarCardPetInfo.newBuilder()
                        .setPetId(service.getPetId())
                        .addAllServices(services))
                .addAllPetDetailIds(services.stream()
                        .map(CalendarCardServiceInfo::getPetDetailId)
                        .toList())
                .setAppointmentColor("#000000")
                // Booking request 不可拖拽，不可调整大小
                .setDraggableInfo(CalendarCardDraggableInfo.newBuilder()
                        .setDraggable(false)
                        .setResize(false)
                        .setNotResizeReason(CalendarCardDraggableInfo.NotResizeReason.IS_BOOKING_REQUEST)
                        .build())
                .setStaffId(service.getStaffId());
        if (groomingService.hasAutoAssign()) {
            builder.setAutoAssign(AutoAssignConverter.INSTANCE.modelToView(groomingService.getAutoAssign()));
        }
        return List.of(builder);
    }

    private static LocalDateTime convertLocalDateTime(String date, int startOrEndTime) {
        LocalDate baseDate = LocalDate.parse(date);

        // Calculate days and minutes in single operation
        int days = startOrEndTime / 1440;
        int minutes = startOrEndTime % 1440;

        // Extract hours and remaining minutes
        int hours = minutes / 60;
        int remainingMinutes = minutes % 60;

        return baseDate.plusDays(days).atTime(hours, remainingMinutes);
    }

    private static List<CalendarCardView.Builder> buildEvaluationServiceCards(
            Long appointmentId,
            Long bookingRequestId,
            Long customerId,
            BookingRequestModel.EvaluationService evaluationService) {
        var service = evaluationService.getService();
        var builder = CalendarCardView.newBuilder()
                .setCardType(CalendarCardType.BOOKING_REQUEST)
                .setAppointmentId(appointmentId)
                .setBookingRequestId(bookingRequestId)
                .setDate(service.getStartDate())
                .setStartTime(service.getStartTime())
                .setEndDate(service.getEndDate())
                .setEndTime(service.getEndTime())
                .setCustomerInfo(CalendarCardCustomerInfo.newBuilder()
                        .setCustomerId(customerId)
                        .build())
                .addPets(CalendarCardPetInfo.newBuilder()
                        .setPetId(service.getPetId())
                        .addEvaluations(ServiceConverter.INSTANCE.toCalendarCard(service)))
                .setAppointmentColor("#000000")
                // Booking request 不可拖拽，不可调整大小
                .setDraggableInfo(CalendarCardDraggableInfo.newBuilder()
                        .setDraggable(false)
                        .setResize(false)
                        .setNotResizeReason(CalendarCardDraggableInfo.NotResizeReason.IS_BOOKING_REQUEST)
                        .build());
        return List.of(builder);
    }

    private static List<CalendarCardView.Builder> buildServiceCalendarCardsFromBookingRequest(
            final BookingRequestModel obRequest) {

        var builder = CalendarCardView.newBuilder()
                .setCardType(CalendarCardType.BOOKING_REQUEST)
                .setAppointmentId(obRequest.getAppointmentId())
                .setBookingRequestId(obRequest.getId())
                .setDate(obRequest.getStartDate())
                .setStartTime(obRequest.getStartTime())
                .setEndDate(obRequest.getEndDate())
                .setEndTime(obRequest.getEndTime())
                .setCustomerInfo(buildCalendarCardCustomerInfo(obRequest))
                .addAllPets(buildCalendarCardPetInfo(obRequest))
                .setHasRelatedSplitCards(false)
                .setAppointmentColor("#000000")
                // Booking request 不可拖拽，不可调整大小
                .setDraggableInfo(CalendarCardDraggableInfo.newBuilder()
                        .setDraggable(false)
                        .setResize(false)
                        .setNotResizeReason(CalendarCardDraggableInfo.NotResizeReason.IS_BOOKING_REQUEST)
                        .build());
        var grooming = obRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasGrooming)
                .map(BookingRequestModel.Service::getGrooming)
                .findFirst()
                .orElse(null);
        if (grooming != null) {
            builder.setStaffId(grooming.getService().getStaffId());
            if (grooming.hasAutoAssign()) {
                builder.setAutoAssign(AutoAssignConverter.INSTANCE.modelToView(grooming.getAutoAssign()));
            }
        }
        return List.of(builder);
    }

    private static CalendarCardCustomerInfo buildCalendarCardCustomerInfo(BookingRequestModel obRequest) {
        return CalendarCardCustomerInfo.newBuilder()
                .setCustomerId(obRequest.getCustomerId())
                .build();
    }

    private static List<CalendarCardPetInfo> buildCalendarCardPetInfo(BookingRequestModel obRequest) {
        Map<Long, CalendarCardPetInfo.Builder> petMap = new HashMap<>();
        obRequest.getServicesList().forEach(s -> {
            switch (s.getServiceCase()) {
                case GROOMING -> addGroomingService(petMap, s.getGrooming());
                case EVALUATION -> addEvaluationService(petMap, s.getEvaluation());
                case DOG_WALKING -> addDogWalkingService(petMap, s.getDogWalking());
                default -> {}
            }
        });
        return petMap.values().stream().map(CalendarCardPetInfo.Builder::build).toList();
    }

    private static void addDogWalkingService(
            Map<Long, CalendarCardPetInfo.Builder> petMap, BookingRequestModel.DogWalkingService dogWalkingService) {
        var service = dogWalkingService.getService();
        petMap.computeIfAbsent(service.getPetId(), k -> CalendarCardPetInfo.newBuilder()
                        .setPetId((int) service.getPetId()))
                .addServices(ServiceConverter.INSTANCE.toCalendarCard(service));
        // TODO dog walking add on
    }

    private static void addGroomingService(
            Map<Long, CalendarCardPetInfo.Builder> petMap, BookingRequestModel.GroomingService groomingService) {
        var service = groomingService.getService();
        petMap.computeIfAbsent(service.getPetId(), k -> CalendarCardPetInfo.newBuilder()
                        .setPetId((int) service.getPetId()))
                .addServices(ServiceConverter.INSTANCE.toCalendarCard(service));
        groomingService.getAddonsList().forEach(addOn -> petMap.get(addOn.getPetId())
                .addServices(ServiceConverter.INSTANCE.toCalendarCard(addOn)));
    }

    private static void addEvaluationService(
            Map<Long, CalendarCardPetInfo.Builder> petMap, BookingRequestModel.EvaluationService evaluationService) {
        var service = evaluationService.getService();
        petMap.computeIfAbsent(service.getPetId(), k -> CalendarCardPetInfo.newBuilder()
                        .setPetId((int) service.getPetId()))
                .addEvaluations(ServiceConverter.INSTANCE.toCalendarCard(service));
    }

    private static boolean canAccessStaff(long staffId, Set<Long> accessStaffIds) {
        return staffId > 0 && accessStaffIds.contains(staffId);
    }

    private static <T> Map<Long, List<T>> filterByStaffId(
            List<T> items,
            Set<Long> accessStaffIds,
            Function<T, Long> staffIdExtractor,
            Function<T, Long> appointmentIdExtractor) {
        return items.stream()
                .filter(item -> accessStaffIds.contains(staffIdExtractor.apply(item)))
                .collect(Collectors.groupingBy(appointmentIdExtractor));
    }

    public static List<CalendarCardView.Builder> buildCalendarCardsFromAppointments(
            List<AppointmentModel> appointments,
            List<PetDetailModel> petDetails,
            List<EvaluationServiceModel> evaluations,
            List<ServiceOperationModel> operations,
            Set<Long> accessStaffIds) {
        // Filter service details by access staff ids
        var appointmentToPetDetailsMap =
                filterByStaffId(petDetails, accessStaffIds, PetDetailModel::getStaffId, PetDetailModel::getGroomingId);
        var appointmentToEvaluationMap = filterByStaffId(
                evaluations,
                accessStaffIds,
                EvaluationServiceModel::getStaffId,
                EvaluationServiceModel::getAppointmentId);
        var appointmentToOperationsMap = filterByStaffId(
                operations, accessStaffIds, ServiceOperationModel::getStaffId, ServiceOperationModel::getAppointmentId);
        // Filter appointments by access appointment ids
        var accessAppointmentIds = new HashSet<>();
        accessAppointmentIds.addAll(appointmentToPetDetailsMap.keySet());
        accessAppointmentIds.addAll(appointmentToEvaluationMap.keySet());
        accessAppointmentIds.addAll(appointmentToOperationsMap.keySet());
        return appointments.stream()
                .filter(a -> accessAppointmentIds.contains(a.getId()))
                .map(appointment -> buildCalendarCardsFromAppointment(
                        appointment,
                        appointmentToPetDetailsMap.getOrDefault(appointment.getId(), List.of()),
                        appointmentToEvaluationMap.getOrDefault(appointment.getId(), List.of()),
                        appointmentToOperationsMap.getOrDefault(appointment.getId(), List.of())))
                .flatMap(List::stream)
                .toList();
    }

    /**
     * 判断所有宠物的服务是否从同一时间开始
     * 由于存在 service duration 为 0 的情况，所以必须判断至少有两只 pet 的 service duration 不为 0 时才允许返回 true
     * FIXME：Multi pets start at same time 逻辑需要优化，持久化该开关
     *
     * @param petDetails appointment 下的所有 pet details
     * @return all pets start at same time
     */
    private static boolean getAllPetsStartAtSameTime(List<PetDetailModel> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return false;
        }

        // Filter out pets with service duration of 0
        var filteredPetDetails = petDetails.stream()
                .filter(petDetail -> petDetail.getServiceTime() > 0)
                .toList();

        long distinctPetCount = filteredPetDetails.stream()
                .map(PetDetailModel::getPetId)
                .distinct()
                .count();

        if (distinctPetCount < 2) {
            return false;
        }

        return filteredPetDetails.stream()
                        .collect(Collectors.toMap(PetDetailModel::getPetId, PetDetailModel::getStartTime, Integer::min))
                        .values()
                        .stream()
                        .distinct()
                        .count()
                == 1;
    }

    private static boolean getBookingRequestAllPetsStartAtSameTime(
            List<BookingRequestModel.GroomingService> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return false;
        }

        // Filter out pets with service duration of 0
        var filteredPetDetails = petDetails.stream()
                .filter(petDetail -> petDetail.getService().getServiceTime() > 0)
                .toList();

        long distinctPetCount = filteredPetDetails.stream()
                .map(petDetail -> petDetail.getService().getPetId())
                .distinct()
                .count();

        if (distinctPetCount < 2) {
            return false;
        }

        return filteredPetDetails.stream()
                        .collect(Collectors.toMap(
                                petDetail -> petDetail.getService().getPetId(),
                                petDetail -> petDetail.getService().getStartTime(),
                                Integer::min))
                        .values()
                        .stream()
                        .distinct()
                        .count()
                == 1;
    }

    public static List<CalendarCardView.Builder> buildCalendarCardsFromAppointment(
            AppointmentModel appointment,
            List<PetDetailModel> petDetails,
            List<EvaluationServiceModel> evaluations,
            List<ServiceOperationModel> operations) {
        // 聚合 pet details 和 evaluation details 并按每只 pet 的 service start time 排序
        var serviceDetails = aggregateServiceDetails(petDetails, evaluations);
        var allPetsStartAtSameTime = getAllPetsStartAtSameTime(serviceDetails);
        if (ObjectUtils.isEmpty(serviceDetails)) {
            return List.of();
        }
        var serviceOperationMap = Optional.ofNullable(operations).orElse(List.of()).stream()
                .collect(Collectors.groupingBy(ServiceOperationModel::getGroomingServiceId));
        var listMap = serviceDetails.stream()
                // 1. 生成单一类型的卡片
                .flatMap(petDetail -> buildSingleTypeCards(appointment, petDetail, serviceOperationMap).stream())
                // 2. 将卡片按规则分组
                .collect(Collectors.groupingBy(card -> buildMergeCardKey(card, allPetsStartAtSameTime)));
        var cards = listMap.values().stream()
                // 3. 将同一天同一 staff 的相邻卡片合并
                .map(CalendarCardUtil::mergeSameDateStaffCards)
                .flatMap(List::stream)
                // 4. 相同 staff 相同 start time 的卡片按照 duration 从小到大排序
                .sorted(Comparator.comparing(CalendarCardView.Builder::getStaffId)
                        .thenComparing(CalendarCardView.Builder::getStartTime)
                        .thenComparing(card -> card.getEndTime() - card.getStartTime()))
                .toList();

        // 5. 如果是 grooming appointment 且仅有一张卡片，升级为 appointment 卡片
        if (isAppointmentCard(appointment, cards)) {
            var card = cards.get(0);
            var notResizeReason = getNotResizeReason(appointment, card);
            card.setCardType(CalendarCardType.APPOINTMENT)
                    .setHasRelatedSplitCards(false)
                    .setDraggableInfo(CalendarCardDraggableInfo.newBuilder()
                            .setDraggable(isAppointmentDraggable(appointment))
                            .addAllUnavailableStaffList(List.of())
                            .setNotResizeReason(notResizeReason)
                            .setResize(Objects.equals(notResizeReason, NOT_RESIZE_REASON_UNSPECIFIED)));
        }

        // 6. grooming only appointment 需要判断每张卡片是否有相关的分裂卡片
        if (isGroomingOnlyAppointment(appointment)) {
            cards.forEach(card -> card.setHasRelatedSplitCards(determineHasRelatedSplitCards(card, cards)));
        }

        return cards;
    }

    private static boolean isGroomingOnlyAppointment(AppointmentModel appointment) {
        return Objects.equals(appointment.getServiceTypeInclude(), ServiceItemType.GROOMING_VALUE);
    }

    static CalendarCardDraggableInfo.NotResizeReason getNotResizeReason(
            AppointmentModel appointment, CalendarCardView.Builder card) {
        var apptFinished = Objects.equals(appointment.getStatus(), AppointmentStatus.FINISHED);
        if (apptFinished) {
            return CalendarCardDraggableInfo.NotResizeReason.APPOINTMENT_FINISHED;
        }
        var multiPets = card.getPetsCount() > 1;
        if (multiPets) {
            return INCLUDE_MULTIPLE_SERVICES;
        }
        var allServices = card.getPetsList().stream()
                .map(CalendarCardPetInfo::getServicesList)
                .flatMap(List::stream)
                .toList();
        return canResizeWithMultipleServices(allServices);
    }

    private static List<CalendarCardView.Builder> mergeSameDateStaffCards(List<CalendarCardView.Builder> cards) {
        // 1. Group cards by pet
        var petGroupedCards = cards.stream()
                .flatMap(card -> card.getPetsList().stream().map(pet -> Pair.of(pet.getPetId(), card)))
                .collect(Collectors.groupingBy(Pair::key, Collectors.mapping(Pair::value, Collectors.toList())));

        // 2. Merge cards within each pet group by time
        var mergedPetCards = petGroupedCards.values().stream()
                .map(CalendarCardUtil::mergeCardsByTime)
                .flatMap(List::stream)
                .toList();

        // 3. Merge the resulting cards from each pet group
        return mergeCardsByTime(mergedPetCards);
    }

    private static List<CalendarCardView.Builder> mergeCardsByTime(List<CalendarCardView.Builder> cards) {
        // 从上往下依次合并相邻的卡片
        var sortedCards = cards.stream()
                .sorted(Comparator.comparing(CalendarCardView.Builder::getStartTime)
                        // 确保 0 分钟 duration 的卡片在前
                        .thenComparing(CalendarCardView.Builder::getEndTime))
                .toList();

        List<CalendarCardView.Builder> mergedCards = new ArrayList<>();

        CalendarCardView.Builder mergedCard = sortedCards.get(0);
        for (int i = 1; i < sortedCards.size(); i++) {
            CalendarCardView.Builder currentCard = sortedCards.get(i);
            if (isAdjacent(mergedCard, currentCard)) {
                mergedCard = mergeAdjacentCardsWithSameStaff(mergedCard, currentCard);
            } else {
                mergedCards.add(mergedCard);
                mergedCard = currentCard;
            }
        }
        mergedCards.add(mergedCard);

        return mergedCards;
    }

    private static boolean isAdjacent(CalendarCardView.Builder card1, CalendarCardView.Builder card2) {
        return card1.getEndTime() == card2.getStartTime()
                && Objects.equals(card1.getCardType(), CalendarCardType.SERVICE)
                && Objects.equals(card2.getCardType(), CalendarCardType.SERVICE);
    }

    private static boolean isAdjacentForBookingRequest(CalendarCardView.Builder card1, CalendarCardView.Builder card2) {
        return card1.getEndTime() == card2.getStartTime()
                && Objects.equals(card1.getCardType(), CalendarCardType.BOOKING_REQUEST)
                && Objects.equals(card2.getCardType(), CalendarCardType.BOOKING_REQUEST);
    }

    private static boolean determineHasRelatedSplitCards(
            CalendarCardView.Builder currentCard, List<CalendarCardView.Builder> allCards) {
        int cardCount = allCards.size();
        if (cardCount == 1) {
            return false;
        }
        // 当前卡片包含了其他卡片的所有 pet details 时，不需要拆分
        return !allCards.stream().allMatch(card -> new HashSet<>(currentCard.getPetDetailIdsList())
                .containsAll(card.getPetDetailIdsList()));
    }

    private static List<PetDetailModel> aggregateServiceDetails(
            List<PetDetailModel> petDetails, List<EvaluationServiceModel> evaluations) {
        if (ObjectUtils.isEmpty(petDetails) && ObjectUtils.isEmpty(evaluations)) {
            return List.of();
        }
        return Stream.of(petDetails, convertEvaluationsToPetDetails(evaluations))
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    private static List<PetDetailModel> convertEvaluationsToPetDetails(List<EvaluationServiceModel> evaluations) {
        if (ObjectUtils.isEmpty(evaluations)) {
            return List.of();
        }
        return evaluations.stream()
                .map(evaluation -> PetDetailConverter.INSTANCE.evaluationToPetDetail(evaluation).toBuilder()
                        .setServiceItemType(ServiceItemType.EVALUATION)
                        .build())
                .toList();
    }

    private static List<CalendarCardView.Builder> buildSingleTypeCards(
            AppointmentModel appointment,
            PetDetailModel petDetail,
            Map<Long, List<ServiceOperationModel>> serviceOperationMap) {
        boolean isMultiStaffMode = petDetail.getEnableOperation() && serviceOperationMap.containsKey(petDetail.getId());
        if (isMultiStaffMode) {
            var operations = serviceOperationMap.get(petDetail.getId());
            var multiStaffIds =
                    operations.stream().map(ServiceOperationModel::getStaffId).collect(Collectors.toSet());
            return operations.stream()
                    .map(operation -> buildOperationCards(appointment, petDetail, operation, multiStaffIds))
                    .flatMap(List::stream)
                    .toList();
        } else {
            return buildServiceCards(appointment, petDetail);
        }
    }

    private static List<CalendarCardView.Builder> buildServiceCards(
            AppointmentModel appointment, PetDetailModel petDetail) {
        var card = CalendarCardView.newBuilder()
                .setCardType(CalendarCardType.SERVICE)
                .setAppointmentStatus(getCompatibleAppointmentStatus(appointment))
                .setAppointmentId(appointment.getId())
                .setStaffId(petDetail.getStaffId())
                .setDate(petDetail.getStartDate())
                .setEndDate(petDetail.getEndDate())
                .setStartTime(petDetail.getStartTime())
                .setEndTime(petDetail.getEndTime())
                .setPaymentStatus(appointment.getIsPaid())
                .setRepeatId(appointment.getRepeatId())
                .setCustomerInfo(CalendarCardCustomerInfo.newBuilder()
                        .setCustomerId(appointment.getCustomerId())
                        .build())
                .addPets(buildCalendarCardPetInfo(petDetail))
                .addPetDetailIds(petDetail.getId())
                .setDraggableInfo(CalendarCardDraggableInfo.newBuilder()
                        .setDraggable(isAppointmentDraggable(appointment))
                        .setResize(true)
                        .setEarliestDate(appointment.getAppointmentDate())
                        .setLatestDate(appointment.getAppointmentEndDate())
                        .addAllUnavailableStaffList(List.of())
                        .build())
                .addServiceItemTypes(petDetail.getServiceItemType())
                .setAppointmentColor(appointment.getColorCode());
        return splitCardByDay(card);
    }

    private static List<CalendarCardView.Builder> buildOperationCards(
            AppointmentModel appointment,
            PetDetailModel petDetail,
            ServiceOperationModel operation,
            Set<Long> multiStaffIds) {
        var card = CalendarCardView.newBuilder()
                .setCardType(CalendarCardType.OPERATION)
                .setAppointmentStatus(getCompatibleAppointmentStatus(appointment))
                .setAppointmentId(appointment.getId())
                .setStaffId(operation.getStaffId())
                .setDate(petDetail.getStartDate())
                .setEndDate(petDetail.getStartDate())
                .setStartTime(operation.getStartTime())
                .setEndTime(operation.getStartTime() + operation.getDuration())
                .setPaymentStatus(appointment.getIsPaid())
                .setRepeatId(appointment.getRepeatId())
                .setCustomerInfo(CalendarCardCustomerInfo.newBuilder()
                        .setCustomerId(appointment.getCustomerId())
                        .build())
                .addPets(buildCalendarCardPetInfoForOperation(petDetail, operation))
                .addPetDetailIds(petDetail.getId())
                .setDraggableInfo(CalendarCardDraggableInfo.newBuilder()
                        .setDraggable(isAppointmentDraggable(appointment))
                        .setResize(false)
                        .setNotResizeReason(INCLUDE_MULTIPLE_SERVICES)
                        .setEarliestDate(petDetail.getStartDate())
                        .setLatestDate(petDetail.getStartDate())
                        .addAllUnavailableStaffList(multiStaffIds.stream()
                                .filter(staffId -> !staffId.equals(operation.getStaffId()))
                                .toList())
                        .build())
                .addServiceItemTypes(petDetail.getServiceItemType())
                .setAppointmentColor(appointment.getColorCode());
        return splitCardByDay(card);
    }

    private static boolean isAppointmentDraggable(AppointmentModel appointment) {
        return !NO_DRAGGABLE_STATUSES.contains(appointment.getStatus());
    }

    private static CalendarCardPetInfo.Builder buildCalendarCardPetInfo(PetDetailModel petDetail) {
        var petBuilder = CalendarCardPetInfo.newBuilder().setPetId((int) petDetail.getPetId());
        if (Objects.equals(petDetail.getServiceItemType(), ServiceItemType.EVALUATION)) {
            petBuilder.addEvaluations(CalendarCardEvaluationInfo.newBuilder()
                    .setEvaluationDetailId(petDetail.getId())
                    .setServiceId(petDetail.getServiceId())
                    .setServiceTime(petDetail.getServiceTime())
                    .setServicePrice(petDetail.getServicePrice())
                    .build());
        } else {
            petBuilder.addServices(CalendarCardServiceInfo.newBuilder()
                    .setPetDetailId((int) petDetail.getId())
                    .setServiceId(petDetail.getServiceId())
                    .setServiceTime(petDetail.getServiceTime())
                    .setServicePrice(petDetail.getServicePrice())
                    .setStartTime(petDetail.getStartTime())
                    .setEndTime(petDetail.getEndTime())
                    .build());
        }
        return petBuilder;
    }

    private static CalendarCardPetInfo.Builder buildCalendarCardPetInfoForOperation(
            PetDetailModel petDetail, final ServiceOperationModel operation) {
        return CalendarCardPetInfo.newBuilder()
                .setPetId((int) petDetail.getPetId())
                .addServices(CalendarCardServiceInfo.newBuilder()
                        .setPetDetailId((int) petDetail.getId())
                        .setServiceId(petDetail.getServiceId())
                        .setServiceTime(petDetail.getServiceTime())
                        .setServicePrice(petDetail.getServicePrice())
                        .setStartTime(operation.getStartTime())
                        .setEndTime(operation.getStartTime() + operation.getDuration())
                        .build());
    }

    private static String buildMergeCardKey(CalendarCardView.Builder card, boolean allPetsStartAtSameTime) {
        if (allPetsStartAtSameTime) {
            return String.format(
                    "%s-%s-%s",
                    card.getDate(),
                    card.getStaffId(),
                    card.getPetsList().stream()
                            .map(CalendarCardPetInfo::getPetId)
                            .toList());
        }
        return String.format("%s-%s", card.getDate(), card.getStaffId());
    }

    private static AppointmentStatus getCompatibleAppointmentStatus(AppointmentModel appointment) {
        return Set.of(AppointmentStatus.UNCONFIRMED, AppointmentStatus.CONFIRMED)
                                .contains(appointment.getStatus())
                        && !Objects.equals(appointment.getCheckInTime(), Timestamp.getDefaultInstance())
                ? AppointmentStatus.CHECKED_IN
                : appointment.getStatus();
    }

    private static CalendarCardView.Builder mergeAdjacentCardsWithSameStaff(
            CalendarCardView.Builder oldCard, CalendarCardView.Builder newCard) {
        if (!Objects.equals(oldCard.getAppointmentId(), newCard.getAppointmentId())
                || !Objects.equals(oldCard.getStaffId(), newCard.getStaffId())) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Appointment id or staff id not match");
        }
        var mergedCardType = buildMergedCardType(oldCard, newCard);
        var period = buildMergedPeriod(oldCard, newCard);
        var mergedPets = buildMergedPetInfo(oldCard, newCard);
        var mergedDraggable = buildMergedDraggableInfo(oldCard, newCard);
        var mergedServiceItemTypes = Stream.of(oldCard.getServiceItemTypesList(), newCard.getServiceItemTypesList())
                .flatMap(List::stream)
                .distinct()
                .toList();
        var mergedPetDetailIds = mergedPets.stream()
                .flatMap(pet -> pet.getServicesList().stream())
                .map(CalendarCardServiceInfo::getPetDetailId)
                .distinct()
                .toList();
        var builder = CalendarCardView.newBuilder()
                .setCardType(mergedCardType)
                .setAppointmentId(oldCard.getAppointmentId())
                .setStaffId(oldCard.getStaffId())
                .setDate(period.key().toLocalDate().toString())
                .setStartTime(period.key().toLocalTime().toSecondOfDay() / 60)
                .setEndDate(period.value().toLocalDate().toString())
                .setEndTime(period.value().toLocalTime().toSecondOfDay() / 60)
                .addAllPets(mergedPets)
                .addAllPetDetailIds(mergedPetDetailIds)
                .setDraggableInfo(mergedDraggable)
                .addAllServiceItemTypes(mergedServiceItemTypes)
                .setAppointmentColor(oldCard.getAppointmentColor());
        if (oldCard.hasAppointmentStatus()) {
            builder.setAppointmentStatus(oldCard.getAppointmentStatus());
        }
        if (oldCard.hasBookingRequestId()) {
            builder.setBookingRequestId(oldCard.getBookingRequestId());
        }
        if (oldCard.hasPaymentStatus()) {
            builder.setPaymentStatus(oldCard.getPaymentStatus());
        }
        if (oldCard.hasRepeatId()) {
            builder.setRepeatId(oldCard.getRepeatId());
        }
        if (oldCard.hasCustomerInfo()) {
            builder.setCustomerInfo(oldCard.getCustomerInfo());
        }
        return builder;
    }

    private static CalendarCardType buildMergedCardType(
            CalendarCardView.Builder oldCard, CalendarCardView.Builder newCard) {
        if (Objects.equals(oldCard.getCardType(), newCard.getCardType())) {
            return oldCard.getCardType();
        }
        if (SERVICE_AND_OPERATIONS.contains(oldCard.getCardType())
                && SERVICE_AND_OPERATIONS.contains(newCard.getCardType())) {
            return CalendarCardType.SERVICE_AND_OPERATION;
        }
        throw bizException(Code.CODE_PARAMS_ERROR, "Card types are not compatible for merging");
    }

    private static List<CalendarCardPetInfo> buildMergedPetInfo(
            CalendarCardView.Builder oldCard, CalendarCardView.Builder newCard) {
        var petMap = new HashMap<Long, CalendarCardPetInfo.Builder>();
        oldCard.getPetsList().forEach(pet -> petMap.put(pet.getPetId(), pet.toBuilder()));
        newCard.getPetsList().forEach(pet -> {
            if (petMap.containsKey(pet.getPetId())) {
                petMap.get(pet.getPetId()).addAllServices(pet.getServicesList());
                petMap.get(pet.getPetId()).addAllEvaluations(pet.getEvaluationsList());
            } else {
                petMap.put(pet.getPetId(), pet.toBuilder());
            }
        });
        return petMap.values().stream().map(CalendarCardPetInfo.Builder::build).toList();
    }

    private static CalendarCardDraggableInfo buildMergedDraggableInfo(
            CalendarCardView.Builder oldCard, CalendarCardView.Builder newCard) {
        var oldDraggable = oldCard.getDraggableInfo();
        var newDraggable = newCard.getDraggableInfo();
        var mergedDraggable = oldDraggable.getDraggable() && newDraggable.getDraggable();
        var builder = CalendarCardDraggableInfo.newBuilder().setDraggable(mergedDraggable);
        if (oldDraggable.hasEarliestDate() && newDraggable.hasEarliestDate()) {
            String mergedEarliestDate = oldDraggable.getEarliestDate().compareTo(newDraggable.getEarliestDate()) > 0
                    ? oldDraggable.getEarliestDate()
                    : newDraggable.getEarliestDate();
            builder.setEarliestDate(mergedEarliestDate);
        }
        if (oldDraggable.hasLatestDate() && newDraggable.hasLatestDate()) {
            String mergedLatestDate = oldDraggable.getLatestDate().compareTo(newDraggable.getLatestDate()) > 0
                    ? newDraggable.getLatestDate()
                    : oldDraggable.getLatestDate();
            builder.setLatestDate(mergedLatestDate);
        }
        var mergedUnavailableStaffs = Stream.of(
                        oldDraggable.getUnavailableStaffListList(), newDraggable.getUnavailableStaffListList())
                .flatMap(List::stream)
                .distinct()
                .toList();
        if (!CollectionUtils.isEmpty(mergedUnavailableStaffs)) {
            builder.addAllUnavailableStaffList(mergedUnavailableStaffs);
        }
        var mergedServices = Stream.concat(
                        getAllServicesFromCard(oldCard).stream(), getAllServicesFromCard(newCard).stream())
                .toList();
        var notResizeReason = canResizeWithMultipleServices(mergedServices);
        if (notResizeReason != NOT_RESIZE_REASON_UNSPECIFIED) {
            builder.setResize(false).setNotResizeReason(notResizeReason);
        } else {
            builder.setResize(true);
        }

        return builder.build();
    }

    /**
     * 判断合并后的日历卡片是否可以调整大小
     * 规则：
     * 1. 如果只有一个服务，可以拉伸
     * 2. 如果有且仅有一个非零时长服务和其他服务，可以拉伸
     * 3. 如果有且仅有一个零时长服务没有其他服务，可以拉伸
     *
     * @param allServices 所有服务
     * @return 不能调整大小的原因，如果可以调整大小则返回null
     */
    static CalendarCardDraggableInfo.NotResizeReason canResizeWithMultipleServices(
            List<CalendarCardServiceInfo> allServices) {
        if (allServices.isEmpty()) {
            return NOT_RESIZE_REASON_UNSPECIFIED;
        }

        // Rule 1
        if (allServices.size() == 1) {
            return NOT_RESIZE_REASON_UNSPECIFIED;
        }

        // Rule 2
        long nonZeroServiceCount = allServices.stream()
                .filter(service -> service.getServiceTime() > 0)
                .count();
        if (nonZeroServiceCount == 1) {
            return NOT_RESIZE_REASON_UNSPECIFIED;
        }

        // Rule 3
        long zeroServiceCount = allServices.stream()
                .filter(service -> service.getServiceTime() == 0)
                .count();
        if (nonZeroServiceCount == 0 && zeroServiceCount == 1) {
            return NOT_RESIZE_REASON_UNSPECIFIED;
        }

        // 其他情况不能调整大小
        return INCLUDE_MULTIPLE_SERVICES;
    }

    private static List<CalendarCardServiceInfo> getAllServicesFromCard(CalendarCardView.Builder card) {
        return card.getPetsList().stream()
                .map(CalendarCardPetInfo::getServicesList)
                .flatMap(List::stream)
                .toList();
    }

    private static Pair<LocalDateTime, LocalDateTime> buildMergedPeriod(
            CalendarCardView.Builder oldCard, CalendarCardView.Builder newCard) {
        LocalDateTime oldStart = buildDateTime(oldCard.getDate(), oldCard.getStartTime());
        LocalDateTime newStart = buildDateTime(newCard.getDate(), newCard.getStartTime());
        LocalDateTime oldEnd = buildDateTime(oldCard.getEndDate(), oldCard.getEndTime());
        LocalDateTime newEnd = buildDateTime(newCard.getEndDate(), newCard.getEndTime());
        return Pair.of(oldStart.isBefore(newStart) ? oldStart : newStart, oldEnd.isAfter(newEnd) ? oldEnd : newEnd);
    }

    private static boolean isAppointmentCard(AppointmentModel appointment, List<CalendarCardView.Builder> cards) {
        boolean groomingOnly = !ServiceItemEnum.BOARDING.isIncludedIn(appointment.getServiceTypeInclude())
                && !ServiceItemEnum.DAYCARE.isIncludedIn(appointment.getServiceTypeInclude());
        return cards.size() == 1 && groomingOnly;
    }

    static List<CalendarCardView.Builder> splitCardByDay(CalendarCardView.Builder card) {
        if (card.getDate().equals(card.getEndDate()) && card.getEndTime() <= MINUTES_IN_DAY) {
            return List.of(card);
        }
        var startDate = LocalDate.parse(card.getDate());
        var endDate = LocalDate.parse(card.getEndDate());

        if (startDate.isEqual(endDate)) {
            return splitCardBySameDay(card, startDate);
        } else {
            return splitCardByDifferentDay(card, startDate, endDate);
        }
    }

    private static List<CalendarCardView.Builder> splitCardBySameDay(
            CalendarCardView.Builder card, LocalDate startDate) {
        var startTime = card.getStartTime();
        var endTime = card.getEndTime();
        List<CalendarCardView.Builder> splitCards = new ArrayList<>();

        while (startTime >= MINUTES_IN_DAY) {
            startTime -= MINUTES_IN_DAY;
            endTime -= MINUTES_IN_DAY;
            startDate = startDate.plusDays(1);
        }

        while (endTime > MINUTES_IN_DAY) {
            splitCards.add(createCard(card, startDate, startTime, MINUTES_IN_DAY));
            startDate = startDate.plusDays(1);
            startTime = 0;
            endTime -= MINUTES_IN_DAY;
        }
        if (endTime > 0) {
            splitCards.add(createCard(card, startDate, startTime, endTime));
        }

        return splitCards;
    }

    private static List<CalendarCardView.Builder> splitCardByDifferentDay(
            CalendarCardView.Builder card, LocalDate startDate, LocalDate endDate) {
        var startTime = card.getStartTime();
        var endTime = card.getEndTime();
        List<CalendarCardView.Builder> splitCards = new ArrayList<>();

        splitCards.add(createCard(card, startDate, startTime, MINUTES_IN_DAY));
        startDate = startDate.plusDays(1);

        while (startDate.isBefore(endDate)) {
            splitCards.add(createCard(card, startDate, 0, MINUTES_IN_DAY));
            startDate = startDate.plusDays(1);
        }

        startTime = 0;
        while (endTime > MINUTES_IN_DAY) {
            splitCards.add(createCard(card, startDate, startTime, MINUTES_IN_DAY));
            startDate = startDate.plusDays(1);
            endTime -= MINUTES_IN_DAY;
        }
        if (endTime > 0) {
            splitCards.add(createCard(card, startDate, startTime, endTime));
        }

        return splitCards;
    }

    private static CalendarCardView.Builder createCard(
            CalendarCardView.Builder card, LocalDate date, int startTime, int endTime) {
        return card.build().toBuilder()
                .setDate(date.toString())
                .setStartTime(startTime)
                .setEndDate(date.toString())
                .setEndTime(endTime);
    }
}
