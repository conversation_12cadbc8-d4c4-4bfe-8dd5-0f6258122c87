package com.moego.api.v3.appointment.converter;

import com.moego.idl.models.organization.v1.LimitationGroup;
import com.moego.idl.models.organization.v1.LimitationGroupHitView;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface LimitationGroupConverter {

    LimitationGroupConverter INSTANCE = Mappers.getMapper(LimitationGroupConverter.class);

    LimitationGroupHitView convertLimitationGroupToHitView(LimitationGroup limitationGroup);

    List<LimitationGroupHitView> convertLimitationGroupListToHitViewList(List<LimitationGroup> limitationGroupList);
}
