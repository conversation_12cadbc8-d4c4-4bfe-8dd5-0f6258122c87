package com.moego.api.v3.appointment.utils;

import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.event_bus.v1.EventType;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.lib.common.exception.ExceptionUtil;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/12/10
 */
public class OrderEnumUtil {

    private static final String FULFILMENT_STATUS_INIT = "unfulfilled";

    /**
     * 为了兼容common 包中的 OrderSourceType : noshow (不是枚举中定义的 NO_SHOW）
     * @param orderSourceType string name from DB
     * @return OrderSourceType
     */
    public static OrderSourceType convert2EnumSourceType(String orderSourceType) {
        if (!StringUtils.hasLength(orderSourceType)) {
            return OrderSourceType.UNRECOGNIZED;
        }

        if (InvoiceStatusEnum.TYPE_NOSHOW.equalsIgnoreCase(orderSourceType)) {
            return OrderSourceType.NO_SHOW;
        }
        return OrderSourceType.valueOf(orderSourceType.toUpperCase());
    }

    public static OrderModel.FulfillmentStatus convert2EnumFulfilmentStatus(String fulfilmentStatus) {
        if (!StringUtils.hasLength(fulfilmentStatus) || FULFILMENT_STATUS_INIT.equalsIgnoreCase(fulfilmentStatus)) {
            return OrderModel.FulfillmentStatus.INIT;
        }

        return OrderModel.FulfillmentStatus.valueOf(fulfilmentStatus.toUpperCase());
    }

    /**
     * order status 是否为终态： 已完成、已取消/已删除
     */
    public static boolean isFinalStatus(Integer status) {
        return InvoiceStatusEnum.INVOICE_STATUS_COMPLETED.equals(status)
                || InvoiceStatusEnum.INVOICE_STATUS_REMOVED.equals(status);
    }

    public static EventType getEventTypeByOrderStatus(Integer status) {
        if (InvoiceStatusEnum.INVOICE_STATUS_COMPLETED.equals(status)) {
            return EventType.ORDER_COMPLETED;
        } else if (InvoiceStatusEnum.INVOICE_STATUS_REMOVED.equals(status)) {
            return EventType.ORDER_CANCELED;
        } else if (InvoiceStatusEnum.INVOICE_STATUS_CREATED.equals(status)) {
            return EventType.ORDER_CREATED;
        } else if (InvoiceStatusEnum.INVOICE_STATUS_PROCESSING.equals(status)) {
            return EventType.ORDER_CREATED;
        }

        throw ExceptionUtil.bizException(Code.CODE_INVOICE_INVALID_STATUS, "invalid order status");
    }
}
