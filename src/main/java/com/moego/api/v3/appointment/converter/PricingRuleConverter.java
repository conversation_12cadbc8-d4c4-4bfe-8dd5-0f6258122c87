package com.moego.api.v3.appointment.converter;

import com.moego.idl.models.appointment.v2.PricingRuleApplyLogDrawerView;
import com.moego.idl.models.appointment.v2.PricingRuleApplyLogModel;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface PricingRuleConverter {

    PricingRuleConverter INSTANCE = org.mapstruct.factory.Mappers.getMapper(PricingRuleConverter.class);

    List<PricingRuleApplyLogDrawerView> toDrawerView(List<PricingRuleApplyLogModel> model);

    PricingRuleApplyLogDrawerView toDrawerView(PricingRuleApplyLogModel model);
}
