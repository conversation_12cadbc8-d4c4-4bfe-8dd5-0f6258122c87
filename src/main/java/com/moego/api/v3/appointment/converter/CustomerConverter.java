package com.moego.api.v3.appointment.converter;

import com.moego.idl.api.appointment.v1.CalendarCardCustomerInfo;
import com.moego.idl.api.appointment.v1.ListDayCardsResult;
import com.moego.idl.models.business_customer.v1.BusinessCustomerCalendarView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerModelNameView;
import com.moego.server.customer.dto.GroomingCalenderCustomerInfo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface CustomerConverter {

    CustomerConverter INSTANCE = Mappers.getMapper(CustomerConverter.class);

    BusinessCustomerCalendarView toOverview(BusinessCustomerModel model);

    @Mapping(target = "clientId", source = "customerId")
    ListDayCardsResult.CalendarCardClientInfo toCardClientInfo(GroomingCalenderCustomerInfo info);

    @Mapping(target = "customerId", source = "id")
    @Mapping(target = "isNewClient", ignore = true)
    @Mapping(target = "areas", ignore = true)
    CalendarCardCustomerInfo toCalendarCard(BusinessCustomerInfoModel model);

    BusinessCustomerModelNameView toNameView(BusinessCustomerInfoModel model);
}
