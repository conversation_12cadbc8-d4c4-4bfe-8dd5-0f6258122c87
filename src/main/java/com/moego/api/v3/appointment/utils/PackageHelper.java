package com.moego.api.v3.appointment.utils;

import com.moego.api.v3.appointment.converter.PackageConverter;
import com.moego.common.dto.BusinessDateTimeDTO;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.api.appointment.v1.CustomerPackageView;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.grooming.client.IGroomingPackageClient;
import com.moego.server.grooming.dto.GroomingPackageDTO;
import com.moego.server.grooming.dto.GroomingPackageServiceDTO;
import com.moego.server.grooming.params.GetPackageServicesParams;
import com.moego.server.grooming.params.GetPackagesParams;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@RequiredArgsConstructor
public class PackageHelper {
    private final IGroomingPackageClient groomingPackageClient;
    private final IBusinessBusinessClient businessClient;

    public CompletableFuture<Map<Long, List<CustomerPackageView>>> listCustomerPackages(
            Long companyId, Long businessId, List<Long> customerIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(customerIds) || !CommonUtil.isNormal(businessId)) {
                        return Map.of();
                    }
                    BusinessDateTimeDTO dateTime = businessClient.getBusinessDateTime(Math.toIntExact(businessId));
                    List<GroomingPackageDTO> packages = groomingPackageClient.getPackages(new GetPackagesParams(
                            companyId,
                            customerIds.stream().map(Long::intValue).toList(),
                            Math.toIntExact(businessId),
                            dateTime.getCurrentDate()));
                    if (CollectionUtils.isEmpty(packages)) {
                        return Map.of();
                    }

                    return packages.stream()
                            .collect(Collectors.groupingBy(
                                    dto -> dto.getCustomerId().longValue(),
                                    Collectors.mapping(PackageConverter.INSTANCE::convert, Collectors.toList())));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Integer, List<CustomerPackageView.PackageDetail>>> listCustomerPackageDetails(
            CompletableFuture<Map<Long, List<CustomerPackageView>>> customerPackagesFuture) {
        return customerPackagesFuture.thenApplyAsync(
                result -> {
                    if (CollectionUtils.isEmpty(result)) {
                        return Map.of();
                    }
                    List<Integer> packageIds = result.values().stream()
                            .flatMap(List::stream)
                            .map(CustomerPackageView::getPackageId)
                            .distinct()
                            .map(Long::intValue)
                            .toList();
                    List<GroomingPackageServiceDTO> packageServices =
                            groomingPackageClient.getPackageServices(new GetPackageServicesParams(packageIds));
                    if (CollectionUtils.isEmpty(packageServices)) {
                        return Map.of();
                    }

                    return packageServices.stream()
                            .collect(Collectors.groupingBy(
                                    GroomingPackageServiceDTO::getPackageId,
                                    Collectors.mapping(PackageConverter.INSTANCE::convert, Collectors.toList())));
                },
                ThreadPool.getSubmitExecutor());
    }

    public static List<CustomerPackageView> getPackageViews(
            Map<Long, List<CustomerPackageView>> customerPackages,
            Map<Integer, List<CustomerPackageView.PackageDetail>> packageDetails,
            Long customerId) {
        return getPackageViews(customerPackages, packageDetails, customerId, false);
    }

    public static List<CustomerPackageView> getPackageViews(
            Map<Long, List<CustomerPackageView>> customerPackages,
            Map<Integer, List<CustomerPackageView.PackageDetail>> packageDetails,
            Long customerId,
            boolean showUsedUp) {
        return customerPackages.getOrDefault(customerId, List.of()).stream()
                .map(customerPackageInfo -> {
                    List<CustomerPackageView.PackageDetail> details =
                            packageDetails.getOrDefault(Math.toIntExact(customerPackageInfo.getPackageId()), List.of());
                    // 过滤掉剩余数量为 0 的 package
                    if (!showUsedUp && details.stream().allMatch(detail -> detail.getRemainingQuantity() <= 0)) {
                        return null;
                    }
                    return customerPackageInfo.toBuilder()
                            .addAllPackageDetails(details)
                            .build();
                })
                .filter(Objects::nonNull)
                .toList();
    }
}
