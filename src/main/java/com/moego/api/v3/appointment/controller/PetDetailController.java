package com.moego.api.v3.appointment.controller;

import static com.moego.idl.service.organization.v1.BusinessServiceGrpc.BusinessServiceBlockingStub;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.api.common.Range;
import com.moego.api.v3.appointment.utils.FeedingMedicationUtil;
import com.moego.idl.api.appointment.v1.CountPetDetailParams;
import com.moego.idl.api.appointment.v1.CountPetDetailResult;
import com.moego.idl.api.appointment.v1.DeletePetEvaluationParams;
import com.moego.idl.api.appointment.v1.DeletePetEvaluationResult;
import com.moego.idl.api.appointment.v1.DeletePetParams;
import com.moego.idl.api.appointment.v1.DeletePetResult;
import com.moego.idl.api.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.api.appointment.v1.PreCreateEvaluationServiceCheckParams;
import com.moego.idl.api.appointment.v1.PreCreateEvaluationServiceCheckResult;
import com.moego.idl.api.appointment.v1.SaveOrUpdatePetDetailsParams;
import com.moego.idl.api.appointment.v1.SaveOrUpdatePetDetailsResult;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.AppointmentCountServiceGrpc.AppointmentCountServiceBlockingStub;
import com.moego.idl.service.appointment.v1.DeletePetEvaluationRequest;
import com.moego.idl.service.appointment.v1.DeletePetRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentCountByDateRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentCountByDateResponse;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc.PetDetailServiceBlockingStub;
import com.moego.idl.service.appointment.v1.SaveOrUpdatePetDetailsRequest;
import com.moego.idl.service.organization.v1.GetCompanyIdRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.grooming.api.IGroomingPetDetailService;
import com.moego.server.grooming.dto.PetDetailDTO;
import io.grpc.stub.StreamObserver;
import java.time.LocalDate;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/1/30
 */
@Service
@RequiredArgsConstructor
public class PetDetailController extends PetDetailServiceGrpc.PetDetailServiceImplBase {

    private final PetDetailServiceBlockingStub petDetailServiceBlockingStub;
    private final AppointmentCountServiceBlockingStub appointmentCountServiceBlockingStub;
    private final BusinessServiceBlockingStub businessServiceStub;
    private final IGroomingPetDetailService petDetailApi;
    private final FeedingMedicationUtil feedingMedicationUtil;

    private static List<PetDetailDef> buildPetDetailDefs(SaveOrUpdatePetDetailsParams request) {
        return request.getPetDetailsCount() != 0 ? request.getPetDetailsList() : List.of(request.getPetDetail());
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void saveOrUpdatePetDetails(
            SaveOrUpdatePetDetailsParams request, StreamObserver<SaveOrUpdatePetDetailsResult> responseObserver) {
        long companyId = AuthContext.get().companyId();
        var petDetailDefs = buildPetDetailDefs(request);
        SaveOrUpdatePetDetailsRequest.Builder builder = SaveOrUpdatePetDetailsRequest.newBuilder()
                .setAppointmentId(request.getAppointmentId())
                .addAllPetDetails(petDetailDefs)
                .setCompanyId(companyId)
                .setBusinessId(AuthContext.get().businessId())
                .setStaffId(AuthContext.get().staffId());
        if (request.hasRepeatAppointmentModifyScope()) {
            builder.setRepeatAppointmentModifyScope(request.getRepeatAppointmentModifyScope());
        }
        petDetailServiceBlockingStub.saveOrUpdatePetDetails(builder.build());
        feedingMedicationUtil.syncPetDetailDef(companyId, petDetailDefs);

        responseObserver.onNext(SaveOrUpdatePetDetailsResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deletePet(DeletePetParams request, StreamObserver<DeletePetResult> responseObserver) {
        DeletePetRequest.Builder builder = DeletePetRequest.newBuilder()
                .setAppointmentId(request.getAppointmentId())
                .setPetId(request.getPetId())
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId())
                .setStaffId(AuthContext.get().staffId());
        if (request.hasRepeatAppointmentModifyScope()) {
            builder.setRepeatAppointmentModifyScope(request.getRepeatAppointmentModifyScope());
        }
        petDetailServiceBlockingStub.deletePet(builder.build());

        responseObserver.onNext(DeletePetResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deletePetEvaluation(
            DeletePetEvaluationParams request, StreamObserver<DeletePetEvaluationResult> responseObserver) {
        petDetailServiceBlockingStub.deletePetEvaluation(DeletePetEvaluationRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setAppointmentId(request.getAppointmentId())
                .setEvaluationServiceDetailId(request.getEvaluationServiceDetailId())
                .setTokenStaffId(AuthContext.get().staffId())
                .build());
        responseObserver.onNext(DeletePetEvaluationResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void preCreateEvaluationServiceCheck(
            PreCreateEvaluationServiceCheckParams request,
            StreamObserver<PreCreateEvaluationServiceCheckResult> responseObserver) {
        var req = GetAppointmentCountByDateRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .addAllServiceItemType(List.of(ServiceItemType.EVALUATION));
        if (request.hasStartDate()) {
            LocalDate startDate = LocalDate.parse(request.getStartDate());
            req.setStartDateGte(startDate.toString());
            req.setEndDateLt(startDate.plusDays(1).toString());
        }
        GetAppointmentCountByDateResponse resp =
                appointmentCountServiceBlockingStub.getAppointmentCountByDate(req.build());
        responseObserver.onNext(PreCreateEvaluationServiceCheckResult.newBuilder()
                .setPetNumForEvaluation(resp.getEvaluationPetCount())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void countPetDetail(CountPetDetailParams request, StreamObserver<CountPetDetailResult> r) {

        checkBusinessCompany(AuthContext.get().companyId(), request.getBusinessId());

        if (LocalDate.parse(request.getStartDate()).plusDays(30).isBefore(LocalDate.parse(request.getEndDate()))) {
            throw bizException(Code.CODE_PARAMS_ERROR, "max 30 days");
        }

        int petCount = countPet(request);

        r.onNext(CountPetDetailResult.newBuilder().setCount(petCount).build());
        r.onCompleted();
    }

    private int countPet(CountPetDetailParams param) {
        return (int) petDetailApi
                .listPetDetailByDateRange(IGroomingPetDetailService.ListPetDetailByDateRangeParam.builder()
                        .businessId((int) param.getBusinessId())
                        .dateRange(new Range<>(param.getStartDate(), param.getEndDate()))
                        .build())
                .stream()
                .filter(e -> param.getServiceItemType() == ServiceItemType.SERVICE_ITEM_TYPE_UNSPECIFIED
                        || e.getServiceItemType() == param.getServiceItemTypeValue())
                .map(PetDetailDTO::getPetId)
                .distinct()
                .count();
    }

    private void checkBusinessCompany(long companyId, long businessId) {
        var resp = businessServiceStub.getCompanyId(
                GetCompanyIdRequest.newBuilder().setBusinessId(businessId).build());
        if (resp.getCompanyId() != companyId) {
            throw bizException(Code.CODE_PARAMS_ERROR, "invalid request params");
        }
    }
}
