package com.moego.api.v3.appointment.service;

import com.moego.server.customer.api.IPetBelongingService;
import com.moego.server.customer.dto.PetBelongingDTO;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class PetBelongingService {
    private final IPetBelongingService petBelongingClient;

    List<PetBelongingDTO> listPetBelongs(List<Long> appointmentIds) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return List.of();
        }
        return petBelongingClient.getPetBelongs(
                appointmentIds.stream().distinct().map(Long::intValue).toList());
    }

    public Map<Long, List<PetBelongingDTO>> getAppointmentPetBelongs(List<Long> appointmentIds) {
        return listPetBelongs(appointmentIds).stream()
                .collect(Collectors.groupingBy(a -> a.getAppointmentId().longValue()));
    }
}
