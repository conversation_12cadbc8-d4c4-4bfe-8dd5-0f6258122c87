package com.moego.api.v3.appointment.dto;

import com.moego.idl.models.organization.v1.LimitationGroup;
import com.moego.lib.utils.model.Pair;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import java.util.List;
import java.util.Set;
import lombok.Data;

@Data
public class CapacityTimeslotDTO {

    private Integer startTime;
    private Integer endTime;
    private Integer capacity;

    /**
     * 根据 slot free service 配置，实际占用的 pet pairs
     */
    private Set<Pair<Integer, Integer>> usedAppointmentPetPairs;

    /**
     * 忽略 slot free service 配置，实际占用的 pet pairs
     */
    private Set<Pair<Integer, Integer>> usedAppointmentPetPairsIgnoreSlotFreeService;

    private List<LimitationGroup> limitationGroups;

    // 该 timeslot 下的 petDetail list
    private List<SmartScheduleGroomingDetailsDTO> petDetails;

    private String note = "";

    public boolean contains(int time) {
        return startTime <= time && time < endTime;
    }
}
