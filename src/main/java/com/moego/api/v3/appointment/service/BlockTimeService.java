package com.moego.api.v3.appointment.service;

import com.google.type.Date;
import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.api.v3.appointment.dto.CalendarCardFilterDTO;
import com.moego.idl.models.appointment.v1.BlockTimeModel;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.ListBlockTimesRequest;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/9/26
 */
@Service
@RequiredArgsConstructor
public class BlockTimeService {

    private final CompanySettingService companySettingService;

    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;

    /**
     * Block time 的时间跨度限制在 60 天允许展示在 calendar 上
     *
     * @param filter calendar card filter
     * @return list of block times
     */
    public List<BlockTimeModel> listCalendarBlockTimes(CalendarCardFilterDTO filter) {
        if (filter.getCompanyId() == null || filter.getBusinessId() == null) {
            return List.of();
        }
        var timezone = companySettingService.mustGetTimeZoneName(filter.getCompanyId());
        return appointmentStub
                .listBlockTimes(ListBlockTimesRequest.newBuilder()
                        .setCompanyId(filter.getCompanyId())
                        .addBusinessIds(filter.getBusinessId())
                        .setFilter(buildBlockTimeFilter(filter.getStartDate(), filter.getEndDate(), timezone))
                        .build())
                .getBlockTimesList();
    }

    private static ListBlockTimesRequest.Filter buildBlockTimeFilter(
            Date startFilter, Date endFilter, String timezone) {
        var startDate = DateTimeConverter.INSTANCE.toZonedDateTime(startFilter, timezone);
        var endDate =
                DateTimeConverter.INSTANCE.toZonedDateTime(endFilter, timezone).plusDays(1);
        return ListBlockTimesRequest.Filter.newBuilder()
                .setStartTimeRange(DateTimeConverter.INSTANCE.buildInterval(
                        endDate.minusDays(60).toEpochSecond(), endDate.toEpochSecond() - 1))
                .setEndTimeRange(DateTimeConverter.INSTANCE.buildInterval(
                        startDate.toEpochSecond(), startDate.plusDays(60).toEpochSecond() - 1))
                .build();
    }
}
