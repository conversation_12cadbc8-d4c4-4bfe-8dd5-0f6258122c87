package com.moego.api.v3.appointment.utils;

import com.google.type.Interval;
import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.idl.api.appointment.v1.ListAppointmentTaskCountByCategoryParams;
import com.moego.idl.api.appointment.v1.ListAppointmentTasksByCategoryParams;
import com.moego.idl.api.appointment.v1.ListAppointmentTasksParams;
import com.moego.idl.api.appointment.v1.SearchPetWithTaskDateParams;
import com.moego.idl.models.appointment.v1.AppointmentTaskCategory;
import com.moego.idl.models.appointment.v1.AppointmentTaskModel;
import com.moego.idl.models.appointment.v1.AppointmentTaskSchedule;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.service.appointment.v1.CountAppointmentTasksRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentTaskGroupsRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentTaskPetsRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentTasksRequest;
import com.moego.idl.utils.v2.OrderBy;
import java.util.List;
import lombok.experimental.UtilityClass;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/12/4
 */
@UtilityClass
public class TaskUtil {

    public AppointmentTaskCategory getCategoryTab(ListAppointmentTasksParams request) {
        if (request.hasFilter() && request.getFilter().hasCategoryTab()) {
            return request.getFilter().getCategoryTab();
        }
        return AppointmentTaskCategory.FEEDING;
    }

    public AppointmentTaskSchedule getScheduleTab(ListAppointmentTasksParams request) {
        if (request.hasFilter() && request.getFilter().hasScheduleTab()) {
            return request.getFilter().getScheduleTab();
        }
        return AppointmentTaskSchedule.AM;
    }

    public AppointmentTaskSchedule getScheduleCategory(AppointmentTaskModel task) {
        if (!task.hasStartTime()) {
            return AppointmentTaskSchedule.UNASSIGNED;
        } else if (task.getStartTime() < 12 * 60) {
            return AppointmentTaskSchedule.AM;
        } else {
            return AppointmentTaskSchedule.PM;
        }
    }

    public ListAppointmentTasksRequest buildTasksRequest(
            long companyId, ListAppointmentTasksParams request, Interval fullyDayInterval) {
        return ListAppointmentTasksRequest.newBuilder()
                .setCompanyId(companyId)
                .addBusinessIds(request.getBusinessId())
                .setFilter(TaskUtil.buildTasksFilter(request.getFilter(), request.getGroupBy(), fullyDayInterval))
                .addAllOrderBys(request.getOrderBysCount() != 0 ? request.getOrderBysList() : buildDefaultOrderBys())
                .setPagination(request.getPagination())
                .build();
    }

    public ListAppointmentTasksRequest buildTasksRequest(
            long companyId, ListAppointmentTasksByCategoryParams request, Interval fullyDayInterval) {
        var filter = ListAppointmentTasksRequest.Filter.newBuilder()
                .setStartDateInterval(fullyDayInterval)
                .addAllCareTypes(request.getFilter().getCareTypesList())
                .addAllStatuses(request.getFilter().getStatusesList());
        if (request.getFilter().hasCategory()) {
            filter.addCategories(request.getFilter().getCategory());
        }
        if (request.getFilter().hasAddOnId()) {
            filter.addAddOnIds(request.getFilter().getAddOnId());
        }
        return ListAppointmentTasksRequest.newBuilder()
                .setCompanyId(companyId)
                .addBusinessIds(request.getBusinessId())
                .setFilter(filter)
                .addAllOrderBys(buildDefaultOrderBys())
                .setPagination(request.getPagination())
                .build();
    }

    private static List<OrderBy> buildDefaultOrderBys() {
        return List.of(
                OrderBy.newBuilder().setFieldName("startTime").setAsc(true).build(),
                OrderBy.newBuilder().setFieldName("lodgingName").setAsc(true).build());
    }

    private ListAppointmentTasksRequest.Filter buildTasksFilter(
            ListAppointmentTasksParams.Filter filter,
            ListAppointmentTasksParams.GroupBy groupBy,
            Interval fullyDayInterval) {
        if (filter.equals(ListAppointmentTasksParams.Filter.getDefaultInstance())) {
            return buildDefaultFilter(groupBy, fullyDayInterval);
        } else {
            return buildCustomFilter(filter, fullyDayInterval);
        }
    }

    private ListAppointmentTasksRequest.Filter buildDefaultFilter(
            ListAppointmentTasksParams.GroupBy groupBy, Interval fullyDayInterval) {
        var builder = ListAppointmentTasksRequest.Filter.newBuilder().setStartDateInterval(fullyDayInterval);

        switch (groupBy) {
            case CATEGORY -> builder.addCategories(AppointmentTaskCategory.FEEDING);
            case SCHEDULE -> builder.setStartTimeInterval(DateTimeConverter.INSTANCE.buildAMInterval());
            default -> {} // No-op
        }

        return builder.build();
    }

    private ListAppointmentTasksRequest.Filter buildCustomFilter(
            ListAppointmentTasksParams.Filter filter, Interval fullyDayInterval) {
        var builder = ListAppointmentTasksRequest.Filter.newBuilder().setStartDateInterval(fullyDayInterval);

        builder.addAllStatuses(filter.getStatusesList())
                .addAllCareTypes(filter.getCareTypesList())
                .addAllStaffIds(filter.getStaffIdsList())
                .addAllPetIds(filter.getPetIdsList());

        handleSelectedTab(filter, builder);
        handleSelectedGroup(filter, builder);

        return builder.build();
    }

    private void handleSelectedTab(
            ListAppointmentTasksParams.Filter filter, ListAppointmentTasksRequest.Filter.Builder builder) {
        switch (filter.getSelectedTabCase()) {
            case SCHEDULE_TAB -> handleScheduleTab(filter.getScheduleTab(), builder);
            case CATEGORY_TAB -> builder.addCategories(filter.getCategoryTab());
            default -> {} // No-op
        }
    }

    private void handleScheduleTab(
            AppointmentTaskSchedule schedule, ListAppointmentTasksRequest.Filter.Builder builder) {
        switch (schedule) {
            case AM -> builder.setStartTimeInterval(DateTimeConverter.INSTANCE.buildAMInterval());
            case PM -> builder.setStartTimeInterval(DateTimeConverter.INSTANCE.buildPMInterval());
            case UNASSIGNED -> builder.setUnassignedStartTime(true);
            default -> {} // No-op
        }
    }

    private void handleSelectedGroup(
            ListAppointmentTasksParams.Filter filter, ListAppointmentTasksRequest.Filter.Builder builder) {
        switch (filter.getSelectedGroupCase()) {
            case SCHEDULE_GROUP -> builder.setTimeLabel(
                            filter.getScheduleGroup().getScheduleName())
                    .setStartTimeInterval(DateTimeConverter.INSTANCE.buildTimeOfDayInterval(
                            filter.getScheduleGroup().getScheduleTime(),
                            filter.getScheduleGroup().getScheduleTime()));
            case CATEGORY_GROUP -> builder.addCategories(filter.getCategoryGroup());
            case ADD_ON_ID -> builder.addAddOnIds(filter.getAddOnId());
            default -> {} // No-op
        }
    }

    public CountAppointmentTasksRequest buildCountRequest(
            long companyId, ListAppointmentTasksParams params, Interval fullyDayInterval) {
        var builder = CountAppointmentTasksRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(params.getBusinessId())
                .setFilter(buildTabFilter(params.getFilter(), fullyDayInterval));

        switch (params.getGroupBy()) {
            case CATEGORY -> builder.addGroupCategories(AppointmentTaskCategory.FEEDING)
                    .addGroupCategories(AppointmentTaskCategory.MEDICATION)
                    .addGroupCategories(AppointmentTaskCategory.ADD_ONS);
            case SCHEDULE -> builder.addGroupSchedules(AppointmentTaskSchedule.AM)
                    .addGroupSchedules(AppointmentTaskSchedule.PM)
                    .addGroupSchedules(AppointmentTaskSchedule.UNASSIGNED);
            default -> {} // No-op
        }

        return builder.build();
    }

    public CountAppointmentTasksRequest buildCountRequest(
            long companyId,
            ListAppointmentTaskCountByCategoryParams params,
            Interval fullyDayInterval,
            List<AppointmentTaskCategory> categories) {
        return CountAppointmentTasksRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(params.getBusinessId())
                .setFilter(CountAppointmentTasksRequest.Filter.newBuilder()
                        .setStartDateInterval(fullyDayInterval)
                        .addAllCareTypes(params.getFilter().getCareTypesList())
                        .addAllStatuses(params.getFilter().getStatusesList())
                        .addAllStaffIds(params.getFilter().getStaffIdsList())
                        .build())
                .addAllGroupCategories(categories)
                .build();
    }

    private CountAppointmentTasksRequest.Filter buildTabFilter(
            ListAppointmentTasksParams.Filter filter, Interval fullyDayInterval) {
        if (filter.equals(ListAppointmentTasksParams.Filter.getDefaultInstance())) {
            return CountAppointmentTasksRequest.Filter.newBuilder()
                    .setStartDateInterval(fullyDayInterval)
                    .build();
        } else {
            return CountAppointmentTasksRequest.Filter.newBuilder()
                    .setStartDateInterval(fullyDayInterval)
                    .addAllCareTypes(filter.getCareTypesList())
                    .addAllStatuses(filter.getStatusesList())
                    .addAllStaffIds(filter.getStaffIdsList())
                    .addAllPetIds(filter.getPetIdsList())
                    .build();
        }
    }

    public ListAppointmentTaskGroupsRequest buildGroupsRequest(
            long companyId, ListAppointmentTasksParams params, Interval fullyDayInterval) {
        var builder = ListAppointmentTaskGroupsRequest.newBuilder();
        if (params.getFilter().hasCategoryTab()) {
            builder.setCategory(params.getFilter().getCategoryTab());
        }
        if (params.getFilter().hasScheduleTab()) {
            builder.setSchedule(params.getFilter().getScheduleTab());
        }
        return builder.setCompanyId(companyId)
                .setBusinessId(params.getBusinessId())
                .setFilter(ListAppointmentTaskGroupsRequest.Filter.newBuilder()
                        .setStartDateInterval(fullyDayInterval)
                        .addAllCareTypes(params.getFilter().getCareTypesList())
                        .addAllStatuses(params.getFilter().getStatusesList())
                        .addAllStaffIds(params.getFilter().getStaffIdsList())
                        .addAllPetIds(params.getFilter().getPetIdsList())
                        .build())
                .build();
    }

    public ListAppointmentTaskPetsRequest buildTaskPetsRequest(
            long companyId, SearchPetWithTaskDateParams request, Interval fullyDayInterval) {
        return ListAppointmentTaskPetsRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(request.getBusinessId())
                .setFilter(ListAppointmentTaskPetsRequest.Filter.newBuilder().setStartDateInterval(fullyDayInterval))
                .build();
    }

    public boolean matchPet(BusinessCustomerPetInfoModel pet, String keyword) {
        return pet != null
                && StringUtils.hasText(keyword)
                && pet.getPetName().toLowerCase().contains(keyword.toLowerCase());
    }

    public boolean matchCustomer(BusinessCustomerInfoModel customer, String keyword) {
        if (customer == null || !StringUtils.hasText(keyword)) {
            return false;
        }
        var fullName = customer.getFirstName() + " " + customer.getLastName();
        return containsIgnoreCase(customer.getFirstName(), keyword)
                || containsIgnoreCase(customer.getLastName(), keyword)
                || containsIgnoreCase(fullName, keyword);
    }

    private boolean containsIgnoreCase(String source, String target) {
        return source != null && target != null && source.toLowerCase().contains(target.toLowerCase());
    }

    public boolean matchPetOrCustomer(
            BusinessCustomerPetInfoModel pet, BusinessCustomerInfoModel customer, String keyword) {
        return matchCustomer(customer, keyword) || matchPet(pet, keyword);
    }
}
