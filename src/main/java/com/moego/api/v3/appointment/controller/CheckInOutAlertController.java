package com.moego.api.v3.appointment.controller;

import com.moego.idl.api.appointment.v1.BatchGetAlertsForCheckInParams;
import com.moego.idl.api.appointment.v1.BatchGetAlertsForCheckInResult;
import com.moego.idl.api.appointment.v1.CheckInOutAlertServiceGrpc;
import com.moego.idl.api.appointment.v1.GetAlertSettingsParams;
import com.moego.idl.api.appointment.v1.GetAlertSettingsResult;
import com.moego.idl.api.appointment.v1.GetAlertsForCheckInParams;
import com.moego.idl.api.appointment.v1.GetAlertsForCheckInResult;
import com.moego.idl.api.appointment.v1.GetAlertsForCheckOutParams;
import com.moego.idl.api.appointment.v1.GetAlertsForCheckOutResult;
import com.moego.idl.api.appointment.v1.SaveAlertSettingsParams;
import com.moego.idl.api.appointment.v1.SaveAlertSettingsResult;
import com.moego.idl.service.appointment.v1.BatchGetAlertsForCheckInRequest;
import com.moego.idl.service.appointment.v1.GetAlertSettingsRequest;
import com.moego.idl.service.appointment.v1.GetAlertsForCheckInRequest;
import com.moego.idl.service.appointment.v1.GetAlertsForCheckOutRequest;
import com.moego.idl.service.appointment.v1.SaveAlertSettingsRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class CheckInOutAlertController extends CheckInOutAlertServiceGrpc.CheckInOutAlertServiceImplBase {

    private final com.moego.idl.service.appointment.v1.CheckInOutAlertServiceGrpc.CheckInOutAlertServiceBlockingStub
            checkInOutAlertClient;

    @Override
    @Auth(AuthType.COMPANY)
    public void getAlertSettings(
            GetAlertSettingsParams request, StreamObserver<GetAlertSettingsResult> responseObserver) {
        var response = checkInOutAlertClient.getAlertSettings(GetAlertSettingsRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .build());

        responseObserver.onNext(GetAlertSettingsResult.newBuilder()
                .setSettings(response.getSettings())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void saveAlertSettings(
            SaveAlertSettingsParams request, StreamObserver<SaveAlertSettingsResult> responseObserver) {
        var builder = SaveAlertSettingsRequest.newBuilder();
        builder.setCompanyId(AuthContext.get().companyId());
        if (request.hasCheckInSettings()) {
            builder.setCheckInSettings(request.getCheckInSettings());
        }
        if (request.hasCheckOutSettings()) {
            builder.setCheckOutSettings(request.getCheckOutSettings());
        }
        var response = checkInOutAlertClient.saveAlertSettings(builder.build());

        responseObserver.onNext(SaveAlertSettingsResult.newBuilder()
                .setSettings(response.getSettings())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void batchGetAlertsForCheckIn(
            BatchGetAlertsForCheckInParams request, StreamObserver<BatchGetAlertsForCheckInResult> responseObserver) {
        var response = checkInOutAlertClient.batchGetAlertsForCheckIn(BatchGetAlertsForCheckInRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .addAllClientPets(request.getClientPetsList())
                .build());

        responseObserver.onNext(BatchGetAlertsForCheckInResult.newBuilder()
                .addAllAlerts(response.getAlertsList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getAlertsForCheckIn(
            GetAlertsForCheckInParams request, StreamObserver<GetAlertsForCheckInResult> responseObserver) {
        var response = checkInOutAlertClient.getAlertsForCheckIn(GetAlertsForCheckInRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .setAppointmentId(request.getAppointmentId())
                .build());

        var builder = GetAlertsForCheckInResult.newBuilder();
        if (response.hasAlert()) {
            builder.setAlert(response.getAlert());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getAlertsForCheckOut(
            GetAlertsForCheckOutParams request, StreamObserver<GetAlertsForCheckOutResult> responseObserver) {
        var response = checkInOutAlertClient.getAlertsForCheckOut(GetAlertsForCheckOutRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .setAppointmentId(request.getAppointmentId())
                .build());

        var builder = GetAlertsForCheckOutResult.newBuilder();
        if (response.hasAlert()) {
            builder.setAlert(response.getAlert());
        }
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
