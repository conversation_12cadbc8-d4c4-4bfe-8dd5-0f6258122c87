package com.moego.api.v3.appointment.controller;

import com.moego.idl.api.appointment.v1.AddLatePickUpServiceChargeDetailParams;
import com.moego.idl.api.appointment.v1.AddLatePickUpServiceChargeDetailResult;
import com.moego.idl.api.appointment.v1.AddServiceChargeDetailParams;
import com.moego.idl.api.appointment.v1.AddServiceChargeDetailResult;
import com.moego.idl.api.appointment.v1.CheckLatePickUpRuleParams;
import com.moego.idl.api.appointment.v1.CheckLatePickUpRuleResult;
import com.moego.idl.api.appointment.v1.DeleteServiceChargeDetailParams;
import com.moego.idl.api.appointment.v1.DeleteServiceChargeDetailResult;
import com.moego.idl.api.appointment.v1.ServiceChargeDetailServiceGrpc;
import com.moego.idl.models.order.v1.ServiceCharge;
import com.moego.idl.service.appointment.v1.AddLatePickUpServiceChargeDetailRequest;
import com.moego.idl.service.appointment.v1.AddServiceChargeDetailRequest;
import com.moego.idl.service.appointment.v1.DeleteServiceChargeDetailRequest;
import com.moego.idl.service.appointment.v1.ListHitLatePickUpRulesRequest;
import com.moego.idl.service.appointment.v1.ServiceChargeDetailServiceGrpc.ServiceChargeDetailServiceBlockingStub;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/5/1
 */
@GrpcService
@RequiredArgsConstructor
public class ServiceChargeDetailController extends ServiceChargeDetailServiceGrpc.ServiceChargeDetailServiceImplBase {

    private final ServiceChargeDetailServiceBlockingStub serviceChargeDetailStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void addServiceChargeDetail(
            AddServiceChargeDetailParams request, StreamObserver<AddServiceChargeDetailResult> responseObserver) {
        serviceChargeDetailStub.addServiceChargeDetail(AddServiceChargeDetailRequest.newBuilder()
                .setAppointmentId(request.getAppointmentId())
                .addAllServiceChargeIds(request.getServiceChargeIdsList())
                .setCompanyId(AuthContext.get().companyId())
                .setStaffId(AuthContext.get().staffId())
                .build());

        responseObserver.onNext(AddServiceChargeDetailResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deleteServiceChargeDetail(
            DeleteServiceChargeDetailParams request, StreamObserver<DeleteServiceChargeDetailResult> responseObserver) {
        serviceChargeDetailStub.deleteServiceChargeDetail(DeleteServiceChargeDetailRequest.newBuilder()
                .setId(request.getId())
                .setCompanyId(AuthContext.get().companyId())
                .setStaffId(AuthContext.get().staffId())
                .build());

        responseObserver.onNext(DeleteServiceChargeDetailResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void checkLatePickUpRule(
            CheckLatePickUpRuleParams request, StreamObserver<CheckLatePickUpRuleResult> responseObserver) {
        var response = serviceChargeDetailStub.listHitLatePickUpRules(ListHitLatePickUpRulesRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setAppointmentId(request.getAppointmentId())
                .build());

        responseObserver.onNext(CheckLatePickUpRuleResult.newBuilder()
                .setHitRule(response.getServiceChargesCount() > 0)
                .addAllServiceChargeIds(response.getServiceChargesList().stream()
                        .map(ServiceCharge::getId)
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void addLatePickUpServiceChargeDetail(
            AddLatePickUpServiceChargeDetailParams request,
            StreamObserver<AddLatePickUpServiceChargeDetailResult> responseObserver) {
        serviceChargeDetailStub.addLatePickUpServiceChargeDetail(AddLatePickUpServiceChargeDetailRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setAppointmentId(request.getAppointmentId())
                .build());

        responseObserver.onNext(AddLatePickUpServiceChargeDetailResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
