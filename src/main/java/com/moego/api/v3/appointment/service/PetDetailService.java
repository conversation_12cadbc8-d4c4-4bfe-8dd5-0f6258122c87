package com.moego.api.v3.appointment.service;

import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.api.v3.appointment.dto.CalendarCardFilterDTO;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.GetPetDetailListRequest;
import com.moego.idl.service.appointment.v1.GetPetDetailListResponse;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.lib.utils.model.Pair;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/9/26
 */
@Service
@RequiredArgsConstructor
public class PetDetailService {

    private final PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailStub;

    public GetPetDetailListResponse getPetDetailsWithActualDates(
            long companyId, @NotNull List<Long> appointmentIdList) {
        appointmentIdList = appointmentIdList.stream()
                .filter(k -> k != null && k > 0)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(appointmentIdList)) {
            return GetPetDetailListResponse.getDefaultInstance();
        }

        var builder = GetPetDetailListRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllAppointmentIds(appointmentIdList)
                .setWithActualDates(true);
        return petDetailStub.getPetDetailList(builder.build());
    }

    public Pair<List<PetDetailModel>, List<EvaluationServiceModel>> listCalendarPetDetails(
            CalendarCardFilterDTO filter, List<AppointmentModel> appointments) {
        if (ObjectUtils.isEmpty(appointments)) {
            return Pair.of(List.of(), List.of());
        }
        var appointmentIds = appointments.stream().map(AppointmentModel::getId).toList();
        var response = petDetailStub.getPetDetailList(GetPetDetailListRequest.newBuilder()
                .setCompanyId(filter.getCompanyId())
                .addAllAppointmentIds(appointmentIds)
                .build());

        var startDate = DateTimeConverter.INSTANCE.toLocalDate(filter.getStartDate());
        var endDate = DateTimeConverter.INSTANCE.toLocalDate(filter.getEndDate());
        var noStartTimeAppointmentIds = appointments.stream()
                .filter(AppointmentModel::getNoStartTime)
                .map(AppointmentModel::getId)
                .collect(Collectors.toSet());

        return Pair.of(
                filterCalendarPetDetails(response.getPetDetailsList(), startDate, endDate, noStartTimeAppointmentIds),
                filterCalendarEvaluations(response.getPetEvaluationsList(), startDate, endDate));
    }

    private static List<EvaluationServiceModel> filterCalendarEvaluations(
            List<EvaluationServiceModel> evaluations, LocalDate startDate, LocalDate endDate) {
        return evaluations.stream()
                .filter(evaluation -> filterTimeRange(evaluation, startDate, endDate))
                .toList();
    }

    private static boolean filterTimeRange(EvaluationServiceModel evaluation, LocalDate startDate, LocalDate endDate) {
        return StringUtils.hasText(evaluation.getStartDate())
                && StringUtils.hasText(evaluation.getEndDate())
                && !LocalDate.parse(evaluation.getStartDate()).isAfter(endDate)
                && !LocalDate.parse(evaluation.getEndDate()).isBefore(startDate);
    }

    private static List<PetDetailModel> filterCalendarPetDetails(
            List<PetDetailModel> petDetails, LocalDate startDate, LocalDate endDate, Set<Long> noStartTimeIds) {
        return petDetails.stream()
                .filter(petDetail ->
                        filterTimeRange(petDetail, startDate, endDate, noStartTimeIds) && filterHasStaff(petDetail))
                .toList();
    }

    /**
     * 需要展示在 calendar 上的 pet detail <br>
     * 1. Grooming 类型的 service & add-on <br>
     * 2. Boarding/Daycare 类型有 staff 的 add-on <br>
     *
     * @param petDetail pet detail
     * @return true if show on calendar
     */
    private static boolean filterHasStaff(PetDetailModel petDetail) {
        boolean isGrooming = Objects.equals(petDetail.getServiceItemType(), ServiceItemType.GROOMING);
        boolean isDogWalking = Objects.equals(petDetail.getServiceItemType(), ServiceItemType.DOG_WALKING);
        boolean isBoardingOrDaycare = Objects.equals(petDetail.getServiceItemType(), ServiceItemType.BOARDING)
                || Objects.equals(petDetail.getServiceItemType(), ServiceItemType.DAYCARE);
        boolean hasStaff = petDetail.getStaffId() != 0;

        return isGrooming || isDogWalking || (isBoardingOrDaycare && hasStaff);
    }

    private static boolean filterTimeRange(
            PetDetailModel petDetail, LocalDate startDate, LocalDate endDate, Set<Long> noStartTimeIds) {
        return !noStartTimeIds.contains(petDetail.getGroomingId())
                && StringUtils.hasText(petDetail.getStartDate())
                && StringUtils.hasText(petDetail.getEndDate())
                && !LocalDate.parse(petDetail.getStartDate()).isAfter(endDate)
                && !LocalDate.parse(petDetail.getEndDate()).isBefore(startDate);
    }
}
