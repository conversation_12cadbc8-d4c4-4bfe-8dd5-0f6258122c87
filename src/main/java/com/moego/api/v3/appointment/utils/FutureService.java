package com.moego.api.v3.appointment.utils;

import com.google.type.Date;
import com.moego.api.v3.appointment.converter.OrderConverter;
import com.moego.api.v3.appointment.converter.PrePayConverter;
import com.moego.api.v3.appointment.dto.ReportCardSendResultDTO;
import com.moego.api.v3.business_customer.service.BusinessPetCodeService;
import com.moego.api.v3.offering.service.EvaluationService;
import com.moego.api.v3.offering.service.LodgingService;
import com.moego.api.v3.order.utils.OrderUtil;
import com.moego.api.v3.shared.helper.OBDepositHelper;
import com.moego.backend.proto.fulfillment.v1.FulfillmentReportServiceGrpc;
import com.moego.backend.proto.fulfillment.v1.ListFulfillmentReportConfigFilter;
import com.moego.backend.proto.fulfillment.v1.ListFulfillmentReportRequest;
import com.moego.backend.proto.fulfillment.v1.ListFulfillmentReportResponse;
import com.moego.backend.proto.fulfillment.v1.PaginationRef;
import com.moego.backend.proto.fulfillment.v1.ReportStatus;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.ReviewBoosterConst;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.enums.groomingreport.GroomingReportStatusEnum;
import com.moego.common.enums.order.OrderSourceType;
import com.moego.common.utils.PermissionUtil;
import com.moego.idl.api.appointment.v1.GetOverviewListParams;
import com.moego.idl.api.appointment.v1.PetVaccineComposite;
import com.moego.idl.api.appointment.v1.ServiceDetailOverview;
import com.moego.idl.api.appointment.v1.VaccineComposite;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentNoteModel;
import com.moego.idl.models.appointment.v1.AppointmentNoteType;
import com.moego.idl.models.appointment.v1.AppointmentOverview;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.BlockTimeModel;
import com.moego.idl.models.appointment.v1.BoardingSplitLodgingModel;
import com.moego.idl.models.appointment.v1.DailyReportConfigDef;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.InvoiceDepositModel;
import com.moego.idl.models.appointment.v1.OverviewDateType;
import com.moego.idl.models.appointment.v1.OverviewReportStatus;
import com.moego.idl.models.appointment.v1.OverviewStatus;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v1.ReportCardStatus;
import com.moego.idl.models.appointment.v1.SentResultDef;
import com.moego.idl.models.appointment.v1.ServiceOperationModel;
import com.moego.idl.models.appointment.v1.WaitListCalendarView;
import com.moego.idl.models.appointment.v1.WaitListStatus;
import com.moego.idl.models.appointment.v2.PricingRuleApplyLogModel;
import com.moego.idl.models.appointment.v2.PricingRuleApplySourceType;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerTagModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeModel;
import com.moego.idl.models.business_customer.v1.BusinessPetIncidentReportModel;
import com.moego.idl.models.business_customer.v1.BusinessPetNoteModel;
import com.moego.idl.models.business_customer.v1.PetEvaluationModel;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BookingRequestAssociatedModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.order.v1.InvoiceCalendarView;
import com.moego.idl.models.order.v1.NoShowInvoiceCalendarView;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderDetailModelV1;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderModelAppointmentView;
import com.moego.idl.models.organization.v1.LocationStaffIdsDef;
import com.moego.idl.models.organization.v1.StaffAccessByLocationDef;
import com.moego.idl.models.organization.v1.StaffAccessControlDef;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.models.payment.v1.PrePayCalendarView;
import com.moego.idl.service.agreement.v1.AgreementServiceGrpc;
import com.moego.idl.service.agreement.v1.BatchGetAgreementUnsignedAppointmentRequest;
import com.moego.idl.service.agreement.v1.BatchGetAgreementUnsignedAppointmentResponse;
import com.moego.idl.service.appointment.v1.AppointmentNoteServiceGrpc;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.BoardingSplitLodgingServiceGrpc;
import com.moego.idl.service.appointment.v1.GetAppointmentListRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentNoteListRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentNoteListResponse;
import com.moego.idl.service.appointment.v1.GetCustomerLastAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetCustomerLastAppointmentResponse;
import com.moego.idl.service.appointment.v1.GetCustomerLastNoteRequest;
import com.moego.idl.service.appointment.v1.GetCustomerLastNoteResponse;
import com.moego.idl.service.appointment.v1.GetDailyReportSentResultRequest;
import com.moego.idl.service.appointment.v1.GetInvoiceDepositListRequest;
import com.moego.idl.service.appointment.v1.GetPetDetailListRequest;
import com.moego.idl.service.appointment.v1.GetPetDetailListResponse;
import com.moego.idl.service.appointment.v1.GetServiceOperationListRequest;
import com.moego.idl.service.appointment.v1.GetWaitListByAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetWaitListByAppointmentResponse;
import com.moego.idl.service.appointment.v1.InvoiceDepositServiceGrpc;
import com.moego.idl.service.appointment.v1.ListAppointmentsRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentsResponse;
import com.moego.idl.service.appointment.v1.ListBlockTimesRequest;
import com.moego.idl.service.appointment.v1.ListBoardingSplitLodgingsRequest;
import com.moego.idl.service.appointment.v1.ListDailyReportConfigRequest;
import com.moego.idl.service.appointment.v1.OverviewStatusEntry;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.service.appointment.v1.PreviewEstimateOrderRequest;
import com.moego.idl.service.appointment.v1.PreviewEstimateOrderResponse;
import com.moego.idl.service.appointment.v1.ServiceOperationServiceGrpc;
import com.moego.idl.service.appointment.v1.WaitListServiceGrpc;
import com.moego.idl.service.appointment.v2.ListPricingRuleApplyLogRequest;
import com.moego.idl.service.appointment.v2.PricingRuleApplyServiceGrpc;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerInfoRequest;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerPrimaryAddressRequest;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerRequest;
import com.moego.idl.service.business_customer.v1.BatchGetCustomerResponse;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BatchListBindingPetCodeRequest;
import com.moego.idl.service.business_customer.v1.BatchListBindingPetCodeResponse;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessCustomerTagServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetCodeServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetCustomerPrimaryAddressRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerPrimaryAddressResponse;
import com.moego.idl.service.business_customer.v1.GetCustomerRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerResponse;
import com.moego.idl.service.business_customer.v1.ListBindingCustomerTagRequest;
import com.moego.idl.service.business_customer.v1.ListBindingCustomerTagResponse;
import com.moego.idl.service.business_customer.v1.ListPetEvaluationRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.ListBookingRequestsRequest;
import com.moego.idl.service.order.v1.GetOrderListRequest;
import com.moego.idl.service.order.v1.GetOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.order.v1.QueryOrderDetailRequest;
import com.moego.idl.service.organization.v1.GetShowOnCalendarStaffsRequest;
import com.moego.idl.service.organization.v1.GetStaffFullDetailRequest;
import com.moego.idl.service.organization.v1.GetStaffFullDetailResponse;
import com.moego.idl.service.organization.v1.QueryStaffByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.lib.utils.model.Pair;
import com.moego.server.business.client.IBusinessServiceAreaClient;
import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.business.params.BatchGetAreasByLocationParams;
import com.moego.server.business.params.GetAreasByLocationParams;
import com.moego.server.customer.client.ICustomerGroomingClient;
import com.moego.server.customer.client.IPetCodeClient;
import com.moego.server.customer.client.IPetVaccineClient;
import com.moego.server.customer.dto.CustomerPetPetCodeDTO;
import com.moego.server.customer.dto.GroomingCalenderCustomerInfo;
import com.moego.server.customer.dto.GroomingQueryDto;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import com.moego.server.customer.params.GroomingCustomerInfoParams;
import com.moego.server.grooming.api.IAutoAssignService;
import com.moego.server.grooming.client.IBookOnlineDepositClient;
import com.moego.server.grooming.client.IGroomingGroomingReportClient;
import com.moego.server.grooming.dto.AutoAssignDTO;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.dto.groomingreport.GroomingReportDTO;
import com.moego.server.grooming.params.groomingreport.GroomingIdListParams;
import com.moego.server.message.api.IGroomingReportSendService;
import com.moego.server.message.client.IBoosterClient;
import com.moego.server.message.dto.GroomingReportSendLogDTO;
import com.moego.server.message.dto.ReviewBoosterRecordDTO;
import com.moego.server.message.params.GetReviewBoosterRecordParams;
import com.moego.server.payment.client.IPaymentPreAuthClient;
import com.moego.server.payment.dto.PreAuthDTO;
import com.moego.server.payment.params.BatchQueryPreAuthParams;
import java.time.LocalDate;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class FutureService {

    private final BookingRequestServiceGrpc.BookingRequestServiceBlockingStub bookingRequestService;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentService;
    private final AppointmentNoteServiceGrpc.AppointmentNoteServiceBlockingStub appointmentNoteService;
    private final PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailService;
    private final BusinessPetCodeService petCodeService;
    private final ServiceOperationServiceGrpc.ServiceOperationServiceBlockingStub serviceOperationService;
    private final WaitListServiceGrpc.WaitListServiceBlockingStub waitListService;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffService;
    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub businessCustomerService;
    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub businessCustomerPetService;
    private final BusinessCustomerTagServiceGrpc.BusinessCustomerTagServiceBlockingStub businessCustomerTagService;
    private final BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub
            businessCustomerAddressService;
    private final com.moego.idl.service.appointment.v1.DailyReportServiceGrpc.DailyReportServiceBlockingStub
            dailyReportService;
    private final IGroomingGroomingReportClient groomingGroomingReportClient;
    private final IGroomingReportSendService groomingReportSendService;
    private final AgreementServiceGrpc.AgreementServiceBlockingStub agreementService;
    private final OrderServiceGrpc.OrderServiceBlockingStub orderService;
    private final IPaymentPreAuthClient paymentPreAuthClient;
    private final IBookOnlineDepositClient bookOnlineDepositClient;
    private final OBDepositHelper obDepositHelper;
    private final IAutoAssignService autoAssignService;
    private final IBoosterClient boosterClient;
    private final InvoiceDepositServiceGrpc.InvoiceDepositServiceBlockingStub invoiceDepositService;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementService;
    private final EvaluationService evaluationService;
    private final PetDetailUtil petDetailUtil;
    private final LodgingService lodgingService;
    private final ICustomerGroomingClient customerGroomingClient;
    private final IPetCodeClient petCodeClient;
    private final IPetVaccineClient petVaccineClient;
    private final IBusinessServiceAreaClient serviceAreaClient;
    private final PricingRuleApplyServiceGrpc.PricingRuleApplyServiceBlockingStub pricingRuleApplyService;
    private final BusinessPetCodeServiceGrpc.BusinessPetCodeServiceBlockingStub petCodeServiceBlockingStub;
    private final com.moego.api.v3.appointment.utils.PermissionUtil permissionUtil;
    private final BoardingSplitLodgingServiceGrpc.BoardingSplitLodgingServiceBlockingStub boardingSplitLodgingService;
    private final com.moego.idl.service.business_customer.v1.BusinessPetEvaluationServiceGrpc
                    .BusinessPetEvaluationServiceBlockingStub
            petEvaluationClient;
    private final FulfillmentReportServiceGrpc.FulfillmentReportServiceBlockingStub fulfillmentReportService;
    private final FeatureFlagApi featureFlagApi;

    public CompletableFuture<Map<Long, StaffModel>> getStaffMap(
            CompletableFuture<GetPetDetailListResponse> petDetailListFuture,
            CompletableFuture<Map<Long, List<ServiceOperationModel>>> serviceOperationMapFuture) {
        return petDetailListFuture.thenCombineAsync(
                serviceOperationMapFuture,
                (petDetailList, serviceOperationMap) -> {
                    var serviceStaffIds = Stream.concat(
                                    petDetailList.getPetDetailsList().stream().map(PetDetailModel::getStaffId),
                                    serviceOperationMap.values().stream()
                                            .flatMap(List::stream)
                                            .map(ServiceOperationModel::getStaffId))
                            .filter(staffId -> staffId > 0)
                            .distinct();
                    var evaluationStaffIds = petDetailList.getPetEvaluationsList().stream()
                            .filter(detail -> detail.hasStaffId() && detail.getStaffId() > 0)
                            .map(EvaluationServiceModel::getStaffId);
                    List<Long> staffIdList = Stream.concat(serviceStaffIds, evaluationStaffIds)
                            .distinct()
                            .toList();
                    if (CollectionUtils.isEmpty(staffIdList)) {
                        return Map.of();
                    }
                    return staffService
                            .queryStaffByIds(QueryStaffByIdsRequest.newBuilder()
                                    .addAllStaffIds(staffIdList)
                                    .build())
                            .getStaffsList()
                            .stream()
                            .collect(Collectors.toMap(StaffModel::getId, Function.identity()));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, StaffModel>> getStaffMap(
            List<PetDetailModel> petDetailsList, List<EvaluationServiceModel> petEvaluationsList) {
        return CompletableFuture.supplyAsync(
                () -> {
                    var serviceStaffIds = petDetailsList.stream()
                            .filter(detail -> List.of(
                                            ServiceItemType.GROOMING,
                                            ServiceItemType.EVALUATION,
                                            ServiceItemType.DOG_WALKING)
                                    .contains(detail.getServiceItemType()))
                            .map(PetDetailModel::getStaffId)
                            .filter(staffId -> staffId > 0);
                    var evaluationStaffIds = petEvaluationsList.stream()
                            .filter(detail -> detail.hasStaffId() && detail.getStaffId() > 0)
                            .map(EvaluationServiceModel::getStaffId);
                    List<Long> staffIdList = Stream.concat(serviceStaffIds, evaluationStaffIds)
                            .distinct()
                            .toList();
                    if (CollectionUtils.isEmpty(staffIdList)) {
                        return Map.of();
                    }
                    return staffService
                            .queryStaffByIds(QueryStaffByIdsRequest.newBuilder()
                                    .addAllStaffIds(staffIdList)
                                    .build())
                            .getStaffsList()
                            .stream()
                            .collect(Collectors.toMap(StaffModel::getId, Function.identity()));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<ServiceOperationModel>>> getServiceOperationMap(
            long companyId, long appointmentId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    List<ServiceOperationModel> serviceOperationList = serviceOperationService
                            .getServiceOperationList(GetServiceOperationListRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .addAppointmentIds(appointmentId)
                                    .build())
                            .getServiceOperationsList();
                    return serviceOperationList.stream()
                            .collect(Collectors.groupingBy(
                                    ServiceOperationModel::getGroomingServiceId, Collectors.toList()));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<ServiceOperationModel>>> getServiceOperationMap(
            long companyId, List<Long> appointmentIds) {
        return CompletableFuture.supplyAsync(
                () -> serviceOperationService
                        .getServiceOperationList(GetServiceOperationListRequest.newBuilder()
                                .setCompanyId(companyId)
                                .addAllAppointmentIds(appointmentIds)
                                .build())
                        .getServiceOperationsList()
                        .stream()
                        .collect(Collectors.groupingBy(ServiceOperationModel::getAppointmentId)),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, ServiceBriefView>> getServiceMap(
            long companyId, CompletableFuture<GetPetDetailListResponse> petDetailListFuture) {
        return petDetailListFuture.thenApplyAsync(
                result -> petDetailUtil.getServiceMap(result.getPetDetailsList(), companyId),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, ServiceBriefView>> getServiceMap(
            long companyId, List<PetDetailModel> petDetailsList) {
        return CompletableFuture.supplyAsync(
                () -> petDetailUtil.getServiceMap(petDetailsList, companyId), ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, ServiceBriefView>> getServiceMapByIds(long companyId, List<Long> serviceIds) {
        return CompletableFuture.supplyAsync(
                () -> ObjectUtils.isEmpty(serviceIds)
                        ? Map.of()
                        : serviceManagementService
                                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                                        .setCompanyId(companyId)
                                        .addAllServiceIds(serviceIds)
                                        .build())
                                .getServicesList()
                                .stream()
                                .collect(Collectors.toMap(ServiceBriefView::getId, Function.identity(), (a, b) -> a)),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, EvaluationBriefView>> getEvaluationMapByIds(List<Long> evaluationIds) {
        return CompletableFuture.supplyAsync(
                () -> evaluationService.getEvaluationMapByIds(evaluationIds), ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, EvaluationBriefView>> getEvaluationMap(
            CompletableFuture<GetPetDetailListResponse> petDetailListFuture) {
        return petDetailListFuture.thenApplyAsync(
                result -> evaluationService.getEvaluationMapByIds(result.getPetEvaluationsList().stream()
                        .map(EvaluationServiceModel::getServiceId)
                        .toList()),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, EvaluationBriefView>> getEvaluationMap(
            List<EvaluationServiceModel> petEvaluationsList) {
        return CompletableFuture.supplyAsync(
                () -> evaluationService.getEvaluationMapByIds(petEvaluationsList.stream()
                        .map(EvaluationServiceModel::getServiceId)
                        .distinct()
                        .toList()),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<PetVaccineComposite>>> getPetVaccineMap(
            CompletableFuture<GetPetDetailListResponse> petDetailListFuture, long companyId) {
        return petDetailListFuture.thenApplyAsync(
                result -> {
                    List<Long> petIdList = PetDetailUtil.getPetDetailPetIDs(
                            result.getPetDetailsList(), result.getPetEvaluationsList());
                    return petDetailUtil.getPetVaccineMap(petIdList, companyId);
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<VaccineComposite>>> getVaccineMap(
            CompletableFuture<GetPetDetailListResponse> petDetailListFuture, long companyId) {
        return petDetailListFuture.thenApplyAsync(
                result -> {
                    List<Long> petIdList = PetDetailUtil.getPetDetailPetIDs(
                            result.getPetDetailsList(), result.getPetEvaluationsList());
                    return petDetailUtil.getVaccineMap(petIdList, companyId);
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<BusinessPetIncidentReportModel>>> getIncidentReportMap(
            CompletableFuture<GetPetDetailListResponse> petDetailListFuture, long companyId) {
        return petDetailListFuture.thenApplyAsync(
                result -> {
                    List<Long> petIdList = PetDetailUtil.getPetDetailPetIDs(
                            result.getPetDetailsList(), result.getPetEvaluationsList());
                    return petDetailUtil.getPetIncidentReport(petIdList, companyId);
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<BusinessPetCodeModel>>> getPetCodeMap(
            CompletableFuture<GetPetDetailListResponse> petDetailListFuture, long companyId) {
        return petDetailListFuture.thenApplyAsync(
                result -> {
                    List<Long> petIdList = PetDetailUtil.getPetDetailPetIDs(
                            result.getPetDetailsList(), result.getPetEvaluationsList());
                    return petCodeService.getPetCodeMap(companyId, petIdList);
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<PetEvaluationModel>>> getPetEvaluationsMap(
            CompletableFuture<GetPetDetailListResponse> petDetailListFuture, long companyId) {
        return petDetailListFuture.thenApplyAsync(
                result -> {
                    List<Long> petIdList = PetDetailUtil.getPetDetailPetIDs(
                            result.getPetDetailsList(), result.getPetEvaluationsList());
                    if (CollectionUtils.isEmpty(petIdList)) {
                        return Map.of();
                    }
                    var petEvaluations = petEvaluationClient
                            .listPetEvaluation(ListPetEvaluationRequest.newBuilder()
                                    .setTenant(Tenant.newBuilder()
                                            .setCompanyId(companyId)
                                            .build())
                                    .addAllPetIds(petIdList)
                                    .build())
                            .getPetEvaluationsList();
                    if (CollectionUtils.isEmpty(petEvaluations)) {
                        return Map.of();
                    }
                    var groupedMap =
                            petEvaluations.stream().collect(Collectors.groupingBy(PetEvaluationModel::getPetId));
                    petIdList.forEach(petId -> groupedMap.putIfAbsent(petId, List.of()));
                    return groupedMap;
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<BusinessPetCodeModel>>> getPetCodeMap(List<Long> petIds, long companyId) {
        if (CollectionUtils.isEmpty(petIds)) {
            return CompletableFuture.completedFuture(Map.of());
        }

        return CompletableFuture.supplyAsync(
                () -> petCodeService.getPetCodeMap(
                        companyId, petIds.stream().distinct().toList()),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Pair<List<BatchListBindingPetCodeResponse.Binding>, List<BusinessPetCodeModel>>>
            getPetCodeBindingMap(List<Long> petIds, long companyId) {
        if (CollectionUtils.isEmpty(petIds)) {
            return CompletableFuture.completedFuture(Pair.of(List.of(), List.of()));
        }

        return CompletableFuture.supplyAsync(
                () -> petCodeService.listPetCodeBindings(
                        companyId, petIds.stream().distinct().toList()),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<ServiceDetailOverview.Binding>>> getPetCodeBindingsMap(
            CompletableFuture<GetPetDetailListResponse> petDetailListFuture, long companyId) {
        return petDetailListFuture.thenApplyAsync(
                result -> {
                    List<Long> petIds = PetDetailUtil.getPetDetailPetIDs(
                            result.getPetDetailsList(), result.getPetEvaluationsList());
                    if (CollectionUtils.isEmpty(petIds)) {
                        return Map.of();
                    }
                    var list = petCodeServiceBlockingStub
                            .batchListBindingPetCode(BatchListBindingPetCodeRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .addAllPetIds(petIds)
                                    .build())
                            .getPetCodeBindingsList();

                    return list.stream()
                            .map(b -> ServiceDetailOverview.Binding.newBuilder()
                                    .setPetId(b.getPetId())
                                    .setCodeId(b.getCodeId())
                                    .setBindingTime(b.getBindingTime())
                                    .setComment(b.getComment())
                                    .build())
                            .collect(Collectors.groupingBy(ServiceDetailOverview.Binding::getPetId));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<BusinessPetNoteModel>>> getPetNoteMap(
            CompletableFuture<GetPetDetailListResponse> petDetailListFuture) {
        return petDetailListFuture.thenApplyAsync(
                result -> {
                    List<Long> petIdList = PetDetailUtil.getPetDetailPetIDs(
                            result.getPetDetailsList(), result.getPetEvaluationsList());
                    return petDetailUtil.getPetNoteMap(petIdList);
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, CustomerPetPetCodeDTO>> getPetInfoWithCodeMap(List<Long> petIds) {
        return CompletableFuture.supplyAsync(
                () -> ObjectUtils.isEmpty(petIds)
                        ? Map.of()
                        : petCodeClient
                                .getCustomerPetPetCodeListByIdList(
                                        false,
                                        petIds.stream().map(Long::intValue).toList())
                                .stream()
                                .collect(Collectors.toMap(
                                        pet -> pet.getPetId().longValue(), Function.identity(), (a, b) -> a)),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<VaccineBindingRecordDto>>> getPetVaccineMap(List<Long> petIds) {
        return CompletableFuture.supplyAsync(
                () -> ObjectUtils.isEmpty(petIds)
                        ? Map.of()
                        : petVaccineClient
                                .getVaccineInfoByPetIdList(
                                        petIds.stream().map(Long::intValue).toList())
                                .entrySet()
                                .stream()
                                .collect(
                                        Collectors.toMap(entry -> entry.getKey().longValue(), Map.Entry::getValue)),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Boolean> getRequiredSign(long businessId, AppointmentModel appointment) {
        return CompletableFuture.supplyAsync(
                () -> {
                    BatchGetAgreementUnsignedAppointmentResponse response =
                            agreementService.batchGetAgreementUnsignedAppointment(
                                    BatchGetAgreementUnsignedAppointmentRequest.newBuilder()
                                            .setBusinessId(businessId)
                                            .addCustomerWithAppointmentId(
                                                    BatchGetAgreementUnsignedAppointmentRequest
                                                            .CustomerWithAppointmentId.newBuilder()
                                                            .setCustomerId(appointment.getCustomerId())
                                                            .setAppointmentId(appointment.getId())
                                                            .build())
                                            .build());
                    List<Long> unsignedAppointmentIds = response.getAppointmentIdList();
                    return !CollectionUtils.isEmpty(unsignedAppointmentIds)
                            && unsignedAppointmentIds.contains(appointment.getId());
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Set<Long>> getRequiredSignAppointmentIdSet(
            long businessId, Map<Long, Long> appointmentIdToCustomerIdMap) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(appointmentIdToCustomerIdMap)) {
                        return Set.of();
                    }
                    List<BatchGetAgreementUnsignedAppointmentRequest.CustomerWithAppointmentId>
                            customerWithAppointmentIds = appointmentIdToCustomerIdMap.entrySet().stream()
                                    .map(entry ->
                                            BatchGetAgreementUnsignedAppointmentRequest.CustomerWithAppointmentId
                                                    .newBuilder()
                                                    .setAppointmentId(entry.getKey())
                                                    .setCustomerId(entry.getValue())
                                                    .build())
                                    .distinct()
                                    .toList();
                    BatchGetAgreementUnsignedAppointmentResponse response =
                            agreementService.batchGetAgreementUnsignedAppointment(
                                    BatchGetAgreementUnsignedAppointmentRequest.newBuilder()
                                            .setBusinessId(businessId)
                                            .addAllCustomerWithAppointmentId(customerWithAppointmentIds)
                                            .build());
                    return new HashSet<>(response.getAppointmentIdList());
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, BusinessCustomerPetModel>> getPetMap(
            long companyId, CompletableFuture<GetPetDetailListResponse> petDetailListFuture) {
        return petDetailListFuture.thenApplyAsync(
                result ->
                        petDetailUtil.getPetMap(result.getPetDetailsList(), result.getPetEvaluationsList(), companyId),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, BusinessCustomerPetInfoModel>> getPetMap(
            long companyId, List<PetDetailModel> petDetailsList, List<EvaluationServiceModel> petEvaluationsList) {
        return CompletableFuture.supplyAsync(
                () -> petDetailUtil.getPetInfoMap(petDetailsList, petEvaluationsList, companyId),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<List<BusinessCustomerPetInfoModel>> getPetInfoList(List<Long> petIds, long companyId) {
        if (CollectionUtils.isEmpty(petIds)) {
            return CompletableFuture.completedFuture(List.of());
        }

        return CompletableFuture.supplyAsync(
                () -> businessCustomerPetService
                        .batchGetPetInfo(BatchGetPetInfoRequest.newBuilder()
                                .setTenant(Tenant.newBuilder()
                                        .setCompanyId(companyId)
                                        .build())
                                .addAllIds(petIds)
                                .build())
                        .getPetsList(),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<List<AppointmentModel>> getAppointmentList(List<Long> appointmentIds, long companyId) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return CompletableFuture.completedFuture(List.of());
        }

        return CompletableFuture.supplyAsync(
                () -> appointmentService
                        .getAppointmentList(GetAppointmentListRequest.newBuilder()
                                .addAllAppointmentId(appointmentIds)
                                .setCompanyId(companyId)
                                .build())
                        .getAppointmentsList(),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<PetDetailModel>>> getPetDetailMap(
            List<Long> appointmentIds, long companyId) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return CompletableFuture.completedFuture(Map.of());
        }

        return CompletableFuture.supplyAsync(
                () -> petDetailUtil.getPetDetailMapByPet(companyId, appointmentIds), ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<GetPetDetailListResponse> getPetDetailList(
            long companyId, Collection<Long> appointmentIdList) {
        return CompletableFuture.supplyAsync(
                () -> petDetailService.getPetDetailList(GetPetDetailListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllAppointmentIds(appointmentIdList)
                        .build()),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<GetPetDetailListResponse> getPetDetailListWithActualDates(
            long companyId, Collection<Long> appointmentIdList) {
        return CompletableFuture.supplyAsync(
                () -> petDetailService.getPetDetailList(GetPetDetailListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllAppointmentIds(appointmentIdList)
                        .setWithActualDates(true)
                        .build()),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<List<PricingRuleApplyLogModel>> getPricingRuleApplyLogList(
            long companyId, Long appointmentId, CompletableFuture<GetPetDetailListResponse> petDetailListFuture) {
        return petDetailListFuture.thenApplyAsync(
                result -> pricingRuleApplyService
                        .listPricingRuleApplyLog(ListPricingRuleApplyLogRequest.newBuilder()
                                .setCompanyId(companyId)
                                .setSourceId(appointmentId)
                                .setSourceType(PricingRuleApplySourceType.SOURCE_TYPE_APPOINTMENT)
                                .build())
                        .getPricingRuleApplyLogsList()
                        .stream()
                        .map(log -> PricingRuleUtil.process(log, result.getPetDetailsList()))
                        .collect(Collectors.toMap(
                                log -> log.getPetId()
                                        + log.getServiceId()
                                        + log.getPricingRule().getId(),
                                Function.identity(),
                                (a, b) -> a))
                        .values()
                        .stream()
                        .toList(),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<AppointmentNoteModel>>> getAppointmentNoteMap(
            long companyId, Collection<Long> appointmentIdList) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(appointmentIdList)) {
                        return Map.of();
                    }
                    GetAppointmentNoteListResponse appointmentNoteListResponse =
                            appointmentNoteService.getAppointmentNoteList(GetAppointmentNoteListRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .addAllAppointmentIds(appointmentIdList)
                                    .build());
                    List<AppointmentNoteModel> notesList = new ArrayList<>(appointmentNoteListResponse.getNotesList());
                    // 兼容逻辑，当 ticket comment 不存在时，尝试获取 additional note，来代替 ticket comment
                    boolean noComment = notesList.stream()
                            .noneMatch(note -> Objects.equals(AppointmentNoteType.COMMENT, note.getType()));
                    Optional<AppointmentNoteModel> additionalNote = notesList.stream()
                            .filter(note -> Objects.equals(AppointmentNoteType.ADDITIONAL, note.getType()))
                            .findAny();
                    if (noComment && additionalNote.isPresent()) {
                        AppointmentNoteModel appointmentNoteModel = additionalNote.get().toBuilder()
                                .setType(AppointmentNoteType.ADDITIONAL)
                                .build();
                        notesList.add(appointmentNoteModel);
                    }
                    return notesList.stream().collect(Collectors.groupingBy(AppointmentNoteModel::getGroomingId));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<AppointmentNoteModel> getCustomerLastAlertNoteFuture(long companyId, long customerId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    GetCustomerLastNoteResponse response =
                            appointmentNoteService.getCustomerLastNote(GetCustomerLastNoteRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .setCustomerId(customerId)
                                    .setType(AppointmentNoteType.ALERT_NOTES)
                                    .build());
                    return response.getNote();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<BusinessCustomerAddressModel> getCustomerPrimaryAddress(long companyId, long customerId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    GetCustomerPrimaryAddressResponse customerPrimaryAddressResponse =
                            businessCustomerAddressService.getCustomerPrimaryAddress(
                                    GetCustomerPrimaryAddressRequest.newBuilder()
                                            .setTenant(Tenant.newBuilder()
                                                    .setCompanyId(companyId)
                                                    .build())
                                            .setCustomerId(customerId)
                                            .build());
                    return customerPrimaryAddressResponse.getAddress();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, BusinessCustomerAddressModel>> getCustomerPrimaryAddressMap(
            long companyId, Collection<Long> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return CompletableFuture.completedFuture(Map.of());
        }
        return CompletableFuture.supplyAsync(
                () -> businessCustomerAddressService
                        .batchGetCustomerPrimaryAddress(BatchGetCustomerPrimaryAddressRequest.newBuilder()
                                .setTenant(Tenant.newBuilder()
                                        .setCompanyId(companyId)
                                        .build())
                                .addAllCustomerIds(customerIds)
                                .build())
                        .getAddressesMap(),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<BusinessCustomerModel> getBusinessCustomerModel(long companyId, long customerId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    GetCustomerResponse customerResponse =
                            businessCustomerService.getCustomer(GetCustomerRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .setId(customerId)
                                    .build());
                    final var customer = customerResponse.getCustomer();

                    boolean needHideClientPhoneAndEmail = permissionUtil.needHideClientPhoneAndEmail(
                            companyId, AuthContext.get().staffId());
                    if (!needHideClientPhoneAndEmail) {
                        return customer;
                    }

                    return customer.toBuilder()
                            .setPhoneNumber(PermissionUtil.phoneNumberConfusion(customer.getPhoneNumber()))
                            .setEmail(PermissionUtil.emailConfusion(customer.getEmail()))
                            .build();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, BusinessCustomerModel>> getBusinessCustomerMap(
            long companyId, long staffId, Collection<Long> customerIdList) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(customerIdList)) {
                        return Map.of();
                    }
                    BatchGetCustomerResponse customerResponse =
                            businessCustomerService.batchGetCustomer(BatchGetCustomerRequest.newBuilder()
                                    .setTenant(Tenant.newBuilder()
                                            .setCompanyId(companyId)
                                            .build())
                                    .addAllIds(customerIdList)
                                    .build());
                    final var customerList = customerResponse.getCustomersList();

                    boolean needHideClientPhoneAndEmail =
                            permissionUtil.needHideClientPhoneAndEmail(companyId, staffId);
                    if (!needHideClientPhoneAndEmail) {
                        return customerList.stream()
                                .collect(Collectors.toMap(BusinessCustomerModel::getId, Function.identity()));
                    }

                    return customerList.stream()
                            .map(customer -> customer.toBuilder()
                                    .setPhoneNumber(PermissionUtil.phoneNumberConfusion(customer.getPhoneNumber()))
                                    .setEmail(PermissionUtil.emailConfusion(customer.getEmail()))
                                    .build())
                            .collect(Collectors.toMap(BusinessCustomerModel::getId, Function.identity()));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, BusinessCustomerInfoModel>> getBusinessCustomerInfoMap(
            long companyId, Collection<Long> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return CompletableFuture.completedFuture(Map.of());
        }
        return CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(customerIds)) {
                        return Map.of();
                    }
                    var customerResponse =
                            businessCustomerService.batchGetCustomerInfo(BatchGetCustomerInfoRequest.newBuilder()
                                    .setTenant(Tenant.newBuilder()
                                            .setCompanyId(companyId)
                                            .build())
                                    .addAllIds(customerIds)
                                    .build());
                    final var customerList = customerResponse.getCustomersList();

                    boolean needHideClientPhoneAndEmail = permissionUtil.needHideClientPhoneAndEmail(
                            companyId, AuthContext.get().staffId());
                    if (!needHideClientPhoneAndEmail) {
                        return customerList.stream()
                                .collect(Collectors.toMap(BusinessCustomerInfoModel::getId, Function.identity()));
                    }

                    return customerList.stream()
                            .map(customer -> customer.toBuilder()
                                    .setPhoneNumber(PermissionUtil.phoneNumberConfusion(customer.getPhoneNumber()))
                                    .setEmail(PermissionUtil.emailConfusion(customer.getEmail()))
                                    .build())
                            .collect(Collectors.toMap(BusinessCustomerInfoModel::getId, Function.identity()));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>>>
            getLodgingMap(List<PetDetailModel> petDetailsList, List<EvaluationServiceModel> petEvaluationsList) {
        return CompletableFuture.supplyAsync(
                () -> lodgingService.getLodgingMap(
                        PetDetailUtil.getPetDetailLodgingIDs(petDetailsList, petEvaluationsList)),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>>>
            getLodgingMap(
                    List<PetDetailModel> petDetailsList,
                    List<EvaluationServiceModel> petEvaluationsList,
                    CompletableFuture<Map<Long, List<BoardingSplitLodgingModel>>> boardingSplitLodgingFuture) {
        return boardingSplitLodgingFuture.thenApplyAsync(
                result -> lodgingService.getLodgingMap(PetDetailUtil.getPetDetailLodgingIDs(
                        petDetailsList,
                        petEvaluationsList,
                        result.values().stream().flatMap(List::stream).toList())),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>>>
            getLodgingMap(CompletableFuture<GetPetDetailListResponse> petDetailListFuture) {
        return petDetailListFuture.thenApplyAsync(
                result -> lodgingService.getLodgingMap(PetDetailUtil.getPetDetailLodgingIDs(
                        result.getPetDetailsList(), result.getPetEvaluationsList())),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>>>
            getLodgingMap(
                    CompletableFuture<GetPetDetailListResponse> petDetailListFuture,
                    CompletableFuture<Map<Long, List<BoardingSplitLodgingModel>>> boardingSplitLodgingFuture) {

        return petDetailListFuture.thenApplyAsync(
                result -> {
                    Map<Long, List<BoardingSplitLodgingModel>> boardingSplitLodgingMap =
                            boardingSplitLodgingFuture.join();
                    return lodgingService.getLodgingMap(PetDetailUtil.getPetDetailLodgingIDs(
                            result.getPetDetailsList(),
                            result.getPetEvaluationsList(),
                            boardingSplitLodgingMap.values().stream()
                                    .flatMap(List::stream)
                                    .toList()));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, AppointmentModel>> getCustomerLastAppointmentMap(
            long companyId, List<Long> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return CompletableFuture.completedFuture(Map.of());
        }
        return CompletableFuture.supplyAsync(
                () -> {
                    GetCustomerLastAppointmentResponse lastAppointmentResponse =
                            appointmentService.getCustomerLastAppointment(GetCustomerLastAppointmentRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .addAllCustomerId(customerIds)
                                    .build());
                    return lastAppointmentResponse.getCustomerLastAppointmentMap();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<List<BusinessCustomerTagModel>> getCustomerTags(long companyId, long customerId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    ListBindingCustomerTagResponse listBindingCustomerTagResponse =
                            businessCustomerTagService.listBindingCustomerTag(ListBindingCustomerTagRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .setCustomerId(customerId)
                                    .build());
                    return listBindingCustomerTagResponse.getTagsList();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Boolean> getIsNewCustomer(long companyId, long customerId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    GetCustomerLastAppointmentResponse customerLastAppointmentResponse =
                            appointmentService.getCustomerLastAppointment(GetCustomerLastAppointmentRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .addCustomerId(customerId)
                                    .setStatus(AppointmentStatus.FINISHED)
                                    .build());
                    return CollectionUtils.isEmpty(customerLastAppointmentResponse.getCustomerLastAppointmentMap())
                            || Objects.isNull(customerLastAppointmentResponse
                                    .getCustomerLastAppointmentMap()
                                    .get(customerId));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<List<Long>> getNewCustomerIdList(long companyId, Collection<Long> customerIdList) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(customerIdList)) {
                        return List.of();
                    }
                    GetCustomerLastAppointmentResponse customerLastAppointmentResponse =
                            appointmentService.getCustomerLastAppointment(GetCustomerLastAppointmentRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .addAllCustomerId(customerIdList)
                                    .setStatus(AppointmentStatus.FINISHED)
                                    .build());
                    return customerIdList.stream()
                            .filter(customerId -> CollectionUtils.isEmpty(
                                            customerLastAppointmentResponse.getCustomerLastAppointmentMap())
                                    || Objects.isNull(customerLastAppointmentResponse
                                            .getCustomerLastAppointmentMap()
                                            .get(customerId)))
                            .toList();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<CertainAreaDTO>>> getClientAreaMap(
            Long businessId, CompletableFuture<Map<Long, GroomingCalenderCustomerInfo>> customerFuture) {
        return customerFuture.thenApplyAsync(result -> {
            List<GetAreasByLocationParams> locationParams = result.values().stream()
                    .filter(customer ->
                            StringUtils.hasText(customer.getLat()) && StringUtils.hasText(customer.getLng()))
                    .map(customer -> new GetAreasByLocationParams(
                            customer.getCustomerId().longValue(),
                            customer.getLat(),
                            customer.getLng(),
                            customer.getZipcode()))
                    .toList();
            if (CollectionUtils.isEmpty(locationParams)) {
                return Map.of();
            }
            return serviceAreaClient.getAreasByLocation(
                    new BatchGetAreasByLocationParams(businessId, null, locationParams));
        });
    }

    public CompletableFuture<WaitListCalendarView> getWaitListCalendarView(
            AppointmentModel appointment, long companyId, long appointmentId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    WaitListCalendarView.Builder waitListBuilder = WaitListCalendarView.newBuilder();
                    if (appointment.getWaitListStatus().equals(WaitListStatus.WAITLISTONLY)
                            || appointment.getWaitListStatus().equals(WaitListStatus.APPTANDWAITLIST)) {
                        GetWaitListByAppointmentResponse appointmentWaitListResponse =
                                waitListService.getWaitListByAppointment(GetWaitListByAppointmentRequest.newBuilder()
                                        .setCompanyId(companyId)
                                        .addAppointmentIds(appointmentId)
                                        .build());
                        WaitListCalendarView appointmentWaitList =
                                appointmentWaitListResponse.getWaitListMap().get(appointmentId);
                        if (Objects.nonNull(appointmentWaitList)) {
                            waitListBuilder.setId(appointmentWaitList.getId()).setAppointmentId(appointmentId);
                        }
                    }
                    return waitListBuilder.build();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, WaitListCalendarView>> getWaitListCalendarViewMap(
            long companyId, Collection<Long> appointmentIdList) {
        return CompletableFuture.supplyAsync(
                () -> {
                    GetWaitListByAppointmentResponse appointmentWaitListResponse =
                            waitListService.getWaitListByAppointment(GetWaitListByAppointmentRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .addAllAppointmentIds(appointmentIdList)
                                    .build());
                    return appointmentWaitListResponse.getWaitListMap();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Set<Long>> getReviewBoosterSentAppointmentIdList(
            long businessId, Collection<Long> appointmentIdList) {
        return CompletableFuture.supplyAsync(
                () -> boosterClient
                        .getAppointmentReviewRecords(
                                Math.toIntExact(businessId),
                                ReviewBoosterConst.REVIEW_SOURCE_SMS,
                                appointmentIdList.stream().map(Long::intValue).toList())
                        .stream()
                        .map(ReviewBoosterRecordDTO::getAppointmentId)
                        .map(Long::valueOf)
                        .collect(Collectors.toSet()),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Boolean> getReviewBoosterSent(long businessId, AppointmentModel appointment) {
        return CompletableFuture.supplyAsync(
                () -> {
                    List<ReviewBoosterRecordDTO> reviewRecords =
                            boosterClient.getAppointmentReviewRecord(new GetReviewBoosterRecordParams()
                                    .setBusinessId(Math.toIntExact(businessId))
                                    .setCustomerId(Math.toIntExact(appointment.getCustomerId()))
                                    .setGroomingId(Math.toIntExact(appointment.getId()))
                                    .setSource(ReviewBoosterConst.REVIEW_SOURCE_SMS));
                    return !CollectionUtils.isEmpty(reviewRecords);
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<AutoAssignDTO> getAutoAssign(AppointmentModel appointment) {
        return CompletableFuture.supplyAsync(
                () -> {
                    AutoAssignDTO autoAssign = autoAssignService.getAutoAssign(Math.toIntExact(appointment.getId()));
                    if (Objects.isNull(autoAssign)) {
                        return new AutoAssignDTO();
                    }
                    return autoAssign;
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<PreAuthDTO> getPreAuth(long businessId, AppointmentModel appointment) {
        return CompletableFuture.supplyAsync(
                () -> {
                    PreAuthDTO preAuthDTO = paymentPreAuthClient.queryByTicketId(
                            Math.toIntExact(businessId), Math.toIntExact(appointment.getId()));
                    if (Objects.isNull(preAuthDTO)) {
                        preAuthDTO = new PreAuthDTO();
                    }
                    return preAuthDTO;
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Integer, PreAuthDTO>> getPreAuthMap(
            long businessId, Collection<Long> appointmentIdList) {
        return CompletableFuture.supplyAsync(
                () -> ObjectUtils.isEmpty(appointmentIdList)
                        ? Map.of()
                        : paymentPreAuthClient
                                .batchQueryByTicketIds(BatchQueryPreAuthParams.builder()
                                        .businessId(Math.toIntExact(businessId))
                                        .ticketIds(appointmentIdList.stream()
                                                .map(Long::intValue)
                                                .toList())
                                        .build())
                                .stream()
                                .collect(Collectors.toMap(PreAuthDTO::getTicketId, Function.identity(), (a, b) -> a)),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<PrePayCalendarView> getPrePayCalendarView(
            CompletableFuture<InvoiceCalendarView> invoiceFuture,
            CompletableFuture<BookOnlineDepositDTO> depositFuture) {
        return invoiceFuture.thenCombineAsync(
                depositFuture,
                (invoice, deposit) -> {
                    PrePayCalendarView.Builder builder = PrePayCalendarView.newBuilder();
                    if (!Objects.isNull(deposit) && DepositPaymentTypeEnum.PrePay.equals(deposit.getDepositType())) {
                        builder.setPrePayAmount(deposit.getAmount().doubleValue())
                                .setPrePayStatus(deposit.getStatus())
                                .setPrePayRate(
                                        PrePayConverter.INSTANCE.getPrePayRate(deposit, invoice.getTotalAmount()));
                    }
                    return builder.build();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<BookOnlineDepositDTO> getBookOnlineDeposit(long businessId, AppointmentModel appointment) {
        return CompletableFuture.supplyAsync(() -> obDepositHelper.getOBDeposit(businessId, appointment.getId()));
    }

    public CompletableFuture<Map<Long, BookOnlineDepositDTO>> getBookOnlineDepositMap(List<Long> appointmentIds) {
        return CompletableFuture.supplyAsync(
                () -> ObjectUtils.isEmpty(appointmentIds)
                        ? Map.of()
                        : bookOnlineDepositClient
                                .getOBDepositByGroomingIds(appointmentIds.stream()
                                        .map(Long::intValue)
                                        .toList())
                                .stream()
                                .collect(Collectors.toMap(
                                        deposit -> deposit.getGroomingId().longValue(),
                                        Function.identity(),
                                        (a, b) -> a)),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<NoShowInvoiceCalendarView> getNoShowInvoiceCalendarView(long appointmentId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    OrderDetailModel noShowInvoice = orderService.getOrderDetail(GetOrderRequest.newBuilder()
                            .setSourceId(appointmentId)
                            .setSourceType(InvoiceStatusEnum.TYPE_NOSHOW)
                            .setLatest(true)
                            .build());
                    return NoShowInvoiceCalendarView.newBuilder()
                            .setInvoiceId(noShowInvoice.getOrder().getId())
                            .setStatus(noShowInvoice.getOrder().getStatus())
                            .build();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, NoShowInvoiceCalendarView>> getNoShowInvoiceCalendarViewMap(
            List<Long> appointmentIdList) {
        return CompletableFuture.supplyAsync(
                () -> {
                    return orderService
                            .getOrderList(GetOrderListRequest.newBuilder()
                                    .addAllSourceIds(appointmentIdList)
                                    .setSourceType(InvoiceStatusEnum.TYPE_NOSHOW)
                                    .build())
                            .getOrderListList()
                            .stream()
                            .collect(Collectors.toMap(
                                    order -> order.getOrder().getSourceId(),
                                    order -> NoShowInvoiceCalendarView.newBuilder()
                                            .setInvoiceId(order.getOrder().getId())
                                            .setStatus(order.getOrder().getStatus())
                                            .build(),
                                    (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, PreviewEstimateOrderResponse.EstimatedOrder>> listEstimatedOrder(
            long companyId, long businessId, List<Long> appointmentIds) {
        return CompletableFuture.supplyAsync(
                () -> appointmentService
                        .previewEstimateOrder(PreviewEstimateOrderRequest.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .addAllAppointmentIds(appointmentIds)
                                .build())
                        .getEstimatedOrdersList()
                        .stream()
                        .collect(Collectors.toMap(
                                PreviewEstimateOrderResponse.EstimatedOrder::getAppointmentId, Function.identity())),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<OrderModelAppointmentView>>> listOrders(List<Long> appointmentIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(appointmentIds)) {
                        return Map.of();
                    }
                    var appointmentIdToOrders = orderService
                            .getOrderList(GetOrderListRequest.newBuilder()
                                    .addAllSourceIds(appointmentIds)
                                    .setSourceType(InvoiceStatusEnum.TYPE_APPOINTMENT)
                                    .setIncludeExtraOrder(true)
                                    .build())
                            .getOrderListList()
                            .stream()
                            .map(OrderDetailModel::getOrder)
                            .collect(Collectors.groupingBy(OrderModel::getSourceId));
                    return appointmentIdToOrders.entrySet().stream()
                            .collect(Collectors.toMap(
                                    Map.Entry::getKey, entry -> OrderConverter.INSTANCE.toView(entry.getValue())));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<List<OrderDetailModelV1>> listOrdersByAppointmentId(long companyId, long appointmentId) {
        return CompletableFuture.supplyAsync(
                () -> orderService
                        .queryOrderDetail(QueryOrderDetailRequest.newBuilder()
                                .setCompanyId(companyId)
                                .setSourceId(appointmentId)
                                .setSourceType(com.moego.idl.models.order.v1.OrderSourceType.APPOINTMENT)
                                .build())
                        .getOrdersList(),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<InvoiceCalendarView> getInvoiceCalendarView(long appointmentId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    var orderList = orderService
                            .getOrderList(GetOrderListRequest.newBuilder()
                                    .setSourceType(InvoiceStatusEnum.TYPE_APPOINTMENT)
                                    .addSourceIds(appointmentId)
                                    .setIncludeExtraOrder(true)
                                    .build())
                            .getOrderListList();
                    // 直接选择最早的一张单子
                    var order = orderList.stream()
                            .reduce((first, current) -> current.getOrder().getCreateTime()
                                            < first.getOrder().getCreateTime()
                                    ? current
                                    : first)
                            .orElse(OrderDetailModel.newBuilder().build())
                            .getOrder();
                    return InvoiceCalendarView.newBuilder()
                            .setInvoiceId(order.getId())
                            .setStatus(order.getStatus())
                            .setPaidAmount(order.getPaidAmount())
                            .setRefundedAmount(order.getRefundedAmount())
                            .setTotalAmount(order.getTotalAmount())
                            .setSubTotalAmount(order.getSubTotalAmount())
                            .setOutstandingBalance(OrderUtil.getOutstandingBalance(order))
                            .setPaymentStatus(convertPaymentStatus(order.getPaymentStatus()))
                            .build();
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, OrderModel>> getOrderModelMap(List<Long> appointmentIds) {
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return CompletableFuture.completedFuture(Map.of());
        }
        return CompletableFuture.supplyAsync(
                () -> orderService
                        .getOrderList(GetOrderListRequest.newBuilder()
                                .addAllSourceIds(appointmentIds)
                                .setSourceType(OrderSourceType.APPOINTMENT.getSource())
                                .build())
                        .getOrderListList()
                        .stream()
                        .collect(Collectors.toMap(
                                order -> order.getOrder().getSourceId(), OrderDetailModel::getOrder, (a, b) -> a)),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, InvoiceCalendarView>> getInvoiceCalendarViewMap(
            Collection<Long> appointmentIdList) {
        return CompletableFuture.supplyAsync(
                () -> ObjectUtils.isEmpty(appointmentIdList)
                        ? Map.of()
                        : orderService
                                .getOrderList(GetOrderListRequest.newBuilder()
                                        .addAllSourceIds(appointmentIdList)
                                        .setSourceType(InvoiceStatusEnum.TYPE_APPOINTMENT)
                                        .build())
                                .getOrderListList()
                                .stream()
                                .collect(Collectors.toMap(
                                        order -> order.getOrder().getSourceId(),
                                        order -> InvoiceCalendarView.newBuilder()
                                                .setInvoiceId(order.getOrder().getId())
                                                .setPaidAmount(order.getOrder().getPaidAmount())
                                                .setRefundedAmount(
                                                        order.getOrder().getRefundedAmount())
                                                .setTotalAmount(order.getOrder().getTotalAmount())
                                                .setSubTotalAmount(
                                                        order.getOrder().getSubTotalAmount())
                                                .setStatus(order.getOrder().getStatus())
                                                .setOutstandingBalance(
                                                        OrderUtil.getOutstandingBalance(order.getOrder()))
                                                .setPaymentStatus(convertPaymentStatus(
                                                        order.getOrder().getPaymentStatus()))
                                                .build(),
                                        (a, b) -> a)),
                ThreadPool.getSubmitExecutor());
    }

    private OrderModel.PaymentStatus convertPaymentStatus(String paymentStatus) {
        return StringUtils.hasLength(paymentStatus)
                ? OrderModel.PaymentStatus.valueOf(paymentStatus)
                : OrderModel.PaymentStatus.PAYMENT_STATUS_UNSPECIFIED;
    }

    public CompletableFuture<List<ServiceItemType>> getServiceTypeFuture(AppointmentModel appointment) {
        return CompletableFuture.supplyAsync(
                () -> ServiceItemEnum.convertBitValueList(appointment.getServiceTypeInclude()).stream()
                        .map(ServiceItemEnum::getServiceItem)
                        .map(ServiceItemType::forNumber)
                        .toList(),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Boolean> getStartAtSameTime(
            CompletableFuture<GetPetDetailListResponse> petDetailListFuture) {
        return petDetailListFuture.thenApplyAsync(
                result -> {
                    // 目前 evaluation 没有 startAtTheSameTime
                    if (!CollectionUtils.isEmpty(result.getPetEvaluationsList())) {
                        return false;
                    }
                    if (1
                            == result.getPetDetailsList().stream()
                                    .map(PetDetailModel::getPetId)
                                    .distinct()
                                    .count()) {
                        return false;
                    }
                    return result.getPetDetailsList().stream()
                                    .filter(petDetailModel -> Objects.equals(
                                                    petDetailModel.getServiceItemType(), ServiceItemType.GROOMING)
                                            || Objects.equals(
                                                    petDetailModel.getServiceItemType(), ServiceItemType.DOG_WALKING))
                                    .collect(Collectors.toMap(
                                            PetDetailModel::getPetId, PetDetailModel::getStartTime, Integer::min))
                                    .values()
                                    .stream()
                                    .distinct()
                                    .count()
                            == 1;
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, InvoiceDepositModel>> getInvoiceDepositMap(
            long companyId, CompletableFuture<Map<Long, InvoiceCalendarView>> invoiceMapFuture) {
        return invoiceMapFuture.thenApplyAsync(
                result -> {
                    List<Long> invoiceIdList = result.values().stream()
                            .map(InvoiceCalendarView::getInvoiceId)
                            .toList();
                    if (CollectionUtils.isEmpty(invoiceIdList)) {
                        return Map.of();
                    }
                    Map<Long, InvoiceDepositModel> responseMap = invoiceDepositService
                            .getInvoiceDepositList(GetInvoiceDepositListRequest.newBuilder()
                                    .setCompanyId(companyId)
                                    .addAllInvoiceIds(invoiceIdList)
                                    .build())
                            .getInvoiceDepositMap();
                    return result.entrySet().stream()
                            .collect(Collectors.toMap(
                                    Map.Entry::getKey,
                                    entry -> responseMap.getOrDefault(
                                            entry.getValue().getInvoiceId(),
                                            InvoiceDepositModel.newBuilder().build())));
                },
                ThreadPool.getSubmitExecutor());
    }

    /**
     * 根据条件获取预约数据
     * 最多获取 2000 条数据 <a href="https://moegoworkspace.slack.com/archives/C06AXC3T4CB/p1723109545560079"/>
     */
    public CompletableFuture<List<AppointmentModel>> listAppointments(
            long companyId, List<Long> businessIds, ListAppointmentsRequest.Filter filter) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (ObjectUtils.isEmpty(businessIds)) {
                        return List.of();
                    }
                    int pageNum = 1;
                    int pageSize = 1000;
                    ListAppointmentsResponse response =
                            appointmentService.listAppointments(ListAppointmentsRequest.newBuilder()
                                    .setPagination(PaginationRequest.newBuilder()
                                            .setPageNum(pageNum)
                                            .setPageSize(pageSize))
                                    .setCompanyId(companyId)
                                    .addAllBusinessIds(businessIds)
                                    .setFilter(filter)
                                    .build());
                    List<AppointmentModel> appointmentList = new ArrayList<>(response.getAppointmentsList());
                    if (response.getPagination().getTotal() > pageSize) {
                        ListAppointmentsResponse listAppointmentsResponse =
                                appointmentService.listAppointments(ListAppointmentsRequest.newBuilder()
                                        .setPagination(PaginationRequest.newBuilder()
                                                .setPageNum(++pageNum)
                                                .setPageSize(pageSize))
                                        .setCompanyId(companyId)
                                        .addAllBusinessIds(businessIds)
                                        .setFilter(filter)
                                        .build());
                        appointmentList.addAll(listAppointmentsResponse.getAppointmentsList());
                    }
                    return appointmentList;
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<List<BlockTimeModel>> listBlockTimes(
            long companyId, List<Long> businessIds, ListBlockTimesRequest.Filter filter) {
        return CompletableFuture.supplyAsync(
                () -> ObjectUtils.isEmpty(businessIds)
                        ? List.of()
                        : appointmentService
                                .listBlockTimes(ListBlockTimesRequest.newBuilder()
                                        .setCompanyId(companyId)
                                        .addAllBusinessIds(businessIds)
                                        .setFilter(filter)
                                        .build())
                                .getBlockTimesList(),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<List<BookingRequestModel>> listBookingRequests(
            List<Long> businessIds,
            Date startDate,
            Date endDate,
            List<BookingRequestAssociatedModel> associatedModels,
            List<ServiceItemType> serviceItems) {
        return CompletableFuture.supplyAsync(
                () -> ObjectUtils.isEmpty(businessIds)
                        ? List.of()
                        : bookingRequestService
                                .listBookingRequests(ListBookingRequestsRequest.newBuilder()
                                        .setBusinessId(businessIds.get(0))
                                        .addAllBusinessIds(businessIds)
                                        .addAllAssociatedModels(associatedModels)
                                        .addStatuses(BookingRequestStatus.SUBMITTED)
                                        .setStartDate(startDate)
                                        .setEndDate(endDate)
                                        .addAllServiceItems(serviceItems)
                                        .addAllPaymentStatuses(List.of(
                                                BookingRequestModel.PaymentStatus.NO_PAYMENT,
                                                BookingRequestModel.PaymentStatus.PROCESSING,
                                                BookingRequestModel.PaymentStatus.SUCCESS))
                                        .build())
                                .getBookingRequestsList(),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, GroomingCalenderCustomerInfo>> listGroomingCustomerInfo(
            Long staffId, List<Long> customerIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(customerIds)) {
                        return Map.of();
                    }
                    List<GroomingQueryDto> dtos = customerIds.stream()
                            .map(customerId -> {
                                GroomingQueryDto dto = new GroomingQueryDto();
                                dto.setCustomerId(customerId.intValue());
                                return dto;
                            })
                            .toList();
                    GroomingCustomerInfoParams params = new GroomingCustomerInfoParams();
                    params.setTokenStaffId(staffId.intValue());
                    params.setTicketInfo(dtos);
                    return customerGroomingClient.getGroomingCalenderCustomerInfo(params).stream()
                            .collect(Collectors.toMap(
                                    info -> info.getCustomerId().longValue(), Function.identity(), (a, b) -> a));
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<List<Long>> getStaffAccessShowOnCalendarStaffIds(
            long companyId, long businessId, long staffId) {
        return CompletableFuture.supplyAsync(
                () -> staffService
                        .getShowOnCalendarStaffIds(GetShowOnCalendarStaffsRequest.newBuilder()
                                .setTokenStaffId(staffId)
                                .setTokenCompanyId(companyId)
                                .addBusinessIds(businessId)
                                .build())
                        .getLocationStaffIdsList()
                        .stream()
                        .filter(obj -> Objects.equals(businessId, obj.getBusinessId()))
                        .map(LocationStaffIdsDef::getStaffIdsList)
                        .flatMap(Collection::stream)
                        .toList(),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<GetStaffFullDetailResponse> getStaffFullDetail(long companyId, long staffId) {
        return CompletableFuture.supplyAsync(
                () -> staffService.getStaffFullDetail(GetStaffFullDetailRequest.newBuilder()
                        .setId(staffId)
                        .setTokenCompanyId(companyId)
                        .build()),
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Boolean> getStaffCanAccessBoardingAndDaycare(
            long companyId, long businessId, long staffId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    GetStaffFullDetailResponse staffFullDetail =
                            staffService.getStaffFullDetail(GetStaffFullDetailRequest.newBuilder()
                                    .setId(staffId)
                                    .setTokenCompanyId(companyId)
                                    .build());
                    // owner
                    if (PermissionUtil.hasOwnerPermission((byte) staffFullDetail
                            .getStaffProfile()
                            .getEmployeeCategory()
                            .getNumber())) {
                        return true;
                    }

                    StaffAccessControlDef accessControl = staffFullDetail.getAccessControl();
                    if (Objects.equals(Boolean.TRUE, accessControl.getAccessAllWorkingLocationsStaffs())) {
                        return true;
                    }

                    return accessControl.getAccessList().getStaffIdsByLocationList().stream()
                            .filter(obj -> Objects.equals(businessId, obj.getLocationId()))
                            .anyMatch(StaffAccessByLocationDef::getAccessLocationAllStaffs);
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<List<ReportCardSendResultDTO>> getAppointmentReportSentResultDTOs(
            List<AppointmentOverview> filteredOverviewList, String date, long companyId, long businessId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    // 根据 service type 区分 report card 的 appointment id
                    Set<Long> dailyReportAppointmentIds = new HashSet<>();
                    Set<Integer> groomingReportAppointmentIds = new HashSet<>();
                    filteredOverviewList.forEach(entry -> {
                        int serviceTypeInclude = entry.getServiceTypeInclude();
                        if (ServiceItemEnum.BOARDING.isIncludedIn(serviceTypeInclude)
                                || ServiceItemEnum.DAYCARE.isIncludedIn(serviceTypeInclude)) {
                            dailyReportAppointmentIds.add(entry.getId());
                        } else if (ServiceItemEnum.GROOMING.isIncludedIn(serviceTypeInclude)) {
                            groomingReportAppointmentIds.add(Math.toIntExact(entry.getId()));
                        }
                    });

                    List<ReportCardSendResultDTO> results = new ArrayList<>();

                    // 切换读数据源
                    boolean enable = featureFlagApi.isOn(
                            FeatureFlags.REPORT_CARD_SWITCH_READ,
                            FeatureFlagContext.builder().company(companyId).build());

                    if (enable) {
                        var appointmentIds = Stream.of(
                                        dailyReportAppointmentIds,
                                        groomingReportAppointmentIds.stream()
                                                .map(Long::valueOf)
                                                .toList())
                                .flatMap(Collection::stream)
                                .distinct()
                                .toList();
                        if (!CollectionUtils.isEmpty(appointmentIds)) {
                            results.addAll(processFulfillmentReports(companyId, businessId, appointmentIds, date));
                        }
                    } else {
                        if (!CollectionUtils.isEmpty(groomingReportAppointmentIds)) {
                            results.addAll(processGroomingReports(businessId, groomingReportAppointmentIds));
                        }

                        if (!CollectionUtils.isEmpty(dailyReportAppointmentIds)) {
                            results.addAll(processDailyReports(companyId, businessId, dailyReportAppointmentIds, date));
                        }
                    }

                    return results;
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<List<ReportCardSendResultDTO>> getReportSentResultDTOs(
            List<OverviewStatusEntry> filteredOverviewList,
            GetOverviewListParams request,
            long companyId,
            long businessId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    List<AppointmentOverview> needReportCardAppointments = filteredOverviewList.stream()
                            .flatMap(entry -> entry.getAppointmentOverviewsList().stream())
                            .toList();

                    // 根据 service type 区分 report card 的 appointment id
                    Set<Long> dailyReportAppointmentIds = new HashSet<>();
                    Set<Integer> groomingReportAppointmentIds = new HashSet<>();
                    needReportCardAppointments.forEach(entry -> {
                        int serviceTypeInclude = entry.getServiceTypeInclude();
                        if (ServiceItemEnum.BOARDING.isIncludedIn(serviceTypeInclude)
                                || ServiceItemEnum.DAYCARE.isIncludedIn(serviceTypeInclude)) {
                            dailyReportAppointmentIds.add(entry.getId());
                        } else if (ServiceItemEnum.GROOMING.isIncludedIn(serviceTypeInclude)) {
                            groomingReportAppointmentIds.add(Math.toIntExact(entry.getId()));
                        }
                    });

                    List<ReportCardSendResultDTO> results = new ArrayList<>();
                    // 切换读数据源
                    boolean enable = featureFlagApi.isOn(
                            FeatureFlags.REPORT_CARD_SWITCH_READ,
                            FeatureFlagContext.builder().company(companyId).build());

                    if (enable) {
                        var appointmentIds = Stream.of(
                                        dailyReportAppointmentIds,
                                        groomingReportAppointmentIds.stream()
                                                .map(Long::valueOf)
                                                .toList())
                                .flatMap(Collection::stream)
                                .distinct()
                                .toList();
                        if (!CollectionUtils.isEmpty(appointmentIds)) {
                            results.addAll(processFulfillmentReports(
                                    companyId, businessId, appointmentIds, request.getDate()));
                        }
                    } else {
                        if (!CollectionUtils.isEmpty(groomingReportAppointmentIds)) {
                            results.addAll(processGroomingReports(businessId, groomingReportAppointmentIds));
                        }

                        if (!CollectionUtils.isEmpty(dailyReportAppointmentIds)) {
                            results.addAll(processDailyReports(
                                    companyId, businessId, dailyReportAppointmentIds, request.getDate()));
                        }
                    }

                    return results;
                },
                ThreadPool.getSubmitExecutor());
    }

    private List<ReportCardSendResultDTO> processFulfillmentReports(
            long companyId, long businessId, List<Long> appointmentIds, String requestDate) {
        List<ListFulfillmentReportResponse.FulfillmentReportCard> fulfillmentReportCardsList = fulfillmentReportService
                .listFulfillmentReport(ListFulfillmentReportRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .setFilter(ListFulfillmentReportConfigFilter.newBuilder()
                                .addAllAppointmentIds(appointmentIds)
                                .setStartDate(requestDate)
                                .setEndDate(requestDate)
                                .build())
                        .setPagination(PaginationRef.newBuilder()
                                .setOffset(0)
                                .setLimit(1000)
                                .build())
                        .build())
                .getFulfillmentReportCardsList();

        if (CollectionUtils.isEmpty(fulfillmentReportCardsList)) {
            return List.of();
        }

        List<ReportCardSendResultDTO> reportCardSentResults = new ArrayList<>();

        fulfillmentReportCardsList.forEach(report -> {
            var petId = report.getPet().getPetId();
            if (Objects.equals(report.getReportStatus(), ReportStatus.DRAFT)) {
                reportCardSentResults.add(
                        new ReportCardSendResultDTO(report.getAppointmentId(), petId, OverviewReportStatus.DRAFT));
                return;
            }

            // sent status, check if sent success
            boolean sentSuccess = Objects.equals(ReportStatus.SENT, report.getReportStatus())
                    && report.getSendRecordList().stream()
                            .anyMatch(result -> Objects.equals(report.getAppointmentId(), result.getAppointmentId())
                                    && Objects.equals(petId, result.getPetId())
                                    && result.getIsSentSuccess());
            if (sentSuccess) {
                reportCardSentResults.add(
                        new ReportCardSendResultDTO(report.getAppointmentId(), petId, OverviewReportStatus.SENT_TODAY));
                return;
            }

            reportCardSentResults.add(new ReportCardSendResultDTO(
                    report.getAppointmentId(), report.getPet().getPetId(), OverviewReportStatus.UNSENT));
        });

        return reportCardSentResults;
    }

    /**
     * @deprecated by Bryson since 2025/9/2, use {@link #processFulfillmentReports(long, long, List, String)} instead
     */
    @Deprecated(since = "2025-09-02")
    private List<ReportCardSendResultDTO> processDailyReports(
            long companyId, long businessId, Set<Long> dailyReportAppointmentIds, String requestDate) {
        List<ReportCardSendResultDTO> reportCardSentResults = new ArrayList<>();
        LocalDate localDate = LocalDate.parse(requestDate);
        Date serviceDate = Date.newBuilder()
                .setYear(localDate.getYear())
                .setMonth(localDate.getMonthValue())
                .setDay(localDate.getDayOfMonth())
                .build();

        List<DailyReportConfigDef> reportConfigs = dailyReportService
                .listDailyReportConfig(ListDailyReportConfigRequest.newBuilder()
                        .addAllAppointmentIds(dailyReportAppointmentIds)
                        .addServiceDate(serviceDate)
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .build())
                .getReportConfigsList();
        if (CollectionUtils.isEmpty(reportConfigs)) {
            return reportCardSentResults;
        }

        List<SentResultDef> sentResults = dailyReportService
                .getDailyReportSentResult(GetDailyReportSentResultRequest.newBuilder()
                        .addAllAppointmentIds(dailyReportAppointmentIds)
                        .setServiceDate(serviceDate)
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .build())
                .getSentResultsList();

        reportConfigs.forEach(report -> {
            // draft status
            if (Objects.equals(ReportCardStatus.REPORT_CARD_DRAFT, report.getStatus())) {
                reportCardSentResults.add(new ReportCardSendResultDTO(
                        report.getAppointmentId(), report.getPetId(), OverviewReportStatus.DRAFT));
                return;
            }
            // sent status, check if sent success
            boolean sentSuccess = Objects.equals(ReportCardStatus.REPORT_CARD_SENT, report.getStatus())
                    && sentResults.stream()
                            .anyMatch(result -> Objects.equals(report.getAppointmentId(), result.getAppointmentId())
                                    && Objects.equals(report.getPetId(), result.getPetId())
                                    && result.getSentSuccess());
            if (sentSuccess) {
                reportCardSentResults.add(new ReportCardSendResultDTO(
                        report.getAppointmentId(), report.getPetId(), OverviewReportStatus.SENT_TODAY));
                return;
            }
            reportCardSentResults.add(new ReportCardSendResultDTO(
                    report.getAppointmentId(), report.getPetId(), OverviewReportStatus.UNSENT));
        });
        return reportCardSentResults;
    }

    /**
     * @deprecated by Bryson since 2025/9/2, use {@link #processFulfillmentReports(long, long, List, String)} instead
     */
    @Deprecated(since = "2025-09-02")
    private List<ReportCardSendResultDTO> processGroomingReports(
            long businessId, Set<Integer> groomingReportAppointmentIds) {
        List<ReportCardSendResultDTO> reportCardSentResults = new ArrayList<>();
        List<GroomingReportDTO> groomingReportList = groomingGroomingReportClient.getGroomingReportList(
                new GroomingIdListParams(Math.toIntExact(businessId), new ArrayList<>(groomingReportAppointmentIds)));

        // key: grooming id
        Map<Integer, List<GroomingReportSendLogDTO>> groomingLastReportSendLogsMap =
                groomingReportSendService.getGroomingLastReportSendLogsMap(new GroomingIdListParams(
                        Math.toIntExact(businessId),
                        groomingReportAppointmentIds.stream().toList()));

        groomingReportList.forEach(report -> {
            Long groomingId = report.getGroomingId().longValue();
            Long petId = report.getPetId().longValue();
            // draft status
            if (GroomingReportStatusEnum.draft.name().equalsIgnoreCase(report.getStatus())) {
                reportCardSentResults.add(new ReportCardSendResultDTO(groomingId, petId, OverviewReportStatus.DRAFT));
                return;
            }
            // sent status, check if sent success
            boolean sentSuccess = GroomingReportStatusEnum.sent.name().equalsIgnoreCase(report.getStatus())
                    && groomingLastReportSendLogsMap.getOrDefault(groomingId.intValue(), List.of()).stream()
                            .anyMatch(entry -> Objects.equals((byte) 0, entry.getStatus()));
            if (sentSuccess) {
                reportCardSentResults.add(
                        new ReportCardSendResultDTO(groomingId, petId, OverviewReportStatus.SENT_TODAY));
                return;
            }
            reportCardSentResults.add(new ReportCardSendResultDTO(groomingId, petId, OverviewReportStatus.UNSENT));
        });

        return reportCardSentResults;
    }

    public CompletableFuture<List<AbstractMap.SimpleEntry<Long, Long>>> getReportSentResults(
            List<OverviewStatusEntry> filteredOverviewList, GetOverviewListParams request, long companyId) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (!Objects.equals(OverviewDateType.NOW_DATE, request.getDateType())) {
                        return List.of();
                    }

                    List<AbstractMap.SimpleEntry<Long, Long>> reportCardSentResults = new ArrayList<>();
                    List<AppointmentOverview> needReportCardAppointments = filteredOverviewList.stream()
                            .filter(entry -> !Objects.equals(OverviewStatus.EXPECTED, entry.getStatus()))
                            .map(OverviewStatusEntry::getAppointmentOverviewsList)
                            .flatMap(List::stream)
                            .toList();

                    Set<Long> dailyReportAppointmentIds = new HashSet<>();
                    Set<Integer> groomingReportAppointmentIds = new HashSet<>();
                    needReportCardAppointments.forEach(entry -> {
                        List<ServiceItemType> serviceItemTypes =
                                ServiceItemEnum.convertBitValueList(entry.getServiceTypeInclude()).stream()
                                        .map(ServiceItemEnum::getServiceItem)
                                        .map(ServiceItemType::forNumber)
                                        .toList();
                        if (serviceItemTypes.contains(ServiceItemType.BOARDING)
                                || serviceItemTypes.contains(ServiceItemType.DAYCARE)) {
                            dailyReportAppointmentIds.add(entry.getId());
                        } else if (serviceItemTypes.contains(ServiceItemType.GROOMING)) {
                            groomingReportAppointmentIds.add(Math.toIntExact(entry.getId()));
                        }
                    });

                    LocalDate localDate = LocalDate.parse(request.getDate());
                    if (!CollectionUtils.isEmpty(groomingReportAppointmentIds)) {
                        groomingReportSendService
                                .getGroomingLastReportSendLogsMap(new GroomingIdListParams(
                                        Math.toIntExact(request.getBusinessId()),
                                        groomingReportAppointmentIds.stream().toList()))
                                .values()
                                .stream()
                                .flatMap(List::stream)
                                .filter(entry -> Objects.equals((byte) 0, entry.getStatus()))
                                .forEach(entry -> reportCardSentResults.add(new AbstractMap.SimpleEntry<>(
                                        entry.getGroomingId().longValue(),
                                        entry.getPetId().longValue())));
                    }
                    if (!CollectionUtils.isEmpty(dailyReportAppointmentIds)) {
                        dailyReportService
                                .getDailyReportSentResult(GetDailyReportSentResultRequest.newBuilder()
                                        .addAllAppointmentIds(dailyReportAppointmentIds)
                                        .setServiceDate(Date.newBuilder()
                                                .setYear(localDate.getYear())
                                                .setMonth(localDate.getMonthValue())
                                                .setDay(localDate.getDayOfMonth())
                                                .build())
                                        .setCompanyId(companyId)
                                        .setBusinessId(request.getBusinessId())
                                        .build())
                                .getSentResultsList()
                                .stream()
                                .filter(SentResultDef::getSentSuccess)
                                .forEach(entry -> reportCardSentResults.add(
                                        new AbstractMap.SimpleEntry<>(entry.getAppointmentId(), entry.getPetId())));
                    }
                    return reportCardSentResults;
                },
                ThreadPool.getSubmitExecutor());
    }

    public CompletableFuture<Map<Long, List<BoardingSplitLodgingModel>>> getBoardingSplitLodgingMap(
            Collection<Long> appointmentIds) {
        return CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(appointmentIds)) {
                        return Map.of();
                    }
                    return boardingSplitLodgingService
                            .listBoardingSplitLodgings(ListBoardingSplitLodgingsRequest.newBuilder()
                                    .addAllAppointmentIds(appointmentIds)
                                    .build())
                            .getBoardingSplitLodgingsList()
                            .stream()
                            .collect(Collectors.groupingBy(BoardingSplitLodgingModel::getAppointmentId));
                },
                ThreadPool.getSubmitExecutor());
    }

    public <T> CompletableFuture<T> supply(Supplier<T> supplier) {
        return CompletableFuture.supplyAsync(supplier, ThreadPool.getSubmitExecutor());
    }
}
