package com.moego.api.v3.appointment.converter;

import com.moego.idl.models.appointment.v1.AutoAssignCalendarView;
import com.moego.idl.models.online_booking.v1.GroomingAutoAssignModel;
import com.moego.idl.models.online_booking.v1.GroomingAutoAssignView;
import com.moego.server.grooming.dto.AutoAssignDTO;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface AutoAssignConverter {

    AutoAssignConverter INSTANCE = Mappers.getMapper(AutoAssignConverter.class);

    AutoAssignCalendarView toView(AutoAssignDTO dto);

    GroomingAutoAssignView modelToView(GroomingAutoAssignModel model);
}
