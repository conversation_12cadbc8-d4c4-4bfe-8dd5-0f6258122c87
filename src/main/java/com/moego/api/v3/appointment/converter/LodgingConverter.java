package com.moego.api.v3.appointment.converter;

import com.moego.idl.api.appointment.v1.GetLodgingCalendarViewV2Params;
import com.moego.idl.api.appointment.v1.GetLodgingCalendarViewV2Result;
import com.moego.idl.models.appointment.v1.AutoAssignCalendarView;
import com.moego.idl.models.appointment.v1.LodgingAssignPetDetailInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetEvaluationInfo;
import com.moego.idl.models.appointment.v1.LodgingListView;
import com.moego.idl.models.appointment.v1.LodgingOccupiedStatus;
import com.moego.idl.models.appointment.v1.LodgingUnitView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.online_booking.v1.GroomingAutoAssignModel;
import com.moego.idl.models.online_booking.v1.GroomingAutoAssignView;
import com.moego.server.grooming.dto.AutoAssignDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper
public interface LodgingConverter {

    LodgingConverter INSTANCE = Mappers.getMapper(LodgingConverter.class);

    AutoAssignCalendarView toView(AutoAssignDTO dto);

    GroomingAutoAssignView modelToView(GroomingAutoAssignModel model);

    default GetLodgingCalendarViewV2Result.PetView buildCalendarViewPetView(BusinessCustomerPetInfoModel pet) {
        return GetLodgingCalendarViewV2Result.PetView.newBuilder()
                .setId(pet.getId())
                .setPetName(pet.getPetName())
                .setAvatarPath(pet.getAvatarPath())
                .setPetType(pet.getPetType())
                .build();
    }

    default List<GetLodgingCalendarViewV2Result.PetView> buildCalendarViewPetView(
            List<BusinessCustomerPetInfoModel> pets) {
        if (CollectionUtils.isEmpty(pets)) {
            return new ArrayList<>();
        }
        return pets.stream().map(this::buildCalendarViewPetView).collect(Collectors.toList());
    }

    default GetLodgingCalendarViewV2Result.PetDetailView buildCalendarViewPetDetailView(
            LodgingAssignPetDetailInfo petDetail) {
        var builder = GetLodgingCalendarViewV2Result.PetDetailView.newBuilder()
                .setIsSplitLodging(petDetail.getIsSplitLodging())
                .setId(petDetail.getId())
                .setStartDate(petDetail.getStartDate())
                .setEndDate(petDetail.getEndDate())
                .addAllSpecificDates(petDetail.getSpecificDatesList())
                .setServiceItemType(petDetail.getServiceItemType());
        if (petDetail.hasDateType()) {
            builder.setDateType(petDetail.getDateType());
        }
        return builder.build();
    }

    default List<GetLodgingCalendarViewV2Result.PetDetailView> buildCalendarViewPetDetailView(
            List<LodgingAssignPetDetailInfo> petDetails) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return new ArrayList<>();
        }
        return petDetails.stream().map(this::buildCalendarViewPetDetailView).collect(Collectors.toList());
    }

    default GetLodgingCalendarViewV2Result.PetEvaluationView buildCalendarViewPetEvaluationView(
            LodgingAssignPetEvaluationInfo detailInfo) {
        var builder = GetLodgingCalendarViewV2Result.PetEvaluationView.newBuilder()
                .setId(detailInfo.getId())
                .setStartDate(detailInfo.getStartDate())
                .setEndDate(detailInfo.getEndDate());
        return builder.build();
    }

    default List<GetLodgingCalendarViewV2Result.PetEvaluationView> buildCalendarViewPetEvaluationView(
            List<LodgingAssignPetEvaluationInfo> details) {
        if (CollectionUtils.isEmpty(details)) {
            return new ArrayList<>();
        }
        return details.stream().map(this::buildCalendarViewPetEvaluationView).collect(Collectors.toList());
    }

    default List<LodgingOccupiedStatus> toLodgingStatus(GetLodgingCalendarViewV2Params params) {
        if (params.hasLodgingOccupiedStatus()) {
            return List.of(params.getLodgingOccupiedStatus());
        }
        if (!params.hasLodgingStatus()) {
            return List.of(
                    LodgingOccupiedStatus.VACANT,
                    LodgingOccupiedStatus.PARTIALLY_OCCUPIED,
                    LodgingOccupiedStatus.FULLY_OCCUPIED);
        }
        switch (params.getLodgingStatus()) {
            case LODGING_STATUS_OCCUPIED -> {
                return List.of(LodgingOccupiedStatus.PARTIALLY_OCCUPIED, LodgingOccupiedStatus.FULLY_OCCUPIED);
            }
            case LODGING_STATUS_VACANT -> {
                return List.of(LodgingOccupiedStatus.VACANT);
            }
            default -> {
                return List.of(
                        LodgingOccupiedStatus.VACANT,
                        LodgingOccupiedStatus.PARTIALLY_OCCUPIED,
                        LodgingOccupiedStatus.FULLY_OCCUPIED);
            }
        }
    }

    default LodgingListView buildGetLodgingListLodgingTypeView(
            LodgingTypeModel lodgingType,
            List<LodgingUnitModel> lodgingUnitList,
            Map<Long, LodgingOccupiedStatus> lodgingStatus) {
        return LodgingListView.newBuilder()
                .setLodgingTypeId(lodgingType.getId())
                .setLodgingTypeName(lodgingType.getName())
                .addAllLodgingUnits(
                        LodgingConverter.INSTANCE.buildGetLodgingListLodgingUnitView(lodgingUnitList, lodgingStatus))
                .build();
    }

    default List<LodgingUnitView> buildGetLodgingListLodgingUnitView(
            List<LodgingUnitModel> lodgingUnits, Map<Long, LodgingOccupiedStatus> lodgingStatus) {
        if (CollectionUtils.isEmpty(lodgingUnits)) {
            return new ArrayList<>();
        }
        return lodgingUnits.stream()
                .map(lodgingUnit -> buildGetLodgingListLodgingUnitView(lodgingUnit, lodgingStatus))
                .collect(Collectors.toList());
    }

    default LodgingUnitView buildGetLodgingListLodgingUnitView(
            LodgingUnitModel lodgingUnit, Map<Long, LodgingOccupiedStatus> lodgingStatus) {
        return LodgingUnitView.newBuilder()
                .setId(lodgingUnit.getId())
                .setName(lodgingUnit.getName())
                .setOccupiedStatus(lodgingStatus.get(lodgingUnit.getId()))
                .build();
    }

    com.moego.idl.models.offering.v1.LodgingUnitView toView(LodgingUnitModel model);
}
