package com.moego.api.v3.appointment.dto;

import java.math.BigDecimal;
import java.util.Map;
import java.util.Set;
import org.apache.commons.lang3.tuple.Pair;

public record LimitSizeKey(Map<Set<Pair<BigDecimal, BigDecimal>>, Integer> key, Boolean onlyAcceptSelected) {

    public void put(final Set<Pair<BigDecimal, BigDecimal>> weightPairSet, final Integer value) {
        if (this.key != null) {
            this.key.put(weightPairSet, value);
        }
    }
}
