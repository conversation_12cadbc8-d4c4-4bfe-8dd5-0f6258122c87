package com.moego.api.v3.appointment.controller;

import com.google.type.Date;
import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.api.v3.appointment.converter.PetDetailConverter;
import com.moego.api.v3.appointment.service.AppointmentCheckerService;
import com.moego.api.v3.appointment.service.CompanySettingService;
import com.moego.api.v3.appointment.service.LodgingUsingService;
import com.moego.api.v3.appointment.service.PetDetailService;
import com.moego.api.v3.appointment.utils.PetDetailUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.api.appointment.v1.AppointmentCheckerServiceGrpc;
import com.moego.idl.api.appointment.v1.AppointmentDateConflictCheckResult;
import com.moego.idl.api.appointment.v1.AppointmentOverview;
import com.moego.idl.api.appointment.v1.BusinessClosedDateCheckResult;
import com.moego.idl.api.appointment.v1.CheckLodgingOverCapacityParams;
import com.moego.idl.api.appointment.v1.CheckLodgingOverCapacityResult;
import com.moego.idl.api.appointment.v1.CheckSaveAppointmentParams;
import com.moego.idl.api.appointment.v1.CheckSaveAppointmentResult;
import com.moego.idl.api.appointment.v1.GetAvailableDatesParams;
import com.moego.idl.api.appointment.v1.GetAvailableDatesResult;
import com.moego.idl.api.appointment.v1.GetEvaluationAvailableTimeParams;
import com.moego.idl.api.appointment.v1.GetEvaluationAvailableTimeResult;
import com.moego.idl.api.appointment.v1.LodgingOverCapacityCheckResult;
import com.moego.idl.api.appointment.v1.LodgingTypeOverview;
import com.moego.idl.api.appointment.v1.LodgingUnitChange;
import com.moego.idl.api.appointment.v1.LodgingUnitOverview;
import com.moego.idl.api.appointment.v1.PetAppointmentsOverview;
import com.moego.idl.api.appointment.v1.ServiceOverview;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.LodgingOccupiedStatus;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.LodgingUnitType;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.GetAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetPetDetailListResponse;
import com.moego.idl.service.appointment.v1.GetTimeOverlapAppointmentListRequest;
import com.moego.idl.service.online_booking.v1.GetAvailableDateTimeRequest;
import com.moego.idl.service.online_booking.v1.GetAvailableDateTimeResponse;
import com.moego.idl.service.online_booking.v1.OBAvailableDateTimeServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import io.grpc.stub.StreamObserver;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class AppointmentCheckerController extends AppointmentCheckerServiceGrpc.AppointmentCheckerServiceImplBase {

    private final CompanySettingService companySettingService;
    private final AppointmentCheckerService appointmentCheckerService;
    private final OBAvailableDateTimeServiceGrpc.OBAvailableDateTimeServiceBlockingStub
            obAvailableDateTimeServiceBlockingStub;
    private final LodgingUsingService lodgingUsingService;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentService;
    private final PetDetailService petDetailService;

    private static final LodgingOverCapacityCheckResult EMPTY_RESULT =
            LodgingOverCapacityCheckResult.newBuilder().build();

    @Override
    @Auth(AuthType.COMPANY)
    public void checkSaveAppointment(
            CheckSaveAppointmentParams request, StreamObserver<CheckSaveAppointmentResult> responseObserver) {
        var businessId = request.getBusinessId();
        var startDate = request.getStartDate();
        var endDate = request.getEndDate();
        var petIds = request.getPetIdsList();
        var customerId = request.getCustomerId();
        var appointmentId = request.getAppointmentId();

        var companyId = AuthContext.get().companyId();

        // check lodging over capacity conflict
        var lodgingOverCapacityCheckResult =
                LodgingOverCapacityCheckResult.newBuilder().build();

        // check appointment date conflict
        var appointmentDateConflictCheckResult = CompletableFuture.supplyAsync(
                () -> buildAppointmentDateConflictCheckResult(
                        companyId, startDate, endDate, petIds, customerId, appointmentId),
                ThreadPool.getSubmitExecutor());

        // check business closed date conflict
        var businessClosedDateCheckResult = CompletableFuture.supplyAsync(
                () -> buildBusinessClosedDateCheckResult(companyId, businessId, startDate, endDate),
                ThreadPool.getSubmitExecutor());

        CompletableFuture.allOf(appointmentDateConflictCheckResult, businessClosedDateCheckResult);

        responseObserver.onNext(CheckSaveAppointmentResult.newBuilder()
                .setLodgingOverCapacityCheckResult(lodgingOverCapacityCheckResult)
                .setAppointmentDateConflictCheckResult(appointmentDateConflictCheckResult.join())
                .setBusinessClosedDateCheckResult(businessClosedDateCheckResult.join())
                .build());
        responseObserver.onCompleted();
    }

    public LodgingOverCapacityCheckResult buildLodgingOverCapacityCheckResult(
            Long companyId,
            Long businessId,
            Date startDate,
            Date endDate,
            List<Long> lodgingUnitIds,
            Long appointmentId,
            LodgingUnitChange changedLodgingUnit) {

        LocalDate startLocalDate = DateTimeConverter.INSTANCE.fromGoogleDate(startDate);
        LocalDate endLocalDate = DateTimeConverter.INSTANCE.fromGoogleDate(endDate);

        if (CollectionUtils.isEmpty(lodgingUnitIds) || lodgingUnitIds.contains(0L)) {
            return EMPTY_RESULT;
        }

        List<LodgingUnitModel> originalLodgingUnitList =
                appointmentCheckerService.getLodgingUnitList(companyId, businessId, lodgingUnitIds);

        if (CollectionUtils.isEmpty(originalLodgingUnitList)) {
            return EMPTY_RESULT;
        }

        List<LodgingTypeModel> lodgingTypeList = appointmentCheckerService.getLodgingTypeList(originalLodgingUnitList);

        Map<Long, LodgingTypeModel> lodgingTypeMap =
                lodgingTypeList.stream().collect(Collectors.toMap(LodgingTypeModel::getId, Function.identity()));

        List<LodgingUnitModel> processingLodgingUnits = new ArrayList<>(originalLodgingUnitList);
        LodgingUnitOverview newLodgingUnitView = null;
        LocalDate effectiveStartDate = startLocalDate;
        LocalDate effectiveEndDate = endLocalDate;

        if (appointmentId != null && appointmentId > 0) {
            AppointmentModel appointment = getAppointment(companyId, businessId, appointmentId);
            LocalDate appointmentStartDate = LocalDate.parse(appointment.getAppointmentDate());
            LocalDate appointmentEndDate = LocalDate.parse(appointment.getAppointmentEndDate());

            // 获取宠物详情并更新住宿单元
            GetPetDetailListResponse petDetailResp =
                    petDetailService.getPetDetailsWithActualDates(companyId, List.of(appointmentId));

            processingLodgingUnits = updateLodgingUnitsForAppointment(companyId, businessId, petDetailResp);

            // 处理修改的住宿单元
            if (changedLodgingUnit != null) {
                if (!Objects.equals(
                        changedLodgingUnit.getOldLodgingUnitId(), changedLodgingUnit.getNewLodgingUnitId())) {
                    // 移除旧单元
                    processingLodgingUnits = processingLodgingUnits.stream()
                            .filter(unit -> !Objects.equals(unit.getId(), changedLodgingUnit.getOldLodgingUnitId()))
                            .collect(Collectors.toList());

                    // 添加新单元
                    List<LodgingUnitModel> newUnits = appointmentCheckerService.getLodgingUnitList(
                            companyId, businessId, List.of(changedLodgingUnit.getNewLodgingUnitId()));

                    if (!new HashSet<>(processingLodgingUnits).containsAll(newUnits)) {
                        processingLodgingUnits.addAll(newUnits);
                    }

                    if (Objects.equals(startLocalDate, appointmentStartDate)
                            && Objects.equals(endLocalDate, appointmentEndDate)) {
                        processingLodgingUnits = processingLodgingUnits.stream()
                                .filter(unit -> Objects.equals(unit.getId(), changedLodgingUnit.getNewLodgingUnitId()))
                                .collect(Collectors.toList());
                    }

                    // 检查新单元容量
                    Map<Long, LodgingOccupiedStatus> newStatus = appointmentCheckerService.getLodgingOccupiedStatusMap(
                            companyId,
                            businessId,
                            startLocalDate.toString(),
                            endLocalDate.toString(),
                            lodgingTypeList,
                            newUnits);

                    newLodgingUnitView = newUnits.stream()
                            .filter(unit -> isOverCapacity(unit, newStatus.get(unit.getId()), lodgingTypeMap))
                            .findFirst()
                            .map(unit -> LodgingUnitOverview.newBuilder()
                                    .setId(unit.getId())
                                    .setName(unit.getName())
                                    .setLodgingTypeId(unit.getLodgingTypeId())
                                    .build())
                            .orElse(null);
                }

                // 更新检查日期范围
                AppointmentCheckerService.DateRange nonOverlapRanges = appointmentCheckerService.getNonOverlapRanges(
                        startLocalDate, endLocalDate, appointmentStartDate, appointmentEndDate);

                effectiveStartDate = nonOverlapRanges.getStartDate();
                effectiveEndDate = nonOverlapRanges.getEndDate();
            }
        }

        Map<Long, LodgingOccupiedStatus> lodgingStatus = appointmentCheckerService.getLodgingOccupiedStatusMap(
                companyId,
                businessId,
                effectiveStartDate.toString(),
                effectiveEndDate.toString(),
                lodgingTypeList,
                processingLodgingUnits);

        List<LodgingUnitModel> overCapacityUnits = appointmentCheckerService.filterOverCapacityUnits(
                processingLodgingUnits, lodgingStatus, lodgingTypeMap);

        if (CollectionUtils.isEmpty(overCapacityUnits)) {
            return EMPTY_RESULT;
        }

        return buildResult(overCapacityUnits, lodgingTypeList, newLodgingUnitView);
    }

    public AppointmentDateConflictCheckResult buildAppointmentDateConflictCheckResult(
            Long companyId, Date startDate, Date endDate, List<Long> petIds, Long customerId, Long appointmentId) {
        var timezone = companySettingService.mustGetTimeZoneName(companyId);

        if (customerId <= 0) {
            return AppointmentDateConflictCheckResult.newBuilder().build();
        }

        // 根据 customerId、petIds 与 dateRange 查出时间冲突的 appointment list map，key 为 petId
        var timeOverlapAppointmentListMap = appointmentCheckerService.getTimeOverlapAppointmentListMap(
                companyId,
                List.of(customerId),
                buildTimeOverlapFilter(startDate, endDate, timezone),
                petIds,
                appointmentId);

        var appointmentIds = timeOverlapAppointmentListMap.values().stream()
                .flatMap(List::stream)
                .map(AppointmentModel::getId)
                .distinct()
                .toList();

        if (CollectionUtils.isEmpty(appointmentIds)) {
            return AppointmentDateConflictCheckResult.newBuilder().build();
        }

        // pet detail list
        var petDetailListFuture = CompletableFuture.supplyAsync(
                () -> appointmentCheckerService.getPetDetailList(companyId, appointmentIds),
                ThreadPool.getSubmitExecutor());

        // pet
        var petMapFuture = petDetailListFuture.thenApplyAsync(
                petDetailList -> appointmentCheckerService.getPetMap(companyId, petDetailList),
                ThreadPool.getSubmitExecutor());

        // service, map<serviceId, serviceBriefView>
        var serviceMapFuture = petDetailListFuture.thenApplyAsync(
                petDetailList -> appointmentCheckerService.getServiceMap(companyId, petDetailList),
                ThreadPool.getSubmitExecutor());

        CompletableFuture.allOf(petDetailListFuture, petMapFuture, serviceMapFuture);

        List<PetDetailModel> petDetailsList =
                new ArrayList<>(petDetailListFuture.join().getPetDetailsList());

        return AppointmentDateConflictCheckResult.newBuilder()
                .addAllConflictAppointments(petIds.stream()
                        .map(petId -> buildPetAppointmentsOverview(
                                petId,
                                petMapFuture.join(),
                                petDetailsList,
                                serviceMapFuture.join(),
                                timeOverlapAppointmentListMap))
                        .filter(petAppointmentsOverview ->
                                !petAppointmentsOverview.getAppointmentsList().isEmpty())
                        .toList())
                .build();
    }

    private GetTimeOverlapAppointmentListRequest.Filter buildTimeOverlapFilter(
            Date startFilter, Date endFilter, String timezone) {
        return GetTimeOverlapAppointmentListRequest.Filter.newBuilder()
                .setDateRange(DateTimeConverter.INSTANCE.buildDayInterval(startFilter, endFilter, timezone))
                .build();
    }

    private PetAppointmentsOverview buildPetAppointmentsOverview(
            Long petId,
            Map<Long, BusinessCustomerPetModel> petMap,
            List<PetDetailModel> petDetailsList,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, List<AppointmentModel>> appointmentListMap) {

        var pet = petMap.get(petId);
        if (pet == null) {
            return PetAppointmentsOverview.newBuilder().build();
        }

        // 构建 appointments 列表
        var appointments = appointmentListMap.getOrDefault(petId, List.of()).stream()
                .map(appointment -> {
                    // 构建 services 列表
                    var services = petDetailsList.stream()
                            .filter(petDetail -> petDetail.getGroomingId() == appointment.getId())
                            .map(petDetail -> {
                                var service = serviceMap.get(petDetail.getServiceId());
                                return ServiceOverview.newBuilder()
                                        .setServiceId(service.getId())
                                        .setServiceName(service.getName())
                                        .setStartDate(
                                                petDetail.getStartDate().isEmpty()
                                                        ? DateTimeConverter.INSTANCE.toGoogleDate(LocalDate.now())
                                                        : DateTimeConverter.INSTANCE.toGoogleDate(
                                                                LocalDate.parse(petDetail.getStartDate())))
                                        .setEndDate(
                                                petDetail.getEndDate().isEmpty()
                                                        ? DateTimeConverter.INSTANCE.toGoogleDate(LocalDate.now())
                                                        : DateTimeConverter.INSTANCE.toGoogleDate(
                                                                LocalDate.parse(petDetail.getEndDate())))
                                        .setServiceItemType(service.getServiceItemType())
                                        .build();
                            })
                            .distinct()
                            .toList();

                    // 构建 AppointmentOverview
                    return AppointmentOverview.newBuilder()
                            .setAppointmentId(appointment.getId())
                            .setStartDate(DateTimeConverter.INSTANCE.toGoogleDate(
                                    LocalDate.parse(appointment.getAppointmentDate())))
                            .setEndDate(DateTimeConverter.INSTANCE.toGoogleDate(
                                    LocalDate.parse(appointment.getAppointmentEndDate())))
                            .setAppointmentStartTime(appointment.getAppointmentStartTime())
                            .setAppointmentEndTime(appointment.getAppointmentEndTime())
                            .addAllServices(services)
                            .build();
                })
                .toList();

        return PetAppointmentsOverview.newBuilder()
                .setPet(PetDetailConverter.INSTANCE.toPetOverview(pet))
                .addAllAppointments(appointments)
                .build();
    }

    public BusinessClosedDateCheckResult buildBusinessClosedDateCheckResult(
            Long companyId, long businessId, Date startDate, Date endDate) {
        var closedDateDefList = appointmentCheckerService.getClosedDateList(companyId, businessId);

        if (CollectionUtils.isEmpty(closedDateDefList)) {
            return BusinessClosedDateCheckResult.newBuilder().build();
        }

        String startDateStr =
                DateTimeConverter.INSTANCE.fromGoogleDate(startDate).toString();
        String endDateStr = DateTimeConverter.INSTANCE.fromGoogleDate(endDate).toString();

        List<String> dateRange = DateUtil.generateAllDatesBetween(startDateStr, endDateStr);
        List<String> closedDates = new ArrayList<>();
        for (var closedDateDef : closedDateDefList) {
            String closedStartDate = closedDateDef.getStartDate();
            String closedEndDate = closedDateDef.getEndDate();

            List<String> closedDateRange = DateUtil.generateAllDatesBetween(closedStartDate, closedEndDate);
            for (String date : closedDateRange) {
                if (dateRange.contains(date) && !closedDates.contains(date)) {
                    closedDates.add(date);
                }
            }
        }

        return BusinessClosedDateCheckResult.newBuilder()
                .addAllClosedDate(closedDates)
                .build();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getAvailableDates(
            GetAvailableDatesParams request, StreamObserver<GetAvailableDatesResult> responseObserver) {
        // 获取 evaluation 的 relation service ids
        var petServicesList = request.getPetServicesList();
        Set<Long> relationServiceIds = petServicesList.stream()
                .flatMap(petServices -> petServices.getEvaluationIdsList().stream())
                .collect(Collectors.toSet());

        var availableDateTime =
                obAvailableDateTimeServiceBlockingStub.getAvailableDateTime(GetAvailableDateTimeRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(request.getBusinessId())
                        .setFromDate(request.getStartDate())
                        .setToDate(request.getEndDate())
                        .setServiceItemType(ServiceItemType.EVALUATION)
                        .addAllRelationServiceIds(relationServiceIds)
                        .build());

        List<Date> availableDates = new ArrayList<>();
        List<Date> unavailableDates = new ArrayList<>();
        for (GetAvailableDateTimeResponse.DateTimeRange dateTimeRange : availableDateTime.getArrivalTimeRangeList()) {
            if (CollectionUtils.isEmpty(dateTimeRange.getTimeRangeList())) {
                unavailableDates.add(dateTimeRange.getDate());
            } else {
                availableDates.add(dateTimeRange.getDate());
            }
        }
        responseObserver.onNext(GetAvailableDatesResult.newBuilder()
                .addAllAvailableDates(availableDates)
                .addAllUnavailableDates(unavailableDates)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getEvaluationAvailableTime(
            GetEvaluationAvailableTimeParams request,
            StreamObserver<GetEvaluationAvailableTimeResult> responseObserver) {
        // 获取 evaluation 的 relation service ids
        var petServicesList = request.getPetServicesList();
        Set<Long> relationServiceIds = petServicesList.stream()
                .flatMap(petServices -> petServices.getEvaluationIdsList().stream())
                .collect(Collectors.toSet());

        var availableDateTime =
                obAvailableDateTimeServiceBlockingStub.getAvailableDateTime(GetAvailableDateTimeRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(request.getBusinessId())
                        .setFromDate(request.getDate())
                        .setToDate(request.getDate())
                        .setServiceItemType(ServiceItemType.EVALUATION)
                        .addAllRelationServiceIds(relationServiceIds)
                        .build());

        List<GetEvaluationAvailableTimeResult.DayAvailableTimeRanges> availableDateTimeRanges = new ArrayList<>();
        for (GetAvailableDateTimeResponse.DateTimeRange dateTimeRange : availableDateTime.getArrivalTimeRangeList()) {
            if (!CollectionUtils.isEmpty(dateTimeRange.getTimeRangeList())) {
                availableDateTimeRanges.add(GetEvaluationAvailableTimeResult.DayAvailableTimeRanges.newBuilder()
                        .setDate(dateTimeRange.getDate())
                        .addAllTimeRange(dateTimeRange.getTimeRangeList())
                        .build());
            }
        }

        responseObserver.onNext(GetEvaluationAvailableTimeResult.newBuilder()
                .addAllDayTimeRanges(availableDateTimeRanges)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void checkLodgingOverCapacity(
            CheckLodgingOverCapacityParams request, StreamObserver<CheckLodgingOverCapacityResult> responseObserver) {
        var businessId = request.getBusinessId();
        var startDate = request.getStartDate();
        var endDate = request.getEndDate();
        var companyId = AuthContext.get().companyId();
        var lodgingUnitIds = request.getLodgingUnitIdsList();
        var appointmentId = request.getAppointmentId();
        var changedLodgingUnit = request.getLodgingUnitChange();

        // check lodging view over capacity
        var checkLodgingViewOverCapacityResult = CompletableFuture.supplyAsync(
                () -> buildLodgingOverCapacityCheckResult(
                        companyId, businessId, startDate, endDate, lodgingUnitIds, appointmentId, changedLodgingUnit),
                ThreadPool.getSubmitExecutor());

        // check business closed date conflict
        var businessClosedDateCheckResult = CompletableFuture.supplyAsync(
                () -> buildBusinessClosedDateCheckResult(companyId, businessId, startDate, endDate),
                ThreadPool.getSubmitExecutor());

        CompletableFuture.allOf(checkLodgingViewOverCapacityResult, businessClosedDateCheckResult);

        responseObserver.onNext(CheckLodgingOverCapacityResult.newBuilder()
                .setLodgingOverCapacityCheckResult(checkLodgingViewOverCapacityResult.join())
                .setBusinessClosedDateCheckResult(businessClosedDateCheckResult.join())
                .build());
        responseObserver.onCompleted();
    }

    private LodgingOverCapacityCheckResult buildResult(
            List<LodgingUnitModel> overCapacityUnits,
            List<LodgingTypeModel> lodgingTypes,
            LodgingUnitOverview newUnitView) {

        List<LodgingTypeOverview> typeOverviews = lodgingTypes.stream()
                .map(type -> LodgingTypeOverview.newBuilder()
                        .setId(type.getId())
                        .setName(type.getName())
                        .build())
                .collect(Collectors.toList());

        List<LodgingUnitOverview> unitOverviews = overCapacityUnits.stream()
                .map(unit -> LodgingUnitOverview.newBuilder()
                        .setId(unit.getId())
                        .setName(unit.getName())
                        .setLodgingTypeId(unit.getLodgingTypeId())
                        .build())
                .collect(Collectors.toList());

        if (newUnitView != null && !unitOverviews.contains(newUnitView)) {
            unitOverviews.add(newUnitView);
        }

        return LodgingOverCapacityCheckResult.newBuilder()
                .addAllLodgingTypes(typeOverviews)
                .addAllLodgingUnits(unitOverviews)
                .build();
    }

    private boolean isOverCapacity(
            LodgingUnitModel unit, LodgingOccupiedStatus status, Map<Long, LodgingTypeModel> typeMap) {
        if (status == null) return false;

        LodgingTypeModel type = typeMap.get(unit.getId());
        if (type == null) return false;

        return status == LodgingOccupiedStatus.FULLY_OCCUPIED
                || (type.getLodgingUnitType() == LodgingUnitType.ROOM
                        && status == LodgingOccupiedStatus.PARTIALLY_OCCUPIED);
    }

    private List<LodgingUnitModel> updateLodgingUnitsForAppointment(
            Long companyId, Long businessId, GetPetDetailListResponse petDetailResp) {

        List<Long> petLodgingIds = PetDetailUtil.getPetDetailLodgingIDs(
                petDetailResp.getPetDetailsList(), petDetailResp.getPetEvaluationsList());

        return appointmentCheckerService.getLodgingUnitList(companyId, businessId, petLodgingIds);
    }

    private AppointmentModel getAppointment(Long companyId, Long businessId, Long appointmentId) {
        return appointmentService
                .getAppointment(GetAppointmentRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setAppointmentId(appointmentId)
                        .setCompanyId(companyId)
                        .build())
                .getAppointment();
    }
}
