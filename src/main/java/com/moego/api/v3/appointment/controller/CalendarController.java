package com.moego.api.v3.appointment.controller;

import static com.moego.api.v3.appointment.utils.CalendarCardUtil.buildCalendarCardsFromAppointment;
import static com.moego.api.v3.appointment.utils.CalendarCardUtil.buildCalendarCardsFromAppointments;
import static com.moego.api.v3.appointment.utils.CalendarCardUtil.buildCalendarCardsFromBlockTimes;
import static com.moego.api.v3.appointment.utils.CalendarCardUtil.buildCalendarCardsFromBookingRequests;
import static java.lang.Math.toIntExact;

import com.google.protobuf.Timestamp;
import com.google.type.Date;
import com.google.type.Interval;
import com.google.type.LatLng;
import com.moego.api.v3.appointment.converter.AppointmentConverter;
import com.moego.api.v3.appointment.converter.AutoAssignConverter;
import com.moego.api.v3.appointment.converter.CardConverter;
import com.moego.api.v3.appointment.converter.CertainAreaConverter;
import com.moego.api.v3.appointment.converter.CustomerConverter;
import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.api.v3.appointment.converter.PetCodeConverter;
import com.moego.api.v3.appointment.converter.PetDetailConverter;
import com.moego.api.v3.appointment.converter.PetVaccineConverter;
import com.moego.api.v3.appointment.converter.PreAuthConverter;
import com.moego.api.v3.appointment.converter.ServiceConverter;
import com.moego.api.v3.appointment.dto.CalendarCardFilterDTO;
import com.moego.api.v3.appointment.service.AppointmentService;
import com.moego.api.v3.appointment.service.BlockTimeService;
import com.moego.api.v3.appointment.service.BookingRequestService;
import com.moego.api.v3.appointment.service.CompanySettingService;
import com.moego.api.v3.appointment.service.OnlineBookingSlotService;
import com.moego.api.v3.appointment.service.PetDetailService;
import com.moego.api.v3.appointment.service.ServiceOperationService;
import com.moego.api.v3.appointment.utils.FutureService;
import com.moego.api.v3.appointment.utils.OrderUtil;
import com.moego.api.v3.business_customer.converter.CustomerAddressConverter;
import com.moego.api.v3.business_customer.service.BusinessCustomerAddressService;
import com.moego.api.v3.business_customer.service.BusinessCustomerService;
import com.moego.api.v3.business_customer.service.BusinessPetCodeService;
import com.moego.api.v3.business_customer.service.BusinessPetService;
import com.moego.api.v3.business_customer.service.BusinessPetVaccineService;
import com.moego.api.v3.business_customer.service.CustomerCertainAreaService;
import com.moego.api.v3.membership.service.MembershipService;
import com.moego.api.v3.organization.service.StaffService;
import com.moego.api.v3.shared.helper.BusinessHelper;
import com.moego.common.constant.CommonConstant;
import com.moego.common.enums.DepositPaymentTypeEnum;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.api.appointment.v1.CalendarCardEvaluationInfo;
import com.moego.idl.api.appointment.v1.CalendarCardPetCodeInfo;
import com.moego.idl.api.appointment.v1.CalendarCardPetInfo;
import com.moego.idl.api.appointment.v1.CalendarCardServiceInfo;
import com.moego.idl.api.appointment.v1.CalendarCardVaccineInfo;
import com.moego.idl.api.appointment.v1.CalendarCardView;
import com.moego.idl.api.appointment.v1.CalendarServiceGrpc;
import com.moego.idl.api.appointment.v1.ListDayCardsParams;
import com.moego.idl.api.appointment.v1.ListDayCardsResult;
import com.moego.idl.api.appointment.v1.ListDayCardsWithMixTypeParams;
import com.moego.idl.api.appointment.v1.ListDayCardsWithMixTypeResult;
import com.moego.idl.api.appointment.v1.ListDaySlotInfosParams;
import com.moego.idl.api.appointment.v1.ListDaySlotInfosResult;
import com.moego.idl.api.appointment.v1.ListMonthCardsParams;
import com.moego.idl.api.appointment.v1.ListMonthCardsResult;
import com.moego.idl.api.appointment.v1.PreviewCalendarCardsParams;
import com.moego.idl.api.appointment.v1.PreviewCalendarCardsResult;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentNoteModel;
import com.moego.idl.models.appointment.v1.AppointmentNoteType;
import com.moego.idl.models.appointment.v1.AppointmentScheduleDef;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.CalendarCardType;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v1.PetServiceCalendarScheduleDef;
import com.moego.idl.models.appointment.v1.ServiceOperationCalendarScheduleDef;
import com.moego.idl.models.appointment.v1.ServiceOperationModel;
import com.moego.idl.models.appointment.v1.ViewType;
import com.moego.idl.models.appointment.v1.WaitListStatus;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeBindingModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRecordBindingModel;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.membership.v1.MembershipSubscriptionListModel;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BookingRequestAssociatedModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.order.v1.InvoiceCalendarView;
import com.moego.idl.models.order.v1.OrderModelAppointmentView;
import com.moego.idl.models.organization.v1.StaffBasicView;
import com.moego.idl.models.payment.v1.DepositStatus;
import com.moego.idl.models.payment.v1.PreAuthCalendarView;
import com.moego.idl.models.payment.v1.PrePayCalendarView;
import com.moego.idl.service.appointment.v1.AppointmentScheduleServiceGrpc.AppointmentScheduleServiceBlockingStub;
import com.moego.idl.service.appointment.v1.ListAppointmentsRequest;
import com.moego.idl.service.appointment.v1.ListBlockTimesRequest;
import com.moego.idl.service.business_customer.v1.BusinessPetSizeServiceGrpc;
import com.moego.idl.service.business_customer.v1.ListPetSizeRequest;
import com.moego.idl.utils.v2.Predicate;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.proto.MoneyUtils;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.lib.utils.model.Pair;
import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.business.dto.PetSizeDTO;
import com.moego.server.customer.client.IPetBreedClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerPetPetCodeDTO;
import com.moego.server.customer.dto.GroomingCalenderCustomerInfo;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import com.moego.server.grooming.dto.SmartScheduleGroomingDetailsDTO;
import com.moego.server.payment.dto.PreAuthDTO;
import io.grpc.stub.StreamObserver;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/5/9
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class CalendarController extends CalendarServiceGrpc.CalendarServiceImplBase {

    private final FutureService futureService;
    private final MembershipService membershipService;

    private static final List<WaitListStatus> WAIT_LIST_STATUSES_FOR_APPOINTMENT =
            List.of(WaitListStatus.APPTONLY, WaitListStatus.APPTANDWAITLIST);
    private static final Set<CalendarCardType> FILL_INFO_CARD_TYPES = Set.of(
            CalendarCardType.APPOINTMENT,
            CalendarCardType.SERVICE,
            CalendarCardType.OPERATION,
            CalendarCardType.SERVICE_AND_OPERATION,
            CalendarCardType.BOOKING_REQUEST);
    private static final Set<Integer> APPOINTMENT_CARD_TYPES =
            Set.of(ServiceItemEnum.GROOMING.getBitValue(), ServiceItemEnum.EVALUATION.getBitValue());
    private final AppointmentScheduleServiceBlockingStub appointmentScheduleService;

    private final CompanySettingService companySettingService;
    private final BookingRequestService bookingRequestService;
    private final BlockTimeService blockTimeService;
    private final AppointmentService appointmentService;
    private final PetDetailService petDetailService;
    private final ServiceOperationService serviceOperationService;
    private final BusinessCustomerService businessCustomerService;
    private final BusinessCustomerAddressService customerAddressService;
    private final CustomerCertainAreaService certainAreaService;
    private final BusinessPetService businessPetService;
    private final BusinessPetCodeService petCodeService;
    private final BusinessPetVaccineService petVaccineService;
    private final BusinessPetSizeServiceGrpc.BusinessPetSizeServiceBlockingStub petSizeService;
    private final PermissionHelper permissionHelper;
    private final StaffService staffService;
    private final OnlineBookingSlotService onlineBookingSlotService;
    private final StaffService staffHelper;
    private final BusinessHelper businessHelper;
    private final BusinessPetSizeServiceGrpc.BusinessPetSizeServiceBlockingStub businessPetSizeServiceBlockingStub;
    private final IPetClient iPetClient;
    private final IPetBreedClient iPetBreedClient;

    /**
     * 包括 appointment, block time, booking request 三种数据源
     *
     * @param businessId        selected business id
     * @param startDate         filter card time start time
     * @param endDate           filter card time end date
     * @param filterNoStartTime filter no start time
     * @return list of calendar card
     */
    private List<ListDayCardsResult.CalendarCardCompositeView.Builder> listCalendarCard(
            long businessId, Date startDate, Date endDate, boolean filterNoStartTime) {
        long companyId = AuthContext.get().companyId();

        var blockCards = CompletableFuture.supplyAsync(
                () -> buildBlockCards(companyId, businessId, startDate, endDate), ThreadPool.getSubmitExecutor());

        var bookingRequestCards = CompletableFuture.supplyAsync(
                () -> buildBookingRequestCards(businessId, startDate, endDate), ThreadPool.getSubmitExecutor());

        var appointmentCards = CompletableFuture.supplyAsync(
                () -> buildAppointmentCards(companyId, businessId, startDate, endDate, filterNoStartTime),
                ThreadPool.getSubmitExecutor());

        return Stream.of(blockCards.join(), bookingRequestCards.join(), appointmentCards.join())
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listDayCards(ListDayCardsParams request, StreamObserver<ListDayCardsResult> responseObserver) {
        businessHelper.checkBusinessCompany(AuthContext.get().companyId(), request.getBusinessId());

        var cards = listCalendarCard(
                request.getBusinessId(), request.getStartDate(), request.getEndDate(), request.getFilterNoStartTime());
        if (CollectionUtils.isEmpty(cards)) {
            responseObserver.onNext(ListDayCardsResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // 组装数据
        fillCalendarCardInfo(request.getBusinessId(), cards);

        responseObserver.onNext(ListDayCardsResult.newBuilder()
                .addAllCards(splitCards(cards).stream()
                        .map(ListDayCardsResult.CalendarCardCompositeView.Builder::build)
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    private void fillCalendarCardInfo(
            long businessId, List<ListDayCardsResult.CalendarCardCompositeView.Builder> cards) {
        long companyId = AuthContext.get().companyId();
        long staffId = AuthContext.get().staffId();
        var queryCards = cards.stream()
                .filter(card -> FILL_INFO_CARD_TYPES.contains(card.getCardType()))
                .toList();
        Map<Long, Long> appointmentIdToCustomerId = queryCards.stream()
                .filter(model -> !Objects.equals(model.getAppointmentId(), 0L))
                .collect(Collectors.toMap(
                        ListDayCardsResult.CalendarCardCompositeView.Builder::getAppointmentId,
                        card -> card.getClientInfo().getClientId(),
                        (a, b) -> a));
        var appointmentIds = queryCards.stream()
                .map(ListDayCardsResult.CalendarCardCompositeView.Builder::getAppointmentId)
                .filter(id -> !Objects.equals(id, 0L))
                .distinct()
                .toList();
        var customerIds = queryCards.stream()
                .map(card -> card.getClientInfo().getClientId())
                .distinct()
                .toList();
        var serviceIds = queryCards.stream()
                .flatMap(card -> card.getPetListList().stream().flatMap(pet -> pet.getServiceListList().stream()
                        .map(ListDayCardsResult.CalendarCardServiceInfo::getServiceId)))
                .distinct()
                .toList();
        var evaluationIds = queryCards.stream()
                .flatMap(card -> card.getPetListList().stream().flatMap(pet -> pet.getEvaluationsList().stream()
                        .map(ListDayCardsResult.CalendarCardEvaluationInfo::getServiceId)))
                .distinct()
                .toList();
        var petIds = queryCards.stream()
                .flatMap(card -> card.getPetListList().stream().map(ListDayCardsResult.CalendarCardPetInfo::getPetId))
                .map(Integer::longValue)
                .distinct()
                .toList();

        var serviceFuture = futureService.getServiceMapByIds(companyId, serviceIds);
        var evaluationFuture = futureService.getEvaluationMapByIds(evaluationIds);
        var customerFuture = futureService.listGroomingCustomerInfo(staffId, customerIds);
        var newCustomerFuture = futureService.getNewCustomerIdList(companyId, customerIds);
        var clientAreaFuture = futureService.getClientAreaMap(businessId, customerFuture);
        var petWithCodeFuture = futureService.getPetInfoWithCodeMap(petIds);
        var petVaccineFuture = futureService.getPetVaccineMap(petIds);
        var ordersFuture = futureService.listOrders(appointmentIds);
        var invoiceFuture = futureService.getInvoiceCalendarViewMap(appointmentIds);
        var depositFuture = futureService.getBookOnlineDepositMap(appointmentIds);
        var requiredSignFuture = futureService.getRequiredSignAppointmentIdSet(businessId, appointmentIdToCustomerId);
        var preAuthFuture = futureService.getPreAuthMap(businessId, appointmentIds);
        var noteFuture = futureService.getAppointmentNoteMap(companyId, appointmentIds);
        final var membershipSubscriptionsFuture =
                futureService.supply(() -> membershipService.getAllSubscriptions(customerIds));
        CompletableFuture.allOf(
                        serviceFuture,
                        evaluationFuture,
                        customerFuture,
                        newCustomerFuture,
                        clientAreaFuture,
                        petWithCodeFuture,
                        petVaccineFuture,
                        ordersFuture,
                        invoiceFuture,
                        depositFuture,
                        requiredSignFuture,
                        preAuthFuture,
                        noteFuture,
                        membershipSubscriptionsFuture)
                .join();

        var serviceMap = serviceFuture.join();
        var evaluationMap = evaluationFuture.join();
        var customerMap = customerFuture.join();
        var newCustomerMap = newCustomerFuture.join();
        var clientAreaMap = clientAreaFuture.join();
        var petWithCodeMap = petWithCodeFuture.join();
        var petVaccineMap = petVaccineFuture.join();
        var ordersMap = ordersFuture.join();
        var invoiceMap = invoiceFuture.join();
        var depositMap = depositFuture.join();
        var requiredSignSet = requiredSignFuture.join();
        var preAuthMap = preAuthFuture.join();
        var noteMap = noteFuture.join();
        final var membershipSubscriptionsMap = membershipSubscriptionsFuture.join();

        cards.stream()
                .filter(card -> FILL_INFO_CARD_TYPES.contains(card.getCardType()))
                .forEach(card -> {
                    fillCalendarCardClientInfo(
                            card, customerMap, newCustomerMap, clientAreaMap, membershipSubscriptionsMap);

                    fillCalendarCardPetInfo(card, serviceMap, evaluationMap, petWithCodeMap, petVaccineMap, Map.of());

                    fillGroomingNote(card, noteMap);

                    card.setRequiredSign(requiredSignSet.contains(card.getAppointmentId()));

                    fillPaymentInfo(card, invoiceMap, depositMap, preAuthMap, serviceMap, ordersMap);
                });
    }

    private void fillCalendarMonthCardInfo(List<ListDayCardsResult.CalendarCardCompositeView.Builder> cards) {
        long companyId = AuthContext.get().companyId();
        long staffId = AuthContext.get().staffId();
        var queryCards = cards.stream()
                .filter(card -> FILL_INFO_CARD_TYPES.contains(card.getCardType()))
                .toList();
        var customerIds = queryCards.stream()
                .map(card -> card.getClientInfo().getClientId())
                .distinct()
                .toList();
        var serviceIds = queryCards.stream()
                .flatMap(card -> card.getPetListList().stream().flatMap(pet -> pet.getServiceListList().stream()
                        .map(ListDayCardsResult.CalendarCardServiceInfo::getServiceId)))
                .distinct()
                .toList();
        var evaluationIds = queryCards.stream()
                .flatMap(card -> card.getPetListList().stream().flatMap(pet -> pet.getEvaluationsList().stream()
                        .map(ListDayCardsResult.CalendarCardEvaluationInfo::getServiceId)))
                .distinct()
                .toList();
        var petIds = queryCards.stream()
                .flatMap(card -> card.getPetListList().stream().map(ListDayCardsResult.CalendarCardPetInfo::getPetId))
                .map(Integer::longValue)
                .distinct()
                .toList();
        var serviceFuture = futureService.getServiceMapByIds(companyId, serviceIds);
        var evaluationFuture = futureService.getEvaluationMapByIds(evaluationIds);
        var customerFuture = futureService.listGroomingCustomerInfo(staffId, customerIds);
        var membershipSubscriptionsFuture =
                futureService.supply(() -> membershipService.getAllSubscriptions(customerIds));
        var petWithCodeFuture = futureService.getPetInfoWithCodeMap(petIds);
        var petSizeFuture = futureService.supply(() -> petSizeService.listPetSize(
                ListPetSizeRequest.newBuilder().setCompanyId(companyId).build()));
        CompletableFuture.allOf(serviceFuture, evaluationFuture, customerFuture, petWithCodeFuture, petSizeFuture)
                .join();
        var serviceMap = serviceFuture.join();
        var evaluationMap = evaluationFuture.join();
        var customerMap = customerFuture.join();
        var membershipSubscriptionMap = membershipSubscriptionsFuture.join();
        var petWithCodeMap = petWithCodeFuture.join();
        var petSizeMap = petSizeFuture.join().getSizesList().stream()
                .collect(Collectors.toMap(
                        petSize -> Pair.of(petSize.getWeightLow(), petSize.getWeightHigh()),
                        Function.identity(),
                        (a, b) -> a));
        cards.stream()
                .filter(card -> FILL_INFO_CARD_TYPES.contains(card.getCardType()))
                .forEach(card -> {
                    fillCalendarCardClientInfo(card, customerMap, List.of(), Map.of(), membershipSubscriptionMap);
                    fillCalendarCardPetInfo(card, serviceMap, evaluationMap, petWithCodeMap, Map.of(), petSizeMap);
                });
    }

    private List<ListDayCardsResult.CalendarCardCompositeView.Builder> buildBlockCards(
            long companyId, long businessId, Date startDate, Date endDate) {
        String timezone = companySettingService.mustGetTimeZoneName(companyId);

        var blockTimes = futureService
                .listBlockTimes(companyId, List.of(businessId), buildBlockTimeFilter(startDate, endDate, timezone))
                .join();
        if (CollectionUtils.isEmpty(blockTimes)) {
            return List.of();
        }
        return blockTimes.stream()
                .map(block -> ListDayCardsResult.CalendarCardCompositeView.newBuilder()
                        .setCardType(CalendarCardType.BLOCK)
                        .setId(block.getId())
                        .setAppointmentId(block.getId())
                        .setDate(block.getStartDate())
                        .setStartTime(block.getStartTime())
                        .setEndDate(block.getEndDate())
                        .setEndTime(block.getEndTime())
                        .setStaffId(block.getStaffId())
                        .setColorCode(block.getColorCode())
                        .setTicketComments(block.getDescription())
                        .setRepeatId(block.getRepeatId()))
                .toList();
    }

    private List<ListDayCardsResult.CalendarCardCompositeView.Builder> buildBookingRequestCards(
            long businessId, Date startDate, Date endDate) {
        boolean hasPermission = permissionHelper.hasPermission(
                AuthContext.get().companyId(),
                AuthContext.get().staffId(),
                PermissionEnums.ACCESS_BOOKING_REQUEST_AND_WAITING_LIST);
        if (!hasPermission) {
            return List.of();
        }

        // 仅 grooming / evaluation / dog walking 的 booking request 需要展示在 calendar view
        var bookingRequests = futureService
                .listBookingRequests(
                        List.of(businessId),
                        startDate,
                        endDate,
                        List.of(
                                BookingRequestAssociatedModel.SERVICE,
                                BookingRequestAssociatedModel.ADD_ON,
                                BookingRequestAssociatedModel.AUTO_ASSIGN),
                        List.of(ServiceItemType.GROOMING, ServiceItemType.EVALUATION, ServiceItemType.DOG_WALKING))
                .join();
        if (CollectionUtils.isEmpty(bookingRequests)) {
            return List.of();
        }
        return bookingRequests.stream()
                .map(obRequest -> {
                    var builder = ListDayCardsResult.CalendarCardCompositeView.newBuilder()
                            .setCardType(CalendarCardType.BOOKING_REQUEST)
                            .setId(obRequest.getId())
                            .setBookingRequestId(obRequest.getId())
                            .setDate(obRequest.getStartDate())
                            .setStartTime(obRequest.getStartTime())
                            .setEndDate(obRequest.getEndDate())
                            .setEndTime(obRequest.getEndTime())
                            .setClientInfo(buildCalendarCardClientInfo(obRequest))
                            .addAllPetList(buildCalendarCardPetInfo(obRequest));
                    fillGroomingInfo(builder, obRequest);
                    return builder;
                })
                .toList();
    }

    private ListDayCardsResult.CalendarCardClientInfo buildCalendarCardClientInfo(BookingRequestModel obRequest) {
        return ListDayCardsResult.CalendarCardClientInfo.newBuilder()
                .setClientId(obRequest.getCustomerId())
                .build();
    }

    private List<ListDayCardsResult.CalendarCardPetInfo> buildCalendarCardPetInfo(BookingRequestModel obRequest) {
        Map<Long, ListDayCardsResult.CalendarCardPetInfo.Builder> petMap = new HashMap<>();
        obRequest.getServicesList().forEach(s -> {
            switch (s.getServiceCase()) {
                case GROOMING -> {
                    var service = s.getGrooming().getService();
                    if (!petMap.containsKey(service.getPetId())) {
                        petMap.put(
                                service.getPetId(),
                                ListDayCardsResult.CalendarCardPetInfo.newBuilder()
                                        .setPetId((int) service.getPetId()));
                    }
                    petMap.get(service.getPetId()).addServiceList(ServiceConverter.INSTANCE.toCardServiceInfo(service));
                    s.getGrooming().getAddonsList().forEach(addOn -> petMap.get(addOn.getPetId())
                            .addServiceList(ServiceConverter.INSTANCE.toCardServiceInfo(addOn)));
                }
                case EVALUATION -> {
                    var service = s.getEvaluation().getService();
                    if (!petMap.containsKey(service.getPetId())) {
                        petMap.put(
                                service.getPetId(),
                                ListDayCardsResult.CalendarCardPetInfo.newBuilder()
                                        .setPetId((int) service.getPetId()));
                    }
                    petMap.get(service.getPetId())
                            .addEvaluations(ServiceConverter.INSTANCE.toCardEvaluationInfo(service));
                }
                case DOG_WALKING -> {
                    var service = s.getDogWalking().getService();
                    if (!petMap.containsKey(service.getPetId())) {
                        petMap.put(
                                service.getPetId(),
                                ListDayCardsResult.CalendarCardPetInfo.newBuilder()
                                        .setPetId((int) service.getPetId()));
                    }
                    petMap.get(service.getPetId()).addServiceList(ServiceConverter.INSTANCE.toCardServiceInfo(service));
                }
                default -> {}
            }
        });
        return petMap.values().stream()
                .map(ListDayCardsResult.CalendarCardPetInfo.Builder::build)
                .toList();
    }

    private void fillGroomingInfo(
            ListDayCardsResult.CalendarCardCompositeView.Builder builder, BookingRequestModel obRequest) {
        if (Objects.equals(obRequest.getServiceTypeInclude(), ServiceItemEnum.GROOMING.getBitValue())) {
            if (!Objects.equals(obRequest.getAppointmentId(), 0L)) {
                builder.setAppointmentId(obRequest.getAppointmentId());
            }
            obRequest.getServicesList().stream()
                    .filter(BookingRequestModel.Service::hasGrooming)
                    .map(BookingRequestModel.Service::getGrooming)
                    .findFirst()
                    .ifPresent(grooming -> {
                        if (!Objects.equals(grooming.getService().getStaffId(), 0L)) {
                            builder.setStaffId(grooming.getService().getStaffId());
                        }
                        if (grooming.hasAutoAssign()) {
                            builder.setAutoAssign(AutoAssignConverter.INSTANCE.modelToView(grooming.getAutoAssign()));
                        }
                    });
        }
    }

    private double calculateEstimatedTotalPrice(
            ListDayCardsResult.CalendarCardCompositeView.Builder card, Map<Long, ServiceBriefView> serviceMap) {
        double serviceTotalPrice = card.getPetListList().stream()
                .flatMap(pet -> pet.getServiceListList().stream())
                .mapToDouble(detail -> {
                    ServiceBriefView service = serviceMap.get(detail.getServiceId());
                    if (service == null) {
                        return detail.getServicePrice();
                    }
                    return switch (service.getPriceUnit()) {
                        case PER_SESSION -> detail.getServicePrice();
                        case PER_HOUR -> detail.getServicePrice() * Math.ceil(detail.getServiceTime() / 60.0);
                        default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unsupported price unit");
                    };
                })
                .sum();
        double evaluationTotalPrice = card.getPetListList().stream()
                .flatMap(pet -> pet.getEvaluationsList().stream())
                .mapToDouble(ListDayCardsResult.CalendarCardEvaluationInfo::getServicePrice)
                .sum();
        return serviceTotalPrice + evaluationTotalPrice;
    }

    private void fillPaymentInfo(
            ListDayCardsResult.CalendarCardCompositeView.Builder card,
            Map<Long, InvoiceCalendarView> invoiceMap,
            Map<Long, BookOnlineDepositDTO> depositMap,
            Map<Integer, PreAuthDTO> preAuthMap,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, List<OrderModelAppointmentView>> ordersMap) {
        // 单张卡片的总价 = 所有 pet 的所有 service 的价格之和
        card.setEstimatedTotalPrice(calculateEstimatedTotalPrice(card, serviceMap));
        Optional.ofNullable(invoiceMap.get(card.getAppointmentId())).ifPresent(card::setInvoice);
        //        Optional.ofNullable(invoiceMap.get(card.getAppointmentId()))
        //                .ifPresent(dto -> card.setPaidAmount(dto.getPaidAmount()));
        Optional.ofNullable(depositMap.get(card.getAppointmentId()))
                .filter(dto -> Objects.equals(dto.getDepositType(), DepositPaymentTypeEnum.PrePay))
                .ifPresent(dto -> card.setPrepaidAmount(dto.getAmount().doubleValue())
                        .setPrepayStatus(DepositStatus.forNumber(dto.getStatus())));
        Optional.ofNullable(preAuthMap.get((int) card.getAppointmentId()))
                .ifPresent(dto -> card.setPreAuthInfo(PreAuthConverter.INSTANCE.toView(dto)));

        var orders = ordersMap.getOrDefault(card.getAppointmentId(), List.of());
        card.setPaidAmount(MoneyUtils.fromGoogleMoney(OrderUtil.calculateTotalPaidAmount(orders))
                        .doubleValue())
                .addAllOrders(orders);
    }

    private void fillGroomingNote(
            ListDayCardsResult.CalendarCardCompositeView.Builder card, Map<Long, List<AppointmentNoteModel>> noteMap) {
        Optional.ofNullable(noteMap.get(card.getAppointmentId())).ifPresent(notes -> {
            var typeMap = notes.stream()
                    .collect(Collectors.toMap(AppointmentNoteModel::getType, Function.identity(), (a, b) -> a));
            Optional.ofNullable(typeMap.get(AppointmentNoteType.COMMENT))
                    .ifPresent(note -> card.setTicketComments(note.getNote()));
            Optional.ofNullable(typeMap.get(AppointmentNoteType.ALERT_NOTES))
                    .ifPresent(note -> card.setAlertNotes(note.getNote()));
        });
    }

    private void fillCalendarCardClientInfo(
            ListDayCardsResult.CalendarCardCompositeView.Builder card,
            Map<Long, GroomingCalenderCustomerInfo> customerMap,
            List<Long> newCustomerIds,
            Map<Long, List<CertainAreaDTO>> clientAreaMap,
            Map<Long, MembershipSubscriptionListModel> membershipSubscriptionsMap) {
        long customerId = card.getClientInfo().getClientId();
        GroomingCalenderCustomerInfo customer = customerMap.get(customerId);
        if (customer == null) {
            throw ExceptionUtil.bizException(Code.CODE_CUSTOMER_NOT_FOUND);
        }
        card.setClientInfo(CustomerConverter.INSTANCE.toCardClientInfo(customer).toBuilder()
                .setIsNewClient(newCustomerIds.contains(customerId))
                .setFullAddress(CommonUtil.getFullAddress(
                        customer.getAddress1(),
                        customer.getAddress2(),
                        customer.getCity(),
                        customer.getState(),
                        customer.getCountry(),
                        customer.getZipcode()))
                .addAllAreas(CertainAreaConverter.INSTANCE.toView(clientAreaMap.getOrDefault(customerId, List.of())))
                .build());
        card.setMembershipSubscriptions(membershipSubscriptionsMap.getOrDefault(
                customerId, MembershipSubscriptionListModel.getDefaultInstance()));
        // lat lng 处理
        if (StringUtils.hasText(customer.getLat()) && StringUtils.hasText(customer.getLng())) {
            try {
                double latitude = Double.parseDouble(customer.getLat());
                double longitude = Double.parseDouble(customer.getLng());
                card.setClientInfo(card.getClientInfo().toBuilder()
                        .setCoordinate(LatLng.newBuilder()
                                .setLatitude(latitude)
                                .setLongitude(longitude)
                                .build())
                        .build());
            } catch (NumberFormatException e) {
                log.info("lat lnt convert error {}", customerId, e);
            }
        }
    }

    private void fillCalendarCardPetInfo(
            ListDayCardsResult.CalendarCardCompositeView.Builder card,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, EvaluationBriefView> evaluationMap,
            Map<Long, CustomerPetPetCodeDTO> petWithCodeMap,
            Map<Long, List<VaccineBindingRecordDto>> petVaccineMap,
            final Map<Pair<Integer, Integer>, BusinessPetSizeModel> petSizeMap) {
        var pets = card.getPetListList().stream()
                .map(pet -> {
                    CustomerPetPetCodeDTO petInfo = petWithCodeMap.get((long) pet.getPetId());
                    if (petInfo == null) {
                        throw ExceptionUtil.bizException(Code.CODE_PET_NOT_FOUND);
                    }
                    var details = fillServiceDetails(pet.getServiceListList(), serviceMap);
                    var evaluationDetails = fillEvaluationInfo(pet.getEvaluationsList(), evaluationMap);

                    return ListDayCardsResult.CalendarCardPetInfo.newBuilder()
                            .setPetId(petInfo.getPetId())
                            .setPetName(petInfo.getPetName())
                            .setPetBreedName(petInfo.getBreed())
                            .setPetType(PetType.forNumber(petInfo.getPetTypeId()))
                            .addAllPetCodeList(PetCodeConverter.INSTANCE.toView(petInfo.getMoePetCodeInfos()))
                            .addAllServiceList(details)
                            .addAllEvaluations(evaluationDetails)
                            .setEnableVaccineExpiryNotification(
                                    Objects.equals(petInfo.getExpiryNotification(), CommonConstant.ENABLE))
                            .addAllVaccines(PetVaccineConverter.INSTANCE.toInfoView(petVaccineMap.getOrDefault(
                                    petInfo.getPetId().longValue(), List.of())))
                            .setPetSizeId(buildCalendarCardPetSizeInfo(petInfo, petSizeMap))
                            .build();
                })
                .toList();
        card.clearPetList().addAllPetList(pets);
    }

    private List<ListDayCardsResult.CalendarCardServiceInfo> fillServiceDetails(
            List<ListDayCardsResult.CalendarCardServiceInfo> serviceInfos, Map<Long, ServiceBriefView> serviceMap) {
        return serviceInfos.stream()
                .map(detail -> {
                    ServiceBriefView service = serviceMap.get(detail.getServiceId());
                    if (service == null) {
                        return detail;
                    }
                    // Service time & price 取快照，其他信息从 service 中获取
                    return ListDayCardsResult.CalendarCardServiceInfo.newBuilder()
                            .setPetDetailId(detail.getPetDetailId())
                            .setServiceId(detail.getServiceId())
                            .setServiceTime(detail.getServiceTime())
                            .setServicePrice(detail.getServicePrice())
                            .setServiceName(service.getName())
                            .setColorCode(service.getColorCode())
                            .build();
                })
                .toList();
    }

    private List<ListDayCardsResult.CalendarCardEvaluationInfo> fillEvaluationInfo(
            List<ListDayCardsResult.CalendarCardEvaluationInfo> evaluationInfos,
            Map<Long, EvaluationBriefView> evaluationMap) {
        return evaluationInfos.stream()
                .map(detail -> {
                    EvaluationBriefView evaluation = evaluationMap.get(detail.getServiceId());
                    return ListDayCardsResult.CalendarCardEvaluationInfo.newBuilder()
                            .setEvaluationDetailId(detail.getEvaluationDetailId())
                            .setServiceId(detail.getServiceId())
                            .setServiceTime(detail.getServiceTime())
                            .setServicePrice(detail.getServicePrice())
                            .setServiceName(evaluation.getName())
                            .setColorCode(evaluation.getColorCode())
                            .build();
                })
                .toList();
    }

    /**
     * 过滤出有日期和时间的 grooming pet detail 详情，并且在筛选时间范围内
     *
     * @param appointments   appointment details
     * @param serviceDetails pet service details
     * @param startDate      filter start date
     * @param endDate        filter end date
     * @return filtered pet details
     */
    private Map<Long, List<PetDetailModel>> buildPetDetails(
            List<AppointmentModel> appointments,
            List<PetDetailModel> serviceDetails,
            LocalDate startDate,
            LocalDate endDate) {
        Set<Long> noStartTimeIds = appointments.stream()
                .filter(AppointmentModel::getNoStartTime)
                .map(AppointmentModel::getId)
                .collect(Collectors.toSet());
        return serviceDetails.stream()
                .filter(petDetail -> hasDateTime(petDetail, noStartTimeIds)
                        && showOnCalendar(petDetail)
                        && matchFilterTime(petDetail, startDate, endDate))
                .collect(Collectors.groupingBy(PetDetailModel::getGroomingId));
    }

    private boolean hasDateTime(PetDetailModel petDetail, Set<Long> noStartTimeIds) {
        return StringUtils.hasText(petDetail.getStartDate())
                && StringUtils.hasText(petDetail.getEndDate())
                && !noStartTimeIds.contains(petDetail.getGroomingId());
    }

    /**
     * 需要展示在 calendar 上的 pet detail <br>
     * 1. Grooming 类型的 service & add-on <br>
     * 2. Boarding/Daycare 类型有 staff 的 add-on <br>
     *
     * @param petDetail pet detail
     * @return true if show on calendar
     */
    private boolean showOnCalendar(PetDetailModel petDetail) {
        boolean isGrooming = Objects.equals(petDetail.getServiceItemType(), ServiceItemType.GROOMING);
        boolean isDogWalking = Objects.equals(petDetail.getServiceItemType(), ServiceItemType.DOG_WALKING);
        boolean isBoardingOrDaycare = Objects.equals(petDetail.getServiceItemType(), ServiceItemType.BOARDING)
                || Objects.equals(petDetail.getServiceItemType(), ServiceItemType.DAYCARE);
        boolean hasStaff = petDetail.getStaffId() != 0;

        return isGrooming || isDogWalking || (isBoardingOrDaycare && hasStaff);
    }

    private boolean matchFilterTime(PetDetailModel petDetail, LocalDate startDate, LocalDate endDate) {
        return !LocalDate.parse(petDetail.getStartDate()).isAfter(endDate)
                && !LocalDate.parse(petDetail.getEndDate()).isBefore(startDate);
    }

    private Map<Long, EvaluationServiceModel> buildEvaluationMap(
            List<EvaluationServiceModel> evaluations, LocalDate startDate, LocalDate endDate) {
        return evaluations.stream()
                .filter(evaluation -> StringUtils.hasText(evaluation.getStartDate())
                        && StringUtils.hasText(evaluation.getEndDate())
                        && !LocalDate.parse(evaluation.getStartDate()).isAfter(endDate)
                        && !LocalDate.parse(evaluation.getEndDate()).isBefore(startDate))
                .collect(Collectors.toMap(EvaluationServiceModel::getAppointmentId, Function.identity(), (a, b) -> a));
    }

    private List<ListDayCardsResult.CalendarCardCompositeView.Builder> buildAppointmentCards(
            long companyId, long businessId, Date startDate, Date endDate, boolean filterNoStartTime) {
        String timezone = companySettingService.mustGetTimeZoneName(companyId);

        var accessStaffIds = new HashSet<>(staffService.getShowOnCalendarStaffIds(
                companyId, businessId, AuthContext.get().staffId()));

        var appointments = futureService
                .listAppointments(
                        companyId,
                        List.of(businessId),
                        buildAppointmentFilter(startDate, endDate, filterNoStartTime, timezone, accessStaffIds))
                .join();
        if (CollectionUtils.isEmpty(appointments)) {
            return List.of();
        }
        List<Long> appointmentIds =
                appointments.stream().map(AppointmentModel::getId).toList();

        var petDetailFuture = futureService.getPetDetailList(companyId, appointmentIds);
        var serviceOperationFuture = futureService.getServiceOperationMap(companyId, appointmentIds);

        LocalDate start = toLocalDate(startDate);
        LocalDate end = toLocalDate(endDate);
        var petDetailMap = buildPetDetails(appointments, petDetailFuture.join().getPetDetailsList(), start, end);
        var evaluationMap = buildEvaluationMap(petDetailFuture.join().getPetEvaluationsList(), start, end);
        if (CollectionUtils.isEmpty(petDetailMap) && CollectionUtils.isEmpty(evaluationMap)) {
            return List.of();
        }
        var serviceOperationMap = serviceOperationFuture.join();

        return buildAppointmentCard(appointments, petDetailMap, evaluationMap, serviceOperationMap);
    }

    private int getPetIndex(
            AtomicReference<ListDayCardsResult.CalendarCardCompositeView.Builder> lastCard, long petId) {
        for (int i = 0; i < lastCard.get().getPetListList().size(); i++) {
            var pet = lastCard.get().getPetListList().get(i);
            if (Objects.equals((long) pet.getPetId(), petId)) {
                return i;
            }
        }
        return -1;
    }

    private PetDetailModel toPetDetailModel(EvaluationServiceModel evaluation) {
        PetDetailModel.Builder builder = PetDetailModel.newBuilder()
                .setId(evaluation.getId())
                .setGroomingId(evaluation.getAppointmentId())
                .setPetId(evaluation.getPetId())
                .setServiceId(evaluation.getServiceId())
                .setServiceTime(evaluation.getServiceTime())
                .setServicePrice(evaluation.getServicePrice())
                .setStartTime(evaluation.getStartTime())
                .setEndTime(evaluation.getEndTime())
                .setStartDate(evaluation.getStartDate())
                .setEndDate(evaluation.getEndDate())
                .setServiceItemType(ServiceItemType.EVALUATION);
        if (evaluation.hasStaffId()) {
            builder.setStaffId(evaluation.getStaffId());
        }
        return builder.build();
    }

    private List<PetDetailModel> buildServiceDetails(
            List<PetDetailModel> petDetails, EvaluationServiceModel evaluationDetail) {
        // 聚合 pet details 和 evaluation details
        var serviceDetails = Stream.concat(
                        petDetails == null ? Stream.empty() : petDetails.stream(),
                        evaluationDetail == null ? Stream.empty() : Stream.of(toPetDetailModel(evaluationDetail)))
                .collect(Collectors.toList());
        // petId - earliestStartTime
        Map<Long, Integer> earliestStartTimesByPetId = serviceDetails.stream()
                .collect(Collectors.groupingBy(
                        PetDetailModel::getPetId,
                        Collectors.mapping(PetDetailModel::getStartTime, Collectors.minBy(Integer::compareTo))))
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().orElse(0)));
        serviceDetails.sort(
                Comparator.comparing((PetDetailModel petDetail) -> earliestStartTimesByPetId.get(petDetail.getPetId()))
                        .thenComparing(PetDetailModel::getPetId)
                        .thenComparing(PetDetailModel::getStaffId)
                        .thenComparing(PetDetailModel::getStartTime)
                        .thenComparing(PetDetailModel::getEndTime));
        return serviceDetails;
    }

    private List<ListDayCardsResult.CalendarCardCompositeView.Builder> buildAppointmentCard(
            List<AppointmentModel> appointments,
            Map<Long, List<PetDetailModel>> petDetailMap,
            Map<Long, EvaluationServiceModel> evaluationMap,
            Map<Long, List<ServiceOperationModel>> apptOperationMap) {
        List<ListDayCardsResult.CalendarCardCompositeView.Builder> result = new ArrayList<>();
        appointments.forEach(appointment -> {
            List<ListDayCardsResult.CalendarCardCompositeView.Builder> cards = new ArrayList<>();
            var petDetails = petDetailMap.get(appointment.getId());
            var evaluationDetail = evaluationMap.get(appointment.getId());
            // 聚合 pet details 和 evaluation details 并按每只 pet 的 service start time 排序
            var serviceDetails = buildServiceDetails(petDetails, evaluationDetail);
            if (ObjectUtils.isEmpty(serviceDetails)) {
                return;
            }
            var serviceOperationMap = apptOperationMap.getOrDefault(appointment.getId(), List.of()).stream()
                    .collect(Collectors.groupingBy(ServiceOperationModel::getGroomingServiceId));

            AtomicReference<ListDayCardsResult.CalendarCardCompositeView.Builder> lastCard = new AtomicReference<>();
            serviceDetails.forEach(petDetail -> {
                /*
                以下情况需要合并卡片：
                1. 前一张卡片类型是 service
                2. 当前 service 没有 operation
                3. 当前 service 的 startTime 与前一张卡片的 endTime 相同
                4. 当前 service 的 staffId 与前一张卡片的 staffId 相同
                 */
                if (hasMergeCard(lastCard.get(), petDetail, serviceOperationMap)) {
                    // 合并卡片，将 endDate & endTime 延长，并且将当前 service 添加到 lastCard 中
                    lastCard.get().setEndDate(petDetail.getEndDate());
                    lastCard.get().setEndTime(petDetail.getEndTime());
                    lastCard.get().getPetListList().stream()
                            .filter(pet -> Objects.equals((long) pet.getPetId(), petDetail.getPetId()))
                            .findFirst()
                            .ifPresentOrElse(
                                    pet -> {
                                        // pet 已存在，只需要添加 service
                                        int petIndex = getPetIndex(lastCard, petDetail.getPetId());
                                        addServiceOrEvaluation(lastCard.get().getPetListBuilder(petIndex), petDetail);
                                    },
                                    () -> {
                                        // pet 不存在，需要新建 pet
                                        var pet = addServiceOrEvaluation(
                                                ListDayCardsResult.CalendarCardPetInfo.newBuilder()
                                                        .setPetId((int) petDetail.getPetId()),
                                                petDetail);
                                        lastCard.get().addPetList(pet);
                                    });

                } else {
                    // 新建下一张 operation / service 卡片，仅 grooming 支持 multi-staff
                    if (Objects.equals(petDetail.getServiceItemType(), ServiceItemType.GROOMING)
                            && serviceOperationMap.containsKey(petDetail.getId())) {
                        var staffIds = serviceOperationMap.get(petDetail.getId()).stream()
                                .map(ServiceOperationModel::getStaffId)
                                .collect(Collectors.toSet());
                        serviceOperationMap.get(petDetail.getId()).forEach(operation -> {
                            var card = buildOperationCardBuilder(appointment, petDetail, operation, staffIds);
                            cards.add(card);
                            lastCard.set(card);
                        });
                    } else {
                        var card = buildServiceCardBuilder(appointment, petDetail);
                        cards.add(card);
                        lastCard.set(card);
                    }
                }
            });
            cards.forEach(card -> card.setAppointmentId(appointment.getId())
                    .setColorCode(appointment.getColorCode())
                    .addAllServiceItemTypes(
                            ServiceItemEnum.convertBitValueList(appointment.getServiceTypeInclude()).stream()
                                    .map(type -> ServiceItemType.forNumber(type.getServiceItem()))
                                    .toList())
                    .setAppointmentStatus(
                            getCompatibleAppointmentStatus(appointment.getStatus(), appointment.getCheckInTime()))
                    .setPaymentStatus(appointment.getIsPaid())
                    .setRepeatId(appointment.getRepeatId())
                    .setClientInfo(ListDayCardsResult.CalendarCardClientInfo.newBuilder()
                            .setClientId(appointment.getCustomerId())
                            .build())
                    .addAllPetDetailIds(card.getPetListList().stream()
                            .flatMap(pet -> pet.getServiceListList().stream())
                            .map(ListDayCardsResult.CalendarCardServiceInfo::getPetDetailId)
                            .distinct()
                            .toList()));
            if (isAppointmentCard(cards, appointment)) {
                var card = cards.get(0);
                boolean allowedNoAssignedStaff = isEvaluationAppointment(appointment);
                card.setCardType(CalendarCardType.APPOINTMENT)
                        .setId(appointment.getId())
                        .setDraggableInfo(ListDayCardsResult.CalendarCardDraggableInfo.newBuilder()
                                .setDraggable(true)
                                .addAllUnavailableStaffList(List.of())
                                .setAllowedNoAssignedStaff(allowedNoAssignedStaff)
                                .build());
            }
            result.addAll(cards);
        });
        return result;
    }

    private boolean isAppointmentCard(
            List<ListDayCardsResult.CalendarCardCompositeView.Builder> cards, AppointmentModel appointment) {
        return cards.size() == 1 && APPOINTMENT_CARD_TYPES.contains(appointment.getServiceTypeInclude());
    }

    private boolean isEvaluationAppointment(AppointmentModel appointment) {
        return appointment.getServiceTypeInclude() == ServiceItemEnum.EVALUATION.getBitValue();
    }

    private ListDayCardsResult.CalendarCardPetInfo.Builder addServiceOrEvaluation(
            ListDayCardsResult.CalendarCardPetInfo.Builder petBuilder, PetDetailModel petDetail) {
        if (Objects.equals(petDetail.getServiceItemType(), ServiceItemType.EVALUATION)) {
            petBuilder.addEvaluations(ListDayCardsResult.CalendarCardEvaluationInfo.newBuilder()
                    .setEvaluationDetailId(petDetail.getId())
                    .setServiceId(petDetail.getServiceId())
                    .setServiceTime(petDetail.getServiceTime())
                    .setServicePrice(petDetail.getServicePrice())
                    .build());
        } else {
            petBuilder.addServiceList(ListDayCardsResult.CalendarCardServiceInfo.newBuilder()
                    .setPetDetailId((int) petDetail.getId())
                    .setServiceId(petDetail.getServiceId())
                    .setServiceTime(petDetail.getServiceTime())
                    .setServicePrice(petDetail.getServicePrice())
                    .build());
        }
        return petBuilder;
    }

    private List<ListDayCardsResult.CalendarCardCompositeView.Builder> splitCards(
            List<ListDayCardsResult.CalendarCardCompositeView.Builder> cards) {
        return cards.stream()
                .map(card -> {
                    if (card.getDate().equals(card.getEndDate())) {
                        return List.of(card);
                    }
                    List<ListDayCardsResult.CalendarCardCompositeView.Builder> splitCards = new ArrayList<>();
                    for (String currentDate = card.getDate();
                            currentDate.compareTo(card.getEndDate()) <= 0;
                            currentDate = DateUtil.getStrDateByDaysDiff(currentDate, -1L)) {
                        var splitCard = card.build().toBuilder();
                        splitCard.setDate(currentDate);
                        splitCard.setEndDate(currentDate);
                        if (currentDate.compareTo(card.getEndDate()) == 0) {
                            splitCard.setEndTime(card.getEndTime());
                        } else {
                            splitCard.setEndTime(24 * 60 - 1L);
                        }
                        if (currentDate.compareTo(card.getDate()) > 0) {
                            splitCard.setStartTime(0L);
                        }
                        splitCards.add(splitCard);
                    }
                    return splitCards;
                })
                .flatMap(List::stream)
                .peek(card -> card.setCardId(buildCardId(card)))
                .sorted(Comparator.comparing(ListDayCardsResult.CalendarCardCompositeView.Builder::getDate)
                        .thenComparing(ListDayCardsResult.CalendarCardCompositeView.Builder::getStartTime))
                .toList();
    }

    private String buildCardId(ListDayCardsResult.CalendarCardCompositeView.Builder card) {
        return String.format("%s%s%s", card.getCardType(), card.getDate(), card.getId())
                .replace("-", "");
    }

    private AppointmentStatus getCompatibleAppointmentStatus(AppointmentStatus status, Timestamp checkInTime) {
        return switch (status) {
            case READY, CHECKED_IN, FINISHED, CANCELED -> status;
            case UNCONFIRMED, CONFIRMED -> !Objects.equals(checkInTime, Timestamp.getDefaultInstance())
                    ? AppointmentStatus.CHECKED_IN
                    : status;
            default -> throw ExceptionUtil.bizException(Code.CODE_APPOINTMENT_INVALID_STATUS, "invalid status");
        };
    }

    private ListDayCardsResult.CalendarCardCompositeView.Builder buildServiceCardBuilder(
            AppointmentModel appointment, PetDetailModel petDetail) {
        var builder = ListDayCardsResult.CalendarCardCompositeView.newBuilder()
                .setId(petDetail.getId())
                .setCardType(CalendarCardType.SERVICE)
                .setDate(petDetail.getStartDate())
                .setStartTime(petDetail.getStartTime())
                .setEndDate(petDetail.getEndDate())
                .setEndTime(petDetail.getEndTime())
                .setDraggableInfo(ListDayCardsResult.CalendarCardDraggableInfo.newBuilder()
                        .setDraggable(true)
                        .setEarliestDate(appointment.getAppointmentDate())
                        .setLatestDate(appointment.getAppointmentEndDate())
                        .addAllUnavailableStaffList(List.of())
                        .build())
                .addPetList(addServiceOrEvaluation(
                        ListDayCardsResult.CalendarCardPetInfo.newBuilder().setPetId((int) petDetail.getPetId()),
                        petDetail));
        if (!Objects.equals(petDetail.getStaffId(), 0L)) {
            builder.setStaffId(petDetail.getStaffId());
        }
        return builder;
    }

    private ListDayCardsResult.CalendarCardCompositeView.Builder buildOperationCardBuilder(
            AppointmentModel appointment,
            PetDetailModel petDetail,
            ServiceOperationModel operation,
            Set<Long> staffIds) {
        return ListDayCardsResult.CalendarCardCompositeView.newBuilder()
                .setId(operation.getId())
                .setCardType(CalendarCardType.OPERATION)
                .setDate(petDetail.getStartDate())
                .setStartTime(operation.getStartTime())
                .setEndDate(petDetail.getEndDate())
                .setEndTime(operation.getStartTime() + operation.getDuration())
                .setStaffId(operation.getStaffId())
                .setDraggableInfo(ListDayCardsResult.CalendarCardDraggableInfo.newBuilder()
                        .setDraggable(true)
                        .setEarliestDate(appointment.getAppointmentDate())
                        .setLatestDate(appointment.getAppointmentEndDate())
                        .addAllUnavailableStaffList(staffIds.stream()
                                .filter(staffId -> !staffId.equals(operation.getStaffId()))
                                .toList())
                        .build())
                .addPetList(addServiceOrEvaluation(
                        ListDayCardsResult.CalendarCardPetInfo.newBuilder().setPetId((int) petDetail.getPetId()),
                        petDetail));
    }

    private boolean hasMergeCard(
            ListDayCardsResult.CalendarCardCompositeView.Builder lastCard,
            PetDetailModel petDetail,
            Map<Long, List<ServiceOperationModel>> serviceOperationMap) {
        return !Objects.isNull(lastCard)
                && Objects.equals(lastCard.getCardType(), CalendarCardType.SERVICE)
                && Objects.equals(lastCard.getEndTime(), (long) petDetail.getStartTime())
                && !serviceOperationMap.containsKey(petDetail.getId())
                && Objects.equals(lastCard.getStaffId(), petDetail.getStaffId());
    }

    private ListAppointmentsRequest.Filter buildAppointmentFilter(
            Date startFilter, Date endFilter, boolean filterNoStartTime, String timezone, Set<Long> accessStaffIds) {
        ZonedDateTime startDate = toZonedDateTime(startFilter, timezone);
        ZonedDateTime endDate = toZonedDateTime(endFilter, timezone).plusDays(1);
        // 限制查询 60 天，避免查询过多数据导致索引失效
        return ListAppointmentsRequest.Filter.newBuilder()
                .setStartTimeRange(buildInterval(endDate.minusDays(60).toEpochSecond(), endDate.toEpochSecond() - 1))
                .setEndTimeRange(buildInterval(
                        startDate.toEpochSecond(), startDate.plusDays(60).toEpochSecond() - 1))
                .addAllStatus(AppointmentService.ACTIVE_STATUS_SET)
                .addAllWaitListStatuses(WAIT_LIST_STATUSES_FOR_APPOINTMENT)
                .setFilterNoStartTime(filterNoStartTime)
                .setFilterBookingRequest(true)
                .addAllStaffIds(accessStaffIds)
                .build();
    }

    private ListBlockTimesRequest.Filter buildBlockTimeFilter(Date startFilter, Date endFilter, String timezone) {
        ZonedDateTime startDate = toZonedDateTime(startFilter, timezone);
        ZonedDateTime endDate = toZonedDateTime(endFilter, timezone).plusDays(1);
        return ListBlockTimesRequest.Filter.newBuilder()
                .setStartTimeRange(buildInterval(endDate.minusDays(60).toEpochSecond(), endDate.toEpochSecond() - 1))
                .setEndTimeRange(buildInterval(
                        startDate.toEpochSecond(), startDate.plusDays(60).toEpochSecond() - 1))
                .build();
    }

    private LocalDate toLocalDate(Date date) {
        return LocalDate.of(date.getYear(), date.getMonth(), date.getDay());
    }

    private ZonedDateTime toZonedDateTime(Date date, String timezone) {
        return toLocalDate(date).atStartOfDay().atZone(ZoneId.of(timezone));
    }

    private Interval buildInterval(long startTimestamp, long endTimestamp) {
        return Interval.newBuilder()
                .setStartTime(Timestamp.newBuilder().setSeconds(startTimestamp).build())
                .setEndTime(Timestamp.newBuilder().setSeconds(endTimestamp).build())
                .build();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listMonthCards(ListMonthCardsParams request, StreamObserver<ListMonthCardsResult> responseObserver) {
        businessHelper.checkBusinessCompany(AuthContext.get().companyId(), request.getBusinessId());

        var cards = listCalendarCard(
                request.getBusinessId(), request.getStartDate(), request.getEndDate(), request.getFilterNoStartTime());
        if (CollectionUtils.isEmpty(cards)) {
            responseObserver.onNext(ListMonthCardsResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        fillCalendarMonthCardInfo(cards);

        // filter
        if (request.hasPredicate()) {
            var predicate = buildPredicateByMonth(request.getPredicate());
            cards = cards.stream().filter(predicate).toList();
        }

        // 简化数据
        var simpleCards = toSimpleView(cards);

        responseObserver.onNext(
                ListMonthCardsResult.newBuilder().addAllCards(simpleCards).build());
        responseObserver.onCompleted();
    }

    private List<ListMonthCardsResult.CalendarCardSimpleView> toSimpleView(
            List<ListDayCardsResult.CalendarCardCompositeView.Builder> cards) {
        return cards.stream()
                .map(card -> {
                    var simpleCard = CardConverter.INSTANCE.toSimpleView(card.build()).toBuilder();

                    fillAutoAssignInfo(card, simpleCard);

                    fillPetDetailInfo(card, simpleCard);

                    fillClientInfo(card, simpleCard);

                    fillBlockInfo(card, simpleCard);
                    return simpleCard.build();
                })
                .sorted(Comparator.comparing(ListMonthCardsResult.CalendarCardSimpleView::getAppointmentDate)
                        .thenComparing(ListMonthCardsResult.CalendarCardSimpleView::getStartTime))
                .toList();
    }

    private void fillAutoAssignInfo(
            ListDayCardsResult.CalendarCardCompositeView.Builder card,
            ListMonthCardsResult.CalendarCardSimpleView.Builder simpleCard) {
        if (card.hasAutoAssign()) {
            simpleCard.setAutoAssign(card.getAutoAssign());
        }
    }

    private void fillBlockInfo(
            ListDayCardsResult.CalendarCardCompositeView.Builder card,
            ListMonthCardsResult.CalendarCardSimpleView.Builder simpleCard) {
        simpleCard.setIsBlock(Objects.equals(card.getCardType(), CalendarCardType.BLOCK));

        if (Objects.equals(card.getCardType(), CalendarCardType.BLOCK)) {
            simpleCard.setDesc(card.getTicketComments());
        }
    }

    private void fillClientInfo(
            ListDayCardsResult.CalendarCardCompositeView.Builder card,
            ListMonthCardsResult.CalendarCardSimpleView.Builder simpleCard) {
        if (!card.hasClientInfo()) {
            return;
        }
        var clientInfo = card.getClientInfo();
        simpleCard
                .setCustomerId(clientInfo.getClientId())
                .setCustomerLastName(clientInfo.getCustomerLastName())
                .setCustomerFirstName(clientInfo.getCustomerFirstName())
                .setCustomerColor(clientInfo.getClientColor());
    }

    private void fillPetDetailInfo(
            ListDayCardsResult.CalendarCardCompositeView.Builder card,
            ListMonthCardsResult.CalendarCardSimpleView.Builder simpleCard) {
        if (card.getPetListCount() == 0) {
            return;
        }
        var petInfo = card.getPetList(0);
        if (petInfo.getServiceListCount() != 0) {
            var serviceDetail = petInfo.getServiceList(0);
            simpleCard.setServiceColorCode(serviceDetail.getColorCode()).setPetDetailId(serviceDetail.getPetDetailId());
        } else if (petInfo.getEvaluationsCount() != 0) {
            simpleCard.setServiceColorCode(petInfo.getEvaluations(0).getColorCode());
        }
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void previewCalendarCards(
            PreviewCalendarCardsParams request, StreamObserver<PreviewCalendarCardsResult> responseObserver) {
        businessHelper.checkBusinessCompany(AuthContext.get().companyId(), request.getBusinessId());

        var previewCalendarScheduleRequest = AppointmentConverter.INSTANCE.paramsToRequest(request).toBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .build();
        var response = appointmentScheduleService.previewCalendarSchedule(previewCalendarScheduleRequest);
        var appointment = response.getAppointment();
        var petDetails = response.getPetDetailsList();
        var operations = response.getOperationsList();

        var cardBuilders = buildCalendarCardsFromAppointment(appointment, petDetails, List.of(), operations);

        fillCalendarCardViewInfo(request.getBusinessId(), cardBuilders);

        var result = PreviewCalendarCardsResult.newBuilder()
                .setBusinessId(request.getBusinessId())
                .setCustomerId(request.getCustomerId())
                .setAppointmentId(request.getAppointmentId())
                .setAllPetsStartAtSameTime(request.getAllPetsStartAtSameTime())
                .setAppointmentSchedule(AppointmentScheduleDef.newBuilder()
                        .setStartDate(appointment.getAppointmentDate())
                        .setStartTime(appointment.getAppointmentStartTime())
                        .setEndDate(appointment.getAppointmentEndDate())
                        .setEndTime(appointment.getAppointmentEndTime())
                        .build())
                .addAllPetServiceSchedules(buildPetServiceCalendarScheduleDefs(petDetails, operations))
                .addAllCards(buildCards(cardBuilders))
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    private List<PetServiceCalendarScheduleDef> buildPetServiceCalendarScheduleDefs(
            List<PetDetailModel> petDetails, List<ServiceOperationModel> serviceOperations) {
        var serviceOperationMap =
                serviceOperations.stream().collect(Collectors.groupingBy(ServiceOperationModel::getGroomingServiceId));
        return petDetails.stream().collect(Collectors.groupingBy(PetDetailModel::getPetId)).entrySet().stream()
                .map(entry -> {
                    var petId = entry.getKey();
                    var groomingServiceSchedules = entry.getValue().stream()
                            .map(petDetail -> {
                                var operations = serviceOperationMap.getOrDefault(petDetail.getId(), List.of());
                                var groomingServiceSchedule = PetDetailConverter.INSTANCE.modelToScheduleDef(petDetail);
                                if (operations.isEmpty()) {
                                    return groomingServiceSchedule;
                                }
                                return groomingServiceSchedule.toBuilder()
                                        .setEnableOperation(true)
                                        .setWorkMode(petDetail.getWorkMode())
                                        .addAllOperationSchedules(
                                                buildServiceOperationCalendarDefs(petDetail, operations))
                                        .build();
                            })
                            .toList();
                    return PetServiceCalendarScheduleDef.newBuilder()
                            .setPetId(petId)
                            .addAllGroomingServiceSchedules(groomingServiceSchedules)
                            .build();
                })
                .toList();
    }

    private static List<ServiceOperationCalendarScheduleDef> buildServiceOperationCalendarDefs(
            PetDetailModel petDetail, List<ServiceOperationModel> operations) {
        return operations.stream()
                .map(operation -> ServiceOperationCalendarScheduleDef.newBuilder()
                        .setStaffId(operation.getStaffId())
                        .setDuration(operation.getDuration())
                        .setStartDate(petDetail.getStartDate())
                        .setEndDate(petDetail.getStartDate())
                        .setStartTime(operation.getStartTime())
                        .setEndTime(operation.getStartTime() + operation.getDuration())
                        .build())
                .toList();
    }

    private <T, R> List<R> extractDistinctIds(List<T> list, Function<T, Stream<R>> mapper) {
        return list.stream()
                .flatMap(mapper)
                .filter(id -> !Objects.equals(id, 0L))
                .distinct()
                .toList();
    }

    private void fillCalendarCardViewInfo(long businessId, List<CalendarCardView.Builder> cards) {
        long companyId = AuthContext.get().companyId();
        var queryCards = cards.stream()
                .filter(card -> FILL_INFO_CARD_TYPES.contains(card.getCardType()))
                .toList();
        if (CollectionUtils.isEmpty(queryCards)) {
            return;
        }
        var appointmentIdToCustomerId = queryCards.stream()
                .filter(card -> card.getAppointmentId() != 0L)
                .collect(Collectors.toMap(
                        CalendarCardView.Builder::getAppointmentId,
                        card -> card.getCustomerInfo().getCustomerId(),
                        (a, b) -> a));

        var appointmentIds = extractDistinctIds(queryCards, card -> Stream.of(card.getAppointmentId()));
        var customerIds = extractDistinctIds(
                queryCards, card -> Stream.of(card.getCustomerInfo().getCustomerId()));
        var serviceIds = extractDistinctIds(queryCards, card -> card.getPetsList().stream()
                .flatMap(pet -> pet.getServicesList().stream().map(CalendarCardServiceInfo::getServiceId)));
        var evaluationIds = extractDistinctIds(queryCards, card -> card.getPetsList().stream()
                .flatMap(pet -> pet.getEvaluationsList().stream().map(CalendarCardEvaluationInfo::getServiceId)));
        var petIds = extractDistinctIds(
                queryCards, card -> card.getPetsList().stream().map(CalendarCardPetInfo::getPetId));

        var serviceFuture = futureService.getServiceMapByIds(companyId, serviceIds);
        var evaluationFuture = futureService.getEvaluationMapByIds(evaluationIds);
        var customerFuture = futureService.supply(() -> businessCustomerService.listBusinessCustomerInfos(customerIds));
        var primaryAddressFuture = futureService.supply(() -> customerAddressService.listPrimaryAddresses(customerIds));
        var newCustomerFuture = futureService.getNewCustomerIdList(companyId, customerIds);
        var certainAreaFuture = futureService.supply(
                () -> certainAreaService.listCertainAreas(businessId, new ArrayList<>(primaryAddressFuture.join())));
        var petFuture = futureService.supply(() -> businessPetService.listPets(petIds));
        var petCodeFuture = futureService.supply(() -> petCodeService.listPetCodes(companyId, petIds));
        var petVaccineFuture = futureService.supply(() -> petVaccineService.listPetVaccines(companyId, petIds));
        var petSizeFuture = futureService.supply(() -> petSizeService.listPetSize(
                ListPetSizeRequest.newBuilder().setCompanyId(companyId).build()));
        var invoiceFuture = futureService.getInvoiceCalendarViewMap(appointmentIds);
        var depositFuture = futureService.getBookOnlineDepositMap(appointmentIds);
        var requiredSignFuture = futureService.getRequiredSignAppointmentIdSet(businessId, appointmentIdToCustomerId);
        var preAuthFuture = futureService.getPreAuthMap(businessId, appointmentIds);
        var noteFuture = futureService.getAppointmentNoteMap(companyId, appointmentIds);
        var membershipSubscriptionsFuture =
                futureService.supply(() -> membershipService.getAllSubscriptions(customerIds));
        CompletableFuture.allOf(
                        serviceFuture,
                        evaluationFuture,
                        customerFuture,
                        primaryAddressFuture,
                        newCustomerFuture,
                        certainAreaFuture,
                        petFuture,
                        petCodeFuture,
                        petVaccineFuture,
                        petSizeFuture,
                        invoiceFuture,
                        depositFuture,
                        requiredSignFuture,
                        preAuthFuture,
                        noteFuture,
                        membershipSubscriptionsFuture)
                .join();

        var serviceMap = serviceFuture.join();
        var evaluationMap = evaluationFuture.join();
        var customerMap = customerFuture.join().stream()
                .collect(Collectors.toMap(BusinessCustomerInfoModel::getId, Function.identity(), (a, b) -> a));
        var primaryAddressMap = primaryAddressFuture.join().stream()
                .collect(Collectors.toMap(
                        BusinessCustomerAddressModel::getCustomerId, Function.identity(), (a, b) -> a));
        var newCustomerIds = newCustomerFuture.join();
        var certainAreaMap = certainAreaFuture.join();
        var petMap = petFuture.join().stream()
                .collect(Collectors.toMap(BusinessCustomerPetModel::getId, Function.identity(), (a, b) -> a));
        var petCodeBindingMap = petCodeFuture.join().key().stream()
                .collect(Collectors.toMap(BusinessPetCodeBindingModel::getPetId, Function.identity(), (a, b) -> a));
        var petCodeMap = petCodeFuture.join().value().stream()
                .collect(Collectors.toMap(BusinessPetCodeModel::getId, Function.identity(), (a, b) -> a));
        var petVaccineBindingMap = petVaccineFuture.join().key().stream()
                .collect(Collectors.toMap(
                        BusinessPetVaccineRecordBindingModel::getPetId, Function.identity(), (a, b) -> a));
        var petVaccineMap = petVaccineFuture.join().value().stream()
                .collect(Collectors.toMap(BusinessPetVaccineModel::getId, Function.identity(), (a, b) -> a));
        var petSizeMap = petSizeFuture.join().getSizesList().stream()
                .collect(Collectors.toMap(
                        petSize -> Pair.of(petSize.getWeightLow(), petSize.getWeightHigh()),
                        Function.identity(),
                        (a, b) -> a));
        var invoiceMap = invoiceFuture.join();
        var depositMap = depositFuture.join();
        var requiredSignSet = requiredSignFuture.join();
        var preAuthMap = preAuthFuture.join();
        var noteMap = noteFuture.join();
        var membershipSubscriptionsMap = membershipSubscriptionsFuture.join();

        cards.stream()
                .filter(card -> FILL_INFO_CARD_TYPES.contains(card.getCardType()))
                .forEach(card -> {
                    // Customer info
                    var customerId = card.getCustomerInfo().getCustomerId();
                    var customer = customerMap.get(customerId);
                    if (customer == null) {
                        throw ExceptionUtil.bizException(Code.CODE_CUSTOMER_NOT_FOUND);
                    }
                    var customerInfo = CustomerConverter.INSTANCE.toCalendarCard(customer).toBuilder()
                            .setIsNewClient(newCustomerIds.contains(customerId))
                            .addAllAreas(CertainAreaConverter.INSTANCE.toCalendarCard(
                                    certainAreaMap.getOrDefault(customerId, List.of())));
                    Optional.ofNullable(primaryAddressMap.get(customerId))
                            .ifPresent(primaryAddress -> customerInfo.setPrimaryAddress(
                                    CustomerAddressConverter.INSTANCE.toView(primaryAddress)));
                    // Pet info
                    var pets = card.getPetsList().stream()
                            .map(pet -> {
                                var petInfo = petMap.get(pet.getPetId());
                                if (petInfo == null) {
                                    throw ExceptionUtil.bizException(Code.CODE_PET_NOT_FOUND);
                                }
                                return buildCalendarCardPetInfo(petInfo)
                                        .addAllPetCodes(buildCalendarCardPetCodeInfo(
                                                petCodeBindingMap.get(pet.getPetId()), petCodeMap))
                                        .addAllServices(buildCalendarCardServiceInfo(pet.getServicesList(), serviceMap))
                                        .addAllEvaluations(buildCalendarCardEvaluationInfo(
                                                pet.getEvaluationsList(), evaluationMap))
                                        .addAllVaccines(buildCalendarCardPetVaccineInfo(
                                                petVaccineBindingMap.get(pet.getPetId()), petVaccineMap))
                                        .setPetSizeId(buildCalendarCardPetSizeInfo(petInfo, petSizeMap))
                                        .build();
                            })
                            .toList();
                    // Appointment info
                    var appointmentId = card.getAppointmentId();
                    var requireSign = requiredSignSet.contains(appointmentId);
                    var estimatedTotalPrice = buildEstimatedTotalPrice(pets, serviceMap);
                    Optional.ofNullable(invoiceMap.get(appointmentId))
                            .map(InvoiceCalendarView::getPaidAmount)
                            .ifPresent(card::setPaidAmount);
                    Optional.ofNullable(invoiceMap.get(card.getAppointmentId())).ifPresent(card::setInvoice);
                    Optional.ofNullable(buildTicketComment(noteMap.get(appointmentId)))
                            .ifPresent(card::setTicketComments);
                    Optional.ofNullable(buildAlertNotes(noteMap.get(appointmentId)))
                            .ifPresent(card::setAlertNotes);
                    Optional.ofNullable(buildPrePayCalendarView(appointmentId, depositMap))
                            .ifPresent(card::setPrePayInfo);
                    Optional.ofNullable(buildPreAuthCalendarView(appointmentId, preAuthMap))
                            .ifPresent(card::setPreAuthInfo);
                    Optional.ofNullable(membershipSubscriptionsMap.get(customerId))
                            .ifPresent(card::setMembershipSubscriptions);
                    card.setCustomerInfo(customerInfo.build())
                            .clearPets()
                            .addAllPets(pets)
                            .setRequiredSign(requireSign)
                            .setEstimatedTotalPrice(estimatedTotalPrice);
                });
    }

    private static long buildCalendarCardPetSizeInfo(
            final CustomerPetPetCodeDTO petInfo, final Map<Pair<Integer, Integer>, BusinessPetSizeModel> petSizeMap) {
        if (!StringUtils.hasText(petInfo.getWeight())) {
            return 0L;
        }
        try {
            var weight = Math.round(Double.parseDouble(petInfo.getWeight()));

            return petSizeMap.entrySet().stream()
                    .filter(entry -> {
                        Pair<Integer, Integer> weightRange = entry.getKey();
                        return weight >= weightRange.key() && weight <= weightRange.value();
                    })
                    .findFirst()
                    .map(entry -> entry.getValue().getId())
                    .orElse(0L);

        } catch (NumberFormatException e) {
            return 0L;
        }
    }

    private static long buildCalendarCardPetSizeInfo(
            final BusinessCustomerPetModel petInfo,
            final Map<Pair<Integer, Integer>, BusinessPetSizeModel> petSizeMap) {
        if (!StringUtils.hasText(petInfo.getWeight())) {
            return 0L;
        }
        try {
            var weight = Math.round(Double.parseDouble(petInfo.getWeight()));

            return petSizeMap.entrySet().stream()
                    .filter(entry -> {
                        Pair<Integer, Integer> weightRange = entry.getKey();
                        return weight >= weightRange.key() && weight <= weightRange.value();
                    })
                    .findFirst()
                    .map(entry -> entry.getValue().getId())
                    .orElse(0L);

        } catch (NumberFormatException e) {
            return 0L;
        }
    }

    private static String generateCardId(CalendarCardView.Builder card) {
        return String.join(
                "_",
                card.getCardType().name(),
                String.valueOf(card.getAppointmentId()),
                card.getDate().replace("-", ""),
                String.valueOf(card.getStartTime()),
                String.valueOf(card.getEndTime()),
                String.valueOf(card.getStaffId()),
                card.getPetDetailIdsList().stream().map(String::valueOf).collect(Collectors.joining("_")));
    }

    private static CalendarCardPetInfo.Builder buildCalendarCardPetInfo(BusinessCustomerPetModel petInfo) {
        return CalendarCardPetInfo.newBuilder()
                .setPetId(toIntExact(petInfo.getId()))
                .setPetName(petInfo.getPetName())
                .setBreed(petInfo.getBreed())
                .setPetType(petInfo.getPetType())
                .setEnableVaccineExpiryNotification(petInfo.getExpiryNotification());
    }

    private static List<CalendarCardPetCodeInfo> buildCalendarCardPetCodeInfo(
            BusinessPetCodeBindingModel petCodeBinding, Map<Long, BusinessPetCodeModel> petCodeMap) {
        if (petCodeBinding == null || ObjectUtils.isEmpty(petCodeMap)) {
            return List.of();
        }
        return petCodeBinding.getPetCodeIdsList().stream()
                .map(petCodeId -> {
                    var petCode = petCodeMap.get(petCodeId);
                    if (petCode == null) {
                        log.error("Pet code not found, petCodeId: {}", petCodeId);
                        return null;
                    }
                    return PetCodeConverter.INSTANCE.toCalendarCard(petCode);
                })
                .filter(Objects::nonNull)
                .toList();
    }

    private static List<CalendarCardServiceInfo> buildCalendarCardServiceInfo(
            List<CalendarCardServiceInfo> serviceInfos, Map<Long, ServiceBriefView> serviceMap) {
        return serviceInfos.stream()
                .map(detail -> {
                    var service = serviceMap.get(detail.getServiceId());
                    if (service == null) {
                        return detail;
                    }
                    // Service time & price 取快照，其他信息从 service 中获取
                    return CalendarCardServiceInfo.newBuilder()
                            .setPetDetailId(detail.getPetDetailId())
                            .setServiceId(detail.getServiceId())
                            .setServiceTime(detail.getServiceTime())
                            .setServicePrice(detail.getServicePrice())
                            .setServiceName(service.getName())
                            .setColorCode(service.getColorCode())
                            .build();
                })
                .toList();
    }

    private static List<CalendarCardEvaluationInfo> buildCalendarCardEvaluationInfo(
            List<CalendarCardEvaluationInfo> evaluationInfos, Map<Long, EvaluationBriefView> evaluationMap) {
        return evaluationInfos.stream()
                .map(detail -> {
                    var evaluation = evaluationMap.get(detail.getServiceId());
                    if (evaluation == null) {
                        return detail;
                    }
                    return CalendarCardEvaluationInfo.newBuilder()
                            .setEvaluationDetailId(detail.getEvaluationDetailId())
                            .setServiceId(detail.getServiceId())
                            .setServiceTime(detail.getServiceTime())
                            .setServicePrice(detail.getServicePrice())
                            .setServiceName(evaluation.getName())
                            .setColorCode(evaluation.getColorCode())
                            .build();
                })
                .toList();
    }

    private static List<CalendarCardVaccineInfo> buildCalendarCardPetVaccineInfo(
            BusinessPetVaccineRecordBindingModel petVaccineBinding, Map<Long, BusinessPetVaccineModel> vaccineMap) {
        if (petVaccineBinding == null || ObjectUtils.isEmpty(vaccineMap)) {
            return List.of();
        }
        return petVaccineBinding.getRecordsList().stream()
                .map(vaccineRecord -> {
                    var vaccine = vaccineMap.get(vaccineRecord.getVaccineId());
                    if (vaccine == null) {
                        log.error("Pet vaccine not found, vaccineId: {}", vaccineRecord.getVaccineId());
                        return null;
                    }
                    var builder = CalendarCardVaccineInfo.newBuilder()
                            .setVaccineId(vaccine.getId())
                            .setVaccineName(vaccine.getName());
                    if (vaccineRecord.hasExpirationDate()) {
                        builder.setExpirationDate(DateTimeConverter.INSTANCE
                                .toLocalDate(vaccineRecord.getExpirationDate())
                                .toString());
                    }
                    return builder.build();
                })
                .filter(Objects::nonNull)
                .toList();
    }

    private static double buildEstimatedTotalPrice(
            List<CalendarCardPetInfo> pets, Map<Long, ServiceBriefView> serviceMap) {
        // 单张卡片的总价 = 所有 pet 的所有 service 的价格之和
        double serviceTotalPrice = pets.stream()
                .flatMap(pet -> pet.getServicesList().stream())
                .mapToDouble(detail -> {
                    var service = serviceMap.get(detail.getServiceId());
                    if (service == null) {
                        return detail.getServicePrice();
                    }
                    return switch (service.getPriceUnit()) {
                        case PER_SESSION -> detail.getServicePrice();
                        case PER_HOUR -> detail.getServicePrice() * Math.ceil(detail.getServiceTime() / 60.0);
                        default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unsupported price unit");
                    };
                })
                .sum();
        double evaluationTotalPrice = pets.stream()
                .flatMap(pet -> pet.getEvaluationsList().stream())
                .mapToDouble(CalendarCardEvaluationInfo::getServicePrice)
                .sum();
        return serviceTotalPrice + evaluationTotalPrice;
    }

    private static String buildTicketComment(List<AppointmentNoteModel> notes) {
        if (CollectionUtils.isEmpty(notes)) {
            return null;
        }
        return notes.stream()
                .filter(note -> Objects.equals(note.getType(), AppointmentNoteType.COMMENT))
                .findFirst()
                .map(AppointmentNoteModel::getNote)
                .orElse(null);
    }

    private static String buildAlertNotes(List<AppointmentNoteModel> notes) {
        if (CollectionUtils.isEmpty(notes)) {
            return null;
        }
        return notes.stream()
                .filter(note -> Objects.equals(note.getType(), AppointmentNoteType.ALERT_NOTES))
                .findFirst()
                .map(AppointmentNoteModel::getNote)
                .orElse(null);
    }

    private static PrePayCalendarView buildPrePayCalendarView(
            long appointmentId, Map<Long, BookOnlineDepositDTO> depositMap) {
        var deposit = depositMap.get(appointmentId);
        if (deposit == null || !Objects.equals(deposit.getDepositType(), DepositPaymentTypeEnum.PrePay)) {
            return null;
        }
        return PrePayCalendarView.newBuilder()
                .setTicketId(appointmentId)
                .setPrePayAmount(deposit.getAmount().doubleValue())
                .setPrePayStatus(deposit.getStatus())
                .build();
    }

    private static PreAuthCalendarView buildPreAuthCalendarView(
            long appointmentId, Map<Integer, PreAuthDTO> preAuthMap) {
        var preAuth = preAuthMap.get((int) appointmentId);
        return preAuth != null ? PreAuthConverter.INSTANCE.toView(preAuth) : null;
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listDayCardsWithMixType(
            ListDayCardsWithMixTypeParams request, StreamObserver<ListDayCardsWithMixTypeResult> responseObserver) {
        businessHelper.checkBusinessCompany(AuthContext.get().companyId(), request.getBusinessId());

        var cardBuilders = listCalendarCardsWithMixType(
                request.getBusinessId(), request.getStartDate(), request.getEndDate(), request.getFilterNoStartTime());

        fillCalendarCardViewInfo(request.getBusinessId(), cardBuilders);

        // filter
        if (request.hasPredicate()) {
            var predicate = buildPredicate(request.getPredicate());
            cardBuilders.stream().filter(predicate).forEach(builder -> builder.setHitPredicate(true));
            if (Objects.equals(ViewType.AGENDA, request.getViewType())) {
                cardBuilders = cardBuilders.stream()
                        .filter(CalendarCardView.Builder::getHitPredicate)
                        .toList();
            }
        }

        responseObserver.onNext(ListDayCardsWithMixTypeResult.newBuilder()
                .addAllCards(buildCards(cardBuilders))
                .build());
        responseObserver.onCompleted();
    }

    private static java.util.function.Predicate<ListDayCardsResult.CalendarCardCompositeView.Builder>
            buildPredicateByMonth(Predicate predicate) {
        // field
        java.util.function.Function<ListDayCardsResult.CalendarCardCompositeView.Builder, List<String>> field =
                switch (predicate.getFieldName().toLowerCase()) {
                    case "pet_size" -> builder -> builder.getPetListList().stream()
                            .map(ListDayCardsResult.CalendarCardPetInfo::getPetSizeId)
                            .filter(CommonUtil::isNormal)
                            .map(String::valueOf)
                            .distinct()
                            .toList();
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unsupported filter field");
                };

        // value or values（如果是 in，需要 values），不能同时为空
        var values = new ArrayList<String>();
        switch (predicate.getValueCase()) {
            case NUMBER -> {
                if (predicate.getNumber().hasValue()) {
                    values.add(String.valueOf(predicate.getNumber().getValue()));
                }
            }
            case STRING -> {
                if (predicate.getString().hasValue()) {
                    values.add(predicate.getString().getValue());
                }
            }
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        switch (predicate.getValueCase()) {
            case NUMBER -> values.addAll(predicate.getNumber().getValuesList().stream()
                    .map(Double::toString)
                    .toList());
            case STRING -> values.addAll(predicate.getString().getValuesList());
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        if (CollectionUtils.isEmpty(values)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }

        // build condition
        var operator = predicate.getOperator();
        java.util.function.Predicate<ListDayCardsResult.CalendarCardCompositeView.Builder> condition =
                switch (operator) {
                    case OPERATOR_EQ -> builder -> values.containsAll(field.apply(builder));
                    case OPERATOR_NE -> builder -> !values.containsAll(field.apply(builder));
                    case OPERATOR_IN -> builder -> field.apply(builder).stream().anyMatch(values::contains);
                    case OPERATOR_NOT_IN -> builder ->
                            field.apply(builder).stream().noneMatch(values::contains);
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
                };

        // and / or condition
        if (predicate.hasAnd()) {
            var anotherCondition = buildPredicateByMonth(predicate.getAnd());
            condition = condition.and(anotherCondition);
        } else if (predicate.hasOr()) {
            var anotherCondition = buildPredicateByMonth(predicate.getOr());
            condition = condition.or(anotherCondition);
        }

        return condition;
    }

    private static java.util.function.Predicate<CalendarCardView.Builder> buildPredicate(Predicate predicate) {
        // field
        java.util.function.Function<CalendarCardView.Builder, List<String>> field =
                switch (predicate.getFieldName().toLowerCase()) {
                    case "pet_size" -> builder -> builder.getPetsList().stream()
                            .map(CalendarCardPetInfo::getPetSizeId)
                            .filter(CommonUtil::isNormal)
                            .map(String::valueOf)
                            .distinct()
                            .toList();
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Unsupported filter field");
                };

        // value or values（如果是 in，需要 values），不能同时为空
        var values = new ArrayList<String>();
        switch (predicate.getValueCase()) {
            case NUMBER -> {
                if (predicate.getNumber().hasValue()) {
                    values.add(String.valueOf(predicate.getNumber().getValue()));
                }
            }
            case STRING -> {
                if (predicate.getString().hasValue()) {
                    values.add(predicate.getString().getValue());
                }
            }
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        switch (predicate.getValueCase()) {
            case NUMBER -> values.addAll(predicate.getNumber().getValuesList().stream()
                    .map(Double::toString)
                    .toList());
            case STRING -> values.addAll(predicate.getString().getValuesList());
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        if (CollectionUtils.isEmpty(values)) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }

        // build condition
        var operator = predicate.getOperator();
        java.util.function.Predicate<CalendarCardView.Builder> condition =
                switch (operator) {
                    case OPERATOR_EQ -> builder -> values.containsAll(field.apply(builder));
                    case OPERATOR_NE -> builder -> !values.containsAll(field.apply(builder));
                    case OPERATOR_IN -> builder -> field.apply(builder).stream().anyMatch(values::contains);
                    case OPERATOR_NOT_IN -> builder ->
                            field.apply(builder).stream().noneMatch(values::contains);
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
                };

        // and / or condition
        if (predicate.hasAnd()) {
            var anotherCondition = buildPredicate(predicate.getAnd());
            condition = condition.and(anotherCondition);
        } else if (predicate.hasOr()) {
            var anotherCondition = buildPredicate(predicate.getOr());
            condition = condition.or(anotherCondition);
        }

        return condition;
    }

    private List<CalendarCardView.Builder> listCalendarCardsWithMixType(
            long businessId, Date startDate, Date endDate, boolean filterNoStartTime) {
        var companyId = AuthContext.get().companyId();
        var staffId = AuthContext.get().staffId();
        var filter = new CalendarCardFilterDTO()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setStartDate(startDate)
                .setEndDate(endDate);

        var blockTimesFuture = futureService.supply(() -> blockTimeService.listCalendarBlockTimes(filter));

        var bookingRequestsFuture =
                futureService.supply(() -> bookingRequestService.listCalendarBookingRequests(filter));

        var appointmentsFuture =
                futureService.supply(() -> appointmentService.listCalendarAppointments(filter, filterNoStartTime));

        var appointments = appointmentsFuture.join();

        var petDetailsFuture =
                futureService.supply(() -> petDetailService.listCalendarPetDetails(filter, appointments));

        var operationsFuture = futureService.supply(() -> serviceOperationService.listServiceOperations(
                companyId, appointments.stream().map(AppointmentModel::getId).toList()));

        var accessStaffFuture = futureService.supply(
                () -> new HashSet<>(staffService.getShowOnCalendarStaffIds(companyId, businessId, staffId)));

        return Stream.of(
                        buildCalendarCardsFromBlockTimes(blockTimesFuture.join()),
                        buildCalendarCardsFromBookingRequests(bookingRequestsFuture.join()),
                        buildCalendarCardsFromAppointments(
                                appointments,
                                petDetailsFuture.join().key(),
                                petDetailsFuture.join().value(),
                                operationsFuture.join(),
                                accessStaffFuture.join()))
                .flatMap(List::stream)
                .toList();
    }

    private List<CalendarCardView> buildCards(List<CalendarCardView.Builder> cardBuilders) {
        return cardBuilders.stream()
                // generate card id
                .peek(card -> card.setCardId(generateCardId(card)))
                // sort by staffId, startTime, duration
                .sorted(Comparator.comparing(CalendarCardView.Builder::getStaffId)
                        .thenComparing(CalendarCardView.Builder::getStartTime)
                        .thenComparing(card -> card.getEndTime() - card.getStartTime()))
                .map(CalendarCardView.Builder::build)
                .toList();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listDaySlotInfos(
            final ListDaySlotInfosParams request, final StreamObserver<ListDaySlotInfosResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var businessId = request.getBusinessId();
        businessHelper.checkBusinessCompany(companyId, businessId);

        List<ListDaySlotInfosResult.DaySlotInfo> slotList = List.of();
        List<ListDaySlotInfosResult.SettingDaySlotInfo> settingList = List.of();
        try {
            var staffIdsFuture = CompletableFuture.supplyAsync(
                    () -> staffHelper.listStaffForBusiness(companyId, businessId, true).stream()
                            .map(StaffBasicView::getId)
                            .distinct()
                            .toList(),
                    ThreadPool.getSubmitExecutor());

            // 从timeslot的配置解析出需要查询日期里的所有的timeslot，以date为key，每个staff的timeslot的map为值
            var timeSlotMapByDateFuture = staffIdsFuture.thenApplyAsync(
                    staffIds -> onlineBookingSlotService.getStaffAvailabilityMapByDate(
                            request.getStartDate(), request.getEndDate(), companyId, businessId, staffIds),
                    ThreadPool.getSubmitExecutor());

            var slotFreeServiceFuture = staffIdsFuture.thenApplyAsync(
                    staffIds -> onlineBookingSlotService.getSlotFreeServices(companyId, businessId, staffIds),
                    ThreadPool.getSubmitExecutor());

            var petCountListFuture = staffIdsFuture.thenCombineAsync(
                    slotFreeServiceFuture,
                    (staffIds, slotFreeServices) -> {
                        LocalDate start = LocalDate.parse(request.getStartDate());
                        LocalDate end = LocalDate.parse(request.getEndDate());
                        var needQueryDays = start.datesUntil(end.plusDays(1))
                                .map(LocalDate::toString)
                                .toList();
                        return onlineBookingSlotService.queryStaffTimeslotPetCount(
                                companyId, businessId, needQueryDays, new HashSet<>(staffIds), slotFreeServices);
                    },
                    ThreadPool.getSubmitExecutor());

            var petBreedMapFuture = CompletableFuture.supplyAsync(
                    () -> iPetBreedClient.getPetBreed(toIntExact(businessId)), ThreadPool.getSubmitExecutor());

            var petSizeMapFuture = CompletableFuture.supplyAsync(
                    () -> {
                        var response = businessPetSizeServiceBlockingStub.listPetSize(ListPetSizeRequest.newBuilder()
                                .setCompanyId(companyId)
                                .build());
                        return response.getSizesList().stream()
                                .collect(Collectors.toMap(BusinessPetSizeModel::getId, size -> PetSizeDTO.builder()
                                        .id(size.getId())
                                        .name(size.getName())
                                        .weightLow(size.getWeightLow())
                                        .weightHigh(size.getWeightHigh())
                                        .build()));
                    },
                    ThreadPool.getSubmitExecutor());

            var serviceMapFuture = CompletableFuture.supplyAsync(
                    () -> onlineBookingSlotService.getServiceMap(companyId, Math.toIntExact(businessId)),
                    ThreadPool.getSubmitExecutor());

            var petIdDetailMapFuture = petCountListFuture.thenApplyAsync(
                    petCountList -> {
                        var petIdList = petCountList.stream()
                                .flatMap(group -> group.getPetDetails().stream())
                                .map(SmartScheduleGroomingDetailsDTO::getPetId)
                                .distinct()
                                .toList();
                        return iPetClient.getCustomerPetListByIdList(petIdList).stream()
                                .collect(Collectors.toMap(CustomerPetDetailDTO::getPetId, Function.identity()));
                    },
                    ThreadPool.getSubmitExecutor());

            CompletableFuture.allOf(
                            staffIdsFuture,
                            timeSlotMapByDateFuture,
                            slotFreeServiceFuture,
                            petCountListFuture,
                            petBreedMapFuture,
                            petSizeMapFuture,
                            serviceMapFuture,
                            petIdDetailMapFuture)
                    .join();

            var staffTimeSlotMap = timeSlotMapByDateFuture.join();
            var petCountList = petCountListFuture.join();
            var petBreedMap = petBreedMapFuture.join();
            var petSizeMap = petSizeMapFuture.join();
            var serviceMap = serviceMapFuture.join();
            var petIdDetailMap = petIdDetailMapFuture.join();

            // 填充时间槽偏移量数据
            OnlineBookingSlotService.offsetFillSlotWithMultiPetCal(staffTimeSlotMap, petCountList);

            slotList = OnlineBookingSlotService.getSlotInfoList(
                    staffTimeSlotMap, petCountList, petBreedMap, petSizeMap, serviceMap, petIdDetailMap);
            settingList = OnlineBookingSlotService.getDaySlotInfoList(staffTimeSlotMap);
        } catch (Exception e) {
            log.error("Failed to list day slot infos", e);
        }

        responseObserver.onNext(ListDaySlotInfosResult.newBuilder()
                .addAllSlotInfos(slotList)
                .addAllSettingSlotInfos(settingList)
                .build());
        responseObserver.onCompleted();
    }
}
