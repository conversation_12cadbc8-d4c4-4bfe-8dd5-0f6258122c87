package com.moego.api.v3.appointment.utils;

import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class PermissionUtil {
    private final PermissionHelper permissionHelper;

    public boolean needHideClientPhoneAndEmail(long companyId, long staffId) {
        return !permissionHelper.hasPermission(
                companyId, staffId, PermissionEnums.ACCESS_CLIENT_EMAIL_AND_PHONE_NUMBER);
    }
}
