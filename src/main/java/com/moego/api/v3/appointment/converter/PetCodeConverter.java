package com.moego.api.v3.appointment.converter;

import com.moego.idl.api.appointment.v1.CalendarCardPetCodeInfo;
import com.moego.idl.api.appointment.v1.ListDayCardsResult;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeView;
import com.moego.server.customer.dto.MoePetCodeInfoDTO;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface PetCodeConverter {

    PetCodeConverter INSTANCE = Mappers.getMapper(PetCodeConverter.class);

    List<ListDayCardsResult.CalendarCardPetCodeInfo> toView(List<MoePetCodeInfoDTO> dtos);

    ListDayCardsResult.CalendarCardPetCodeInfo toView(MoePetCodeInfoDTO dto);

    @Mapping(target = "petCodeId", source = "id")
    CalendarCardPetCodeInfo toCalendarCard(BusinessPetCodeModel model);

    BusinessPetCodeView toView(BusinessPetCodeModel model);
}
