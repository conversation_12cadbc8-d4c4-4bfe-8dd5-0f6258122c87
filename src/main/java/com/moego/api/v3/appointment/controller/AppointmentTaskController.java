package com.moego.api.v3.appointment.controller;

import static com.moego.api.v3.shared.util.OperationUtil.extractDistinctIds;
import static java.util.concurrent.CompletableFuture.completedFuture;
import static java.util.concurrent.CompletableFuture.supplyAsync;

import com.google.type.Interval;
import com.moego.api.v3.appointment.converter.AppointmentTaskConverter;
import com.moego.api.v3.appointment.converter.CustomerConverter;
import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.api.v3.appointment.converter.LodgingConverter;
import com.moego.api.v3.appointment.converter.PetCodeConverter;
import com.moego.api.v3.appointment.converter.PetConverter;
import com.moego.api.v3.appointment.converter.StaffConverter;
import com.moego.api.v3.appointment.service.AppointmentTaskService;
import com.moego.api.v3.appointment.service.CompanySettingService;
import com.moego.api.v3.appointment.utils.FutureService;
import com.moego.api.v3.appointment.utils.LodgingUtil;
import com.moego.api.v3.appointment.utils.TaskUtil;
import com.moego.api.v3.business_customer.service.BusinessCustomerService;
import com.moego.api.v3.business_customer.service.BusinessPetCodeService;
import com.moego.api.v3.business_customer.service.BusinessPetService;
import com.moego.api.v3.offering.service.LodgingService;
import com.moego.api.v3.offering.service.ServiceService;
import com.moego.api.v3.organization.service.StaffService;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.api.appointment.v1.AppointmentTaskItem;
import com.moego.idl.api.appointment.v1.AppointmentTaskServiceGrpc;
import com.moego.idl.api.appointment.v1.BatchUpdateAppointmentTaskParams;
import com.moego.idl.api.appointment.v1.BatchUpdateAppointmentTaskResult;
import com.moego.idl.api.appointment.v1.CategoryGroup;
import com.moego.idl.api.appointment.v1.CategoryGroupItem;
import com.moego.idl.api.appointment.v1.CategoryTaskGroup;
import com.moego.idl.api.appointment.v1.GetAppointmentTasksByAppointmentIdParams;
import com.moego.idl.api.appointment.v1.GetAppointmentTasksByAppointmentIdResult;
import com.moego.idl.api.appointment.v1.ListAppointmentTaskCountByCategoryParams;
import com.moego.idl.api.appointment.v1.ListAppointmentTaskCountByCategoryResult;
import com.moego.idl.api.appointment.v1.ListAppointmentTaskFiltersParams;
import com.moego.idl.api.appointment.v1.ListAppointmentTaskFiltersResult;
import com.moego.idl.api.appointment.v1.ListAppointmentTasksByCategoryParams;
import com.moego.idl.api.appointment.v1.ListAppointmentTasksByCategoryResult;
import com.moego.idl.api.appointment.v1.ListAppointmentTasksMultiGroupResult;
import com.moego.idl.api.appointment.v1.ListAppointmentTasksParams;
import com.moego.idl.api.appointment.v1.RemoveTaskNoteParams;
import com.moego.idl.api.appointment.v1.RemoveTaskNoteResult;
import com.moego.idl.api.appointment.v1.RemoveTaskStaffParams;
import com.moego.idl.api.appointment.v1.RemoveTaskStaffResult;
import com.moego.idl.api.appointment.v1.ScheduleFilter;
import com.moego.idl.api.appointment.v1.ScheduleGroup;
import com.moego.idl.api.appointment.v1.ScheduleTaskGroup;
import com.moego.idl.api.appointment.v1.SearchPetWithTaskDateParams;
import com.moego.idl.api.appointment.v1.SearchPetWithTaskDateResult;
import com.moego.idl.api.appointment.v1.TabCount;
import com.moego.idl.api.appointment.v1.TaskGroup;
import com.moego.idl.api.appointment.v1.UpdateAppointmentTaskParams;
import com.moego.idl.api.appointment.v1.UpdateAppointmentTaskResult;
import com.moego.idl.models.appointment.v1.AppointmentTaskCategory;
import com.moego.idl.models.appointment.v1.AppointmentTaskModel;
import com.moego.idl.models.appointment.v1.AppointmentTaskSchedule;
import com.moego.idl.models.appointment.v1.AppointmentTaskStatus;
import com.moego.idl.models.appointment.v1.BoardingSplitLodgingModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.appointment.v1.AppointmentTaskServiceGrpc.AppointmentTaskServiceBlockingStub;
import com.moego.idl.service.appointment.v1.BoardingSplitLodgingServiceGrpc;
import com.moego.idl.service.appointment.v1.CountAppointmentTasksResponse;
import com.moego.idl.service.appointment.v1.GetAppointmentTaskRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentTaskGroupsResponse;
import com.moego.idl.service.appointment.v1.ListAppointmentTasksResponse;
import com.moego.idl.service.appointment.v1.ListBoardingSplitLodgingsRequest;
import com.moego.idl.service.appointment.v1.PatchAppointmentTaskRequest;
import com.moego.idl.service.offering.v1.GetServiceDetailRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.lib.utils.model.Pair;
import io.grpc.stub.StreamObserver;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/12/3
 */
@GrpcService
@RequiredArgsConstructor
public class AppointmentTaskController extends AppointmentTaskServiceGrpc.AppointmentTaskServiceImplBase {

    private final AppointmentTaskServiceBlockingStub appointmentTaskStub;
    private final ServiceManagementServiceBlockingStub serviceStub;

    private final CompanySettingService companySettingService;
    private final BusinessPetService businessPetService;
    private final BusinessPetCodeService petCodeService;
    private final LodgingService lodgingService;
    private final StaffService staffService;
    private final BusinessCustomerService businessCustomerService;
    private final AppointmentTaskService appointmentTaskService;
    private final PermissionHelper permissionHelper;
    private final ServiceService service;
    private final FutureService futureService;
    private final BoardingSplitLodgingServiceGrpc.BoardingSplitLodgingServiceBlockingStub boardingSplitLodgingService;

    private static final int REQUIRE_PAGING_THRESHOLDS_SIZE = 20;

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_TASKS})
    public void listAppointmentTaskFilters(
            ListAppointmentTaskFiltersParams request,
            StreamObserver<ListAppointmentTaskFiltersResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var fullyDayInterval = appointmentTaskService.getDateInterval(companyId, request.getDate());

        // Care types
        var careTypeResult = supplyAsync(
                () -> appointmentTaskService.countTaskByCareTypes(
                        companyId,
                        request.getBusinessId(),
                        fullyDayInterval,
                        List.of(ServiceItemType.BOARDING, ServiceItemType.DAYCARE)),
                ThreadPool.getSubmitExecutor());

        // Statuses
        var statusResult = supplyAsync(
                () -> appointmentTaskService.countTaskByStatus(
                        companyId,
                        request.getBusinessId(),
                        fullyDayInterval,
                        List.of(AppointmentTaskStatus.INCOMPLETE, AppointmentTaskStatus.COMPLETED)),
                ThreadPool.getSubmitExecutor());

        CompletableFuture.allOf(careTypeResult, statusResult).join();

        var careTypeOptions = careTypeResult.join().stream()
                .map(careTypeCount -> ListAppointmentTaskFiltersResult.Option.newBuilder()
                        .setKey(careTypeCount.getCareType().name())
                        .setLabel(StringUtils.capitalize(
                                careTypeCount.getCareType().name().toLowerCase()))
                        .setCount(careTypeCount.getCount())
                        .setCareType(careTypeCount.getCareType())
                        .build())
                .toList();

        var statusOptions = statusResult.join().stream()
                .map(statusCount -> ListAppointmentTaskFiltersResult.Option.newBuilder()
                        .setKey(statusCount.getStatus().name())
                        .setLabel(StringUtils.capitalize(
                                statusCount.getStatus().name().toLowerCase()))
                        .setCount(statusCount.getCount())
                        .setStatus(statusCount.getStatus())
                        .build())
                .toList();

        var result = List.of(
                ListAppointmentTaskFiltersResult.Filter.newBuilder()
                        .setKey("careType")
                        .setLabel("Care Type")
                        .addAllOptions(careTypeOptions)
                        .build(),
                ListAppointmentTaskFiltersResult.Filter.newBuilder()
                        .setKey("status")
                        .setLabel("Status")
                        .addAllOptions(statusOptions)
                        .build());

        responseObserver.onNext(ListAppointmentTaskFiltersResult.newBuilder()
                .addAllFilters(result)
                .build());
        responseObserver.onCompleted();
    }

    private ListAppointmentTasksParams buildAssignedTaskParams(ListAppointmentTasksParams request) {
        boolean accessAllTasks = appointmentTaskService.isAccessAllTasks(
                AuthContext.get().companyId(), AuthContext.get().staffId());
        if (accessAllTasks) {
            return request;
        }
        // Only access assigned task
        return request.toBuilder()
                .setFilter(request.getFilter().toBuilder()
                        .clearStaffIds()
                        .addStaffIds(AuthContext.get().staffId()))
                .build();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_TASKS})
    public void listAppointmentTasksMultiGroup(
            ListAppointmentTasksParams params, StreamObserver<ListAppointmentTasksMultiGroupResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var request = buildAssignedTaskParams(params);
        var timeZoneName = companySettingService.mustGetTimeZoneName(companyId);
        var fullyDayInterval = DateTimeConverter.INSTANCE.buildFullDayInterval(request.getDate(), timeZoneName);

        var taskResult = supplyAsync(
                () -> appointmentTaskService.listAppointmentTasks(companyId, request, fullyDayInterval),
                ThreadPool.getSubmitExecutor());

        var countResult = supplyAsync(
                () -> appointmentTaskService.countAppointmentTasks(companyId, request, fullyDayInterval),
                ThreadPool.getSubmitExecutor());

        var groupResult = supplyAsync(
                () -> appointmentTaskService.listAppointmentTaskGroups(companyId, request, fullyDayInterval),
                ThreadPool.getSubmitExecutor());

        var tabTaskResult = supplyAsync(
                () -> {
                    var tabRequest = request.toBuilder()
                            .setFilter(request.getFilter().toBuilder().clearSelectedGroup())
                            .build();
                    return appointmentTaskService.listAppointmentTasks(companyId, tabRequest, fullyDayInterval);
                },
                ThreadPool.getSubmitExecutor());

        var filterResponse = taskResult.join();
        var tabResponse = tabTaskResult.join();
        var countResponse = countResult.join();
        var groupResponse = groupResult.join();

        var builder = ListAppointmentTasksMultiGroupResult.newBuilder().addAllTabCounts(buildTabCounts(countResponse));

        var needFetchLastGroup = shouldFetchLastGroup(request, filterResponse, tabResponse);
        // 1. 当前 group by 模式下所有 tab 的 count 数量 countResponse
        // 2. 取第一个 tab 的条件作为 filter 筛选统计出所有的 group
        // 3. 查询最后一个 group 下的所有 task
        // 4. 组装当前 tab 下的所有 group 以及 task 返回
        var groups = groupResponse.getGroupsList();
        if (needFetchLastGroup) {
            switch (request.getGroupBy()) {
                case CATEGORY -> handleCategoryGroup(request, builder, fullyDayInterval, groups);
                case SCHEDULE -> handleScheduleGroup(request, builder, fullyDayInterval, groups);
                default -> {} // No-op
            }
        } else {
            // 当前 tab 下数量小于 20 直接返回整个 tab 数据
            var response = tabResponse.getTasksCount() <= REQUIRE_PAGING_THRESHOLDS_SIZE ? tabResponse : filterResponse;
            var mergedGroups =
                    switch (request.getGroupBy()) {
                        case CATEGORY -> mergeCategoryTasks(request, response, groups);
                        case SCHEDULE -> mergeScheduleTasks(request, response, groups);
                        default -> buildTaskGroups(request.getGroupBy(), response.getTasksList());
                    };
            builder.addAllTaskGroups(mergedGroups).setPagination(response.getPagination());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    private static boolean shouldFetchLastGroup(
            ListAppointmentTasksParams request,
            ListAppointmentTasksResponse filterResponse,
            ListAppointmentTasksResponse tabResponse) {
        // 当前 tab 下总数未超过 20 时，直接返回整个 tab 数据
        if (tabResponse.getPagination().getTotal() <= REQUIRE_PAGING_THRESHOLDS_SIZE) {
            return false;
        }
        // 未传 group filter 且总数大于 20 时, 默认取最后一个分组
        return (request.getFilter().getSelectedGroupCase()
                                == ListAppointmentTasksParams.Filter.SelectedGroupCase.SELECTEDGROUP_NOT_SET
                        && filterResponse.getPagination().getTotal() > REQUIRE_PAGING_THRESHOLDS_SIZE)
                // 传递 group filter 且总数为 0 时，取最后一个分组
                || (request.getFilter().getSelectedGroupCase()
                                != ListAppointmentTasksParams.Filter.SelectedGroupCase.SELECTEDGROUP_NOT_SET
                        && filterResponse.getPagination().getTotal() == 0);
    }

    private List<TaskGroup> mergeCategoryTasks(
            ListAppointmentTasksParams request,
            ListAppointmentTasksResponse response,
            List<ListAppointmentTaskGroupsResponse.Group> groups) {
        return switch (TaskUtil.getCategoryTab(request)) {
            case FEEDING, MEDICATION:
                yield buildCategoryTabGroups(request, response, groups);
            case ADD_ONS:
                yield buildAddOnTabGroups(request, response, groups);
            default:
                yield buildTaskGroups(request.getGroupBy(), response.getTasksList());
        };
    }

    private List<TaskGroup> mergeScheduleTasks(
            ListAppointmentTasksParams request,
            ListAppointmentTasksResponse response,
            List<ListAppointmentTaskGroupsResponse.Group> groups) {
        return switch (TaskUtil.getScheduleTab(request)) {
            case AM, PM:
                yield buildScheduleTabGroups(request, response, groups);
            case UNASSIGNED:
                yield buildAddOnTabGroups(request, response, groups);
            default:
                yield buildTaskGroups(request.getGroupBy(), response.getTasksList());
        };
    }

    private void handleCategoryGroup(
            ListAppointmentTasksParams request,
            ListAppointmentTasksMultiGroupResult.Builder builder,
            Interval fullyDayInterval,
            List<ListAppointmentTaskGroupsResponse.Group> countGroups) {
        var pair =
                switch (TaskUtil.getCategoryTab(request)) {
                    case FEEDING, MEDICATION -> handleCategoryGroup(request, fullyDayInterval, countGroups);
                    case ADD_ONS -> handleAddOnGroup(request, fullyDayInterval, countGroups);
                    default -> buildEmptyResponse(request);
                };

        builder.addAllTaskGroups(pair.key()).setPagination(pair.value());
    }

    private Pair<List<TaskGroup>, PaginationResponse> handleCategoryGroup(
            ListAppointmentTasksParams request,
            Interval fullyDayInterval,
            List<ListAppointmentTaskGroupsResponse.Group> countGroups) {
        var maxTimeGroup = getMaxTimeGroup(countGroups);
        if (maxTimeGroup == null) {
            return buildEmptyResponse(request);
        }
        var params = buildParamsWithSchedule(request, maxTimeGroup);
        var response =
                appointmentTaskService.listAppointmentTasks(AuthContext.get().companyId(), params, fullyDayInterval);

        var tasksAndMergedGroups = buildCategoryTabGroups(request, response, countGroups);

        return Pair.of(tasksAndMergedGroups, response.getPagination());
    }

    private List<TaskGroup> buildCategoryTabGroups(
            ListAppointmentTasksParams request,
            ListAppointmentTasksResponse response,
            List<ListAppointmentTaskGroupsResponse.Group> groups) {
        // 1. 取当前所选择的 category tab
        var categoryTab = TaskUtil.getCategoryTab(request);
        // 2. 构造 tasks 及所在的 schedule group
        var taskGroups = buildTaskGroups(request.getGroupBy(), response.getTasksList());
        // 3. 构造当前 category tab 下的所有 schedule group
        var otherGroups = buildScheduleGroups(groups);
        // 4. 合并 tasks 及 group
        return taskGroups.stream()
                .map(taskGroup -> shouldUpdateAddOnTab(taskGroup, categoryTab)
                        ? updateCategoryTabGroups(taskGroup, otherGroups)
                        : taskGroup)
                .toList();
    }

    private List<TaskGroup> buildScheduleTabGroups(
            ListAppointmentTasksParams request,
            ListAppointmentTasksResponse response,
            List<ListAppointmentTaskGroupsResponse.Group> groups) {
        // 1. 取当前所选择的 schedule tab
        var scheduleTab = TaskUtil.getScheduleTab(request);
        // 2. 构造 tasks 及所在的 category group
        var taskGroups = buildTaskGroups(request.getGroupBy(), response.getTasksList());
        // 3. 构造当前 schedule tab 下的所有 category group
        var otherGroups = buildCategoryGroups(groups);
        // 4. 合并 tasks 及 group
        return taskGroups.stream()
                .map(taskGroup -> shouldUpdateAddOnTab(taskGroup, scheduleTab)
                        ? updateScheduleGroup(taskGroup, otherGroups)
                        : taskGroup)
                .toList();
    }

    private List<TaskGroup> buildAddOnTabGroups(
            ListAppointmentTasksParams request,
            ListAppointmentTasksResponse response,
            List<ListAppointmentTaskGroupsResponse.Group> groups) {
        // 1. Add-On 默认为 UNASSIGNED tab
        // 2. 构造 tasks 及所在的 schedule group
        var taskGroups = buildTaskGroups(request.getGroupBy(), response.getTasksList());
        // 3. 构造当前 category tab 下的所有 group
        var otherGroups = buildAddOnGroups(groups);
        // 4. 合并 tasks 及 group
        return taskGroups.stream()
                .map(taskGroup ->
                        shouldUpdateAddOnTab(taskGroup) ? updateScheduleGroup(taskGroup, otherGroups) : taskGroup)
                .toList();
    }

    private static Pair<List<TaskGroup>, PaginationResponse> buildEmptyResponse(ListAppointmentTasksParams request) {
        return Pair.of(
                List.of(),
                PaginationResponse.newBuilder()
                        .setPageNum(request.getPagination().getPageNum())
                        .setPageSize(request.getPagination().getPageSize())
                        .build());
    }

    private static List<ScheduleTaskGroup> buildScheduleGroups(List<ListAppointmentTaskGroupsResponse.Group> groups) {
        return groups.stream()
                .map(ListAppointmentTaskGroupsResponse.Group::getTime)
                .map(timeGroup -> ScheduleTaskGroup.newBuilder()
                        .setSchedule(ScheduleFilter.newBuilder()
                                .setScheduleTime(timeGroup.getStartTime())
                                .setScheduleName(timeGroup.getTimeLabel())
                                .build())
                        .setCount(timeGroup.getCount())
                        .build())
                .toList();
    }

    private static boolean shouldUpdateAddOnTab(TaskGroup taskGroup, AppointmentTaskCategory category) {
        return taskGroup.hasCategoryGroup()
                && Objects.equals(taskGroup.getCategoryGroup().getCategory(), category);
    }

    private static TaskGroup updateCategoryTabGroups(TaskGroup taskGroup, List<ScheduleTaskGroup> timeGroups) {
        var sortedGroups = combineScheduleGroups(taskGroup.getCategoryGroup().getGroupsList(), timeGroups);
        return taskGroup.toBuilder()
                .setCategoryGroup(taskGroup.getCategoryGroup().toBuilder()
                        .clearGroups()
                        .addAllGroups(sortedGroups)
                        .build())
                .build();
    }

    private static List<ScheduleTaskGroup> combineScheduleGroups(
            List<ScheduleTaskGroup> groups1, List<ScheduleTaskGroup> groups2) {
        return Stream.concat(groups1.stream(), groups2.stream())
                .collect(Collectors.toMap(
                        ScheduleTaskGroup::getSchedule,
                        Function.identity(),
                        (existing, replacement) -> existing.toBuilder()
                                .addAllTaskItems(replacement.getTaskItemsList())
                                .setCount(existing.getCount() + replacement.getCount())
                                .build()))
                .values()
                .stream()
                .sorted(Comparator.comparingInt(group -> group.getSchedule().getScheduleTime()))
                .toList();
    }

    private static List<CategoryTaskGroup> buildCategoryGroups(List<ListAppointmentTaskGroupsResponse.Group> groups) {
        return groups.stream()
                .map(ListAppointmentTaskGroupsResponse.Group::getCategory)
                .map(categoryGroup -> CategoryTaskGroup.newBuilder()
                        .setCategoryItem(CategoryGroupItem.newBuilder()
                                .setCategory(categoryGroup.getCategory())
                                .setCategoryName(categoryGroup.getCategory().name())
                                .build())
                        .setCount(categoryGroup.getCount())
                        .build())
                .toList();
    }

    private static boolean shouldUpdateAddOnTab(TaskGroup taskGroup, AppointmentTaskSchedule schedule) {
        return taskGroup.hasScheduleGroup()
                && Objects.equals(taskGroup.getScheduleGroup().getSchedule(), schedule);
    }

    private List<CategoryTaskGroup> buildAddOnGroups(List<ListAppointmentTaskGroupsResponse.Group> groups) {
        var serviceIdToInfo = getServices(groups);
        return groups.stream()
                .map(ListAppointmentTaskGroupsResponse.Group::getAddOn)
                .map(serviceGroup -> {
                    var service = serviceIdToInfo.get(serviceGroup.getAddOnId());
                    return CategoryTaskGroup.newBuilder()
                            .setCategoryItem(CategoryGroupItem.newBuilder()
                                    .setCategory(AppointmentTaskCategory.ADD_ONS)
                                    .setCategoryName(service.getName())
                                    .setAddOnId(serviceGroup.getAddOnId())
                                    .build())
                            .setCount(serviceGroup.getCount())
                            .build();
                })
                .toList();
    }

    private Map<Long, ServiceBriefView> getServices(List<ListAppointmentTaskGroupsResponse.Group> groups) {
        var addOnIds = groups.stream()
                .map(ListAppointmentTaskGroupsResponse.Group::getAddOn)
                .map(ListAppointmentTaskGroupsResponse.AddOnGroup::getAddOnId)
                .toList();
        return serviceStub
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .addAllServiceIds(addOnIds)
                        .build())
                .getServicesList()
                .stream()
                .collect(Collectors.toMap(ServiceBriefView::getId, Function.identity()));
    }

    private static boolean shouldUpdateAddOnTab(TaskGroup taskGroup) {
        return taskGroup.hasScheduleGroup()
                && Objects.equals(taskGroup.getScheduleGroup().getSchedule(), AppointmentTaskSchedule.UNASSIGNED);
    }

    private static TaskGroup updateScheduleGroup(TaskGroup taskGroup, List<CategoryTaskGroup> categoryGroups) {
        var sortedGroups = combineAddOnGroups(taskGroup.getScheduleGroup().getGroupsList(), categoryGroups);
        return taskGroup.toBuilder()
                .setScheduleGroup(taskGroup.getScheduleGroup().toBuilder()
                        .clearGroups()
                        .addAllGroups(sortedGroups)
                        .build())
                .build();
    }

    private static List<CategoryTaskGroup> combineAddOnGroups(
            List<CategoryTaskGroup> groups1, List<CategoryTaskGroup> groups2) {
        return Stream.concat(groups1.stream(), groups2.stream())
                .collect(Collectors.toMap(
                        CategoryTaskGroup::getCategoryItem,
                        Function.identity(),
                        (existing, replacement) -> existing.toBuilder()
                                .addAllTaskItems(replacement.getTaskItemsList())
                                .setCount(existing.getCount() + replacement.getCount())
                                .build()))
                .values()
                .stream()
                .sorted(Comparator.comparingLong(
                        group -> group.getCategoryItem().getAddOnId()))
                .toList();
    }

    private void handleScheduleGroup(
            ListAppointmentTasksParams request,
            ListAppointmentTasksMultiGroupResult.Builder builder,
            Interval fullyDayInterval,
            List<ListAppointmentTaskGroupsResponse.Group> countGroups) {
        var pair =
                switch (TaskUtil.getScheduleTab(request)) {
                    case AM, PM -> handleScheduleGroup(request, fullyDayInterval, countGroups);
                    case UNASSIGNED -> handleAddOnGroup(request, fullyDayInterval, countGroups);
                    default -> buildEmptyResponse(request);
                };

        builder.addAllTaskGroups(pair.key()).setPagination(pair.value());
    }

    private Pair<List<TaskGroup>, PaginationResponse> handleScheduleGroup(
            ListAppointmentTasksParams request,
            Interval fullyDayInterval,
            List<ListAppointmentTaskGroupsResponse.Group> countGroups) {
        var lastCategoryGroup = getLastCategoryGroup(countGroups);
        if (lastCategoryGroup == null) {
            return buildEmptyResponse(request);
        }
        var params = buildParamsWithCategory(request, lastCategoryGroup.getCategory());
        var response =
                appointmentTaskService.listAppointmentTasks(AuthContext.get().companyId(), params, fullyDayInterval);

        var tasksAndMergedGroups = buildScheduleTabGroups(request, response, countGroups);
        return Pair.of(tasksAndMergedGroups, response.getPagination());
    }

    private Pair<List<TaskGroup>, PaginationResponse> handleAddOnGroup(
            ListAppointmentTasksParams request,
            Interval fullyDayInterval,
            List<ListAppointmentTaskGroupsResponse.Group> countGroups) {
        var lastAddOnGroup = getLastAddOnGroup(countGroups);
        if (lastAddOnGroup == null) {
            return buildEmptyResponse(request);
        }
        var params = buildParamsWithService(request, lastAddOnGroup.getAddOnId());
        var response =
                appointmentTaskService.listAppointmentTasks(AuthContext.get().companyId(), params, fullyDayInterval);

        var tasksAndMergedGroups = buildAddOnTabGroups(request, response, countGroups);
        return Pair.of(tasksAndMergedGroups, response.getPagination());
    }

    private static ListAppointmentTasksParams buildParamsWithCategory(
            ListAppointmentTasksParams request, AppointmentTaskCategory category) {
        return request.toBuilder()
                .setFilter(request.getFilter().toBuilder().clearSelectedGroup().setCategoryGroup(category))
                .build();
    }

    private static ListAppointmentTasksParams buildParamsWithSchedule(
            ListAppointmentTasksParams request, ListAppointmentTaskGroupsResponse.TimeGroup timeGroup) {
        return request.toBuilder()
                .setFilter(request.getFilter().toBuilder()
                        .clearSelectedGroup()
                        .setScheduleGroup(ScheduleFilter.newBuilder()
                                .setScheduleTime(timeGroup.getStartTime())
                                .setScheduleName(timeGroup.getTimeLabel())))
                .build();
    }

    private static ListAppointmentTasksParams buildParamsWithService(
            ListAppointmentTasksParams request, long serviceId) {
        return request.toBuilder()
                .setFilter(request.getFilter().toBuilder().clearSelectedGroup().setAddOnId(serviceId))
                .build();
    }

    private static ListAppointmentTaskGroupsResponse.AddOnGroup getLastAddOnGroup(
            List<ListAppointmentTaskGroupsResponse.Group> groups) {
        return groups.stream()
                .map(ListAppointmentTaskGroupsResponse.Group::getAddOn)
                .max(Comparator.comparing(ListAppointmentTaskGroupsResponse.AddOnGroup::getAddOnId))
                .orElse(null);
    }

    private static ListAppointmentTaskGroupsResponse.TimeGroup getMaxTimeGroup(
            List<ListAppointmentTaskGroupsResponse.Group> groups) {
        return groups.stream()
                .map(ListAppointmentTaskGroupsResponse.Group::getTime)
                .max(Comparator.comparingInt(ListAppointmentTaskGroupsResponse.TimeGroup::getStartTime))
                .orElse(null);
    }

    private static ListAppointmentTaskGroupsResponse.CategoryGroup getLastCategoryGroup(
            List<ListAppointmentTaskGroupsResponse.Group> groups) {
        return groups.stream()
                .map(ListAppointmentTaskGroupsResponse.Group::getCategory)
                .max(Comparator.comparingInt(ListAppointmentTaskGroupsResponse.CategoryGroup::getCategoryValue))
                .orElse(null);
    }

    private static List<TabCount> buildTabCounts(CountAppointmentTasksResponse response) {
        return Stream.concat(
                        response.getCategoryCountsList().stream().map(categoryCount -> TabCount.newBuilder()
                                .setCategory(categoryCount.getCategory())
                                .setCount(categoryCount.getCount())
                                .build()),
                        response.getScheduleCountsList().stream().map(scheduleCount -> TabCount.newBuilder()
                                .setSchedule(scheduleCount.getSchedule())
                                .setCount(scheduleCount.getCount())
                                .build()))
                .toList();
    }

    private List<TaskGroup> buildTaskGroups(
            ListAppointmentTasksParams.GroupBy groupBy, List<AppointmentTaskModel> tasks) {
        if (CollectionUtils.isEmpty(tasks)) {
            return List.of();
        }
        return switch (groupBy) {
            case CATEGORY -> tasks.stream()
                    .collect(Collectors.groupingBy(AppointmentTaskModel::getTaskCategory))
                    .entrySet()
                    .stream()
                    .map(this::buildCategoryTaskGroup)
                    .toList();
            case SCHEDULE -> tasks.stream()
                    .collect(Collectors.groupingBy(TaskUtil::getScheduleCategory))
                    .entrySet()
                    .stream()
                    .map(entry -> TaskGroup.newBuilder()
                            .setScheduleGroup(ScheduleGroup.newBuilder()
                                    .setSchedule(entry.getKey())
                                    .addAllGroups(buildCategoryTaskGroups(entry.getValue())))
                            .build())
                    .toList();
            default -> List.of();
        };
    }

    private TaskGroup buildCategoryTaskGroup(Map.Entry<AppointmentTaskCategory, List<AppointmentTaskModel>> entry) {
        // Add-on 特殊处理成 schedule group
        if (Objects.equals(entry.getKey(), AppointmentTaskCategory.ADD_ONS)) {
            return TaskGroup.newBuilder()
                    .setScheduleGroup(ScheduleGroup.newBuilder()
                            .setSchedule(AppointmentTaskSchedule.UNASSIGNED)
                            .addAllGroups(buildCategoryTaskGroups(entry.getValue())))
                    .build();
        }
        return TaskGroup.newBuilder()
                .setCategoryGroup(CategoryGroup.newBuilder()
                        .setCategory(entry.getKey())
                        .addAllGroups(buildScheduleTaskGroups(entry.getValue())))
                .build();
    }

    private List<ScheduleTaskGroup> buildScheduleTaskGroups(List<AppointmentTaskModel> tasks) {
        return tasks.stream()
                .collect(Collectors.groupingBy(task -> Pair.of(task.getTimeLabel(), task.getStartTime())))
                .entrySet()
                .stream()
                .map(entry -> ScheduleTaskGroup.newBuilder()
                        .setSchedule(ScheduleFilter.newBuilder()
                                .setScheduleName(entry.getKey().key())
                                .setScheduleTime(entry.getKey().value())
                                .build())
                        .addAllTaskItems(buildTaskItems(entry.getValue()))
                        .build())
                .toList();
    }

    private List<CategoryTaskGroup> buildCategoryTaskGroups(List<AppointmentTaskModel> tasks) {
        return tasks.stream()
                .collect(Collectors.groupingBy(task -> {
                    if (task.getTaskCategory() == AppointmentTaskCategory.ADD_ONS) {
                        return Pair.of(AppointmentTaskCategory.ADD_ONS, task.getAddOnId());
                    } else {
                        // Feeding, Medication
                        return Pair.of(task.getTaskCategory(), 0L);
                    }
                }))
                .entrySet()
                .stream()
                .map(entry -> CategoryTaskGroup.newBuilder()
                        .setCategoryItem(buildCategoryGroupItem(entry.getKey()))
                        .addAllTaskItems(buildTaskItems(entry.getValue()))
                        .build())
                .toList();
    }

    private CategoryGroupItem buildCategoryGroupItem(Pair<AppointmentTaskCategory, Long> pair) {
        var builder = CategoryGroupItem.newBuilder()
                .setCategory(pair.key())
                .setCategoryName(pair.key().name());
        var serviceId = pair.value();
        if (CommonUtil.isNormal(serviceId)) {
            var service = serviceStub
                    .getServiceDetail(GetServiceDetailRequest.newBuilder()
                            .setServiceId(serviceId)
                            .build())
                    .getService();
            builder.setAddOnId(serviceId).setCategoryName(service.getName());
        }
        return builder.build();
    }

    private List<AppointmentTaskItem> buildTaskItems(List<AppointmentTaskModel> tasks) {
        var appointmentIds = extractDistinctIds(tasks, AppointmentTaskModel::getAppointmentId);
        var petIds = extractDistinctIds(tasks, AppointmentTaskModel::getPetId);
        var lodgingIds = extractDistinctIds(tasks, AppointmentTaskModel::getLodgingId);
        var staffIds = extractDistinctIds(tasks, AppointmentTaskModel::getStaffId);

        // boarding split lodgings
        var boardingSplitLodgingResult = supplyAsync(
                () -> boardingSplitLodgingService
                        .listBoardingSplitLodgings(ListBoardingSplitLodgingsRequest.newBuilder()
                                .addAllAppointmentIds(appointmentIds)
                                .build())
                        .getBoardingSplitLodgingsList(),
                ThreadPool.getSubmitExecutor());
        var petResult = supplyAsync(() -> businessPetService.listPetsInfo(petIds), ThreadPool.getSubmitExecutor());
        var lodgingResult = boardingSplitLodgingResult.thenApplyAsync(
                result -> {
                    var splitLodgingIds = extractDistinctIds(result, BoardingSplitLodgingModel::getLodgingId);
                    return lodgingService.getLodgingMap(Stream.concat(splitLodgingIds.stream(), lodgingIds.stream())
                            .distinct()
                            .toList());
                },
                ThreadPool.getSubmitExecutor());
        var staffResult = supplyAsync(() -> staffService.getStaffMap(staffIds), ThreadPool.getSubmitExecutor());
        var petCodeResult = supplyAsync(
                () -> petCodeService.getPetCodeMap(AuthContext.get().companyId(), petIds),
                ThreadPool.getSubmitExecutor());

        CompletableFuture.allOf(boardingSplitLodgingResult, petResult, lodgingResult, staffResult, petCodeResult)
                .join();

        var boardingSplitLodgingModels = boardingSplitLodgingResult.join();
        var petIdToInfoMap = petResult.join();
        var lodgingInfoMap = lodgingResult.join();
        var lodgingUnitMap = lodgingInfoMap.getKey();
        var lodgingTypeMap = lodgingInfoMap.getValue();
        var staffIdToInfoMap = staffResult.join();
        var petIdToCodesMap = petCodeResult.join();

        return tasks.stream()
                .map(task -> {
                    var builder =
                            AppointmentTaskItem.newBuilder().setTask(AppointmentTaskConverter.INSTANCE.toView(task));
                    Optional.ofNullable(PetConverter.INSTANCE.toView(petIdToInfoMap.get(task.getPetId())))
                            .ifPresent(builder::setPet);
                    Optional.ofNullable(LodgingConverter.INSTANCE.toView(lodgingUnitMap.get(task.getLodgingId())))
                            .ifPresent(builder::setLodging);
                    Optional.ofNullable(StaffConverter.INSTANCE.toView(staffIdToInfoMap.get(task.getStaffId())))
                            .ifPresent(builder::setStaff);
                    builder.addAllPetCodeViews(petIdToCodesMap.getOrDefault(task.getPetId(), List.of()).stream()
                            .map(PetCodeConverter.INSTANCE::toView)
                            .toList());

                    if (!CollectionUtils.isEmpty(boardingSplitLodgingModels)) {
                        Integer startTime = task.hasStartTime() ? task.getStartTime() : null;
                        var currentBoardingSplitLodgingModels = LodgingUtil.filterBoardingSplitLodgingsByPetId(
                                boardingSplitLodgingModels,
                                task.getPetId(),
                                DateTimeConverter.INSTANCE.fromGoogleDate(task.getStartDate()),
                                startTime);
                        var splitLodgingDetailDefs = LodgingUtil.getBoardingSplitLodgingDefs(
                                lodgingUnitMap, lodgingTypeMap, currentBoardingSplitLodgingModels);
                        builder.addAllSplitLodgings(splitLodgingDetailDefs);
                    }

                    return builder.build();
                })
                .toList();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_TASKS})
    public void updateAppointmentTask(
            UpdateAppointmentTaskParams request, StreamObserver<UpdateAppointmentTaskResult> responseObserver) {
        checkTenant(request.getTaskId());
        if (request.hasStaffId()) {
            checkAssignStaffToTask();
        }

        var patch = AppointmentTaskConverter.INSTANCE.toRequest(request).toBuilder()
                .setTokenStaffId(AuthContext.get().staffId())
                .build();

        appointmentTaskStub.patchAppointmentTask(patch);

        responseObserver.onNext(UpdateAppointmentTaskResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private BatchUpdateAppointmentTaskParams buildAssignedTaskParams(BatchUpdateAppointmentTaskParams request) {
        boolean accessAllTasks = appointmentTaskService.isAccessAllTasks(
                AuthContext.get().companyId(), AuthContext.get().staffId());
        if (accessAllTasks) {
            return request;
        }
        // Only access assigned task
        return request.toBuilder()
                .setFilter(request.getFilter().toBuilder()
                        .clearStaffIds()
                        .addStaffIds(AuthContext.get().staffId()))
                .build();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_TASKS})
    public void batchUpdateAppointmentTask(
            BatchUpdateAppointmentTaskParams params,
            StreamObserver<BatchUpdateAppointmentTaskResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        if (params.hasStaffId()) {
            checkAssignStaffToTask();
        }
        var request = buildAssignedTaskParams(params);
        var dateInterval = appointmentTaskService.getDateInterval(companyId, request.getDate());
        var batchPatch = AppointmentTaskConverter.INSTANCE.toRequest(request, dateInterval).toBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(companyId))
                .build();

        var response = appointmentTaskStub.batchPatchAppointmentTask(batchPatch);

        responseObserver.onNext(BatchUpdateAppointmentTaskResult.newBuilder()
                .setAffectedRow(response.getAffectedRow())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_TASKS})
    public void removeTaskStaff(RemoveTaskStaffParams request, StreamObserver<RemoveTaskStaffResult> responseObserver) {
        checkTenant(request.getTaskId());
        checkAssignStaffToTask();

        var patchRequest = PatchAppointmentTaskRequest.newBuilder()
                .setTaskId(request.getTaskId())
                .setStaffId(0)
                .build();
        appointmentTaskStub.patchAppointmentTask(patchRequest);

        responseObserver.onNext(RemoveTaskStaffResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private void checkTenant(long taskId) {
        var request = GetAppointmentTaskRequest.newBuilder().setTaskId(taskId).build();
        var task = appointmentTaskStub.getAppointmentTask(request).getTask();
        if (!Objects.equals(task.getCompanyId(), AuthContext.get().companyId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Appointment task not found");
        }
    }

    private void checkAssignStaffToTask() {
        permissionHelper.checkPermission(
                AuthContext.get().companyId(),
                Set.of(AuthContext.get().businessId()),
                AuthContext.get().staffId(),
                PermissionEnums.ASSIGN_STAFF_TO_TASK);
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_TASKS})
    public void removeTaskNote(RemoveTaskNoteParams request, StreamObserver<RemoveTaskNoteResult> responseObserver) {
        checkTenant(request.getTaskId());

        var patchRequest = PatchAppointmentTaskRequest.newBuilder()
                .setTaskId(request.getTaskId())
                .setCustomizedNoteStatus("")
                .setNoteContent("")
                .setNoteFeedback("")
                .build();
        appointmentTaskStub.patchAppointmentTask(patchRequest);

        responseObserver.onNext(RemoveTaskNoteResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_TASKS})
    public void searchPetWithTaskDate(
            SearchPetWithTaskDateParams request, StreamObserver<SearchPetWithTaskDateResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var timeZoneName = companySettingService.mustGetTimeZoneName(companyId);
        var fullyDayInterval = DateTimeConverter.INSTANCE.buildFullDayInterval(request.getDate(), timeZoneName);

        var petsRequest = TaskUtil.buildTaskPetsRequest(companyId, request, fullyDayInterval);
        var petIds = appointmentTaskStub.listAppointmentTaskPets(petsRequest).getPetIdsList();
        if (CollectionUtils.isEmpty(petIds)) {
            responseObserver.onNext(SearchPetWithTaskDateResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        var pets = businessPetService.listPetsInfo(petIds).values();

        var customerIds = pets.stream()
                .map(BusinessCustomerPetInfoModel::getCustomerId)
                .distinct()
                .toList();
        var customerIdToInfo = businessCustomerService.listBusinessCustomerInfos(customerIds).stream()
                .collect(Collectors.toMap(BusinessCustomerInfoModel::getId, Function.identity()));

        // 匹配 pet name 和 customer name
        var allPetsAndClients = pets.stream()
                .filter(pet -> TaskUtil.matchPetOrCustomer(
                        pet, customerIdToInfo.get(pet.getCustomerId()), request.getKeyword()))
                .sorted(Comparator.comparing(BusinessCustomerPetInfoModel::getPetName, String.CASE_INSENSITIVE_ORDER))
                .skip(getOffset(request.getPagination()))
                .limit(request.getPagination().getPageSize())
                .map(pet -> SearchPetWithTaskDateResult.PetAndClientView.newBuilder()
                        .setPet(PetConverter.INSTANCE.toView(pet))
                        .setClient(CustomerConverter.INSTANCE.toNameView(customerIdToInfo.get(pet.getCustomerId())))
                        .build())
                .toList();

        responseObserver.onNext(SearchPetWithTaskDateResult.newBuilder()
                .addAllPets(allPetsAndClients)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_TASKS})
    public void getAppointmentTasksByAppointmentId(
            GetAppointmentTasksByAppointmentIdParams request,
            StreamObserver<GetAppointmentTasksByAppointmentIdResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var businessId = request.getBusinessId();
        var tasksResponse = appointmentTaskService.listAppointmentTasks(companyId, businessId, request);
        var tasks = tasksResponse.getTasksList().stream()
                .map(AppointmentTaskConverter.INSTANCE::toView)
                .toList();

        responseObserver.onNext(GetAppointmentTasksByAppointmentIdResult.newBuilder()
                .addAllTask(tasks)
                .build());
        responseObserver.onCompleted();
    }

    private static int getOffset(PaginationRequest pagination) {
        return (pagination.getPageNum() - 1) * pagination.getPageSize();
    }

    private ListAppointmentTaskCountByCategoryParams buildAssignedTaskParams(
            ListAppointmentTaskCountByCategoryParams request) {
        boolean accessAllTasks = appointmentTaskService.isAccessAllTasks(
                AuthContext.get().companyId(), AuthContext.get().staffId());
        if (accessAllTasks) {
            return request;
        }
        // Only access assigned task
        return request.toBuilder()
                .setFilter(request.getFilter().toBuilder()
                        .clearStaffIds()
                        .addStaffIds(AuthContext.get().staffId()))
                .build();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_TASKS})
    public void listAppointmentTaskCountByCategory(
            ListAppointmentTaskCountByCategoryParams params,
            StreamObserver<ListAppointmentTaskCountByCategoryResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var request = buildAssignedTaskParams(params);
        var timeZoneName = companySettingService.mustGetTimeZoneName(companyId);
        var fullyDayInterval = DateTimeConverter.INSTANCE.buildFullDayInterval(request.getDate(), timeZoneName);

        // Total count
        var totalCategoryCountResult =
                futureService.supply(() -> appointmentTaskService.countAppointmentTasks(companyId, request));
        var totalAddOnCountResult = futureService.supply(
                () -> appointmentTaskService.listAddOnGroups(companyId, request, fullyDayInterval));

        // Completed count
        CompletableFuture<CountAppointmentTasksResponse> completedCategoryCountResult;
        CompletableFuture<ListAppointmentTaskGroupsResponse> completedAddOnCountResult;
        if (isSelectedAllStatuses(request)) { // 等同于全选，需要统计 completed 数量
            var completedRequest = buildCompletedParams(request);
            completedCategoryCountResult = futureService.supply(
                    () -> appointmentTaskService.countAppointmentTasks(companyId, completedRequest));
            completedAddOnCountResult = futureService.supply(
                    () -> appointmentTaskService.listAddOnGroups(companyId, completedRequest, fullyDayInterval));
        } else if (isSelectedCompleted(request)) { // completed 复用当前 filter 结果
            completedCategoryCountResult = totalCategoryCountResult;
            completedAddOnCountResult = totalAddOnCountResult;
        } else { // incomplete 互斥返回 0
            completedCategoryCountResult = completedFuture(CountAppointmentTasksResponse.getDefaultInstance());
            completedAddOnCountResult = completedFuture(ListAppointmentTaskGroupsResponse.getDefaultInstance());
        }

        CompletableFuture.allOf(
                        totalCategoryCountResult,
                        totalAddOnCountResult,
                        completedCategoryCountResult,
                        completedAddOnCountResult)
                .join();

        var allCategories = Stream.concat(
                        buildGroups(totalCategoryCountResult.join(), completedCategoryCountResult.join()).stream(),
                        buildGroups(totalAddOnCountResult.join(), completedAddOnCountResult.join()).stream())
                .filter(item -> item.getCount() > 0)
                .toList();

        responseObserver.onNext(ListAppointmentTaskCountByCategoryResult.newBuilder()
                .setCount(count(allCategories))
                .addAllCategoryItems(allCategories)
                .build());
        responseObserver.onCompleted();
    }

    static boolean isSelectedAllStatuses(ListAppointmentTaskCountByCategoryParams params) {
        return params.getFilter().getStatusesCount() == 0 || params.getFilter().getStatusesCount() == 2;
    }

    static boolean isSelectedCompleted(ListAppointmentTaskCountByCategoryParams params) {
        return params.getFilter().getStatusesList().contains(AppointmentTaskStatus.COMPLETED);
    }

    static ListAppointmentTaskCountByCategoryParams buildCompletedParams(
            ListAppointmentTaskCountByCategoryParams params) {
        return params.toBuilder()
                .setFilter(params.getFilter().toBuilder().clearStatuses().addStatuses(AppointmentTaskStatus.COMPLETED))
                .build();
    }

    static int count(List<ListAppointmentTaskCountByCategoryResult.CategoryGroupCountItem> allCategories) {
        return allCategories.stream()
                .map(ListAppointmentTaskCountByCategoryResult.CategoryGroupCountItem::getCount)
                .reduce(Integer::sum)
                .orElse(0);
    }

    static List<ListAppointmentTaskCountByCategoryResult.CategoryGroupCountItem> buildGroups(
            CountAppointmentTasksResponse response, CountAppointmentTasksResponse completedResponse) {
        var categoryToCompletedCount = completedResponse.getCategoryCountsList().stream()
                .collect(Collectors.toMap(
                        CountAppointmentTasksResponse.CategoryCount::getCategory,
                        CountAppointmentTasksResponse.CategoryCount::getCount,
                        (a, b) -> a));
        return response.getCategoryCountsList().stream()
                .map(categoryCount -> ListAppointmentTaskCountByCategoryResult.CategoryGroupCountItem.newBuilder()
                        .setCategory(categoryCount.getCategory())
                        .setCategoryName(StringUtils.capitalize(
                                categoryCount.getCategory().name().toLowerCase()))
                        .setCount(categoryCount.getCount())
                        .setCompletedCount(categoryToCompletedCount.getOrDefault(categoryCount.getCategory(), 0))
                        .build())
                .toList();
    }

    List<ListAppointmentTaskCountByCategoryResult.CategoryGroupCountItem> buildGroups(
            ListAppointmentTaskGroupsResponse response, ListAppointmentTaskGroupsResponse completedResponse) {
        var addOnIdToCompletedCount = completedResponse.getGroupsList().stream()
                .map(ListAppointmentTaskGroupsResponse.Group::getAddOn)
                .collect(Collectors.toMap(
                        ListAppointmentTaskGroupsResponse.AddOnGroup::getAddOnId,
                        ListAppointmentTaskGroupsResponse.AddOnGroup::getCount,
                        (a, b) -> a));

        var addOnGroups = response.getGroupsList().stream()
                .map(ListAppointmentTaskGroupsResponse.Group::getAddOn)
                .toList();

        var addOnIds = addOnGroups.stream()
                .map(ListAppointmentTaskGroupsResponse.AddOnGroup::getAddOnId)
                .toList();
        var serviceIdToInfo = service.getServiceMap(null, addOnIds);

        return addOnGroups.stream()
                .map(addOnGroup -> ListAppointmentTaskCountByCategoryResult.CategoryGroupCountItem.newBuilder()
                        .setCategory(AppointmentTaskCategory.ADD_ONS)
                        .setCategoryName(getAddOnName(serviceIdToInfo, addOnGroup.getAddOnId()))
                        .setCount(addOnGroup.getCount())
                        .setAddOnId(addOnGroup.getAddOnId())
                        .setCompletedCount(addOnIdToCompletedCount.getOrDefault(addOnGroup.getAddOnId(), 0))
                        .build())
                .toList();
    }

    private static String getAddOnName(Map<Long, ServiceBriefView> serviceIdToInfo, long addOnId) {
        var addOn = serviceIdToInfo.get(addOnId);
        return addOn != null ? addOn.getName() : "";
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_TASKS})
    public void listAppointmentTasksByCategory(
            ListAppointmentTasksByCategoryParams request,
            StreamObserver<ListAppointmentTasksByCategoryResult> responseObserver) {
        boolean accessAllTasks = appointmentTaskService.isAccessAllTasks(
                AuthContext.get().companyId(), AuthContext.get().staffId());
        if (!accessAllTasks) {
            throw ExceptionUtil.bizException(Code.CODE_PERMISSION_NOT_ENOUGH, "Access all tasks denied");
        }

        var response =
                appointmentTaskService.listAppointmentTasks(AuthContext.get().companyId(), request);

        buildResponse(responseObserver, response);
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_TASKS})
    public void listOwnerAppointmentTasks(
            ListAppointmentTasksByCategoryParams request,
            StreamObserver<ListAppointmentTasksByCategoryResult> responseObserver) {

        var response = appointmentTaskService.listStaffAppointmentTasks(
                AuthContext.get().companyId(), AuthContext.get().staffId(), request);

        buildResponse(responseObserver, response);
    }

    private void buildResponse(
            StreamObserver<ListAppointmentTasksByCategoryResult> responseObserver,
            ListAppointmentTasksResponse response) {
        var categoryTaskGroup = buildCategoryTaskGroups(response.getTasksList()).stream()
                .findFirst()
                .orElse(null);

        var builder = ListAppointmentTasksByCategoryResult.newBuilder().setPagination(response.getPagination());
        if (categoryTaskGroup != null) {
            builder.addAllTaskItems(categoryTaskGroup.getTaskItemsList())
                    .setCategory(categoryTaskGroup.getCategoryItem());
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
