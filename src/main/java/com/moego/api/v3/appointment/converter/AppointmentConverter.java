package com.moego.api.v3.appointment.converter;

import com.moego.idl.api.appointment.v1.CreateAppointmentParams;
import com.moego.idl.api.appointment.v1.GetAppointmentLodgingResult;
import com.moego.idl.api.appointment.v1.PreviewCalendarCardsParams;
import com.moego.idl.api.appointment.v1.RescheduleAppointmentParams;
import com.moego.idl.api.appointment.v1.RescheduleBoardingServiceParams;
import com.moego.idl.api.appointment.v1.RescheduleCalendarCardParams;
import com.moego.idl.api.appointment.v1.RescheduleDaycareServiceParams;
import com.moego.idl.api.appointment.v1.RescheduleEvaluationServiceParams;
import com.moego.idl.api.appointment.v1.RescheduleGroomingServiceParams;
import com.moego.idl.api.appointment.v1.ReschedulePetDetailsParams;
import com.moego.idl.api.appointment.v1.UpdateAppointmentParams;
import com.moego.idl.models.appointment.v1.AppointmentCalendarView;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.CreateAppointmentRequest;
import com.moego.idl.service.appointment.v1.PreviewCalendarScheduleRequest;
import com.moego.idl.service.appointment.v1.RescheduleBoardingServiceRequest;
import com.moego.idl.service.appointment.v1.RescheduleCalendarCardRequest;
import com.moego.idl.service.appointment.v1.RescheduleDaycareServiceRequest;
import com.moego.idl.service.appointment.v1.RescheduleEvaluationServiceRequest;
import com.moego.idl.service.appointment.v1.RescheduleGroomingServiceRequest;
import com.moego.idl.service.appointment.v1.ReschedulePetDetailsRequest;
import com.moego.idl.service.appointment.v1.UpdateAppointmentRequest;
import com.moego.lib.common.util.JsonUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/1/24
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AppointmentConverter {

    AppointmentConverter INSTANCE = org.mapstruct.factory.Mappers.getMapper(AppointmentConverter.class);

    CreateAppointmentRequest paramsToRequest(CreateAppointmentParams params);

    UpdateAppointmentRequest paramsToRequest(UpdateAppointmentParams params);

    RescheduleBoardingServiceRequest paramsToRequest(RescheduleBoardingServiceParams params);

    RescheduleEvaluationServiceRequest paramsToRequest(RescheduleEvaluationServiceParams params);

    @Mapping(target = "invoiceId", source = "model.orderId") // 暂时用 orderId 代替 invoiceId
    AppointmentCalendarView modelToCalendarView(AppointmentModel model, Boolean startAtSameTime);

    RescheduleGroomingServiceRequest paramsToRequest(RescheduleGroomingServiceParams params);

    ReschedulePetDetailsRequest paramsToRequest(ReschedulePetDetailsParams params);

    default RescheduleDaycareServiceRequest.DaycareServiceSchedule rescheduleDaycareServiceToRequest(
            RescheduleDaycareServiceParams.RescheduleDaycareServiceSchedule params) {
        RescheduleDaycareServiceRequest.DaycareServiceSchedule.Builder schedule =
                RescheduleDaycareServiceRequest.DaycareServiceSchedule.newBuilder()
                        .setPetId(params.getPetId())
                        .setStartTime(params.getStartTime())
                        .setEndTime(params.getEndTime())
                        .setLodgingId(params.getLodgingId())
                        .addAllSpecificDates(params.getSpecificDatesList());
        if (StringUtils.hasText(params.getStartDate())) {
            schedule.setStartDate(params.getStartDate());
        }
        if (StringUtils.hasText(params.getEndDate())) {
            schedule.setEndDate(params.getEndDate());
        }
        if (params.hasDateType()) {
            schedule.setDateType(params.getDateType());
        } else {
            schedule.setDateType(PetDetailDateType.PET_DETAIL_DATE_DATE_POINT);
        }
        return schedule.build();
    }

    default List<RescheduleDaycareServiceRequest.DaycareServiceSchedule> rescheduleDaycareServiceToRequest(
            List<RescheduleDaycareServiceParams.RescheduleDaycareServiceSchedule> params) {
        if (params == null) {
            return null;
        }

        List<RescheduleDaycareServiceRequest.DaycareServiceSchedule> list = new ArrayList<>(params.size());
        for (RescheduleDaycareServiceParams.RescheduleDaycareServiceSchedule param : params) {
            list.add(rescheduleDaycareServiceToRequest(param));
        }

        return list;
    }

    PreviewCalendarScheduleRequest paramsToRequest(PreviewCalendarCardsParams params);

    RescheduleCalendarCardRequest paramsToRequest(RescheduleCalendarCardParams params);

    RescheduleCalendarCardRequest paramsToRequest(RescheduleAppointmentParams params);

    default GetAppointmentLodgingResult.PetLodgingView toAppointmentLodgingPetLodgingView(
            Long petId,
            List<PetDetailModel> petDetails,
            List<EvaluationServiceModel> petEvaluations,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, EvaluationBriefView> evaluationMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap) {
        var petServiceLodgingViews = petDetails.stream()
                .map(petDetail -> toAppointmentLodgingPetServiceLodgingView(petDetail, serviceMap, lodgingUnitMap))
                .toList();
        var petEvaluationLodgingViews = petEvaluations.stream()
                .map(petEvaluation ->
                        toAppointmentLodgingPetEvaluationLodgingView(petEvaluation, evaluationMap, lodgingUnitMap))
                .toList();
        return GetAppointmentLodgingResult.PetLodgingView.newBuilder()
                .setPetId(petId)
                .addAllServiceLodgings(petServiceLodgingViews)
                .addAllEvaluationLodgings(petEvaluationLodgingViews)
                .build();
    }

    default GetAppointmentLodgingResult.ServiceLodgingView toAppointmentLodgingPetServiceLodgingView(
            PetDetailModel petDetail,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap) {
        var serviceName = serviceMap.containsKey(petDetail.getServiceId())
                ? serviceMap.get(petDetail.getServiceId()).getName()
                : "";
        var lodgingName = lodgingUnitMap.containsKey(petDetail.getLodgingId())
                ? lodgingUnitMap.get(petDetail.getLodgingId()).getName()
                : "";
        List<String> specificDates = new ArrayList<>();
        if (StringUtils.hasText(petDetail.getSpecificDates())) {
            specificDates = JsonUtil.toList(petDetail.getSpecificDates(), String.class);
        }
        return GetAppointmentLodgingResult.ServiceLodgingView.newBuilder()
                .setServiceItemType(petDetail.getServiceItemType())
                .setServiceId(petDetail.getServiceId())
                .setServiceName(serviceName)
                .setPetServiceDetailId(petDetail.getId())
                .setStartDate(petDetail.getStartDate())
                .setEndDate(petDetail.getEndDate())
                .addAllSpecificDates(specificDates)
                .setLodgingUnitId(petDetail.getLodgingId())
                .setLodgingUnitName(lodgingName)
                .build();
    }

    default GetAppointmentLodgingResult.EvaluationLodgingView toAppointmentLodgingPetEvaluationLodgingView(
            EvaluationServiceModel petEvaluation,
            Map<Long, EvaluationBriefView> evaluationMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap) {
        var serviceName = evaluationMap.containsKey(petEvaluation.getServiceId())
                ? evaluationMap.get(petEvaluation.getServiceId()).getName()
                : "";
        var lodgingName = lodgingUnitMap.containsKey(petEvaluation.getLodgingId())
                ? lodgingUnitMap.get(petEvaluation.getLodgingId()).getName()
                : "";
        return GetAppointmentLodgingResult.EvaluationLodgingView.newBuilder()
                .setServiceItemType(ServiceItemType.EVALUATION)
                .setEvaluationId(petEvaluation.getServiceId())
                .setEvaluationName(serviceName)
                .setPetServiceEvaluationId(petEvaluation.getId())
                .setStartDate(petEvaluation.getStartDate())
                .setEndDate(petEvaluation.getEndDate())
                .setLodgingUnitId(petEvaluation.getLodgingId())
                .setLodgingUnitName(lodgingName)
                .build();
    }
}
