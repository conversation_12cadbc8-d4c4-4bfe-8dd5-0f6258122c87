package com.moego.api.v3.appointment.controller;

import static java.util.Comparator.comparingLong;

import com.google.common.collect.Lists;
import com.google.type.Date;
import com.moego.api.v3.appointment.converter.CustomerConverter;
import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.api.v3.appointment.converter.PetConverter;
import com.moego.api.v3.appointment.converter.PreAuthConverter;
import com.moego.api.v3.appointment.dto.ReportCardSendResultDTO;
import com.moego.api.v3.appointment.service.CompanySettingService;
import com.moego.api.v3.appointment.service.PaymentService;
import com.moego.api.v3.appointment.service.PetEvaluationService;
import com.moego.api.v3.appointment.service.ReportService;
import com.moego.api.v3.appointment.utils.FutureService;
import com.moego.api.v3.appointment.utils.LodgingUtil;
import com.moego.api.v3.appointment.utils.OrderUtil;
import com.moego.api.v3.appointment.utils.PackageHelper;
import com.moego.api.v3.appointment.utils.PetDetailUtil;
import com.moego.api.v3.membership.service.MembershipService;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.PermissionUtil;
import com.moego.idl.api.appointment.v1.AddOnCompositeOverview;
import com.moego.idl.api.appointment.v1.AppointmentCountByServiceItemType;
import com.moego.idl.api.appointment.v1.CountOverviewAppointmentParams;
import com.moego.idl.api.appointment.v1.CountOverviewAppointmentResult;
import com.moego.idl.api.appointment.v1.CustomerCompositeOverview;
import com.moego.idl.api.appointment.v1.EvaluationServiceOverview;
import com.moego.idl.api.appointment.v1.GetOverviewListParams;
import com.moego.idl.api.appointment.v1.GetOverviewListResult;
import com.moego.idl.api.appointment.v1.ListOverviewAppointmentParams;
import com.moego.idl.api.appointment.v1.ListOverviewAppointmentResult;
import com.moego.idl.api.appointment.v1.OverviewEntry;
import com.moego.idl.api.appointment.v1.OverviewItem;
import com.moego.idl.api.appointment.v1.OverviewServiceGrpc;
import com.moego.idl.api.appointment.v1.PetCountByServiceItemType;
import com.moego.idl.api.appointment.v1.ReportStatus;
import com.moego.idl.api.appointment.v1.ServiceCompositeOverview;
import com.moego.idl.api.appointment.v1.ServiceDetailOverview;
import com.moego.idl.api.appointment.v1.VaccineComposite;
import com.moego.idl.models.appointment.v1.AppointmentOverview;
import com.moego.idl.models.appointment.v1.BoardingSplitLodgingModel;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.InvoiceDepositModel;
import com.moego.idl.models.appointment.v1.OverviewReportStatus;
import com.moego.idl.models.appointment.v1.OverviewStatus;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v1.ServiceOperationModel;
import com.moego.idl.models.appointment.v1.WaitListCalendarView;
import com.moego.idl.models.appointment.v1.WaitListStatus;
import com.moego.idl.models.business_customer.v1.BusinessCustomerModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModelOverview;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeModel;
import com.moego.idl.models.business_customer.v1.BusinessPetIncidentReportModel;
import com.moego.idl.models.business_customer.v1.BusinessPetNoteModel;
import com.moego.idl.models.business_customer.v1.PetEvaluationModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.membership.v1.MembershipSubscriptionListModel;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.order.v1.InvoiceCalendarView;
import com.moego.idl.models.order.v1.NoShowInvoiceCalendarView;
import com.moego.idl.models.order.v1.OrderModelAppointmentView;
import com.moego.idl.models.organization.v1.StaffAccessByLocationDef;
import com.moego.idl.models.organization.v1.StaffAccessControlDef;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.service.appointment.v1.GetOverviewListRequest;
import com.moego.idl.service.appointment.v1.GetOverviewListResponse;
import com.moego.idl.service.appointment.v1.GetPetDetailListResponse;
import com.moego.idl.service.appointment.v1.ListOverviewAppointmentRequest;
import com.moego.idl.service.appointment.v1.ListOverviewAppointmentResponse;
import com.moego.idl.service.appointment.v1.OverviewStatusEntry;
import com.moego.idl.service.appointment.v1.PreviewEstimateOrderResponse;
import com.moego.idl.service.organization.v1.GetStaffFullDetailResponse;
import com.moego.idl.service.reporting.v2.ListCustomerUnpaidAmountResponse;
import com.moego.idl.utils.v2.OrderBy;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.params.SearchCustomerIdsParam;
import com.moego.server.payment.dto.PreAuthDTO;
import io.grpc.stub.StreamObserver;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@GrpcService
@RequiredArgsConstructor
@Slf4j
public class OverviewController extends OverviewServiceGrpc.OverviewServiceImplBase {

    private final com.moego.idl.service.appointment.v1.OverviewServiceGrpc.OverviewServiceBlockingStub overviewService;
    private final FutureService futureService;
    private final PackageHelper packageHelper;
    private final ICustomerCustomerClient customerClient;
    private final CompanySettingService companySettingService;
    private final MembershipService membershipService;
    private final PaymentService paymentService;
    private final ReportService reportService;

    private static final String START_TIME = "startTime";
    private static final String END_TIME = "endTime";
    private static final String LODGING_SORT = "lodgingSort";
    private static final String PET_NAME = "pet";
    private static final String CLIENT_FIRST_NAME = "clientFirstName";
    private static final String CLIENT_LAST_NAME = "clientLastName";
    private static final String SERVICE_NAME = "serviceName";

    @Override
    @Auth(AuthType.COMPANY)
    public void getOverviewList(GetOverviewListParams request, StreamObserver<GetOverviewListResult> responseObserver) {
        long businessId = request.getBusinessId();
        long companyId = AuthContext.get().companyId();
        long staffId = AuthContext.get().staffId();

        GetOverviewListResponse response = overviewService.getOverviewList(GetOverviewListRequest.newBuilder()
                .setBusinessId(businessId)
                .setCompanyId(companyId)
                .setDate(request.getDate())
                .addAllServiceItemTypes(request.getServiceItemTypesList())
                .setDateType(request.getDateType())
                .build());

        List<OverviewStatusEntry> entryList = response.getEntriesList();
        List<AppointmentOverview> appointmentOverviewsList = entryList.stream()
                .map(OverviewStatusEntry::getAppointmentOverviewsList)
                .flatMap(List::stream)
                .toList();

        if (CollectionUtils.isEmpty(appointmentOverviewsList)) {
            List<OverviewEntry> result = entryList.stream()
                    .map(entry -> OverviewEntry.newBuilder()
                            .setStatus(entry.getStatus())
                            .setCount(0)
                            .addAllItems(List.of())
                            .build())
                    .toList();
            responseObserver.onNext(
                    GetOverviewListResult.newBuilder().addAllEntries(result).build());
            responseObserver.onCompleted();
            return;
        }

        List<Long> appointmentIdList = appointmentOverviewsList.stream()
                .map(AppointmentOverview::getId)
                .toList();

        // pet detail
        var petDetailListFuture = futureService.getPetDetailListWithActualDates(companyId, appointmentIdList);

        // operation
        var serviceOperationMapFuture = futureService.getServiceOperationMap(companyId, appointmentIdList);

        // staff access control
        var staffFullDetailFuture = futureService.getStaffFullDetail(companyId, staffId);
        var showOnCalendarStaffIdsFuture =
                futureService.getStaffAccessShowOnCalendarStaffIds(companyId, businessId, staffId);

        CompletableFuture.allOf(
                        petDetailListFuture,
                        serviceOperationMapFuture,
                        staffFullDetailFuture,
                        showOnCalendarStaffIdsFuture)
                .join();

        // filter by staff access control
        List<OverviewStatusEntry> filteredOverviewList = filterOverviewList(
                businessId,
                entryList,
                petDetailListFuture.join(),
                serviceOperationMapFuture.join(),
                staffFullDetailFuture.join(),
                showOnCalendarStaffIdsFuture.join());

        List<Long> filteredAppointmentIds = filteredOverviewList.stream()
                .map(OverviewStatusEntry::getAppointmentOverviewsList)
                .flatMap(List::stream)
                .map(AppointmentOverview::getId)
                .distinct()
                .toList();

        if (CollectionUtils.isEmpty(filteredAppointmentIds)) {
            List<OverviewEntry> result = entryList.stream()
                    .map(entry -> OverviewEntry.newBuilder()
                            .setStatus(entry.getStatus())
                            .setCount(0)
                            .addAllItems(List.of())
                            .build())
                    .toList();
            responseObserver.onNext(
                    GetOverviewListResult.newBuilder().addAllEntries(result).build());
            responseObserver.onCompleted();
            return;
        }

        List<PetDetailModel> filteredPetDetailsList =
                new ArrayList<>(petDetailListFuture.join().getPetDetailsList());
        List<EvaluationServiceModel> filteredPetEvaluationsList =
                new ArrayList<>(petDetailListFuture.join().getPetEvaluationsList());
        filteredPetDetailsList.removeIf(
                petDetail -> filteredAppointmentIds.stream().noneMatch(id -> id.equals(petDetail.getGroomingId())));
        filteredPetEvaluationsList.removeIf(evaluation ->
                filteredAppointmentIds.stream().noneMatch(id -> id.equals(evaluation.getAppointmentId())));

        Map<Long, PetDetailModel> petDetailMap =
                filteredPetDetailsList.stream().collect(Collectors.toMap(PetDetailModel::getId, Function.identity()));

        // report card
        var reportCardSentResultsFuture =
                futureService.getReportSentResultDTOs(filteredOverviewList, request, companyId, businessId);

        // note
        var noteMapFuture = futureService.getAppointmentNoteMap(companyId, appointmentIdList);

        // customer
        List<Long> customerIdList = appointmentOverviewsList.stream()
                .map(AppointmentOverview::getCustomerId)
                .distinct()
                .toList();
        var customerMapFuture = futureService.getBusinessCustomerMap(companyId, staffId, customerIdList);
        var membershipSubscriptionsFuture =
                futureService.supply(() -> membershipService.getAllSubscriptions(customerIdList));

        // agreement
        Map<Long, Long> appointmentIdToCustomerIdMap = appointmentOverviewsList.stream()
                .collect(Collectors.toMap(AppointmentOverview::getId, AppointmentOverview::getCustomerId, (a, b) -> a));
        var requiredSignAppointmentIdFuture =
                futureService.getRequiredSignAppointmentIdSet(businessId, appointmentIdToCustomerIdMap);

        // new customer
        var newCustomerIdListFuture = futureService.getNewCustomerIdList(companyId, customerIdList);

        // wait list
        List<Long> waitListAppointmentIdList = appointmentOverviewsList.stream()
                .filter(appointment -> appointment.getWaitListStatus().equals(WaitListStatus.WAITLISTONLY)
                        || appointment.getWaitListStatus().equals(WaitListStatus.APPTANDWAITLIST))
                .map(AppointmentOverview::getId)
                .toList();
        var waitListMapFuture = futureService.getWaitListCalendarViewMap(companyId, waitListAppointmentIdList);

        // review booster sent
        var reviewBoosterSentAppointmentIdFuture =
                futureService.getReviewBoosterSentAppointmentIdList(businessId, appointmentIdList);

        // invoice
        var invoiceMapFuture = futureService.getInvoiceCalendarViewMap(appointmentIdList);

        // no show invoice
        var noShowInvoiceMapFuture = futureService.getNoShowInvoiceCalendarViewMap(appointmentIdList);

        // pre auth
        var preAuthMapFuture = futureService.getPreAuthMap(businessId, appointmentIdList);

        // pet
        var petMapFuture = futureService.getPetMap(companyId, petDetailListFuture);

        // service
        var serviceMapFuture = futureService.getServiceMap(companyId, filteredPetDetailsList);

        // lodgings
        var lodgingMapFuture = futureService.getLodgingMap(filteredPetDetailsList, filteredPetEvaluationsList);

        // staff
        var staffMapFuture = futureService.getStaffMap(filteredPetDetailsList, filteredPetEvaluationsList);

        // invoice deposit
        var invoiceDepositMapFuture = futureService.getInvoiceDepositMap(companyId, invoiceMapFuture);

        // evaluation
        var evaluationFuture = futureService.getEvaluationMap(filteredPetEvaluationsList);

        // customer package
        var customerPackageFuture = packageHelper.listCustomerPackages(companyId, businessId, customerIdList);
        var customerPackageDetailFuture = packageHelper.listCustomerPackageDetails(customerPackageFuture);

        // pet code
        var petCodeMapFuture = futureService.getPetCodeMap(petDetailListFuture, companyId);

        // pet code bindings
        var petCodeBindingsMapFuture = futureService.getPetCodeBindingsMap(petDetailListFuture, companyId);

        // pet evaluations
        var petEvaluationsMapFuture = futureService.getPetEvaluationsMap(petDetailListFuture, companyId);

        // pet note
        var petNoteMapFuture = futureService.getPetNoteMap(petDetailListFuture);

        // vaccine
        var petVaccineMapFuture = futureService.getVaccineMap(petDetailListFuture, companyId);

        // pet incident report
        var petIncidentReportMapFuture = futureService.getIncidentReportMap(petDetailListFuture, companyId);

        CompletableFuture.allOf(
                        reportCardSentResultsFuture,
                        noteMapFuture,
                        customerMapFuture,
                        requiredSignAppointmentIdFuture,
                        newCustomerIdListFuture,
                        waitListMapFuture,
                        reviewBoosterSentAppointmentIdFuture,
                        invoiceMapFuture,
                        noShowInvoiceMapFuture,
                        preAuthMapFuture,
                        petMapFuture,
                        serviceMapFuture,
                        lodgingMapFuture,
                        staffMapFuture,
                        invoiceDepositMapFuture,
                        evaluationFuture,
                        membershipSubscriptionsFuture,
                        customerPackageFuture,
                        customerPackageDetailFuture,
                        petCodeBindingsMapFuture,
                        petCodeMapFuture,
                        petNoteMapFuture,
                        petEvaluationsMapFuture,
                        petVaccineMapFuture,
                        petIncidentReportMapFuture)
                .join();

        List<OverviewEntry> result = filteredOverviewList.stream()
                .map(entry -> {
                    List<OverviewItem> items = entry.getAppointmentOverviewsList().stream()
                            .map(appointment -> {
                                List<ServiceDetailOverview> serviceDetailOverviewList = getServiceDetailOverviewList(
                                        appointment,
                                        filteredPetDetailsList,
                                        filteredPetEvaluationsList,
                                        petMapFuture.join(),
                                        serviceMapFuture.join(),
                                        lodgingMapFuture.join().getKey(),
                                        lodgingMapFuture.join().getValue(),
                                        staffMapFuture.join(),
                                        evaluationFuture.join(),
                                        petCodeMapFuture.join(),
                                        petNoteMapFuture.join(),
                                        petEvaluationsMapFuture.join(),
                                        petVaccineMapFuture.join(),
                                        petIncidentReportMapFuture.join(),
                                        petCodeBindingsMapFuture.join(),
                                        Map.of());
                                return OverviewItem.newBuilder()
                                        .setAppointment(appointment)
                                        .addAllServiceDetail(serviceDetailOverviewList)
                                        .addAllNotes(noteMapFuture.join().getOrDefault(appointment.getId(), List.of()))
                                        .setCustomer(CustomerCompositeOverview.newBuilder()
                                                .setCustomerProfile(
                                                        CustomerConverter.INSTANCE.toOverview(customerMapFuture
                                                                .join()
                                                                .getOrDefault(
                                                                        appointment.getCustomerId(),
                                                                        BusinessCustomerModel.newBuilder()
                                                                                .build())))
                                                .setIsNewCustomer(newCustomerIdListFuture
                                                        .join()
                                                        .contains(appointment.getCustomerId()))
                                                .setRequiredSign(requiredSignAppointmentIdFuture
                                                        .join()
                                                        .contains(appointment.getId()))
                                                .setReviewBoosterSent(reviewBoosterSentAppointmentIdFuture
                                                        .join()
                                                        .contains(appointment.getId()))
                                                .addAllCustomerPackages(PackageHelper.getPackageViews(
                                                        customerPackageFuture.join(),
                                                        customerPackageDetailFuture.join(),
                                                        appointment.getCustomerId()))
                                                .build())
                                        .setMembershipSubscriptions(membershipSubscriptionsFuture
                                                .join()
                                                .getOrDefault(
                                                        appointment.getCustomerId(),
                                                        MembershipSubscriptionListModel.getDefaultInstance()))
                                        .setWaitList(waitListMapFuture
                                                .join()
                                                .getOrDefault(
                                                        appointment.getId(), WaitListCalendarView.getDefaultInstance()))
                                        .addAllServiceItemTypes(
                                                ServiceItemEnum.convertBitValueList(appointment.getServiceTypeInclude())
                                                        .stream()
                                                        .map(ServiceItemEnum::getServiceItem)
                                                        .map(ServiceItemType::forNumber)
                                                        .toList())
                                        .setPreAuth(PreAuthConverter.INSTANCE.toView(preAuthMapFuture
                                                .join()
                                                .getOrDefault(Math.toIntExact(appointment.getId()), new PreAuthDTO())))
                                        .setDeposits(invoiceDepositMapFuture
                                                .join()
                                                .getOrDefault(
                                                        appointment.getId(), InvoiceDepositModel.getDefaultInstance()))
                                        .setInvoice(invoiceMapFuture
                                                .join()
                                                .getOrDefault(
                                                        appointment.getId(), InvoiceCalendarView.getDefaultInstance()))
                                        .setNoShowInvoice(noShowInvoiceMapFuture
                                                .join()
                                                .getOrDefault(
                                                        appointment.getId(),
                                                        NoShowInvoiceCalendarView.getDefaultInstance()))
                                        .addAllReportStatuses(serviceDetailOverviewList.stream()
                                                .map(ServiceDetailOverview::getPet)
                                                .map(BusinessCustomerPetModelOverview::getId)
                                                .map(petId -> {
                                                    Optional<ReportCardSendResultDTO> reportResult =
                                                            reportCardSentResultsFuture.join().stream()
                                                                    .filter(log -> Objects.equals(
                                                                                    log.appointmentId(),
                                                                                    appointment.getId())
                                                                            && Objects.equals(log.petId(), petId))
                                                                    .findAny();
                                                    return reportResult
                                                            .map(reportCardSendResultDTO -> ReportStatus.newBuilder()
                                                                    .setPetId(petId)
                                                                    .setStatus(reportCardSendResultDTO.status())
                                                                    .build())
                                                            .orElseGet(() -> ReportStatus.newBuilder()
                                                                    .setPetId(petId)
                                                                    .setStatus(OverviewReportStatus.UNSENT)
                                                                    .build());
                                                })
                                                .toList())
                                        .build();
                            })
                            .filter(appointment -> filterWithKeyword(appointment, request.getKeyword()))
                            .filter(appointment -> filterWithFilter(appointment, entry, request))
                            .filter(appointment -> isMatchingServiceItemTypeInDateRange(
                                    appointment,
                                    new HashSet<>(request.getServiceItemTypesList()),
                                    LocalDate.parse(request.getDate()),
                                    petDetailMap))
                            .sorted(createComparator(entry, request))
                            .toList();
                    return OverviewEntry.newBuilder()
                            .setStatus(entry.getStatus())
                            .setCount(items.size())
                            .addAllItems(items)
                            .addAllAppointmentCountByServiceItemTypes(countAppointmentByServiceItemType(items))
                            .addAllPetCountByServiceItemTypes(countUniquePetsByServiceItemType(items))
                            .build();
                })
                .toList();

        responseObserver.onNext(
                GetOverviewListResult.newBuilder().addAllEntries(result).build());
        responseObserver.onCompleted();
    }

    private static int countUniquePets(List<OverviewItem> overviewItems) {
        return overviewItems.stream()
                .flatMap(item -> item.getServiceDetailList().stream()
                        .map(serviceDetail -> serviceDetail.getPet().getId()))
                .collect(Collectors.toSet()) // 收集到 Set 去重
                .size(); // 返回唯一 petId 的数量
    }

    private List<PetCountByServiceItemType> countUniquePetsByServiceItemType(List<OverviewItem> overviewItems) {
        return overviewItems.stream()
                .flatMap(item -> item.getServiceItemTypesList().stream()
                        .map(serviceItemType -> new AbstractMap.SimpleEntry<>(serviceItemType, item)))
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.flatMapping(
                                entry -> entry.getValue().getServiceDetailList().stream()
                                        .map(serviceDetail ->
                                                serviceDetail.getPet().getId()),
                                Collectors.toSet())))
                .entrySet()
                .stream()
                .map(entry -> PetCountByServiceItemType.newBuilder()
                        .setServiceItemType(entry.getKey())
                        .setCount(entry.getValue().size())
                        .build())
                .toList();
    }

    private List<AppointmentCountByServiceItemType> countAppointmentByServiceItemType(
            List<OverviewItem> overviewItems) {
        return overviewItems.stream()
                .flatMap(item -> item.getServiceItemTypesList().stream())
                .collect(Collectors.groupingBy(Function.identity(), Collectors.counting()))
                .entrySet()
                .stream()
                .map(entry -> AppointmentCountByServiceItemType.newBuilder()
                        .setServiceItemType(entry.getKey())
                        .setCount(entry.getValue())
                        .build())
                .toList();
    }

    private static List<OverviewStatusEntry> filterOverviewList(
            long businessId,
            List<OverviewStatusEntry> entryList,
            GetPetDetailListResponse petDetailList,
            Map<Long, List<ServiceOperationModel>> serviceOperationMap,
            GetStaffFullDetailResponse staffFullDetail,
            List<Long> showOnCalendarStaffIds) {
        return entryList.stream()
                .map(entry -> {
                    List<AppointmentOverview> overviewList = entry.getAppointmentOverviewsList().stream()
                            .filter(appointment -> filterByStaffAccessControl(
                                    businessId,
                                    appointment,
                                    petDetailList.getPetDetailsList(),
                                    petDetailList.getPetEvaluationsList(),
                                    serviceOperationMap.getOrDefault(appointment.getId(), List.of()),
                                    showOnCalendarStaffIds,
                                    staffFullDetail))
                            .toList();
                    return OverviewStatusEntry.newBuilder()
                            .setStatus(entry.getStatus())
                            .setCount(overviewList.size())
                            .addAllAppointmentOverviews(overviewList)
                            .build();
                })
                .toList();
    }

    private static List<AppointmentOverview> filterAppointmentOverviewList(
            long businessId,
            List<AppointmentOverview> appointmentOverviewsList,
            GetPetDetailListResponse petDetailList,
            Map<Long, List<ServiceOperationModel>> serviceOperationMap,
            GetStaffFullDetailResponse staffFullDetail,
            List<Long> showOnCalendarStaffIds) {
        return appointmentOverviewsList.stream()
                .filter(appointment -> filterByStaffAccessControl(
                        businessId,
                        appointment,
                        petDetailList.getPetDetailsList(),
                        petDetailList.getPetEvaluationsList(),
                        serviceOperationMap.getOrDefault(appointment.getId(), List.of()),
                        showOnCalendarStaffIds,
                        staffFullDetail))
                .toList();
    }

    /**
     * 1. owner： <br>
     * 可以查看所有预约 <br>
     * <p>
     * 2. 非 owner：<br>
     * 2.1 boarding&daycare 预约：根据「Access other staff on Calendar」开关判断 <br>
     * 2.2 evaluation 预约：如果有 staff，根据「Show on Calendar」开关判断；如果没有 staff，根据「Access other staff on Calendar」开关判断 <br>
     * 2.3 grooming 预约：根据「Show on Calendar」开关判断 <br>
     */
    private static boolean filterByStaffAccessControl(
            long businessId,
            AppointmentOverview appointment,
            List<PetDetailModel> allPetDetailList,
            List<EvaluationServiceModel> allPetEvaluationsList,
            List<ServiceOperationModel> operationModelList,
            List<Long> showOnCalendarStaffIds,
            GetStaffFullDetailResponse staffFullDetail) {

        // owner
        if (PermissionUtil.hasOwnerPermission(
                (byte) staffFullDetail.getStaffProfile().getEmployeeCategory().getNumber())) {
            return true;
        }

        // non-owner
        ServiceItemEnum mainServiceItemType =
                ServiceItemEnum.getMainServiceItemType(appointment.getServiceTypeInclude());
        return switch (mainServiceItemType) {
            case BOARDING, DAYCARE -> hasAccessToLocationStaff(businessId, staffFullDetail);
            case EVALUATION -> canAccessEvaluationAppointment(
                    businessId, appointment, allPetEvaluationsList, showOnCalendarStaffIds, staffFullDetail);
            case GROOMING, DOG_WALKING -> canAccessGroomingAppointment(
                    appointment, allPetDetailList, operationModelList, showOnCalendarStaffIds);
            default -> false;
        };
    }

    private static boolean canAccessGroomingAppointment(
            AppointmentOverview appointment,
            List<PetDetailModel> allPetDetailList,
            List<ServiceOperationModel> operationModelList,
            List<Long> showOnCalendarStaffIds) {
        List<PetDetailModel> petDetailList = allPetDetailList.stream()
                .filter(petDetailModel -> Objects.equals(petDetailModel.getGroomingId(), appointment.getId()))
                .toList();
        List<Long> appointmentStaffIds = petDetailList.stream()
                .map(petDetailModel -> {
                    List<Long> staffIds = new ArrayList<>();
                    staffIds.add(petDetailModel.getStaffId());
                    if (petDetailModel.getEnableOperation()) {
                        operationModelList.stream()
                                .map(ServiceOperationModel::getStaffId)
                                .forEach(staffIds::add);
                    }
                    return staffIds;
                })
                .flatMap(List::stream)
                .filter(staffId -> staffId > 0)
                .distinct()
                .toList();
        return appointmentStaffIds.stream().anyMatch(showOnCalendarStaffIds::contains);
    }

    private static boolean canAccessEvaluationAppointment(
            long businessId,
            AppointmentOverview appointment,
            List<EvaluationServiceModel> allPetEvaluationsList,
            List<Long> showOnCalendarStaffIds,
            GetStaffFullDetailResponse staffFullDetail) {
        List<Long> appointmentStaffIds = allPetEvaluationsList.stream()
                .filter(evaluation -> Objects.equals(evaluation.getAppointmentId(), appointment.getId()))
                .map(EvaluationServiceModel::getStaffId)
                .filter(staffId -> staffId > 0)
                .toList();
        if (!CollectionUtils.isEmpty(appointmentStaffIds)) {
            return appointmentStaffIds.stream().anyMatch(showOnCalendarStaffIds::contains);
        }

        return hasAccessToLocationStaff(businessId, staffFullDetail);
    }

    private static boolean hasAccessToLocationStaff(long businessId, GetStaffFullDetailResponse staffFullDetail) {
        StaffAccessControlDef accessControl = staffFullDetail.getAccessControl();
        if (Objects.equals(Boolean.TRUE, accessControl.getAccessAllWorkingLocationsStaffs())) {
            return true;
        }

        return accessControl.getAccessList().getStaffIdsByLocationList().stream()
                .filter(obj -> Objects.equals(businessId, obj.getLocationId()))
                .anyMatch(StaffAccessByLocationDef::getAccessLocationAllStaffs);
    }

    private static boolean filterWithKeyword(OverviewItem appointment, String keyword) {
        if (!StringUtils.hasText(keyword)) {
            return true;
        }
        List<String> searchList = appointment.getServiceDetailList().stream()
                .map(ServiceDetailOverview::getPet)
                .map(BusinessCustomerPetModelOverview::getPetName)
                .distinct()
                .collect(Collectors.toList());
        searchList.addAll(List.of(
                appointment.getCustomer().getCustomerProfile().getFirstName(),
                appointment.getCustomer().getCustomerProfile().getLastName(),
                appointment.getCustomer().getCustomerProfile().getPhoneNumber()));
        // 分词匹配 CRM-1639
        var keywords =
                Arrays.stream(keyword.split(" ")).map(String::toLowerCase).collect(Collectors.toSet());
        var search = searchList.stream().map(String::toLowerCase).collect(Collectors.toSet());
        return search.stream().anyMatch(s -> keywords.stream().anyMatch(s::contains));
        //        return searchList.stream().map(String::toLowerCase).anyMatch(s -> s.contains(keyword.toLowerCase()));
    }

    private static boolean filterWithFilter(
            OverviewItem appointment, OverviewStatusEntry entry, GetOverviewListParams request) {
        if (!request.hasFilter()
                || !request.hasSelectedStatus()
                || !Objects.equals(entry.getStatus(), request.getSelectedStatus())) {
            return true;
        }
        GetOverviewListParams.Filter filter = request.getFilter();
        boolean isExpected = true;
        if (filter.hasReportStatus()) {
            isExpected = appointment.getReportStatusesList().stream()
                    .anyMatch(reportStatus -> Objects.equals(reportStatus.getStatus(), filter.getReportStatus()));
        }
        if (!CollectionUtils.isEmpty(filter.getAppointmentStatusesList())) {
            isExpected = isExpected
                    && filter.getAppointmentStatusesList()
                            .contains(appointment.getAppointment().getStatus());
        }
        return isExpected;
    }

    private static boolean filterWithFilter(OverviewItem appointment, ListOverviewAppointmentParams.Filter filter) {
        boolean isExpected = true;
        if (filter.hasReportStatus()) {
            isExpected = appointment.getReportStatusesList().stream()
                    .anyMatch(reportStatus -> Objects.equals(reportStatus.getStatus(), filter.getReportStatus()));
        }
        return isExpected;
    }

    private static boolean isMatchingServiceItemTypeInDateRange(
            OverviewItem appointment,
            Collection<ServiceItemType> serviceItemTypesList,
            LocalDate requestLocalDate,
            Map<Long, PetDetailModel> petDetailMap) {
        if (CollectionUtils.isEmpty(serviceItemTypesList)) {
            return true;
        }
        return appointment.getServiceDetailList().stream().anyMatch(serviceDetail -> {
            boolean serviceMatch = isServiceMatchInDateRange(
                    serviceDetail.getServicesList(), serviceItemTypesList, requestLocalDate, petDetailMap);
            boolean addOnMatch = isAddOnMatchInDateRange(
                    serviceDetail.getAddOnsList(), serviceItemTypesList, requestLocalDate, petDetailMap);
            if (serviceMatch || addOnMatch) {
                return true;
            }
            return isEvaluationMatchInDateRange(
                    serviceDetail.getEvaluationsList(), serviceItemTypesList, requestLocalDate);
        });
    }

    private static boolean isAddOnMatchInDateRange(
            List<AddOnCompositeOverview> itemList,
            Collection<ServiceItemType> serviceItemTypesList,
            LocalDate requestLocalDate,
            Map<Long, PetDetailModel> petDetailMap) {
        return itemList.stream()
                .filter(item -> serviceItemTypesList.contains(item.getServiceItemType()))
                .anyMatch(item -> {
                    PetDetailModel petDetailModel =
                            petDetailMap.getOrDefault(item.getId(), PetDetailModel.getDefaultInstance());
                    return isPetDetailMatchInDateRange(petDetailModel, requestLocalDate);
                });
    }

    static boolean isServiceMatchInDateRange(
            List<ServiceCompositeOverview> itemList,
            Collection<ServiceItemType> serviceItemTypesList,
            LocalDate requestLocalDate,
            Map<Long, PetDetailModel> petDetailMap) {
        return itemList.stream()
                .filter(item -> serviceItemTypesList.contains(item.getServiceItemType()))
                .anyMatch(item -> {
                    PetDetailModel petDetailModel =
                            petDetailMap.getOrDefault(item.getId(), PetDetailModel.getDefaultInstance());
                    return isPetDetailMatchInDateRange(petDetailModel, requestLocalDate);
                });
    }

    static boolean isPetDetailMatchInDateRange(PetDetailModel petDetailModel, LocalDate requestLocalDate) {
        if (StringUtils.hasText(petDetailModel.getStartDate()) && StringUtils.hasText(petDetailModel.getEndDate())) {
            LocalDate startDate = LocalDate.parse(petDetailModel.getStartDate());
            LocalDate endDate = LocalDate.parse(petDetailModel.getEndDate());
            return isInDateBetween(requestLocalDate, startDate, endDate);
        } else if (StringUtils.hasText(petDetailModel.getSpecificDates())) {
            return PetDetailUtil.getSpecificDates(petDetailModel.getSpecificDates()).stream()
                    .map(LocalDate::parse)
                    .anyMatch(date -> date.equals(requestLocalDate));
        }
        return false;
    }

    static boolean isEvaluationMatchInDateRange(
            List<EvaluationServiceOverview> itemList,
            Collection<ServiceItemType> serviceItemTypesList,
            LocalDate requestLocalDate) {
        return itemList.stream()
                .filter(item -> serviceItemTypesList.contains(item.getServiceItemType()))
                .anyMatch(item -> {
                    if (StringUtils.hasText(item.getStartDate()) && StringUtils.hasText(item.getEndDate())) {
                        LocalDate startDate = LocalDate.parse(item.getStartDate());
                        LocalDate endDate = LocalDate.parse(item.getEndDate());
                        return isInDateBetween(requestLocalDate, startDate, endDate);
                    }
                    return false;
                });
    }

    private static boolean isInDateBetween(LocalDate requestLocalDate, LocalDate startDate, LocalDate endDate) {
        return (startDate.equals(requestLocalDate) || startDate.isBefore(requestLocalDate))
                && (endDate.equals(requestLocalDate) || endDate.isAfter(requestLocalDate));
    }

    private static Comparator<OverviewItem> createComparator(OverviewStatusEntry entry, GetOverviewListParams request) {
        List<OrderBy> orderBysList = request.getOrderBysList();
        if (CollectionUtils.isEmpty(orderBysList)
                || !request.hasSelectedStatus()
                || !Objects.equals(entry.getStatus(), request.getSelectedStatus())) {
            return Comparator.comparingInt(a -> 0);
        }

        Comparator<OverviewItem> comparator = null;
        for (OrderBy orderBy : orderBysList) {
            String field = orderBy.getFieldName();
            boolean ascending = orderBy.getAsc();

            Comparator<OverviewItem> fieldComparator =
                    switch (field) {
                        case START_TIME -> Comparator.comparing(
                                item -> item.getAppointment().getAppointmentStartTime());
                        case END_TIME -> Comparator.comparing(
                                item -> item.getAppointment().getAppointmentEndTime());
                        default -> throw ExceptionUtil.bizException(
                                Code.CODE_PARAMS_ERROR, "Unknown sort field: " + field);
                    };

            if (!ascending) {
                fieldComparator = fieldComparator.reversed();
            }

            if (comparator == null) {
                comparator = fieldComparator;
            } else {
                comparator = comparator.thenComparing(fieldComparator);
            }
        }
        return comparator;
    }

    static Comparator<OverviewItem> createComparator(
            List<OrderBy> orderBysList,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {
        if (CollectionUtils.isEmpty(orderBysList)) {
            return Comparator.comparingInt(a -> 0);
        }

        Comparator<OverviewItem> comparator = null;
        for (OrderBy orderBy : orderBysList) {
            String field = orderBy.getFieldName();
            boolean ascending = orderBy.getAsc();

            Comparator<OverviewItem> fieldComparator =
                    switch (field) {
                        case START_TIME -> Comparator.comparing((OverviewItem item) ->
                                        item.getAppointment().getAppointmentDate())
                                .thenComparing(item -> item.getAppointment().getAppointmentStartTime());
                        case END_TIME -> Comparator.comparing((OverviewItem item) ->
                                        item.getAppointment().getAppointmentEndDate())
                                .thenComparing(item -> item.getAppointment().getAppointmentEndTime());
                        case LODGING_SORT -> {
                            Comparator<OverviewItem> lodgingTypeComparator = Comparator.comparing(
                                    item -> findLodgingTypeSort(item, lodgingUnitMap, lodgingTypeMap));

                            Comparator<OverviewItem> lodgingUnitComparator =
                                    Comparator.comparing(item -> findLodgingUnitSort(item, lodgingUnitMap));

                            yield lodgingTypeComparator.thenComparing(lodgingUnitComparator);
                        }
                        case PET_NAME -> Comparator.comparing(item -> item.getServiceDetailList().stream()
                                .map(ServiceDetailOverview::getPet)
                                .map(BusinessCustomerPetModelOverview::getPetName)
                                .findFirst()
                                .map(String::toLowerCase)
                                .orElse(""));
                        case CLIENT_FIRST_NAME -> Comparator.comparing(item -> item.getCustomer()
                                .getCustomerProfile()
                                .getFirstName()
                                .toLowerCase());
                        case CLIENT_LAST_NAME -> Comparator.comparing(item -> item.getCustomer()
                                .getCustomerProfile()
                                .getLastName()
                                .toLowerCase());

                            // 取第一只 pet 的 mainServiceName 参与排序
                        case SERVICE_NAME -> Comparator.comparing(item -> item.getServiceDetailList().stream()
                                .findFirst()
                                .map(ServiceDetailOverview::getServicesList)
                                .map(OverviewController::getMainService)
                                .map(ServiceCompositeOverview::getServiceName)
                                .map(String::toLowerCase)
                                .orElse(""));
                        default -> throw ExceptionUtil.bizException(
                                Code.CODE_PARAMS_ERROR, "Unknown sort field: " + field);
                    };

            if (!ascending) {
                fieldComparator = fieldComparator.reversed();
            }

            if (comparator == null) {
                comparator = fieldComparator;
            } else {
                comparator = comparator.thenComparing(fieldComparator);
            }
        }
        return comparator;
    }

    private static ServiceCompositeOverview getMainService(
            Collection<ServiceCompositeOverview> serviceCompositeOverviews) {
        var mainServiceItemType = ServiceItemEnum.getMainServiceItemType(serviceCompositeOverviews.stream()
                .map(e -> e.getServiceItemType().getNumber())
                .distinct()
                .toList());
        return serviceCompositeOverviews.stream()
                .filter(v -> v.getServiceItemType().getNumber() == mainServiceItemType.getServiceItem())
                .findFirst()
                .orElse(null);
    }

    /**
     * @return 返回 OverviewItem 中查询到的第一个 lodging type sort
     */
    private static int findLodgingTypeSort(
            OverviewItem item, Map<Long, LodgingUnitModel> lodgingUnitMap, Map<Long, LodgingTypeModel> lodgingTypeMap) {
        // 遍历所有 service detail
        for (ServiceDetailOverview detail : item.getServiceDetailList()) {
            // 先遍历常规 service
            for (ServiceCompositeOverview service : detail.getServicesList()) {
                if (service.getLodgingId() > 0) {
                    LodgingUnitModel lodgingUnit = lodgingUnitMap.get(service.getLodgingId());
                    if (Objects.isNull(lodgingUnit)) continue;

                    LodgingTypeModel lodgingType = lodgingTypeMap.get(lodgingUnit.getLodgingTypeId());
                    if (Objects.nonNull(lodgingType)) return lodgingType.getSort();
                }
            }

            // 上面没获取到再检查 evaluation service
            for (EvaluationServiceOverview eval : detail.getEvaluationsList()) {
                if (eval.hasLodgingId() && eval.getLodgingId() > 0) {
                    LodgingUnitModel lodgingUnit = lodgingUnitMap.get(eval.getLodgingId());
                    if (Objects.isNull(lodgingUnit)) continue;

                    LodgingTypeModel lodgingType = lodgingTypeMap.get(lodgingUnit.getLodgingTypeId());
                    if (Objects.nonNull(lodgingType)) return lodgingType.getSort();
                }
            }
        }

        // 确保无lodging信息的排在后面
        return Integer.MAX_VALUE;
    }

    /**
     * @return 返回 OverviewItem 中查询到的第一个 lodging unit sort
     * 此方法应与findLodgingTypeSort保持一致的逻辑，以确保排序的一致性
     */
    private static int findLodgingUnitSort(OverviewItem item, Map<Long, LodgingUnitModel> lodgingUnitMap) {
        // 遍历所有 service detail
        for (ServiceDetailOverview detail : item.getServiceDetailList()) {
            // 先遍历常规 service
            for (ServiceCompositeOverview service : detail.getServicesList()) {
                if (service.getLodgingId() > 0) {
                    LodgingUnitModel lodgingUnit = lodgingUnitMap.get(service.getLodgingId());
                    if (Objects.nonNull(lodgingUnit)) return lodgingUnit.getSort();
                }
            }

            // 上面没获取到再检查 evaluation service
            for (EvaluationServiceOverview eval : detail.getEvaluationsList()) {
                if (eval.hasLodgingId() && eval.getLodgingId() > 0) {
                    LodgingUnitModel lodgingUnit = lodgingUnitMap.get(eval.getLodgingId());
                    if (Objects.nonNull(lodgingUnit)) return lodgingUnit.getSort();
                }
            }
        }

        // 确保无lodging信息的排在后面
        return Integer.MAX_VALUE;
    }

    private List<ServiceDetailOverview> getServiceDetailOverviewList(
            AppointmentOverview appointment,
            List<PetDetailModel> petDetailsList,
            List<EvaluationServiceModel> petEvaluationsList,
            Map<Long, BusinessCustomerPetModel> petMap,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap,
            Map<Long, StaffModel> staffMap,
            Map<Long, EvaluationBriefView> evaluationMap,
            Map<Long, List<BusinessPetCodeModel>> petCodeMap,
            Map<Long, List<BusinessPetNoteModel>> petNoteMap,
            Map<Long, List<PetEvaluationModel>> petEvaluationsMap,
            Map<Long, List<VaccineComposite>> petVaccineMap,
            Map<Long, List<BusinessPetIncidentReportModel>> BusinessPetIncidentReportModel,
            Map<Long, List<ServiceDetailOverview.Binding>> petCodeBindingMap,
            Map<Long, List<BoardingSplitLodgingModel>> boardingSplitLodgingMap) {

        // 兼容历史数据，根据 serviceId 获取 serviceType
        List<PetDetailModel> newPetDetailList = petDetailsList.stream()
                .map(petDetailModel -> {
                    ServiceBriefView serviceBriefView = serviceMap.get(petDetailModel.getServiceId());
                    if (Objects.isNull(serviceBriefView)) {
                        log.error("service not found, serviceId: {}", petDetailModel.getServiceId());
                        return null;
                    }
                    return petDetailModel.toBuilder()
                            .setServiceType(serviceBriefView.getType())
                            .build();
                })
                .filter(Objects::nonNull)
                .toList();

        Map<Long, List<PetDetailModel>> petDetailMap = newPetDetailList.stream()
                .filter(petDetail -> petDetail.getGroomingId() == appointment.getId())
                .collect(Collectors.groupingBy(PetDetailModel::getPetId, Collectors.toList()));
        Map<Long, List<EvaluationServiceModel>> petEvaluationDetailMap = petEvaluationsList.stream()
                .filter(evaluation -> evaluation.getAppointmentId() == appointment.getId())
                .collect(Collectors.groupingBy(EvaluationServiceModel::getPetId, Collectors.toList()));

        // 根据 petId 分组，取每个 pet 最旧的服务，按更新时间和开始时间排序
        Set<Long> petIdList = new HashSet<>(petDetailMap.values().stream()
                .flatMap(List::stream)
                .collect(Collectors.groupingBy(
                        PetDetailModel::getPetId, Collectors.minBy(comparingLong(PetDetailModel::getUpdateTime))))
                .values()
                .stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .sorted(comparingLong(PetDetailModel::getUpdateTime).thenComparingInt(PetDetailModel::getStartTime))
                .map(PetDetailModel::getPetId)
                .toList());
        petIdList.addAll(petEvaluationDetailMap.keySet());

        return petIdList.stream()
                .map(petId -> {
                    List<PetDetailModel> detailModels = petDetailMap.getOrDefault(petId, List.of());
                    List<PetDetailModel> serviceModels = detailModels.stream()
                            .filter(petDetailModel -> petDetailModel.getServiceType() == ServiceType.SERVICE)
                            .toList();
                    List<PetDetailModel> addOnModels = detailModels.stream()
                            .filter(petDetailModel -> petDetailModel.getServiceType() == ServiceType.ADDON)
                            .toList();
                    List<EvaluationServiceModel> evaluationModels =
                            petEvaluationDetailMap.getOrDefault(petId, List.of());

                    return ServiceDetailOverview.newBuilder()
                            .setPet(PetConverter.INSTANCE.toOverview(
                                    petMap.getOrDefault(petId, BusinessCustomerPetModel.getDefaultInstance())))
                            .addAllServices(serviceModels.stream()
                                    .map(petDetailModel -> {
                                        var boardingSplitLodgingModels = LodgingUtil.filterBoardingSplitLodgings(
                                                boardingSplitLodgingMap,
                                                petDetailModel.getGroomingId(),
                                                petDetailModel.getId(),
                                                null,
                                                null);
                                        var splitLodgingDetailDefs = LodgingUtil.getBoardingSplitLodgingDefs(
                                                lodgingUnitMap, lodgingTypeMap, boardingSplitLodgingModels);

                                        var lodgingInfo = CollectionUtils.isEmpty(splitLodgingDetailDefs)
                                                ? LodgingUtil.getLodgingInfo(
                                                        lodgingUnitMap, lodgingTypeMap, petDetailModel.getLodgingId())
                                                : LodgingUtil.getLodgingInfo(
                                                        lodgingUnitMap,
                                                        lodgingTypeMap,
                                                        splitLodgingDetailDefs
                                                                .get(splitLodgingDetailDefs.size() - 1)
                                                                .getLodgingId());

                                        return PetDetailUtil.buildServiceCompositeOverview(
                                                petDetailModel,
                                                lodgingInfo,
                                                staffMap.get(petDetailModel.getStaffId()),
                                                serviceMap.get(petDetailModel.getServiceId()),
                                                splitLodgingDetailDefs);
                                    })
                                    .toList())
                            .addAllEvaluations(evaluationModels.stream()
                                    .map(evaluation -> PetDetailUtil.buildEvaluationServiceOverview(
                                            evaluation, evaluationMap, staffMap, lodgingTypeMap, lodgingUnitMap))
                                    .toList())
                            .addAllAddOns(addOnModels.stream()
                                    .map(petDetailModel -> PetDetailUtil.buildAddOnCompositeOverview(
                                            petDetailModel,
                                            staffMap.get(petDetailModel.getStaffId()),
                                            serviceMap.get(petDetailModel.getServiceId())))
                                    .toList())
                            .addAllCodes(petCodeMap.getOrDefault(petId, List.of()))
                            .addAllBindings(petCodeBindingMap.getOrDefault(petId, List.of()))
                            .addAllNotes(petNoteMap.getOrDefault(petId, List.of()))
                            .addAllPetEvaluations(PetEvaluationService.findPetEvaluationModel(
                                    petId, petEvaluationDetailMap, petEvaluationsMap))
                            .addAllVaccines(petVaccineMap.getOrDefault(petId, List.of()))
                            .addAllIncidentReports(BusinessPetIncidentReportModel.getOrDefault(petId, List.of()))
                            .build();
                })
                .toList();
    }

    private List<ServiceDetailOverview> getSimpleServiceDetailOverviewList(
            Long appointmentId,
            Map<Long, ServiceBriefView> serviceMap,
            List<PetDetailModel> petDetailsList,
            List<EvaluationServiceModel> petEvaluationsList,
            Map<Long, BusinessCustomerPetInfoModel> petMap) {

        // 兼容历史数据，根据 serviceId 获取 serviceType
        List<PetDetailModel> newPetDetailList = petDetailsList.stream()
                .map(petDetailModel -> {
                    ServiceBriefView serviceBriefView = serviceMap.get(petDetailModel.getServiceId());
                    if (Objects.isNull(serviceBriefView)) {
                        log.error("service not found, serviceId: {}", petDetailModel.getServiceId());
                        return null;
                    }
                    return petDetailModel.toBuilder()
                            .setServiceType(serviceBriefView.getType())
                            .build();
                })
                .filter(Objects::nonNull)
                .toList();

        Map<Long, List<PetDetailModel>> petDetailMap = newPetDetailList.stream()
                .filter(petDetail -> petDetail.getGroomingId() == appointmentId)
                .collect(Collectors.groupingBy(PetDetailModel::getPetId, Collectors.toList()));
        Map<Long, List<EvaluationServiceModel>> petEvaluationDetailMap = petEvaluationsList.stream()
                .filter(evaluation -> evaluation.getAppointmentId() == appointmentId)
                .collect(Collectors.groupingBy(EvaluationServiceModel::getPetId, Collectors.toList()));

        List<Long> petIds = Stream.concat(petDetailMap.keySet().stream(), petEvaluationDetailMap.keySet().stream())
                .distinct()
                .toList();

        return petIds.stream()
                .map(petId -> {
                    List<PetDetailModel> detailModels = petDetailMap.getOrDefault(petId, List.of());
                    List<PetDetailModel> serviceModels = detailModels.stream()
                            .filter(petDetailModel -> petDetailModel.getServiceType() == ServiceType.SERVICE)
                            .toList();
                    List<PetDetailModel> addOnModels = detailModels.stream()
                            .filter(petDetailModel -> petDetailModel.getServiceType() == ServiceType.ADDON)
                            .toList();
                    List<EvaluationServiceModel> evaluationModels =
                            petEvaluationDetailMap.getOrDefault(petId, List.of());

                    return ServiceDetailOverview.newBuilder()
                            .setPet(PetConverter.INSTANCE.toOverview(
                                    petMap.getOrDefault(petId, BusinessCustomerPetInfoModel.getDefaultInstance())))
                            .addAllServices(serviceModels.stream()
                                    .map(petDetailModel -> ServiceCompositeOverview.newBuilder()
                                            .setId(petDetailModel.getId())
                                            .setServiceItemType(petDetailModel.getServiceItemType())
                                            .build())
                                    .toList())
                            .addAllAddOns(addOnModels.stream()
                                    .map(petDetailModel -> AddOnCompositeOverview.newBuilder()
                                            .setId(petDetailModel.getId())
                                            .setServiceItemType(petDetailModel.getServiceItemType())
                                            .build())
                                    .toList())
                            .addAllEvaluations(evaluationModels.stream()
                                    .map(evaluation -> EvaluationServiceOverview.newBuilder()
                                            .setId(evaluation.getId())
                                            .setServiceItemType(ServiceItemType.EVALUATION)
                                            .setStartDate(evaluation.getStartDate())
                                            .setEndDate(evaluation.getEndDate())
                                            .build())
                                    .toList())
                            .build();
                })
                .toList();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listOverviewAppointment(
            ListOverviewAppointmentParams request, StreamObserver<ListOverviewAppointmentResult> responseObserver) {
        long businessId = request.getBusinessId();
        long companyId = AuthContext.get().companyId();
        long staffId = AuthContext.get().staffId();
        ListOverviewAppointmentParams.Filter filter = request.getFilter();

        Set<Long> clientIdList = Set.of();
        if (filter.hasKeyword() && StringUtils.hasText(filter.getKeyword())) {
            clientIdList = getCustomerIdsByKeyword(businessId, filter.getKeyword());
            // add {pet name} {company name} search CRM-1639
            clientIdList.addAll(getCustomerIdsByCompanyKeyword(companyId, filter.getKeyword()));
        }

        Date requestDate = request.getDate();
        ListOverviewAppointmentResponse response =
                overviewService.listOverviewAppointment(ListOverviewAppointmentRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setCompanyId(companyId)
                        .setDate(requestDate)
                        .setDateType(request.getDateType())
                        .setOverviewStatus(request.getOverviewStatus())
                        .addAllServiceItemTypes(filter.getServiceItemTypesList())
                        .addAllAppointmentStatuses(filter.getAppointmentStatusesList())
                        .addAllCustomerIds(clientIdList)
                        .build());

        List<AppointmentOverview> appointmentOverviewsList = response.getAppointmentsList();

        if (CollectionUtils.isEmpty(appointmentOverviewsList)) {
            responseObserver.onNext(ListOverviewAppointmentResult.newBuilder()
                    .addAllItems(List.of())
                    .setPagination(PaginationResponse.getDefaultInstance())
                    .setPaginationV2(PaginationResponse.getDefaultInstance())
                    .build());
            responseObserver.onCompleted();
            return;
        }

        List<Long> appointmentIdList = appointmentOverviewsList.stream()
                .map(AppointmentOverview::getId)
                .toList();

        // pet detail
        var petDetailListFuture = futureService.getPetDetailListWithActualDates(companyId, appointmentIdList);

        // operation
        var serviceOperationMapFuture = futureService.getServiceOperationMap(companyId, appointmentIdList);

        // staff access control
        var staffFullDetailFuture = futureService.getStaffFullDetail(companyId, staffId);
        var showOnCalendarStaffIdsFuture =
                futureService.getStaffAccessShowOnCalendarStaffIds(companyId, businessId, staffId);

        CompletableFuture.allOf(
                        petDetailListFuture,
                        serviceOperationMapFuture,
                        staffFullDetailFuture,
                        showOnCalendarStaffIdsFuture)
                .join();

        // filter by staff access control
        List<AppointmentOverview> filteredOverviewList = filterAppointmentOverviewList(
                businessId,
                appointmentOverviewsList,
                petDetailListFuture.join(),
                serviceOperationMapFuture.join(),
                staffFullDetailFuture.join(),
                showOnCalendarStaffIdsFuture.join());

        List<Long> filteredAppointmentIds = filteredOverviewList.stream()
                .map(AppointmentOverview::getId)
                .distinct()
                .toList();

        if (CollectionUtils.isEmpty(filteredAppointmentIds)) {
            responseObserver.onNext(ListOverviewAppointmentResult.newBuilder()
                    .addAllItems(List.of())
                    .setPagination(PaginationResponse.getDefaultInstance())
                    .setPaginationV2(PaginationResponse.getDefaultInstance())
                    .build());
            responseObserver.onCompleted();
            return;
        }

        List<PetDetailModel> filteredPetDetailsList =
                new ArrayList<>(petDetailListFuture.join().getPetDetailsList());
        List<EvaluationServiceModel> filteredPetEvaluationsList =
                new ArrayList<>(petDetailListFuture.join().getPetEvaluationsList());
        filteredPetDetailsList.removeIf(
                petDetail -> filteredAppointmentIds.stream().noneMatch(id -> id.equals(petDetail.getGroomingId())));
        filteredPetEvaluationsList.removeIf(evaluation ->
                filteredAppointmentIds.stream().noneMatch(id -> id.equals(evaluation.getAppointmentId())));

        Map<Long, PetDetailModel> petDetailMap =
                filteredPetDetailsList.stream().collect(Collectors.toMap(PetDetailModel::getId, Function.identity()));

        // report card
        LocalDate requestLocalDate = DateTimeConverter.INSTANCE.toLocalDate(requestDate);
        String dateStr = requestLocalDate.toString();
        var reportCardSentResultsFuture =
                futureService.getAppointmentReportSentResultDTOs(filteredOverviewList, dateStr, companyId, businessId);

        // note
        var noteMapFuture = futureService.getAppointmentNoteMap(companyId, filteredAppointmentIds);

        // customer
        List<Long> customerIdList = filteredOverviewList.stream()
                .map(AppointmentOverview::getCustomerId)
                .distinct()
                .toList();
        var customerMapFuture = futureService.getBusinessCustomerMap(companyId, staffId, customerIdList);
        var membershipSubscriptionsFuture =
                futureService.supply(() -> membershipService.getAllSubscriptions(customerIdList));

        // agreement
        Map<Long, Long> appointmentIdToCustomerIdMap = filteredOverviewList.stream()
                .collect(Collectors.toMap(AppointmentOverview::getId, AppointmentOverview::getCustomerId, (a, b) -> a));
        var requiredSignAppointmentIdFuture =
                futureService.getRequiredSignAppointmentIdSet(businessId, appointmentIdToCustomerIdMap);

        // new customer
        var newCustomerIdListFuture = futureService.getNewCustomerIdList(companyId, customerIdList);

        // wait list
        List<Long> waitListAppointmentIdList = filteredOverviewList.stream()
                .filter(appointment -> appointment.getWaitListStatus().equals(WaitListStatus.WAITLISTONLY)
                        || appointment.getWaitListStatus().equals(WaitListStatus.APPTANDWAITLIST))
                .map(AppointmentOverview::getId)
                .toList();
        var waitListMapFuture = futureService.getWaitListCalendarViewMap(companyId, waitListAppointmentIdList);

        // review booster sent
        var reviewBoosterSentAppointmentIdFuture =
                futureService.getReviewBoosterSentAppointmentIdList(businessId, filteredAppointmentIds);

        // estimate order
        var estimatedOrderFuture = futureService.listEstimatedOrder(companyId, businessId, filteredAppointmentIds);

        // order list
        var ordersFuture = futureService.listOrders(filteredAppointmentIds);

        // invoice
        var invoiceMapFuture = futureService.getInvoiceCalendarViewMap(filteredAppointmentIds);

        // no show invoice
        var noShowInvoiceMapFuture = futureService.getNoShowInvoiceCalendarViewMap(filteredAppointmentIds);

        // pre auth
        var preAuthMapFuture = futureService.getPreAuthMap(businessId, filteredAppointmentIds);

        // pet
        var petMapFuture = futureService.getPetMap(companyId, petDetailListFuture);

        // service
        var serviceMapFuture = futureService.getServiceMap(companyId, filteredPetDetailsList);

        // boarding lodging split
        var boardingLodgingSplitFuture = futureService.getBoardingSplitLodgingMap(filteredAppointmentIds);

        // lodgings
        var lodgingMapFuture = futureService.getLodgingMap(
                filteredPetDetailsList, filteredPetEvaluationsList, boardingLodgingSplitFuture);

        // staff
        var staffMapFuture = futureService.getStaffMap(filteredPetDetailsList, filteredPetEvaluationsList);

        // invoice deposit
        var invoiceDepositMapFuture = futureService.getInvoiceDepositMap(companyId, invoiceMapFuture);

        // evaluation
        var evaluationFuture = futureService.getEvaluationMap(filteredPetEvaluationsList);

        // customer package
        var customerPackageFuture = packageHelper.listCustomerPackages(companyId, businessId, customerIdList);
        var customerPackageDetailFuture = packageHelper.listCustomerPackageDetails(customerPackageFuture);

        // pet code
        var petCodeMapFuture = futureService.getPetCodeMap(petDetailListFuture, companyId);

        // pet note
        var petNoteMapFuture = futureService.getPetNoteMap(petDetailListFuture);

        // pet evaluations
        var petEvaluationsMapFuture = futureService.getPetEvaluationsMap(petDetailListFuture, companyId);

        // pet code bindings
        var petCodeBindingMapFuture = futureService.getPetCodeBindingsMap(petDetailListFuture, companyId);

        // vaccine
        var petVaccineMapFuture = futureService.getVaccineMap(petDetailListFuture, companyId);

        // pet incident report
        var petIncidentReportMapFuture = futureService.getIncidentReportMap(petDetailListFuture, companyId);

        // cof status
        var cofStatusMapFuture = futureService.supply(() -> paymentService.getCOFStatusMap(customerIdList));
        // unpaid amount
        var unpaidAmountMapFuture =
                futureService.supply(() -> reportService.listCustomerUnpaidAmount(companyId, customerIdList));

        // company currency
        var companyCurrencyFuture = futureService.supply(() -> companySettingService.getComapnyCurrency(companyId));

        CompletableFuture.allOf(
                        // reportCardSentResultsFuture,
                        noteMapFuture,
                        customerMapFuture,
                        requiredSignAppointmentIdFuture,
                        newCustomerIdListFuture,
                        waitListMapFuture,
                        reviewBoosterSentAppointmentIdFuture,
                        estimatedOrderFuture,
                        ordersFuture,
                        invoiceMapFuture,
                        noShowInvoiceMapFuture,
                        preAuthMapFuture,
                        petMapFuture,
                        serviceMapFuture,
                        boardingLodgingSplitFuture,
                        lodgingMapFuture,
                        staffMapFuture,
                        invoiceDepositMapFuture,
                        evaluationFuture,
                        membershipSubscriptionsFuture,
                        customerPackageFuture,
                        customerPackageDetailFuture,
                        petCodeMapFuture,
                        petCodeBindingMapFuture,
                        petNoteMapFuture,
                        petEvaluationsMapFuture,
                        petVaccineMapFuture,
                        petIncidentReportMapFuture,
                        cofStatusMapFuture,
                        unpaidAmountMapFuture,
                        companyCurrencyFuture)
                .join();

        List<OverviewItem> items = filteredOverviewList.stream()
                .map(appointment -> {
                    List<ServiceDetailOverview> serviceDetailOverviewList = getServiceDetailOverviewList(
                            appointment,
                            filteredPetDetailsList,
                            filteredPetEvaluationsList,
                            petMapFuture.join(),
                            serviceMapFuture.join(),
                            lodgingMapFuture.join().getKey(),
                            lodgingMapFuture.join().getValue(),
                            staffMapFuture.join(),
                            evaluationFuture.join(),
                            petCodeMapFuture.join(),
                            petNoteMapFuture.join(),
                            petEvaluationsMapFuture.join(),
                            petVaccineMapFuture.join(),
                            petIncidentReportMapFuture.join(),
                            petCodeBindingMapFuture.join(),
                            boardingLodgingSplitFuture.join());
                    return OverviewItem.newBuilder()
                            .setAppointment(appointment)
                            .addAllServiceDetail(serviceDetailOverviewList)
                            .addAllNotes(noteMapFuture.join().getOrDefault(appointment.getId(), List.of()))
                            .setCustomer(CustomerCompositeOverview.newBuilder()
                                    .setCustomerProfile(CustomerConverter.INSTANCE.toOverview(customerMapFuture
                                            .join()
                                            .getOrDefault(
                                                    appointment.getCustomerId(),
                                                    BusinessCustomerModel.getDefaultInstance())))
                                    .setIsNewCustomer(
                                            newCustomerIdListFuture.join().contains(appointment.getCustomerId()))
                                    .setRequiredSign(requiredSignAppointmentIdFuture
                                            .join()
                                            .contains(appointment.getId()))
                                    .setReviewBoosterSent(reviewBoosterSentAppointmentIdFuture
                                            .join()
                                            .contains(appointment.getId()))
                                    .addAllCustomerPackages(PackageHelper.getPackageViews(
                                            customerPackageFuture.join(),
                                            customerPackageDetailFuture.join(),
                                            appointment.getCustomerId()))
                                    .setCofStatus(cofStatusMapFuture
                                            .join()
                                            .getOrDefault(
                                                    appointment.getCustomerId(),
                                                    CustomerCompositeOverview.COFStatus.NO_CARD_ON_FILE))
                                    .setUnpaidAmount(unpaidAmountMapFuture
                                            .join()
                                            .getOrDefault(
                                                    appointment.getCustomerId(),
                                                    ListCustomerUnpaidAmountResponse.CustomerUnpaidAmount
                                                            .getDefaultInstance())
                                            .getAmount())
                                    .build())
                            .setMembershipSubscriptions(membershipSubscriptionsFuture
                                    .join()
                                    .getOrDefault(
                                            appointment.getCustomerId(),
                                            MembershipSubscriptionListModel.getDefaultInstance()))
                            .setWaitList(waitListMapFuture
                                    .join()
                                    .getOrDefault(appointment.getId(), WaitListCalendarView.getDefaultInstance()))
                            .addAllServiceItemTypes(
                                    ServiceItemEnum.convertBitValueList(appointment.getServiceTypeInclude()).stream()
                                            .map(ServiceItemEnum::getServiceItem)
                                            .map(ServiceItemType::forNumber)
                                            .toList())
                            .setPreAuth(PreAuthConverter.INSTANCE.toView(preAuthMapFuture
                                    .join()
                                    .getOrDefault(Math.toIntExact(appointment.getId()), new PreAuthDTO())))
                            .setDeposits(invoiceDepositMapFuture
                                    .join()
                                    .getOrDefault(appointment.getId(), InvoiceDepositModel.getDefaultInstance()))
                            .setPaymentSummary(buildPaymentSummary(
                                    estimatedOrderFuture
                                            .join()
                                            .getOrDefault(
                                                    appointment.getId(),
                                                    PreviewEstimateOrderResponse.EstimatedOrder.getDefaultInstance()),
                                    ordersFuture.join().getOrDefault(appointment.getId(), List.of())))
                            .addAllOrders(ordersFuture.join().getOrDefault(appointment.getId(), List.of()))
                            .setInvoice(invoiceMapFuture
                                    .join()
                                    .getOrDefault(appointment.getId(), InvoiceCalendarView.getDefaultInstance()))
                            .setNoShowInvoice(noShowInvoiceMapFuture
                                    .join()
                                    .getOrDefault(appointment.getId(), NoShowInvoiceCalendarView.getDefaultInstance()))
                            .addAllReportStatuses(serviceDetailOverviewList.stream()
                                    .map(ServiceDetailOverview::getPet)
                                    .map(BusinessCustomerPetModelOverview::getId)
                                    .map(petId -> {
                                        Optional<ReportCardSendResultDTO> reportResult =
                                                reportCardSentResultsFuture.join().stream()
                                                        .filter(log ->
                                                                Objects.equals(log.appointmentId(), appointment.getId())
                                                                        && Objects.equals(log.petId(), petId))
                                                        .findAny();
                                        return reportResult
                                                .map(reportCardSendResultDTO -> ReportStatus.newBuilder()
                                                        .setPetId(petId)
                                                        .setStatus(reportCardSendResultDTO.status())
                                                        .build())
                                                .orElseGet(() -> ReportStatus.newBuilder()
                                                        .setPetId(petId)
                                                        .setStatus(OverviewReportStatus.UNSENT)
                                                        .build());
                                    })
                                    .toList())
                            .build();
                })
                .filter(appointment -> filterWithKeyword(appointment, filter.getKeyword()))
                .filter(appointment -> filterWithFilter(appointment, filter))
                .filter(appointment -> isMatchingServiceItemTypeInDateRange(
                        appointment,
                        new HashSet<>(filter.getServiceItemTypesList()),
                        DateTimeConverter.INSTANCE.toLocalDate(requestDate),
                        petDetailMap))
                .sorted(createComparator(
                        request.getOrderBysList(),
                        lodgingMapFuture.join().getKey(),
                        lodgingMapFuture.join().getValue()))
                .toList();

        int pageSize = request.getPagination().getPageSize();
        int pageNum = request.getPagination().getPageNum();

        List<List<OverviewItem>> partition = Lists.partition(items, pageSize);
        List<OverviewItem> itemsPage =
                (pageNum > 0 && pageNum <= partition.size()) ? partition.get(pageNum - 1) : List.of();

        responseObserver.onNext(ListOverviewAppointmentResult.newBuilder()
                .addAllItems(itemsPage)
                .addAllAppointmentCountByServiceItemTypes(countAppointmentByServiceItemType(items))
                .addAllPetCountByServiceItemTypes(countUniquePetsByServiceItemType(items))
                .setPagination(PaginationResponse.newBuilder()
                        .setPageNum(pageNum)
                        .setPageSize(pageSize)
                        .setTotal(countUniquePets(items))
                        .build())
                .setPaginationV2(PaginationResponse.newBuilder()
                        .setPageNum(pageNum)
                        .setPageSize(pageSize)
                        .setTotal(items.size())
                        .build())
                .setPetCount(countUniquePets(items))
                .build());
        responseObserver.onCompleted();
    }

    private static OverviewItem.PaymentSummary buildPaymentSummary(
            PreviewEstimateOrderResponse.EstimatedOrder order, List<OrderModelAppointmentView> orders) {
        return OverviewItem.PaymentSummary.newBuilder()
                .setSubtotalAmount(order.getEstimatedTotal())
                .setCollectedDepositAmount(OrderUtil.calculateDepositPaidAmount(orders))
                .build();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void countOverviewAppointment(
            CountOverviewAppointmentParams request, StreamObserver<CountOverviewAppointmentResult> responseObserver) {
        long businessId = request.getBusinessId();
        long companyId = AuthContext.get().companyId();
        long staffId = AuthContext.get().staffId();
        CountOverviewAppointmentParams.Filter filter = request.getFilter();

        final Set<Long> clientIdList = new HashSet<>();
        if (filter.hasKeyword() && StringUtils.hasText(filter.getKeyword())) {
            clientIdList.addAll(getCustomerIdsByKeyword(businessId, filter.getKeyword()));
            clientIdList.addAll(getCustomerIdsByCompanyKeyword(companyId, filter.getKeyword()));
        }

        final var timeZoneName = companySettingService.mustGetTimeZoneName(companyId);

        List<CountOverviewAppointmentResult.CountWithOverviewStatus> countResults =
                getFilteredStatusList(request, timeZoneName).stream()
                        .map(status -> CompletableFuture.supplyAsync(
                                () -> {
                                    ListOverviewAppointmentResponse response = overviewService.listOverviewAppointment(
                                            ListOverviewAppointmentRequest.newBuilder()
                                                    .setBusinessId(businessId)
                                                    .setCompanyId(companyId)
                                                    .setDate(request.getDate())
                                                    .setDateType(request.getDateType())
                                                    .setOverviewStatus(status)
                                                    .addAllServiceItemTypes(filter.getServiceItemTypesList())
                                                    .addAllCustomerIds(clientIdList)
                                                    .build());

                                    List<AppointmentOverview> appointmentOverviewsList = response.getAppointmentsList();
                                    if (CollectionUtils.isEmpty(appointmentOverviewsList)) {
                                        return CountOverviewAppointmentResult.CountWithOverviewStatus.newBuilder()
                                                .setOverviewStatus(status)
                                                .setCount(0)
                                                .build();
                                    }

                                    long count = calculatePetCount(
                                            businessId,
                                            companyId,
                                            staffId,
                                            filter,
                                            request.getDate(),
                                            appointmentOverviewsList);

                                    return CountOverviewAppointmentResult.CountWithOverviewStatus.newBuilder()
                                            .setOverviewStatus(status)
                                            .setCount(count)
                                            .build();
                                },
                                ThreadPool.getSubmitExecutor()))
                        .map(CompletableFuture::join)
                        .toList();

        responseObserver.onNext(CountOverviewAppointmentResult.newBuilder()
                .addAllCountWithOverviewStatus(countResults)
                .setCount(countResults.stream()
                        .mapToLong(CountOverviewAppointmentResult.CountWithOverviewStatus::getCount)
                        .sum())
                .build());
        responseObserver.onCompleted();
    }

    static List<OverviewStatus> getFilteredStatusList(CountOverviewAppointmentParams params, String timeZoneName) {
        if (params.hasOverviewStatus()) {
            return List.of(params.getOverviewStatus());
        }

        if (!CollectionUtils.isEmpty(params.getOverviewStatusesList())) {
            return params.getOverviewStatusesList();
        }

        LocalDate date = DateTimeConverter.INSTANCE.toLocalDate(params.getDate());
        LocalDate now = LocalDate.now(ZoneId.of(timeZoneName));
        if (date.isBefore(now)) {
            return List.of(OverviewStatus.APPOINTMENT_FINISHED);
        }

        if (date.isAfter(now)) {
            return List.of(OverviewStatus.EXPECTED, OverviewStatus.IN_STORE, OverviewStatus.APPOINTMENT_FINISHED);
        }

        return List.of(
                OverviewStatus.EXPECTED,
                OverviewStatus.IN_STORE,
                OverviewStatus.READY_TO_GO,
                OverviewStatus.APPOINTMENT_FINISHED);
    }

    private long calculatePetCount(
            long businessId,
            long companyId,
            long staffId,
            CountOverviewAppointmentParams.Filter filter,
            Date requestDate,
            List<AppointmentOverview> appointmentOverviewsList) {
        List<Long> appointmentIdList = appointmentOverviewsList.stream()
                .map(AppointmentOverview::getId)
                .toList();

        var petDetailListFuture = futureService.getPetDetailListWithActualDates(companyId, appointmentIdList);
        var serviceOperationMapFuture = futureService.getServiceOperationMap(companyId, appointmentIdList);
        var staffFullDetailFuture = futureService.getStaffFullDetail(companyId, staffId);
        var showOnCalendarStaffIdsFuture =
                futureService.getStaffAccessShowOnCalendarStaffIds(companyId, businessId, staffId);

        CompletableFuture.allOf(
                        petDetailListFuture,
                        serviceOperationMapFuture,
                        staffFullDetailFuture,
                        showOnCalendarStaffIdsFuture)
                .join();

        List<AppointmentOverview> filteredOverviewList = filterAppointmentOverviewList(
                businessId,
                appointmentOverviewsList,
                petDetailListFuture.join(),
                serviceOperationMapFuture.join(),
                staffFullDetailFuture.join(),
                showOnCalendarStaffIdsFuture.join());

        List<PetDetailModel> filteredPetDetailsList =
                new ArrayList<>(petDetailListFuture.join().getPetDetailsList());
        List<EvaluationServiceModel> filteredPetEvaluationsList =
                new ArrayList<>(petDetailListFuture.join().getPetEvaluationsList());

        Map<Long, PetDetailModel> petDetailMap =
                filteredPetDetailsList.stream().collect(Collectors.toMap(PetDetailModel::getId, Function.identity()));

        List<Long> customerIdList = filteredOverviewList.stream()
                .map(AppointmentOverview::getCustomerId)
                .distinct()
                .toList();

        var customerMapFuture = futureService.getBusinessCustomerMap(companyId, staffId, customerIdList);
        var petMapFuture = futureService.getPetMap(companyId, filteredPetDetailsList, filteredPetEvaluationsList);
        var serviceMapFuture = futureService.getServiceMap(companyId, filteredPetDetailsList);

        CompletableFuture.allOf(customerMapFuture, petMapFuture, serviceMapFuture)
                .join();

        return filteredOverviewList.stream()
                .map(appointment -> {
                    List<ServiceDetailOverview> overviewList = getSimpleServiceDetailOverviewList(
                            appointment.getId(),
                            serviceMapFuture.join(),
                            filteredPetDetailsList,
                            filteredPetEvaluationsList,
                            petMapFuture.join());
                    var customerId = appointment.getCustomerId();
                    var newOverviewList = overviewList.stream()
                            .filter(overview -> overview.getPet().getCustomerId() == customerId)
                            .toList();
                    return OverviewItem.newBuilder()
                            .addAllServiceDetail(newOverviewList)
                            .setCustomer(CustomerCompositeOverview.newBuilder()
                                    .setCustomerProfile(CustomerConverter.INSTANCE.toOverview(customerMapFuture
                                            .join()
                                            .getOrDefault(customerId, BusinessCustomerModel.getDefaultInstance())))
                                    .build())
                            .build();
                })
                .filter(appointment -> filterWithKeyword(appointment, filter.getKeyword()))
                .filter(appointment -> isMatchingServiceItemTypeInDateRange(
                        appointment,
                        new HashSet<>(filter.getServiceItemTypesList()),
                        DateTimeConverter.INSTANCE.toLocalDate(requestDate),
                        petDetailMap))
                .flatMap(appointment -> appointment.getServiceDetailList().stream())
                .map(serviceDetail -> serviceDetail.getPet().getId())
                .distinct()
                .count();
    }

    private Set<Long> getCustomerIdsByKeyword(long businessId, String filter) {
        SearchCustomerIdsParam param = new SearchCustomerIdsParam()
                .setBusinessId(Math.toIntExact(businessId))
                .setKeyword(filter);
        return customerClient.searchCustomerIds(param).stream()
                .map(Integer::longValue)
                .collect(Collectors.toSet());
    }

    private Set<Long> getCustomerIdsByCompanyKeyword(long companyId, String keyword) {
        return customerClient.searchCustomerIdsByFullText(companyId, keyword);
    }
}
