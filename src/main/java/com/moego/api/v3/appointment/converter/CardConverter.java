package com.moego.api.v3.appointment.converter;

import com.moego.idl.api.appointment.v1.ListDayCardsResult;
import com.moego.idl.api.appointment.v1.ListMonthCardsResult;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface CardConverter {

    CardConverter INSTANCE = Mappers.getMapper(CardConverter.class);

    @Mapping(target = "appointmentDate", source = "date")
    ListMonthCardsResult.CalendarCardSimpleView toSimpleView(ListDayCardsResult.CalendarCardCompositeView view);
}
