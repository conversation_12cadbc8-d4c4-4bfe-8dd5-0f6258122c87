package com.moego.api.v3.appointment.converter;

import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import java.math.BigDecimal;
import java.math.RoundingMode;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface PrePayConverter {

    PrePayConverter INSTANCE = Mappers.getMapper(PrePayConverter.class);

    default Double getPrePayRate(BookOnlineDepositDTO obDeposit, double totalAmount) {
        if (obDeposit == null) {
            return 0d;
        }
        if (totalAmount <= 0) {
            return 1d;
        }

        BigDecimal amountExcludeTipAndFee =
                obDeposit.getAmount().subtract(obDeposit.getConvenienceFee()).subtract(obDeposit.getTipsAmount());

        return amountExcludeTipAndFee
                .divide(BigDecimal.valueOf(totalAmount), 2, RoundingMode.HALF_UP)
                .doubleValue();
    }
}
