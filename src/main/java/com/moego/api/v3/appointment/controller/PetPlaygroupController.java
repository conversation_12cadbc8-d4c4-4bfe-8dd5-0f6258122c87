package com.moego.api.v3.appointment.controller;

import com.google.type.Date;
import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.api.v3.appointment.converter.PetPlaygroupConverter;
import com.moego.api.v3.appointment.utils.FutureService;
import com.moego.api.v3.appointment.utils.PetDetailUtil;
import com.moego.api.v3.shared.helper.BusinessHelper;
import com.moego.idl.api.appointment.v1.ListPlaygroupCalendarViewParams;
import com.moego.idl.api.appointment.v1.ListPlaygroupCalendarViewResult;
import com.moego.idl.api.appointment.v1.PetPlaygroupServiceGrpc;
import com.moego.idl.api.appointment.v1.ReschedulePetPlaygroupParams;
import com.moego.idl.api.appointment.v1.ReschedulePetPlaygroupResult;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v1.PetPlaygroupModel;
import com.moego.idl.models.offering.v1.PlaygroupModel;
import com.moego.idl.service.appointment.v1.ListPetPlaygroupRequest;
import com.moego.idl.service.appointment.v1.PetPlaygroupServiceGrpc.PetPlaygroupServiceBlockingStub;
import com.moego.idl.service.appointment.v1.ReschedulePetPlaygroupRequest;
import com.moego.idl.service.offering.v1.ListPlaygroupRequest;
import com.moego.idl.service.offering.v1.ListPlaygroupResponse;
import com.moego.idl.service.offering.v1.PlaygroupServiceGrpc.PlaygroupServiceBlockingStub;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class PetPlaygroupController extends PetPlaygroupServiceGrpc.PetPlaygroupServiceImplBase {

    private final PetPlaygroupServiceBlockingStub petPlaygroupServiceBlockingStub;
    private final PlaygroupServiceBlockingStub playgroupServiceBlockingStub;
    private final PetDetailUtil petDetailUtil;
    private final FutureService futureService;
    private final BusinessHelper businessHelper;

    @Override
    @Auth(AuthType.COMPANY)
    public void listPlaygroupCalendarView(
            ListPlaygroupCalendarViewParams request, StreamObserver<ListPlaygroupCalendarViewResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var businessId = request.getBusinessId();
        businessHelper.checkBusinessCompany(companyId, businessId);

        // playgroup models
        ListPlaygroupResponse listPlaygroupResponse =
                playgroupServiceBlockingStub.listPlaygroup(ListPlaygroupRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setPagination(request.getPlaygroupPagination())
                        .build());
        List<PlaygroupModel> playgroupsList = listPlaygroupResponse.getPlaygroupsList();
        var playgroupIds = playgroupsList.stream().map(PlaygroupModel::getId).toList();

        if (playgroupIds.isEmpty()) {
            responseObserver.onNext(ListPlaygroupCalendarViewResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // init result with playgroups and playgroup dailies
        List<ListPlaygroupCalendarViewResult.PlaygroupDailyView> initPlaygroupDailies =
                initPlaygroupDailies(request.getStartDate(), request.getEndDate(), playgroupsList);
        ListPlaygroupCalendarViewResult result = ListPlaygroupCalendarViewResult.newBuilder()
                .addAllPlaygroups(playgroupsList)
                .addAllPlaygroupDailies(initPlaygroupDailies)
                .addAllAppointments(List.of())
                .addAllPets(List.of())
                .build();

        var playgroupMap =
                playgroupsList.stream().collect(Collectors.toMap(PlaygroupModel::getId, Function.identity()));

        // pet playgroup daily list
        List<PetPlaygroupModel> petPlaygroupsList = petPlaygroupServiceBlockingStub
                .listPetPlaygroup(ListPetPlaygroupRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .addAllPlaygroupIds(playgroupIds)
                        .setStartDate(request.getStartDate())
                        .setEndDate(request.getEndDate())
                        .build())
                .getPetPlaygroupsList();

        if (petPlaygroupsList.isEmpty()) {
            responseObserver.onNext(result);
            responseObserver.onCompleted();
            return;
        }

        List<ListPlaygroupCalendarViewResult.PlaygroupDailyView> playgroupDailies =
                buildPlaygroupDailies(initPlaygroupDailies, petPlaygroupsList);

        // appointment info & pet list
        var appointmentIds = petPlaygroupsList.stream()
                .map(PetPlaygroupModel::getAppointmentId)
                .distinct()
                .toList();

        var appointmentsFuture = futureService.getAppointmentList(appointmentIds, companyId);
        var petDetailMapFuture = futureService.getPetDetailMap(appointmentIds, companyId);

        CompletableFuture.allOf(appointmentsFuture, petDetailMapFuture).join();

        var petDetails = petDetailMapFuture.join();
        var petIds = petDetails.values().stream()
                .flatMap(List::stream)
                .map(PetDetailModel::getPetId)
                .distinct()
                .toList();
        var customerIds = appointmentsFuture.join().stream()
                .map(AppointmentModel::getCustomerId)
                .distinct()
                .toList();

        var petInfoListFuture = futureService.getPetInfoList(petIds, companyId);
        var vaccineMapFuture = futureService.supply(() -> petDetailUtil.getVaccineMap(petIds, companyId));
        var customerMapFuture = futureService.getBusinessCustomerInfoMap(companyId, customerIds);

        CompletableFuture.allOf(petInfoListFuture, vaccineMapFuture).join();

        result = ListPlaygroupCalendarViewResult.newBuilder()
                .addAllPlaygroups(playgroupsList)
                .addAllPlaygroupDailies(playgroupDailies)
                .addAllAppointments(
                        PetPlaygroupConverter.Instance.toAppointmentViewList(appointmentsFuture.join(), petDetails))
                .addAllPets(PetPlaygroupConverter.Instance.toPetViewList(
                        petInfoListFuture.join(), playgroupMap, vaccineMapFuture.join(), customerMapFuture.join()))
                .setPlaygroupPagination(PaginationResponse.newBuilder()
                        .setPageNum(listPlaygroupResponse.getPagination().getPageNum())
                        .setPageSize(listPlaygroupResponse.getPagination().getPageNum())
                        .setTotal(listPlaygroupResponse.getPagination().getTotal())
                        .build())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void reschedulePetPlaygroup(
            ReschedulePetPlaygroupParams request, StreamObserver<ReschedulePetPlaygroupResult> responseObserver) {

        petPlaygroupServiceBlockingStub.reschedulePetPlaygroup(ReschedulePetPlaygroupRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .setStaffId(AuthContext.get().staffId())
                .setPlaygroupId(request.getPlaygroupId())
                .setPetPlaygroupId(request.getPetPlaygroupId())
                .setIndex(request.getIndex())
                .setDate(request.getDate())
                .build());

        responseObserver.onNext(ReschedulePetPlaygroupResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private List<ListPlaygroupCalendarViewResult.PlaygroupDailyView> buildPlaygroupDailies(
            List<ListPlaygroupCalendarViewResult.PlaygroupDailyView> initPlaygroupViews,
            List<PetPlaygroupModel> petPlaygroupsList) {

        // 1. 按日期和 playgroupId 进行分组
        Map<Date, Map<Long, List<PetPlaygroupModel>>> groupedByDateAndPlaygroup = petPlaygroupsList.stream()
                .collect(Collectors.groupingBy(
                        PetPlaygroupModel::getDate,
                        Collectors.groupingBy(
                                PetPlaygroupModel::getPlaygroupId,
                                Collectors.collectingAndThen(Collectors.toList(), list -> {
                                    list.sort(Comparator.comparing(PetPlaygroupModel::getSort));
                                    return list;
                                }))));

        // 2. 基于初始结构填充数据
        return initPlaygroupViews.stream()
                .map(dailyView -> {
                    Date date = dailyView.getDate();
                    Map<Long, List<PetPlaygroupModel>> datePlaygroups =
                            groupedByDateAndPlaygroup.getOrDefault(date, Map.of());

                    List<ListPlaygroupCalendarViewResult.PlaygroupView> updatedPlaygroups =
                            dailyView.getPlaygroupsList().stream()
                                    .map(playgroupView -> {
                                        long playgroupId = playgroupView.getPlaygroupId();
                                        List<PetPlaygroupModel> petPlaygroups =
                                                datePlaygroups.getOrDefault(playgroupId, List.of());

                                        return ListPlaygroupCalendarViewResult.PlaygroupView.newBuilder()
                                                .setPlaygroupId(playgroupId)
                                                .setPetNumber(petPlaygroups.stream()
                                                        .map(PetPlaygroupModel::getPetId)
                                                        .collect(Collectors.toSet())
                                                        .size())
                                                .addAllPetPlaygroups(buildPetPlaygroupViews(petPlaygroups))
                                                .build();
                                    })
                                    .toList();

                    return ListPlaygroupCalendarViewResult.PlaygroupDailyView.newBuilder()
                            .setDate(date)
                            .addAllPlaygroups(updatedPlaygroups)
                            .build();
                })
                .toList();
    }

    private List<ListPlaygroupCalendarViewResult.PetPlaygroupView> buildPetPlaygroupViews(
            List<PetPlaygroupModel> petPlaygroups) {
        return petPlaygroups.stream()
                .map(petPlaygroup -> ListPlaygroupCalendarViewResult.PetPlaygroupView.newBuilder()
                        .setPetPlaygroupId(petPlaygroup.getId())
                        .setPetId(petPlaygroup.getPetId())
                        .setAppointmentId(petPlaygroup.getAppointmentId())
                        .setSort(petPlaygroup.getSort())
                        .build())
                .toList();
    }

    private List<ListPlaygroupCalendarViewResult.PlaygroupDailyView> initPlaygroupDailies(
            Date startDate, Date endDate, List<PlaygroupModel> playgroupsList) {
        playgroupsList = playgroupsList.stream()
                .sorted(Comparator.comparing(PlaygroupModel::getSort))
                .toList();
        // 创建一个空的 playgroup 视图列表
        List<ListPlaygroupCalendarViewResult.PlaygroupView> emptyPlaygroups = playgroupsList.stream()
                .map(playgroup -> ListPlaygroupCalendarViewResult.PlaygroupView.newBuilder()
                        .setPlaygroupId(playgroup.getId())
                        .setPetNumber(0)
                        .addAllPetPlaygroups(List.of())
                        .build())
                .toList();

        LocalDate start = DateTimeConverter.INSTANCE.toLocalDate(startDate);
        LocalDate end = DateTimeConverter.INSTANCE.toLocalDate(endDate);

        return start.datesUntil(end.plusDays(1))
                .map(DateTimeConverter.INSTANCE::toGoogleDate)
                .map(date -> ListPlaygroupCalendarViewResult.PlaygroupDailyView.newBuilder()
                        .setDate(date)
                        .addAllPlaygroups(emptyPlaygroups)
                        .build())
                .toList();
    }
}
