package com.moego.api.v3.appointment.dto;

import com.moego.lib.utils.model.Pair;
import java.util.Set;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class DaySlotInfoDetailDTO {
    // the start date
    private String date;
    // the staff id
    private long staffId;
    // the start time, in minutes
    private int startTime;
    // the end time, in minutes
    private int endTime;
    // pet capacity
    private int petCapacity;

    // used pet ids
    private Set<Pair<Long, Long>> usedAppointmentPetPairs;

    public boolean contains(int time) {
        return startTime <= time && time < endTime;
    }
}
