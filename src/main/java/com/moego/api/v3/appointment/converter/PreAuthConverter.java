package com.moego.api.v3.appointment.converter;

import com.moego.idl.models.payment.v1.PreAuthCalendarView;
import com.moego.idl.models.payment.v1.PreAuthEnableDef;
import com.moego.server.grooming.params.PreAuthParams;
import com.moego.server.payment.dto.PreAuthDTO;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/1/25
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface PreAuthConverter {

    PreAuthConverter INSTANCE = Mappers.getMapper(PreAuthConverter.class);

    @Mapping(target = "preAuthEnable", source = "enable")
    @Mapping(target = "preAuthPaymentMethod", source = "paymentMethodId")
    @Mapping(target = "preAuthCardNumber", source = "cardBrandLast4")
    PreAuthParams defToParams(PreAuthEnableDef def);

    @Mapping(target = ".", source = "feesDetail")
    PreAuthCalendarView toView(PreAuthDTO dto);

    List<PreAuthCalendarView> toView(List<PreAuthDTO> dto);
}
