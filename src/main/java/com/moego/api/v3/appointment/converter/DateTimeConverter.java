package com.moego.api.v3.appointment.converter;

import static java.time.ZoneOffset.UTC;

import com.google.protobuf.Timestamp;
import com.google.type.Date;
import com.google.type.Interval;
import com.google.type.TimeOfDay;
import com.moego.idl.utils.v1.TimeOfDayInterval;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.Calendar;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2024/9/26
 */
@Mapper
public interface DateTimeConverter {

    DateTimeConverter INSTANCE = Mappers.getMapper(DateTimeConverter.class);

    default LocalDate toLocalDate(Date date) {
        return LocalDate.of(date.getYear(), date.getMonth(), date.getDay());
    }

    default Date toGoogleDate(LocalDate date) {
        return Date.newBuilder()
                .setDay(date.getDayOfMonth())
                .setMonth(date.getMonthValue())
                .setYear(date.getYear())
                .build();
    }

    default ZonedDateTime toZonedDateTime(Date date, String timezone) {
        return toLocalDate(date).atStartOfDay().atZone(ZoneId.of(timezone));
    }

    default Interval buildInterval(long startTimestamp, long endTimestamp) {
        return Interval.newBuilder()
                .setStartTime(Timestamp.newBuilder().setSeconds(startTimestamp).build())
                .setEndTime(Timestamp.newBuilder().setSeconds(endTimestamp).build())
                .build();
    }

    default TimeOfDayInterval buildAMInterval() {
        return TimeOfDayInterval.newBuilder()
                .setEnd(TimeOfDay.newBuilder().setHours(11).setMinutes(59))
                .build();
    }

    default TimeOfDayInterval buildPMInterval() {
        return TimeOfDayInterval.newBuilder()
                .setStart(TimeOfDay.newBuilder().setHours(12))
                .build();
    }

    default TimeOfDay minutesToTimeOfDay(int minutes) {
        return TimeOfDay.newBuilder()
                .setHours(minutes / 60)
                .setMinutes(minutes % 60)
                .build();
    }

    default TimeOfDayInterval buildTimeOfDayInterval(int startMinutes, int endMinutes) {
        return TimeOfDayInterval.newBuilder()
                .setStart(minutesToTimeOfDay(startMinutes))
                .setEnd(minutesToTimeOfDay(endMinutes))
                .build();
    }

    default Interval buildFullDayInterval(Date date, String timeZoneName) {
        var zonedDateTime = DateTimeConverter.INSTANCE.toZonedDateTime(date, timeZoneName);
        return Interval.newBuilder()
                .setStartTime(Timestamp.newBuilder()
                        .setSeconds(zonedDateTime.toEpochSecond())
                        .build())
                .setEndTime(Timestamp.newBuilder()
                        .setSeconds(zonedDateTime.plusDays(1).toEpochSecond() - 1)
                        .build())
                .build();
    }

    default Interval buildDayInterval(Date startDate, Date endDate, String timeZoneName) {
        var startZonedDateTime = DateTimeConverter.INSTANCE.toZonedDateTime(startDate, timeZoneName);
        var endZonedDateTime = DateTimeConverter.INSTANCE.toZonedDateTime(endDate, timeZoneName);
        return Interval.newBuilder()
                .setStartTime(Timestamp.newBuilder()
                        .setSeconds(startZonedDateTime.toEpochSecond())
                        .build())
                .setEndTime(Timestamp.newBuilder()
                        .setSeconds(endZonedDateTime.toEpochSecond())
                        .build())
                .build();
    }

    default LocalDate fromGoogleDate(Date date) {
        return LocalDate.of(date.getYear(), date.getMonth(), date.getDay());
    }

    default java.util.Date convertToJavaDate(Date googleDate) {
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.YEAR, googleDate.getYear());
        cal.set(Calendar.MONTH, googleDate.getMonth() - 1);
        cal.set(Calendar.DAY_OF_MONTH, googleDate.getDay());
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTime();
    }

    default Date convertToGoogleDate(java.util.Date javaDate) {
        Calendar cal = Calendar.getInstance();
        cal.setTime(javaDate);

        return Date.newBuilder()
                .setYear(cal.get(Calendar.YEAR))
                .setMonth(cal.get(Calendar.MONTH) + 1)
                .setDay(cal.get(Calendar.DAY_OF_MONTH))
                .build();
    }

    default LocalDateTime toLocalDateTime(Timestamp timestamp) {
        return LocalDateTime.ofEpochSecond(timestamp.getSeconds(), timestamp.getNanos(), UTC);
    }

    default Timestamp toTimestamp(LocalDateTime localDateTime) {
        return Timestamp.newBuilder()
                .setSeconds(localDateTime.toEpochSecond(UTC))
                .setNanos(localDateTime.getNano())
                .build();
    }
}
