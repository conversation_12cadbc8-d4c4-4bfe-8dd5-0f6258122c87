package com.moego.api.v3.appointment.controller;

import static java.util.Comparator.comparingLong;
import static java.util.stream.Collectors.groupingBy;

import com.google.protobuf.Timestamp;
import com.moego.api.v3.appointment.converter.LodgingConverter;
import com.moego.api.v3.appointment.service.LodgingUsingService;
import com.moego.api.v3.appointment.utils.FutureService;
import com.moego.api.v3.appointment.utils.LodgingUtil;
import com.moego.api.v3.appointment.utils.PetDetailUtil;
import com.moego.api.v3.offering.service.EvaluationService;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.api.appointment.v1.GetLastLodgingParams;
import com.moego.idl.api.appointment.v1.GetLastLodgingResult;
import com.moego.idl.api.appointment.v1.GetLodgingCalendarViewParams;
import com.moego.idl.api.appointment.v1.GetLodgingCalendarViewResult;
import com.moego.idl.api.appointment.v1.GetLodgingCalendarViewV2Params;
import com.moego.idl.api.appointment.v1.GetLodgingCalendarViewV2Result;
import com.moego.idl.api.appointment.v1.GetLodgingListParams;
import com.moego.idl.api.appointment.v1.GetLodgingListResult;
import com.moego.idl.api.appointment.v1.LodgingInUseCheckParams;
import com.moego.idl.api.appointment.v1.LodgingInUseCheckResult;
import com.moego.idl.api.appointment.v1.LodgingServiceGrpc.LodgingServiceImplBase;
import com.moego.idl.api.appointment.v1.LodgingTransferParams;
import com.moego.idl.api.appointment.v1.LodgingTransferResult;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.LodgingAssignAppointmentInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetDetailInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetEvaluationInfo;
import com.moego.idl.models.appointment.v1.LodgingListView;
import com.moego.idl.models.appointment.v1.LodgingOccupiedStatus;
import com.moego.idl.models.appointment.v1.LodgingUnitView;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.LodgingUnitType;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.offering.v1.GetServiceDetailRequest;
import com.moego.idl.service.offering.v1.GetServiceDetailResponse;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@GrpcService
@RequiredArgsConstructor
public class LodgingController extends LodgingServiceImplBase {
    private final PetDetailUtil petDetailUtil;
    private final LodgingUtil lodgingUtil;
    private final EvaluationService evaluationService;
    private final LodgingUsingService lodgingUsingService;
    private final FutureService futureService;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementService;

    @Override
    @Auth(AuthType.COMPANY)
    public void getLodgingCalendarView(
            GetLodgingCalendarViewParams request, StreamObserver<GetLodgingCalendarViewResult> responseObserver) {
        throw ExceptionUtil.bizException(
                Code.CODE_FORBIDDEN, "Your page is out of date, please refresh the page and try again.");
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getLodgingCalendarViewV2(
            GetLodgingCalendarViewV2Params request, StreamObserver<GetLodgingCalendarViewV2Result> responseObserver) {
        long businessId = request.getBusinessId();
        long companyId = AuthContext.get().companyId();
        long staffId = AuthContext.get().staffId();

        if (DateUtil.countDaysBetween(request.getStartDate(), request.getEndDate()) > 13) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "The maximum number of weeks that can be queried is 2 weeks.");
        }

        // 获取 lodging 信息
        List<LodgingUnitModel> lodgingUnitList = lodgingUtil.getLodgingUnit(companyId, businessId, null, null);
        List<LodgingTypeModel> lodgingTypeList = lodgingUtil.getLodgingTypeByIds(
                lodgingUnitList.stream().map(LodgingUnitModel::getLodgingTypeId).toList());

        // 获取 lodging 使用信息
        List<LodgingAssignInfo> assignInfoList = new ArrayList<>();
        Map<Long, String> appointmentIdToColorCode = new HashMap<>();
        // staff access control 判断是否能获取使用信息
        Boolean canAccessBoardingAndDaycare = futureService
                .getStaffCanAccessBoardingAndDaycare(companyId, businessId, staffId)
                .join();
        if (Boolean.TRUE.equals(canAccessBoardingAndDaycare)) {
            assignInfoList = lodgingUtil.getLodgingAssignInfo(
                    companyId, businessId, request.getStartDate(), request.getEndDate());
            appointmentIdToColorCode = getLodgingAppointmentColor(companyId, assignInfoList);
        }

        responseObserver.onNext(GetLodgingCalendarViewV2Result.newBuilder()
                .addAllLodgingTypes(buildLodgingViewLodgingTypeView(
                        request.getStartDate(),
                        request.getEndDate(),
                        assignInfoList,
                        getSortedLodgingTypeWithDefaultArea(lodgingTypeList),
                        getLodgingUnitWithDefaultArea(lodgingUnitList),
                        new HashSet<>(request.getLodgingTypeIdsList()),
                        LodgingConverter.INSTANCE.toLodgingStatus(request),
                        appointmentIdToColorCode))
                .addAllPets(LodgingConverter.INSTANCE.buildCalendarViewPetView(
                        getAssignedPetInfo(companyId, assignInfoList).values().stream()
                                .toList()))
                .addAllPetDetails(LodgingConverter.INSTANCE.buildCalendarViewPetDetailView(
                        LodgingUtil.getAssignedPetDetails(assignInfoList)))
                .addAllPetEvaluations(LodgingConverter.INSTANCE.buildCalendarViewPetEvaluationView(
                        LodgingUtil.getAssignedPetEvaluations(assignInfoList)))
                .setMaxPetTotalNum(LodgingUtil.calculateMaxPetNum(lodgingTypeList, lodgingUnitList))
                .putAllDateToExistPetCount(LodgingUtil.calculatePetCntStatPerDay(
                        request.getStartDate(), request.getEndDate(), assignInfoList))
                .build());
        responseObserver.onCompleted();
    }

    List<LodgingUnitModel> getLodgingUnitWithDefaultArea(List<LodgingUnitModel> lodgingUnitList) {
        List<LodgingUnitModel> result = new ArrayList<>(lodgingUnitList);
        result.add(LodgingUnitModel.newBuilder()
                .setId(LodgingUtil.DEFAULT_LODGING_UNIT_ID)
                .setName(LodgingUtil.DEFAULT_LODGING_UNIT_NAME)
                .setSort(Integer.MAX_VALUE)
                .build());
        return result;
    }

    List<LodgingTypeModel> getSortedLodgingTypeWithDefaultArea(List<LodgingTypeModel> lodgingTypeList) {
        List<LodgingTypeModel> result = new ArrayList<>(lodgingTypeList);
        result.add(LodgingTypeModel.newBuilder()
                .setId(LodgingUtil.DEFAULT_LODGING_TYPE_ID)
                .setName(LodgingUtil.DEFAULT_LODGING_TYPE_NAME)
                .setMaxPetNum(LodgingUtil.UNLIMITED_MAX_PET_NUM)
                .setSort(Integer.MAX_VALUE)
                .build());
        return result.stream()
                .sorted(Comparator.comparingInt(LodgingTypeModel::getSort))
                .toList();
    }

    private Map<Long, String> getLodgingAppointmentColor(Long companyId, List<LodgingAssignInfo> assignInfoList) {
        List<Long> appointmentIds = LodgingUtil.getAssignedAppointmentId(assignInfoList);
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return new HashMap<>();
        }

        // 拉取 pet detail 信息
        var petDetails = petDetailUtil.getPetDetails(companyId, appointmentIds);
        Map<Long, List<PetDetailModel>> appointmentIdToPetDetails =
                petDetails.getPetDetailsList().stream().collect(groupingBy(PetDetailModel::getGroomingId));
        Map<Long, List<EvaluationServiceModel>> appointmentIdToPetEvaluations =
                petDetails.getPetEvaluationsList().stream()
                        .collect(groupingBy(EvaluationServiceModel::getAppointmentId));

        // 同一个预约的 color 由 service color 决定. 对于 pet detail 优先取 primary pet detail 的
        // service color
        Map<Long, Long> appointmentIdToServiceId = new HashMap<>();
        Map<Long, Long> appointmentIdToEvaluationId = new HashMap<>();
        for (Long appointmentId : appointmentIds) {
            List<EvaluationServiceModel> petEvaluationList = appointmentIdToPetEvaluations.get(appointmentId);
            if (!CollectionUtils.isEmpty(petEvaluationList)) {
                Long serviceId = petEvaluationList.get(0).getServiceId();
                appointmentIdToEvaluationId.put(appointmentId, serviceId);
                continue;
            }

            List<PetDetailModel> petDetailList = appointmentIdToPetDetails.get(appointmentId);
            if (!CollectionUtils.isEmpty(petDetailList)) {
                // 找到其 primary pet detail
                Long serviceId = petDetailList.stream()
                        .min(comparingLong(PetDetailModel::getId))
                        .get()
                        .getServiceId();
                appointmentIdToServiceId.put(appointmentId, serviceId);
            }
        }

        // 拉取 service 详情
        Map<Long, ServiceBriefView> serviceMap =
                petDetailUtil.getServiceMap(companyId, new ArrayList<>(appointmentIdToServiceId.values()));
        Map<Long, EvaluationBriefView> evaluationMap =
                evaluationService.getEvaluationMapByIds(new ArrayList<>(appointmentIdToEvaluationId.values()));

        // 组装 lodgingAppointment color 信息
        Map<Long, String> appointmentColor = new HashMap<>();
        for (Long appointmentId : appointmentIds) {
            Optional.ofNullable(appointmentIdToEvaluationId.get(appointmentId))
                    .map(evaluationMap::get)
                    .ifPresent(evaluation -> appointmentColor.put(appointmentId, evaluation.getColorCode()));
            Optional.ofNullable(appointmentIdToServiceId.get(appointmentId))
                    .map(serviceMap::get)
                    .ifPresent(service -> appointmentColor.put(appointmentId, service.getColorCode()));
        }

        return appointmentColor;
    }

    Map<Long, BusinessCustomerPetInfoModel> getAssignedPetInfo(Long companyId, List<LodgingAssignInfo> assignInfoList) {
        List<Long> petIds = LodgingUtil.getAssignedPetId(assignInfoList).stream()
                .map(k -> (long) k)
                .toList();
        return petDetailUtil.getPetMap(companyId, petIds);
    }

    private static List<GetLodgingCalendarViewV2Result.LodgingTypeView> buildLodgingViewLodgingTypeView(
            String startDate,
            String endDate,
            List<LodgingAssignInfo> assignInfoList,
            List<LodgingTypeModel> sortedLodgingTypes,
            List<LodgingUnitModel> lodgingUnits,
            Set<Long> lodgingTypeIdsFilter,
            List<LodgingOccupiedStatus> statuses,
            Map<Long, String> appointmentIdToColorCode) {
        var lodgingType2SortedUnits = lodgingUnits.stream()
                .collect(groupingBy(
                        LodgingUnitModel::getLodgingTypeId,
                        Collectors.collectingAndThen(Collectors.toList(), list -> list.stream()
                                .sorted(Comparator.comparing(LodgingUnitModel::getSort))
                                .toList())));
        var petCntPerDayPerLodging = LodgingUtil.calPetCntPerDayPerLodging(startDate, endDate, assignInfoList);

        List<GetLodgingCalendarViewV2Result.LodgingTypeView> result = new ArrayList<>();
        for (LodgingTypeModel lodgingType : sortedLodgingTypes) {
            if (!lodgingTypeIdsFilter.isEmpty() && !lodgingTypeIdsFilter.contains(lodgingType.getId())) {
                continue;
            }
            var lodgingTypeUnits = lodgingType2SortedUnits.get(lodgingType.getId());

            // 计算该 lodging type 下的宠物数量
            int maxPetTotalNum = LodgingUtil.calculateMaxPetNum(List.of(lodgingType), lodgingTypeUnits);
            Map<String, Integer> existPetPerDay =
                    LodgingUtil.calPetCntPerDay(startDate, endDate, lodgingTypeUnits, petCntPerDayPerLodging);

            // 按占用状态过滤出需要展示的 lodging unit
            Map<Long, LodgingOccupiedStatus> lodgingUnitStatus =
                    LodgingUtil.calLodgingStatus(startDate, endDate, lodgingTypeUnits, petCntPerDayPerLodging);
            lodgingTypeUnits =
                    LodgingUtil.filterLodgingUnitByOccupiedStatus(statuses, lodgingTypeUnits, lodgingUnitStatus);
            // lodging type 下没有需要展示的 lodging unit，则该 lodging type 不展示
            if (CollectionUtils.isEmpty(lodgingTypeUnits)) {
                continue;
            }

            int totalCapacity = LodgingUtil.calculateTotalCapacity(List.of(lodgingType), lodgingTypeUnits);
            Map<String, Integer> capacityOccupiedPerDay = LodgingUtil.calculateCapacityPerDay(
                    startDate, endDate, lodgingType, lodgingTypeUnits, petCntPerDayPerLodging);

            var build = GetLodgingCalendarViewV2Result.LodgingTypeView.newBuilder()
                    .setId(lodgingType.getId())
                    .setName(lodgingType.getName())
                    .setLodgingUnitType(lodgingType.getLodgingUnitType())
                    .setMaxPetNum(lodgingType.getMaxPetNum())
                    .setAllPetSizes(lodgingType.getPetSizeFilter())
                    .setPetSizeFilter(lodgingType.getPetSizeFilter())
                    .addAllPetSizeIds(lodgingType.getPetSizeIdsList())
                    .setMaxPetTotalNum(maxPetTotalNum)
                    .putAllExistPetPerDay(existPetPerDay)
                    .addAllLodgingUnits(
                            buildLodgingUnitView(assignInfoList, lodgingTypeUnits, appointmentIdToColorCode))
                    .setTotalCapacity(totalCapacity)
                    .putAllOccupiedCapacityPerDay(capacityOccupiedPerDay)
                    .build();
            result.add(build);
        }
        return result;
    }

    private static List<GetLodgingCalendarViewV2Result.LodgingUnitView> buildLodgingUnitView(
            List<LodgingAssignInfo> assignInfoList,
            List<LodgingUnitModel> lodgingUnits,
            Map<Long, String> appointmentIdToColorCode) {
        Map<Long, List<LodgingAssignAppointmentInfo>> lodgingAppointmentMap = assignInfoList.stream()
                .collect(Collectors.toMap(
                        LodgingAssignInfo::getLodgingId, LodgingAssignInfo::getAppointmentsList, (k1, k2) -> k1));

        List<GetLodgingCalendarViewV2Result.LodgingUnitView> result = new ArrayList<>();
        for (LodgingUnitModel lodgingUnit : lodgingUnits) {
            GetLodgingCalendarViewV2Result.LodgingUnitView.Builder lodgingUnitViewBuilder =
                    GetLodgingCalendarViewV2Result.LodgingUnitView.newBuilder()
                            .setId(lodgingUnit.getId())
                            .setName(lodgingUnit.getName());
            // 对 lodging unit 下每一个预约，计算 ticket
            for (var appointmentInfo : lodgingAppointmentMap.getOrDefault(lodgingUnit.getId(), List.of())) {
                long appointmentId = appointmentInfo.getId();
                String colorCode = appointmentIdToColorCode.getOrDefault(appointmentId, "");
                // <ticketTimeRange, <pet, lodgingUsage>>
                Map<String, Map<Integer, GetLodgingCalendarViewV2Result.LodgingUsage.Builder>> ticketPetMap =
                        new HashMap<>();
                // 对预约下每一只 pet 计算 ticket
                // 如果该预约下多个 pet 对应的 ticket 时间上完全重合，则合并为一张 ticket
                appointmentInfo.getPetDetailsList().stream()
                        .collect(groupingBy(LodgingAssignPetDetailInfo::getPetId))
                        .forEach((petId, petDetails) -> collectTicketInfo(petId, petDetails, ticketPetMap));

                for (LodgingAssignPetEvaluationInfo petEvaluation : appointmentInfo.getPetEvaluationsList()) {
                    collectTicketInfo(
                            petEvaluation.getPetId(),
                            petEvaluation.getStartDate(),
                            petEvaluation.getEndDate(),
                            petEvaluation.getStartTime(),
                            petEvaluation.getEndTime(),
                            null,
                            List.of(petEvaluation.getId()),
                            ticketPetMap);
                }

                lodgingUnitViewBuilder.addAllLodgingTickets(ticketPetMap.entrySet().stream()
                        .map(entry -> {
                            String timeRange = entry.getKey();
                            List<GetLodgingCalendarViewV2Result.LodgingUsage> petUsages =
                                    entry.getValue().values().stream()
                                            .map(GetLodgingCalendarViewV2Result.LodgingUsage.Builder::build)
                                            .toList();

                            GetLodgingCalendarViewV2Result.LodgingTicket.Builder ticketBuilder =
                                    GetLodgingCalendarViewV2Result.LodgingTicket.newBuilder()
                                            .setAppointmentId(appointmentId)
                                            .setCustomerId(appointmentInfo.getCustomerId())
                                            .setColorCode(colorCode)
                                            .addAllUsages(petUsages);
                            fillTicketTimeRange(ticketBuilder, timeRange);
                            return ticketBuilder.build();
                        })
                        .toList());
            }
            result.add(lodgingUnitViewBuilder.build());
        }
        return result;
    }

    static void handleBoardingTickets(
            Integer petId,
            List<LodgingAssignPetDetailInfo> petDetails,
            Map<String, Map<Integer, GetLodgingCalendarViewV2Result.LodgingUsage.Builder>> ticketPetMap) {
        var hasSplitLodging = petDetails.stream().allMatch(LodgingAssignPetDetailInfo::getIsSplitLodging);
        if (hasSplitLodging) {
            petDetails.forEach(petDetail -> collectTicketInfo(
                    petId,
                    petDetail.getStartDate(),
                    petDetail.getEndDate(),
                    petDetail.getStartTime(),
                    petDetail.getEndTime(),
                    petDetails.stream()
                            .map(LodgingAssignPetDetailInfo::getId)
                            .distinct()
                            .toList(),
                    null,
                    ticketPetMap));
        } else {
            var period = PetDetailUtil.calculatePeriod(petDetails);
            if (period == null) {
                return;
            }
            collectTicketInfo(
                    petId,
                    period.key().toLocalDate().toString(),
                    period.value().toLocalDate().toString(),
                    DateUtil.getMinutesOfDay(period.key()),
                    DateUtil.getMinutesOfDay(period.value()),
                    petDetails.stream()
                            .map(LodgingAssignPetDetailInfo::getId)
                            .distinct()
                            .toList(),
                    null,
                    ticketPetMap);
        }
    }

    static void handleDaycareTickets(
            Integer petId,
            List<LodgingAssignPetDetailInfo> petDetails,
            Map<String, Map<Integer, GetLodgingCalendarViewV2Result.LodgingUsage.Builder>> ticketPetMap) {
        // daycare service detail 按天拆分
        for (LodgingAssignPetDetailInfo lodgingPetDetail : petDetails) {
            if (!lodgingPetDetail.getServiceItemType().equals(ServiceItemType.DAYCARE)) {
                continue;
            }
            PetDetailDateType dateType = lodgingPetDetail.hasDateType()
                    ? lodgingPetDetail.getDateType()
                    : PetDetailDateType.PET_DETAIL_DATE_TYPE_UNSPECIFIED;
            switch (dateType) {
                case PET_DETAIL_DATE_EVERYDAY -> {
                    if (!StringUtils.hasText(lodgingPetDetail.getStartDate())
                            || !StringUtils.hasText(lodgingPetDetail.getEndDate())) {
                        continue;
                    }
                    LocalDate start = LocalDate.parse(lodgingPetDetail.getStartDate());
                    LocalDate end = LocalDate.parse(lodgingPetDetail.getEndDate());
                    for (LocalDate cur = start; !cur.isAfter(end); cur = cur.plusDays(1)) {
                        collectTicketInfo(
                                petId,
                                cur.toString(),
                                cur.toString(),
                                lodgingPetDetail.getStartTime(),
                                lodgingPetDetail.getEndTime(),
                                List.of(lodgingPetDetail.getId()),
                                null,
                                ticketPetMap);
                    }
                }
                case PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY -> {
                    if (!StringUtils.hasText(lodgingPetDetail.getStartDate())
                            || !StringUtils.hasText(lodgingPetDetail.getEndDate())) {
                        continue;
                    }
                    LocalDate start = LocalDate.parse(lodgingPetDetail.getStartDate());
                    LocalDate end = LocalDate.parse(lodgingPetDetail.getEndDate());
                    for (LocalDate cur = start; cur.isBefore(end) || cur.isEqual(end); cur = cur.plusDays(1)) {
                        collectTicketInfo(
                                petId,
                                cur.toString(),
                                cur.toString(),
                                lodgingPetDetail.getStartTime(),
                                lodgingPetDetail.getEndTime(),
                                List.of(lodgingPetDetail.getId()),
                                null,
                                ticketPetMap);
                    }
                }
                case PET_DETAIL_DATE_SPECIFIC_DATE -> {
                    for (String cur : lodgingPetDetail.getSpecificDatesList()) {
                        if (!StringUtils.hasText(cur)) {
                            continue;
                        }
                        collectTicketInfo(
                                petId,
                                cur,
                                cur,
                                lodgingPetDetail.getStartTime(),
                                lodgingPetDetail.getEndTime(),
                                List.of(lodgingPetDetail.getId()),
                                null,
                                ticketPetMap);
                    }
                }
                case PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY -> {
                    if (!StringUtils.hasText(lodgingPetDetail.getStartDate())
                            || !StringUtils.hasText(lodgingPetDetail.getEndDate())) {
                        continue;
                    }
                    LocalDate start = LocalDate.parse(lodgingPetDetail.getStartDate());
                    LocalDate end = LocalDate.parse(lodgingPetDetail.getEndDate());
                    for (LocalDate cur = start.plusDays(1);
                            cur.isBefore(end) || cur.isEqual(end);
                            cur = cur.plusDays(1)) {
                        collectTicketInfo(
                                petId,
                                cur.toString(),
                                cur.toString(),
                                lodgingPetDetail.getStartTime(),
                                lodgingPetDetail.getEndTime(),
                                List.of(lodgingPetDetail.getId()),
                                null,
                                ticketPetMap);
                    }
                }
                case PET_DETAIL_DATE_LAST_DAY -> {
                    if (!StringUtils.hasText(lodgingPetDetail.getEndDate())) {
                        continue;
                    }
                    LocalDate end = LocalDate.parse(lodgingPetDetail.getEndDate());
                    collectTicketInfo(
                            petId,
                            end.toString(),
                            end.toString(),
                            lodgingPetDetail.getStartTime(),
                            lodgingPetDetail.getEndTime(),
                            List.of(lodgingPetDetail.getId()),
                            null,
                            ticketPetMap);
                }
                case PET_DETAIL_DATE_FIRST_DAY -> {
                    if (!StringUtils.hasText(lodgingPetDetail.getStartDate())) {
                        continue;
                    }
                    LocalDate start = LocalDate.parse(lodgingPetDetail.getStartDate());
                    collectTicketInfo(
                            petId,
                            start.toString(),
                            start.toString(),
                            lodgingPetDetail.getStartTime(),
                            lodgingPetDetail.getEndTime(),
                            List.of(lodgingPetDetail.getId()),
                            null,
                            ticketPetMap);
                }
                default -> {
                    if (!StringUtils.hasText(lodgingPetDetail.getStartDate())
                            || !StringUtils.hasText(lodgingPetDetail.getEndDate())) {
                        continue;
                    }
                    collectTicketInfo(
                            petId,
                            lodgingPetDetail.getStartDate(),
                            lodgingPetDetail.getEndDate(),
                            lodgingPetDetail.getStartTime(),
                            lodgingPetDetail.getEndTime(),
                            List.of(lodgingPetDetail.getId()),
                            null,
                            ticketPetMap);
                }
            }
        }
    }

    // 对相同 lodging、相同 appointment 下一只 pet 计算 ticket
    // 一只 pet 有多个服务时，可能对应有多个 ticket。 如果不同服务存在时间完全重合的 ticket，需要合并 ticket
    private static void collectTicketInfo(
            Integer petId,
            List<LodgingAssignPetDetailInfo> petDetails,
            Map<String, Map<Integer, GetLodgingCalendarViewV2Result.LodgingUsage.Builder>> ticketPetMap) {
        boolean hasBoarding =
                petDetails.stream().anyMatch(k -> Objects.equals(k.getServiceItemType(), ServiceItemType.BOARDING));
        // 按现有业务逻辑，boarding 下的 daycare 预约，日期上不得超出 boarding 范围. 因此将 daycare、boarding
        // service 合并为一张 ticket
        if (hasBoarding) {
            handleBoardingTickets(petId, petDetails, ticketPetMap);
        } else {
            handleDaycareTickets(petId, petDetails, ticketPetMap);
        }
    }

    // 相同时间的 petDetail 需要合并
    private static void collectTicketInfo(
            Integer petId,
            String startDate,
            String endDate,
            Integer startTime,
            Integer endTime,
            List<Long> petDetailIds,
            List<Long> petEvaluationIds,
            Map<String, Map<Integer, GetLodgingCalendarViewV2Result.LodgingUsage.Builder>> ticketPetMap) {
        GetLodgingCalendarViewV2Result.LodgingUsage.Builder builder = ticketPetMap
                .computeIfAbsent(buildTicketUniqueKey(startDate, startTime, endDate, endTime), k -> new HashMap<>())
                .computeIfAbsent(petId, k -> GetLodgingCalendarViewV2Result.LodgingUsage.newBuilder()
                        .setPetId(petId));
        if (!CollectionUtils.isEmpty(petDetailIds)) {
            builder.addAllPetDetailId(petDetailIds);
        }
        if (!CollectionUtils.isEmpty(petEvaluationIds)) {
            builder.addAllPetEvaluationId(petEvaluationIds);
        }
    }

    // 相同 lodging，相同 appointment 下的 lodging view 卡片唯一标识. 用于合并相同 卡片
    public static String buildTicketUniqueKey(String startDate, Integer startTime, String endDate, Integer endTime) {
        return startDate + "_" + startTime + "_" + endDate + "_" + endTime;
    }

    private static void fillTicketTimeRange(
            GetLodgingCalendarViewV2Result.LodgingTicket.Builder ticket, String ticketUniqueKey) {
        String[] ticketTime = ticketUniqueKey.split("_");
        ticket.setStartDate(ticketTime[0]);
        ticket.setStartTime(Integer.parseInt(ticketTime[1]));
        ticket.setEndDate(ticketTime[2]);
        ticket.setEndTime(Integer.parseInt(ticketTime[3]));
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getLodgingList(GetLodgingListParams request, StreamObserver<GetLodgingListResult> responseObserver) {
        long companyId = AuthContext.get().companyId();
        long businessId = request.getBusinessId();

        if (DateUtil.countDaysBetween(request.getStartDate(), request.getEndDate()) > 61) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "The maximum number of months that can be queried is 2 months.");
        }

        List<LodgingAssignInfo> assignInfoList =
                lodgingUtil.getLodgingAssignInfo(companyId, businessId, request.getStartDate(), request.getEndDate());

        var calculateEndDate = getCalculateEndDate(companyId, request.getServiceId(), request.getEndDate());
        var petCntPerDayPerLodging =
                LodgingUtil.calPetCntPerDayPerLodging(request.getStartDate(), calculateEndDate, assignInfoList);

        // 获取 lodging 信息
        List<LodgingUnitModel> applicableLodgingUnitList = lodgingUtil.getLodgingUnit(
                companyId,
                businessId,
                request.hasServiceId() ? request.getServiceId() : null,
                request.hasEvaluationServiceId() ? request.getEvaluationServiceId() : null);
        List<LodgingUnitModel> allLodgingUnitList = lodgingUtil.getLodgingUnit(companyId, businessId, null, null);
        List<LodgingTypeModel> lodgingTypeList = lodgingUtil.getLodgingTypeByIds(allLodgingUnitList.stream()
                .map(LodgingUnitModel::getLodgingTypeId)
                .toList());
        Map<Long, LodgingTypeModel> lodgingTypeMap = lodgingTypeList.stream()
                .collect(Collectors.toMap(LodgingTypeModel::getId, Function.identity(), (k1, k2) -> k1));

        // filter lodging unit by service id
        var applicableLodgingUnitIds =
                applicableLodgingUnitList.stream().map(LodgingUnitModel::getId).collect(Collectors.toSet());
        var notApplicableLodgingUnitIds = allLodgingUnitList.stream()
                .map(LodgingUnitModel::getId)
                .filter(Predicate.not(applicableLodgingUnitIds::contains))
                .collect(Collectors.toSet());

        if (request.hasPetId()) {
            BusinessCustomerPetInfoModel pet = petDetailUtil.getPet(companyId, request.getPetId());
            if (StringUtils.hasText(pet.getWeight())) {
                Long petSizeId = petDetailUtil.getPetSizeId(companyId, pet.getWeight());
                List<LodgingTypeModel> applicableLodgingTypeList =
                        LodgingUtil.filterLodgingTypeByPetSize(lodgingTypeList, petSizeId);
                var applicableLodgingTypeIds = applicableLodgingTypeList.stream()
                        .map(LodgingTypeModel::getId)
                        .collect(Collectors.toSet());
                // filter lodging unit by pet size
                allLodgingUnitList.stream()
                        .filter(k -> !applicableLodgingTypeIds.contains(k.getLodgingTypeId()))
                        .map(LodgingUnitModel::getId)
                        .forEach(notApplicableLodgingUnitIds::add);
            }
        }

        // calculate lodging status
        Map<Long, LodgingOccupiedStatus> lodgingStatus =
                LodgingUtil.calLodgingStatusForScheduling(lodgingTypeList, allLodgingUnitList, petCntPerDayPerLodging);

        // filter lodging unit by status
        allLodgingUnitList.stream()
                .filter(k -> LodgingOccupiedStatus.FULLY_OCCUPIED == lodgingStatus.get(k.getId())
                        || (lodgingTypeMap
                                        .get(k.getLodgingTypeId())
                                        .getLodgingUnitType()
                                        .equals(LodgingUnitType.ROOM)
                                && LodgingOccupiedStatus.PARTIALLY_OCCUPIED == lodgingStatus.get(k.getId())))
                .map(LodgingUnitModel::getId)
                .forEach(notApplicableLodgingUnitIds::add);
        var lodgingReleaseMap = lodgingUtil.getUnitReleaseTime(
                companyId,
                businessId,
                request.getStartDate(),
                allLodgingUnitList.stream()
                        .map(LodgingUnitModel::getId)
                        .distinct()
                        .collect(Collectors.toList()));

        responseObserver.onNext(GetLodgingListResult.newBuilder()
                .addAllLodgingUnitList(buildGetLodgingListLodgingTypeView(
                        allLodgingUnitList,
                        lodgingTypeList,
                        lodgingStatus,
                        notApplicableLodgingUnitIds,
                        request.getIsApplicable(),
                        lodgingReleaseMap))
                .build());
        responseObserver.onCompleted();
    }

    private String getCalculateEndDate(long companyId, Long serviceId, String endDate) {
        if (!CommonUtil.isNormal(serviceId)) {
            return endDate;
        }
        // 根据 service item 以及 excludeBoardingLastDay 判断是否需要判断 endDate
        GetServiceDetailResponse serviceDetail =
                serviceManagementService.getServiceDetail(GetServiceDetailRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setServiceId(serviceId)
                        .build());
        return Objects.equals(serviceDetail.getService().getServiceItemType(), ServiceItemType.BOARDING)
                ? LocalDate.parse(endDate).minusDays(1).toString()
                : endDate;
    }

    static Map<Long, List<LodgingUnitModel>> getLodgingTypeToUnits(List<LodgingUnitModel> lodgingUnitList) {
        if (CollectionUtils.isEmpty(lodgingUnitList)) {
            return Map.of();
        }
        return lodgingUnitList.stream()
                .collect(groupingBy(
                        LodgingUnitModel::getLodgingTypeId,
                        Collectors.collectingAndThen(Collectors.toList(), units -> units.stream()
                                .sorted(Comparator.comparingInt(LodgingUnitModel::getSort))
                                .toList())));
    }

    static List<LodgingListView> buildGetLodgingListLodgingTypeView(
            final List<LodgingUnitModel> lodgingUnitList,
            final List<LodgingTypeModel> lodgingTypeList,
            final Map<Long, LodgingOccupiedStatus> lodgingStatus,
            final Set<Long> notApplicableLodgingUnitIds,
            final boolean isApplicable,
            final Map<Long, Timestamp> lodgingReleaseMap) {
        var lodgingType2Unit = getLodgingTypeToUnits(lodgingUnitList);
        List<LodgingListView> lodgingListViews = new ArrayList<>();
        lodgingTypeList.stream()
                .sorted(Comparator.comparingInt(LodgingTypeModel::getSort))
                .forEach(lodgingType -> {
                    List<LodgingUnitModel> lodgingTypeUnits = lodgingType2Unit.get(lodgingType.getId());
                    if (CollectionUtils.isEmpty(lodgingTypeUnits)) {
                        return;
                    }

                    // lodging unit 按满载程度由低到高排序
                    var lodgingListView = LodgingConverter.INSTANCE.buildGetLodgingListLodgingTypeView(
                            lodgingType,
                            lodgingTypeUnits.stream()
                                    .sorted(Comparator.comparingInt(k ->
                                            LodgingUtil.LODGING_STATUS_ORDER_MAP.get(lodgingStatus.get(k.getId()))))
                                    .toList(),
                            lodgingStatus);

                    var lodgingUnitViews = lodgingListView.getLodgingUnitsList().stream()
                            .map(v -> {
                                var builder =
                                        v.toBuilder().setIsApplicable(!notApplicableLodgingUnitIds.contains(v.getId()));
                                var releaseTime = lodgingReleaseMap.get(v.getId());
                                if (releaseTime != null) {
                                    builder.setReleaseTime(releaseTime);
                                }
                                return builder.build();
                            })
                            .toList();

                    if (isApplicable) {
                        lodgingUnitViews = lodgingUnitViews.stream()
                                .filter(LodgingUnitView::getIsApplicable)
                                .toList();
                    }

                    if (CollectionUtils.isEmpty(lodgingUnitViews)) {
                        return;
                    }

                    var builder = lodgingListView.toBuilder().clearLodgingUnits();
                    builder.addAllLodgingUnits(lodgingUnitViews);
                    lodgingListViews.add(builder.build());
                });
        return lodgingListViews;
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void lodgingTransfer(LodgingTransferParams request, StreamObserver<LodgingTransferResult> responseObserver) {
        List<LodgingUnitModel> lodgingUnitList = lodgingUtil
                .getLodgingUnitByIds(List.of(request.getLodgingIdFrom(), request.getLodgingIdTo()))
                .getLodgingUnitListList();
        if (lodgingUnitList.size() != 2) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "lodging unit not found");
        }
        if (!Objects.equals(
                lodgingUnitList.get(0).getLodgingTypeId(),
                lodgingUnitList.get(1).getLodgingTypeId())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "lodging type not match");
        }
        lodgingUtil.lodgingTransfer(
                AuthContext.get().companyId(),
                request.getBusinessId(),
                request.getLodgingIdFrom(),
                request.getLodgingIdTo());
        responseObserver.onNext(LodgingTransferResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void lodgingInUseCheck(
            LodgingInUseCheckParams request, StreamObserver<LodgingInUseCheckResult> responseObserver) {
        responseObserver.onNext(LodgingInUseCheckResult.newBuilder()
                .setUpcomingAppointments(lodgingUsingService.getUpcomingAppointmentCnt(
                        AuthContext.get().companyId(), request.getBusinessId(), request.getLodgingId()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getLastLodging(GetLastLodgingParams request, StreamObserver<GetLastLodgingResult> responseObserver) {
        long businessId = request.getBusinessId();
        long customerId = request.getCustomerId();
        long petId = request.getPetId();
        long serviceId = request.getServiceId();
        long companyId = AuthContext.get().companyId();

        Optional<PetDetailModel> lastPetDetail =
                petDetailUtil.getLastPetDetail(companyId, businessId, customerId, petId, serviceId);

        if (lastPetDetail.isEmpty() || !CommonUtil.isNormal(lastPetDetail.get().getLodgingId())) {
            responseObserver.onNext(GetLastLodgingResult.newBuilder().build());
            responseObserver.onCompleted();
            return;
        }

        // 获取 lodging 信息
        long lodgingUnitId = lastPetDetail.get().getLodgingId();
        List<LodgingUnitModel> lodgingUnitList =
                lodgingUtil.getLodgingUnitByUnitIds(companyId, businessId, List.of(lodgingUnitId));
        if (CollectionUtils.isEmpty(lodgingUnitList)) {
            responseObserver.onNext(GetLastLodgingResult.newBuilder().build());
            responseObserver.onCompleted();
            return;
        }

        List<LodgingTypeModel> lodgingTypeList = lodgingUtil.getLodgingTypeByIds(
                lodgingUnitList.stream().map(LodgingUnitModel::getLodgingTypeId).toList());

        Optional<LodgingUnitModel> lodgingUnit = lodgingUnitList.stream()
                .filter(unit -> Objects.equals(unit.getId(), lodgingUnitId))
                .findAny();
        Optional<LodgingTypeModel> lodgingType = lodgingTypeList.stream()
                .filter(type -> Objects.equals(type.getId(), lodgingUnit.get().getLodgingTypeId()))
                .findAny();

        LodgingListView lodgingListView = LodgingListView.newBuilder()
                .setLodgingTypeId(lodgingType.get().getId())
                .setLodgingTypeName(lodgingType.get().getName())
                .addLodgingUnits(LodgingUnitView.newBuilder()
                        .setId(lodgingUnitId)
                        .setName(lodgingUnit.get().getName())
                        .build())
                .build();
        responseObserver.onNext(GetLastLodgingResult.newBuilder()
                .setLastLodging(lodgingListView)
                .build());
        responseObserver.onCompleted();
    }
}
