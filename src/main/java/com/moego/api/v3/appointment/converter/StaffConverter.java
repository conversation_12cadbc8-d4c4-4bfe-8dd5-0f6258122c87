package com.moego.api.v3.appointment.converter;

import com.moego.idl.models.organization.v1.StaffBasicView;
import com.moego.idl.models.organization.v1.StaffModel;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface StaffConverter {

    StaffConverter INSTANCE = Mappers.getMapper(StaffConverter.class);

    StaffBasicView toView(StaffModel model);
}
