package com.moego.api.v3.appointment.service;

import com.moego.idl.service.appointment.v1.LodgingInUseCheckRequest;
import com.moego.idl.service.appointment.v1.LodgingServiceGrpc;
import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class LodgingUsingService {

    private final LodgingServiceGrpc.LodgingServiceBlockingStub lodgingClient;

    /**
     * 支持查询 company 下多个 business 的 lodging 使用情况
     */
    public Map<Long, List<Long>> getUpcomingAppointments(
            Long companyId, @Nullable Long businessId, List<Long> lodgingIds) {
        if (CollectionUtils.isEmpty(lodgingIds)) {
            return Map.of();
        }
        LodgingInUseCheckRequest.Builder request = LodgingInUseCheckRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllLodgingIds(lodgingIds.stream().distinct().toList());
        if (businessId != null) {
            request.setBusinessId(businessId);
        }
        return lodgingClient.lodgingInUseCheck(request.build()).getLodgingUpcomingAppointmentsMap().entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey, entry -> entry.getValue().getValuesList()));
    }

    public Integer getUpcomingAppointmentCnt(Long companyId, Long businessId, Long lodgingId) {
        return getUpcomingAppointments(companyId, businessId, List.of(lodgingId))
                .getOrDefault(lodgingId, List.of())
                .size();
    }
}
