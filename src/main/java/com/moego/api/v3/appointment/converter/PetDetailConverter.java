package com.moego.api.v3.appointment.converter;

import com.moego.idl.api.appointment.v1.AddOnCompositeOverview;
import com.moego.idl.api.appointment.v1.PetOverview;
import com.moego.idl.api.appointment.v1.ServiceCompositeOverview;
import com.moego.idl.api.appointment.v1.ServiceDetailComposite;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.GroomingServiceCalendarScheduleDef;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.lib.common.util.JsonUtil;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface PetDetailConverter {

    PetDetailConverter INSTANCE = Mappers.getMapper(PetDetailConverter.class);

    @Mapping(target = "serviceName", source = "service.name")
    @Mapping(target = "isDeleted", source = "service.isDeleted")
    @Mapping(target = "inactive", source = "service.inactive")
    @Mapping(target = "appointmentId", source = "model.groomingId")
    @Mapping(target = "serviceId", source = "model.serviceId")
    @Mapping(target = "serviceItemType", source = "model.serviceItemType")
    @Mapping(target = "priceUnit", source = "model.priceUnit")
    @Mapping(target = "id", source = "model.id")
    @Mapping(target = "specificDates", ignore = true)
    ServiceDetailComposite toComposite(
            PetDetailModel model,
            String lodgingUnitName,
            String lodgingTypeName,
            String staffName,
            ServiceBriefView service);

    @Mapping(target = "serviceName", source = "service.name")
    @Mapping(target = "appointmentId", source = "model.groomingId")
    @Mapping(target = "serviceId", source = "model.serviceId")
    @Mapping(target = "serviceItemType", source = "model.serviceItemType")
    @Mapping(target = "id", source = "model.id")
    @Mapping(target = "priceUnit", source = "service.priceUnit")
    ServiceCompositeOverview toServiceCompositeOverview(
            PetDetailModel model,
            String lodgingUnitName,
            String lodgingTypeName,
            String staffName,
            ServiceBriefView service);

    @Mapping(target = "serviceName", source = "service.name")
    @Mapping(target = "appointmentId", source = "model.groomingId")
    @Mapping(target = "serviceId", source = "model.serviceId")
    @Mapping(target = "serviceItemType", source = "model.serviceItemType")
    @Mapping(target = "id", source = "model.id")
    @Mapping(target = "specificDates", ignore = true)
    AddOnCompositeOverview toAddOnCompositeOverview(PetDetailModel model, String staffName, ServiceBriefView service);

    @AfterMapping
    default void setSpecificDate(PetDetailModel model, @MappingTarget ServiceDetailComposite.Builder composite) {
        if (StringUtils.hasText(model.getSpecificDates()) && !"[]".equals(model.getSpecificDates())) {
            String str = model.getSpecificDates().trim();
            List<String> specificDates = JsonUtil.toList(str, String.class);
            composite.addAllSpecificDates(specificDates);
        }
    }

    @AfterMapping
    default void setSpecificDate(PetDetailModel model, @MappingTarget AddOnCompositeOverview.Builder composite) {
        if (StringUtils.hasText(model.getSpecificDates()) && !"[]".equals(model.getSpecificDates())) {
            String str = model.getSpecificDates().trim();
            List<String> specificDates = JsonUtil.toList(str, String.class);
            composite.addAllSpecificDates(specificDates);
        }
    }

    @Mapping(target = "groomingId", source = "appointmentId")
    PetDetailModel evaluationToPetDetail(EvaluationServiceModel evaluation);

    GroomingServiceCalendarScheduleDef modelToScheduleDef(PetDetailModel model);

    PetOverview toPetOverview(BusinessCustomerPetModel pet);
}
