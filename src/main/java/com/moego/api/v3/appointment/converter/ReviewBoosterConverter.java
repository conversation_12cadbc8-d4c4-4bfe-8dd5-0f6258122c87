package com.moego.api.v3.appointment.converter;

import com.moego.idl.models.review_booster.v1.ReviewBoosterRecordView;
import com.moego.server.message.dto.ReviewBoosterRecordDTO;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface ReviewBoosterConverter {

    ReviewBoosterConverter INSTANCE = Mappers.getMapper(ReviewBoosterConverter.class);

    List<ReviewBoosterRecordView> toView(List<ReviewBoosterRecordDTO> record);

    ReviewBoosterRecordView toView(ReviewBoosterRecordDTO record);
}
