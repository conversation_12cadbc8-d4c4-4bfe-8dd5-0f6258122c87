package com.moego.api.v3.appointment.utils;

import com.google.common.collect.Lists;
import com.moego.api.v3.appointment.converter.PetDetailConverter;
import com.moego.api.v3.appointment.dto.LodgingInfoDTO;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.PageUtil;
import com.moego.idl.api.appointment.v1.AddOnComposite;
import com.moego.idl.api.appointment.v1.AddOnCompositeOverview;
import com.moego.idl.api.appointment.v1.EvaluationServiceComposite;
import com.moego.idl.api.appointment.v1.EvaluationServiceOverview;
import com.moego.idl.api.appointment.v1.PetVaccineComposite;
import com.moego.idl.api.appointment.v1.ServiceComposite;
import com.moego.idl.api.appointment.v1.ServiceCompositeOverview;
import com.moego.idl.api.appointment.v1.VaccineComposite;
import com.moego.idl.models.appointment.v1.BoardingSplitLodgingDetailDef;
import com.moego.idl.models.appointment.v1.BoardingSplitLodgingModel;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.LodgingAssignPetDetailInfo;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v1.ServiceOperationModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.business_customer.v1.BusinessPetIncidentReportModel;
import com.moego.idl.models.business_customer.v1.BusinessPetNoteModel;
import com.moego.idl.models.business_customer.v1.BusinessPetSizeModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRecordBindingModel;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.appointment.v1.GetLastPetDetailRequest;
import com.moego.idl.service.appointment.v1.GetPetDetailListRequest;
import com.moego.idl.service.appointment.v1.GetPetDetailListResponse;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.service.business_customer.v1.BatchGetPetIncidentReportRequest;
import com.moego.idl.service.business_customer.v1.BatchGetPetIncidentReportResponse;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BatchGetPetRequest;
import com.moego.idl.service.business_customer.v1.BatchListPetNoteRequest;
import com.moego.idl.service.business_customer.v1.BatchListPetNoteResponse;
import com.moego.idl.service.business_customer.v1.BatchListVaccineRecordRequest;
import com.moego.idl.service.business_customer.v1.BatchListVaccineRecordResponse;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetIncidentReportServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetNoteServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetSizeServiceGrpc;
import com.moego.idl.service.business_customer.v1.BusinessPetVaccineRecordServiceGrpc;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.lib.utils.model.Pair;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class PetDetailUtil {
    private final PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailService;
    private final BusinessPetVaccineRecordServiceGrpc.BusinessPetVaccineRecordServiceBlockingStub
            businessPetVaccinRecordeService;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementService;
    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub businessCustomerPetService;
    private final BusinessPetSizeServiceGrpc.BusinessPetSizeServiceBlockingStub businessPetSizeServiceClient;
    private final BusinessPetNoteServiceGrpc.BusinessPetNoteServiceBlockingStub businessPetNoteService;
    private final BusinessPetIncidentReportServiceGrpc.BusinessPetIncidentReportServiceBlockingStub
            businessPetIncidentReportStub;

    public GetPetDetailListResponse getPetDetails(long companyId, List<Long> appointmentIdList) {
        if (CollectionUtils.isEmpty(appointmentIdList)) {
            return GetPetDetailListResponse.getDefaultInstance();
        }
        return petDetailService.getPetDetailList(GetPetDetailListRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllAppointmentIds(appointmentIdList.stream().distinct().toList())
                .build());
    }

    public Map<Long, List<PetDetailModel>> getPetDetailMapByPet(long companyId, List<Long> appointmentIdList) {
        return getPetDetails(companyId, appointmentIdList).getPetDetailsList().stream()
                .collect(Collectors.groupingBy(PetDetailModel::getGroomingId));
    }

    public static List<Long> getPetDetailPetIDs(
            List<PetDetailModel> petDetailList, List<EvaluationServiceModel> petEvaluationList) {
        return Stream.of(
                        petDetailList.stream().map(PetDetailModel::getPetId),
                        petEvaluationList.stream().map(EvaluationServiceModel::getPetId))
                .flatMap(Function.identity())
                .distinct()
                .toList();
    }

    public static List<Long> getPetDetailServiceIDs(List<PetDetailModel> petDetailList) {
        if (CollectionUtils.isEmpty(petDetailList)) {
            return List.of();
        }
        return petDetailList.stream()
                .map(PetDetailModel::getServiceId)
                .filter(k -> k > 0)
                .distinct()
                .toList();
    }

    public static List<Long> getPetEvaluationIDs(List<EvaluationServiceModel> petEvaluations) {
        if (CollectionUtils.isEmpty(petEvaluations)) {
            return List.of();
        }
        return petEvaluations.stream()
                .map(EvaluationServiceModel::getServiceId)
                .filter(k -> k > 0)
                .distinct()
                .toList();
    }

    public static List<Long> getPetDetailLodgingIDs(
            List<PetDetailModel> petDetailList, List<EvaluationServiceModel> petEvaluationList) {
        List<Long> lodgingIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(petDetailList)) {
            lodgingIds.addAll(
                    petDetailList.stream().map(PetDetailModel::getLodgingId).toList());
        }
        if (!CollectionUtils.isEmpty(petEvaluationList)) {
            lodgingIds.addAll(petEvaluationList.stream()
                    .map(EvaluationServiceModel::getLodgingId)
                    .toList());
        }
        return lodgingIds.stream().filter(k -> k > 0).distinct().toList();
    }

    public static List<Long> getPetDetailLodgingIDs(
            List<PetDetailModel> petDetailList,
            List<EvaluationServiceModel> petEvaluationList,
            List<BoardingSplitLodgingModel> boardingSplitLodgingList) {
        List<Long> lodgingIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(petDetailList)) {
            lodgingIds.addAll(
                    petDetailList.stream().map(PetDetailModel::getLodgingId).toList());
        }
        if (!CollectionUtils.isEmpty(petEvaluationList)) {
            lodgingIds.addAll(petEvaluationList.stream()
                    .map(EvaluationServiceModel::getLodgingId)
                    .toList());
        }
        if (!CollectionUtils.isEmpty(boardingSplitLodgingList)) {
            lodgingIds.addAll(boardingSplitLodgingList.stream()
                    .map(BoardingSplitLodgingModel::getLodgingId)
                    .toList());
        }
        return lodgingIds.stream().filter(k -> k > 0).distinct().toList();
    }

    public static List<Long> getStaffIDs(
            List<PetDetailModel> petDetailList, List<EvaluationServiceModel> petEvaluationList) {
        List<Long> staffIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(petDetailList)) {
            staffIds.addAll(
                    petDetailList.stream().map(PetDetailModel::getStaffId).toList());
        }
        if (!CollectionUtils.isEmpty(petEvaluationList)) {
            staffIds.addAll(petEvaluationList.stream()
                    .map(EvaluationServiceModel::getStaffId)
                    .toList());
        }
        return staffIds.stream().filter(k -> k > 0).distinct().toList();
    }

    public Map<Long, List<PetVaccineComposite>> getPetVaccineMap(Collection<Long> petIdList, long companyId) {
        if (CollectionUtils.isEmpty(petIdList)) {
            return Map.of();
        }
        Map<Long, List<PetVaccineComposite>> petVaccineMap = new HashMap<>(8);
        BatchListVaccineRecordResponse batchListVaccineRecordResponse =
                businessPetVaccinRecordeService.batchListVaccineRecord(BatchListVaccineRecordRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllPetIds(petIdList)
                        .setIncludeVaccine(true)
                        .build());
        List<BusinessPetVaccineRecordBindingModel> petVaccineRecordBindingModelList =
                batchListVaccineRecordResponse.getBindingsList();
        List<BusinessPetVaccineModel> petVaccinesList = batchListVaccineRecordResponse.getVaccinesList();
        petIdList.forEach(petId -> petVaccineRecordBindingModelList.stream()
                .filter(binding -> Objects.equals(binding.getPetId(), petId))
                .map(BusinessPetVaccineRecordBindingModel::getRecordsList)
                .flatMap(List::stream)
                .forEach(vaccineRecord -> {
                    Optional<BusinessPetVaccineModel> vaccineModel = petVaccinesList.stream()
                            .filter(vaccine -> Objects.equals(vaccine.getId(), vaccineRecord.getVaccineId()))
                            .findFirst();
                    if (vaccineModel.isEmpty()) {
                        return;
                    }
                    PetVaccineComposite build = PetVaccineComposite.newBuilder()
                            .setId(vaccineRecord.getId())
                            .setVaccineId(vaccineRecord.getVaccineId())
                            .setExpirationDate(vaccineRecord.getExpirationDate())
                            .addAllDocumentUrls(vaccineRecord.getDocumentUrlsList())
                            .setVaccineName(vaccineModel.get().getName())
                            .build();
                    petVaccineMap.computeIfAbsent(petId, k -> new ArrayList<>()).add(build);
                }));
        return petVaccineMap;
    }

    public Map<Long, List<VaccineComposite>> getVaccineMap(Collection<Long> petIdList, long companyId) {
        if (CollectionUtils.isEmpty(petIdList)) {
            return Map.of();
        }

        BatchListVaccineRecordResponse batchResponse =
                businessPetVaccinRecordeService.batchListVaccineRecord(BatchListVaccineRecordRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllPetIds(petIdList)
                        .setIncludeVaccine(true)
                        .build());

        Map<Long, BusinessPetVaccineModel> vaccineModelMap = batchResponse.getVaccinesList().stream()
                .collect(Collectors.toMap(BusinessPetVaccineModel::getId, Function.identity()));

        return batchResponse.getBindingsList().stream()
                .filter(binding -> petIdList.contains(binding.getPetId()))
                .flatMap(binding -> binding.getRecordsList().stream()
                        .filter(rec -> vaccineModelMap.containsKey(rec.getVaccineId()))
                        .map(rec -> new AbstractMap.SimpleEntry<>(
                                binding.getPetId(),
                                VaccineComposite.newBuilder()
                                        .setVaccineBindingId(rec.getId())
                                        .setVaccineId(rec.getVaccineId())
                                        .setExpirationDate(rec.getExpirationDate())
                                        .addAllDocumentUrls(rec.getDocumentUrlsList())
                                        .setVaccineName(vaccineModelMap
                                                .get(rec.getVaccineId())
                                                .getName())
                                        .build())))
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey, Collectors.mapping(Map.Entry::getValue, Collectors.toList())));
    }

    public Map<Long, List<BusinessPetIncidentReportModel>> getPetIncidentReport(
            Collection<Long> petIds, long companyId) {
        if (CollectionUtils.isEmpty(petIds)) {
            return Map.of();
        }

        BatchGetPetIncidentReportResponse batchGetPetIncidentReportResponse =
                businessPetIncidentReportStub.batchGetPetIncidentReport(BatchGetPetIncidentReportRequest.newBuilder()
                        .addAllPetIds(new HashSet<>(petIds))
                        .setCompanyId(companyId)
                        .build());

        return batchGetPetIncidentReportResponse.getIncidentReportsMapMap().entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey, entry -> entry.getValue().getIncidentReportsList()));
    }

    public Map<Long, List<BusinessPetNoteModel>> getPetNoteMap(Collection<Long> petIdList) {
        if (CollectionUtils.isEmpty(petIdList)) {
            return Map.of();
        }

        BatchListPetNoteResponse response = businessPetNoteService.batchListPetNote(
                BatchListPetNoteRequest.newBuilder().addAllPetIds(petIdList).build());
        return response.getPetNotesMapMap().entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey, entry -> entry.getValue().getNotesList()));
    }

    public Map<Long, ServiceBriefView> getServiceMap(List<PetDetailModel> petDetailList, long companyId) {
        List<Long> serviceIdList = getPetDetailServiceIDs(petDetailList);
        return getServiceMap(companyId, serviceIdList);
    }

    public Map<Long, ServiceBriefView> getServiceMap(long companyId, List<Long> serviceIds) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return Map.of();
        }
        List<ServiceBriefView> serviceList = serviceManagementService
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllServiceIds(serviceIds.stream().distinct().toList())
                        .build())
                .getServicesList();
        return serviceList.stream()
                .collect(Collectors.toMap(ServiceBriefView::getId, Function.identity(), (a, b) -> a));
    }

    public Map<Long, BusinessCustomerPetInfoModel> getPetInfoMap(
            List<PetDetailModel> petDetailList, List<EvaluationServiceModel> petEvaluationList, long companyId) {
        List<Long> petIdList = getPetDetailPetIDs(petDetailList, petEvaluationList);
        if (CollectionUtils.isEmpty(petIdList)) {
            return Map.of();
        }
        return PageUtil.mapAll(petIdList, p -> businessCustomerPetService
                        .batchGetPetInfo(BatchGetPetInfoRequest.newBuilder()
                                .setTenant(Tenant.newBuilder()
                                        .setCompanyId(companyId)
                                        .build())
                                .addAllIds(p)
                                .build())
                        .getPetsList())
                .stream()
                .collect(Collectors.toMap(BusinessCustomerPetInfoModel::getId, Function.identity()));
    }

    // deprecated
    public Map<Long, BusinessCustomerPetModel> getPetMap(
            List<PetDetailModel> petDetailList, List<EvaluationServiceModel> petEvaluationList, long companyId) {
        List<Long> petIdList = getPetDetailPetIDs(petDetailList, petEvaluationList);
        if (CollectionUtils.isEmpty(petIdList)) {
            return Map.of();
        }
        return businessCustomerPetService
                .batchGetPet(BatchGetPetRequest.newBuilder()
                        .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                        .addAllIds(petIdList)
                        .build())
                .getPetsList()
                .stream()
                .collect(Collectors.toMap(BusinessCustomerPetModel::getId, Function.identity()));
    }

    public Map<Long, BusinessCustomerPetInfoModel> getPetMap(long companyId, List<Long> petIdList) {
        if (CollectionUtils.isEmpty(petIdList)) {
            return Map.of();
        }

        var petIds = petIdList.stream().distinct().toList();

        return Lists.partition(petIds, 500).stream()
                .map(ids -> CompletableFuture.supplyAsync(
                        () -> businessCustomerPetService
                                .batchGetPetInfo(BatchGetPetInfoRequest.newBuilder()
                                        .setTenant(Tenant.newBuilder()
                                                .setCompanyId(companyId)
                                                .build())
                                        .addAllIds(ids)
                                        .build())
                                .getPetsList(),
                        ThreadPool.getSubmitExecutor()))
                .map(CompletableFuture::join)
                .flatMap(List::stream)
                .collect(Collectors.toMap(BusinessCustomerPetInfoModel::getId, Function.identity(), (o, n) -> o));
    }

    public BusinessCustomerPetInfoModel getPet(long companyId, Long petId) {
        return getPetMap(companyId, List.of(petId)).get(petId);
    }

    public Long getPetSizeId(Long companyId, String weight) {
        var petSizeList = businessPetSizeServiceClient.listPetSize(
                com.moego.idl.service.business_customer.v1.ListPetSizeRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build());
        return getPetSizeId(weight, petSizeList.getSizesList());
    }

    public static Long getPetSizeId(String weight, List<BusinessPetSizeModel> petSizeList) {
        if (!StringUtils.hasText(weight)) {
            return null;
        }
        BigDecimal weightDecimal;
        try {
            weightDecimal = new BigDecimal(weight);
        } catch (NumberFormatException e) {
            return null;
        }

        var roundedWeight = weightDecimal.setScale(0, RoundingMode.HALF_UP).intValue();
        BusinessPetSizeModel targetSize = petSizeList.stream()
                .filter(size -> size.getWeightLow() <= roundedWeight && size.getWeightHigh() >= roundedWeight)
                .findFirst()
                .orElse(null);
        if (targetSize == null) {
            return null;
        }
        return targetSize.getId();
    }

    /**
     * Build LocalDateTime based on date and minute of the day
     * If the minute of day exceeds 1440, it is added to the date
     *
     * @param date        yyyy-MM-dd
     * @param minuteOfDay minutes of the day
     * @return LocalDateTime
     */
    public static LocalDateTime buildDateTime(String date, Integer minuteOfDay) {
        LocalDate localDate = LocalDate.parse(date);
        if (minuteOfDay < 0) {
            int daysToSubtract = Math.abs(minuteOfDay) / 1440 + 1;
            localDate = localDate.minusDays(daysToSubtract);
            minuteOfDay = 1440 - Math.abs(minuteOfDay) % 1440;
        } else if (minuteOfDay >= 1440) {
            int daysToAdd = minuteOfDay / 1440;
            localDate = localDate.plusDays(daysToAdd);
            minuteOfDay = minuteOfDay % 1440;
        }
        return LocalDateTime.of(localDate, LocalTime.ofSecondOfDay(minuteOfDay * 60));
    }

    // 计算起始结束时间
    public static Pair<LocalDateTime, LocalDateTime> calculatePeriod(
            List<LodgingAssignPetDetailInfo> lodgingPetDetail) {
        LocalDateTime start = lodgingPetDetail.stream()
                .filter(k -> StringUtils.hasText(k.getStartDate()))
                .map(k -> buildDateTime(k.getStartDate(), k.getStartTime()))
                .min(Comparator.naturalOrder())
                .orElse(null);
        LocalDateTime end = lodgingPetDetail.stream()
                .filter(k -> StringUtils.hasText(k.getEndDate()))
                .map(k -> buildDateTime(k.getEndDate(), k.getEndTime()))
                .max(Comparator.naturalOrder())
                .orElse(null);
        if (start == null || end == null) {
            return null;
        }
        return Pair.of(start, end);
    }

    public static ServiceComposite buildServiceComposite(
            PetDetailModel petDetail,
            String lodgingUnitName,
            String lodgingTypeName,
            StaffModel staff,
            ServiceBriefView service,
            List<ServiceOperationModel> operations,
            List<BoardingSplitLodgingDetailDef> splitLodgingModels,
            final boolean isSlotFreeService) {
        return ServiceComposite.newBuilder()
                .setServiceDetail(PetDetailConverter.INSTANCE.toComposite(
                        petDetail,
                        lodgingUnitName,
                        lodgingTypeName,
                        staff == null ? "" : staff.getFirstName() + " " + staff.getLastName(),
                        service == null ? ServiceBriefView.getDefaultInstance() : service))
                .addAllOperations(operations == null ? List.of() : operations)
                .addAllSplitLodgings(splitLodgingModels)
                .setIsSlotFreeService(isSlotFreeService)
                .build();
    }

    public static AddOnComposite buildAddOnComposite(
            PetDetailModel petDetail,
            StaffModel staff,
            ServiceBriefView service,
            List<ServiceOperationModel> operations) {
        return AddOnComposite.newBuilder()
                .setServiceDetail(PetDetailConverter.INSTANCE.toComposite(
                        petDetail,
                        "",
                        "",
                        staff == null ? "" : staff.getFirstName() + " " + staff.getLastName(),
                        service == null ? ServiceBriefView.getDefaultInstance() : service))
                .addAllOperations(operations == null ? List.of() : operations)
                .build();
    }

    public static EvaluationServiceComposite buildPetEvaluationComposite(
            EvaluationServiceModel evaluationServiceModel,
            Map<Long, EvaluationBriefView> evaluationMap,
            Map<Long, StaffModel> staffMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap) {
        var builder = EvaluationServiceComposite.newBuilder()
                .setId(evaluationServiceModel.getId())
                .setAppointmentId(evaluationServiceModel.getAppointmentId())
                .setPetId(evaluationServiceModel.getPetId())
                .setServiceId(evaluationServiceModel.getServiceId())
                .setServiceTime(evaluationServiceModel.getServiceTime())
                .setServicePrice(evaluationServiceModel.getServicePrice())
                .setStartDate(evaluationServiceModel.getStartDate())
                .setStartTime(evaluationServiceModel.getStartTime())
                .setEndTime(evaluationServiceModel.getEndTime())
                .setEndDate(evaluationServiceModel.getEndDate())
                .setServiceItemType(ServiceItemType.EVALUATION);
        if (evaluationServiceModel.hasStaffId()) {
            builder.setStaffId(evaluationServiceModel.getStaffId());
            StaffModel staff = staffMap.get(evaluationServiceModel.getStaffId());
            if (staff != null) {
                builder.setStaffName(staff.getFirstName() + " " + staff.getLastName());
            }
        }
        EvaluationBriefView evaluation = evaluationMap.get(evaluationServiceModel.getServiceId());
        if (evaluation != null) {
            builder.setServiceName(evaluation.getName());
        }
        if (evaluationServiceModel.hasLodgingId()) {
            builder.setLodgingId(evaluationServiceModel.getLodgingId());
            LodgingUnitModel lodgingUnitModel = lodgingUnitMap.get(evaluationServiceModel.getLodgingId());
            if (lodgingUnitModel != null) {
                builder.setLodgingUnitName(lodgingUnitModel.getName());
                LodgingTypeModel lodgingType = lodgingTypeMap.get(lodgingUnitModel.getLodgingTypeId());
                if (lodgingType != null) {
                    builder.setLodgingTypeName(lodgingType.getName());
                }
            }
        }
        return builder.build();
    }

    public static ServiceCompositeOverview buildServiceCompositeOverview(
            PetDetailModel petDetail,
            LodgingInfoDTO lodgingInfo,
            StaffModel staff,
            ServiceBriefView service,
            List<BoardingSplitLodgingDetailDef> splitLodgingDetailDefs) {
        var lodgingUnitName = lodgingInfo.getLodgingUnitName();
        var lodgingTypeName = lodgingInfo.getLodgingTypeName();
        var serviceCompositeOverview = PetDetailConverter.INSTANCE.toServiceCompositeOverview(
                petDetail,
                lodgingUnitName,
                lodgingTypeName,
                staff == null
                                || List.of(ServiceItemType.BOARDING, ServiceItemType.DAYCARE)
                                        .contains(petDetail.getServiceItemType())
                        ? ""
                        : staff.getFirstName() + " " + staff.getLastName(),
                service == null ? ServiceBriefView.getDefaultInstance() : service);

        if (!CollectionUtils.isEmpty(splitLodgingDetailDefs)) {
            serviceCompositeOverview = serviceCompositeOverview.toBuilder()
                    .addAllLodgingInfos(splitLodgingDetailDefs.stream()
                            .map(def -> ServiceCompositeOverview.LodgingInfo.newBuilder()
                                    .setLodgingUnitName(def.getLodgingUnitName())
                                    .setLodgingTypeName(def.getLodgingTypeName())
                                    .build())
                            .toList())
                    .build();
        }

        if (CommonUtil.isNormal(lodgingInfo.getLodgingUnitId())) {
            serviceCompositeOverview = serviceCompositeOverview.toBuilder()
                    .setLodgingId(lodgingInfo.getLodgingUnitId())
                    .build();
        }
        return serviceCompositeOverview;
    }

    public static AddOnCompositeOverview buildAddOnCompositeOverview(
            PetDetailModel petDetail, StaffModel staff, ServiceBriefView service) {
        return PetDetailConverter.INSTANCE.toAddOnCompositeOverview(
                petDetail,
                staff == null ? "" : staff.getFirstName() + " " + staff.getLastName(),
                service == null ? ServiceBriefView.getDefaultInstance() : service);
    }

    public static EvaluationServiceOverview buildEvaluationServiceOverview(
            EvaluationServiceModel evaluationServiceModel,
            Map<Long, EvaluationBriefView> evaluationMap,
            Map<Long, StaffModel> staffMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap) {
        EvaluationServiceOverview.Builder builder = EvaluationServiceOverview.newBuilder()
                .setId(evaluationServiceModel.getId())
                .setAppointmentId(evaluationServiceModel.getAppointmentId())
                .setPetId(evaluationServiceModel.getPetId())
                .setServiceId(evaluationServiceModel.getServiceId())
                .setStartDate(evaluationServiceModel.getStartDate())
                .setEndDate(evaluationServiceModel.getEndDate())
                .setStartTime(evaluationServiceModel.getStartTime())
                .setEndTime(evaluationServiceModel.getEndTime())
                .setServiceItemType(ServiceItemType.EVALUATION);
        if (evaluationServiceModel.hasStaffId()) {
            builder.setStaffId(evaluationServiceModel.getStaffId());
            StaffModel staff = staffMap.get(evaluationServiceModel.getStaffId());
            if (staff != null) {
                builder.setStaffName(staff.getFirstName() + " " + staff.getLastName());
            }
        }
        EvaluationBriefView evaluation = evaluationMap.get(evaluationServiceModel.getServiceId());
        if (evaluation != null) {
            builder.setServiceName(evaluation.getName());
        }
        if (evaluationServiceModel.hasLodgingId()) {
            builder.setLodgingId(evaluationServiceModel.getLodgingId());
            LodgingUnitModel lodgingUnitModel = lodgingUnitMap.get(evaluationServiceModel.getLodgingId());
            if (lodgingUnitModel != null) {
                builder.setLodgingUnitName(lodgingUnitModel.getName());
                LodgingTypeModel lodgingType = lodgingTypeMap.get(lodgingUnitModel.getLodgingTypeId());
                if (lodgingType != null) {
                    builder.setLodgingTypeName(lodgingType.getName());
                }
            }
        }
        return builder.build();
    }

    public Optional<PetDetailModel> getLastPetDetail(
            long companyId, long businessId, long customerId, long petId, long serviceId) {
        return petDetailService
                .getLastPetDetail(GetLastPetDetailRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addCustomerId(customerId)
                        .addPetId(petId)
                        .setFilter(GetLastPetDetailRequest.Filter.newBuilder()
                                .setBusinessId(businessId)
                                .addServiceIds(serviceId)
                                .build())
                        .build())
                .getPetDetailsList()
                .stream()
                .filter(p -> Objects.equals(p.getPetId(), petId) && Objects.equals(p.getServiceId(), serviceId))
                .findAny();
    }

    public static Map<Long, List<Long>> getPetAppointments(
            List<PetDetailModel> petDetails, List<EvaluationServiceModel> petEvaluations) {
        Map<Long, Set<Long>> result = new HashMap<>();
        if (!CollectionUtils.isEmpty(petDetails)) {
            for (var detail : petDetails) {
                result.computeIfAbsent(detail.getPetId(), k -> new HashSet<>()).add(detail.getGroomingId());
            }
        }
        if (!CollectionUtils.isEmpty(petEvaluations)) {
            for (var evaluation : petEvaluations) {
                result.computeIfAbsent(evaluation.getPetId(), k -> new HashSet<>())
                        .add(evaluation.getAppointmentId());
            }
        }
        return result.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> new ArrayList<>(entry.getValue())));
    }

    public static boolean isContainDate(String targetDate, String startDate, String endDate, String specificDates) {
        var dates = getSpecificDates(specificDates);
        if (!CollectionUtils.isEmpty(dates)) {
            return dates.contains(targetDate);
        }
        return DateUtil.generateAllDatesBetween(startDate, endDate).contains(targetDate);
    }

    public static List<String> getSpecificDates(String specificDates) {
        List<String> result = new ArrayList<>();
        if (StringUtils.hasText(specificDates)) {
            result = JsonUtil.toList(specificDates, String.class);
        }
        return result;
    }
}
