package com.moego.api.v3.appointment.controller;

import com.google.type.Date;
import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.api.v3.appointment.converter.PrintCardConverter;
import com.moego.api.v3.appointment.service.AppointmentService;
import com.moego.api.v3.appointment.service.AppointmentTaskService;
import com.moego.api.v3.appointment.service.CompanySettingService;
import com.moego.api.v3.appointment.service.LodgingUsingService;
import com.moego.api.v3.appointment.service.PetBelongingService;
import com.moego.api.v3.appointment.service.PetDetailService;
import com.moego.api.v3.appointment.utils.FutureService;
import com.moego.api.v3.appointment.utils.LodgingUtil;
import com.moego.api.v3.appointment.utils.PetDetailUtil;
import com.moego.api.v3.business_customer.service.BusinessCustomerService;
import com.moego.api.v3.business_customer.service.BusinessPetCodeService;
import com.moego.api.v3.business_customer.service.BusinessPetService;
import com.moego.api.v3.offering.service.EvaluationService;
import com.moego.api.v3.offering.service.LodgingService;
import com.moego.api.v3.offering.service.ServiceService;
import com.moego.api.v3.organization.service.StaffService;
import com.moego.idl.api.appointment.v1.ListAppointmentCardParams;
import com.moego.idl.api.appointment.v1.ListAppointmentCardResult;
import com.moego.idl.api.appointment.v1.ListBoardingArrivalCardParams;
import com.moego.idl.api.appointment.v1.ListBoardingArrivalCardResult;
import com.moego.idl.api.appointment.v1.ListBoardingDepartureCardParams;
import com.moego.idl.api.appointment.v1.ListBoardingDepartureCardResult;
import com.moego.idl.api.appointment.v1.ListDailyPlaygroupCardParams;
import com.moego.idl.api.appointment.v1.ListDailyPlaygroupCardResult;
import com.moego.idl.api.appointment.v1.ListDailyTasksParams;
import com.moego.idl.api.appointment.v1.ListDailyTasksResult;
import com.moego.idl.api.appointment.v1.PrintCardServiceGrpc;
import com.moego.idl.api.appointment.v1.ServiceFilter;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.AppointmentTaskCategory;
import com.moego.idl.models.appointment.v1.AppointmentTaskModel;
import com.moego.idl.models.appointment.v1.BoardingSplitLodgingDetailDef;
import com.moego.idl.models.appointment.v1.BoardingSplitLodgingModel;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v1.PetPlaygroupModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.PlaygroupModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.AppointmentTaskServiceGrpc;
import com.moego.idl.service.appointment.v1.BoardingSplitLodgingServiceGrpc;
import com.moego.idl.service.appointment.v1.GetAppointmentListRequest;
import com.moego.idl.service.appointment.v1.ListBoardingSplitLodgingsRequest;
import com.moego.idl.service.appointment.v1.ListPetPlaygroupRequest;
import com.moego.idl.service.appointment.v1.PetPlaygroupServiceGrpc;
import com.moego.idl.service.offering.v1.ListPlaygroupRequest;
import com.moego.idl.service.offering.v1.PlaygroupServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.thread.ThreadPool;
import io.grpc.stub.StreamObserver;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2024/1/30
 */
@Service
@RequiredArgsConstructor
public class PrintCardController extends PrintCardServiceGrpc.PrintCardServiceImplBase {

    private final StaffService staffService;
    private final AppointmentService appointmentService;
    private final ServiceService serviceService;
    private final EvaluationService evaluationService;
    private final LodgingService lodgingService;
    private final PetDetailService petDetailService;
    private final PetDetailUtil petDetailUtil;
    private final BusinessPetService businessPetService;
    private final BusinessPetCodeService petCodeService;
    private final AppointmentTaskServiceGrpc.AppointmentTaskServiceBlockingStub appointmentTaskStub;
    private final CompanySettingService companySettingService;
    private final AppointmentTaskService appointmentTaskService;
    private final BusinessCustomerService businessCustomerService;
    private final LodgingUtil lodgingUtil;
    private final PetBelongingService petBelongingService;
    private final PetPlaygroupServiceGrpc.PetPlaygroupServiceBlockingStub petPlaygroupServiceBlockingStub;
    private final PlaygroupServiceGrpc.PlaygroupServiceBlockingStub playgroupServiceBlockingStub;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentServiceBlockingStub;
    private final FutureService futureService;
    private final BoardingSplitLodgingServiceGrpc.BoardingSplitLodgingServiceBlockingStub boardingSplitLodgingService;
    private final BusinessPetCodeService businessPetCodeService;
    private final LodgingUsingService lodgingUsingService;

    private static List<ServiceItemType> buildServiceItemTypes(List<ServiceItemType> requests) {
        if (CollectionUtils.isEmpty(requests)) {
            return List.of(
                    ServiceItemType.BOARDING,
                    ServiceItemType.DAYCARE,
                    ServiceItemType.GROOMING,
                    ServiceItemType.EVALUATION,
                    ServiceItemType.DOG_WALKING);
        }
        return requests;
    }

    private boolean filterService(List<ServiceFilter> serviceFilters, PetDetailModel petDetailModel) {
        // 没传 service filters，默认为 true
        if (CollectionUtils.isEmpty(serviceFilters)) {
            return true;
        }

        ServiceFilter serviceFilter = serviceFilters.stream()
                .filter(e -> e.getServiceItemType().equals(petDetailModel.getServiceItemType()))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(serviceFilter)) {
            return false;
        }

        return serviceFilter.getIsAllService()
                || serviceFilter.getServiceIdsList().contains(petDetailModel.getServiceId());
    }

    private boolean filterService(List<ServiceFilter> serviceFilters, EvaluationServiceModel evaluationServiceModel) {
        // 没传 service filters，默认为 true
        if (CollectionUtils.isEmpty(serviceFilters)) {
            return true;
        }

        ServiceFilter serviceFilter = serviceFilters.stream()
                .filter(e -> e.getServiceItemType().equals(ServiceItemType.EVALUATION))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(serviceFilter)) {
            return false;
        }

        return serviceFilter.getIsAllService()
                || serviceFilter.getServiceIdsList().contains(evaluationServiceModel.getServiceId());
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listAppointmentCard(
            ListAppointmentCardParams request, StreamObserver<ListAppointmentCardResult> responseObserver) {
        long companyId = AuthContext.get().companyId();
        LocalDate date = DateTimeConverter.INSTANCE.toLocalDate(request.getDate());
        String dateStr = date.toString();
        List<ServiceFilter> serviceFiltersList = request.getServiceFiltersList();
        var serviceItemTypes = buildServiceItemTypes(request.getTypesList());

        // appointment
        var appointmentModelMap = appointmentService
                .listAppointmentPrintCardList(
                        companyId,
                        request.getBusinessId(),
                        date,
                        serviceItemTypes,
                        request.getIsPetsCheckedInOnly()
                                ? List.of(AppointmentStatus.CHECKED_IN)
                                : AppointmentService.ACTIVE_STATUS_SET)
                .stream()
                .collect(Collectors.toMap(AppointmentModel::getId, x -> x, (x, y) -> x));
        var appointmentIdList = appointmentModelMap.keySet().stream().toList();
        var appointmentNotes = appointmentService.getAppointmentNotes(companyId, appointmentIdList);

        // pet detail
        var petDetailResp = petDetailService.getPetDetailsWithActualDates(companyId, appointmentIdList);
        var allPetDetails = petDetailResp.getPetDetailsList();
        var allPetEvaluations = petDetailResp.getPetEvaluationsList();
        var appointmentPetDetails =
                allPetDetails.stream().collect(Collectors.groupingBy(PetDetailModel::getGroomingId));
        var appointmentPetEvaluations =
                allPetEvaluations.stream().collect(Collectors.groupingBy(EvaluationServiceModel::getAppointmentId));

        // pet
        var petIds = PetDetailUtil.getPetDetailPetIDs(allPetDetails, allPetEvaluations);
        var petMap = petDetailUtil.getPetMap(allPetDetails, allPetEvaluations, companyId);
        var petAppointments = PetDetailUtil.getPetAppointments(allPetDetails, allPetEvaluations);
        var petCodes = petCodeService.getPetCodeIdsMap(companyId, petIds);
        var petNotes = businessPetService.getPetNoteDTOs(petIds);
        // customer, id -> customer
        var customerIds = petMap.values().stream()
                .map(BusinessCustomerPetModel::getCustomerId)
                .distinct()
                .toList();
        var customerMap = businessCustomerService.listBusinessCustomerInfos(customerIds).stream()
                .collect(Collectors.toMap(
                        BusinessCustomerInfoModel::getId, customer -> customer, (existing, replacement) -> existing));

        // pet incident report
        List<Long> petIdList = PetDetailUtil.getPetDetailPetIDs(
                petDetailResp.getPetDetailsList(), petDetailResp.getPetEvaluationsList());
        var petIncidentReportMap = petDetailUtil.getPetIncidentReport(petIdList, companyId);

        // service
        var serviceMap = serviceService.getServiceMap(companyId, PetDetailUtil.getPetDetailServiceIDs(allPetDetails));
        var evaluationMap =
                evaluationService.getEvaluationMapByIds(PetDetailUtil.getPetEvaluationIDs(allPetEvaluations));

        // staff
        var staffMap = staffService.getStaffMap(PetDetailUtil.getStaffIDs(allPetDetails, allPetEvaluations));

        // boarding split lodgings
        var boardingSplitLodgings = boardingSplitLodgingService
                .listBoardingSplitLodgings(ListBoardingSplitLodgingsRequest.newBuilder()
                        .addAllAppointmentIds(appointmentIdList)
                        .build())
                .getBoardingSplitLodgingsList();

        var boardingSplitLodgingMap = boardingSplitLodgings.stream()
                .collect(Collectors.groupingBy(BoardingSplitLodgingModel::getAppointmentId));

        // lodging
        var lodgingMap = lodgingService.getLodgingMap(
                PetDetailUtil.getPetDetailLodgingIDs(allPetDetails, allPetEvaluations, boardingSplitLodgings));
        var lodgingUnitMap = lodgingMap.getKey();
        var lodgingTypeMap = lodgingMap.getValue();

        var assignInfoList = lodgingUtil.getLodgingAssignInfo(companyId, request.getBusinessId(), dateStr, dateStr);
        var calPetCntPerDayPerLodging = LodgingUtil.calPetCntPerDayPerLodging(dateStr, dateStr, assignInfoList);
        var petCodeBindings = businessPetCodeService.listPetCodeBindings(companyId, petIds);
        // 包含所有的 bd view 数据，可能用来构造 其它 view 数据，比如 grooming，不能返回给前端
        List<ListAppointmentCardResult.BoardingView> boardingViewsAll = new ArrayList<>();
        List<ListAppointmentCardResult.DaycareView> daycareViewsAll = new ArrayList<>();
        // 基于 filter 筛选出来的 view 数据，用来返回给前端
        List<ListAppointmentCardResult.BoardingView> boardingViews = new ArrayList<>();
        List<ListAppointmentCardResult.DaycareView> daycareViews = new ArrayList<>();
        List<ListAppointmentCardResult.EvaluationView> evaluationViews = new ArrayList<>();
        List<ListAppointmentCardResult.GroomingView> groomingViews = new ArrayList<>();
        List<ListAppointmentCardResult.DogWalkingView> dogWalkingViews = new ArrayList<>();

        // 计算当天每个 lodging type 的 pet 数量
        // 这里需要从 db 获取一遍 lodging unit，因为 lodgingMap 中只包含了当天有 pet 的 lodging unit
        Map<Long, List<LodgingUnitModel>> lodgingUnitByLodgingTypes =
                lodgingService
                        .getLodgingUnit(
                                companyId,
                                request.getBusinessId(),
                                lodgingTypeMap.keySet().stream().toList())
                        .stream()
                        .collect(Collectors.groupingBy(LodgingUnitModel::getLodgingTypeId));
        List<ListAppointmentCardResult.LodgingTypeDayView> lodgingTypeDayViews =
                PrintCardConverter.INSTANCE.toAppointmentCardPetLodgingTypeDayView(
                        dateStr, calPetCntPerDayPerLodging, lodgingUnitByLodgingTypes, lodgingTypeMap);
        List<LodgingUnitModel> lodgingUnitModels =
                lodgingMap.getKey().values().stream().toList();
        var petsSorted = petMap.values().stream()
                .sorted(Comparator.comparing(BusinessCustomerPetModel::getPetName, String.CASE_INSENSITIVE_ORDER))
                .toList();
        for (var pet : petsSorted) {
            for (var appointmentId : petAppointments.getOrDefault(pet.getId(), List.of())) {
                var appointment = appointmentModelMap.get(appointmentId);
                if (appointment == null) {
                    return;
                }

                var serviceItemTypeToPetDetails = appointmentPetDetails.getOrDefault(appointmentId, List.of()).stream()
                        .filter(k -> k.getPetId() == pet.getId())
                        .filter(k -> PetDetailUtil.isContainDate(
                                date.toString(), k.getStartDate(), k.getEndDate(), k.getSpecificDates()))
                        .collect(Collectors.groupingBy(PetDetailModel::getServiceItemType));
                var petEvaluations = appointmentPetEvaluations.getOrDefault(appointmentId, List.of()).stream()
                        .filter(k -> k.getPetId() == pet.getId())
                        .filter(k -> PetDetailUtil.isContainDate(date.toString(), k.getStartDate(), k.getEndDate(), ""))
                        .filter(k -> filterService(serviceFiltersList, k))
                        .toList();

                var boardingServicesAll =
                        serviceItemTypeToPetDetails.getOrDefault(ServiceItemType.BOARDING, List.of()).stream()
                                .filter(k -> Objects.equals(k.getServiceType(), ServiceType.SERVICE))
                                .toList();

                if (!boardingServicesAll.isEmpty() && serviceItemTypes.contains(ServiceItemType.BOARDING)) {
                    // 精确过滤后的
                    var boardingServices = boardingServicesAll.stream()
                            .filter(k -> filterService(serviceFiltersList, k))
                            .toList();

                    if (!boardingServices.isEmpty()) {
                        boardingViews.add(buildBoardingView(
                                date,
                                appointmentId,
                                pet.getId(),
                                boardingServices,
                                serviceMap,
                                lodgingUnitMap,
                                lodgingTypeMap,
                                boardingSplitLodgingMap));
                    }

                    // 全部的
                    boardingViewsAll.add(buildBoardingView(
                            date,
                            appointmentId,
                            pet.getId(),
                            boardingServicesAll,
                            serviceMap,
                            lodgingUnitMap,
                            lodgingTypeMap,
                            boardingSplitLodgingMap));
                }

                var daycareServicesAll =
                        serviceItemTypeToPetDetails.getOrDefault(ServiceItemType.DAYCARE, List.of()).stream()
                                .filter(k -> Objects.equals(k.getServiceType(), ServiceType.SERVICE))
                                .toList();
                if (!daycareServicesAll.isEmpty() && serviceItemTypes.contains(ServiceItemType.DAYCARE)) {
                    // 精确过滤后的
                    var daycareServices = daycareServicesAll.stream()
                            .filter(k -> filterService(serviceFiltersList, k))
                            .toList();

                    if (!daycareServices.isEmpty()) {
                        daycareViews.add(PrintCardConverter.INSTANCE.toAppointmentCardPetDaycareView(
                                appointmentId,
                                pet.getId(),
                                daycareServices,
                                serviceMap,
                                lodgingUnitMap,
                                lodgingTypeMap));
                    }

                    // 全部的
                    daycareViewsAll.add(PrintCardConverter.INSTANCE.toAppointmentCardPetDaycareView(
                            appointmentId,
                            pet.getId(),
                            daycareServicesAll,
                            serviceMap,
                            lodgingUnitMap,
                            lodgingTypeMap));
                }

                var groomingDetails =
                        serviceItemTypeToPetDetails.getOrDefault(ServiceItemType.GROOMING, List.of()).stream()
                                .filter(k -> filterService(serviceFiltersList, k))
                                .toList();
                if (!CollectionUtils.isEmpty(groomingDetails) && serviceItemTypes.contains(ServiceItemType.GROOMING)) {
                    var appointmentCardPetGroomingView = PrintCardConverter.INSTANCE.toAppointmentCardPetGroomingView(
                            appointmentId,
                            pet.getId(),
                            groomingDetails,
                            serviceMap,
                            staffMap,
                            lodgingUnitMap,
                            lodgingTypeMap);
                    // 将 lodging 信息，从 boarding 或 daycare 中获取，补充到 grooming view 中
                    if (!CollectionUtils.isEmpty(daycareViewsAll)) {
                        appointmentCardPetGroomingView =
                                extractGroomingViewFromDaycareViews(appointmentCardPetGroomingView, daycareViewsAll);
                    }
                    if (!CollectionUtils.isEmpty(boardingViewsAll)) {
                        appointmentCardPetGroomingView =
                                extractGroomingViewFromBoardingViews(appointmentCardPetGroomingView, boardingViewsAll);
                    }
                    groomingViews.add(appointmentCardPetGroomingView);
                }

                if (!CollectionUtils.isEmpty(petEvaluations) && serviceItemTypes.contains(ServiceItemType.EVALUATION)) {
                    evaluationViews.add(PrintCardConverter.INSTANCE.toAppointmentCardPetEvaluationView(
                            appointmentId, pet.getId(), petEvaluations, evaluationMap, lodgingUnitMap, lodgingTypeMap));
                }

                var dogWalkingDetails =
                        serviceItemTypeToPetDetails.getOrDefault(ServiceItemType.DOG_WALKING, List.of()).stream()
                                .filter(k -> filterService(serviceFiltersList, k))
                                .toList();
                if (!CollectionUtils.isEmpty(dogWalkingDetails)
                        && serviceItemTypes.contains(ServiceItemType.DOG_WALKING)) {
                    dogWalkingViews.add(PrintCardConverter.INSTANCE.toAppointmentCardPetDogWalkingView(
                            appointmentId, pet.getId(), dogWalkingDetails, serviceMap, staffMap));
                }
            }
        }

        responseObserver.onNext(ListAppointmentCardResult.newBuilder()
                .addAllBoardings(boardingViews)
                .addAllDaycares(daycareViews)
                .addAllGroomings(groomingViews)
                .addAllEvaluations(evaluationViews)
                .addAllDogWalkings(dogWalkingViews)
                .addAllPets(petMap.values().stream()
                        .map(k -> PrintCardConverter.INSTANCE.toAppointmentCardPetView(
                                k, customerMap, petCodes, petNotes, petCodeBindings, petIncidentReportMap))
                        .toList())
                .addAllAppointments(appointmentModelMap.values().stream()
                        .map(k -> PrintCardConverter.INSTANCE.toAppointmentCardAppointmentView(k, appointmentNotes))
                        .toList())
                .addAllLodgingTypes(lodgingTypeDayViews)
                .addAllLodgingUnits(lodgingUnitModels)
                .build());
        responseObserver.onCompleted();
    }

    public static ListAppointmentCardResult.BoardingView buildBoardingView(
            LocalDate date,
            Long appointmentId,
            Long petId,
            List<PetDetailModel> boardingServices,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap,
            Map<Long, List<BoardingSplitLodgingModel>> boardingSplitLodgingMap) {
        var appointmentCardPetBoardingView = PrintCardConverter.INSTANCE.toAppointmentCardPetBoardingView(
                appointmentId, petId, boardingServices, serviceMap, lodgingUnitMap, lodgingTypeMap);

        if (!CollectionUtils.isEmpty(boardingSplitLodgingMap) && boardingSplitLodgingMap.containsKey(appointmentId)) {
            var petServiceViews = appointmentCardPetBoardingView.getPetServicesList().stream()
                    .map(petServiceView -> {
                        // boarding
                        var builder = petServiceView.toBuilder();

                        var boardingSplitLodgingModels = LodgingUtil.filterBoardingSplitLodgings(
                                boardingSplitLodgingMap, appointmentId, petServiceView.getPetServiceId(), date, null);
                        var splitLodgingDetailDefs = LodgingUtil.getBoardingSplitLodgingDefs(
                                lodgingUnitMap, lodgingTypeMap, boardingSplitLodgingModels);

                        if (!CollectionUtils.isEmpty(splitLodgingDetailDefs)) {
                            var lastSplitLodging = Optional.ofNullable(
                                            CollectionUtils.lastElement(splitLodgingDetailDefs))
                                    .orElse(BoardingSplitLodgingDetailDef.getDefaultInstance());
                            builder.addAllSplitLodgings(splitLodgingDetailDefs)
                                    .setLodgingUnitName(lastSplitLodging.getLodgingUnitName())
                                    .setLodgingTypeName(lastSplitLodging.getLodgingTypeName())
                                    .setLodgingTypeId(lastSplitLodging.getLodgingTypeId())
                                    .setLodgingUnitId(lastSplitLodging.getLodgingId());
                        }
                        return builder.build();
                    })
                    .toList();
            appointmentCardPetBoardingView = appointmentCardPetBoardingView.toBuilder()
                    .clearPetServices()
                    .addAllPetServices(petServiceViews)
                    .build();
        }
        return appointmentCardPetBoardingView;
    }

    /**
     * 同 pet 的同个 appointment，用户需要从 grooming 信息内，看到关联的 lodging 信息
     * 所以要从 boarding 或 daycare 内获取
     * @param groomingView
     * @param boardingViews
     */
    public static ListAppointmentCardResult.GroomingView extractGroomingViewFromBoardingViews(
            ListAppointmentCardResult.GroomingView groomingView,
            List<ListAppointmentCardResult.BoardingView> boardingViews) {

        // 查找匹配的 boarding
        var matchingBoardingView = boardingViews.stream()
                .filter(bv -> bv.getAppointmentId() == groomingView.getAppointmentId()
                        && bv.getPetId() == groomingView.getPetId())
                .findFirst()
                .orElse(null);

        if (matchingBoardingView == null) {
            return groomingView;
        }

        // 查找 boarding 下 有 lodging 的 petService
        var boardingPetService = matchingBoardingView.getPetServicesList().stream()
                .filter(bpsv -> bpsv.getLodgingTypeId() != 0)
                .findFirst()
                .orElse(null);

        if (boardingPetService == null) {
            return groomingView;
        }

        // 转化
        var updatedPetServices = groomingView.getPetServicesList().stream()
                .map(groomingPetService -> {
                    var builder = groomingPetService.toBuilder();
                    builder.setLodgingTypeId(boardingPetService.getLodgingTypeId());
                    builder.setLodgingTypeName(boardingPetService.getLodgingTypeName());
                    builder.setLodgingUnitId(boardingPetService.getLodgingUnitId());
                    builder.setLodgingUnitName(boardingPetService.getLodgingUnitName());
                    builder.addAllSplitLodgings(boardingPetService.getSplitLodgingsList());
                    return builder.build();
                })
                .toList();

        return groomingView.toBuilder()
                .clearPetServices()
                .addAllPetServices(updatedPetServices)
                .build();
    }

    /**
     * 同 pet 的同个 appointment，用户需要从 grooming 信息内，看到关联的 lodging 信息
     * 所以要从 boarding 或 daycare 内获取
     * @param groomingView
     * @param daycareViews
     */
    public static ListAppointmentCardResult.GroomingView extractGroomingViewFromDaycareViews(
            ListAppointmentCardResult.GroomingView groomingView,
            List<ListAppointmentCardResult.DaycareView> daycareViews) {

        //  查找匹配的 daycare
        var matchingDaycareView = daycareViews.stream()
                .filter(dv -> dv.getAppointmentId() == groomingView.getAppointmentId()
                        && dv.getPetId() == groomingView.getPetId())
                .findFirst()
                .orElse(null);

        if (matchingDaycareView == null) {
            return groomingView;
        }

        // 查找 daycare 下 有 lodging 的 petService
        var daycarePetService = matchingDaycareView.getPetServicesList().stream()
                .filter(dpsv -> dpsv.getLodgingTypeId() != 0)
                .findFirst()
                .orElse(null);

        if (daycarePetService == null) {
            return groomingView;
        }

        // 转化
        var updatedPetServices = groomingView.getPetServicesList().stream()
                .map(groomingPetService -> {
                    var builder = groomingPetService.toBuilder();
                    builder.setLodgingTypeId(daycarePetService.getLodgingTypeId());
                    builder.setLodgingTypeName(daycarePetService.getLodgingTypeName());
                    builder.setLodgingUnitId(daycarePetService.getLodgingUnitId());
                    builder.setLodgingUnitName(daycarePetService.getLodgingUnitName());
                    return builder.build();
                })
                .toList();

        return groomingView.toBuilder()
                .clearPetServices()
                .addAllPetServices(updatedPetServices)
                .build();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listDailyTasks(ListDailyTasksParams request, StreamObserver<ListDailyTasksResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var date = DateTimeConverter.INSTANCE.toLocalDate(request.getDate());
        var statuses = buildStatuses();
        var appointmentIds = appointmentService
                .listAppointmentPrintCardList(
                        companyId, request.getBusinessId(), date, request.getServiceItemTypesList(), statuses)
                .stream()
                .map(AppointmentModel::getId)
                .toList();
        if (CollectionUtils.isEmpty(appointmentIds)) {
            responseObserver.onNext(ListDailyTasksResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        var tasks =
                getTasks(request.getBusinessId(), request.getServiceItemTypesList(), request.getDate(), appointmentIds);

        var petIds =
                tasks.stream().map(AppointmentTaskModel::getPetId).distinct().toList();
        var staffIds = tasks.stream()
                .map(AppointmentTaskModel::getStaffId)
                .filter(id -> id != 0)
                .distinct()
                .toList();
        var serviceIds = tasks.stream()
                .map(AppointmentTaskModel::getServiceId)
                .distinct()
                .toList();

        var petFuture = CompletableFuture.supplyAsync(
                () -> businessPetService.listPetsInfo(petIds), ThreadPool.getSubmitExecutor());
        var staffFuture =
                CompletableFuture.supplyAsync(() -> staffService.getStaffMap(staffIds), ThreadPool.getSubmitExecutor());
        var serviceFuture = CompletableFuture.supplyAsync(
                () -> serviceService.getServiceMap(companyId, serviceIds), ThreadPool.getSubmitExecutor());

        CompletableFuture.allOf(petFuture, staffFuture, serviceFuture).join();

        var petMap = petFuture.join();
        var staffMap = staffFuture.join();
        var serviceMap = serviceFuture.join();

        var tasksResult = transform(tasks, staffMap, petMap, serviceMap);

        responseObserver.onNext(tasksResult);
        responseObserver.onCompleted();
    }

    public ListDailyTasksResult transform(
            List<AppointmentTaskModel> tasks,
            Map<Long, StaffModel> staffMap,
            Map<Long, BusinessCustomerPetInfoModel> petMap,
            Map<Long, ServiceBriefView> serviceMap) {

        ListDailyTasksResult.Builder result = ListDailyTasksResult.newBuilder();

        // Group tasks by category
        Map<AppointmentTaskCategory, List<AppointmentTaskModel>> groupedTasks =
                tasks.stream().collect(Collectors.groupingBy(AppointmentTaskModel::getTaskCategory));

        // Transform feeding tasks
        var feedingRows = transformTasks(
                groupedTasks.getOrDefault(AppointmentTaskCategory.FEEDING, Collections.emptyList()),
                staffMap,
                petMap,
                serviceMap);
        result.addAllFeedings(feedingRows);

        // Transform medication tasks
        var medicationRows = transformTasks(
                groupedTasks.getOrDefault(AppointmentTaskCategory.MEDICATION, Collections.emptyList()),
                staffMap,
                petMap,
                serviceMap);
        result.addAllMedications(medicationRows);

        // Transform and group add-on tasks
        var addOnGroups = tasks.stream()
                .filter(task -> isAddOnTask(task.getTaskCategory()))
                .collect(Collectors.groupingBy(AppointmentTaskModel::getServiceId));

        var addOnRows = addOnGroups.entrySet().stream()
                .map(entry -> createAddOnGroup(entry.getKey(), entry.getValue(), staffMap, petMap, serviceMap))
                .collect(Collectors.toList());
        result.addAllAddOns(addOnRows);

        return result.build();
    }

    private List<ListDailyTasksResult.TaskRow> transformTasks(
            List<AppointmentTaskModel> tasks,
            Map<Long, StaffModel> staffMap,
            Map<Long, BusinessCustomerPetInfoModel> petMap,
            Map<Long, ServiceBriefView> serviceMap) {

        return tasks.stream()
                .map(task -> createTaskRow(task, staffMap, petMap, serviceMap))
                .collect(Collectors.toList());
    }

    private ListDailyTasksResult.TaskRow createTaskRow(
            AppointmentTaskModel task,
            Map<Long, StaffModel> staffMap,
            Map<Long, BusinessCustomerPetInfoModel> petMap,
            Map<Long, ServiceBriefView> serviceMap) {

        return ListDailyTasksResult.TaskRow.newBuilder()
                .setCategory(task.getTaskCategory())
                .setPet(createPetColumn(petMap.get(task.getPetId())))
                .setService(createServiceColumn(serviceMap.get(task.getServiceId())))
                .setInstruction(task.getInstruction())
                .setTime(task.getStartTime())
                .setStaff(createStaffColumn(staffMap.get(task.getStaffId())))
                .build();
    }

    private ListDailyTasksResult.PetColumn createPetColumn(BusinessCustomerPetInfoModel pet) {
        if (pet == null) {
            return ListDailyTasksResult.PetColumn.getDefaultInstance();
        }

        return ListDailyTasksResult.PetColumn.newBuilder()
                .setId(pet.getId())
                .setName(pet.getPetName())
                .setPetType(pet.getPetType())
                .setBreed(pet.getBreed())
                .setGender(pet.getGender())
                .setAvatarPath(pet.getAvatarPath())
                .setWeight(pet.getWeight())
                .build();
    }

    private ListDailyTasksResult.ServiceColumn createServiceColumn(ServiceBriefView service) {
        if (service == null) {
            return ListDailyTasksResult.ServiceColumn.getDefaultInstance();
        }

        return ListDailyTasksResult.ServiceColumn.newBuilder()
                .setId(service.getId())
                .setName(service.getName())
                .setServiceItemType(service.getServiceItemType())
                .build();
    }

    private ListDailyTasksResult.StaffColumn createStaffColumn(StaffModel staff) {
        if (staff == null) {
            return ListDailyTasksResult.StaffColumn.getDefaultInstance();
        }

        return ListDailyTasksResult.StaffColumn.newBuilder()
                .setId(staff.getId())
                .setFirstName(staff.getFirstName())
                .setLastName(staff.getLastName())
                .build();
    }

    private ListDailyTasksResult.AddOnGroup createAddOnGroup(
            long addOnId,
            List<AppointmentTaskModel> tasks,
            Map<Long, StaffModel> staffMap,
            Map<Long, BusinessCustomerPetInfoModel> petMap,
            Map<Long, ServiceBriefView> serviceMap) {

        return ListDailyTasksResult.AddOnGroup.newBuilder()
                .setId(addOnId)
                .setName(getServiceName(serviceMap, addOnId))
                .addAllTasks(transformTasks(tasks, staffMap, petMap, serviceMap))
                .build();
    }

    private static String getServiceName(Map<Long, ServiceBriefView> serviceMap, long addOnId) {
        var addOn = serviceMap.get(addOnId);
        return addOn != null ? addOn.getName() : "";
    }

    private static boolean isAddOnTask(AppointmentTaskCategory category) {
        // Add logic to determine if a task category is an add-on
        // You might want to customize this based on your business rules
        return category != AppointmentTaskCategory.FEEDING && category != AppointmentTaskCategory.MEDICATION;
    }

    private static List<AppointmentStatus> buildStatuses() {
        return AppointmentService.ACTIVE_STATUS_SET;
    }

    private List<AppointmentTaskModel> getTasks(
            long businessId, List<ServiceItemType> serviceItemTypes, Date date, List<Long> appointmentIds) {
        var companyId = AuthContext.get().companyId();
        var timeZoneName = companySettingService.mustGetTimeZoneName(companyId);
        var interval = DateTimeConverter.INSTANCE.buildFullDayInterval(date, timeZoneName);

        var tasksResponse = appointmentTaskService.listAppointmentTasks(
                companyId, businessId, interval, serviceItemTypes, appointmentIds);

        int total = tasksResponse.getPagination().getTotal();
        if (total <= 1000) {
            return tasksResponse.getTasksList();
        }
        var nextResponse = appointmentTaskService.listAppointmentTasks(
                companyId, businessId, interval, serviceItemTypes, appointmentIds);

        return Stream.concat(tasksResponse.getTasksList().stream(), nextResponse.getTasksList().stream())
                .toList();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listBoardingDepartureCard(
            final ListBoardingDepartureCardParams request,
            final StreamObserver<ListBoardingDepartureCardResult> responseObserver) {
        long companyId = AuthContext.get().companyId();
        LocalDate date = DateTimeConverter.INSTANCE.toLocalDate(request.getDate());
        String dateStr = date.toString();

        // appointment
        var appointmentModelMap = appointmentService
                .listBoardingDeparutrePrintCardList(
                        companyId,
                        request.getBusinessId(),
                        date,
                        request.getIsPetsCheckedInOnly()
                                ? List.of(AppointmentStatus.CHECKED_IN)
                                : AppointmentService.ACTIVE_STATUS_SET)
                .stream()
                .collect(Collectors.toMap(AppointmentModel::getId, x -> x, (x, y) -> x));
        var appointmentIdList = appointmentModelMap.keySet().stream().toList();
        var appointmentNotes = appointmentService.getAppointmentNotes(companyId, appointmentIdList);

        // pet detail
        var petDetailResp = petDetailService.getPetDetailsWithActualDates(companyId, appointmentIdList);
        var allPetDetails = petDetailResp.getPetDetailsList();
        var allPetEvaluations = petDetailResp.getPetEvaluationsList();
        var appointmentPetDetails =
                allPetDetails.stream().collect(Collectors.groupingBy(PetDetailModel::getGroomingId));

        // pet
        var petIds = PetDetailUtil.getPetDetailPetIDs(allPetDetails, allPetEvaluations);
        var petMap = petDetailUtil.getPetMap(allPetDetails, allPetEvaluations, companyId);
        var petAppointments = PetDetailUtil.getPetAppointments(allPetDetails, allPetEvaluations);
        var petCodes = petCodeService.getPetCodeIdsMap(companyId, petIds);
        var petNotes = businessPetService.getPetNoteDTOs(petIds);
        // customer, id -> customer
        var customerIds = petMap.values().stream()
                .map(BusinessCustomerPetModel::getCustomerId)
                .distinct()
                .toList();
        var customerMap = businessCustomerService.listBusinessCustomerInfos(customerIds).stream()
                .collect(Collectors.toMap(
                        BusinessCustomerInfoModel::getId, customer -> customer, (existing, replacement) -> existing));
        // service
        var serviceMap = serviceService.getServiceMap(companyId, PetDetailUtil.getPetDetailServiceIDs(allPetDetails));

        // pet incident report
        List<Long> petIdList = PetDetailUtil.getPetDetailPetIDs(
                petDetailResp.getPetDetailsList(), petDetailResp.getPetEvaluationsList());
        var petIncidentReportMap = petDetailUtil.getPetIncidentReport(petIdList, companyId);

        // boarding split lodgings
        var boardingSplitLodgings = boardingSplitLodgingService
                .listBoardingSplitLodgings(ListBoardingSplitLodgingsRequest.newBuilder()
                        .addAllAppointmentIds(appointmentIdList)
                        .build())
                .getBoardingSplitLodgingsList();

        var boardingSplitLodgingMap = boardingSplitLodgings.stream()
                .collect(Collectors.groupingBy(BoardingSplitLodgingModel::getAppointmentId));

        // lodging
        var lodgingMap = lodgingService.getLodgingMap(
                PetDetailUtil.getPetDetailLodgingIDs(allPetDetails, allPetEvaluations, boardingSplitLodgings));
        var lodgingUnitMap = lodgingMap.getKey();
        var lodgingTypeMap = lodgingMap.getValue();

        var assignInfoList = lodgingUtil.getLodgingAssignInfo(companyId, request.getBusinessId(), dateStr, dateStr);
        var calPetCntPerDayPerLodging = LodgingUtil.calPetCntPerDayPerLodging(dateStr, dateStr, assignInfoList);

        // pet belongings
        var petBelongings = petBelongingService.getAppointmentPetBelongs(appointmentIdList);
        var petCodeBindings = businessPetCodeService.listPetCodeBindings(companyId, petIds);

        List<ListBoardingDepartureCardResult.BoardingView> boardingViews = new ArrayList<>();

        // 计算当天每个 lodging type 的 pet 数量
        // 这里需要从 db 获取一遍 lodging unit，因为 lodgingMap 中只包含了当天有 pet 的 lodging unit
        Map<Long, List<LodgingUnitModel>> lodgingUnitByLodgingTypes =
                lodgingService
                        .getLodgingUnit(
                                companyId,
                                request.getBusinessId(),
                                lodgingTypeMap.keySet().stream().toList())
                        .stream()
                        .collect(Collectors.groupingBy(LodgingUnitModel::getLodgingTypeId));
        List<ListAppointmentCardResult.LodgingTypeDayView> lodgingTypeDayViews =
                PrintCardConverter.INSTANCE.toAppointmentCardPetLodgingTypeDayView(
                        dateStr, calPetCntPerDayPerLodging, lodgingUnitByLodgingTypes, lodgingTypeMap);
        List<LodgingUnitModel> lodgingUnitModels =
                lodgingMap.getKey().values().stream().toList();
        var petsSorted = petMap.values().stream()
                .sorted(Comparator.comparing(BusinessCustomerPetModel::getPetName, String.CASE_INSENSITIVE_ORDER))
                .toList();
        for (var pet : petsSorted) {
            for (var appointmentId : petAppointments.getOrDefault(pet.getId(), List.of())) {
                var appointment = appointmentModelMap.get(appointmentId);
                if (appointment == null) {
                    return;
                }

                var serviceItemTypeToPetDetails = appointmentPetDetails.getOrDefault(appointmentId, List.of()).stream()
                        .filter(k -> k.getPetId() == pet.getId())
                        .filter(k -> PetDetailUtil.isContainDate(
                                date.toString(), k.getStartDate(), k.getEndDate(), k.getSpecificDates()))
                        .collect(Collectors.groupingBy(PetDetailModel::getServiceItemType));

                var boardingServices =
                        serviceItemTypeToPetDetails.getOrDefault(ServiceItemType.BOARDING, List.of()).stream()
                                .filter(k -> Objects.equals(k.getServiceType(), ServiceType.SERVICE))
                                .toList();
                if (!CollectionUtils.isEmpty(boardingServices)) {
                    var boardingDepartureCardPetBoardingView =
                            PrintCardConverter.INSTANCE.toBoardingDepartureCardPetBoardingView(
                                    appointmentId,
                                    pet.getId(),
                                    boardingServices,
                                    serviceMap,
                                    lodgingMap.getKey(),
                                    lodgingMap.getValue(),
                                    petBelongings);

                    if (!CollectionUtils.isEmpty(boardingSplitLodgingMap)
                            && boardingSplitLodgingMap.containsKey(appointmentId)) {
                        var petServiceViews = boardingDepartureCardPetBoardingView.getPetServicesList().stream()
                                .map(petServiceView -> {
                                    var builder = petServiceView.toBuilder();

                                    var boardingSplitLodgingModels = LodgingUtil.filterBoardingSplitLodgings(
                                            boardingSplitLodgingMap,
                                            appointmentId,
                                            petServiceView.getPetServiceId(),
                                            date,
                                            null);
                                    var splitLodgingDetailDefs = LodgingUtil.getBoardingSplitLodgingDefs(
                                            lodgingUnitMap, lodgingTypeMap, boardingSplitLodgingModels);

                                    if (!CollectionUtils.isEmpty(splitLodgingDetailDefs)) {
                                        var lastSplitLodging = Optional.ofNullable(
                                                        CollectionUtils.lastElement(splitLodgingDetailDefs))
                                                .orElse(BoardingSplitLodgingDetailDef.getDefaultInstance());
                                        builder.addAllSplitLodgings(splitLodgingDetailDefs)
                                                .setLodgingUnitName(lastSplitLodging.getLodgingUnitName())
                                                .setLodgingTypeName(lastSplitLodging.getLodgingTypeName())
                                                .setLodgingTypeId(lastSplitLodging.getLodgingTypeId());
                                    }
                                    return builder.build();
                                })
                                .toList();
                        boardingDepartureCardPetBoardingView = boardingDepartureCardPetBoardingView.toBuilder()
                                .clearPetServices()
                                .addAllPetServices(petServiceViews)
                                .build();
                    }

                    boardingViews.add(boardingDepartureCardPetBoardingView);
                }
            }
        }

        responseObserver.onNext(ListBoardingDepartureCardResult.newBuilder()
                .addAllBoardings(boardingViews)
                .addAllPets(petMap.values().stream()
                        .map(k -> PrintCardConverter.INSTANCE.toAppointmentCardPetView(
                                k, customerMap, petCodes, petNotes, petCodeBindings, petIncidentReportMap))
                        .toList())
                .addAllAppointments(appointmentModelMap.values().stream()
                        .map(k -> PrintCardConverter.INSTANCE.toAppointmentCardAppointmentView(k, appointmentNotes))
                        .toList())
                .addAllLodgingTypes(lodgingTypeDayViews)
                .addAllLodgingUnits(lodgingUnitModels)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listDailyPlaygroupCard(
            ListDailyPlaygroupCardParams request, StreamObserver<ListDailyPlaygroupCardResult> responseObserver) {
        long companyId = AuthContext.get().companyId();
        long businessId = request.getBusinessId();

        List<PetPlaygroupModel> petPlaygroupsList = petPlaygroupServiceBlockingStub
                .listPetPlaygroup(ListPetPlaygroupRequest.newBuilder()
                        .setStartDate(request.getDate())
                        .setEndDate(request.getDate())
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .build())
                .getPetPlaygroupsList();
        if (CollectionUtils.isEmpty(petPlaygroupsList)) {
            responseObserver.onNext(ListDailyPlaygroupCardResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        var appointmentIds = petPlaygroupsList.stream()
                .map(PetPlaygroupModel::getAppointmentId)
                .distinct()
                .toList();
        List<AppointmentModel> appointments = appointmentServiceBlockingStub
                .getAppointmentList(GetAppointmentListRequest.newBuilder()
                        .addAllAppointmentId(appointmentIds)
                        .setCompanyId(companyId)
                        .build())
                .getAppointmentsList();

        if (request.getIsPetsCheckedInOnly()) {
            appointments = appointments.stream()
                    .filter(appointmentModel -> appointmentModel.getStatus() == AppointmentStatus.CHECKED_IN)
                    .toList();
            var checkInAppointmentIds = appointments.stream()
                    .map(AppointmentModel::getId)
                    .distinct()
                    .toList();
            petPlaygroupsList = petPlaygroupsList.stream()
                    .filter(petPlaygroupModel -> checkInAppointmentIds.stream()
                            .anyMatch(appointmentId -> appointmentId.equals(petPlaygroupModel.getAppointmentId())))
                    .toList();
        }

        var playgroupIds = petPlaygroupsList.stream()
                .map(PetPlaygroupModel::getPlaygroupId)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(playgroupIds)) {
            responseObserver.onNext(ListDailyPlaygroupCardResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        List<PlaygroupModel> playgroupsList = playgroupServiceBlockingStub
                .listPlaygroup(ListPlaygroupRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllIds(playgroupIds)
                        .build())
                .getPlaygroupsList();
        Map<Long, PlaygroupModel> playgroupModelMap =
                playgroupsList.stream().collect(Collectors.toMap(PlaygroupModel::getId, Function.identity()));

        var petIds = petPlaygroupsList.stream()
                .map(PetPlaygroupModel::getPetId)
                .distinct()
                .toList();
        var customerIds = appointments.stream()
                .map(AppointmentModel::getCustomerId)
                .distinct()
                .toList();
        var petInfoListFuture = futureService.getPetInfoList(petIds, companyId);
        var petCodeFuture = futureService.getPetCodeBindingMap(petIds, companyId);
        var customerMapFuture = futureService.getBusinessCustomerInfoMap(companyId, customerIds);
        CompletableFuture.allOf(petInfoListFuture, petCodeFuture, customerMapFuture)
                .join();

        ListDailyPlaygroupCardResult result = ListDailyPlaygroupCardResult.newBuilder()
                .addAllPlaygroups(playgroupsList)
                .addAllPlaygroupViews(
                        PrintCardConverter.INSTANCE.toDailyPlaygroupCardPetPlaygroupView(petPlaygroupsList))
                .addAllPets(PrintCardConverter.INSTANCE.toDailyPlaygroupCardPetView(
                        petInfoListFuture.join(), playgroupModelMap, petCodeFuture.join(), customerMapFuture.join()))
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listBoardingArrivalCard(
            final ListBoardingArrivalCardParams request,
            final StreamObserver<ListBoardingArrivalCardResult> responseObserver) {
        long companyId = AuthContext.get().companyId();
        LocalDate date = DateTimeConverter.INSTANCE.toLocalDate(request.getDate());
        String dateStr = date.toString();

        // appointment
        var appointmentModelMap = appointmentService
                .listBoardingArrivalPrintCardList(
                        companyId,
                        request.getBusinessId(),
                        date,
                        request.getIsPetsCheckedInOnly()
                                ? List.of(AppointmentStatus.CHECKED_IN)
                                : AppointmentService.ACTIVE_STATUS_SET)
                .stream()
                .collect(Collectors.toMap(AppointmentModel::getId, x -> x, (x, y) -> x));
        var appointmentIdList = appointmentModelMap.keySet().stream().toList();
        var appointmentNotes = appointmentService.getAppointmentNotes(companyId, appointmentIdList);

        // pet detail
        var petDetailResp = petDetailService.getPetDetailsWithActualDates(companyId, appointmentIdList);
        var allPetDetails = petDetailResp.getPetDetailsList();
        var allPetEvaluations = petDetailResp.getPetEvaluationsList();
        var appointmentPetDetails =
                allPetDetails.stream().collect(Collectors.groupingBy(PetDetailModel::getGroomingId));

        // pet
        var petIds = PetDetailUtil.getPetDetailPetIDs(allPetDetails, allPetEvaluations);
        var petMap = petDetailUtil.getPetMap(allPetDetails, allPetEvaluations, companyId);
        var petAppointments = PetDetailUtil.getPetAppointments(allPetDetails, allPetEvaluations);
        var petCodes = petCodeService.getPetCodeIdsMap(companyId, petIds);
        var petNotes = businessPetService.getPetNoteDTOs(petIds);
        // customer, id -> customer
        var customerIds = petMap.values().stream()
                .map(BusinessCustomerPetModel::getCustomerId)
                .distinct()
                .toList();
        var customerMap = businessCustomerService.listBusinessCustomerInfos(customerIds).stream()
                .collect(Collectors.toMap(
                        BusinessCustomerInfoModel::getId, customer -> customer, (existing, replacement) -> existing));
        // service
        var serviceMap = serviceService.getServiceMap(companyId, PetDetailUtil.getPetDetailServiceIDs(allPetDetails));

        // pet incident report
        List<Long> petIdList = PetDetailUtil.getPetDetailPetIDs(
                petDetailResp.getPetDetailsList(), petDetailResp.getPetEvaluationsList());
        var petIncidentReportMap = petDetailUtil.getPetIncidentReport(petIdList, companyId);

        // boarding split lodgings
        var boardingSplitLodgings = boardingSplitLodgingService
                .listBoardingSplitLodgings(ListBoardingSplitLodgingsRequest.newBuilder()
                        .addAllAppointmentIds(appointmentIdList)
                        .build())
                .getBoardingSplitLodgingsList();

        var boardingSplitLodgingMap = boardingSplitLodgings.stream()
                .collect(Collectors.groupingBy(BoardingSplitLodgingModel::getAppointmentId));

        // lodging
        var lodgingMap = lodgingService.getLodgingMap(
                PetDetailUtil.getPetDetailLodgingIDs(allPetDetails, allPetEvaluations, boardingSplitLodgings));
        var lodgingUnitMap = lodgingMap.getKey();
        var lodgingTypeMap = lodgingMap.getValue();

        var assignInfoList = lodgingUtil.getLodgingAssignInfo(companyId, request.getBusinessId(), dateStr, dateStr);
        var calPetCntPerDayPerLodging = LodgingUtil.calPetCntPerDayPerLodging(dateStr, dateStr, assignInfoList);

        // pet belongings
        var petBelongings = petBelongingService.getAppointmentPetBelongs(appointmentIdList);
        var petCodeBindings = businessPetCodeService.listPetCodeBindings(companyId, petIds);

        List<ListBoardingArrivalCardResult.BoardingView> boardingViews = new ArrayList<>();

        // 计算当天每个 lodging type 的 pet 数量
        // 这里需要从 db 获取一遍 lodging unit，因为 lodgingMap 中只包含了当天有 pet 的 lodging unit
        Map<Long, List<LodgingUnitModel>> lodgingUnitByLodgingTypes =
                lodgingService
                        .getLodgingUnit(
                                companyId,
                                request.getBusinessId(),
                                lodgingTypeMap.keySet().stream().toList())
                        .stream()
                        .collect(Collectors.groupingBy(LodgingUnitModel::getLodgingTypeId));
        List<ListAppointmentCardResult.LodgingTypeDayView> lodgingTypeDayViews =
                PrintCardConverter.INSTANCE.toAppointmentCardPetLodgingTypeDayView(
                        dateStr, calPetCntPerDayPerLodging, lodgingUnitByLodgingTypes, lodgingTypeMap);
        List<LodgingUnitModel> lodgingUnitModels =
                lodgingMap.getKey().values().stream().toList();
        var petsSorted = petMap.values().stream()
                .sorted(Comparator.comparing(BusinessCustomerPetModel::getPetName, String.CASE_INSENSITIVE_ORDER))
                .toList();
        for (var pet : petsSorted) {
            for (var appointmentId : petAppointments.getOrDefault(pet.getId(), List.of())) {
                var appointment = appointmentModelMap.get(appointmentId);
                if (appointment == null) {
                    return;
                }

                var serviceItemTypeToPetDetails = appointmentPetDetails.getOrDefault(appointmentId, List.of()).stream()
                        .filter(k -> k.getPetId() == pet.getId())
                        .filter(k -> PetDetailUtil.isContainDate(
                                date.toString(), k.getStartDate(), k.getEndDate(), k.getSpecificDates()))
                        .collect(Collectors.groupingBy(PetDetailModel::getServiceItemType));

                var boardingServices =
                        serviceItemTypeToPetDetails.getOrDefault(ServiceItemType.BOARDING, List.of()).stream()
                                .filter(k -> Objects.equals(k.getServiceType(), ServiceType.SERVICE))
                                .toList();
                if (!CollectionUtils.isEmpty(boardingServices)) {
                    var boardingArrivalCardPetBoardingView =
                            PrintCardConverter.INSTANCE.toBoardingArrivalCardPetBoardingView(
                                    appointmentId,
                                    pet.getId(),
                                    boardingServices,
                                    serviceMap,
                                    lodgingMap.getKey(),
                                    lodgingMap.getValue(),
                                    petBelongings);

                    if (!CollectionUtils.isEmpty(boardingSplitLodgingMap)
                            && boardingSplitLodgingMap.containsKey(appointmentId)) {
                        var petServiceViews = boardingArrivalCardPetBoardingView.getPetServicesList().stream()
                                .map(petServiceView -> {
                                    var builder = petServiceView.toBuilder();

                                    var boardingSplitLodgingModels = LodgingUtil.filterBoardingSplitLodgings(
                                            boardingSplitLodgingMap,
                                            appointmentId,
                                            petServiceView.getPetServiceId(),
                                            date,
                                            null);
                                    var splitLodgingDetailDefs = LodgingUtil.getBoardingSplitLodgingDefs(
                                            lodgingUnitMap, lodgingTypeMap, boardingSplitLodgingModels);

                                    if (!CollectionUtils.isEmpty(splitLodgingDetailDefs)) {
                                        var lastSplitLodging = Optional.ofNullable(
                                                        CollectionUtils.lastElement(splitLodgingDetailDefs))
                                                .orElse(BoardingSplitLodgingDetailDef.getDefaultInstance());
                                        builder.addAllSplitLodgings(splitLodgingDetailDefs)
                                                .setLodgingUnitName(lastSplitLodging.getLodgingUnitName())
                                                .setLodgingTypeName(lastSplitLodging.getLodgingTypeName())
                                                .setLodgingTypeId(lastSplitLodging.getLodgingTypeId());
                                    }
                                    return builder.build();
                                })
                                .toList();
                        boardingArrivalCardPetBoardingView = boardingArrivalCardPetBoardingView.toBuilder()
                                .clearPetServices()
                                .addAllPetServices(petServiceViews)
                                .build();
                    }

                    boardingViews.add(boardingArrivalCardPetBoardingView);
                }
            }
        }

        responseObserver.onNext(ListBoardingArrivalCardResult.newBuilder()
                .addAllBoardings(boardingViews)
                .addAllPets(petMap.values().stream()
                        .map(k -> PrintCardConverter.INSTANCE.toAppointmentCardPetView(
                                k, customerMap, petCodes, petNotes, petCodeBindings, petIncidentReportMap))
                        .toList())
                .addAllAppointments(appointmentModelMap.values().stream()
                        .map(k -> PrintCardConverter.INSTANCE.toAppointmentCardAppointmentView(k, appointmentNotes))
                        .toList())
                .addAllLodgingTypes(lodgingTypeDayViews)
                .addAllLodgingUnits(lodgingUnitModels)
                .build());
        responseObserver.onCompleted();
    }
}
