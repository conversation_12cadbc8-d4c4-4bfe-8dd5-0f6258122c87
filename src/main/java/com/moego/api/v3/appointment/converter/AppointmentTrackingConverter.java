package com.moego.api.v3.appointment.converter;

import com.moego.idl.api.appointment.v1.ListStaffAppointmentTrackingParams;
import com.moego.idl.models.appointment.v1.Address;
import com.moego.idl.models.appointment.v1.AppointmentTracking;
import com.moego.idl.models.appointment.v1.AppointmentTrackingView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.service.appointment.v1.ListAppointmentTrackingRequest;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AppointmentTrackingConverter {

    AppointmentTrackingConverter INSTANCE = org.mapstruct.factory.Mappers.getMapper(AppointmentTrackingConverter.class);

    AppointmentTrackingView toAppointmentTrackingView(AppointmentTracking appointmentTracking);

    List<AppointmentTrackingView> toAppointmentTrackingView(List<AppointmentTracking> appointmentTrackingList);

    ListAppointmentTrackingRequest.Filter toSvcFilter(ListStaffAppointmentTrackingParams.Filter filter);

    Address toAddress(BusinessCustomerAddressModel in);
}
