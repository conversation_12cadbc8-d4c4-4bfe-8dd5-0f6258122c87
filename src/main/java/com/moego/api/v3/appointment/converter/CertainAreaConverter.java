package com.moego.api.v3.appointment.converter;

import com.moego.idl.api.appointment.v1.CalendarCardCertainArea;
import com.moego.idl.api.appointment.v1.ListDayCardsResult;
import com.moego.server.business.dto.CertainAreaDTO;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface CertainAreaConverter {

    CertainAreaConverter INSTANCE = Mappers.getMapper(CertainAreaConverter.class);

    List<ListDayCardsResult.CalendarCardCertainArea> toView(List<CertainAreaDTO> dtos);

    ListDayCardsResult.CalendarCardCertainArea toView(CertainAreaDTO dto);

    List<CalendarCardCertainArea> toCalendarCard(List<CertainAreaDTO> dtos);

    CalendarCardCertainArea toCalendarCard(CertainAreaDTO dto);
}
