package com.moego.api.v3.appointment.converter;

import com.moego.idl.api.appointment.v1.CalendarCardEvaluationInfo;
import com.moego.idl.api.appointment.v1.CalendarCardServiceInfo;
import com.moego.idl.api.appointment.v1.ListDayCardsResult;
import com.moego.idl.models.online_booking.v1.DogWalkingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.EvaluationTestDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetailModel;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface ServiceConverter {

    ServiceConverter INSTANCE = Mappers.getMapper(ServiceConverter.class);

    @Mapping(target = "petDetailId", source = "id")
    ListDayCardsResult.CalendarCardServiceInfo toCardServiceInfo(DogWalkingServiceDetailModel model);

    @Mapping(target = "petDetailId", source = "id")
    ListDayCardsResult.CalendarCardServiceInfo toCardServiceInfo(GroomingServiceDetailModel model);

    @Mapping(target = "petDetailId", source = "id")
    @Mapping(target = "serviceId", source = "addOnId")
    ListDayCardsResult.CalendarCardServiceInfo toCardServiceInfo(GroomingAddOnDetailModel model);

    @Mapping(target = "evaluationDetailId", source = "id")
    @Mapping(target = "serviceId", source = "evaluationId")
    @Mapping(target = "serviceTime", source = "duration")
    ListDayCardsResult.CalendarCardEvaluationInfo toCardEvaluationInfo(EvaluationTestDetailModel model);

    @Mapping(target = "petDetailId", source = "id")
    CalendarCardServiceInfo toCalendarCard(GroomingServiceDetailModel model);

    @Mapping(target = "petDetailId", source = "id")
    @Mapping(target = "serviceId", source = "addOnId")
    CalendarCardServiceInfo toCalendarCard(GroomingAddOnDetailModel model);

    @Mapping(target = "evaluationDetailId", source = "id")
    @Mapping(target = "serviceId", source = "evaluationId")
    @Mapping(target = "serviceTime", source = "duration")
    CalendarCardEvaluationInfo toCalendarCard(EvaluationTestDetailModel model);

    @Mapping(target = "petDetailId", source = "id")
    CalendarCardServiceInfo toCalendarCard(DogWalkingServiceDetailModel model);
}
