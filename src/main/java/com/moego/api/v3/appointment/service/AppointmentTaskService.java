package com.moego.api.v3.appointment.service;

import com.google.type.Date;
import com.google.type.Interval;
import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.api.v3.appointment.utils.TaskUtil;
import com.moego.idl.api.appointment.v1.GetAppointmentTasksByAppointmentIdParams;
import com.moego.idl.api.appointment.v1.ListAppointmentTaskCountByCategoryParams;
import com.moego.idl.api.appointment.v1.ListAppointmentTasksByCategoryParams;
import com.moego.idl.api.appointment.v1.ListAppointmentTasksParams;
import com.moego.idl.models.appointment.v1.AppointmentTaskCategory;
import com.moego.idl.models.appointment.v1.AppointmentTaskStatus;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.appointment.v1.AppointmentTaskServiceGrpc;
import com.moego.idl.service.appointment.v1.CountAppointmentTasksRequest;
import com.moego.idl.service.appointment.v1.CountAppointmentTasksResponse;
import com.moego.idl.service.appointment.v1.ListAppointmentTaskGroupsRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentTaskGroupsResponse;
import com.moego.idl.service.appointment.v1.ListAppointmentTasksRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentTasksResponse;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2025/1/3
 */
@Service
@RequiredArgsConstructor
public class AppointmentTaskService {

    private final AppointmentTaskServiceGrpc.AppointmentTaskServiceBlockingStub appointmentTaskStub;
    private final CompanySettingService companySettingService;
    private final PermissionHelper permissionHelper;

    public List<CountAppointmentTasksResponse.CareTypeCount> countTaskByCareTypes(
            long companyId, long businessId, Interval interval, List<ServiceItemType> careTypes) {
        var request = CountAppointmentTasksRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setFilter(CountAppointmentTasksRequest.Filter.newBuilder()
                        .setStartDateInterval(interval)
                        .build())
                .addAllGroupCareTypes(careTypes)
                .build();

        return appointmentTaskStub.countAppointmentTasks(request).getCareTypeCountsList();
    }

    public List<CountAppointmentTasksResponse.StatusCount> countTaskByStatus(
            long companyId, long businessId, Interval interval, List<AppointmentTaskStatus> statuses) {
        var request = CountAppointmentTasksRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setFilter(CountAppointmentTasksRequest.Filter.newBuilder()
                        .setStartDateInterval(interval)
                        .build())
                .addAllGroupStatus(statuses)
                .build();

        return appointmentTaskStub.countAppointmentTasks(request).getStatusCountsList();
    }

    public ListAppointmentTasksResponse listAppointmentTasks(
            long companyId, ListAppointmentTasksParams params, Interval fullyDayInterval) {
        var tasksRequest = TaskUtil.buildTasksRequest(companyId, params, fullyDayInterval);
        return appointmentTaskStub.listAppointmentTasks(tasksRequest);
    }

    public ListAppointmentTasksResponse listAppointmentTasks(
            long companyId, ListAppointmentTasksByCategoryParams params) {
        var interval = getDateInterval(companyId, params.getDate());

        var tasksRequest = TaskUtil.buildTasksRequest(companyId, params, interval);

        return appointmentTaskStub.listAppointmentTasks(tasksRequest);
    }

    public ListAppointmentTasksResponse listStaffAppointmentTasks(
            long companyId, long staffId, ListAppointmentTasksByCategoryParams params) {
        var interval = getDateInterval(companyId, params.getDate());

        var tasksRequest = TaskUtil.buildTasksRequest(companyId, params, interval);
        var staffTasksRequest = tasksRequest.toBuilder()
                .setFilter(tasksRequest.getFilter().toBuilder().addStaffIds(staffId))
                .build();

        return appointmentTaskStub.listAppointmentTasks(staffTasksRequest);
    }

    public CountAppointmentTasksResponse countAppointmentTasks(
            long companyId, ListAppointmentTasksParams params, Interval fullyDayInterval) {
        var countRequest = TaskUtil.buildCountRequest(companyId, params, fullyDayInterval);
        return appointmentTaskStub.countAppointmentTasks(countRequest);
    }

    public ListAppointmentTaskGroupsResponse listAppointmentTaskGroups(
            long companyId, ListAppointmentTasksParams params, Interval fullyDayInterval) {
        var groupsRequest = TaskUtil.buildGroupsRequest(companyId, params, fullyDayInterval);
        return appointmentTaskStub.listAppointmentTaskGroups(groupsRequest);
    }

    public ListAppointmentTaskGroupsResponse listAddOnGroups(
            long companyId, ListAppointmentTaskCountByCategoryParams params, Interval fullyDayInterval) {
        var groupsRequest = ListAppointmentTaskGroupsRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(params.getBusinessId())
                .setCategory(AppointmentTaskCategory.ADD_ONS)
                .setFilter(ListAppointmentTaskGroupsRequest.Filter.newBuilder()
                        .setStartDateInterval(fullyDayInterval)
                        .addAllCareTypes(params.getFilter().getCareTypesList())
                        .addAllStatuses(params.getFilter().getStatusesList())
                        .addAllStaffIds(params.getFilter().getStaffIdsList())
                        .build())
                .build();
        return appointmentTaskStub.listAppointmentTaskGroups(groupsRequest);
    }

    public CountAppointmentTasksResponse countAppointmentTasks(
            long companyId, ListAppointmentTaskCountByCategoryParams params) {
        var fullyDayInterval = getDateInterval(companyId, params.getDate());
        var countRequest = TaskUtil.buildCountRequest(
                companyId,
                params,
                fullyDayInterval,
                List.of(AppointmentTaskCategory.FEEDING, AppointmentTaskCategory.MEDICATION));

        return appointmentTaskStub.countAppointmentTasks(countRequest);
    }

    public Interval getDateInterval(long companyId, Date date) {
        var timeZoneName = companySettingService.mustGetTimeZoneName(companyId);
        return DateTimeConverter.INSTANCE.buildFullDayInterval(date, timeZoneName);
    }

    public ListAppointmentTasksResponse listAppointmentTasks(
            long companyId,
            long businessId,
            Interval interval,
            List<ServiceItemType> serviceItemTypes,
            List<Long> appointmentIds) {
        return appointmentTaskStub.listAppointmentTasks(ListAppointmentTasksRequest.newBuilder()
                .setCompanyId(companyId)
                .addBusinessIds(businessId)
                .setFilter(ListAppointmentTasksRequest.Filter.newBuilder()
                        .setStartDateInterval(interval)
                        .addAllCareTypes(serviceItemTypes)
                        .addAllAppointmentIds(appointmentIds)
                        .build())
                .setPagination(PaginationRequest.newBuilder()
                        .setPageNum(1)
                        .setPageSize(1000)
                        .build())
                .build());
    }

    public ListAppointmentTasksResponse listAppointmentTasks(
            long companyId, long businessId, GetAppointmentTasksByAppointmentIdParams request) {
        var filter = ListAppointmentTasksRequest.Filter.newBuilder()
                .addAllCategories(List.of(request.getCategory()))
                .addAllAppointmentIds(List.of(request.getAppointmentId()))
                .addAllPetIds(List.of(request.getPetId()))
                .build();
        return appointmentTaskStub.listAppointmentTasks(ListAppointmentTasksRequest.newBuilder()
                .setCompanyId(companyId)
                .addBusinessIds(businessId)
                .setFilter(filter)
                .setPagination(PaginationRequest.newBuilder().setPageNum(1).setPageSize(1000))
                .build());
    }

    /**
     * Access all tasks
     *
     * @return true means all tasks, otherwise only current staff can be accessed
     */
    public boolean isAccessAllTasks(long companyId, long staffId) {
        var scope = permissionHelper.getPermissionScopeIndex(companyId, staffId, PermissionEnums.ACCESS_TASKS);
        if (scope == null) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PERMISSION_NOT_ENOUGH,
                    String.format("permission %s is required", PermissionEnums.ACCESS_TASKS));
        }
        return scope == 1; // 1 means all tasks
    }
}
