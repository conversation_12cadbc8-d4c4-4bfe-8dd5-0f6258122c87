package com.moego.api.v3.appointment.converter;

import com.google.type.Money;
import com.moego.api.v3.appointment.utils.OrderEnumUtil;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderModelAppointmentView;
import com.moego.idl.models.order.v1.OrderModelV1;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.models.order.v1.OrderStatus;
import com.moego.lib.common.proto.MoneyUtils;
import java.math.BigDecimal;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface OrderConverter {
    OrderConverter INSTANCE = Mappers.getMapper(OrderConverter.class);

    List<OrderModelAppointmentView> toView(List<OrderModel> models);

    @Mapping(target = "subTotalAmount", source = "subTotalAmount", qualifiedByName = "toMoney")
    @Mapping(target = "totalAmount", source = "totalAmount", qualifiedByName = "toMoney")
    @Mapping(target = "paidAmount", source = "paidAmount", qualifiedByName = "toMoney")
    @Mapping(target = "remainAmount", source = "remainAmount", qualifiedByName = "toMoney")
    @Mapping(target = "refundedAmount", source = "refundedAmount", qualifiedByName = "toMoney")
    @Mapping(target = "tipsAmount", source = "tipsAmount", qualifiedByName = "toMoney")
    OrderModelAppointmentView toView(OrderModel model);

    OrderModelAppointmentView toView(OrderModelV1 modelV1);

    @Named("toMoney")
    default Money toMoney(double amount) {
        return MoneyUtils.toGoogleMoney(BigDecimal.valueOf(amount));
    }

    default OrderModel.PaymentStatus toPaymentStatus(String paymentStatus) {
        return OrderModel.PaymentStatus.valueOf(paymentStatus);
    }

    default OrderSourceType toSourceType(String sourceType) {
        return OrderEnumUtil.convert2EnumSourceType(sourceType);
    }

    default OrderStatus toStatus(int status) {
        return OrderStatus.forNumber(status);
    }
}
