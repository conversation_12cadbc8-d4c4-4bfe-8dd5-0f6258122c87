package com.moego.api.v3.appointment.utils;

import com.google.type.Money;
import com.moego.idl.models.order.v1.OrderDetailModelV1;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderModelAppointmentView;
import com.moego.idl.models.order.v1.OrderPromotionModel;
import com.moego.lib.common.proto.MoneyUtils;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2025/5/17
 */
public class OrderUtil {

    public static Money calculateTotalPaidAmount(List<OrderModelAppointmentView> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return Money.getDefaultInstance();
        }
        var paidAmount = orders.stream()
                .map(OrderModelAppointmentView::getPaidAmount)
                .map(MoneyUtils::fromGoogleMoney)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        var currencyCode = orders.get(0).getPaidAmount().getCurrencyCode();
        return MoneyUtils.toGoogleMoney(paidAmount, currencyCode);
    }

    public static Money calculateDepositTotalAmount(List<OrderModelAppointmentView> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return Money.getDefaultInstance();
        }
        var paidAmount = orders.stream()
                .filter(order -> Objects.equals(order.getOrderType(), OrderModel.OrderType.DEPOSIT))
                .map(OrderModelAppointmentView::getTotalAmount)
                .map(MoneyUtils::fromGoogleMoney)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        var currencyCode = orders.get(0).getTotalAmount().getCurrencyCode();
        return MoneyUtils.toGoogleMoney(paidAmount, currencyCode);
    }

    public static Money calculateDepositPaidAmount(List<OrderModelAppointmentView> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return Money.getDefaultInstance();
        }
        var paidAmount = orders.stream()
                .filter(order -> Objects.equals(order.getOrderType(), OrderModel.OrderType.DEPOSIT))
                .map(OrderModelAppointmentView::getPaidAmount)
                .map(MoneyUtils::fromGoogleMoney)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        var currencyCode = orders.get(0).getPaidAmount().getCurrencyCode();
        return MoneyUtils.toGoogleMoney(paidAmount, currencyCode);
    }

    public static Money calculateTipsAmount(List<OrderModelAppointmentView> orders) {
        if (CollectionUtils.isEmpty(orders)) {
            return Money.getDefaultInstance();
        }
        var tipsAmount = orders.stream()
                .map(OrderModelAppointmentView::getTipsAmount)
                .map(MoneyUtils::fromGoogleMoney)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        var currencyCode = orders.get(0).getTipsAmount().getCurrencyCode();
        return MoneyUtils.toGoogleMoney(tipsAmount, currencyCode);
    }

    public static boolean hasUseStoreCredit(List<OrderDetailModelV1> orderDetails) {
        if (CollectionUtils.isEmpty(orderDetails)) {
            return false;
        }

        return orderDetails.stream()
                .map(OrderDetailModelV1::getOrderPromotionsList)
                .flatMap(List::stream)
                .anyMatch(OrderPromotionModel::hasStoreCredit);
    }
}
