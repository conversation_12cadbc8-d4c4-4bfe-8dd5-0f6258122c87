package com.moego.api.v3.appointment.service;

import com.moego.api.v3.appointment.dto.CalendarCardFilterDTO;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BookingRequestAssociatedModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.ListBookingRequestsRequest;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2024/9/26
 */
@Service
@RequiredArgsConstructor
public class BookingRequestService {

    private final BookingRequestServiceGrpc.BookingRequestServiceBlockingStub bookingRequestStub;

    /**
     * Grooming/Evaluation 类型 Submitted 状态预约请求需要展示在 Calendar
     *
     * @param filter calendar card filter
     * @return list of booking requests
     */
    public List<BookingRequestModel> listCalendarBookingRequests(CalendarCardFilterDTO filter) {
        if (filter.getBusinessId() == null) {
            return List.of();
        }
        return bookingRequestStub
                .listBookingRequests(ListBookingRequestsRequest.newBuilder()
                        .addBusinessIds(filter.getBusinessId())
                        .addAllAssociatedModels(List.of(
                                BookingRequestAssociatedModel.SERVICE,
                                BookingRequestAssociatedModel.ADD_ON,
                                BookingRequestAssociatedModel.AUTO_ASSIGN))
                        .addStatuses(BookingRequestStatus.SUBMITTED)
                        .setStartDate(filter.getStartDate())
                        .setEndDate(filter.getEndDate())
                        .addAllServiceItems(List.of(
                                ServiceItemType.GROOMING, ServiceItemType.EVALUATION, ServiceItemType.DOG_WALKING))
                        .addAllPaymentStatuses(List.of(
                                BookingRequestModel.PaymentStatus.NO_PAYMENT,
                                BookingRequestModel.PaymentStatus.PROCESSING,
                                BookingRequestModel.PaymentStatus.SUCCESS))
                        .build())
                .getBookingRequestsList();
    }
}
