package com.moego.api.v3.appointment.converter;

import static com.moego.common.utils.DateUtil.STANDARD_DATE;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.DateUtil;
import com.moego.idl.api.appointment.v1.ListPlaygroupCalendarViewResult;
import com.moego.idl.api.appointment.v1.VaccineComposite;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.offering.v1.PlaygroupModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PetPlaygroupConverter {
    PetPlaygroupConverter Instance = Mappers.getMapper(PetPlaygroupConverter.class);

    default ListPlaygroupCalendarViewResult.PetView toPetView(
            BusinessCustomerPetInfoModel petModel,
            PlaygroupModel playgroupModel,
            List<VaccineComposite> vaccineComposites,
            BusinessCustomerInfoModel customer) {
        return ListPlaygroupCalendarViewResult.PetView.newBuilder()
                .setPetId(petModel.getId())
                .setPetName(petModel.getPetName())
                .setPetImage(petModel.getAvatarPath())
                .setPetType(petModel.getPetType())
                .setEnableVaccineExpiryNotification(petModel.getExpiryNotification())
                .addAllVaccines(vaccineComposites)
                .setPetPlaygroupColor(Objects.nonNull(playgroupModel) ? playgroupModel.getColorCode() : "")
                .setCustomer(toCustomerView(customer))
                .build();
    }

    default ListPlaygroupCalendarViewResult.AppointmentView toAppointmentView(
            AppointmentModel appointmentModel, List<PetDetailModel> petDetails) {
        return ListPlaygroupCalendarViewResult.AppointmentView.newBuilder()
                .setAppointmentId(appointmentModel.getId())
                .setAppointmentStatus(appointmentModel.getStatus())
                .addAllServiceItemTypes(
                        ServiceItemEnum.convertBitValueList(appointmentModel.getServiceTypeInclude()).stream()
                                .map(ServiceItemEnum::getServiceItem)
                                .map(ServiceItemType::forNumber)
                                .toList())
                .setAppointmentStartDate(DateTimeConverter.INSTANCE.convertToGoogleDate(
                        DateUtil.parseDate(appointmentModel.getAppointmentDate(), STANDARD_DATE)))
                .addAllPetIds(petDetails.stream()
                        .map(PetDetailModel::getPetId)
                        .distinct()
                        .toList())
                .build();
    }

    default List<ListPlaygroupCalendarViewResult.PetView> toPetViewList(
            List<BusinessCustomerPetInfoModel> petModels,
            Map<Long, PlaygroupModel> playgroupModelMap,
            Map<Long, List<VaccineComposite>> vaccineMap,
            Map<Long, BusinessCustomerInfoModel> customerMap) {
        return petModels.stream()
                .map(petModel -> {
                    var playgroupModel = playgroupModelMap.get(petModel.getPlaygroupId());
                    var vaccineComposites = vaccineMap.getOrDefault(petModel.getId(), List.of());
                    var customerModel = customerMap.get(petModel.getCustomerId());
                    return toPetView(petModel, playgroupModel, vaccineComposites, customerModel);
                })
                .toList();
    }

    default List<ListPlaygroupCalendarViewResult.AppointmentView> toAppointmentViewList(
            List<AppointmentModel> appointmentModels, Map<Long, List<PetDetailModel>> petDetailMap) {
        return appointmentModels.stream()
                .map(appointmentModel -> {
                    var petDetails = petDetailMap.getOrDefault(appointmentModel.getId(), List.of());
                    return toAppointmentView(appointmentModel, petDetails);
                })
                .toList();
    }

    default ListPlaygroupCalendarViewResult.CustomerView toCustomerView(BusinessCustomerInfoModel customerModel) {
        if (customerModel == null) {
            return ListPlaygroupCalendarViewResult.CustomerView.getDefaultInstance();
        }
        return ListPlaygroupCalendarViewResult.CustomerView.newBuilder()
                .setCustomerId(customerModel.getId())
                .setLastName(customerModel.getLastName())
                .setFirstName(customerModel.getFirstName())
                .build();
    }
}
