package com.moego.api.v3.appointment.utils;

import static java.util.stream.Collectors.groupingBy;

import com.google.protobuf.Timestamp;
import com.google.protobuf.util.Timestamps;
import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.api.v3.appointment.dto.LodgingInfoDTO;
import com.moego.api.v3.appointment.service.CompanySettingService;
import com.moego.api.v3.offering.service.AppointmentService;
import com.moego.common.utils.DateUtil;
import com.moego.idl.api.appointment.v1.GetLodgingCalendarViewV2Result;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.BoardingSplitLodgingDetailDef;
import com.moego.idl.models.appointment.v1.BoardingSplitLodgingModel;
import com.moego.idl.models.appointment.v1.LodgingAssignAppointmentInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetDetailInfo;
import com.moego.idl.models.appointment.v1.LodgingAssignPetEvaluationInfo;
import com.moego.idl.models.appointment.v1.LodgingOccupiedStatus;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.LodgingUnitType;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.service.appointment.v1.LodgingAssignInfoRequest;
import com.moego.idl.service.appointment.v1.LodgingServiceGrpc;
import com.moego.idl.service.appointment.v1.LodgingTransferRequest;
import com.moego.idl.service.offering.v1.GetLodgingUnitListRequest;
import com.moego.idl.service.offering.v1.LodgingTypeServiceGrpc;
import com.moego.idl.service.offering.v1.LodgingUnitServiceGrpc;
import com.moego.idl.service.offering.v1.MGetLodgingTypeRequest;
import com.moego.idl.service.offering.v1.MGetLodgingUnitRequest;
import com.moego.idl.service.offering.v1.MGetLodgingUnitResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import java.time.DateTimeException;
import java.time.Instant;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.util.CollectionUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class LodgingUtil {
    private final LodgingTypeServiceGrpc.LodgingTypeServiceBlockingStub lodgingTypeClient;
    private final LodgingUnitServiceGrpc.LodgingUnitServiceBlockingStub lodgingUnitClient;
    private final LodgingServiceGrpc.LodgingServiceBlockingStub lodgingClient;

    public static final Integer UNLIMITED_MAX_PET_NUM = -1;
    public static final Long DEFAULT_LODGING_TYPE_ID = 0L;
    public static final String DEFAULT_LODGING_TYPE_NAME = "Default area by system";
    public static final Long DEFAULT_LODGING_UNIT_ID = 0L;
    public static final String DEFAULT_LODGING_UNIT_NAME = "Default area";

    // 按满载程度由低到高排序
    public static final Map<LodgingOccupiedStatus, Integer> LODGING_STATUS_ORDER_MAP = Map.of(
            LodgingOccupiedStatus.VACANT,
            1,
            LodgingOccupiedStatus.PARTIALLY_OCCUPIED,
            2,
            LodgingOccupiedStatus.FULLY_OCCUPIED,
            3);
    private final AppointmentService appointmentService;
    private final CompanySettingService companySettingService;

    public static LodgingInfoDTO getLodgingInfo(
            final Map<Long, LodgingUnitModel> lodgingUnitMap,
            final Map<Long, LodgingTypeModel> lodgingTypeMap,
            long lodgingId) {
        var lodgingInfoDTO = new LodgingInfoDTO();
        lodgingInfoDTO.setLodgingUnitId(lodgingId);
        lodgingInfoDTO.setLodgingUnitName("");
        lodgingInfoDTO.setLodgingTypeName("");

        LodgingUnitModel lodgingUnitModel = lodgingUnitMap.get(lodgingId);
        if (Objects.isNull(lodgingUnitModel)) {
            return lodgingInfoDTO;
        }
        lodgingInfoDTO.setLodgingUnitName(lodgingUnitModel.getName());
        lodgingInfoDTO.setSort(lodgingUnitModel.getSort());

        var lodgingTypeModel = lodgingTypeMap.get(lodgingUnitModel.getLodgingTypeId());
        if (Objects.nonNull(lodgingTypeModel)) {
            lodgingInfoDTO.setLodgingTypeId(lodgingTypeModel.getId());
            lodgingInfoDTO.setLodgingTypeName(lodgingTypeModel.getName());
        }
        return lodgingInfoDTO;
    }

    @NotNull
    public static List<BoardingSplitLodgingDetailDef> getBoardingSplitLodgingDefs(
            final Map<Long, LodgingUnitModel> lodgingUnitMap,
            final Map<Long, LodgingTypeModel> lodgingTypeMap,
            final List<BoardingSplitLodgingModel> splitLodgingModels) {

        return splitLodgingModels.stream()
                .sorted(Comparator.comparing(BoardingSplitLodgingModel::getStartDateTime, Timestamps.comparator()))
                .map(model -> {
                    var lodgingId = model.getLodgingId();
                    var startDateTime = DateTimeConverter.INSTANCE.toLocalDateTime(model.getStartDateTime());
                    var endDateTime = DateTimeConverter.INSTANCE.toLocalDateTime(model.getEndDateTime());

                    var lodgingInfo = LodgingUtil.getLodgingInfo(lodgingUnitMap, lodgingTypeMap, lodgingId);

                    var builder = BoardingSplitLodgingDetailDef.newBuilder()
                            .setLodgingId(lodgingId)
                            .setStartDate(startDateTime.toLocalDate().toString())
                            .setEndDate(endDateTime.toLocalDate().toString())
                            .setStartTime(DateUtil.getMinutesOfDay(startDateTime))
                            .setEndTime(DateUtil.getMinutesOfDay(endDateTime))
                            .setPrice(model.getPrice())
                            .setLodgingUnitName(lodgingInfo.getLodgingUnitName())
                            .setLodgingTypeName(lodgingInfo.getLodgingTypeName())
                            .setIsApplicable(model.getIsApplicable());
                    Optional.ofNullable(lodgingInfo.getLodgingTypeId()).ifPresent(builder::setLodgingTypeId);
                    return builder.build();
                })
                .toList();
    }

    @NotNull
    public static List<BoardingSplitLodgingModel> filterBoardingSplitLodgings(
            final Map<Long, List<BoardingSplitLodgingModel>> boardingSplitLodgingMap,
            final long appointmentId,
            final long petDetailId,
            final LocalDate localDate,
            final Number minutes) {
        return boardingSplitLodgingMap.getOrDefault(appointmentId, List.of()).stream()
                .filter(boardingSplitLodgingModel ->
                        Objects.equals(petDetailId, boardingSplitLodgingModel.getPetDetailId()))
                .filter(boardingSplitLodgingModel -> {
                    if (Objects.isNull(localDate)) {
                        return true;
                    }
                    if (Objects.isNull(minutes)) {
                        return isDateInRange(
                                localDate,
                                DateTimeConverter.INSTANCE.toLocalDateTime(
                                        boardingSplitLodgingModel.getStartDateTime()),
                                DateTimeConverter.INSTANCE.toLocalDateTime(boardingSplitLodgingModel.getEndDateTime()));
                    }
                    return isTimeInRange(
                            localDate,
                            minutes,
                            DateTimeConverter.INSTANCE.toLocalDateTime(boardingSplitLodgingModel.getStartDateTime()),
                            DateTimeConverter.INSTANCE.toLocalDateTime(boardingSplitLodgingModel.getEndDateTime()));
                })
                .toList();
    }

    @NotNull
    public static List<BoardingSplitLodgingModel> filterBoardingSplitLodgingsByPetId(
            final List<BoardingSplitLodgingModel> boardingSplitLodgings,
            final long petId,
            final LocalDate localDate,
            final Number minutes) {
        return boardingSplitLodgings.stream()
                .filter(boardingSplitLodgingModel -> Objects.equals(petId, boardingSplitLodgingModel.getPetId()))
                .filter(boardingSplitLodgingModel -> {
                    if (Objects.isNull(localDate)) {
                        return true;
                    }
                    if (Objects.isNull(minutes)) {
                        return isDateInRange(
                                localDate,
                                DateTimeConverter.INSTANCE.toLocalDateTime(
                                        boardingSplitLodgingModel.getStartDateTime()),
                                DateTimeConverter.INSTANCE.toLocalDateTime(boardingSplitLodgingModel.getEndDateTime()));
                    }
                    return isTimeInRange(
                            localDate,
                            minutes,
                            DateTimeConverter.INSTANCE.toLocalDateTime(boardingSplitLodgingModel.getStartDateTime()),
                            DateTimeConverter.INSTANCE.toLocalDateTime(boardingSplitLodgingModel.getEndDateTime()));
                })
                .toList();
    }

    private static boolean isDateInRange(
            LocalDate serviceDate, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        return !serviceDate.isBefore(startDateTime.toLocalDate()) && !serviceDate.isAfter(endDateTime.toLocalDate());
    }

    private static boolean isTimeInRange(
            LocalDate serviceDate, Number minutes, LocalDateTime startDateTime, LocalDateTime endDateTime) {
        LocalDateTime serviceDateTime =
                serviceDate.atTime(Math.floorDiv(minutes.intValue(), 60), Math.floorMod(minutes.intValue(), 60), 0);
        return !serviceDateTime.isBefore(startDateTime) && !serviceDateTime.isAfter(endDateTime);
    }

    // 按 id 查询 lodging type，包含软删除记录
    public List<LodgingTypeModel> getLodgingTypeByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return new ArrayList<>();
        }
        return lodgingTypeClient
                .mGetLodgingType(
                        MGetLodgingTypeRequest.newBuilder().addAllIdList(idList).build())
                .getLodgingTypeListList();
    }

    // 按 id 查询 lodging type，包含软删除记录
    public MGetLodgingUnitResponse getLodgingUnitByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return MGetLodgingUnitResponse.getDefaultInstance();
        }
        return lodgingUnitClient.mGetLodgingUnit(MGetLodgingUnitRequest.newBuilder()
                .addAllIdList(idList.stream()
                        .filter(k -> k != null && k > 0)
                        .distinct()
                        .toList())
                .build());
    }

    public List<LodgingUnitModel> getLodgingUnit(
            Long companyId, Long businessId, Long serviceId, Long evaluationServiceId) {
        GetLodgingUnitListRequest.Builder getLodgingUnitBuilder =
                GetLodgingUnitListRequest.newBuilder().setCompanyId(companyId).setBusinessId(businessId);
        Optional.ofNullable(serviceId).ifPresent(getLodgingUnitBuilder::setServiceId);
        Optional.ofNullable(evaluationServiceId).ifPresent(getLodgingUnitBuilder::setEvaluationServiceId);
        return lodgingUnitClient
                .getLodgingUnitList(getLodgingUnitBuilder.build())
                .getLodgingUnitListList();
    }

    public List<LodgingUnitModel> getLodgingUnitByUnitIds(long companyId, long businessId, Collection<Long> unitIds) {
        GetLodgingUnitListRequest.Builder getLodgingUnitBuilder = GetLodgingUnitListRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .addAllUnitIds(unitIds);
        return lodgingUnitClient
                .getLodgingUnitList(getLodgingUnitBuilder.build())
                .getLodgingUnitListList();
    }

    public List<LodgingAssignInfo> getLodgingAssignInfo(
            Long companyId, Long businessId, String startDate, String endDate) {
        return lodgingClient
                .lodgingAssignInfo(LodgingAssignInfoRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .setStartDate(startDate)
                        .setEndDate(endDate)
                        .build())
                .getLodgingAssignInfoList();
    }

    public Map<Long, Timestamp> getUnitReleaseTime(
            Long companyId, Long businessId, String date, List<Long> lodgingUnitIds) {
        // 查询 assign info
        var assignInfoList = lodgingClient
                .lodgingAssignInfo(LodgingAssignInfoRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .setStartDate(date)
                        .setEndDate(date)
                        .addAllLodgingIds(lodgingUnitIds)
                        .build())
                .getLodgingAssignInfoList();
        if (CollectionUtils.isEmpty(assignInfoList)) {
            return Map.of();
        }
        // 查询 assign info 内的 appt info
        List<Long> appointmentIds = assignInfoList.stream()
                .map(LodgingAssignInfo::getAppointmentsList)
                .flatMap(List::stream)
                .map(LodgingAssignAppointmentInfo::getId)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(appointmentIds)) {
            return Map.of();
        }
        var apptList = appointmentService.batchGetAppointmentModel(companyId, appointmentIds);
        if (CollectionUtils.isEmpty(apptList)) {
            return Map.of();
        }

        // convert to map by id
        var assignInfoMap = assignInfoList.stream()
                .collect(Collectors.toMap(LodgingAssignInfo::getLodgingId, k -> k, (v1, v2) -> v1));
        var apptMap = apptList.stream().collect(Collectors.toMap(AppointmentModel::getId, k -> k, (v1, v2) -> v1));

        // timezone
        var timezone = companySettingService.mustGetTimeZoneName(companyId);
        var returnMap = new HashMap<Long, Timestamp>();
        for (Long lodgingUnitId : lodgingUnitIds) {
            var assignInfo = assignInfoMap.get(lodgingUnitId);
            if (assignInfo == null) continue;
            // foreach appointment
            for (LodgingAssignAppointmentInfo lodgingAssignAppointmentInfo : assignInfo.getAppointmentsList()) {
                var appt = apptMap.get(lodgingAssignAppointmentInfo.getId());
                if (appt == null || !appt.getAppointmentEndDate().equals(date)) {
                    continue;
                }
                Timestamp releaseTime;
                if (appt.hasCheckOutTime() && appt.getCheckOutTime().getSeconds() != 0L) {
                    releaseTime = appt.getCheckOutTime();
                } else {
                    releaseTime =
                            convertEndDateTime(timezone, appt.getAppointmentEndDate(), appt.getAppointmentEndTime());
                    if (releaseTime == null) {
                        continue;
                    }
                }

                // 最更靠后的那个
                var existing = returnMap.get(lodgingUnitId);
                if (existing == null || existing.getSeconds() < releaseTime.getSeconds()) {
                    returnMap.put(lodgingUnitId, releaseTime);
                }
            }
        }
        return returnMap;
    }

    /**
     * @param timeZoneName 目标时区
     * @param endDate      目标时区的 Y-m-d
     * @param endTime      分钟数 e.g.:540 代表上午 9 点
     * @return Timestamp
     */
    public static Timestamp convertEndDateTime(String timeZoneName, String endDate, Integer endTime) {
        // endTime 合法检测
        int maxMinutes = LocalTime.MAX.getHour() * 60 + LocalTime.MAX.getMinute();
        endTime = Math.min(endTime, maxMinutes);

        try {
            LocalDate localDate = LocalDate.parse(endDate);
            LocalTime localTime = LocalTime.of(endTime / 60, endTime % 60);
            ZonedDateTime zdt = ZonedDateTime.of(localDate, localTime, ZoneId.of(timeZoneName));
            Instant instant = zdt.toInstant();
            return Timestamp.newBuilder()
                    .setSeconds(instant.getEpochSecond())
                    .setNanos(instant.getNano())
                    .build();
        } catch (DateTimeException e) {
            return null;
        }
    }

    public void lodgingTransfer(Long companyId, Long businessId, Long lodgingIdFrom, Long lodgingIdTo) {
        lodgingClient.lodgingTransfer(LodgingTransferRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setLodgingIdFrom(lodgingIdFrom)
                .setLodgingIdTo(lodgingIdTo)
                .build());
    }

    public static List<Long> getAssignedAppointmentId(List<LodgingAssignInfo> assignInfoList) {
        List<Long> appointmentIds = new ArrayList<>();
        if (CollectionUtils.isEmpty(assignInfoList)) {
            return appointmentIds;
        }
        return assignInfoList.stream()
                .map(LodgingAssignInfo::getAppointmentsList)
                .flatMap(List::stream)
                .map(LodgingAssignAppointmentInfo::getId)
                .distinct()
                .collect(Collectors.toList());
    }

    public static Set<Integer> getAssignedPetId(List<LodgingAssignInfo> assignInfoList) {
        Set<Integer> petIds = new HashSet<>();
        if (CollectionUtils.isEmpty(assignInfoList)) {
            return petIds;
        }
        for (LodgingAssignInfo assignInfo : assignInfoList) {
            for (LodgingAssignAppointmentInfo appointmentInfo : assignInfo.getAppointmentsList()) {
                petIds.addAll(appointmentInfo.getPetDetailsList().stream()
                        .map(LodgingAssignPetDetailInfo::getPetId)
                        .filter(k -> k > 0)
                        .toList());
                petIds.addAll(appointmentInfo.getPetEvaluationsList().stream()
                        .map(LodgingAssignPetEvaluationInfo::getPetId)
                        .filter(k -> k > 0)
                        .toList());
            }
        }
        return petIds;
    }

    public static List<LodgingAssignPetDetailInfo> getAssignedPetDetails(List<LodgingAssignInfo> assignInfoList) {
        List<LodgingAssignPetDetailInfo> petDetails = new ArrayList<>();
        if (CollectionUtils.isEmpty(assignInfoList)) {
            return petDetails;
        }
        return assignInfoList.stream()
                .map(LodgingAssignInfo::getAppointmentsList)
                .flatMap(List::stream)
                .map(LodgingAssignAppointmentInfo::getPetDetailsList)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    public static List<LodgingAssignPetEvaluationInfo> getAssignedPetEvaluations(
            List<LodgingAssignInfo> assignInfoList) {
        List<LodgingAssignPetEvaluationInfo> petEvaluations = new ArrayList<>();
        if (CollectionUtils.isEmpty(assignInfoList)) {
            return petEvaluations;
        }
        return assignInfoList.stream()
                .map(LodgingAssignInfo::getAppointmentsList)
                .flatMap(List::stream)
                .map(LodgingAssignAppointmentInfo::getPetEvaluationsList)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    // return <key: date, value: petCnt>
    public static Map<String, GetLodgingCalendarViewV2Result.ExistPetCountView> calculatePetCntStatPerDay(
            String startDate, String endDate, List<LodgingAssignInfo> assignInfoList) {
        Map<String, GetLodgingCalendarViewV2Result.ExistPetCountView> result = new HashMap<>();

        var petCntPerDayPerLodging = calPetCntStatPerDayPerLodging(startDate, endDate, assignInfoList);
        petCntPerDayPerLodging.forEach((date, petCntPerLodging) -> {
            int allPetCnt = 0;
            int allPetCntWithLodging = 0;
            int boardingPetCnt = 0;
            int daycarePetCnt = 0;
            int evaluationPetCnt = 0;
            for (var entry : petCntPerLodging.entrySet()) {
                Long lodgingId = entry.getKey();
                var petCntInfo = entry.getValue();
                allPetCnt += petCntInfo.getPetCountAll();
                if (lodgingId > 0) {
                    allPetCntWithLodging += petCntInfo.getPetCountAll();
                }
                boardingPetCnt += petCntInfo.getPetCountBoarding();
                daycarePetCnt += petCntInfo.getPetCountDaycare();
                evaluationPetCnt += petCntInfo.getPetCountEvaluation();
            }
            result.put(
                    date,
                    GetLodgingCalendarViewV2Result.ExistPetCountView.newBuilder()
                            .setPetCountAll(allPetCnt)
                            .setPetCountWithLodging(allPetCntWithLodging)
                            .setPetCountBoarding(boardingPetCnt)
                            .setPetCountDaycare(daycarePetCnt)
                            .setPetCountEvaluation(evaluationPetCnt)
                            .build());
        });
        return result;
    }

    // return <key: date, value: <key: lodgingUnitId, value: ExistPetCntView>>
    public static Map<String, Map<Long, GetLodgingCalendarViewV2Result.ExistPetCountView>>
            calPetCntStatPerDayPerLodging(String startDate, String endDate, List<LodgingAssignInfo> assignInfoList) {
        // <key: date, value: <key: lodgingId, value: Set<petId>>>
        Map<String, Map<Long, Set<Integer>>> allPets = new HashMap<>();
        Map<String, Map<Long, Set<Integer>>> boardingPets = new HashMap<>();
        Map<String, Map<Long, Set<Integer>>> daycarePets = new HashMap<>();
        Map<String, Map<Long, Set<Integer>>> evaluationPets = new HashMap<>();

        // 收集 pet 寄养信息
        Set<String> allDates = new HashSet<>(DateUtil.generateAllDatesBetween(startDate, endDate));
        for (LodgingAssignInfo assignInfo : assignInfoList) {
            for (LodgingAssignAppointmentInfo appointmentInfo : assignInfo.getAppointmentsList()) {
                for (LodgingAssignPetDetailInfo petDetailInfo : appointmentInfo.getPetDetailsList()) {
                    List<String> dates = petDetailInfo.getSpecificDatesList();
                    if (CollectionUtils.isEmpty(dates)) {
                        dates = DateUtil.generateAllDatesBetween(
                                petDetailInfo.getStartDate(), petDetailInfo.getEndDate());
                    }
                    for (String date : dates) {
                        if (!allDates.contains(date)) {
                            continue;
                        }
                        switch (petDetailInfo.getServiceItemType()) {
                            case BOARDING -> {
                                if (Objects.equals(date, petDetailInfo.getEndDate())) {
                                    continue;
                                }
                                boardingPets
                                        .computeIfAbsent(date, k -> new HashMap<>())
                                        .computeIfAbsent(assignInfo.getLodgingId(), k -> new HashSet<>())
                                        .add(petDetailInfo.getPetId());
                            }
                            case DAYCARE -> daycarePets
                                    .computeIfAbsent(date, k -> new HashMap<>())
                                    .computeIfAbsent(assignInfo.getLodgingId(), k -> new HashSet<>())
                                    .add(petDetailInfo.getPetId());
                            default -> {}
                        }
                        allPets.computeIfAbsent(date, k -> new HashMap<>())
                                .computeIfAbsent(assignInfo.getLodgingId(), k -> new HashSet<>())
                                .add(petDetailInfo.getPetId());
                    }
                }
                for (LodgingAssignPetEvaluationInfo petEvaluationInfo : appointmentInfo.getPetEvaluationsList()) {
                    List<String> dates = DateUtil.generateAllDatesBetween(
                            petEvaluationInfo.getStartDate(), petEvaluationInfo.getEndDate());
                    for (String date : dates) {
                        if (!allDates.contains(date)) {
                            continue;
                        }
                        evaluationPets
                                .computeIfAbsent(date, k -> new HashMap<>())
                                .computeIfAbsent(assignInfo.getLodgingId(), k -> new HashSet<>())
                                .add(petEvaluationInfo.getPetId());
                        allPets.computeIfAbsent(date, k -> new HashMap<>())
                                .computeIfAbsent(assignInfo.getLodgingId(), k -> new HashSet<>())
                                .add(petEvaluationInfo.getPetId());
                    }
                }
            }
        }

        Map<String, Map<Long, GetLodgingCalendarViewV2Result.ExistPetCountView>> result = new HashMap<>();
        allPets.forEach((date, petCntPerLodging) -> {
            Map<Long, GetLodgingCalendarViewV2Result.ExistPetCountView> lodgingPetCntMap = new HashMap<>();
            for (var entry : petCntPerLodging.entrySet()) {
                Long lodgingId = entry.getKey();
                lodgingPetCntMap.put(
                        lodgingId,
                        GetLodgingCalendarViewV2Result.ExistPetCountView.newBuilder()
                                .setPetCountAll(entry.getValue().size())
                                .setPetCountBoarding(boardingPets
                                        .computeIfAbsent(date, k -> new HashMap<>())
                                        .getOrDefault(lodgingId, new HashSet<>())
                                        .size())
                                .setPetCountDaycare(daycarePets
                                        .computeIfAbsent(date, k -> new HashMap<>())
                                        .getOrDefault(lodgingId, new HashSet<>())
                                        .size())
                                .setPetCountEvaluation(evaluationPets
                                        .computeIfAbsent(date, k -> new HashMap<>())
                                        .getOrDefault(lodgingId, new HashSet<>())
                                        .size())
                                .build());
            }
            result.put(date, lodgingPetCntMap);
        });
        return result;
    }

    // return <key: date, value: <key: lodgingUnitId, value: petCnt>>
    public static Map<String, Map<Long, Integer>> calPetCntPerDayPerLodging(
            String startDate, String endDate, List<LodgingAssignInfo> assignInfoList) {
        var petCntPerDayPerLodging = calPetCntStatPerDayPerLodging(startDate, endDate, assignInfoList);
        Map<String, Map<Long, Integer>> result = new HashMap<>();
        petCntPerDayPerLodging.forEach((date, petCntPerLodging) -> {
            Map<Long, Integer> petCntPerLodgingMap = new HashMap<>();
            petCntPerLodging.forEach(
                    (lodgingId, petCnt) -> petCntPerLodgingMap.put(lodgingId, petCnt.getPetCountAll()));
            result.put(date, petCntPerLodgingMap);
        });
        return result;
    }

    /**
     * 根据住宿类型计算每天的容量
     * 如果是 AREA 类型，返回每天所有宠物的总数
     * 如果是 ROOM 类型，返回每天被占用的房间数
     *
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @param lodgingType 住宿类型
     * @param lodgingUnits 住宿单元列表
     * @param petCntPerDayPerLodging 每天每个住宿单元的宠物数量
     * @return 每天的容量统计
     */
    public static Map<String, Integer> calculateCapacityPerDay(
            String startDate,
            String endDate,
            LodgingTypeModel lodgingType,
            List<LodgingUnitModel> lodgingUnits,
            Map<String, Map<Long, Integer>> petCntPerDayPerLodging) {
        if (lodgingType.getLodgingUnitType() == LodgingUnitType.AREA) {
            return calPetCntPerDay(startDate, endDate, lodgingUnits, petCntPerDayPerLodging);
        } else {
            return calOccupiedRoomPerDay(startDate, endDate, lodgingUnits, petCntPerDayPerLodging);
        }
    }

    public static Map<String, Integer> calPetCntPerDay(
            String startDate,
            String endDate,
            List<LodgingUnitModel> lodgingUnits,
            Map<String, Map<Long, Integer>> petCntPerDayPerLodging) {
        Map<String, Integer> petPerDay = new HashMap<>();
        List<String> days = DateUtil.generateAllDatesBetween(startDate, endDate);
        for (String date : days) {
            petPerDay.put(
                    date,
                    lodgingUnits.stream()
                            .map(k -> petCntPerDayPerLodging
                                    .getOrDefault(date, new HashMap<>())
                                    .getOrDefault(k.getId(), 0))
                            .reduce(0, Integer::sum));
        }
        return petPerDay;
    }

    public static Map<String, Integer> calOccupiedRoomPerDay(
            String startDate,
            String endDate,
            List<LodgingUnitModel> lodgingUnits,
            Map<String, Map<Long, Integer>> petCntPerDayPerLodging) {
        Map<String, Integer> capacityPerDay = new HashMap<>();
        List<String> days = DateUtil.generateAllDatesBetween(startDate, endDate);
        for (String date : days) {
            capacityPerDay.put(date, (int) lodgingUnits.stream()
                    .map(k -> petCntPerDayPerLodging
                            .getOrDefault(date, new HashMap<>())
                            .getOrDefault(k.getId(), 0))
                    .filter(k -> k > 0)
                    .count());
        }
        return capacityPerDay;
    }

    // return <key: lodgingUnitId, value: <key: date, value: petCnt>>
    public static Map<Long, Map<String, Integer>> calPetCntPerLodgingPerDay(
            String startDate, String endDate, List<LodgingAssignInfo> assignInfoList) {
        // <key: lodgingId, value: <key: date, value: Set<petId>>>
        Map<Long, Map<String, Set<Integer>>> petsPerLodgingPerDay = new HashMap<>();

        // 收集 pet 寄养信息
        Set<String> datesToCollect = new HashSet<>(DateUtil.generateAllDatesBetween(startDate, endDate));
        for (LodgingAssignInfo assignInfo : assignInfoList) {
            for (LodgingAssignAppointmentInfo appointmentInfo : assignInfo.getAppointmentsList()) {
                appointmentInfo
                        .getPetDetailsList()
                        .forEach(k -> collectPetDetailLodgingCnt(
                                assignInfo.getLodgingId(), k, datesToCollect, petsPerLodgingPerDay));

                appointmentInfo
                        .getPetEvaluationsList()
                        .forEach(k -> collectPetEvaluationLodgingCnt(
                                assignInfo.getLodgingId(), k, datesToCollect, petsPerLodgingPerDay));
            }
        }

        Map<Long, Map<String, Integer>> result = new HashMap<>();
        petsPerLodgingPerDay.forEach((lodgingId, petDateMap) -> {
            Map<String, Integer> petCntMap = new HashMap<>();
            petDateMap.forEach((date, petSet) -> petCntMap.put(date, petSet.size()));
            result.put(lodgingId, petCntMap);
        });
        return result;
    }

    // 收集 pet service detail 占用的 lodging 排期信息
    private static void collectPetDetailLodgingCnt(
            Long lodgingUnitId,
            LodgingAssignPetDetailInfo petDetail,
            Set<String> datesToCollect,
            Map<Long, Map<String, Set<Integer>>> petsPerLodgingPerDay) {
        List<String> dates = petDetail.getSpecificDatesList();
        if (CollectionUtils.isEmpty(dates)) {
            dates = DateUtil.generateAllDatesBetween(petDetail.getStartDate(), petDetail.getEndDate());
        }
        for (String date : dates) {
            if (!datesToCollect.contains(date)) {
                continue;
            }
            petsPerLodgingPerDay
                    .computeIfAbsent(lodgingUnitId, k -> new HashMap<>())
                    .computeIfAbsent(date, k -> new HashSet<>())
                    .add(petDetail.getPetId());
        }
    }

    // 收集 pet service detail 占用的 lodging 排期信息
    private static void collectPetEvaluationLodgingCnt(
            Long lodgingUnitId,
            LodgingAssignPetEvaluationInfo petEvaluation,
            Set<String> allDates,
            Map<Long, Map<String, Set<Integer>>> petsPerLodgingPerDay) {
        List<String> dates = DateUtil.generateAllDatesBetween(petEvaluation.getStartDate(), petEvaluation.getEndDate());
        for (String date : dates) {
            if (!allDates.contains(date)) {
                continue;
            }
            petsPerLodgingPerDay
                    .computeIfAbsent(lodgingUnitId, k -> new HashMap<>())
                    .computeIfAbsent(date, k -> new HashSet<>())
                    .add(petEvaluation.getPetId());
        }
    }

    /**
     * calculate lodging status for each lodging unit per day
     * 1. when every day of the lodging unit is vacant, the status is VACANT
     * 2. when every day of the lodging unit is fully occupied, the status is
     * FULLY_OCCUPIED
     * 3. otherwise, the status is PARTIALLY_OCCUPIED
     *
     * @param startDate
     * @param endDate
     * @param lodgingUnitList
     * @param petCntPerDayPerLodging
     * @return <key: lodgingUnitId, value: LodgingOccupiedStatus>
     */
    public static Map<Long, LodgingOccupiedStatus> calLodgingStatus(
            String startDate,
            String endDate,
            List<LodgingUnitModel> lodgingUnitList,
            Map<String, Map<Long, Integer>> petCntPerDayPerLodging) {
        Map<Long, LodgingOccupiedStatus> result = new HashMap<>();
        if (CollectionUtils.isEmpty(lodgingUnitList)) {
            return result;
        }
        // 初始化所有 lodging 为未使用状态
        for (LodgingUnitModel lodgingUnit : lodgingUnitList) {
            result.put(lodgingUnit.getId(), LodgingOccupiedStatus.VACANT);
        }

        // 计算每一个 lodging 每一天占用状态
        for (LodgingUnitModel lodgingUnit : lodgingUnitList) {
            boolean isAllVacant = true;
            boolean isFullyOccupied = true;
            for (String date : DateUtil.generateAllDatesBetween(startDate, endDate)) {
                Integer petCnt = petCntPerDayPerLodging
                        .getOrDefault(date, new HashMap<>())
                        .getOrDefault(lodgingUnit.getId(), 0);
                if (petCnt > 0) {
                    isAllVacant = false;
                } else {
                    isFullyOccupied = false;
                }
            }
            if (isAllVacant) {
                result.put(lodgingUnit.getId(), LodgingOccupiedStatus.VACANT);
            } else if (isFullyOccupied) {
                result.put(lodgingUnit.getId(), LodgingOccupiedStatus.FULLY_OCCUPIED);
            } else {
                result.put(lodgingUnit.getId(), LodgingOccupiedStatus.PARTIALLY_OCCUPIED);
            }
        }
        return result;
    }

    public static Map<Long, LodgingOccupiedStatus> calLodgingStatusForScheduling(
            List<LodgingTypeModel> lodgingTypeList,
            List<LodgingUnitModel> lodgingUnitList,
            Map<String, Map<Long, Integer>> petCntPerDayPerLodging) {
        Map<Long, LodgingOccupiedStatus> result = new HashMap<>();
        if (CollectionUtils.isEmpty(lodgingUnitList)) {
            return result;
        }
        result = lodgingUnitList.stream()
                .collect(Collectors.toMap(LodgingUnitModel::getId, k -> LodgingOccupiedStatus.VACANT));
        Map<Long, List<LodgingUnitModel>> lodgingType2Unit =
                lodgingUnitList.stream().collect(groupingBy(LodgingUnitModel::getLodgingTypeId));
        for (LodgingTypeModel lodgingType : lodgingTypeList) {
            Map<Long, LodgingOccupiedStatus> status = calLodgingStatusForScheduling(
                    lodgingType, lodgingType2Unit.get(lodgingType.getId()), petCntPerDayPerLodging);
            result.putAll(status);
        }
        return result;
    }

    private static Map<Long, LodgingOccupiedStatus> calLodgingStatusForScheduling(
            LodgingTypeModel lodgingType,
            List<LodgingUnitModel> lodgingUnitList,
            Map<String, Map<Long, Integer>> petCntPerDayPerLodging) {
        Map<Long, LodgingOccupiedStatus> result = new HashMap<>();
        if (CollectionUtils.isEmpty(lodgingUnitList)) {
            return result;
        }
        // 初始化所有 lodging 为未使用状态
        for (LodgingUnitModel lodgingUnit : lodgingUnitList) {
            result.put(lodgingUnit.getId(), LodgingOccupiedStatus.VACANT);
        }

        // 计算每一个 lodging 每一天占用状态
        petCntPerDayPerLodging.forEach((date, petCntPerLodging) -> {
            petCntPerLodging.forEach((lodgingId, petCnt) -> {
                if (!result.containsKey(lodgingId)) {
                    return;
                }
                LodgingOccupiedStatus status = calLodgingStatusPerDay(lodgingType, petCnt);
                if (LODGING_STATUS_ORDER_MAP.get(status) > LODGING_STATUS_ORDER_MAP.get(result.get(lodgingId))) {
                    result.put(lodgingId, status);
                }
            });
        });
        return result;
    }

    private static LodgingOccupiedStatus calLodgingStatusPerDay(LodgingTypeModel lodgingType, Integer petCnt) {
        if (petCnt <= 0) {
            return LodgingOccupiedStatus.VACANT;
        }
        if (petCnt < lodgingType.getMaxPetNum() || UNLIMITED_MAX_PET_NUM.equals(lodgingType.getMaxPetNum())) {
            return LodgingOccupiedStatus.PARTIALLY_OCCUPIED;
        }
        return LodgingOccupiedStatus.FULLY_OCCUPIED;
    }

    public static List<LodgingUnitModel> filterLodgingUnitByOccupiedStatus(
            List<LodgingOccupiedStatus> statuses,
            List<LodgingUnitModel> lodgingUnitList,
            Map<Long, LodgingOccupiedStatus> lodgingUnitStatus) {
        if (CollectionUtils.isEmpty(lodgingUnitList) || CollectionUtils.isEmpty(statuses)) {
            return new ArrayList<>();
        }
        return lodgingUnitList.stream()
                .filter(k -> statuses.contains(lodgingUnitStatus.get(k.getId())))
                .collect(Collectors.toList());
    }

    /**
     * 检查 lodging 容量是否足够寄养
     *
     * @param maxPetNum       lodging 最大寄养宠物数量
     * @param datePetCntNeed  <key: date, value: petCnt> 每一天需要寄养的宠物数量。 petCnt 为 0
     *                        时表示不需要寄养，不检查容量
     * @param datePetCntExist <key: date, value: petCnt> 每一天已经寄养的宠物数量
     * @return 是否有足够的容量寄养
     */
    public static boolean isLodgingAvailable(
            Integer maxPetNum, Map<String, Integer> datePetCntNeed, Map<String, Integer> datePetCntExist) {
        for (var entry : datePetCntNeed.entrySet()) {
            String date = entry.getKey();
            Integer needCnt = entry.getValue();
            if (needCnt == 0) {
                continue;
            }
            if (needCnt + datePetCntExist.getOrDefault(date, 0) > maxPetNum) {
                return false;
            }
        }
        return true;
    }

    public static List<LodgingTypeModel> filterLodgingTypeByPetSize(
            List<LodgingTypeModel> lodgingTypeList, Long petSizeId) {
        if (CollectionUtils.isEmpty(lodgingTypeList)) {
            return new ArrayList<>();
        }
        return lodgingTypeList.stream()
                .filter(k -> {
                    if (!k.getPetSizeFilter()) {
                        return true;
                    }
                    if (petSizeId == null) {
                        return false;
                    }
                    return k.getPetSizeIdsList().contains(petSizeId);
                })
                .collect(Collectors.toList());
    }

    public static List<LodgingTypeModel> filterLodgingTypeByService(
            List<LodgingTypeModel> lodgingTypeList, ServiceBriefView service) {
        if (CollectionUtils.isEmpty(lodgingTypeList)) {
            return new ArrayList<>();
        }
        if (!service.getLodgingFilter()) {
            return lodgingTypeList;
        }
        return lodgingTypeList.stream()
                .filter(k -> service.getCustomizedLodgingsList().contains(k.getId()))
                .collect(Collectors.toList());
    }

    // 计算所有 lodging 最多能寄养的宠物数量
    public static int calculateMaxPetNum(
            List<LodgingTypeModel> lodgingTypeList, List<LodgingUnitModel> lodgingUnitList) {
        Map<Long, Integer> lodgingTypeMaxPetNum = lodgingTypeList.stream()
                .collect(Collectors.toMap(LodgingTypeModel::getId, LodgingTypeModel::getMaxPetNum));
        return lodgingUnitList.stream()
                .map(k -> lodgingTypeMaxPetNum.get(k.getLodgingTypeId()))
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
    }

    // 根据 lodging type 计算所有 lodging 最多能寄养的 pet 数量 or lodging 数量
    public static int calculateTotalCapacity(
            List<LodgingTypeModel> lodgingTypeList, List<LodgingUnitModel> lodgingUnitList) {
        Map<Long, Integer> lodgingTypeMaxPetNum = lodgingTypeList.stream()
                .collect(Collectors.toMap(LodgingTypeModel::getId, k -> switch (k.getLodgingUnitType()) {
                    case ROOM -> 1;
                    case AREA -> k.getMaxPetNum();
                    default -> 0; // default lodging
                }));
        return lodgingUnitList.stream()
                .map(k -> lodgingTypeMaxPetNum.get(k.getLodgingTypeId()))
                .filter(Objects::nonNull)
                .reduce(0, Integer::sum);
    }
}
