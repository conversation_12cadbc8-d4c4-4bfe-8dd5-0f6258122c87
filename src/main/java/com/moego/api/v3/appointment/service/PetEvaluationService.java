package com.moego.api.v3.appointment.service;

import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.business_customer.v1.PetEvaluationModel;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
public class PetEvaluationService {

    /**
     *
     * @param petId target pet id
     * @param petEvaluationDetailMap appt 上每个 pet 的多个 evaluationService
     * @param petEvaluationsMap pet 所有 evaluation 记录，可能缺失，缺失为 0
     * @return
     */
    public static List<PetEvaluationModel> findPetEvaluationModel(
            Long petId,
            Map<Long, List<EvaluationServiceModel>> petEvaluationDetailMap,
            Map<Long, List<PetEvaluationModel>> petEvaluationsMap) {
        // convert evaluation
        Set<Long> evaluationIds = petEvaluationDetailMap.getOrDefault(petId, List.of()).stream()
                .map(EvaluationServiceModel::getServiceId)
                .collect(Collectors.toSet());

        if (CollectionUtils.isEmpty(evaluationIds)) {
            return List.of();
        }

        // convert petEvaluations
        List<PetEvaluationModel> originalList = petEvaluationsMap.getOrDefault(petId, List.of());
        Map<Long, PetEvaluationModel> evaluationMap = originalList.stream()
                .filter(petEvaluation -> evaluationIds.contains(petEvaluation.getEvaluationId()))
                .collect(Collectors.toMap(PetEvaluationModel::getEvaluationId, Function.identity(), (a, b) -> a));

        return evaluationIds.stream()
                .map(id -> evaluationMap.getOrDefault(
                        id,
                        PetEvaluationModel.getDefaultInstance().toBuilder()
                                .setEvaluationId(id)
                                .setPetId(petId)
                                .build()))
                .toList();
    }
}
