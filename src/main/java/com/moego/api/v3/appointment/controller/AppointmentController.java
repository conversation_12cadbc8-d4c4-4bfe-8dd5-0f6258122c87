package com.moego.api.v3.appointment.controller;

import static com.moego.idl.models.offering.v1.ServiceItemType.BOARDING_VALUE;
import static com.moego.idl.models.offering.v1.ServiceItemType.DAYCARE_VALUE;
import static com.moego.idl.models.offering.v1.ServiceItemType.DOG_WALKING_VALUE;
import static com.moego.idl.models.offering.v1.ServiceItemType.EVALUATION;
import static com.moego.idl.models.offering.v1.ServiceItemType.EVALUATION_VALUE;
import static com.moego.idl.models.offering.v1.ServiceItemType.GROOMING;
import static com.moego.idl.models.offering.v1.ServiceItemType.GROOMING_VALUE;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.lib.common.thread.ThreadPool.getSubmitExecutor;
import static java.util.Comparator.comparingInt;
import static java.util.Comparator.comparingLong;
import static java.util.concurrent.CompletableFuture.supplyAsync;
import static java.util.stream.Collectors.groupingBy;
import static java.util.stream.Collectors.toList;

import com.google.protobuf.Timestamp;
import com.google.type.Money;
import com.moego.api.common.Range;
import com.moego.api.v3.appointment.converter.AppointmentConverter;
import com.moego.api.v3.appointment.converter.AutoAssignConverter;
import com.moego.api.v3.appointment.converter.OrderConverter;
import com.moego.api.v3.appointment.converter.PreAuthConverter;
import com.moego.api.v3.appointment.converter.PricingRuleConverter;
import com.moego.api.v3.appointment.dto.LodgingInfoDTO;
import com.moego.api.v3.appointment.service.AppointmentService;
import com.moego.api.v3.appointment.service.OnlineBookingSlotService;
import com.moego.api.v3.appointment.service.PetDetailService;
import com.moego.api.v3.appointment.service.PetEvaluationService;
import com.moego.api.v3.appointment.utils.FeedingMedicationUtil;
import com.moego.api.v3.appointment.utils.FutureService;
import com.moego.api.v3.appointment.utils.LodgingUtil;
import com.moego.api.v3.appointment.utils.OrderUtil;
import com.moego.api.v3.appointment.utils.PackageHelper;
import com.moego.api.v3.appointment.utils.PetDetailUtil;
import com.moego.api.v3.membership.service.MembershipService;
import com.moego.api.v3.offering.service.EvaluationService;
import com.moego.api.v3.offering.service.LodgingService;
import com.moego.api.v3.offering.service.ServiceService;
import com.moego.api.v3.shared.helper.BusinessHelper;
import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.api.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.api.appointment.v1.BatchBookAgainAppointmentParams;
import com.moego.idl.api.appointment.v1.BatchBookAgainAppointmentResult;
import com.moego.idl.api.appointment.v1.BatchCancelAppointmentParams;
import com.moego.idl.api.appointment.v1.BatchCancelAppointmentResult;
import com.moego.idl.api.appointment.v1.BatchQuickCheckInParams;
import com.moego.idl.api.appointment.v1.BatchQuickCheckInResult;
import com.moego.idl.api.appointment.v1.CalculateAppointmentInvoiceParams;
import com.moego.idl.api.appointment.v1.CalculateAppointmentInvoiceResult;
import com.moego.idl.api.appointment.v1.CareCategory;
import com.moego.idl.api.appointment.v1.CreateAppointmentParams;
import com.moego.idl.api.appointment.v1.CreateAppointmentResult;
import com.moego.idl.api.appointment.v1.CustomerComposite;
import com.moego.idl.api.appointment.v1.DailyStatus;
import com.moego.idl.api.appointment.v1.GetAppointmentLodgingParams;
import com.moego.idl.api.appointment.v1.GetAppointmentLodgingResult;
import com.moego.idl.api.appointment.v1.GetAppointmentParams;
import com.moego.idl.api.appointment.v1.GetAppointmentResult;
import com.moego.idl.api.appointment.v1.GetCustomerLastAppointmentParams;
import com.moego.idl.api.appointment.v1.GetCustomerLastAppointmentResult;
import com.moego.idl.api.appointment.v1.GetDaySummaryParams;
import com.moego.idl.api.appointment.v1.GetDaySummaryResult;
import com.moego.idl.api.appointment.v1.GetInProgressEvaluationAppointmentParams;
import com.moego.idl.api.appointment.v1.GetInProgressEvaluationAppointmentResult;
import com.moego.idl.api.appointment.v1.GetServiceSummaryParams;
import com.moego.idl.api.appointment.v1.GetServiceSummaryResult;
import com.moego.idl.api.appointment.v1.GetServiceSummaryResult.CountedByAppointment;
import com.moego.idl.api.appointment.v1.GetServiceSummaryResult.CountedByPet;
import com.moego.idl.api.appointment.v1.ListStaffAppointmentsParams;
import com.moego.idl.api.appointment.v1.ListStaffAppointmentsResult;
import com.moego.idl.api.appointment.v1.PaymentSummary;
import com.moego.idl.api.appointment.v1.PetDaySummary;
import com.moego.idl.api.appointment.v1.PetVaccineComposite;
import com.moego.idl.api.appointment.v1.RescheduleBoardingAppointmentParams;
import com.moego.idl.api.appointment.v1.RescheduleBoardingAppointmentResult;
import com.moego.idl.api.appointment.v1.ServiceDetail;
import com.moego.idl.api.appointment.v1.ServiceItem;
import com.moego.idl.api.appointment.v1.UpdateAppointmentParams;
import com.moego.idl.api.appointment.v1.UpdateAppointmentResult;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentNoteModel;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.BoardingSplitLodgingModel;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.PetDetailDateType;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v1.SelectedAddOnDef;
import com.moego.idl.models.appointment.v1.SelectedEvaluationDef;
import com.moego.idl.models.appointment.v1.SelectedServiceDef;
import com.moego.idl.models.appointment.v1.ServiceOperationModel;
import com.moego.idl.models.appointment.v1.WaitListStatus;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerTagModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeModel;
import com.moego.idl.models.business_customer.v1.PetEvaluationModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.EvaluationModel;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.order.v1.OrderDetailModelV1;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc.AppointmentServiceBlockingStub;
import com.moego.idl.service.appointment.v1.BatchBookAgainAppointmentRequest;
import com.moego.idl.service.appointment.v1.BatchCancelAppointmentRequest;
import com.moego.idl.service.appointment.v1.BatchQuickCheckInRequest;
import com.moego.idl.service.appointment.v1.CalculateAppointmentInvoiceRequest;
import com.moego.idl.service.appointment.v1.CalculateAppointmentInvoiceResponse;
import com.moego.idl.service.appointment.v1.CreateAppointmentRequest;
import com.moego.idl.service.appointment.v1.CreateAppointmentResponse;
import com.moego.idl.service.appointment.v1.GetAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentResponse;
import com.moego.idl.service.appointment.v1.GetInProgressAppointmentRequest;
import com.moego.idl.service.appointment.v1.GetInProgressAppointmentResponse;
import com.moego.idl.service.appointment.v1.GetPetDetailListRequest;
import com.moego.idl.service.appointment.v1.GetPetDetailListResponse;
import com.moego.idl.service.appointment.v1.ListAppointmentsRequest;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.service.appointment.v1.PreviewEstimateOrderResponse;
import com.moego.idl.service.appointment.v1.RescheduleBoardingAppointmentRequest;
import com.moego.idl.service.appointment.v1.UpdateAppointmentRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ListEvaluationRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc.BusinessServiceBlockingStub;
import com.moego.idl.service.organization.v1.QueryStaffByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.idl.utils.v2.OrderBy;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import com.moego.server.customer.api.IPetBelongingService;
import com.moego.server.customer.dto.PetBelongingDTO;
import com.moego.server.grooming.api.IGroomingAppointmentService;
import com.moego.server.grooming.api.IGroomingAppointmentService.ListGroupedAppointmentByDateRangeParam;
import com.moego.server.grooming.api.IGroomingPetDetailService;
import com.moego.server.grooming.client.IAbandonRecordClient;
import com.moego.server.grooming.dto.AppointmentDTO;
import com.moego.server.grooming.dto.PetDetailDTO;
import io.grpc.stub.StreamObserver;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/1/23
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class AppointmentController extends AppointmentServiceGrpc.AppointmentServiceImplBase {

    private final AppointmentServiceBlockingStub appointmentServiceBlockingStub;
    private final FutureService futureService;
    private final MembershipService membershipService;
    private final BusinessServiceBlockingStub businessServiceStub;
    private final IGroomingAppointmentService appointmentApi;
    private final IGroomingPetDetailService petDetailApi;
    private final IPetBelongingService petBelongingApi;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffService;
    private final FeedingMedicationUtil feedingMedicationUtil;
    private final PackageHelper packageHelper;
    private final IAbandonRecordClient abandonRecordClient;
    private final AppointmentService appointmentService;
    private final PetDetailService petDetailService;
    private final ServiceService serviceService;
    private final EvaluationService evaluationService;
    private final LodgingService lodgingService;
    private final PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailStub;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub
            serviceManagementServiceBlockingStub;
    private final com.moego.idl.service.offering.v1.EvaluationServiceGrpc.EvaluationServiceBlockingStub
            evaluationServiceClient;
    private final BusinessHelper businessHelper;
    private final OnlineBookingSlotService onlineBookingSlotService;

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.CREATE_APPOINTMENT})
    public void createAppointment(
            CreateAppointmentParams request, StreamObserver<CreateAppointmentResult> responseObserver) {
        long companyId = AuthContext.get().companyId();
        businessHelper.checkBusinessCompany(companyId, request.getBusinessId());
        // check
        checkPetDetail(request.getPetDetailsList());

        CreateAppointmentRequest createAppointmentRequest =
                AppointmentConverter.INSTANCE.paramsToRequest(request).toBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setStaffId(
                                request.hasCreatedBy()
                                        ? request.getCreatedBy()
                                        : AuthContext.get().staffId())
                        .setCreatedAt(
                                request.hasCreatedAt()
                                        ? request.getCreatedAt()
                                        : Timestamp.newBuilder()
                                                .setSeconds(CommonUtil.get10Timestamp())
                                                .build())
                        .build();
        CreateAppointmentResponse response = appointmentServiceBlockingStub.createAppointment(createAppointmentRequest);

        addPetBelongings(request.getPetBelongingsList(), response.getAppointmentId());

        feedingMedicationUtil.syncPetDetailDef(companyId, request.getPetDetailsList());

        try {
            abandonRecordClient.updateAbandonRecordToRecoveredByCustomer(
                    Math.toIntExact(request.getBusinessId()),
                    Math.toIntExact(request.getAppointment().getCustomerId()),
                    response.getAppointmentId());
        } catch (Exception e) {
            log.error("Failed to create abandon record", e);
            // 非核心逻辑，不影响主流程
        }

        responseObserver.onNext(CreateAppointmentResult.newBuilder()
                .setAppointmentId(response.getAppointmentId())
                .build());
        responseObserver.onCompleted();
    }

    private void checkPetDetail(List<PetDetailDef> petDetails) {
        // check staff is valid
        checkStaff(petDetails);
    }

    private void checkStaff(List<PetDetailDef> petDetails) {
        List<Long> staffIds = petDetails.stream()
                .flatMap(def -> Stream.concat(
                        Stream.concat(
                                def.getServicesList().stream()
                                        .filter(SelectedServiceDef::hasStaffId)
                                        .map(SelectedServiceDef::getStaffId),
                                def.getAddOnsList().stream()
                                        .filter(SelectedAddOnDef::hasStaffId)
                                        .map(SelectedAddOnDef::getStaffId)),
                        def.getEvaluationsList().stream()
                                .filter(SelectedEvaluationDef::hasStaffId)
                                .map(SelectedEvaluationDef::getStaffId)))
                .filter(CommonUtil::isNormal)
                .distinct()
                .toList();
        if (CollectionUtils.isEmpty(staffIds)) {
            return;
        }

        boolean hasDeletedStaff = staffService
                .queryStaffByIds(QueryStaffByIdsRequest.newBuilder()
                        .addAllStaffIds(staffIds)
                        .build())
                .getStaffsList()
                .stream()
                .anyMatch(StaffModel::getIsDeleted);
        if (hasDeletedStaff) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Staff not found");
        }
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.EDIT_APPOINTMENT})
    public void updateAppointment(
            UpdateAppointmentParams request, StreamObserver<UpdateAppointmentResult> responseObserver) {
        UpdateAppointmentRequest updateAppointmentRequest =
                AppointmentConverter.INSTANCE.paramsToRequest(request).toBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(AuthContext.get().businessId())
                        .setStaffId(AuthContext.get().staffId())
                        .build();
        appointmentServiceBlockingStub.updateAppointment(updateAppointmentRequest);

        responseObserver.onNext(UpdateAppointmentResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getAppointment(GetAppointmentParams request, StreamObserver<GetAppointmentResult> responseObserver) {
        long businessId = AuthContext.get().businessId();
        long companyId = AuthContext.get().companyId();
        long appointmentId = request.getAppointmentId();
        GetAppointmentResponse appointmentResponse =
                appointmentServiceBlockingStub.getAppointment(GetAppointmentRequest.newBuilder()
                        .setAppointmentId(appointmentId)
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .build());
        AppointmentModel appointment = appointmentResponse.getAppointment();
        if (appointment.getId() <= 0) {
            responseObserver.onNext(GetAppointmentResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // note
        var noteListFuture = futureService.getAppointmentNoteMap(companyId, List.of(appointmentId));

        // customer
        var customerFuture = futureService.getBusinessCustomerModel(companyId, appointment.getCustomerId());

        // customer is new
        var isNewCustomerFuture = futureService.getIsNewCustomer(companyId, appointment.getCustomerId());

        // customer tag
        var customerTagListFuture = futureService.getCustomerTags(companyId, appointment.getCustomerId());

        // customer address
        var customerPrimaryAddressFuture =
                futureService.getCustomerPrimaryAddress(companyId, appointment.getCustomerId());

        // pet detail
        var petDetailListFuture = futureService.getPetDetailList(companyId, List.of(appointmentId));

        // pet
        var petMapFuture = futureService.getPetMap(companyId, petDetailListFuture);

        // agreement
        var requiredSignFuture = futureService.getRequiredSign(businessId, appointment);

        // estimate order
        var estimatedOrderFuture = futureService.listEstimatedOrder(companyId, businessId, List.of(appointmentId));

        // orders
        var ordersFuture = futureService.listOrdersByAppointmentId(companyId, appointmentId);

        // invoice
        var invoiceFuture = futureService.getInvoiceCalendarView(appointmentId);

        // no show
        var noShowInvoiceFuture = futureService.getNoShowInvoiceCalendarView(appointmentId);

        // deposit
        var depositFuture = futureService.getBookOnlineDeposit(businessId, appointment);

        // pre pay
        var prePayFuture = futureService.getPrePayCalendarView(invoiceFuture, depositFuture);

        // pre auth
        var preAuthFuture = futureService.getPreAuth(businessId, appointment);

        // auto assign
        var autoAssignFuture = futureService.getAutoAssign(appointment);

        // review booster
        var reviewBoosterSentFuture = futureService.getReviewBoosterSent(businessId, appointment);

        // wait list
        var waitListCalendarViewFuture = futureService.getWaitListCalendarView(appointment, companyId, appointmentId);

        // pet code
        var petCodeMapFuture = futureService.getPetCodeMap(petDetailListFuture, companyId);

        // pet evaluations
        var petEvaluationsMapFuture = futureService.getPetEvaluationsMap(petDetailListFuture, companyId);

        // vaccine
        var petVaccineMapFuture = futureService.getPetVaccineMap(petDetailListFuture, companyId);

        // service
        var serviceMapFuture = futureService.getServiceMap(companyId, petDetailListFuture);

        // operation
        var serviceOperationMapFuture = futureService.getServiceOperationMap(companyId, appointmentId);

        // boarding lodging split
        var boardingLodgingSplitFuture = futureService.getBoardingSplitLodgingMap(List.of(appointmentId));

        // staff
        var staffMapFuture = futureService.getStaffMap(petDetailListFuture, serviceOperationMapFuture);

        // lodging
        var lodgingMapFuture = futureService.getLodgingMap(petDetailListFuture, boardingLodgingSplitFuture);

        // start at the same time
        var startAtSameTimeFuture = futureService.getStartAtSameTime(petDetailListFuture);

        // service types
        var serviceTypeFuture = futureService.getServiceTypeFuture(appointment);

        // evaluation
        var evaluationFuture = futureService.getEvaluationMap(petDetailListFuture);

        // memberships
        final var membershipSubscriptionsFuture =
                futureService.supply(() -> membershipService.getSubscriptions(appointment.getCustomerId()));

        // pricing rule apply log
        var pricingRuleApplyLogListFuture =
                futureService.getPricingRuleApplyLogList(companyId, appointmentId, petDetailListFuture);

        // customer package
        var customerPackageFuture = packageHelper.listCustomerPackages(
                companyId, appointment.getBusinessId(), List.of(appointment.getCustomerId()));
        var customerPackageDetailFuture = packageHelper.listCustomerPackageDetails(customerPackageFuture);

        // slot free service
        final var slotFreeServiceFuture = staffMapFuture.thenApplyAsync(
                staffMap -> onlineBookingSlotService.getSlotFreeServices(companyId, businessId, staffMap.keySet()),
                ThreadPool.getSubmitExecutor());

        CompletableFuture.allOf(
                        noteListFuture,
                        customerFuture,
                        isNewCustomerFuture,
                        customerTagListFuture,
                        customerPrimaryAddressFuture,
                        petDetailListFuture,
                        petMapFuture,
                        requiredSignFuture,
                        estimatedOrderFuture,
                        ordersFuture,
                        invoiceFuture,
                        noShowInvoiceFuture,
                        depositFuture,
                        prePayFuture,
                        preAuthFuture,
                        autoAssignFuture,
                        reviewBoosterSentFuture,
                        waitListCalendarViewFuture,
                        petCodeMapFuture,
                        petEvaluationsMapFuture,
                        petVaccineMapFuture,
                        serviceMapFuture,
                        serviceOperationMapFuture,
                        staffMapFuture,
                        lodgingMapFuture,
                        startAtSameTimeFuture,
                        serviceTypeFuture,
                        evaluationFuture,
                        membershipSubscriptionsFuture,
                        pricingRuleApplyLogListFuture,
                        customerPackageFuture,
                        customerPackageDetailFuture,
                        boardingLodgingSplitFuture,
                        slotFreeServiceFuture)
                .join();

        List<ServiceDetail> petDetailCompositeList = getServiceDetails(
                petDetailListFuture.join().getPetDetailsList(),
                petDetailListFuture.join().getPetEvaluationsList(),
                petMapFuture.join(),
                serviceMapFuture.join(),
                lodgingMapFuture.join().getKey(),
                lodgingMapFuture.join().getValue(),
                staffMapFuture.join(),
                serviceOperationMapFuture.join(),
                petCodeMapFuture.join(),
                petEvaluationsMapFuture.join(),
                petVaccineMapFuture.join(),
                evaluationFuture.join(),
                boardingLodgingSplitFuture.join(),
                slotFreeServiceFuture.join());

        responseObserver.onNext(GetAppointmentResult.newBuilder()
                .setAppointment(
                        AppointmentConverter.INSTANCE.modelToCalendarView(appointment, startAtSameTimeFuture.join()))
                .addAllServiceDetail(petDetailCompositeList)
                .addAllNotes(noteListFuture.join().getOrDefault(appointmentId, List.of()))
                .setCustomer(CustomerComposite.newBuilder()
                        .setCustomerProfile(customerFuture.join())
                        .addAllCustomerTags(customerTagListFuture.join())
                        .setCustomerAddress(customerPrimaryAddressFuture.join())
                        .setIsNewCustomer(isNewCustomerFuture.join())
                        .setRequiredSign(requiredSignFuture.join())
                        .setReviewBoosterSent(reviewBoosterSentFuture.join())
                        .addAllCustomerPackages(PackageHelper.getPackageViews(
                                customerPackageFuture.join(),
                                customerPackageDetailFuture.join(),
                                appointment.getCustomerId()))
                        .build())
                .setPaymentSummary(buildPaymentSummary(
                        estimatedOrderFuture
                                .join()
                                .getOrDefault(
                                        appointmentId,
                                        PreviewEstimateOrderResponse.EstimatedOrder.getDefaultInstance()),
                        ordersFuture.join()))
                .addAllOrders(ordersFuture.join().stream()
                        .map(o -> OrderConverter.INSTANCE.toView(o.getOrder()))
                        .toList())
                .setInvoice(invoiceFuture.join())
                .setNoShowInvoice(noShowInvoiceFuture.join())
                .setPrePay(prePayFuture.join())
                .setPreAuth(PreAuthConverter.INSTANCE.toView(preAuthFuture.join()))
                .setAutoAssign(AutoAssignConverter.INSTANCE.toView(autoAssignFuture.join()))
                .setWaitList(waitListCalendarViewFuture.join())
                .addAllStaffs(staffMapFuture.join().values())
                .addAllServiceItemTypes(serviceTypeFuture.join())
                .setMembershipSubscriptions(membershipSubscriptionsFuture.join())
                .addAllPricingRuleApplyLogsV2(
                        PricingRuleConverter.INSTANCE.toDrawerView(pricingRuleApplyLogListFuture.join()))
                .build());
        responseObserver.onCompleted();
    }

    private static PaymentSummary buildPaymentSummary(
            PreviewEstimateOrderResponse.EstimatedOrder estimatedOrder, List<OrderDetailModelV1> orderDetails) {
        var orders = orderDetails.stream()
                .map(o -> OrderConverter.INSTANCE.toView(o.getOrder()))
                .toList();
        return PaymentSummary.newBuilder()
                .setSubtotalAmount(estimatedOrder.getEstimatedTotal())
                .setCollectedAmount(OrderUtil.calculateTotalPaidAmount(orders))
                .setDepositAmount(OrderUtil.calculateDepositTotalAmount(orders))
                .setCollectedDepositAmount(OrderUtil.calculateDepositPaidAmount(orders))
                .setUseStoreCredit(OrderUtil.hasUseStoreCredit(orderDetails))
                .setCollectedTipsAmount(OrderUtil.calculateTipsAmount(orders))
                .build();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getCustomerLastAppointment(
            GetCustomerLastAppointmentParams request,
            StreamObserver<GetCustomerLastAppointmentResult> responseObserver) {
        long businessId = AuthContext.get().businessId();
        long companyId = AuthContext.get().companyId();
        long customerId = request.getCustomerId();
        long selectedBusinessId = request.getSelectedBusinessId();

        // appointment
        CompletableFuture<Map<Long, AppointmentModel>> lastAppointmentMapFuture =
                futureService.getCustomerLastAppointmentMap(companyId, List.of(customerId));

        // customer
        CompletableFuture<BusinessCustomerModel> customerFuture =
                futureService.getBusinessCustomerModel(companyId, customerId);

        // customer tag
        CompletableFuture<List<BusinessCustomerTagModel>> customerTagListFuture =
                futureService.getCustomerTags(companyId, customerId);

        // customer address
        CompletableFuture<BusinessCustomerAddressModel> customerPrimaryAddressFuture =
                futureService.getCustomerPrimaryAddress(companyId, customerId);

        // customer last alert note
        CompletableFuture<AppointmentNoteModel> lastAlertNoteFuture =
                futureService.getCustomerLastAlertNoteFuture(companyId, customerId);

        // new customer
        CompletableFuture<Boolean> isNewCustomerFuture = futureService.getIsNewCustomer(companyId, customerId);

        // customer package
        var customerPackageFuture =
                packageHelper.listCustomerPackages(companyId, selectedBusinessId, List.of(customerId));
        var customerPackageDetailFuture = packageHelper.listCustomerPackageDetails(customerPackageFuture);

        CompletableFuture.allOf(
                        lastAppointmentMapFuture,
                        customerFuture,
                        customerTagListFuture,
                        customerPrimaryAddressFuture,
                        lastAlertNoteFuture,
                        isNewCustomerFuture,
                        customerPackageFuture,
                        customerPackageDetailFuture)
                .join();

        Map<Long, AppointmentModel> lastAppointmentMap = lastAppointmentMapFuture.join();
        if (CollectionUtils.isEmpty(lastAppointmentMap) || !lastAppointmentMap.containsKey(customerId)) {
            responseObserver.onNext(GetCustomerLastAppointmentResult.newBuilder()
                    .setHasLastAppointment(false)
                    .setCustomer(CustomerComposite.newBuilder()
                            .setCustomerProfile(customerFuture.join())
                            .addAllCustomerTags(customerTagListFuture.join())
                            .setCustomerAddress(customerPrimaryAddressFuture.join())
                            .setIsNewCustomer(isNewCustomerFuture.join())
                            .setLastAlertNote(lastAlertNoteFuture.join())
                            .addAllCustomerPackages(PackageHelper.getPackageViews(
                                    customerPackageFuture.join(), customerPackageDetailFuture.join(), customerId))
                            .build())
                    .build());
            responseObserver.onCompleted();
            return;
        }

        AppointmentModel appointment = lastAppointmentMap.get(customerId);
        long appointmentId = appointment.getId();

        // note
        var noteListFuture = futureService.getAppointmentNoteMap(companyId, List.of(appointmentId));

        // pet detail
        var petDetailListFuture = futureService.getPetDetailList(companyId, List.of(appointmentId));

        // pet
        var petMapFuture = futureService.getPetMap(companyId, petDetailListFuture);

        // agreement
        var requiredSignFuture = futureService.getRequiredSign(businessId, appointment);

        // pet code
        var petCodeMapFuture = futureService.getPetCodeMap(petDetailListFuture, companyId);

        // pet evaluations
        var petEvaluationsMapFuture = futureService.getPetEvaluationsMap(petDetailListFuture, companyId);

        // vaccine
        var petVaccineMapFuture = futureService.getPetVaccineMap(petDetailListFuture, companyId);

        // service
        var serviceMapFuture = futureService.getServiceMap(companyId, petDetailListFuture);

        // operation
        var serviceOperationMapFuture = futureService.getServiceOperationMap(companyId, appointmentId);

        // boarding lodging split
        var boardingLodgingSplitFuture = futureService.getBoardingSplitLodgingMap(List.of(appointmentId));

        // staff
        var staffMapFuture = futureService.getStaffMap(petDetailListFuture, serviceOperationMapFuture);

        // lodging
        var lodgingMapFuture = futureService.getLodgingMap(petDetailListFuture, boardingLodgingSplitFuture);

        // start at the same time
        var startAtSameTimeFuture = futureService.getStartAtSameTime(petDetailListFuture);

        // service types
        var serviceTypeFuture = futureService.getServiceTypeFuture(appointment);

        // evaluation
        var evaluationFuture = futureService.getEvaluationMap(petDetailListFuture);

        // pricing rule apply log
        var pricingRuleApplyLogListFuture =
                futureService.getPricingRuleApplyLogList(companyId, appointmentId, petDetailListFuture);

        CompletableFuture.allOf(
                        noteListFuture,
                        customerFuture,
                        petDetailListFuture,
                        petMapFuture,
                        requiredSignFuture,
                        petCodeMapFuture,
                        petEvaluationsMapFuture,
                        petVaccineMapFuture,
                        serviceMapFuture,
                        serviceOperationMapFuture,
                        staffMapFuture,
                        lodgingMapFuture,
                        startAtSameTimeFuture,
                        serviceTypeFuture,
                        evaluationFuture,
                        pricingRuleApplyLogListFuture,
                        boardingLodgingSplitFuture)
                .join();

        List<ServiceDetail> petDetailCompositeList = getServiceDetails(
                petDetailListFuture.join().getPetDetailsList(),
                petDetailListFuture.join().getPetEvaluationsList(),
                petMapFuture.join(),
                serviceMapFuture.join(),
                lodgingMapFuture.join().getKey(),
                lodgingMapFuture.join().getValue(),
                staffMapFuture.join(),
                serviceOperationMapFuture.join(),
                petCodeMapFuture.join(),
                petEvaluationsMapFuture.join(),
                petVaccineMapFuture.join(),
                evaluationFuture.join(),
                boardingLodgingSplitFuture.join(),
                Map.of());

        responseObserver.onNext(GetCustomerLastAppointmentResult.newBuilder()
                .setHasLastAppointment(true)
                .setAppointment(
                        AppointmentConverter.INSTANCE.modelToCalendarView(appointment, startAtSameTimeFuture.join()))
                .addAllServiceDetail(petDetailCompositeList)
                .addAllNotes(noteListFuture.join().getOrDefault(appointmentId, List.of()))
                .setCustomer(CustomerComposite.newBuilder()
                        .setCustomerProfile(customerFuture.join())
                        .addAllCustomerTags(customerTagListFuture.join())
                        .setCustomerAddress(customerPrimaryAddressFuture.join())
                        .setIsNewCustomer(isNewCustomerFuture.join())
                        .setRequiredSign(requiredSignFuture.join())
                        .setLastAlertNote(lastAlertNoteFuture.join())
                        .addAllCustomerPackages(PackageHelper.getPackageViews(
                                customerPackageFuture.join(), customerPackageDetailFuture.join(), customerId))
                        .build())
                .addAllStaffs(staffMapFuture.join().values())
                .addAllServiceItemTypes(serviceTypeFuture.join())
                .addAllPricingRuleApplyLogsV2(
                        PricingRuleConverter.INSTANCE.toDrawerView(pricingRuleApplyLogListFuture.join()))
                .build());
        responseObserver.onCompleted();
    }

    private static List<ServiceDetail> getServiceDetails(
            List<PetDetailModel> petDetailList,
            List<EvaluationServiceModel> petEvaluationList,
            Map<Long, BusinessCustomerPetModel> petMap,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap,
            Map<Long, StaffModel> staffMap,
            Map<Long, List<ServiceOperationModel>> serviceOperationMap,
            Map<Long, List<BusinessPetCodeModel>> petCodeMap,
            Map<Long, List<PetEvaluationModel>> petEvaluationsMap,
            Map<Long, List<PetVaccineComposite>> petVaccineMap,
            Map<Long, EvaluationBriefView> evaluationMap,
            Map<Long, List<BoardingSplitLodgingModel>> boardingSplitLodgingMap,
            final Map<Long, Set<Long>> slotFreeServiceMap) {
        // 兼容历史数据，根据 serviceId 获取 serviceType
        List<PetDetailModel> newPetDetailList = petDetailList.stream()
                .map(petDetailModel -> {
                    ServiceBriefView serviceBriefView = serviceMap.get(petDetailModel.getServiceId());
                    if (Objects.isNull(serviceBriefView)) {
                        log.error("service not found, serviceId: {}", petDetailModel.getServiceId());
                        return null;
                    }
                    return petDetailModel.toBuilder()
                            .setServiceType(serviceBriefView.getType())
                            .build();
                })
                .filter(Objects::nonNull)
                .toList();

        boolean groomingOnly = newPetDetailList.stream()
                .allMatch(petDetail -> Objects.equals(petDetail.getServiceItemType(), GROOMING));

        // 根据 petId 分组，取每个 pet 最旧的服务，grooming only 开始时间排序，bd 按更新时间和开始时间排序
        Set<Long> petIdList = new HashSet<>(newPetDetailList.stream()
                .collect(Collectors.groupingBy(
                        PetDetailModel::getPetId, Collectors.minBy(comparingLong(PetDetailModel::getUpdateTime))))
                .values()
                .stream()
                .filter(Optional::isPresent)
                .map(Optional::get)
                .sorted(
                        groomingOnly
                                ? comparingInt(PetDetailModel::getStartTime)
                                        .thenComparing(PetDetailModel::getUpdateTime)
                                : comparingLong(PetDetailModel::getUpdateTime)
                                        .thenComparingInt(PetDetailModel::getStartTime))
                .map(PetDetailModel::getPetId)
                .toList());
        petIdList.addAll(
                petEvaluationList.stream().map(EvaluationServiceModel::getPetId).toList());

        Map<Long, List<PetDetailModel>> petDetailMap =
                newPetDetailList.stream().collect(Collectors.groupingBy(PetDetailModel::getPetId, toList()));
        Map<Long, List<EvaluationServiceModel>> petEvaluationDetailMap = petEvaluationList.stream()
                .collect(Collectors.groupingBy(EvaluationServiceModel::getPetId, Collectors.toList()));
        return petIdList.stream()
                .map(petId -> {
                    Map<ServiceType, List<PetDetailModel>> serviceTypeMap =
                            petDetailMap.getOrDefault(petId, List.of()).stream()
                                    .collect(groupingBy(
                                            PetDetailModel::getServiceType,
                                            Collectors.collectingAndThen(
                                                    Collectors.toCollection(ArrayList::new), list -> list.stream()
                                                            .sorted(comparingLong(PetDetailModel::getUpdateTime))
                                                            .toList())));

                    return ServiceDetail.newBuilder()
                            .setPet(petMap.getOrDefault(petId, BusinessCustomerPetModel.getDefaultInstance()))
                            .addAllServices(serviceTypeMap.getOrDefault(ServiceType.SERVICE, List.of()).stream()
                                    .map(petDetailModel -> {
                                        var boardingSplitLodgingModels = LodgingUtil.filterBoardingSplitLodgings(
                                                boardingSplitLodgingMap,
                                                petDetailModel.getGroomingId(),
                                                petDetailModel.getId(),
                                                null,
                                                null);
                                        var splitLodgingDetailDefs = LodgingUtil.getBoardingSplitLodgingDefs(
                                                lodgingUnitMap, lodgingTypeMap, boardingSplitLodgingModels);

                                        var lodgingInfo = CollectionUtils.isEmpty(splitLodgingDetailDefs)
                                                ? LodgingUtil.getLodgingInfo(
                                                        lodgingUnitMap, lodgingTypeMap, petDetailModel.getLodgingId())
                                                : new LodgingInfoDTO();

                                        var isSlotFreeService = slotFreeServiceMap
                                                .getOrDefault(petDetailModel.getStaffId(), Set.of())
                                                .contains(petDetailModel.getServiceId());

                                        return PetDetailUtil.buildServiceComposite(
                                                petDetailModel,
                                                lodgingInfo.getLodgingUnitName(),
                                                lodgingInfo.getLodgingTypeName(),
                                                staffMap.get(petDetailModel.getStaffId()),
                                                serviceMap.get(petDetailModel.getServiceId()),
                                                serviceOperationMap.get(petDetailModel.getId()),
                                                splitLodgingDetailDefs,
                                                isSlotFreeService);
                                    })
                                    .toList())
                            .addAllEvaluations(petEvaluationDetailMap.getOrDefault(petId, List.of()).stream()
                                    .map(evaluation -> PetDetailUtil.buildPetEvaluationComposite(
                                            evaluation, evaluationMap, staffMap, lodgingTypeMap, lodgingUnitMap))
                                    .toList())
                            .addAllAddOns(serviceTypeMap.getOrDefault(ServiceType.ADDON, List.of()).stream()
                                    .map(petDetailModel -> PetDetailUtil.buildAddOnComposite(
                                            petDetailModel,
                                            staffMap.get(petDetailModel.getStaffId()),
                                            serviceMap.get(petDetailModel.getServiceId()),
                                            serviceOperationMap.get(petDetailModel.getId())))
                                    .toList())
                            .addAllPetCodes(petCodeMap.get(petId))
                            .addAllPetEvaluations(PetEvaluationService.findPetEvaluationModel(
                                    petId, petEvaluationDetailMap, petEvaluationsMap))
                            .addAllVaccines(petVaccineMap.getOrDefault(petId, List.of()))
                            .build();
                })
                .toList();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void calculateAppointmentInvoice(
            CalculateAppointmentInvoiceParams request,
            StreamObserver<CalculateAppointmentInvoiceResult> responseObserver) {
        CalculateAppointmentInvoiceRequest calculateAppointmentInvoiceRequest =
                CalculateAppointmentInvoiceRequest.newBuilder()
                        .addAllPetDetails(request.getPetDetailsList())
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(AuthContext.get().businessId())
                        .setStaffId(AuthContext.get().staffId())
                        .build();
        CalculateAppointmentInvoiceResponse response =
                appointmentServiceBlockingStub.calculateAppointmentInvoice(calculateAppointmentInvoiceRequest);

        responseObserver.onNext(CalculateAppointmentInvoiceResult.newBuilder()
                .setOrder(response.getOrder())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getInProgressEvaluationAppointment(
            GetInProgressEvaluationAppointmentParams request,
            StreamObserver<GetInProgressEvaluationAppointmentResult> responseObserver) {
        GetInProgressAppointmentResponse response =
                appointmentServiceBlockingStub.getInProgressAppointment(GetInProgressAppointmentRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(request.getBusinessId())
                        .setCustomerId(request.getCustomerId())
                        .setPetId(request.getPetId())
                        .setServiceItemType(EVALUATION)
                        .build());

        var result = GetInProgressEvaluationAppointmentResult.newBuilder();
        if (response.hasAppointmentId()) {
            result.setAppointmentId(response.getAppointmentId());
        }
        responseObserver.onNext(result.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getDaySummary(GetDaySummaryParams request, StreamObserver<GetDaySummaryResult> r) {
        businessHelper.checkBusinessCompany(AuthContext.get().companyId(), request.getBusinessId());
        // 通过date和businessID拉appointmentList
        var appointmentList = getAppointmentList(request.getDate(), (int) request.getBusinessId());
        // 通过appointmentList拉petDetailList
        var petDetailRsp = getPetDetailRsp(appointmentList);
        // 通过petDetailList中去重的serviceID拉service表
        var serviceNameMap = getServiceIDNameMap(petDetailRsp);
        // 组包，卡片类型和服务类型的拼接
        var careCategoryList =
                getDaySummaryCareCategory(request.getDate(), petDetailRsp, serviceNameMap, appointmentList);
        // 算in、out、overnight、total数据
        var petTotal = getPetTotalData(request.getDate(), petDetailRsp);
        // 写回包
        r.onNext(buildGetDaySummaryResult(
                request.getDate(), petTotal.in, petTotal.out, petTotal.overnight, petTotal.total, careCategoryList));
        r.onCompleted();
    }

    private record PetTotal(Integer in, Integer out, Integer overnight, Integer total) {}

    private PetTotal getPetTotalData(String date, GetPetDetailListResponse petDetailListResponse) {
        var petDetailList = petDetailListResponse.getPetDetailsList();
        var evaluateList = petDetailListResponse.getPetEvaluationsList();
        var petDetailsWithService = getServiceList(petDetailList);
        var in = getDaySummaryIn(date, petDetailsWithService, evaluateList);
        var out = getDaySummaryOut(date, petDetailsWithService, evaluateList);
        var overnight = getDaySummaryOvernight(date, petDetailsWithService, evaluateList);
        int total = (int)
                Stream.of(in, out, overnight).flatMap(Set::stream).distinct().count();
        return new PetTotal(in.size(), out.size(), overnight.size(), total);
    }

    private Set<Long> getDaySummaryIn(
            String date, List<PetDetailModel> petDetailList, List<EvaluationServiceModel> evaluationServiceModelList) {
        Set<Long> resultSet = new HashSet<>();
        // 合并两个流
        Stream.concat(
                        petDetailList.stream()
                                .filter(e -> Objects.equals(e.getStartDate(), date))
                                .map(PetDetailModel::getPetId),
                        evaluationServiceModelList.stream()
                                .filter(e -> Objects.equals(e.getStartDate(), date))
                                .map(EvaluationServiceModel::getPetId))
                .forEach(resultSet::add);
        return resultSet;
    }

    private Set<Long> getDaySummaryOut(
            String date, List<PetDetailModel> petDetailList, List<EvaluationServiceModel> evaluationServiceModelList) {
        Set<Long> resultSet = new HashSet<>();
        // 合并两个流
        Stream.concat(
                        petDetailList.stream()
                                .filter(e -> Objects.equals(e.getEndDate(), date))
                                .map(PetDetailModel::getPetId),
                        evaluationServiceModelList.stream()
                                .filter(e -> Objects.equals(e.getEndDate(), date))
                                .map(EvaluationServiceModel::getPetId))
                .forEach(resultSet::add);
        return resultSet;
    }

    private static final String ADDON = "ADDON";
    private static final String EVALUATE = "EVALUATE";

    private Set<Long> getDaySummaryOvernight(
            String date, List<PetDetailModel> petDetailList, List<EvaluationServiceModel> evaluationServiceModelList) {
        Set<Long> result = new HashSet<>();
        result.addAll(filterByDateAndGetPetIds(
                petDetailList,
                date,
                PetDetailModel::getStartDate,
                PetDetailModel::getEndDate,
                PetDetailModel::getPetId));
        result.addAll(filterByDateAndGetPetIds(
                evaluationServiceModelList,
                date,
                EvaluationServiceModel::getStartDate,
                EvaluationServiceModel::getEndDate,
                EvaluationServiceModel::getPetId));
        return result;
    }

    private <T> Set<Long> filterByDateAndGetPetIds(
            List<T> list,
            String date,
            Function<T, String> startDateGetter,
            Function<T, String> endDateGetter,
            Function<T, Long> petIdGetter) {
        return list.stream()
                .filter(e -> isValidDateRange(startDateGetter.apply(e), endDateGetter.apply(e), date))
                .map(petIdGetter)
                .collect(Collectors.toSet());
    }

    private boolean isValidDateRange(String startDate, String endDate, String targetDate) {
        return StringUtils.hasText(startDate)
                && StringUtils.hasText(endDate)
                && startDate.compareTo(targetDate) <= 0
                && endDate.compareTo(targetDate) > 0;
    }

    private List<PetDetailModel> getServiceList(List<PetDetailModel> petDetailModelList) {
        return petDetailModelList.stream()
                .filter(petDetail -> petDetail.getServiceType() == ServiceType.SERVICE) // SERVICE_TYPE = 1
                .toList();
    }

    private List<PetDetailModel> getAddonList(List<PetDetailModel> petDetailModelList) {
        return petDetailModelList.stream()
                .filter(petDetail -> petDetail.getServiceType() == ServiceType.ADDON) // SERVICE_TYPE = 2
                .toList();
    }

    private Map<String, List<ServiceItem>> getServiceItems(
            String date,
            List<PetDetailModel> petDetailModelList,
            Map<Long, ServiceItemType> serviceIdToServiceItemTypeMap,
            Map<Long, String> serviceNameMap,
            Map<Integer, AppointmentDTO> appointmentIDMap) {
        Map<String, List<ServiceItem>> serviceItemTypeListMap = new HashMap<>();
        Map<Long, List<PetDetailModel>> groupedByServiceId =
                petDetailModelList.stream().collect(groupingBy(PetDetailModel::getServiceId));
        groupedByServiceId.forEach((serviceId, petDetails) -> {
            List<PetDetailModel> distinctPetDetails = petDetails.stream()
                    .collect(Collectors.toMap(
                            PetDetailModel::getPetId, // key: petId
                            pet -> pet, // value: PetDetailModel
                            (existing, replacement) -> existing))
                    .values()
                    .stream()
                    .toList();
            var validDistinctPetDetails = distinctPetDetails.stream()
                    .filter(distinctPetDetail -> verifyReservation(date, distinctPetDetail, appointmentIDMap))
                    .toList();
            var serviceItemType = serviceIdToServiceItemTypeMap.get(serviceId);
            if (!validDistinctPetDetails.isEmpty()) {
                serviceItemTypeListMap
                        .computeIfAbsent(serviceItemType.name(), k -> new ArrayList<>())
                        .add(ServiceItem.newBuilder()
                                .setName(serviceNameMap.get(serviceId))
                                .setTotal(validDistinctPetDetails.size())
                                .build());
            }
        });
        return serviceItemTypeListMap;
    }

    private CareCategory buildEvaluateCareCategoryList(String date, List<EvaluationServiceModel> evaluationModelList) {
        // 要获取evaluate详情
        List<Long> evaluationIdList = evaluationModelList.stream()
                .map(EvaluationServiceModel::getServiceId)
                .distinct()
                .collect(Collectors.toList());
        var listEvaluationReq = ListEvaluationRequest.newBuilder()
                .setFilter(ListEvaluationRequest.Filter.newBuilder()
                        .addCompanyIds(AuthContext.get().companyId())
                        .addAllIds(evaluationIdList)
                        .build())
                .build();
        var evaluationModels =
                evaluationServiceClient.listEvaluation(listEvaluationReq).getEvaluationsList();
        // 拼evaluationId和name的映射
        Map<Long, String> idToNameMap = Optional.of(evaluationModels).orElse(Collections.emptyList()).stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(
                        EvaluationModel::getId,
                        model -> {
                            model.getName();
                            return model.getName();
                        },
                        (oldValue, newValue) -> oldValue));
        List<ServiceItem> serviceItems = new ArrayList<>();
        // 通过group算total
        Map<Long, List<EvaluationServiceModel>> groupedByServiceId =
                evaluationModelList.stream().collect(groupingBy(EvaluationServiceModel::getServiceId));
        groupedByServiceId.forEach((evaluationId, evaluationServiceModels) -> {
            List<EvaluationServiceModel> distinctPetDetails = evaluationServiceModels.stream()
                    .collect(Collectors.toMap(
                            EvaluationServiceModel::getPetId, // key: groomingId
                            pet -> pet, // value: PetDetailModel
                            (existing, replacement) -> existing // 如果重复，保留第一个
                            ))
                    .values()
                    .stream()
                    .toList();
            var validDistinctPetDetails = distinctPetDetails.stream()
                    .filter(distinctPetDetail ->
                            isDateInRange(date, distinctPetDetail.getStartDate(), distinctPetDetail.getEndDate()))
                    .toList();
            if (!validDistinctPetDetails.isEmpty()) {
                serviceItems.add(ServiceItem.newBuilder()
                        .setName(idToNameMap.get(evaluationId))
                        .setTotal(distinctPetDetails.size())
                        .build());
            }
        });
        return buildCareCategoryRsp(EVALUATE, serviceItems);
    }

    private CareCategory buildCareCategoryRsp(String type, List<ServiceItem> serviceItems) {
        if (serviceItems != null && !serviceItems.isEmpty()) {
            return CareCategory.newBuilder()
                    .setType(type) // 填充 ServiceItemType 到 type 字段
                    .setTotal(serviceItems.size()) // 填充去重后的数量到 total 字段
                    .addAllServiceList(serviceItems)
                    .build();
        }
        return CareCategory.newBuilder().build();
    }

    private CareCategory buildAddonCareCategoryList(
            String date,
            List<PetDetailModel> addonList,
            Map<Long, String> serviceNameMap,
            Map<Integer, AppointmentDTO> appointmentIDMap) {
        List<ServiceItem> serviceItems = new ArrayList<>();
        // 通过serviceID进行group
        Map<Long, List<PetDetailModel>> groupedByServiceId =
                addonList.stream().collect(groupingBy(PetDetailModel::getServiceId));
        groupedByServiceId.forEach((serviceId, evaluationServiceModels) -> {
            List<PetDetailModel> distinctPetDetails = evaluationServiceModels.stream()
                    .collect(Collectors.toMap(
                            PetDetailModel::getPetId, // key: groomingId
                            pet -> pet, // value: PetDetailModel
                            (existing, replacement) -> existing // 如果重复，保留第一个
                            ))
                    .values()
                    .stream()
                    .toList();
            // 不在时间区间的要过滤掉
            var validDistinctPetDetails = distinctPetDetails.stream()
                    .filter(distinctPetDetail -> verifyAddonReservation(date, distinctPetDetail, appointmentIDMap))
                    .toList();
            if (!validDistinctPetDetails.isEmpty()) {
                serviceItems.add(ServiceItem.newBuilder()
                        .setName(serviceNameMap.get(serviceId))
                        .setTotal(validDistinctPetDetails.size())
                        .build());
            }
        });
        return buildCareCategoryRsp(ADDON, serviceItems);
    }

    /*
    *   1. pet_detail表的dateType=2，去pet_detail表的specific_dates字段查时间
        2. pet_detail表的dateType=1或者4，去grooming_appointment表的appointment_time和appointment_end_date查时间
        3. pet_detail表的dateType=3，去pet_detail表的start_date和end_date字段查时间
    * */
    public static boolean verifyReservation(
            String date, PetDetailModel petDetailModel, Map<Integer, AppointmentDTO> appointmentIDMap) {
        var dateType = petDetailModel.getDateType();
        var appointment = appointmentIDMap.get((int) petDetailModel.getGroomingId());
        return verifyDateType(date, dateType, petDetailModel, appointment);
    }

    private static boolean verifyDateType(
            String date,
            PetDetailDateType petDetailDateType,
            PetDetailModel petDetailModel,
            AppointmentDTO appointment) {
        return switch (petDetailDateType) {
            case PET_DETAIL_DATE_SPECIFIC_DATE -> // type 2
            checkSpecificDates(date, petDetailModel.getSpecificDates());
            case PET_DETAIL_DATE_EVERYDAY -> // type 1
            checkEverydayExceptCheckout(date, appointment.getAppointmentDate(), appointment.getAppointmentEndDate());
            case PET_DETAIL_DATE_EVERYDAY_INCLUDE_CHECKOUT_DAY -> // type 4
            checkEverydayIncludeCheckout(date, appointment.getAppointmentDate(), appointment.getAppointmentEndDate());
            case PET_DETAIL_DATE_FIRST_DAY -> // type 5
            checkFirstDay(date, appointment.getAppointmentDate());
            case PET_DETAIL_DATE_LAST_DAY -> // type 6
            checkLastDay(date, appointment.getAppointmentEndDate());
            case PET_DETAIL_DATE_EVERYDAY_EXCEPT_CHECKIN_DAY -> // type 7
            checkEveryDayExceptCheckInDay(date, appointment.getAppointmentDate(), appointment.getAppointmentEndDate());
            default -> isDateInRange(date, petDetailModel.getStartDate(), petDetailModel.getEndDate());
        };
    }

    public static boolean verifyAddonReservation(
            String date, PetDetailModel petDetailModel, Map<Integer, AppointmentDTO> appointmentIDMap) {
        var appointment = appointmentIDMap.get((int) petDetailModel.getGroomingId());
        if (appointment.getAppointmentDate().equals(appointment.getAppointmentEndDate())
                && date.equals(appointment.getAppointmentDate())) {
            return true;
        }
        var dateType = petDetailModel.getDateType();
        return verifyDateType(date, dateType, petDetailModel, appointment);
    }

    private static boolean checkSpecificDates(String date, String specificDatesJson) {
        if (specificDatesJson == null || specificDatesJson.isEmpty()) {
            return false;
        }
        return PetDetailUtil.getSpecificDates(specificDatesJson).contains(date);
    }

    private static boolean checkEverydayExceptCheckout(String date, String startDate, String endDate) {
        if (startDate == null || endDate == null) {
            return false;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate target = LocalDate.parse(date, formatter);
            LocalDate start = LocalDate.parse(startDate, formatter);
            LocalDate end = LocalDate.parse(endDate, formatter);
            return (target.isAfter(start) || target.isEqual(start)) && target.isBefore(end);
        } catch (Exception e) {
            log.error("checkEverydayExceptCheckout error", e);
            return false;
        }
    }

    private static boolean checkEverydayIncludeCheckout(String date, String startDate, String endDate) {
        if (startDate == null || endDate == null) {
            return false;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate target = LocalDate.parse(date, formatter);
            LocalDate start = LocalDate.parse(startDate, formatter);
            LocalDate end = LocalDate.parse(endDate, formatter);
            return (target.isAfter(start) || target.isEqual(start)) && (target.isBefore(end) || target.isEqual(end));
        } catch (Exception e) {
            log.error("checkEverydayIncludeCheckout error", e);
            return false;
        }
    }

    private static boolean checkEveryDayExceptCheckInDay(String date, String startDate, String endDate) {
        if (startDate == null || endDate == null) {
            return false;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate target = LocalDate.parse(date, formatter);
            LocalDate start = LocalDate.parse(startDate, formatter);
            LocalDate end = LocalDate.parse(endDate, formatter);
            return target.isAfter(start) && (target.isBefore(end) || target.isEqual(end));
        } catch (Exception e) {
            log.error("checkEveryDayExceptCheckInDay error", e);
            return false;
        }
    }

    private static boolean checkFirstDay(String date, String startDate) {
        if (startDate == null || startDate.isEmpty()) {
            return false;
        }
        return date.equals(startDate);
    }

    private static boolean checkLastDay(String date, String endDate) {
        if (endDate == null || endDate.isEmpty()) {
            return false;
        }
        return date.equals(endDate);
    }

    public static boolean isDateInRange(String date, String startDate, String endDate) {
        // 检查 startDate 和 endDate 是否有值
        if (startDate == null || startDate.isEmpty() || endDate == null || endDate.isEmpty()) {
            return false;
        }
        try {
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
            LocalDate targetDate = LocalDate.parse(date, formatter);
            LocalDate start = LocalDate.parse(startDate, formatter);
            LocalDate end = LocalDate.parse(endDate, formatter);

            return !targetDate.isBefore(start) && !targetDate.isAfter(end);
        } catch (DateTimeParseException e) {
            throw new IllegalArgumentException("Invalid date format. Expected yyyy-MM-dd.");
        }
    }

    private List<CareCategory> getCareCategoryList(
            String date, List<PetDetailModel> serviceList, Map<String, List<ServiceItem>> serviceItemTypeListMap) {
        List<CareCategory> careCategories = new ArrayList<>();
        Map<ServiceItemType, List<PetDetailModel>> groupedByServiceItemType =
                serviceList.stream().collect(groupingBy(PetDetailModel::getServiceItemType));
        // 遍历Map，对每个List<PetDetailModel>按pet_id去重
        groupedByServiceItemType.forEach((serviceItemType, petDetails) -> {
            List<PetDetailModel> distinctPetDetails = new ArrayList<>(new ArrayList<>(petDetails.stream()
                    .collect(Collectors.toMap(
                            PetDetailModel::getPetId, // key: pet_id
                            pet -> pet, // value: PetDetailModel
                            (existing, replacement) -> existing // 如果重复，保留第一个
                            ))
                    .values()
                    .stream()
                    .toList()));
            // 去掉不符合日期的数据
            var validDistinctPetDetails = distinctPetDetails.stream()
                    .filter(distinctPetDetail ->
                            isDateInRange(date, distinctPetDetail.getStartDate(), distinctPetDetail.getEndDate()))
                    .toList();
            // 创建 CareCategory 并填充数据
            List<ServiceItem> serviceItemList = serviceItemTypeListMap.get(serviceItemType.name());
            if (serviceItemList != null && !serviceItemList.isEmpty()) {
                CareCategory careCategory = CareCategory.newBuilder()
                        .setType(serviceItemType.name()) // 填充 ServiceItemType 到 type 字段
                        .setTotal(validDistinctPetDetails.size()) // 填充去重后的数量到 total 字段
                        .addAllServiceList(serviceItemTypeListMap.get(serviceItemType.name()))
                        .build();
                careCategories.add(careCategory);
            }
        });
        return careCategories;
    }

    List<CareCategory> getServiceCareCategoryList(
            String date,
            List<PetDetailModel> serviceList,
            Map<Long, String> serviceNameMap,
            Map<Integer, AppointmentDTO> appointmentIDMap) {
        // serviceID到ServiceItemType的映射
        var serviceIdToServiceItemTypeMap = serviceList.stream()
                .collect(Collectors.toMap(
                        PetDetailModel::getServiceId, // Key: service_id
                        PetDetailModel::getServiceItemType, // Value: ServiceItemType
                        (existing, replacement) -> existing // 如果重复，保留已存在的值
                        ));
        // 获取serviceItem，通过serviceID和groomID一起聚合拿到的结果，key是serviceItemType
        Map<String, List<ServiceItem>> serviceItemTypeListMap =
                getServiceItems(date, serviceList, serviceIdToServiceItemTypeMap, serviceNameMap, appointmentIDMap);
        // 拿到serviceList基于ServiceItemType的group结果，然后通过pet_id去重，并且统计ServiceItemType的数量
        return getCareCategoryList(date, serviceList, serviceItemTypeListMap);
    }

    List<CareCategory> getDaySummaryCareCategory(
            String date,
            GetPetDetailListResponse petDetailListRsp,
            Map<Long, String> serviceNameMap,
            List<AppointmentDTO> appointmentList) {
        // pet_detail返回了两个List，一个是pet_detail列表，一个是evaluationList，后者是前者service_item_type，这里先拿pet_detail列表
        var petDetailModelList = petDetailListRsp.getPetDetailsList();
        // pet_detail表，又通过service_type区分了service_type和addon，这里拿到拿到petDetailServiceList
        List<PetDetailModel> petDetailServiceList = getServiceList(petDetailModelList);
        // 通过petDetailServiceList拿到返回给前端的结果
        var appointmentIDMap =
                appointmentList.stream().collect(Collectors.toMap(AppointmentDTO::getId, appointment -> appointment));
        var careCategoryList = getServiceCareCategoryList(date, petDetailServiceList, serviceNameMap, appointmentIDMap);
        // 获取addon数据
        List<PetDetailModel> petDetailAddonList = getAddonList(petDetailModelList);
        CareCategory addonCategory =
                buildAddonCareCategoryList(date, petDetailAddonList, serviceNameMap, appointmentIDMap);
        // 获取evaluation数据
        var evaluationsList = petDetailListRsp.getPetEvaluationsList();
        CareCategory evaluateCategory = buildEvaluateCareCategoryList(date, evaluationsList);
        List<CareCategory> careCategories = new ArrayList<>(careCategoryList);
        careCategories.add(addonCategory);
        careCategories.add(evaluateCategory);
        return careCategories;
    }

    private List<AppointmentDTO> getAppointmentList(String date, Integer businessID) {
        // 取appointments
        Map<Integer, List<AppointmentDTO>> serviceTypeToAppointments =
                appointmentApi.listGroupedAppointmentByDateRange(ListGroupedAppointmentByDateRangeParam.builder()
                        .businessId(businessID)
                        .dateRange(new Range<>(date, date))
                        .build());
        return serviceTypeToAppointments.values().stream().flatMap(List::stream).toList();
    }

    private GetPetDetailListResponse getPetDetailRsp(List<AppointmentDTO> appointmentList) {
        // 拿出所有的appointmentID(appointment表的主键)
        List<Long> appointmentIds = appointmentList.stream()
                .map(AppointmentDTO::getId)
                .map(Integer::longValue)
                .toList();
        var petDetailListReq = GetPetDetailListRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .addAllAppointmentIds(appointmentIds);
        // appointment表的主键是pet_detail表的groomingID
        return petDetailStub.getPetDetailList(petDetailListReq.build());
    }

    private Map<Long, String> getServiceIDNameMap(GetPetDetailListResponse petDetailList) {
        List<Long> serviceIds = petDetailList.getPetDetailsList().stream()
                .map(PetDetailModel::getServiceId)
                .toList();
        var serviceListReq = GetServiceListByIdsRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .addAllServiceIds(serviceIds.stream().distinct().toList())
                .build();
        List<ServiceBriefView> serviceList = serviceManagementServiceBlockingStub
                .getServiceListByIds(serviceListReq)
                .getServicesList();
        Map<Long, String> serviceNameMap = new HashMap<>();
        for (ServiceBriefView service : serviceList) {
            serviceNameMap.put(service.getId(), service.getName());
        }
        return serviceNameMap;
    }

    private static GetDaySummaryResult buildGetDaySummaryResult(
            String date,
            Integer in,
            Integer out,
            Integer overnight,
            Integer total,
            List<CareCategory> careCategoryList) {
        var getDaySummaryResult = GetDaySummaryResult.newBuilder();
        var petDaySummary = PetDaySummary.newBuilder();
        petDaySummary
                .setDailyStatus(DailyStatus.newBuilder()
                        .setIn(in)
                        .setOut(out)
                        .setOvernight(overnight)
                        .setTotal(total))
                .build();
        petDaySummary.addAllCareList(careCategoryList);
        getDaySummaryResult.setPetDaySummary(petDaySummary);
        getDaySummaryResult.setDate(date);
        return getDaySummaryResult.build();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getServiceSummary(GetServiceSummaryParams request, StreamObserver<GetServiceSummaryResult> r) {

        businessHelper.checkBusinessCompany(AuthContext.get().companyId(), request.getBusinessId());

        // 限制查 60 天，防止数据量过大
        if (LocalDate.parse(request.getStartDate()).plusDays(60).isBefore(LocalDate.parse(request.getEndDate()))) {
            throw bizException(Code.CODE_PARAMS_ERROR, "date range should be less than 60 days");
        }

        r.onNext(getServiceSummaryResult(request));
        r.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getAppointmentLodging(
            GetAppointmentLodgingParams request, StreamObserver<GetAppointmentLodgingResult> responseObserver) {
        long companyId = AuthContext.get().companyId();
        long appointmentId = request.getId();
        // pet detail
        var petDetailResp = petDetailService.getPetDetailsWithActualDates(companyId, List.of(appointmentId));
        var petDetails = petDetailResp.getPetDetailsList();
        var petEvaluations = petDetailResp.getPetEvaluationsList();

        // service
        var serviceMap = serviceService.getServiceMap(companyId, PetDetailUtil.getPetDetailServiceIDs(petDetails));
        var evaluationMap = evaluationService.getEvaluationMapByIds(PetDetailUtil.getPetEvaluationIDs(petEvaluations));

        // lodging
        var lodgingMap = lodgingService.getLodgingMap(PetDetailUtil.getPetDetailLodgingIDs(petDetails, petEvaluations));

        var petIds = PetDetailUtil.getPetDetailPetIDs(petDetails, petEvaluations);

        List<GetAppointmentLodgingResult.PetLodgingView> result = new ArrayList<>();
        petIds.forEach(petId -> {
            var details = petDetails.stream()
                    .filter(p -> p.getPetId() == petId)
                    .filter(p -> p.getServiceType() == ServiceType.SERVICE)
                    .filter(k -> k.getServiceItemType() != GROOMING)
                    .toList();
            var evaluations =
                    petEvaluations.stream().filter(p -> p.getPetId() == petId).toList();
            if (CollectionUtils.isEmpty(details) && CollectionUtils.isEmpty(evaluations)) {
                return;
            }
            result.add(AppointmentConverter.INSTANCE.toAppointmentLodgingPetLodgingView(
                    petId, details, evaluations, serviceMap, evaluationMap, lodgingMap.getKey()));
        });
        responseObserver.onNext(GetAppointmentLodgingResult.newBuilder()
                .addAllPetLodgingViews(result)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.CREATE_APPOINTMENT})
    public void batchQuickCheckIn(
            BatchQuickCheckInParams request, StreamObserver<BatchQuickCheckInResult> responseObserver) {
        var resp = appointmentServiceBlockingStub.batchQuickCheckIn(BatchQuickCheckInRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .setStaffId(AuthContext.get().staffId())
                .setServiceId(request.getServiceId())
                .addAllPetIds(request.getPetIdsList())
                .setSource(request.getSource())
                .setDate(request.getDate())
                .build());
        responseObserver.onNext(BatchQuickCheckInResult.newBuilder()
                .addAllCreatedAppointmentIds(resp.getCreatedAppointmentIdsList())
                .addAllUpdatedAppointmentIds(resp.getUpdatedAppointmentIdsList())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void listStaffAppointments(
            ListStaffAppointmentsParams request, StreamObserver<ListStaffAppointmentsResult> responseObserver) {
        long companyId = AuthContext.get().companyId();
        long staffId =
                request.hasStaffId() ? request.getStaffId() : AuthContext.get().staffId();
        ListAppointmentsRequest.Filter apptFilter = ListAppointmentsRequest.Filter.newBuilder()
                .addStaffIds(staffId)
                .setStartTimeRange(request.getPeriod())
                .setFilterBookingRequest(true)
                .setFilterNoStartTime(true)
                .addAllWaitListStatuses(List.of(WaitListStatus.APPTONLY, WaitListStatus.APPTANDWAITLIST))
                .addAllStatus(List.of(
                        AppointmentStatus.UNCONFIRMED,
                        AppointmentStatus.CONFIRMED,
                        AppointmentStatus.FINISHED,
                        AppointmentStatus.READY,
                        AppointmentStatus.CHECKED_IN))
                .setIncludeServiceOperation(request.getIncludeServiceOperation())
                .build();
        if (request.hasAppointmentDate()) {
            apptFilter = apptFilter.toBuilder()
                    .setAppointmentDate(request.getAppointmentDate())
                    .build();
        }
        var orderByApptDate = OrderBy.newBuilder()
                .setFieldName("appointmentDate")
                .setAsc(false)
                .build();
        var orderByStartTime = OrderBy.newBuilder()
                .setFieldName("appointmentStartTime")
                .setAsc(false)
                .build();
        var listApptsRequest = ListAppointmentsRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .addAllBusinessIds(request.getBusinessIdsList())
                .setFilter(apptFilter)
                .setPagination(request.getPagination())
                .addAllOrderBys(List.of(orderByApptDate, orderByStartTime))
                .build();

        var response = appointmentServiceBlockingStub.listAppointments(listApptsRequest);

        if (response.getAppointmentsList().isEmpty()) {
            responseObserver.onNext(ListStaffAppointmentsResult.newBuilder()
                    .setPagination(response.getPagination())
                    .build());
            responseObserver.onCompleted();
            return;
        }

        // 查询 invoice、customer
        List<Long> apptIds = response.getAppointmentsList().stream()
                .map(AppointmentModel::getId)
                .distinct()
                .toList();
        List<Long> customerIds = response.getAppointmentsList().stream()
                .map(AppointmentModel::getCustomerId)
                .distinct()
                .toList();

        // order
        var orderMapFuture = futureService.getOrderModelMap(apptIds);
        // customer
        var customerMapFuture = futureService.getBusinessCustomerInfoMap(companyId, customerIds);
        // customer primary address
        var customerAddressMapFuture = futureService.getCustomerPrimaryAddressMap(companyId, customerIds);
        // customer last appointment
        var customerLastAppointmentFuture = futureService.getCustomerLastAppointmentMap(companyId, customerIds);
        // pet detail
        var petDetailFuture = futureService.getPetDetailList(companyId, apptIds);
        // pet
        var petMapFuture = futureService.getPetMap(companyId, petDetailFuture);
        // pet code
        var petCodeMapFuture = futureService.getPetCodeMap(petDetailFuture, companyId);
        // pet evaluations
        var petEvaluationsMapFuture = futureService.getPetEvaluationsMap(petDetailFuture, companyId);
        // pet vaccine
        var petVaccineMapFuture = futureService.getPetVaccineMap(petDetailFuture, companyId);
        // service
        var serviceMapFuture = futureService.getServiceMap(companyId, petDetailFuture);
        // lodging
        var lodgingMapFuture = futureService.getLodgingMap(petDetailFuture);
        // service operation
        var serviceOperationMapFuture = futureService.getServiceOperationMap(companyId, apptIds);
        // staff
        var staffMapFuture = futureService.getStaffMap(petDetailFuture, serviceOperationMapFuture);
        // evaluation
        var evaluationMapFuture = futureService.getEvaluationMap(petDetailFuture);

        CompletableFuture.allOf(
                        orderMapFuture,
                        customerMapFuture,
                        customerAddressMapFuture,
                        customerLastAppointmentFuture,
                        petDetailFuture,
                        petMapFuture,
                        petCodeMapFuture,
                        petEvaluationsMapFuture,
                        petVaccineMapFuture,
                        serviceMapFuture,
                        lodgingMapFuture,
                        serviceOperationMapFuture,
                        staffMapFuture,
                        evaluationMapFuture)
                .join();

        var petDetailsMap = petDetailFuture.join().getPetDetailsList().stream()
                .collect(Collectors.groupingBy(PetDetailModel::getGroomingId));
        var petEvaluationsMap = petDetailFuture.join().getPetEvaluationsList().stream()
                .collect(Collectors.groupingBy(EvaluationServiceModel::getAppointmentId));
        var customerHasLastAppointments = customerLastAppointmentFuture.join().keySet();
        var serviceOperationMap = serviceOperationMapFuture.join().entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> e.getValue().stream()
                        .collect(Collectors.groupingBy(ServiceOperationModel::getGroomingServiceId))));

        var appointmentList = response.getAppointmentsList().stream()
                .map(appointment -> {
                    long appointmentId = appointment.getId();
                    long customerId = appointment.getCustomerId();

                    return ListStaffAppointmentsResult.AppointmentDetailView.newBuilder()
                            .setAppointment(appointment)
                            .setInvoice(buildStaffInvoiceView(
                                    orderMapFuture.join().getOrDefault(appointmentId, OrderModel.getDefaultInstance())))
                            .setCustomer(buildStaffCustomerView(
                                    customerMapFuture
                                            .join()
                                            .getOrDefault(customerId, BusinessCustomerInfoModel.getDefaultInstance()),
                                    !customerHasLastAppointments.contains(customerId),
                                    customerAddressMapFuture
                                            .join()
                                            .getOrDefault(
                                                    customerId, BusinessCustomerAddressModel.getDefaultInstance())))
                            .addAllServiceDetail(getServiceDetails(
                                    petDetailsMap.getOrDefault(appointmentId, List.of()),
                                    petEvaluationsMap.getOrDefault(appointmentId, List.of()),
                                    petMapFuture.join(),
                                    serviceMapFuture.join(),
                                    lodgingMapFuture.join().getKey(),
                                    lodgingMapFuture.join().getValue(),
                                    staffMapFuture.join(),
                                    serviceOperationMap.getOrDefault(appointmentId, Map.of()),
                                    petCodeMapFuture.join(),
                                    petEvaluationsMapFuture.join(),
                                    petVaccineMapFuture.join(),
                                    evaluationMapFuture.join(),
                                    Map.of(),
                                    Map.of()))
                            .addAllServiceItemTypes(
                                    ServiceItemEnum.convertBitValueList(appointment.getServiceTypeInclude()).stream()
                                            .map(item -> ServiceItemType.forNumber(item.getServiceItem()))
                                            .toList())
                            .build();
                })
                .toList();

        responseObserver.onNext(ListStaffAppointmentsResult.newBuilder()
                .setPagination(response.getPagination())
                .addAllAppointments(appointmentList)
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void batchBookAgainAppointment(
            BatchBookAgainAppointmentParams request, StreamObserver<BatchBookAgainAppointmentResult> responseObserver) {

        List<Long> appointmentIds =
                appointmentService.getAppointmentByStaffAndDate(request.getStaffId(), request.getSourceDate(), true);

        if (appointmentIds.isEmpty()) {
            responseObserver.onNext(BatchBookAgainAppointmentResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        responseObserver.onNext(BatchBookAgainAppointmentResult.newBuilder()
                .addAllAppointments(appointmentServiceBlockingStub
                        .batchBookAgainAppointment(BatchBookAgainAppointmentRequest.newBuilder()
                                .setCompanyId(AuthContext.get().companyId())
                                .addAllAppointmentIds(appointmentIds)
                                .setTargetDate(request.getTargetDate())
                                .setRebookBy(AuthContext.get().staffId())
                                .build())
                        .getAppointmentsList())
                .build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.COMPANY)
    @Override
    public void batchCancelAppointment(
            BatchCancelAppointmentParams request, StreamObserver<BatchCancelAppointmentResult> responseObserver) {

        List<Long> appointmentIds =
                appointmentService.getAppointmentByStaffAndDate(request.getStaffId(), request.getDate(), false);

        if (appointmentIds.isEmpty()) {
            responseObserver.onNext(BatchCancelAppointmentResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        responseObserver.onNext(BatchCancelAppointmentResult.newBuilder()
                .addAllAppointments(appointmentServiceBlockingStub
                        .batchCancelAppointment(BatchCancelAppointmentRequest.newBuilder()
                                .setCompanyId(AuthContext.get().companyId())
                                .addAllAppointmentIds(appointmentIds)
                                .setCancelBy(AuthContext.get().staffId())
                                .setCancelReason(request.getCancelReason())
                                .build())
                        .getAppointmentsList())
                .build());
        responseObserver.onCompleted();
    }

    GetServiceSummaryResult getServiceSummaryResult(GetServiceSummaryParams request) {
        var appointmentFuture = supplyAsync(() -> getCountByAppointment(request), getSubmitExecutor());
        var petFuture = supplyAsync(() -> getCountByPet(request), getSubmitExecutor());
        CompletableFuture.allOf(appointmentFuture, petFuture).join();

        var builder = GetServiceSummaryResult.newBuilder();
        builder.setCountByAppointment(appointmentFuture.join());
        builder.setCountByPet(petFuture.join());
        return builder.build();
    }

    private CountedByPet getCountByPet(GetServiceSummaryParams request) {

        List<PetDetailDTO> petDetails =
                petDetailApi.listPetDetailByDateRange(IGroomingPetDetailService.ListPetDetailByDateRangeParam.builder()
                        .businessId((int) request.getBusinessId())
                        .dateRange(new Range<>(request.getStartDate(), request.getEndDate()))
                        .build());

        var byPet = CountedByPet.newBuilder();
        LocalDate start = LocalDate.parse(request.getStartDate());
        LocalDate end = LocalDate.parse(request.getEndDate());
        for (var date = start; date.isBefore(end) || date.isEqual(end); date = date.plusDays(1)) {
            byPet.addRows(buildRowForPet(date.toString(), petDetails));
        }

        return byPet.build();
    }

    private static GetServiceSummaryResult.ServiceSummaryRow buildRowForPet(
            String date, List<PetDetailDTO> petDetails) {

        var serviceTypeToPetDetails = petDetails.stream().collect(groupingBy(PetDetailDTO::getServiceItemType));

        List<PetDetailDTO> groomings = serviceTypeToPetDetails.getOrDefault(GROOMING_VALUE, List.of());
        List<PetDetailDTO> boardings = serviceTypeToPetDetails.getOrDefault(BOARDING_VALUE, List.of());
        List<PetDetailDTO> daycares = serviceTypeToPetDetails.getOrDefault(DAYCARE_VALUE, List.of());
        List<PetDetailDTO> evaluations = serviceTypeToPetDetails.getOrDefault(EVALUATION_VALUE, List.of());
        List<PetDetailDTO> dogWalkings = serviceTypeToPetDetails.getOrDefault(DOG_WALKING_VALUE, List.of());

        var children = GetServiceSummaryResult.ServiceSummaryChildren.newBuilder();

        // order: Boarding-Daycare-Grooming-Dog walking-Evaluation
        children.setBoarding(buildChildRowForPet(date, boardings));
        children.setDaycare(buildChildRowForPet(date, daycares));
        children.setGrooming(buildChildRowForPet(date, groomings));
        children.setDogWalking(buildChildRowForPet(date, dogWalkings));
        children.setEvaluation(buildChildRowForPet(date, evaluations));

        var row = GetServiceSummaryResult.ServiceSummaryRow.newBuilder();

        row.setDate(date);

        var petRow = buildPetRow(date, petDetails);
        row.setIn(petRow.in().size());
        row.setOut(petRow.out().size());
        row.setOvernight(petRow.overnight().size());
        row.setTotal(petRow.total());

        row.setChildren(children.build());

        return row.build();
    }

    private static Set<Integer> filterOvernightForPet(String date, List<PetDetailDTO> petDetails) {
        return petDetails.stream()
                .filter(e -> StringUtils.hasText(e.getStartDate())
                        && StringUtils.hasText(e.getEndDate())
                        && e.getStartDate().compareTo(date) <= 0
                        && e.getEndDate().compareTo(date) > 0)
                .map(PetDetailDTO::getPetId)
                .collect(Collectors.toSet());
    }

    private static Set<Integer> filterOutForPet(String date, List<PetDetailDTO> petDetails) {
        return petDetails.stream()
                .filter(e -> Objects.equals(e.getEndDate(), date))
                .map(PetDetailDTO::getPetId)
                .collect(Collectors.toSet());
    }

    private static Set<Integer> filterInForPet(String date, List<PetDetailDTO> petDetails) {
        return petDetails.stream()
                .filter(e -> Objects.equals(e.getStartDate(), date))
                .map(PetDetailDTO::getPetId)
                .collect(Collectors.toSet());
    }

    private static GetServiceSummaryResult.ServiceRow buildChildRowForPet(String date, List<PetDetailDTO> petDetails) {
        var builder = GetServiceSummaryResult.ServiceRow.newBuilder();

        var row = buildPetRow(date, petDetails);
        builder.setIn(row.in().size());
        builder.setOut(row.out().size());
        builder.setOvernight(row.overnight().size());
        builder.setTotal(row.total());

        return builder.build();
    }

    private static ServiceSummaryPetRow buildPetRow(String date, List<PetDetailDTO> petDetails) {
        Set<Integer> in = filterInForPet(date, petDetails);
        Set<Integer> out = filterOutForPet(date, petDetails);
        Set<Integer> overnight = filterOvernightForPet(date, petDetails);
        int total = (int)
                Stream.of(in, out, overnight).flatMap(Set::stream).distinct().count();
        return new ServiceSummaryPetRow(in, out, overnight, total);
    }

    private CountedByAppointment getCountByAppointment(GetServiceSummaryParams request) {

        Map<Integer, List<AppointmentDTO>> serviceTypeToAppointments =
                appointmentApi.listGroupedAppointmentByDateRange(ListGroupedAppointmentByDateRangeParam.builder()
                        .businessId((int) request.getBusinessId())
                        .dateRange(new Range<>(request.getStartDate(), request.getEndDate()))
                        .build());

        var byAppt = CountedByAppointment.newBuilder();
        LocalDate start = LocalDate.parse(request.getStartDate());
        LocalDate end = LocalDate.parse(request.getEndDate());
        for (var date = start; date.isBefore(end) || date.isEqual(end); date = date.plusDays(1)) {
            byAppt.addRows(buildRowForAppointment(date.toString(), serviceTypeToAppointments));
        }

        return byAppt.build();
    }

    private static GetServiceSummaryResult.ServiceSummaryRow buildRowForAppointment(
            String date, Map<Integer, List<AppointmentDTO>> serviceTypeToAppointments) {

        var groomings = serviceTypeToAppointments.getOrDefault(GROOMING_VALUE, List.of());
        var boardings = serviceTypeToAppointments.getOrDefault(BOARDING_VALUE, List.of());
        var daycares = serviceTypeToAppointments.getOrDefault(DAYCARE_VALUE, List.of());
        var evaluations = serviceTypeToAppointments.getOrDefault(EVALUATION_VALUE, List.of());
        var dogWalkings = serviceTypeToAppointments.getOrDefault(ServiceItemType.DOG_WALKING_VALUE, List.of());

        var children = GetServiceSummaryResult.ServiceSummaryChildren.newBuilder();

        // order: Boarding-Daycare-Grooming-Dog walking-Evaluation
        children.setBoarding(buildChildRowForAppointment(date, boardings));
        children.setDaycare(buildChildRowForAppointment(date, daycares));
        children.setGrooming(buildChildRowForAppointment(date, groomings));
        children.setDogWalking(buildChildRowForAppointment(date, dogWalkings));
        children.setEvaluation(buildChildRowForAppointment(date, evaluations));

        List<AppointmentDTO> all = serviceTypeToAppointments.values().stream()
                .flatMap(List::stream)
                .toList();

        var row = GetServiceSummaryResult.ServiceSummaryRow.newBuilder();

        row.setDate(date);

        var appointmentRow = buildAppointmentRow(date, all);
        row.setIn(appointmentRow.in().size());
        row.setOut(appointmentRow.out().size());
        row.setOvernight(appointmentRow.overnight().size());
        row.setTotal(appointmentRow.total());

        row.setChildren(children.build());

        return row.build();
    }

    private static ServiceSummaryAppointmentRow buildAppointmentRow(String date, List<AppointmentDTO> all) {
        var in = filterInForAppointment(date, all);
        var out = filterOutForAppointment(date, all);
        var overnight = filterOvernightForAppointment(date, all);
        int total = (int)
                Stream.of(in, out, overnight).flatMap(Set::stream).distinct().count();
        return new ServiceSummaryAppointmentRow(in, out, overnight, total);
    }

    private static GetServiceSummaryResult.ServiceRow buildChildRowForAppointment(
            String date, List<AppointmentDTO> appointments) {

        var row = GetServiceSummaryResult.ServiceRow.newBuilder();

        var appointmentRow = buildAppointmentRow(date, appointments);

        row.setIn(appointmentRow.in().size());
        row.setOut(appointmentRow.out().size());
        row.setOvernight(appointmentRow.overnight().size());
        row.setTotal(appointmentRow.total());

        return row.build();
    }

    private static Set<Integer> filterOvernightForAppointment(String date, List<AppointmentDTO> appointments) {
        return appointments.stream()
                .filter(e -> StringUtils.hasText(e.getAppointmentDate())
                        && StringUtils.hasText(e.getAppointmentEndDate())
                        && e.getAppointmentDate().compareTo(date) <= 0
                        && e.getAppointmentEndDate().compareTo(date) > 0)
                .map(AppointmentDTO::getId)
                .collect(Collectors.toSet());
    }

    private static Set<Integer> filterOutForAppointment(String date, List<AppointmentDTO> appointments) {
        return appointments.stream()
                .filter(e -> Objects.equals(e.getAppointmentEndDate(), date))
                .map(AppointmentDTO::getId)
                .collect(Collectors.toSet());
    }

    private static Set<Integer> filterInForAppointment(String date, List<AppointmentDTO> appointments) {
        return appointments.stream()
                .filter(e -> Objects.equals(e.getAppointmentDate(), date))
                .map(AppointmentDTO::getId)
                .collect(Collectors.toSet());
    }

    private void addPetBelongings(List<CreateAppointmentParams.PetBelonging> petBelongings, long appointmentId) {
        for (var petBelonging : petBelongings) {
            petBelongingApi.insert(toPetBelongingDTO(appointmentId, petBelonging));
        }
    }

    private static PetBelongingDTO toPetBelongingDTO(
            long appointmentId, CreateAppointmentParams.PetBelonging petBelonging) {
        var dto = new PetBelongingDTO();
        dto.setAppointmentId((int) appointmentId);
        dto.setPetId((int) petBelonging.getPetId());
        dto.setName(petBelonging.getName());
        if (petBelonging.hasArea()) {
            dto.setArea(petBelonging.getArea());
        }
        if (petBelonging.hasPhotoUrl()) {
            dto.setPhotoUrls(List.of(petBelonging.getPhotoUrl()));
        }
        return dto;
    }

    /**
     * @param in        appointment id list
     * @param out       appointment id list
     * @param overnight appointment id list
     * @param total     total appointment count
     */
    private record ServiceSummaryAppointmentRow(Set<Integer> in, Set<Integer> out, Set<Integer> overnight, int total) {}

    /**
     * @param in        pet id list
     * @param out       pet id list
     * @param overnight pet id list
     * @param total     total pet count
     */
    private record ServiceSummaryPetRow(Set<Integer> in, Set<Integer> out, Set<Integer> overnight, int total) {}

    private ListStaffAppointmentsResult.InvoiceView buildStaffInvoiceView(OrderModel order) {
        return ListStaffAppointmentsResult.InvoiceView.newBuilder()
                .setNetSale(toMoney(order.getSubTotalAmount() - order.getDiscountAmount() - order.getRefundedAmount()))
                .setTips(toMoney(order.getTipsAmount()))
                .build();
    }

    private static Money toMoney(double amount) {
        BigDecimal decimal = BigDecimal.valueOf(amount);
        return Money.newBuilder()
                .setCurrencyCode("USD")
                .setUnits(decimal.longValue())
                .setNanos(decimal.remainder(BigDecimal.ONE)
                        .movePointRight(decimal.scale())
                        .intValue())
                .build();
    }

    private ListStaffAppointmentsResult.CustomerView buildStaffCustomerView(
            BusinessCustomerInfoModel customer, boolean isNew, BusinessCustomerAddressModel address) {
        return ListStaffAppointmentsResult.CustomerView.newBuilder()
                .setCustomerProfile(customer)
                .setIsNewCustomer(isNew)
                .setCustomerAddress(address)
                .build();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void rescheduleBoardingAppointment(
            RescheduleBoardingAppointmentParams request,
            StreamObserver<RescheduleBoardingAppointmentResult> responseObserver) {
        var builder = RescheduleBoardingAppointmentRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setAppointmentId(request.getAppointmentId());
        if (request.hasStartDate()) {
            builder.setStartDate(request.getStartDate());
        }
        if (request.hasEndDate()) {
            builder.setEndDate(request.getEndDate());
        }
        appointmentServiceBlockingStub.rescheduleBoardingAppointment(builder.build());

        responseObserver.onNext(RescheduleBoardingAppointmentResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
