package com.moego.api.v3.appointment.service;

import com.moego.idl.models.appointment.v1.ServiceOperationModel;
import com.moego.idl.service.appointment.v1.GetServiceOperationListRequest;
import com.moego.idl.service.appointment.v1.ServiceOperationServiceGrpc;
import java.util.List;
import javax.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024/9/26
 */
@Service
@RequiredArgsConstructor
public class ServiceOperationService {

    private final ServiceOperationServiceGrpc.ServiceOperationServiceBlockingStub serviceOperationStub;

    public List<ServiceOperationModel> listServiceOperations(@Nullable Long companyId, List<Long> appointmentIds) {
        if (companyId == null || ObjectUtils.isEmpty(appointmentIds)) {
            return List.of();
        }
        return serviceOperationStub
                .getServiceOperationList(GetServiceOperationListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllAppointmentIds(appointmentIds)
                        .build())
                .getServiceOperationsList();
    }
}
