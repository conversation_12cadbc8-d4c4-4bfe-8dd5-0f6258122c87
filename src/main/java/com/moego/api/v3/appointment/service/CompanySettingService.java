package com.moego.api.v3.appointment.service;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/9/26
 */
@Service
@RequiredArgsConstructor
public class CompanySettingService {

    private final CompanyServiceGrpc.CompanyServiceBlockingStub companyStub;

    public String mustGetTimeZoneName(long companyId) {
        String timeZoneName = companyStub
                .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getPreferenceSetting()
                .getTimeZone()
                .getName();
        if (StringUtils.hasText(timeZoneName)) {
            return timeZoneName;
        }
        throw ExceptionUtil.bizException(Code.CODE_COMPANY_NOT_FOUND);
    }

    public String getComapnyCurrency(long companyId) {
        return companyStub
                .getCompanyPreferenceSetting(GetCompanyPreferenceSettingRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getPreferenceSetting()
                .getCurrencyCode();
    }
}
