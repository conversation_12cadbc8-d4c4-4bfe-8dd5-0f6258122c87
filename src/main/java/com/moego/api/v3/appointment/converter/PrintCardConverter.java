package com.moego.api.v3.appointment.converter;

import static java.util.Comparator.comparing;
import static java.util.Comparator.reverseOrder;

import com.moego.api.v3.appointment.utils.LodgingUtil;
import com.moego.idl.api.appointment.v1.ListAppointmentCardResult;
import com.moego.idl.api.appointment.v1.ListBoardingArrivalCardResult;
import com.moego.idl.api.appointment.v1.ListBoardingDepartureCardResult;
import com.moego.idl.api.appointment.v1.ListDailyPlaygroupCardResult;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentNoteModel;
import com.moego.idl.models.appointment.v1.AppointmentNoteType;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v1.PetPlaygroupModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeModel;
import com.moego.idl.models.business_customer.v1.BusinessPetIncidentReportModel;
import com.moego.idl.models.business_customer.v1.BusinessPetNoteModel;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.PlaygroupModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.service.business_customer.v1.BatchListBindingPetCodeResponse;
import com.moego.lib.utils.model.Pair;
import com.moego.server.customer.dto.PetBelongingDTO;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper()
public interface PrintCardConverter {

    PrintCardConverter INSTANCE = Mappers.getMapper(PrintCardConverter.class);

    long PET_NOTES_MAX_COUNT = 3;

    default ListAppointmentCardResult.PetView toAppointmentCardPetView(
            BusinessCustomerPetModel pet,
            Map<Long, BusinessCustomerInfoModel> customerMap,
            Map<Long, List<Long>> petCodes,
            Map<Long, List<BusinessPetNoteModel>> petNotes,
            Pair<List<BatchListBindingPetCodeResponse.Binding>, List<BusinessPetCodeModel>> petCodePair,
            Map<Long, List<BusinessPetIncidentReportModel>> petIncidentReports) {
        var customer = customerMap.getOrDefault(pet.getCustomerId(), BusinessCustomerInfoModel.getDefaultInstance());
        Map<Long, BusinessPetCodeModel> petCodeMap = petCodePair.value().stream()
                .collect(Collectors.toMap(BusinessPetCodeModel::getId, Function.identity()));
        Map<Long, List<BatchListBindingPetCodeResponse.Binding>> petCodeBindingMap = petCodePair.key().stream()
                .collect(Collectors.groupingBy(BatchListBindingPetCodeResponse.Binding::getPetId));
        var petCodeBindingsMap = toAppointmentCardPetCodeBindingsViews(petCodeMap, petCodeBindingMap);

        return ListAppointmentCardResult.PetView.newBuilder()
                .setId(pet.getId())
                .setName(pet.getPetName())
                .setAvatarPath(pet.getAvatarPath())
                .setGender(pet.getGender())
                .setBreed(pet.getBreed())
                .setPetType(pet.getPetType())
                .setWeight(pet.getWeight())
                .setPetFixed(pet.getFixed())
                .setOwnerName(customer.getFirstName().trim() + " "
                        + customer.getLastName().trim())
                .setOwnerFirstName(customer.getFirstName())
                .setOwnerLastName(customer.getLastName())
                .setPetAppearanceColor(pet.getPetAppearanceColor())
                .setPetAppearanceNotes(pet.getPetAppearanceNotes())
                .addAllPetCodeIds(petCodes.getOrDefault(pet.getId(), List.of()))
                .addAllPetNotes(petNotes.getOrDefault(pet.getId(), List.of()).stream()
                        .sorted(comparing(BusinessPetNoteModel::getId, reverseOrder()))
                        .limit(PET_NOTES_MAX_COUNT)
                        .map(BusinessPetNoteModel::getNote)
                        .toList())
                .addAllPetCodeBindings(petCodeBindingsMap.getOrDefault(pet.getId(), List.of()))
                .addAllIncidentReports(petIncidentReports.getOrDefault(pet.getId(), List.of()))
                .build();
    }

    default ListAppointmentCardResult.AppointmentView toAppointmentCardAppointmentView(
            AppointmentModel appointment, Map<Long, List<AppointmentNoteModel>> appointmentNotes) {
        var builder = ListAppointmentCardResult.AppointmentView.newBuilder().setId(appointment.getId());
        builder.setAppointmentEndDate(appointment.getAppointmentEndDate());
        builder.setAppointmentEndTime(appointment.getAppointmentEndTime());
        var notes = appointmentNotes.get(appointment.getId());
        if (!CollectionUtils.isEmpty(notes)) {
            AppointmentNoteModel commentNote = notes.stream()
                    .filter(note -> Objects.equals(AppointmentNoteType.COMMENT, note.getType()))
                    .findFirst()
                    .orElse(null);
            AppointmentNoteModel alertNote = notes.stream()
                    .filter(note -> Objects.equals(AppointmentNoteType.ALERT_NOTES, note.getType()))
                    .findFirst()
                    .orElse(null);
            builder.setComment(commentNote == null ? "" : commentNote.getNote());
            builder.setAlert(alertNote == null ? "" : alertNote.getNote());
        }
        return builder.build();
    }

    default ListAppointmentCardResult.BoardingView toAppointmentCardPetBoardingView(
            Long appointmentId,
            Long petId,
            List<PetDetailModel> petDetails,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {

        return ListAppointmentCardResult.BoardingView.newBuilder()
                .setAppointmentId(appointmentId)
                .setPetId(petId)
                .addAllPetServices(
                        toAppointmentCardPetBoardingServiceView(petDetails, serviceMap, lodgingUnitMap, lodgingTypeMap))
                .build();
    }

    default ListBoardingDepartureCardResult.BoardingView toBoardingDepartureCardPetBoardingView(
            Long appointmentId,
            Long petId,
            List<PetDetailModel> petDetails,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap,
            Map<Long, List<PetBelongingDTO>> petBelongingMap) {

        return ListBoardingDepartureCardResult.BoardingView.newBuilder()
                .setAppointmentId(appointmentId)
                .setPetId(petId)
                .addAllPetServices(
                        toAppointmentCardPetBoardingServiceView(petDetails, serviceMap, lodgingUnitMap, lodgingTypeMap))
                .addAllPetBelongings(toBoardingDepartureCardPetBoardingPetBelongingView(petDetails, petBelongingMap))
                .build();
    }

    default ListBoardingArrivalCardResult.BoardingView toBoardingArrivalCardPetBoardingView(
            Long appointmentId,
            Long petId,
            List<PetDetailModel> petDetails,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap,
            Map<Long, List<PetBelongingDTO>> petBelongingMap) {

        return ListBoardingArrivalCardResult.BoardingView.newBuilder()
                .setAppointmentId(appointmentId)
                .setPetId(petId)
                .addAllPetServices(
                        toAppointmentCardPetBoardingServiceView(petDetails, serviceMap, lodgingUnitMap, lodgingTypeMap))
                .addAllPetBelongings(toBoardingArrivalCardPetBoardingPetBelongingView(petDetails, petBelongingMap))
                .build();
    }

    default List<ListAppointmentCardResult.BoardingView.PetServiceView> toAppointmentCardPetBoardingServiceView(
            List<PetDetailModel> petDetails,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return List.of();
        }
        return petDetails.stream()
                .map(petDetail ->
                        toAppointmentCardPetBoardingServiceView(petDetail, serviceMap, lodgingUnitMap, lodgingTypeMap))
                .toList();
    }

    default List<ListBoardingDepartureCardResult.BoardingView.PetBelonging>
            toBoardingDepartureCardPetBoardingPetBelongingView(
                    List<PetDetailModel> petDetails, Map<Long, List<PetBelongingDTO>> petBelongingMap) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return List.of();
        }
        return petDetails.stream()
                .map(petDetail -> toBoardingDepartureCardPetBoardingPetBelongingView(petDetail, petBelongingMap))
                .toList();
    }

    default List<ListBoardingArrivalCardResult.BoardingView.PetBelonging>
            toBoardingArrivalCardPetBoardingPetBelongingView(
                    List<PetDetailModel> petDetails, Map<Long, List<PetBelongingDTO>> petBelongingMap) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return List.of();
        }
        return petDetails.stream()
                .map(petDetail -> toBoardingArrivalCardPetBoardingPetBelongingView(petDetail, petBelongingMap))
                .toList();
    }

    default ListAppointmentCardResult.BoardingView.PetServiceView toAppointmentCardPetBoardingServiceView(
            PetDetailModel petDetail,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {
        var service = serviceMap.get(petDetail.getServiceId());
        var serviceName = service == null ? "" : service.getName();

        var lodgingUnit = lodgingUnitMap.get(petDetail.getLodgingId());
        var lodgingUnitName = lodgingUnit == null ? "" : lodgingUnit.getName();
        var lodgingType = lodgingUnit == null ? null : lodgingTypeMap.get(lodgingUnit.getLodgingTypeId());
        var lodgingTypeName = lodgingType == null ? "" : lodgingType.getName();
        var lodgingTypeId = lodgingType == null ? 0 : lodgingType.getId();

        return ListAppointmentCardResult.BoardingView.PetServiceView.newBuilder()
                .setPetServiceId(petDetail.getId())
                .setServiceName(serviceName)
                .setStartDate(petDetail.getStartDate())
                .setStartTime(petDetail.getStartTime())
                .setEndDate(petDetail.getEndDate())
                .setEndTime(petDetail.getEndTime())
                .setLodgingTypeName(lodgingTypeName)
                .setLodgingUnitName(lodgingUnitName)
                .setLodgingTypeId(lodgingTypeId)
                .setLodgingUnitId(petDetail.getLodgingId())
                .build();
    }

    default ListBoardingDepartureCardResult.BoardingView.PetBelonging
            toBoardingDepartureCardPetBoardingPetBelongingView(
                    PetDetailModel petDetail, Map<Long, List<PetBelongingDTO>> petBelongingMap) {
        var petBelonging = petBelongingMap.getOrDefault(petDetail.getGroomingId(), List.of()).stream()
                .filter(belonging -> Objects.equals(belonging.getAppointmentId().longValue(), petDetail.getGroomingId())
                        && Objects.equals(belonging.getPetId().longValue(), petDetail.getPetId()))
                .findFirst();
        return petBelonging
                .map(petBelongingDTO -> ListBoardingDepartureCardResult.BoardingView.PetBelonging.newBuilder()
                        .setName(petBelongingDTO.getName())
                        .setArea(petBelongingDTO.getArea())
                        .build())
                .orElseGet(ListBoardingDepartureCardResult.BoardingView.PetBelonging::getDefaultInstance);
    }

    default ListBoardingArrivalCardResult.BoardingView.PetBelonging toBoardingArrivalCardPetBoardingPetBelongingView(
            PetDetailModel petDetail, Map<Long, List<PetBelongingDTO>> petBelongingMap) {
        var petBelonging = petBelongingMap.getOrDefault(petDetail.getGroomingId(), List.of()).stream()
                .filter(belonging -> Objects.equals(belonging.getAppointmentId().longValue(), petDetail.getGroomingId())
                        && Objects.equals(belonging.getPetId().longValue(), petDetail.getPetId()))
                .findFirst();
        return petBelonging
                .map(petBelongingDTO -> ListBoardingArrivalCardResult.BoardingView.PetBelonging.newBuilder()
                        .setName(petBelongingDTO.getName())
                        .setArea(petBelongingDTO.getArea())
                        .build())
                .orElseGet(ListBoardingArrivalCardResult.BoardingView.PetBelonging::getDefaultInstance);
    }

    default ListAppointmentCardResult.DaycareView toAppointmentCardPetDaycareView(
            Long appointmentId,
            Long petId,
            List<PetDetailModel> petDetails,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {

        return ListAppointmentCardResult.DaycareView.newBuilder()
                .setAppointmentId(appointmentId)
                .setPetId(petId)
                .addAllPetServices(
                        toAppointmentCardPetDaycareServiceView(petDetails, serviceMap, lodgingUnitMap, lodgingTypeMap))
                .build();
    }

    default List<ListAppointmentCardResult.DaycareView.PetServiceView> toAppointmentCardPetDaycareServiceView(
            List<PetDetailModel> petDetails,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return List.of();
        }
        return petDetails.stream()
                .map(petDetail ->
                        toAppointmentCardPetDaycareServiceView(petDetail, serviceMap, lodgingUnitMap, lodgingTypeMap))
                .toList();
    }

    default ListAppointmentCardResult.DaycareView.PetServiceView toAppointmentCardPetDaycareServiceView(
            PetDetailModel petDetail,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {
        var service = serviceMap.get(petDetail.getServiceId());
        var serviceName = service == null ? "" : service.getName();

        var lodgingUnit = lodgingUnitMap.get(petDetail.getLodgingId());
        var lodgingUnitName = lodgingUnit == null ? "" : lodgingUnit.getName();
        var lodgingType = lodgingUnit == null ? null : lodgingTypeMap.get(lodgingUnit.getLodgingTypeId());
        var lodgingTypeName = lodgingType == null ? "" : lodgingType.getName();
        var lodgingTypeId = lodgingType == null ? 0 : lodgingType.getId();

        return ListAppointmentCardResult.DaycareView.PetServiceView.newBuilder()
                .setPetServiceId(petDetail.getId())
                .setServiceName(serviceName)
                .setStartTime(petDetail.getStartTime())
                .setEndTime(petDetail.getEndTime())
                .setLodgingTypeName(lodgingTypeName)
                .setLodgingUnitName(lodgingUnitName)
                .setLodgingTypeId(lodgingTypeId)
                .setLodgingUnitId(petDetail.getLodgingId())
                .build();
    }

    default ListAppointmentCardResult.EvaluationView toAppointmentCardPetEvaluationView(
            Long appointmentId,
            Long petId,
            List<EvaluationServiceModel> petEvaluations,
            Map<Long, EvaluationBriefView> serviceMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {

        return ListAppointmentCardResult.EvaluationView.newBuilder()
                .setAppointmentId(appointmentId)
                .setPetId(petId)
                .addAllPetServices(toAppointmentCardPetEvaluationServiceView(
                        petEvaluations, serviceMap, lodgingUnitMap, lodgingTypeMap))
                .build();
    }

    default ListAppointmentCardResult.DogWalkingView toAppointmentCardPetDogWalkingView(
            Long appointmentId,
            Long petId,
            List<PetDetailModel> petDetails,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, StaffModel> staffMap) {

        return ListAppointmentCardResult.DogWalkingView.newBuilder()
                .setAppointmentId(appointmentId)
                .setPetId(petId)
                .addAllPetServices(toAppointmentCardPetDogWalkingServiceView(petDetails, serviceMap, staffMap))
                .build();
    }

    default List<ListAppointmentCardResult.EvaluationView.PetServiceView> toAppointmentCardPetEvaluationServiceView(
            List<EvaluationServiceModel> petEvaluations,
            Map<Long, EvaluationBriefView> serviceMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {
        if (CollectionUtils.isEmpty(petEvaluations)) {
            return List.of();
        }
        return petEvaluations.stream()
                .map(petDetail -> toAppointmentCardPetEvaluationServiceView(
                        petDetail, serviceMap, lodgingUnitMap, lodgingTypeMap))
                .toList();
    }

    default ListAppointmentCardResult.EvaluationView.PetServiceView toAppointmentCardPetEvaluationServiceView(
            EvaluationServiceModel petEvaluations,
            Map<Long, EvaluationBriefView> serviceMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {
        var service = serviceMap.get(petEvaluations.getServiceId());
        var serviceName = service == null ? "" : service.getName();

        var lodgingUnit = lodgingUnitMap.get(petEvaluations.getLodgingId());
        var lodgingUnitName = lodgingUnit == null ? "" : lodgingUnit.getName();
        var lodgingType = lodgingUnit == null ? null : lodgingTypeMap.get(lodgingUnit.getLodgingTypeId());
        var lodgingTypeName = lodgingType == null ? "" : lodgingType.getName();
        var lodgingTypeId = lodgingType == null ? 0 : lodgingType.getId();

        return ListAppointmentCardResult.EvaluationView.PetServiceView.newBuilder()
                .setPetServiceId(petEvaluations.getId())
                .setServiceName(serviceName)
                .setStartTime(petEvaluations.getStartTime())
                .setEndTime(petEvaluations.getEndTime())
                .setLodgingTypeName(lodgingTypeName)
                .setLodgingUnitName(lodgingUnitName)
                .setLodgingTypeId(lodgingTypeId)
                .setLodgingUnitId(petEvaluations.getLodgingId())
                .build();
    }

    default ListAppointmentCardResult.GroomingView toAppointmentCardPetGroomingView(
            Long appointmentId,
            Long petId,
            List<PetDetailModel> petDetails,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, StaffModel> staffMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {

        return ListAppointmentCardResult.GroomingView.newBuilder()
                .setAppointmentId(appointmentId)
                .setPetId(petId)
                .addAllPetServices(toAppointmentCardPetGroomingServiceView(
                        petDetails, serviceMap, staffMap, lodgingUnitMap, lodgingTypeMap))
                .build();
    }

    default List<ListAppointmentCardResult.GroomingView.PetServiceView> toAppointmentCardPetGroomingServiceView(
            List<PetDetailModel> petDetails,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, StaffModel> staffMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return List.of();
        }
        return petDetails.stream()
                .map(petDetail -> toAppointmentCardPetGroomingServiceView(
                        petDetail, serviceMap, staffMap, lodgingUnitMap, lodgingTypeMap))
                .sorted(comparing(ListAppointmentCardResult.GroomingView.PetServiceView::getServiceType))
                .toList();
    }

    default ListAppointmentCardResult.GroomingView.PetServiceView toAppointmentCardPetGroomingServiceView(
            PetDetailModel petDetail,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, StaffModel> staffMap,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {
        var staff = staffMap.get(petDetail.getStaffId());
        var staffFirstName = staff == null ? "" : staff.getFirstName();
        var staffLastName = staff == null ? "" : staff.getLastName();

        var service = serviceMap.get(petDetail.getServiceId());
        var serviceName = service == null ? "" : service.getName();

        var lodgingUnit = lodgingUnitMap.get(petDetail.getLodgingId());
        var lodgingUnitName = lodgingUnit == null ? "" : lodgingUnit.getName();
        var lodgingType = lodgingUnit == null ? null : lodgingTypeMap.get(lodgingUnit.getLodgingTypeId());
        var lodgingTypeName = lodgingType == null ? "" : lodgingType.getName();
        var lodgingTypeId = lodgingType == null ? 0 : lodgingType.getId();

        return ListAppointmentCardResult.GroomingView.PetServiceView.newBuilder()
                .setPetServiceId(petDetail.getId())
                .setServiceName(serviceName)
                .setStartTime(petDetail.getStartTime())
                .setEndTime(petDetail.getEndTime())
                .setStaffFirstName(staffFirstName)
                .setStaffLastName(staffLastName)
                .setServiceType(service == null ? petDetail.getServiceType() : service.getType())
                .setLodgingTypeName(lodgingTypeName)
                .setLodgingUnitName(lodgingUnitName)
                .setLodgingTypeId(lodgingTypeId)
                .setLodgingUnitId(petDetail.getLodgingId())
                .build();
    }

    default List<ListAppointmentCardResult.DogWalkingView.PetServiceView> toAppointmentCardPetDogWalkingServiceView(
            List<PetDetailModel> petDetails, Map<Long, ServiceBriefView> serviceMap, Map<Long, StaffModel> staffMap) {
        if (CollectionUtils.isEmpty(petDetails)) {
            return List.of();
        }
        return petDetails.stream()
                .map(petDetail -> toAppointmentCardPetDogWalkingServiceView(petDetail, serviceMap, staffMap))
                .toList();
    }

    default ListAppointmentCardResult.DogWalkingView.PetServiceView toAppointmentCardPetDogWalkingServiceView(
            PetDetailModel petDetail, Map<Long, ServiceBriefView> serviceMap, Map<Long, StaffModel> staffMap) {
        var staff = staffMap.get(petDetail.getStaffId());
        var staffFirstName = staff == null ? "" : staff.getFirstName();
        var staffLastName = staff == null ? "" : staff.getLastName();

        var service = serviceMap.get(petDetail.getServiceId());
        var serviceName = service == null ? "" : service.getName();

        return ListAppointmentCardResult.DogWalkingView.PetServiceView.newBuilder()
                .setPetServiceId(petDetail.getId())
                .setServiceName(serviceName)
                .setStartTime(petDetail.getStartTime())
                .setEndTime(petDetail.getEndTime())
                .setStaffFirstName(staffFirstName)
                .setStaffLastName(staffLastName)
                .build();
    }

    default List<ListAppointmentCardResult.LodgingTypeDayView> toAppointmentCardPetLodgingTypeDayView(
            String date,
            Map<String, Map<Long, Integer>> petCntPerDayPerLodging,
            Map<Long, List<LodgingUnitModel>> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {

        List<ListAppointmentCardResult.LodgingTypeDayView> result = new ArrayList<>();
        lodgingTypeMap.forEach((lodgingTypeId, lodgingType) -> {
            var lodgingTypeUnits = lodgingUnitMap.getOrDefault(lodgingTypeId, List.of());
            int maxPetTotalNum = LodgingUtil.calculateMaxPetNum(List.of(lodgingType), lodgingTypeUnits);
            Map<String, Integer> existPetPerDay =
                    LodgingUtil.calPetCntPerDay(date, date, lodgingTypeUnits, petCntPerDayPerLodging);

            result.add(ListAppointmentCardResult.LodgingTypeDayView.newBuilder()
                    .setId(lodgingTypeId)
                    .setName(lodgingType.getName())
                    .setMaxPetTotalNum(maxPetTotalNum)
                    .setMaxPetNum(lodgingType.getMaxPetNum())
                    .setOccupiedPetNum(existPetPerDay.getOrDefault(date, 0))
                    .setSort(lodgingType.getSort())
                    .build());
        });

        return result;
    }

    default List<ListDailyPlaygroupCardResult.PlaygroupView> toDailyPlaygroupCardPetPlaygroupView(
            List<PetPlaygroupModel> petPlaygroups) {
        return petPlaygroups.stream()
                .collect(Collectors.groupingBy(PetPlaygroupModel::getPlaygroupId))
                .entrySet()
                .stream()
                .map(entry -> {
                    Long playgroupId = entry.getKey();
                    List<PetPlaygroupModel> petPlaygroupList = entry.getValue();
                    return ListDailyPlaygroupCardResult.PlaygroupView.newBuilder()
                            .setPlaygroupId(playgroupId)
                            .setPetNumber(petPlaygroupList.size())
                            .addAllPetPlaygroups(petPlaygroupList.stream()
                                    .map(e -> ListDailyPlaygroupCardResult.PetPlaygroupView.newBuilder()
                                            .setPetId(e.getPetId())
                                            .setPetPlaygroupId(e.getId())
                                            .setSort(e.getSort())
                                            .build())
                                    .collect(Collectors.toList()))
                            .build();
                })
                .toList();
    }

    default List<ListDailyPlaygroupCardResult.PetView> toDailyPlaygroupCardPetView(
            List<BusinessCustomerPetInfoModel> pets,
            Map<Long, PlaygroupModel> playgroupMap,
            Pair<List<BatchListBindingPetCodeResponse.Binding>, List<BusinessPetCodeModel>> petCodePair,
            Map<Long, BusinessCustomerInfoModel> customerMap) {

        Map<Long, BusinessPetCodeModel> petCodeMap = petCodePair.value().stream()
                .collect(Collectors.toMap(BusinessPetCodeModel::getId, Function.identity()));
        Map<Long, List<BatchListBindingPetCodeResponse.Binding>> petCodeBindingMap = petCodePair.key().stream()
                .collect(Collectors.groupingBy(BatchListBindingPetCodeResponse.Binding::getPetId));

        Map<Long, List<ListDailyPlaygroupCardResult.PetCodeBindingsView>> dailyPlaygroupCardPetCodeBindingsViewMap =
                toDailyPlaygroupCardPetCodeBindingsViews(petCodeMap, petCodeBindingMap);

        return pets.stream()
                .map(pet -> toDailyPlaygroupCardPetView(
                        pet,
                        playgroupMap.get(pet.getPlaygroupId()),
                        customerMap.get(pet.getCustomerId()),
                        dailyPlaygroupCardPetCodeBindingsViewMap.getOrDefault(pet.getId(), List.of())))
                .toList();
    }

    default ListDailyPlaygroupCardResult.PetView toDailyPlaygroupCardPetView(
            BusinessCustomerPetInfoModel pet,
            PlaygroupModel playgroup,
            BusinessCustomerInfoModel customer,
            List<ListDailyPlaygroupCardResult.PetCodeBindingsView> petCodeBindings) {
        return ListDailyPlaygroupCardResult.PetView.newBuilder()
                .setPetId(pet.getId())
                .setPetName(pet.getPetName())
                .setPetType(pet.getPetType())
                .setPetPlaygroupColor(Objects.nonNull(playgroup) ? playgroup.getColorCode() : "")
                .setCustomer(toDailyPlaygroupCardCustomerView(customer))
                .addAllPetCodeBindings(petCodeBindings)
                .setPetBreed(pet.getBreed())
                .build();
    }

    default ListDailyPlaygroupCardResult.CustomerView toDailyPlaygroupCardCustomerView(
            BusinessCustomerInfoModel customer) {
        if (customer == null) {
            return ListDailyPlaygroupCardResult.CustomerView.getDefaultInstance();
        }
        return ListDailyPlaygroupCardResult.CustomerView.newBuilder()
                .setCustomerId(customer.getId())
                .setFirstName(customer.getFirstName())
                .setLastName(customer.getLastName())
                .build();
    }

    default Map<Long /*pet_id*/, List<ListDailyPlaygroupCardResult.PetCodeBindingsView>>
            toDailyPlaygroupCardPetCodeBindingsViews(
                    Map<Long, BusinessPetCodeModel> petCodeMap,
                    Map<Long, List<BatchListBindingPetCodeResponse.Binding>> petCodeBindingMap) {

        return petCodeBindingMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                        .map(binding -> toDailyPlaygroupCardPetCodeBindingsView(binding, petCodeMap))
                        .filter(Objects::nonNull)
                        .toList()));
    }

    default ListDailyPlaygroupCardResult.PetCodeBindingsView toDailyPlaygroupCardPetCodeBindingsView(
            BatchListBindingPetCodeResponse.Binding binding, Map<Long, BusinessPetCodeModel> petCodeMap) {
        var petCode = petCodeMap.get(binding.getCodeId());
        if (petCode == null) {
            return null;
        }
        return ListDailyPlaygroupCardResult.PetCodeBindingsView.newBuilder()
                .setAbbreviation(petCode.getAbbreviation())
                .setColor(petCode.getColor())
                .setUniqueComment(binding.getComment())
                .build();
    }

    default Map<Long /*pet_id*/, List<ListAppointmentCardResult.PetCodeBindingsView>>
            toAppointmentCardPetCodeBindingsViews(
                    Map<Long, BusinessPetCodeModel> petCodeMap,
                    Map<Long, List<BatchListBindingPetCodeResponse.Binding>> petCodeBindingMap) {

        return petCodeBindingMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, entry -> entry.getValue().stream()
                        .map(binding -> toDailyAppointmentCardPetCodeBindingsView(binding, petCodeMap))
                        .filter(Objects::nonNull)
                        .toList()));
    }

    default ListAppointmentCardResult.PetCodeBindingsView toDailyAppointmentCardPetCodeBindingsView(
            BatchListBindingPetCodeResponse.Binding binding, Map<Long, BusinessPetCodeModel> petCodeMap) {
        var petCode = petCodeMap.get(binding.getCodeId());
        if (petCode == null) {
            return null;
        }
        return ListAppointmentCardResult.PetCodeBindingsView.newBuilder()
                .setPetCodeId(petCode.getId())
                .setAbbreviation(petCode.getAbbreviation())
                .setColor(petCode.getColor())
                .setUniqueComment(binding.getComment())
                .build();
    }
}
