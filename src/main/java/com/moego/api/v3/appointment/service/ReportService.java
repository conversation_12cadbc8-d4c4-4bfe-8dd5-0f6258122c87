package com.moego.api.v3.appointment.service;

import com.moego.idl.service.reporting.v2.ExternalServiceGrpc;
import com.moego.idl.service.reporting.v2.ListCustomerUnpaidAmountRequest;
import com.moego.idl.service.reporting.v2.ListCustomerUnpaidAmountResponse;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class ReportService {

    private final ExternalServiceGrpc.ExternalServiceBlockingStub externalService;
    private final CompanySettingService companySettingService;

    public Map<Long, ListCustomerUnpaidAmountResponse.CustomerUnpaidAmount> listCustomerUnpaidAmount(
            long companyId, List<Long> customerIds) {
        String currencyCode = companySettingService.getComapnyCurrency(companyId);

        var customerUnpaidAmounts = externalService
                .listCustomerUnpaidAmount(ListCustomerUnpaidAmountRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllCustomerIds(customerIds)
                        .build())
                .getCustomerUnpaidAmountsList();

        return customerUnpaidAmounts.stream()
                .map(item -> {
                    var amount = item.getAmount();
                    if (Strings.isNotBlank(amount.getCurrencyCode())) {
                        return item;
                    }
                    return item.toBuilder()
                            .setAmount(amount.toBuilder()
                                    .setCurrencyCode(currencyCode)
                                    .build())
                            .build();
                })
                .collect(Collectors.toMap(
                        ListCustomerUnpaidAmountResponse.CustomerUnpaidAmount::getCustomerId,
                        Function.identity(),
                        (a, b) -> a));
    }
}
