package com.moego.api.v3.appointment.converter;

import com.google.type.Date;
import com.moego.idl.api.appointment.v1.CustomerPackageView;
import com.moego.idl.models.pkg.v1.Item;
import com.moego.idl.models.pkg.v1.PackageModel;
import com.moego.server.grooming.dto.GroomingPackageDTO;
import com.moego.server.grooming.dto.GroomingPackageServiceDTO;
import com.moego.server.retail.dto.PackageInfoDto;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface PackageConverter {

    PackageConverter INSTANCE = Mappers.getMapper(PackageConverter.class);

    default CustomerPackageView convert(GroomingPackageDTO groomingPackageDTO) {
        LocalDate startDate = Instant.ofEpochSecond(groomingPackageDTO.getStartTime())
                .atZone(ZoneId.systemDefault())
                .toLocalDate();
        CustomerPackageView.Builder builder = CustomerPackageView.newBuilder()
                .setCustomerId(groomingPackageDTO.getCustomerId())
                .setPackageId(groomingPackageDTO.getId())
                .setPackageName(groomingPackageDTO.getPackageName())
                .setStartDate(Date.newBuilder()
                        .setYear(startDate.getYear())
                        .setMonth(startDate.getMonthValue())
                        .setDay(startDate.getDayOfMonth())
                        .build());
        if (!Objects.equals(GroomingPackageDTO.ExpirationDate.NEVER_EXPIRE, groomingPackageDTO.getExpirationDate())
                && StringUtils.hasText(groomingPackageDTO.getExpirationDate())) {
            LocalDate parse = LocalDate.parse(groomingPackageDTO.getExpirationDate());
            builder.setExpirationDate(Date.newBuilder()
                    .setYear(parse.getYear())
                    .setMonth(parse.getMonthValue())
                    .setDay(parse.getDayOfMonth())
                    .build());
        }
        return builder.build();
    }

    default CustomerPackageView.PackageDetail convert(GroomingPackageServiceDTO groomingPackageServiceDTO) {
        if (groomingPackageServiceDTO == null) {
            return null;
        }

        var packageDetail = CustomerPackageView.PackageDetail.newBuilder();

        if (groomingPackageServiceDTO.getServiceId() != null) {
            packageDetail.setServiceId(groomingPackageServiceDTO.getServiceId());
        }
        if (groomingPackageServiceDTO.getServiceName() != null) {
            packageDetail.setServiceName(groomingPackageServiceDTO.getServiceName());
        }
        if (groomingPackageServiceDTO.getRemainingQuantity() != null) {
            packageDetail.setRemainingQuantity(groomingPackageServiceDTO.getRemainingQuantity());
        }
        if (groomingPackageServiceDTO.getTotalQuantity() != null) {
            packageDetail.setTotalQuantity(groomingPackageServiceDTO.getTotalQuantity());
        }
        if (groomingPackageServiceDTO.getServices() != null) {
            for (var service : groomingPackageServiceDTO.getServices()) {
                var s = CustomerPackageView.PackageDetail.Service.newBuilder();
                if (service.getServiceId() != null) {
                    s.setId(service.getServiceId());
                }
                if (service.getName() != null) {
                    s.setName(service.getName());
                }
                if (service.getUnitPrice() != null) {
                    s.setUnitPrice(service.getUnitPrice().doubleValue());
                }
                packageDetail.addServices(s.build());
            }
        }

        return packageDetail.build();
    }

    default PackageModel toModel(PackageInfoDto dto) {
        if (dto == null) {
            return null;
        }

        PackageModel.Builder packageModel = PackageModel.newBuilder();
        packageModel.setId(dto.getId().longValue());
        packageModel.setPrice(dto.getPrice().doubleValue());
        packageModel.setTotalValue(dto.getTotalValue().doubleValue());
        packageModel.setTaxId(dto.getTaxId().longValue());
        packageModel.setName(dto.getName());
        packageModel.setDescription(dto.getDescription());
        packageModel.setIsActive(dto.getIsActive());
        packageModel.setEnableOnlineBooking(dto.getEnableOnlineBooking());
        packageModel.setExpirationDays(dto.getExpirationDays());
        packageModel.setBusinessId(dto.getBusinessId().longValue());
        packageModel.setCompanyId(dto.getCompanyId().longValue());

        if (dto.getItems() != null) {
            packageModel.addAllItems(toModel(dto.getItems()));
        }

        return packageModel.build();
    }

    default List<Item> toModel(List<PackageInfoDto.Item> items) {
        List<Item> result = new ArrayList<>();
        for (var item : items) {
            Item.Builder builder = Item.newBuilder();
            builder.addAllServiceIds(item.getServices().stream()
                    .map(PackageInfoDto.Service::getServiceId)
                    .map(Long::valueOf)
                    .toList());
            builder.setQuantity(item.getQuantity());
            result.add(builder.build());
        }
        return result;
    }
}
