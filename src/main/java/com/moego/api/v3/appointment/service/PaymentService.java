package com.moego.api.v3.appointment.service;

import com.moego.idl.api.appointment.v1.CustomerCompositeOverview;
import com.moego.idl.models.payment.v2.EntityType;
import com.moego.idl.models.payment.v2.RecurringPaymentMethodModel;
import com.moego.idl.models.payment.v2.User;
import com.moego.idl.service.payment.v2.ListRecurringPaymentMethodsRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.utils.PageUtil;
import com.moego.lib.utils.model.Pair;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service("appointmentPaymentService")
@RequiredArgsConstructor
public class PaymentService {
    private final com.moego.idl.service.payment.v2.PaymentServiceGrpc.PaymentServiceBlockingStub paymentClient;

    private static final int BATCH_SIZE = 100;

    public Map<Long, CustomerCompositeOverview.COFStatus> getCOFStatusMap(List<Long> customerIds) {
        if (customerIds.isEmpty()) {
            return Collections.emptyMap();
        }
        var paymentMethods = PageUtil.fetchAllGrpc(BATCH_SIZE, (page, size) -> {
            var request = ListRecurringPaymentMethodsRequest.newBuilder()
                    .setPaginationRequest(
                            PaginationRequest.newBuilder().setPageNum(page).setPageSize(size))
                    .setFilter(ListRecurringPaymentMethodsRequest.Filter.newBuilder()
                            .addAllUsers(customerIds.stream()
                                    .map(customerId -> User.newBuilder()
                                            .setEntityType(EntityType.CUSTOMER)
                                            .setEntityId(customerId)
                                            .build())
                                    .toList())
                            .build())
                    .build();
            var response = paymentClient.listRecurringPaymentMethods(request);
            return Pair.of(response.getPaymentMethodsList(), response.getPaginationResponse());
        });

        return paymentMethods.stream()
                .collect(Collectors.groupingBy(r -> r.getUser().getEntityId()))
                .entrySet()
                .stream()
                .collect(Collectors.toMap(Map.Entry::getKey, e -> getCOFStatus(e.getValue())));
    }

    private static CustomerCompositeOverview.COFStatus getCOFStatus(
            List<RecurringPaymentMethodModel> recurringPaymentMethods) {
        if (recurringPaymentMethods.isEmpty()) {
            return CustomerCompositeOverview.COFStatus.NO_CARD_ON_FILE;
        }

        if (recurringPaymentMethods.stream()
                .anyMatch(r -> r.getExtra().getCard().getIsAuthorized())) {
            return CustomerCompositeOverview.COFStatus.AUTHORIZED;
        }

        if (recurringPaymentMethods.stream()
                .anyMatch(r -> !r.getExtra().getCard().hasIsAuthorized())) {
            return CustomerCompositeOverview.COFStatus.PENDING;
        }

        return CustomerCompositeOverview.COFStatus.FAILED;
    }
}
