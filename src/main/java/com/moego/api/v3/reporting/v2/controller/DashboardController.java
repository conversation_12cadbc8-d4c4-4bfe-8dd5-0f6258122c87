package com.moego.api.v3.reporting.v2.controller;

import com.moego.api.v3.reporting.v2.converter.DashboardConverter;
import com.moego.idl.api.reporting.v2.DashboardServiceGrpc;
import com.moego.idl.api.reporting.v2.FetchDashboardDataRequest;
import com.moego.idl.api.reporting.v2.FetchDashboardDataResponse;
import com.moego.idl.api.reporting.v2.QueryDashboardPagesRequest;
import com.moego.idl.api.reporting.v2.QueryDashboardPagesResponse;
import com.moego.idl.models.reporting.v2.TokenInfo;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class DashboardController extends DashboardServiceGrpc.DashboardServiceImplBase {

    private final com.moego.idl.service.reporting.v2.DashboardServiceGrpc.DashboardServiceBlockingStub dashboardService;
    private final DashboardConverter converter = DashboardConverter.INSTANCE;

    @Override
    @Auth(AuthType.COMPANY)
    public void queryDashboardPages(
            QueryDashboardPagesRequest request, StreamObserver<QueryDashboardPagesResponse> responseObserver) {
        var res = dashboardService.queryDashboardPages(
                converter.toQueryPageParams(request, buildTokenInfo(AuthContext.get())));

        responseObserver.onNext(converter.toQueryPagesResponse(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void fetchDashboardData(
            FetchDashboardDataRequest request, StreamObserver<FetchDashboardDataResponse> responseObserver) {
        var res = dashboardService.fetchDashboardData(converter.toFetchDataParams(request));

        responseObserver.onNext(converter.toFetchDashboardDataResponse(res));
        responseObserver.onCompleted();
    }

    private TokenInfo buildTokenInfo(AuthContext context) {
        return TokenInfo.newBuilder()
                .setCompanyId(context.companyId())
                .setStaffId(context.staffId())
                .build();
    }
}
