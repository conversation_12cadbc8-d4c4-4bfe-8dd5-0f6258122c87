package com.moego.api.v3.reporting.v2.converter;

import com.moego.idl.models.reporting.v2.GetDimensionsParams;
import com.moego.idl.models.reporting.v2.GetDimensionsResult;
import com.moego.idl.models.reporting.v2.GetMetricsCategoriesParams;
import com.moego.idl.models.reporting.v2.GetMetricsCategoriesResult;
import com.moego.idl.models.reporting.v2.ReportingScene;
import com.moego.idl.service.reporting.v2.GetDimensionsRequest;
import com.moego.idl.service.reporting.v2.GetDimensionsResponse;
import com.moego.idl.service.reporting.v2.GetMetricsCategoriesRequest;
import com.moego.idl.service.reporting.v2.GetMetricsCategoriesResponse;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface AttributeConverter {

    AttributeConverter INSTANCE = Mappers.getMapper(AttributeConverter.class);

    GetDimensionsRequest toGetDimensionsParams(GetDimensionsParams params, ReportingScene scene);

    GetDimensionsResult toGetDimensionsResult(GetDimensionsResponse response);

    GetMetricsCategoriesRequest toGetMetricsCategoriesParams(GetMetricsCategoriesParams params, ReportingScene scene);

    GetMetricsCategoriesResult toGetMetricsCategoriesResult(GetMetricsCategoriesResponse response);
}
