package com.moego.api.v3.reporting.v2.converter;

import com.moego.idl.api.reporting.v2.FetchDashboardDataRequest;
import com.moego.idl.api.reporting.v2.FetchDashboardDataResponse;
import com.moego.idl.api.reporting.v2.QueryDashboardPagesRequest;
import com.moego.idl.api.reporting.v2.QueryDashboardPagesResponse;
import com.moego.idl.models.reporting.v2.TokenInfo;
import com.moego.idl.service.reporting.v2.FetchDashboardDataParams;
import com.moego.idl.service.reporting.v2.QueryDashboardPagesParams;
import com.moego.idl.service.reporting.v2.QueryDashboardPagesResult;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface DashboardConverter {

    DashboardConverter INSTANCE = Mappers.getMapper(DashboardConverter.class);

    QueryDashboardPagesParams toQueryPageParams(QueryDashboardPagesRequest request, TokenInfo tokenInfo);

    QueryDashboardPagesResponse toQueryPagesResponse(QueryDashboardPagesResult result);

    FetchDashboardDataParams toFetchDataParams(FetchDashboardDataRequest request);

    FetchDashboardDataResponse toFetchDashboardDataResponse(
            com.moego.idl.service.reporting.v2.FetchDashboardDataResult res);
}
