package com.moego.api.v3.reporting.v2.converter;

import com.moego.idl.models.reporting.v2.DeleteCustomReportParams;
import com.moego.idl.models.reporting.v2.DeleteCustomReportResult;
import com.moego.idl.models.reporting.v2.DuplicateCustomReportParams;
import com.moego.idl.models.reporting.v2.DuplicateCustomReportResult;
import com.moego.idl.models.reporting.v2.ModifyCustomDiagramParams;
import com.moego.idl.models.reporting.v2.ModifyCustomDiagramResult;
import com.moego.idl.models.reporting.v2.ReportingScene;
import com.moego.idl.models.reporting.v2.SaveCustomReportParams;
import com.moego.idl.models.reporting.v2.SaveCustomReportResult;
import com.moego.idl.service.reporting.v2.DeleteCustomReportRequest;
import com.moego.idl.service.reporting.v2.DeleteCustomReportResponse;
import com.moego.idl.service.reporting.v2.DuplicateCustomReportRequest;
import com.moego.idl.service.reporting.v2.DuplicateCustomReportResponse;
import com.moego.idl.service.reporting.v2.ModifyCustomDiagramRequest;
import com.moego.idl.service.reporting.v2.ModifyCustomDiagramResponse;
import com.moego.idl.service.reporting.v2.SaveCustomReportRequest;
import com.moego.idl.service.reporting.v2.SaveCustomReportResponse;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface CustomReportConverter {

    CustomReportConverter INSTANCE = Mappers.getMapper(CustomReportConverter.class);

    SaveCustomReportRequest toSaveCustomReportRequest(SaveCustomReportParams params, ReportingScene scene);

    SaveCustomReportResult toSaveCustomReportResult(SaveCustomReportResponse response);

    ModifyCustomDiagramRequest toModifyCustomDiagramRequest(ModifyCustomDiagramParams params, ReportingScene scene);

    ModifyCustomDiagramResult toModifyCustomDiagramResult(ModifyCustomDiagramResponse response);

    DuplicateCustomReportRequest toDuplicateCustomReportParams(
            DuplicateCustomReportParams params, ReportingScene scene);

    DuplicateCustomReportResult toDuplicateCustomReportResult(DuplicateCustomReportResponse response);

    DeleteCustomReportRequest toDeleteCustomReportRequest(DeleteCustomReportParams params, ReportingScene scene);

    DeleteCustomReportResult toDeleteCustomReportResult(DeleteCustomReportResponse response);
}
