package com.moego.api.v3.reporting.v2.controller;

import com.google.protobuf.Empty;
import com.moego.api.v3.reporting.v2.converter.ReportsConverter;
import com.moego.idl.api.reporting.v2.ExportReportDataRequest;
import com.moego.idl.api.reporting.v2.ExportReportDataResponse;
import com.moego.idl.api.reporting.v2.FetchReportDataRequest;
import com.moego.idl.api.reporting.v2.FetchReportDataResponse;
import com.moego.idl.api.reporting.v2.MarkReportFavoriteRequest;
import com.moego.idl.api.reporting.v2.MarkReportFavoriteResponse;
import com.moego.idl.api.reporting.v2.QueryReportMetasRequest;
import com.moego.idl.api.reporting.v2.QueryReportPagesRequest;
import com.moego.idl.api.reporting.v2.QueryReportPagesResponse;
import com.moego.idl.api.reporting.v2.QueryReportsMetasResponse;
import com.moego.idl.api.reporting.v2.ReportServiceGrpc;
import com.moego.idl.api.reporting.v2.SaveReportCustomizeConfigRequest;
import com.moego.idl.models.reporting.v2.ExportDataParams;
import com.moego.idl.models.reporting.v2.ExportDataResult;
import com.moego.idl.models.reporting.v2.FetchDataParams;
import com.moego.idl.models.reporting.v2.FetchDataResult;
import com.moego.idl.models.reporting.v2.QueryMetasParams;
import com.moego.idl.models.reporting.v2.QueryMetasResult;
import com.moego.idl.models.reporting.v2.QueryPageMetaParams;
import com.moego.idl.models.reporting.v2.QueryPageMetaResult;
import com.moego.idl.models.reporting.v2.ReportingScene;
import com.moego.idl.models.reporting.v2.ScopeFilter;
import com.moego.idl.models.reporting.v2.TokenInfo;
import com.moego.idl.service.organization.v1.BatchGetLocationIdListRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import org.springframework.util.CollectionUtils;

@GrpcService
@RequiredArgsConstructor
public class ReportsController extends ReportServiceGrpc.ReportServiceImplBase {

    private final com.moego.idl.service.reporting.v2.ReportServiceGrpc.ReportServiceBlockingStub reportService;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessService;
    private final ReportsConverter converter = ReportsConverter.INSTANCE;

    @Override
    @Auth(AuthType.COMPANY)
    public void queryReportPages(
            QueryReportPagesRequest request, StreamObserver<QueryReportPagesResponse> responseObserver) {
        var res = reportService.queryReportPages(
                converter.toQueryPagesParams(request, buildTokenInfo(AuthContext.get())));

        responseObserver.onNext(ReportsConverter.INSTANCE.toQueryPagesResponse(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void markReportFavorite(
            MarkReportFavoriteRequest request, StreamObserver<MarkReportFavoriteResponse> responseObserver) {
        var res = reportService.markReportFavorite(
                converter.toMarkFavoriteParams(request, buildTokenInfo(AuthContext.get())));

        responseObserver.onNext(MarkReportFavoriteResponse.newBuilder()
                .setResult(res.getResult())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void saveReportCustomizeConfig(
            SaveReportCustomizeConfigRequest request, StreamObserver<Empty> responseObserver) {
        var res = reportService.saveReportCustomizeConfig(
                converter.toSaveCustomizeConfigParams(request, buildTokenInfo(AuthContext.get())));

        responseObserver.onNext(res);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void queryReportMetas(
            QueryReportMetasRequest request, StreamObserver<QueryReportsMetasResponse> responseObserver) {
        var res = reportService.queryReportMetas(
                converter.toQueryMetasParams(request, buildTokenInfo(AuthContext.get())));

        responseObserver.onNext(converter.toQueryMetasResponse(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void fetchReportData(
            FetchReportDataRequest request, StreamObserver<FetchReportDataResponse> responseObserver) {
        var res =
                reportService.fetchReportData(converter.toFetchDataParams(request, buildTokenInfo(AuthContext.get())));
        responseObserver.onNext(converter.toFetchDataResponse(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void exportReportData(
            ExportReportDataRequest request, StreamObserver<ExportReportDataResponse> responseObserver) {
        var res = reportService.exportReportData(
                converter.toExportDataParams(request, buildTokenInfo(AuthContext.get())));
        responseObserver.onNext(converter.toExportDataResponse(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void queryPages(QueryPageMetaParams params, StreamObserver<QueryPageMetaResult> responseObserver) {
        var res = reportService.queryPages(converter.toQueryPageMetaRequest(params, ReportingScene.COMMON));
        responseObserver.onNext(converter.toQueryPageMetaResult(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void queryMetas(QueryMetasParams params, StreamObserver<QueryMetasResult> responseObserver) {
        var res = reportService.queryMetas(converter.toQueryMetasRequest(params, ReportingScene.COMMON));
        responseObserver.onNext(converter.toQueryMetasResult(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void fetchData(FetchDataParams params, StreamObserver<FetchDataResult> responseObserver) {
        var request = converter.toFetchDataRequest(params, ReportingScene.COMMON);
        if (!params.hasScope()
                || (params.getScope().getAllScopes()
                        && CollectionUtils.isEmpty(params.getScope().getScopeIdsList()))) {
            var locationIds = businessService
                    .batchGetLocationIdList(BatchGetLocationIdListRequest.newBuilder()
                            .addCompanyId(AuthContext.get().companyId())
                            .build())
                    .getCompanyLocationIdListMapMap()
                    .get(AuthContext.get().companyId())
                    .getValuesList();
            request = request.toBuilder()
                    .setScope(ScopeFilter.newBuilder()
                            .setAllScopes(true)
                            .addAllScopeIds(locationIds)
                            .build())
                    .build();
        }
        var res = reportService.fetchData(request);
        responseObserver.onNext(converter.toFetchDataResult(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void exportData(ExportDataParams params, StreamObserver<ExportDataResult> responseObserver) {
        var request = converter.toExportDataRequest(params, ReportingScene.COMMON);
        if (!params.hasScope()
                || (params.getScope().getAllScopes()
                        && CollectionUtils.isEmpty(params.getScope().getScopeIdsList()))) {
            var locationIds = businessService
                    .batchGetLocationIdList(BatchGetLocationIdListRequest.newBuilder()
                            .addCompanyId(AuthContext.get().companyId())
                            .build())
                    .getCompanyLocationIdListMapMap()
                    .get(AuthContext.get().companyId())
                    .getValuesList();
            request = request.toBuilder()
                    .setScope(ScopeFilter.newBuilder()
                            .setAllScopes(true)
                            .addAllScopeIds(locationIds)
                            .build())
                    .build();
        }
        var res = reportService.exportData(request);
        responseObserver.onNext(converter.toExportDataResult(res));
        responseObserver.onCompleted();
    }

    private TokenInfo buildTokenInfo(AuthContext context) {
        return TokenInfo.newBuilder()
                .setCompanyId(context.companyId())
                .setStaffId(context.staffId())
                .build();
    }
}
