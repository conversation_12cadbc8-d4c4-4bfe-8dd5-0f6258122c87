package com.moego.api.v3.reporting.v2.controller;

import com.moego.api.v3.reporting.v2.converter.AttributeConverter;
import com.moego.idl.api.reporting.v2.AttributeServiceGrpc;
import com.moego.idl.models.reporting.v2.GetDimensionsParams;
import com.moego.idl.models.reporting.v2.GetDimensionsResult;
import com.moego.idl.models.reporting.v2.GetMetricsCategoriesParams;
import com.moego.idl.models.reporting.v2.GetMetricsCategoriesResult;
import com.moego.idl.models.reporting.v2.ReportingScene;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class AttributeController extends AttributeServiceGrpc.AttributeServiceImplBase {

    private final com.moego.idl.service.reporting.v2.AttributeServiceGrpc.AttributeServiceBlockingStub attributeService;
    private final AttributeConverter converter = AttributeConverter.INSTANCE;

    @Override
    @Auth(AuthType.COMPANY)
    public void getDimensions(GetDimensionsParams request, StreamObserver<GetDimensionsResult> responseObserver) {
        var res = attributeService.getDimensions(converter.toGetDimensionsParams(request, ReportingScene.COMMON));
        responseObserver.onNext(converter.toGetDimensionsResult(res));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getMetricsCategories(
            GetMetricsCategoriesParams request, StreamObserver<GetMetricsCategoriesResult> responseObserver) {
        var res = attributeService.getMetricsCategories(
                converter.toGetMetricsCategoriesParams(request, ReportingScene.COMMON));
        responseObserver.onNext(converter.toGetMetricsCategoriesResult(res));
        responseObserver.onCompleted();
    }
}
