package com.moego.api.v3.fulfillment.converter;

import com.moego.idl.models.organization.v1.TaxRuleModel;
import com.moego.idl.models.organization.v1.TaxRuleModelPublicView;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface TaxConverter {

    TaxConverter INSTANCE = Mappers.getMapper(TaxConverter.class);

    TaxRuleModelPublicView toView(TaxRuleModel model);
}
