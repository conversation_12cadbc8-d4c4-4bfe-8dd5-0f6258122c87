package com.moego.api.v3.fulfillment.consts;

import com.moego.idl.models.fulfillment.v1.Status;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/4/18
 */
public interface FulfillmentStatusConst {

    List<Status> PRE_PAYMENT_MODE_ACTIVE_STATUSES =
            List.of(Status.UNCONFIRMED, Status.CONFIRMED, Status.CHECK_IN, Status.READY, Status.FINISHED);

    List<Status> POST_PAYMENT_MODE_ACTIVE_STATUSES = List.of(
            Status.UNCONFIRMED, Status.CONFIRMED, Status.CHECK_IN, Status.READY, Status.FINISHED, Status.COMPLETED);
}
