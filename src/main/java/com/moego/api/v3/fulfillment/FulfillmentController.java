package com.moego.api.v3.fulfillment;

import com.moego.idl.api.fulfillment.v1.CheckInGroupClassSessionParams;
import com.moego.idl.api.fulfillment.v1.CheckInGroupClassSessionResult;
import com.moego.idl.api.fulfillment.v1.EnrollPetParams;
import com.moego.idl.api.fulfillment.v1.EnrollPetResult;
import com.moego.idl.api.fulfillment.v1.FulfillmentServiceGrpc;
import com.moego.idl.api.fulfillment.v1.PreviewOrderLineItemsParams;
import com.moego.idl.api.fulfillment.v1.PreviewOrderLineItemsResult;
import com.moego.idl.api.fulfillment.v1.RemovePetParams;
import com.moego.idl.api.fulfillment.v1.RemovePetResult;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.PreviewOrderLineItemsRequest;
import com.moego.idl.service.fulfillment.v1.CheckInGroupClassSessionRequest;
import com.moego.idl.service.fulfillment.v1.EnrollPetRequest;
import com.moego.idl.service.fulfillment.v1.FulfillmentServiceGrpc.FulfillmentServiceBlockingStub;
import com.moego.idl.service.fulfillment.v1.GroupClassDetailServiceGrpc;
import com.moego.idl.service.fulfillment.v1.RemovePetRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2025/4/14
 */
@GrpcService
@RequiredArgsConstructor
public class FulfillmentController extends FulfillmentServiceGrpc.FulfillmentServiceImplBase {

    private final FulfillmentServiceBlockingStub fulfillmentStub;
    private final GroupClassDetailServiceGrpc.GroupClassDetailServiceBlockingStub groupClassDetailStub;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void enrollPet(EnrollPetParams request, StreamObserver<EnrollPetResult> responseObserver) {
        var enrollPetRequest = EnrollPetRequest.newBuilder()
                .setBusinessId(request.getBusinessId())
                .setInstanceId(request.getInstanceId())
                .setPetId(request.getPetId())
                .setCompanyId(AuthContext.get().companyId())
                .setSource(request.getSource())
                .setStaffId(AuthContext.get().staffId())
                .build();

        var response = fulfillmentStub.enrollPet(enrollPetRequest);

        responseObserver.onNext(
                EnrollPetResult.newBuilder().setOrderId(response.getOrderId()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void removePet(RemovePetParams request, StreamObserver<RemovePetResult> responseObserver) {
        var req = RemovePetRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setInstanceId(request.getInstanceId())
                .setPetId(request.getPetId())
                .setAutoRefundOrder(request.getAutoRefundOrder())
                .build();

        fulfillmentStub.removePet(req);

        responseObserver.onNext(RemovePetResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void checkInGroupClassSession(
            CheckInGroupClassSessionParams request, StreamObserver<CheckInGroupClassSessionResult> responseObserver) {
        var builder = CheckInGroupClassSessionRequest.newBuilder()
                .setBusinessId(request.getBusinessId())
                .addAllPetIds(request.getPetIdsList())
                .setCompanyId(AuthContext.get().companyId())
                .setCheckInStaffId(AuthContext.get().staffId());
        if (request.hasGroupClassSessionId()) {
            builder.setGroupClassSessionId(request.getGroupClassSessionId());
        }
        var response = groupClassDetailStub.checkInGroupClassSession(builder.build());

        responseObserver.onNext(CheckInGroupClassSessionResult.newBuilder()
                .setCheckInCount(response.getCheckedInPetIdsCount())
                .addAllCheckedInInstanceIds(response.getCheckedInInstanceIdsList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void previewOrderLineItems(
            PreviewOrderLineItemsParams request, StreamObserver<PreviewOrderLineItemsResult> responseObserver) {
        var response = appointmentStub.previewOrderLineItems(PreviewOrderLineItemsRequest.newBuilder()
                .setAppointmentId(request.getId())
                .build());
        responseObserver.onNext(PreviewOrderLineItemsResult.newBuilder()
                .addAllPetServices(response.getPetServicesList())
                .addAllSurcharges(response.getSurchargesList())
                .build());
        responseObserver.onCompleted();
    }
}
