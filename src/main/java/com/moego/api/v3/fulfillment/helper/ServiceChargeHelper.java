package com.moego.api.v3.fulfillment.helper;

import com.moego.idl.models.order.v1.ServiceCharge;
import com.moego.idl.service.order.v1.GetServiceChargeListInput;
import com.moego.idl.service.order.v1.ServiceChargeServiceGrpc;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2025/5/15
 */
@Service
@RequiredArgsConstructor
public class ServiceChargeHelper {

    private final ServiceChargeServiceGrpc.ServiceChargeServiceBlockingStub serviceChargeStub;

    public Map<Long, ServiceCharge> listByIds(long companyId, long businessId, List<Long> serviceChargeIds) {
        if (CollectionUtils.isEmpty(serviceChargeIds)) {
            return Map.of();
        }
        var serviceCharges = serviceChargeStub
                .getServiceChargeList(GetServiceChargeListInput.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .build())
                .getServiceChargeList();
        var serviceChargeIdSet = new HashSet<>(serviceChargeIds);
        return serviceCharges.stream()
                .filter(i -> serviceChargeIdSet.contains(i.getId()))
                .collect(Collectors.toMap(ServiceCharge::getId, Function.identity()));
    }
}
