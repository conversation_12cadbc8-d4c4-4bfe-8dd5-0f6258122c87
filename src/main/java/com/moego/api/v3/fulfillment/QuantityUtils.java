package com.moego.api.v3.fulfillment;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2025/5/13
 */
public class QuantityUtils {

    public static int calculateDays(String startDate, String endDate) {
        if (!StringUtils.hasLength(startDate) || !StringUtils.hasLength(endDate)) {
            return 1;
        }

        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);

        // Including both start and end day
        return (int) (ChronoUnit.DAYS.between(start, end) + 1);
    }

    public static int calculateNights(String startDate, String endDate) {
        if (!StringUtils.hasLength(startDate) || !StringUtils.hasLength(endDate)) {
            return 1;
        }

        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);

        // Number of nights is one less than number of days
        return (int) ChronoUnit.DAYS.between(start, end);
    }

    public static int calculateHours(String startDate, String endDate, int startTimeMinutes, int endTimeMinutes) {
        if (!StringUtils.hasLength(startDate) || !StringUtils.hasLength(endDate)) {
            return 1;
        }

        LocalDate startLD = LocalDate.parse(startDate);
        LocalDate endLD = LocalDate.parse(endDate);

        // Convert minutes to hours and minutes
        int startHour = startTimeMinutes / 60;
        int startMinute = startTimeMinutes % 60;
        int endHour = endTimeMinutes / 60;
        int endMinute = endTimeMinutes % 60;

        LocalDateTime start = LocalDateTime.of(
                startLD.getYear(), startLD.getMonth(), startLD.getDayOfMonth(), startHour, startMinute);
        LocalDateTime end =
                LocalDateTime.of(endLD.getYear(), endLD.getMonth(), endLD.getDayOfMonth(), endHour, endMinute);

        // Calculate hours between the two times, rounding up partial hours
        long minutes = ChronoUnit.MINUTES.between(start, end);
        return (int) Math.ceil(minutes / 60.0);
    }

    public static int calculateDates(String specificDates) {
        if (!StringUtils.hasLength(specificDates)) {
            return 1;
        }

        // Assuming specific dates are comma-separated
        return specificDates.split(",").length;
    }
}
