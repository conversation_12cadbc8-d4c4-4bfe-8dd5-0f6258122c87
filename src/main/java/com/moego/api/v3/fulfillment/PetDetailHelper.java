package com.moego.api.v3.fulfillment;

import com.moego.idl.models.appointment.v1.BoardingSplitLodgingModel;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.models.appointment.v2.PricingRuleApplyLogModel;
import com.moego.idl.models.appointment.v2.PricingRuleApplySourceType;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.organization.v1.TaxRuleModel;
import com.moego.idl.service.appointment.v1.BoardingSplitLodgingServiceGrpc;
import com.moego.idl.service.appointment.v1.ListBoardingSplitLodgingsRequest;
import com.moego.idl.service.appointment.v2.ListPricingRuleApplyLogRequest;
import com.moego.idl.service.appointment.v2.PricingRuleApplyServiceGrpc;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.GetLodgingTypeListRequest;
import com.moego.idl.service.offering.v1.GetLodgingUnitListRequest;
import com.moego.idl.service.offering.v1.LodgingTypeServiceGrpc;
import com.moego.idl.service.offering.v1.LodgingUnitServiceGrpc;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.organization.v1.BatchGetTaxRuleRequest;
import com.moego.idl.service.organization.v1.QueryStaffByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.idl.service.organization.v1.TaxRuleServiceGrpc;
import com.moego.lib.utils.model.Pair;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2025/5/13
 */
@Service
@RequiredArgsConstructor
public class PetDetailHelper {

    private final LodgingUnitServiceGrpc.LodgingUnitServiceBlockingStub lodgingUnitStub;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementStub;
    private final BoardingSplitLodgingServiceGrpc.BoardingSplitLodgingServiceBlockingStub boardingSplitLodgingStub;
    private final PricingRuleApplyServiceGrpc.PricingRuleApplyServiceBlockingStub pricingRuleApplyStub;
    private final TaxRuleServiceGrpc.TaxRuleServiceBlockingStub taxRuleStub;
    private final LodgingTypeServiceGrpc.LodgingTypeServiceBlockingStub lodgingTypeStub;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffStub;

    public Map<Long, StaffModel> getStaffMap(Collection<PetDetailModel> petDetails) {
        var staffIds = petDetails.stream()
                .map(PetDetailModel::getStaffId)
                .distinct()
                .filter(s -> s > 0)
                .toList();
        if (CollectionUtils.isEmpty(staffIds)) {
            return Map.of();
        }
        return staffStub
                .queryStaffByIds(QueryStaffByIdsRequest.newBuilder()
                        .addAllStaffIds(staffIds)
                        .build())
                .getStaffsList()
                .stream()
                .collect(Collectors.toMap(StaffModel::getId, Function.identity()));
    }

    public Map<Long, LodgingTypeModel> getLodgingTypeMap(long companyId) {
        return lodgingTypeStub
                .getLodgingTypeList(GetLodgingTypeListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .build())
                .getLodgingTypeListList()
                .stream()
                .collect(Collectors.toMap(LodgingTypeModel::getId, Function.identity()));
    }

    public Map<Long, LodgingUnitModel> getLodgingMap(
            Collection<PetDetailModel> petDetailList,
            Collection<BoardingSplitLodgingModel> splitLodgings,
            long companyId) {
        if (CollectionUtils.isEmpty(petDetailList) && CollectionUtils.isEmpty(splitLodgings)) {
            return Map.of();
        }
        var allLodgingUnitIds = Stream.concat(
                        petDetailList.stream().map(PetDetailModel::getLodgingId),
                        splitLodgings.stream().map(BoardingSplitLodgingModel::getLodgingId))
                .filter(k -> k > 0)
                .distinct()
                .toList();
        return lodgingUnitStub
                .getLodgingUnitList(GetLodgingUnitListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllUnitIds(allLodgingUnitIds)
                        .build())
                .getLodgingUnitListList()
                .stream()
                .collect(Collectors.toMap(LodgingUnitModel::getId, Function.identity()));
    }

    public Map<Long, CustomizedServiceView> getCustomizedServiceMap(
            Collection<PetDetailModel> petDetailList, long companyId, long businessId) {
        if (CollectionUtils.isEmpty(petDetailList)) {
            return Map.of();
        }
        var serviceIds = petDetailList.stream()
                .map(PetDetailModel::getServiceId)
                .filter(k -> k > 0)
                .distinct()
                .toList();
        var queryConditions = serviceIds.stream()
                .map(serviceId -> CustomizedServiceQueryCondition.newBuilder()
                        .setServiceId(serviceId)
                        .setBusinessId(businessId)
                        .build())
                .toList();
        return serviceManagementStub
                .batchGetCustomizedService(BatchGetCustomizedServiceRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllQueryConditionList(queryConditions)
                        .build())
                .getCustomizedServiceListList()
                .stream()
                .collect(Collectors.toMap(
                        // Key: service ID from query condition
                        serviceInfo -> serviceInfo.getQueryCondition().getServiceId(),
                        BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService,
                        (existing, replacement) -> existing));
    }

    public Map<Long, TaxRuleModel> getTaxRuleMap(Collection<CustomizedServiceView> services) {
        if (CollectionUtils.isEmpty(services)) {
            return Map.of();
        }
        var taxIds = services.stream()
                .map(CustomizedServiceView::getTaxId)
                .distinct()
                .toList();
        return taxRuleStub
                .batchGetTaxRule(
                        BatchGetTaxRuleRequest.newBuilder().addAllIds(taxIds).build())
                .getRulesList()
                .stream()
                .collect(Collectors.toMap(TaxRuleModel::getId, Function.identity()));
    }

    public Map<Long, List<BoardingSplitLodgingModel>> getBoardingSplitLodgingMap(long appointmentId) {
        return boardingSplitLodgingStub
                .listBoardingSplitLodgings(ListBoardingSplitLodgingsRequest.newBuilder()
                        .addAppointmentIds(appointmentId)
                        .build())
                .getBoardingSplitLodgingsList()
                .stream()
                .collect(Collectors.groupingBy(BoardingSplitLodgingModel::getPetDetailId));
    }

    public Map<Pair<Long, Long>, List<PricingRuleApplyLogModel>> getPricingRuleApplyLogMap(
            long companyId, long appointmentId) {
        return pricingRuleApplyStub
                .listPricingRuleApplyLog(ListPricingRuleApplyLogRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setSourceId(appointmentId)
                        .setSourceType(PricingRuleApplySourceType.SOURCE_TYPE_APPOINTMENT)
                        .build())
                .getPricingRuleApplyLogsList()
                .stream()
                .collect(Collectors.groupingBy(p -> Pair.of(p.getPetId(), p.getServiceId()), Collectors.toList()));
    }
}
