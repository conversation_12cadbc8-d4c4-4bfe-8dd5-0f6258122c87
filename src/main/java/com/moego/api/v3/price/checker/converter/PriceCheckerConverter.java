package com.moego.api.v3.price.checker.converter;

import com.moego.idl.api.price_checker.v1.GetBasicInfoRequest;
import com.moego.idl.api.price_checker.v1.GetBasicInfoResponse;
import com.moego.idl.api.price_checker.v1.GetCityRequest;
import com.moego.idl.api.price_checker.v1.GetCityResponse;
import com.moego.idl.api.price_checker.v1.GetPriceDistributionRequest;
import com.moego.idl.api.price_checker.v1.GetPriceDistributionResponse;
import com.moego.idl.api.price_checker.v1.GetStateResponse;
import com.moego.idl.api.price_checker.v1.PostPriceDistributionRequest;
import com.moego.idl.api.price_checker.v1.PostPriceDistributionResponse;
import com.moego.idl.api.price_checker.v1.UnsubscribeEmailRequest;
import com.moego.idl.api.price_checker.v1.UnsubscribeEmailResponse;
import com.moego.idl.service.price_checker.v1.GetBasicInfoInput;
import com.moego.idl.service.price_checker.v1.GetBasicInfoOutput;
import com.moego.idl.service.price_checker.v1.GetCityInput;
import com.moego.idl.service.price_checker.v1.GetCityOutput;
import com.moego.idl.service.price_checker.v1.GetPriceDistributionInput;
import com.moego.idl.service.price_checker.v1.GetPriceDistributionOutput;
import com.moego.idl.service.price_checker.v1.GetStateOutput;
import com.moego.idl.service.price_checker.v1.PostPriceDistributionInput;
import com.moego.idl.service.price_checker.v1.PostPriceDistributionOutput;
import com.moego.idl.service.price_checker.v1.UnsubscribeEmailInput;
import com.moego.idl.service.price_checker.v1.UnsubscribeEmailOutput;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface PriceCheckerConverter {

    PriceCheckerConverter INSTANCE = Mappers.getMapper(PriceCheckerConverter.class);

    GetStateResponse toResponse(GetStateOutput output);

    GetCityInput toInput(GetCityRequest request);

    GetCityResponse toResponse(GetCityOutput output);

    GetBasicInfoInput toInput(GetBasicInfoRequest request);

    GetBasicInfoResponse toResponse(GetBasicInfoOutput output);

    GetPriceDistributionInput toInput(GetPriceDistributionRequest request);

    GetPriceDistributionResponse toResponse(GetPriceDistributionOutput output);

    PostPriceDistributionInput toInput(PostPriceDistributionRequest request);

    PostPriceDistributionResponse toResponse(PostPriceDistributionOutput output);

    UnsubscribeEmailInput toInput(UnsubscribeEmailRequest request);

    UnsubscribeEmailResponse toResponse(UnsubscribeEmailOutput output);
}
