package com.moego.api.v3.price.checker.controller;

import static com.moego.lib.common.auth.AuthType.ANONYMOUS;

import com.google.protobuf.Empty;
import com.moego.api.v3.price.checker.converter.PriceCheckerConverter;
import com.moego.idl.api.price_checker.v1.GetBasicInfoRequest;
import com.moego.idl.api.price_checker.v1.GetBasicInfoResponse;
import com.moego.idl.api.price_checker.v1.GetCityRequest;
import com.moego.idl.api.price_checker.v1.GetCityResponse;
import com.moego.idl.api.price_checker.v1.GetPriceDistributionRequest;
import com.moego.idl.api.price_checker.v1.GetPriceDistributionResponse;
import com.moego.idl.api.price_checker.v1.GetStateResponse;
import com.moego.idl.api.price_checker.v1.PostPriceDistributionRequest;
import com.moego.idl.api.price_checker.v1.PostPriceDistributionResponse;
import com.moego.idl.api.price_checker.v1.PriceCheckerServiceGrpc;
import com.moego.idl.api.price_checker.v1.StartTaskRequest;
import com.moego.idl.api.price_checker.v1.UnsubscribeEmailRequest;
import com.moego.idl.api.price_checker.v1.UnsubscribeEmailResponse;
import com.moego.idl.service.price_checker.v1.GetBasicInfoInput;
import com.moego.idl.service.price_checker.v1.GetBasicInfoOutput;
import com.moego.idl.service.price_checker.v1.GetCityInput;
import com.moego.idl.service.price_checker.v1.GetCityOutput;
import com.moego.idl.service.price_checker.v1.GetPriceDistributionInput;
import com.moego.idl.service.price_checker.v1.GetPriceDistributionOutput;
import com.moego.idl.service.price_checker.v1.GetStateOutput;
import com.moego.idl.service.price_checker.v1.PostPriceDistributionInput;
import com.moego.idl.service.price_checker.v1.PostPriceDistributionOutput;
import com.moego.idl.service.price_checker.v1.PriceCheckerServiceGrpc.PriceCheckerServiceBlockingStub;
import com.moego.idl.service.price_checker.v1.StartTaskInput;
import com.moego.idl.service.price_checker.v1.UnsubscribeEmailInput;
import com.moego.idl.service.price_checker.v1.UnsubscribeEmailOutput;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
public class PriceCheckerController extends PriceCheckerServiceGrpc.PriceCheckerServiceImplBase {

    @Resource
    private PriceCheckerServiceBlockingStub priceCheckerClient;

    @Override
    @Auth(ANONYMOUS)
    public void getState(Empty request, StreamObserver<GetStateResponse> responseObserver) {

        GetStateOutput output = priceCheckerClient.getState(Empty.newBuilder().build());

        responseObserver.onNext(PriceCheckerConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void getCity(GetCityRequest request, StreamObserver<GetCityResponse> responseObserver) {
        GetCityInput input = PriceCheckerConverter.INSTANCE.toInput(request);

        GetCityOutput output = priceCheckerClient.getCity(input);

        responseObserver.onNext(PriceCheckerConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void getBasicInfo(GetBasicInfoRequest request, StreamObserver<GetBasicInfoResponse> responseObserver) {
        GetBasicInfoInput input = PriceCheckerConverter.INSTANCE.toInput(request);

        GetBasicInfoOutput output = priceCheckerClient.getBasicInfo(input);

        responseObserver.onNext(PriceCheckerConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void getPriceDistribution(
            GetPriceDistributionRequest request, StreamObserver<GetPriceDistributionResponse> responseObserver) {
        GetPriceDistributionInput input = PriceCheckerConverter.INSTANCE.toInput(request);

        GetPriceDistributionOutput output = priceCheckerClient.getPriceDistribution(input);

        responseObserver.onNext(PriceCheckerConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void postPriceDistribution(
            PostPriceDistributionRequest request, StreamObserver<PostPriceDistributionResponse> responseObserver) {
        PostPriceDistributionInput input = PriceCheckerConverter.INSTANCE.toInput(request);

        PostPriceDistributionOutput output = priceCheckerClient.postPriceDistribution(input);

        responseObserver.onNext(PriceCheckerConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void startTask(StartTaskRequest request, StreamObserver<Empty> responseObserver) {
        StartTaskInput input = StartTaskInput.newBuilder()
                .setBusinessId(request.getBusinessId())
                .setStartDate(request.getStartDate())
                .setEndDate(request.getEndDate())
                .build();

        responseObserver.onNext(priceCheckerClient.startTask(input));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void unsubscribeEmail(
            UnsubscribeEmailRequest request, StreamObserver<UnsubscribeEmailResponse> responseObserver) {
        UnsubscribeEmailInput input = PriceCheckerConverter.INSTANCE.toInput(request);
        UnsubscribeEmailOutput output = priceCheckerClient.unsubscribeEmail(input);

        responseObserver.onNext(PriceCheckerConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }
}
