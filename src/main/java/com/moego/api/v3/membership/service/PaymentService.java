package com.moego.api.v3.membership.service;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.payment.client.IPaymentStripeClient;
import com.moego.server.payment.params.CheckMoeGoPayStatusRequest;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class PaymentService {
    private final IPaymentStripeClient paymentStripeClient;

    public void checkMoegoPayStatus(Long businessId) {
        Boolean isMoegoPayEnabled = paymentStripeClient
                .checkMoeGoPayStatus(new CheckMoeGoPayStatusRequest(null, Set.of(businessId.intValue())))
                .businessMoeGoPayStatusMap()
                .getOrDefault(businessId.intValue(), false);
        if (!isMoegoPayEnabled) {
            throw ExceptionUtil.bizException(Code.CODE_MOEGO_PAY_NOT_SET_UP, "Moego pay is not set up");
        }
    }
}
