/*
 * @since 2024-06-18 09:47:09
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.membership.controller;

import static com.moego.lib.common.auth.AuthType.COMPANY;

import com.moego.api.v3.appointment.utils.PetDetailUtil;
import com.moego.api.v3.membership.converter.MembershipConverter;
import com.moego.api.v3.offering.service.PetService;
import com.moego.api.v3.shared.property.TaxProperty;
import com.moego.api.v3.subscription.service.SubscriptionService;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.order.DiscountType;
import com.moego.common.enums.order.LineApplyType;
import com.moego.common.enums.order.OrderItemType;
import com.moego.idl.api.membership.v1.ApplyMembershipParams;
import com.moego.idl.api.membership.v1.ApplyMembershipResult;
import com.moego.idl.api.membership.v1.CreateMembershipParams;
import com.moego.idl.api.membership.v1.CreateMembershipResult;
import com.moego.idl.api.membership.v1.DeleteMembershipParams;
import com.moego.idl.api.membership.v1.DeleteMembershipResult;
import com.moego.idl.api.membership.v1.GetMembershipParams;
import com.moego.idl.api.membership.v1.GetMembershipResult;
import com.moego.idl.api.membership.v1.GetPerkUsageDetailParams;
import com.moego.idl.api.membership.v1.GetPerkUsageDetailResult;
import com.moego.idl.api.membership.v1.GetRedeemHistoryParams;
import com.moego.idl.api.membership.v1.GetRedeemHistoryResult;
import com.moego.idl.api.membership.v1.ListAllPerkCycleParams;
import com.moego.idl.api.membership.v1.ListAllPerkCycleResult;
import com.moego.idl.api.membership.v1.ListMembershipsForCustomerParams;
import com.moego.idl.api.membership.v1.ListMembershipsForCustomerResult;
import com.moego.idl.api.membership.v1.ListMembershipsForSaleParams;
import com.moego.idl.api.membership.v1.ListMembershipsForSaleResult;
import com.moego.idl.api.membership.v1.ListMembershipsParams;
import com.moego.idl.api.membership.v1.ListMembershipsResult;
import com.moego.idl.api.membership.v1.ListRecommendMembershipsParams;
import com.moego.idl.api.membership.v1.ListRecommendMembershipsResult;
import com.moego.idl.api.membership.v1.MembershipServiceGrpc.MembershipServiceImplBase;
import com.moego.idl.api.membership.v1.RemoveMembershipParams;
import com.moego.idl.api.membership.v1.RemoveMembershipResult;
import com.moego.idl.api.membership.v1.TransferCreditsParams;
import com.moego.idl.api.membership.v1.TransferCreditsResult;
import com.moego.idl.api.membership.v1.UpdateMembershipParams;
import com.moego.idl.api.membership.v1.UpdateMembershipResult;
import com.moego.idl.api.subscription.v1.UpdateCreditParams;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.membership.v1.MembershipModel;
import com.moego.idl.models.membership.v1.MembershipSummaryModel;
import com.moego.idl.models.membership.v1.MembershipUsageView;
import com.moego.idl.models.membership.v1.PetFilter;
import com.moego.idl.models.membership.v1.RedeemContext;
import com.moego.idl.models.membership.v1.RedeemScenario;
import com.moego.idl.models.membership.v1.RedeemScenarioItem;
import com.moego.idl.models.membership.v1.TargetType;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderLineDiscountModel;
import com.moego.idl.models.order.v1.OrderLineItemModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.subscription.v1.SubscriptionStat;
import com.moego.idl.models.subscription.v1.UpdateCredit;
import com.moego.idl.models.subscription.v1.User;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.membership.v1.GetPerkUsageDetailRequest;
import com.moego.idl.service.membership.v1.GetPerkUsageDetailResponse;
import com.moego.idl.service.membership.v1.GetRecommendBenefitUsageRequest;
import com.moego.idl.service.membership.v1.GetRecommendBenefitUsageResponse;
import com.moego.idl.service.membership.v1.GetRedeemHistoryRequest;
import com.moego.idl.service.membership.v1.GetRedeemHistoryResponse;
import com.moego.idl.service.membership.v1.ListAllPerkCycleRequest;
import com.moego.idl.service.membership.v1.ListAllPerkCycleResponse;
import com.moego.idl.service.membership.v1.ListMembershipsForSaleRequest;
import com.moego.idl.service.membership.v1.ListMembershipsForSaleResponse;
import com.moego.idl.service.membership.v1.ListRecommendMembershipsRequest;
import com.moego.idl.service.membership.v1.ListRecommendMembershipsResponse;
import com.moego.idl.service.membership.v1.MembershipServiceGrpc;
import com.moego.idl.service.membership.v1.MembershipServiceGrpc.MembershipServiceBlockingStub;
import com.moego.idl.service.membership.v1.TransferCreditsRequest;
import com.moego.idl.service.membership.v1.UpsertRecommendBenefitUsageRequest;
import com.moego.idl.service.order.v1.GetOrderRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.order.v1.UpdateOrderIncrRequest;
import com.moego.idl.service.organization.v1.TaxRuleServiceGrpc.TaxRuleServiceBlockingStub;
import com.moego.idl.service.subscription.v1.SubscriptionServiceGrpc.SubscriptionServiceBlockingStub;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import com.moego.server.grooming.api.IInvoiceApplyPackageService;
import io.grpc.stub.StreamObserver;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class MembershipController extends MembershipServiceImplBase {

    private final MembershipConverter membershipConverter;
    private final MembershipServiceBlockingStub membershipServiceBlockingStub;
    private final SubscriptionServiceBlockingStub subscriptionServiceBlockingStub;
    private final TaxRuleServiceBlockingStub taxRuleServiceBlockingStub;
    private final TaxProperty taxProperty;
    private final PetService petService;
    private final MembershipServiceGrpc.MembershipServiceBlockingStub membershipService;
    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub businessCustomerPetService;
    private final OrderServiceGrpc.OrderServiceBlockingStub orderClient;
    private final IInvoiceApplyPackageService invoiceApplyPackageApi;
    private final PetDetailUtil petDetailUtil;

    @Autowired
    private SubscriptionService subscriptionService;

    @Override
    @Auth(COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MEMBERSHIP, PermissionEnums.MANAGE_MEMBERSHIP})
    public void createMembership(
            CreateMembershipParams request, StreamObserver<CreateMembershipResult> responseObserver) {
        taxProperty.check(request.getMembershipDef().getTaxId());
        final var input = membershipConverter.createMembershipRequest(request);
        final var output = membershipServiceBlockingStub.createMembership(input);
        final var response = membershipConverter.createMembershipResult(output);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MEMBERSHIP})
    public void getMembership(GetMembershipParams request, StreamObserver<GetMembershipResult> responseObserver) {
        final var input = membershipConverter.getMembershipRequest(request);
        final var output = membershipServiceBlockingStub.getMembership(input);
        final var taxInput = membershipConverter.getTaxRuleRequest(output);
        final var taxOutput = taxRuleServiceBlockingStub.getTaxRule(taxInput);
        final var response = membershipConverter.getMembershipResult(output, taxOutput);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MEMBERSHIP})
    public void listMemberships(ListMembershipsParams request, StreamObserver<ListMembershipsResult> responseObserver) {
        final var input = membershipConverter.listMembershipsRequest(request);
        final var output = membershipServiceBlockingStub.listMemberships(input);
        final var productionIdToMembershipId = output.getMembershipsList().stream()
                .collect(Collectors.toMap(MembershipModel::getInternalProductId, MembershipModel::getId));
        final var statsInput = membershipConverter.statSubscriptionRequest(
                null, productionIdToMembershipId.keySet().stream().toList());
        final var stats = subscriptionServiceBlockingStub.statSubscription(statsInput);
        final var summaries = stats.getStatsList().stream()
                .collect(Collectors.groupingBy(SubscriptionStat::getProductId))
                .entrySet()
                .stream()
                .map(s -> {
                    if (!productionIdToMembershipId.containsKey(s.getKey())) {
                        return MembershipSummaryModel.getDefaultInstance();
                    }
                    final var builder =
                            MembershipSummaryModel.newBuilder().setId(productionIdToMembershipId.get(s.getKey()));
                    s.getValue().forEach(v -> {
                        switch (v.getStatus()) {
                            case ACTIVE, GRACE -> builder.setInSubscriptionCount(
                                    builder.getInSubscriptionCount() + v.getCount());
                            case CANCELLED -> builder.setCancelledCount(builder.getCancelledCount() + v.getCount());
                            case PAUSED -> builder.setPausedCount(builder.getPausedCount() + v.getCount());
                            default -> {}
                        }
                    });
                    return builder.build();
                })
                .toList();
        final var response = membershipConverter.listMembershipsResult(output, summaries);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MEMBERSHIP, PermissionEnums.MANAGE_MEMBERSHIP})
    public void updateMembership(
            UpdateMembershipParams request, StreamObserver<UpdateMembershipResult> responseObserver) {
        if (request.getMembershipDef().hasTaxId()) {
            taxProperty.check(request.getMembershipDef().getTaxId());
        }
        final var input = membershipConverter.updateMembershipRequest(request);
        final var output = membershipServiceBlockingStub.updateMembership(input);
        final var response = membershipConverter.updateMembershipResult(output);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void listRecommendedMemberships(
            ListRecommendMembershipsParams request, StreamObserver<ListRecommendMembershipsResult> responseObserver) {
        var input = membershipConverter.getListRecommendedMembershipsRequest(request);
        ListRecommendMembershipsRequest.Builder builder = input.toBuilder();
        if (request.hasFilter()) {
            ListRecommendMembershipsRequest.Filter.Builder filter = ListRecommendMembershipsRequest.Filter.newBuilder();
            if (!request.getFilter().getTargetMembershipIdsList().isEmpty()) {
                filter.addAllTargetMembershipIds(request.getFilter().getTargetMembershipIdsList());
            }
            builder.setFilter(filter);
        }
        builder.setBusinessId(AuthContext.get().getBusinessId())
                .setCompanyId(AuthContext.get().getCompanyId());
        final var output = membershipServiceBlockingStub.listRecommendedMemberships(builder.build());
        final var response = membershipConverter.getListRecommendedMembershipsResult(output);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MEMBERSHIP})
    public void listMembershipsForCustomer(
            ListMembershipsForCustomerParams request,
            StreamObserver<ListMembershipsForCustomerResult> responseObserver) {
        var input = membershipConverter.getListMembershipsForCustomerRequest(request);
        input = input.toBuilder()
                .setCompanyId(AuthContext.get().getCompanyId())
                .setBusinessId(AuthContext.get().getBusinessId())
                .build();
        final var output = membershipServiceBlockingStub.listMembershipsForCustomer(input);
        final var response = membershipConverter.getListMembershipsForCustomerResult(output);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void getRedeemHistory(
            GetRedeemHistoryParams request, StreamObserver<GetRedeemHistoryResult> responseObserver) {
        final GetRedeemHistoryRequest input = GetRedeemHistoryRequest.newBuilder()
                .setCompanyId(AuthContext.get().getCompanyId())
                .setBusinessId(AuthContext.get().getBusinessId())
                .setMembershipId(request.getMembershipId())
                .setCustomerId(request.getCustomerId())
                .setPagination(request.getPagination())
                .build();
        final GetRedeemHistoryResponse output = membershipServiceBlockingStub.getRedeemHistory(input);
        final GetRedeemHistoryResult response = GetRedeemHistoryResult.newBuilder()
                .addAllRedeemHistory(output.getRedeemHistoryList())
                .addAllIncludedBenefits(output.getIncludedBenefitsList())
                .setPagination(output.getPagination())
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void deleteMembership(
            DeleteMembershipParams request, StreamObserver<DeleteMembershipResult> responseObserver) {
        throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "deleteMembership not implemented");
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void applyMembership(ApplyMembershipParams request, StreamObserver<ApplyMembershipResult> responseObserver) {
        long orderId = request.getOrderId();
        List<Long> needAppliedMembershipIds = request.getMembershipIdsList();
        Long businessId = AuthContext.get().businessId();
        Long companyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();

        // auto apply
        if (CollectionUtils.isEmpty(needAppliedMembershipIds)) {
            applyMemberships(businessId, companyId, staffId, orderId, List.of());
            responseObserver.onNext(ApplyMembershipResult.newBuilder().build());
            responseObserver.onCompleted();
            return;
        }

        // get used memberships
        GetRecommendBenefitUsageResponse membershipsUsageResponse = membershipService.getRecommendBenefitUsage(
                GetRecommendBenefitUsageRequest.newBuilder().setOrderId(orderId).build());
        Set<Long> allMembershipIds = membershipsUsageResponse.getUsageViewsList().stream()
                .map(MembershipUsageView::getMembershipId)
                .collect(Collectors.toSet());
        allMembershipIds.addAll(needAppliedMembershipIds);

        boolean useMembership = applyMemberships(businessId, companyId, staffId, orderId, needAppliedMembershipIds);
        if (!useMembership) {
            responseObserver.onNext(ApplyMembershipResult.newBuilder()
                    .setMessage("The membership is not available for this appointment.")
                    .build());
            responseObserver.onCompleted();
            return;
        }
        responseObserver.onNext(ApplyMembershipResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void removeMembership(
            RemoveMembershipParams request, StreamObserver<RemoveMembershipResult> responseObserver) {
        long orderId = request.getOrderId();
        List<Long> needRemovedMembershipIds = request.getMembershipIdsList();
        Long businessId = AuthContext.get().businessId();
        Long companyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();

        // get used memberships
        GetRecommendBenefitUsageResponse membershipsUsageResponse = membershipService.getRecommendBenefitUsage(
                GetRecommendBenefitUsageRequest.newBuilder().setOrderId(orderId).build());
        Set<Long> needAppliedMembershipIds = membershipsUsageResponse.getUsageViewsList().stream()
                .map(MembershipUsageView::getMembershipId)
                .collect(Collectors.toSet());
        needRemovedMembershipIds.forEach(needAppliedMembershipIds::remove);
        if (CollectionUtils.isEmpty(needAppliedMembershipIds)) {
            removeMemberships(businessId, staffId, orderId);
            responseObserver.onNext(RemoveMembershipResult.newBuilder().build());
            responseObserver.onCompleted();
            return;
        }

        applyMemberships(businessId, companyId, staffId, orderId, needAppliedMembershipIds);
        responseObserver.onNext(RemoveMembershipResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listAllPerkCycle(
            ListAllPerkCycleParams request, StreamObserver<ListAllPerkCycleResult> responseObserver) {
        Integer tokenCompanyId = AuthContext.get().getCompanyId();
        if (tokenCompanyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id is null");
        }
        ListAllPerkCycleRequest.Builder builder = ListAllPerkCycleRequest.newBuilder();
        builder.setCompanyId(tokenCompanyId)
                .setMembershipId(request.getMembershipId())
                .setCustomerId(request.getCustomerId());
        ListAllPerkCycleResponse listAllPerkCycleResponse = membershipService.listAllPerkCycle(builder.build());
        responseObserver.onNext(ListAllPerkCycleResult.newBuilder()
                .addAllPerkCycleItem(listAllPerkCycleResponse.getPerkCycleItemList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getPerkUsageDetail(
            GetPerkUsageDetailParams request, StreamObserver<GetPerkUsageDetailResult> responseObserver) {
        Integer tokenCompanyId = AuthContext.get().getCompanyId();
        if (tokenCompanyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id is null");
        }
        GetPerkUsageDetailRequest.Builder builder = GetPerkUsageDetailRequest.newBuilder();
        builder.setCompanyId(tokenCompanyId)
                .setCustomerId(request.getCustomerId())
                .setMembershipId(request.getMembershipId());
        if (request.hasFilter()) {
            GetPerkUsageDetailRequest.Filter.Builder filterBuilder = GetPerkUsageDetailRequest.Filter.newBuilder();
            if (request.getFilter().hasValidityStartTime()) {
                filterBuilder.setValidityStartTime(request.getFilter().getValidityStartTime());
            }
            builder.setFilter(filterBuilder.build());
        }
        GetPerkUsageDetailResponse perkUsageDetail = membershipService.getPerkUsageDetail(builder.build());
        responseObserver.onNext(GetPerkUsageDetailResult.newBuilder()
                .addAllIncludedBenefits(perkUsageDetail.getIncludedBenefitsList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void transferCredits(TransferCreditsParams request, StreamObserver<TransferCreditsResult> responseObserver) {
        Integer tokenCompanyId = AuthContext.get().getCompanyId();
        if (tokenCompanyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id is null");
        }
        TransferCreditsRequest.Builder builder = TransferCreditsRequest.newBuilder();
        builder.setCompanyId(tokenCompanyId)
                .setCustomerId(request.getCustomerId())
                .setQuantityId(request.getQuantityId())
                .setValidityStartTime(request.getValidityStartTime())
                .setTransferCreditNum(request.getTransferCreditNum())
                .setTransferQuantityNum(request.getTransferQuantityNum());
        membershipService.transferCredits(builder.build());

        // 构建UpdateCreditParams
        UpdateCreditParams updateCreditParams = UpdateCreditParams.newBuilder()
                .setUser(User.newBuilder()
                        .setId(request.getCustomerId())
                        .setType(User.Type.CUSTOMER)
                        .build())
                .setCredit(request.getTransferCreditNum())
                .setType(UpdateCredit.Type.TYPE_MANUAL)
                .setReason(UpdateCredit.Reason.TRANSFER_FROM_PERKS)
                .setNote("from membership")
                .build();

        // 调用subscriptionService.updateCredit
        subscriptionService.updateCredit(updateCreditParams);

        responseObserver.onNext(TransferCreditsResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listMembershipsForSale(
            ListMembershipsForSaleParams request, StreamObserver<ListMembershipsForSaleResult> responseObserver) {
        Integer tokenCompanyId = AuthContext.get().getCompanyId();
        if (tokenCompanyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id is null");
        }

        ListMembershipsForSaleRequest.Builder builder = ListMembershipsForSaleRequest.newBuilder();
        ListMembershipsForSaleRequest.Filter.Builder filterBuilder = ListMembershipsForSaleRequest.Filter.newBuilder();
        filterBuilder.setCompanyId(tokenCompanyId);

        if (request.hasFilter()) {
            var filter = request.getFilter();
            if (filter.hasNameLike()) {
                filterBuilder.setNameLike(filter.getNameLike());
            }

            if (filter.hasStatus()) {
                filterBuilder.addStatusIn(filter.getStatus());
            }

            // pet filter 处理
            if (!filter.getPetIdsList().isEmpty()) {
                var petIds = filter.getPetIdsList();
                var petFilterMap = petService.batchGetPetFilter(tokenCompanyId.longValue(), petIds);

                List<PetFilter> petFilters = petIds.stream()
                        .map(petFilterMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                filterBuilder.addAllPetFilters(petFilters);
            }
        }

        builder.setFilter(filterBuilder);

        ListMembershipsForSaleResponse response = membershipService.listMembershipsForSale(builder.build());

        responseObserver.onNext(ListMembershipsForSaleResult.newBuilder()
                .addAllMemberships(response.getMembershipsList())
                .setPagination(response.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    private boolean applyMemberships(
            Long businessId, Long companyId, Long staffId, long orderId, Collection<Long> needAppliedMembershipIds) {
        OrderDetailModel orderDetail = getOrderDetailByOrderId(businessId, orderId);
        long customerId = orderDetail.getOrder().getCustomerId();

        // membership info for apply benefits
        ListRecommendMembershipsResponse membershipsResponse = getListRecommendMembershipsResponse(
                businessId, companyId, needAppliedMembershipIds, orderDetail, customerId);
        log.info("apply memberships: {}", membershipsResponse);
        List<MembershipUsageView> usageViews = membershipsResponse.getUsageViewsList();
        if (CollectionUtils.isEmpty(usageViews)) {
            // clear applied records
            membershipService.upsertRecommendBenefitUsage(UpsertRecommendBenefitUsageRequest.newBuilder()
                    .setOrderId(orderId)
                    .setCustomerId(orderDetail.getOrder().getCustomerId())
                    .build());
            return false;
        }

        // remove discounts
        List<OrderLineDiscountModel> deleteAllUsedDiscounts = orderDetail.getLineDiscountsList().stream()
                .map(discount -> {
                    OrderLineDiscountModel.Builder lineDiscountBuilder =
                            discount.toBuilder().setIsDeleted(true);
                    return lineDiscountBuilder.build();
                })
                .toList();
        // remove packages
        invoiceApplyPackageApi.removeAllPackages(IInvoiceApplyPackageService.RemoveAllPackagesParam.builder()
                .orderId(orderId)
                .checkRefund(true)
                .build());

        // apply discounts
        Map<Long, OrderLineDiscountModel> discountMap =
                usageViews.stream()
                        .collect(Collectors.groupingBy(MembershipUsageView::getOrderItemId))
                        .entrySet()
                        .stream()
                        .map(entry -> convertToLineDiscountModel(
                                entry.getKey(), entry.getValue(), orderId, businessId, staffId))
                        .filter(Objects::nonNull)
                        .collect(Collectors.toMap(OrderLineDiscountModel::getOrderItemId, Function.identity()));

        if (CollectionUtils.isEmpty(discountMap)) {
            return false;
        }

        // update order
        UpdateOrderIncrRequest.Builder requestBuilder = UpdateOrderIncrRequest.newBuilder()
                .setOrderId(orderId)
                .setOrder(OrderModel.newBuilder()
                        .setId(orderId)
                        .setUpdateBy(staffId)
                        .setBusinessId(businessId)
                        .build())
                // .addAllLineItems(newItems)
                .addAllLineDiscounts(deleteAllUsedDiscounts)
                .addAllLineDiscounts(discountMap.values());
        orderClient.updateOrderIncremental(requestBuilder.build());

        // upsert applied records
        membershipService.upsertRecommendBenefitUsage(UpsertRecommendBenefitUsageRequest.newBuilder()
                .setOrderId(orderId)
                .setCustomerId(customerId)
                .addAllAllMemberships(membershipsResponse.getAllList())
                .addAllBenefitCombination(membershipsResponse.getBenefitCombinationList())
                .addAllUsageViews(usageViews)
                .build());
        return true;
    }

    private void removeMemberships(Long businessId, Long staffId, long orderId) {
        GetOrderRequest.Builder request =
                GetOrderRequest.newBuilder().setBusinessId(businessId).setId(orderId);
        OrderDetailModel orderDetail = orderClient.getOrderDetail(request.build());
        if (orderDetail == null) {
            throw ExceptionUtil.bizException(Code.CODE_INVOICE_NOT_FOUND);
        }

        // clear applied records
        membershipService.upsertRecommendBenefitUsage(UpsertRecommendBenefitUsageRequest.newBuilder()
                .setOrderId(orderId)
                .setCustomerId(orderDetail.getOrder().getCustomerId())
                .build());

        if (CollectionUtils.isEmpty(orderDetail.getLineDiscountsList())) {
            return;
        }

        // remove discounts
        List<OrderLineDiscountModel> deleteAllUsedDiscounts = orderDetail.getLineDiscountsList().stream()
                // .filter(discount -> LineApplyType.TYPE_ALL.getType().equals(discount.getApplyType()))
                .map(discount -> {
                    OrderLineDiscountModel.Builder lineDiscountBuilder =
                            discount.toBuilder().setIsDeleted(true);
                    return lineDiscountBuilder.build();
                })
                .toList();

        // update order
        UpdateOrderIncrRequest.Builder requestBuilder = UpdateOrderIncrRequest.newBuilder()
                .setOrderId(orderId)
                .setOrder(OrderModel.newBuilder()
                        .setId(orderId)
                        .setUpdateBy(staffId)
                        .setBusinessId(businessId)
                        .build())
                // .addAllLineItems(newItems)
                .addAllLineDiscounts(deleteAllUsedDiscounts);
        orderClient.updateOrderIncremental(requestBuilder.build());
    }

    private ListRecommendMembershipsResponse getListRecommendMembershipsResponse(
            Long businessId,
            Long companyId,
            Collection<Long> needAppliedMembershipIds,
            OrderDetailModel orderDetail,
            long customerId) {
        List<Long> serviceIds = orderDetail.getLineItemsList().stream()
                .filter(item -> Objects.equals(OrderItemType.ITEM_TYPE_SERVICE.getType(), item.getType()))
                .map(OrderLineItemModel::getObjectId)
                .distinct()
                .toList();
        Map<Long, ServiceBriefView> serviceMap = petDetailUtil.getServiceMap(companyId, serviceIds);

        List<RedeemScenarioItem> list = orderDetail.getLineItemsList().stream()
                .map(item -> {
                    RedeemScenarioItem.Builder builder = RedeemScenarioItem.newBuilder()
                            .setTargetId(item.getObjectId())
                            .setTargetType(getTargetType(item.getType(), item.getObjectId(), serviceMap))
                            .setAmount(item.getQuantity())
                            .setPrice(item.getUnitPrice())
                            .setOrderItemId(item.getId());
                    return builder.build();
                })
                .filter(item -> !Objects.equals(item.getTargetType(), TargetType.TARGET_TYPE_UNSPECIFIED))
                .toList();

        RedeemContext.Builder builder = RedeemContext.newBuilder()
                .setScenario(RedeemScenario.REDEEM_BY_CHECKOUT)
                .addAllItems(list);

        // membership info for apply benefits
        // get membership
        ListRecommendMembershipsRequest.Builder requestBuilder = ListRecommendMembershipsRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setCustomerId(customerId)
                .setContext(builder.build());
        if (!CollectionUtils.isEmpty(needAppliedMembershipIds)) {
            requestBuilder.setFilter(ListRecommendMembershipsRequest.Filter.newBuilder()
                    .addAllTargetMembershipIds(needAppliedMembershipIds)
                    .build());
        }
        return membershipService.listRecommendedMemberships(requestBuilder.build());
    }

    private OrderLineDiscountModel convertToLineDiscountModel(
            Long orderItemId, List<MembershipUsageView> values, Long orderId, Long businessId, Long staffId) {
        if (CollectionUtils.isEmpty(values)) {
            return null;
        }

        double discountAmount = values.stream()
                .map(MembershipUsageView::getPriceReduction)
                .reduce(Double::sum)
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "price reduction not found"));

        OrderLineDiscountModel.Builder discountBuilder = OrderLineDiscountModel.newBuilder();
        discountBuilder.setDiscountType(DiscountType.AMOUNT.getType());
        discountBuilder.setDiscountAmount(discountAmount);
        discountBuilder.setOrderId(orderId);
        discountBuilder.setOrderItemId(orderItemId);
        discountBuilder.setApplyBy(staffId);
        discountBuilder.setBusinessId(businessId);
        discountBuilder.setApplyType(LineApplyType.TYPE_ITEM.getType());
        discountBuilder.setIsDeleted(false);
        return discountBuilder.build();
    }

    private OrderDetailModel getOrderDetailByOrderId(Long businessId, Long orderId) {
        if (orderId == null) {
            return null;
        }

        var builder = GetOrderRequest.newBuilder().setId(orderId).setSourceType(InvoiceStatusEnum.TYPE_APPOINTMENT);
        if (businessId != null) {
            builder.setBusinessId(businessId);
        }
        OrderDetailModel orderDetail = orderClient.getOrderDetail(builder.build());
        if (!orderDetail.hasOrder()) {
            throw ExceptionUtil.bizException(Code.CODE_INVOICE_NOT_FOUND);
        }
        return orderDetail;
    }

    private TargetType getTargetType(String type, Long objectId, Map<Long, ServiceBriefView> serviceMap) {
        if (Objects.equals(OrderItemType.ITEM_TYPE_SERVICE.getType(), type)) {
            if (!serviceMap.containsKey(objectId)) {
                return TargetType.TARGET_TYPE_UNSPECIFIED;
            }
            ServiceBriefView service = serviceMap.get(objectId);
            if (Objects.equals(ServiceType.SERVICE, service.getType())) {
                return TargetType.SERVICE;
            } else if (Objects.equals(ServiceType.ADDON, service.getType())) {
                return TargetType.ADDON;
            }
        } else if (Objects.equals(OrderItemType.ITEM_TYPE_PRODUCT.getType(), type)) {
            return TargetType.PRODUCT;
        }
        return TargetType.TARGET_TYPE_UNSPECIFIED;
    }
}
