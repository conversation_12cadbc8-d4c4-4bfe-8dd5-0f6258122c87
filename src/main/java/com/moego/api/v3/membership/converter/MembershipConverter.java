/*
 * @since 2024-06-18 09:50:09
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.membership.converter;

import static org.mapstruct.ReportingPolicy.WARN;

import com.moego.api.v3.shared.converter.PaginationConverter;
import com.moego.api.v3.shared.util.OperationUtil;
import com.moego.idl.api.membership.v1.CancelSubscriptionParams;
import com.moego.idl.api.membership.v1.CancelSubscriptionResult;
import com.moego.idl.api.membership.v1.CreateCardParams;
import com.moego.idl.api.membership.v1.CreateCardResult;
import com.moego.idl.api.membership.v1.CreateMembershipParams;
import com.moego.idl.api.membership.v1.CreateMembershipResult;
import com.moego.idl.api.membership.v1.CreateSellLinkParams;
import com.moego.idl.api.membership.v1.CreateSellLinkResult;
import com.moego.idl.api.membership.v1.GetBuyerReportParams;
import com.moego.idl.api.membership.v1.GetBuyerReportResult;
import com.moego.idl.api.membership.v1.GetMembershipParams;
import com.moego.idl.api.membership.v1.GetMembershipResult;
import com.moego.idl.api.membership.v1.GetSubscriptionDetailParams;
import com.moego.idl.api.membership.v1.GetSubscriptionDetailResult;
import com.moego.idl.api.membership.v1.ListActivityLogsParams;
import com.moego.idl.api.membership.v1.ListActivityLogsResult;
import com.moego.idl.api.membership.v1.ListBuyersParams;
import com.moego.idl.api.membership.v1.ListBuyersResult;
import com.moego.idl.api.membership.v1.ListMembershipsForCustomerParams;
import com.moego.idl.api.membership.v1.ListMembershipsForCustomerResult;
import com.moego.idl.api.membership.v1.ListMembershipsParams;
import com.moego.idl.api.membership.v1.ListMembershipsResult;
import com.moego.idl.api.membership.v1.ListPaymentHistoryHistoryResult;
import com.moego.idl.api.membership.v1.ListPaymentHistoryParams;
import com.moego.idl.api.membership.v1.ListRecommendMembershipsParams;
import com.moego.idl.api.membership.v1.ListRecommendMembershipsResult;
import com.moego.idl.api.membership.v1.ListSubscriptionsParams;
import com.moego.idl.api.membership.v1.ListSubscriptionsResult;
import com.moego.idl.api.membership.v1.PauseSubscriptionParams;
import com.moego.idl.api.membership.v1.PauseSubscriptionResult;
import com.moego.idl.api.membership.v1.RenewSubscriptionParams;
import com.moego.idl.api.membership.v1.RenewSubscriptionResult;
import com.moego.idl.api.membership.v1.ResumeSubscriptionParams;
import com.moego.idl.api.membership.v1.ResumeSubscriptionResult;
import com.moego.idl.api.membership.v1.RetrySubscriptionPaymentParams;
import com.moego.idl.api.membership.v1.RetrySubscriptionPaymentResult;
import com.moego.idl.api.membership.v1.SellMembershipParams;
import com.moego.idl.api.membership.v1.SellMembershipResult;
import com.moego.idl.api.membership.v1.UpdateMembershipParams;
import com.moego.idl.api.membership.v1.UpdateMembershipResult;
import com.moego.idl.api.membership.v1.UpdateSubscriptionParams;
import com.moego.idl.api.membership.v1.UpdateSubscriptionResult;
import com.moego.idl.models.business_customer.v1.BusinessCustomerModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerModelPublicView;
import com.moego.idl.models.membership.v1.MembershipCreateDef;
import com.moego.idl.models.membership.v1.MembershipModel;
import com.moego.idl.models.membership.v1.MembershipModelPublicView;
import com.moego.idl.models.membership.v1.MembershipSummaryModel;
import com.moego.idl.models.membership.v1.SubscriptionModel;
import com.moego.idl.models.membership.v1.SubscriptionModelPublicView;
import com.moego.idl.models.organization.v1.LocationModel;
import com.moego.idl.models.organization.v1.LocationModelPublicView;
import com.moego.idl.models.organization.v1.TaxRuleModel;
import com.moego.idl.models.organization.v1.TaxRuleModelPublicView;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.models.payment.v1.CreditCardModelPublicView;
import com.moego.idl.service.business_customer.v1.GetCustomerRequest;
import com.moego.idl.service.membership.v1.CancelSubscriptionRequest;
import com.moego.idl.service.membership.v1.CancelSubscriptionResponse;
import com.moego.idl.service.membership.v1.CreateMembershipRequest;
import com.moego.idl.service.membership.v1.CreateMembershipResponse;
import com.moego.idl.service.membership.v1.CreateSellLinkRequest;
import com.moego.idl.service.membership.v1.CreateSellLinkResponse;
import com.moego.idl.service.membership.v1.CreateSubscriptionRequest;
import com.moego.idl.service.membership.v1.CreateSubscriptionResponse;
import com.moego.idl.service.membership.v1.GetBuyerReportRequest;
import com.moego.idl.service.membership.v1.GetBuyerReportResponse;
import com.moego.idl.service.membership.v1.GetMembershipRequest;
import com.moego.idl.service.membership.v1.GetMembershipResponse;
import com.moego.idl.service.membership.v1.GetSellLinkRequest;
import com.moego.idl.service.membership.v1.GetSellLinkResponse;
import com.moego.idl.service.membership.v1.GetSubscriptionRequest;
import com.moego.idl.service.membership.v1.GetSubscriptionResponse;
import com.moego.idl.service.membership.v1.ListActivityLogsRequest;
import com.moego.idl.service.membership.v1.ListActivityLogsResponse;
import com.moego.idl.service.membership.v1.ListBuyersRequest;
import com.moego.idl.service.membership.v1.ListBuyersResponse;
import com.moego.idl.service.membership.v1.ListMembershipsForCustomerRequest;
import com.moego.idl.service.membership.v1.ListMembershipsForCustomerResponse;
import com.moego.idl.service.membership.v1.ListMembershipsRequest;
import com.moego.idl.service.membership.v1.ListMembershipsResponse;
import com.moego.idl.service.membership.v1.ListPaymentHistoryRequest;
import com.moego.idl.service.membership.v1.ListPaymentHistoryResponse;
import com.moego.idl.service.membership.v1.ListRecommendMembershipsRequest;
import com.moego.idl.service.membership.v1.ListRecommendMembershipsResponse;
import com.moego.idl.service.membership.v1.ListSubscriptionsRequest;
import com.moego.idl.service.membership.v1.ListSubscriptionsResponse;
import com.moego.idl.service.membership.v1.PauseSubscriptionRequest;
import com.moego.idl.service.membership.v1.PauseSubscriptionResponse;
import com.moego.idl.service.membership.v1.RenewSubscriptionRequest;
import com.moego.idl.service.membership.v1.RenewSubscriptionResponse;
import com.moego.idl.service.membership.v1.ResumeSubscriptionRequest;
import com.moego.idl.service.membership.v1.ResumeSubscriptionResponse;
import com.moego.idl.service.membership.v1.RetrySubscriptionPaymentRequest;
import com.moego.idl.service.membership.v1.RetrySubscriptionPaymentResponse;
import com.moego.idl.service.membership.v1.UpdateMembershipRequest;
import com.moego.idl.service.membership.v1.UpdateMembershipResponse;
import com.moego.idl.service.membership.v1.UpdateSubscriptionRequest;
import com.moego.idl.service.membership.v1.UpdateSubscriptionResponse;
import com.moego.idl.service.organization.v1.GetCompanyPreferenceSettingRequest;
import com.moego.idl.service.organization.v1.GetLocationDetailRequest;
import com.moego.idl.service.organization.v1.GetTaxRuleRequest;
import com.moego.idl.service.organization.v1.GetTaxRuleResponse;
import com.moego.idl.service.subscription.v1.StatSubscriptionRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.server.payment.dto.CardDTO;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.springframework.beans.factory.annotation.Autowired;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE,
        unmappedTargetPolicy = WARN,
        imports = {AuthContext.class, OperationUtil.class, SubscriptionModel.class, PaginationConverter.class})
public abstract class MembershipConverter {
    @Autowired
    protected PaginationConverter paginationConverter;

    public abstract Tenant tenant(Long companyId, Long businessId);

    public Tenant tenant() {
        return tenant(AuthContext.get().companyId(), AuthContext.get().businessId());
    }

    public long companyId() {
        return AuthContext.get().companyId();
    }

    public long businessId() {
        return AuthContext.get().businessId();
    }

    @Mapping(target = "companyId", expression = "java(companyId())")
    @Mapping(target = "businessId", expression = "java(businessId())")
    @Mapping(target = "operation", expression = "java(OperationUtil.staffOperation())")
    public abstract CreateMembershipRequest createMembershipRequest(CreateMembershipParams params);

    public abstract CreateMembershipResult createMembershipResult(CreateMembershipResponse response);

    @Mapping(target = "companyId", expression = "java(companyId())")
    public abstract GetMembershipRequest getMembershipRequest(GetMembershipParams params);

    @Mapping(target = "tax", source = "taxOutput.rule")
    public abstract GetMembershipResult getMembershipResult(
            GetMembershipResponse membership, GetTaxRuleResponse taxOutput);

    @Mapping(target = "companyId", expression = "java(companyId())")
    @Mapping(target = "statusIn", source = "status")
    @Mapping(target = "pagination", defaultExpression = "java(paginationConverter.request())")
    public abstract ListMembershipsRequest listMembershipsRequest(ListMembershipsParams params);

    public abstract ListMembershipsResult listMembershipsResult(
            ListMembershipsResponse response, List<MembershipSummaryModel> membershipSummaries);

    public abstract StatSubscriptionRequest statSubscriptionRequest(Integer dummy, List<Long> productIds);

    @Mapping(target = "companyId", expression = "java(companyId())")
    @Mapping(target = "businessId", expression = "java(businessId())")
    @Mapping(target = "operation", expression = "java(OperationUtil.staffOperation())")
    public abstract UpdateMembershipRequest updateMembershipRequest(UpdateMembershipParams params);

    public abstract UpdateMembershipResult updateMembershipResult(UpdateMembershipResponse response);

    @Mapping(target = "companyId", expression = "java(companyId())")
    @Mapping(target = "operation", expression = "java(OperationUtil.staffOperation())")
    public abstract CreateSellLinkRequest createSellLinkRequest(CreateSellLinkParams params);

    public abstract CreateSellLinkResult createSellLinkResult(CreateSellLinkResponse response);

    @Mapping(target = "companyId", source = "sellLink.companyId")
    public abstract GetCompanyPreferenceSettingRequest getCompanyPreferenceSettingRequest(
            GetSellLinkResponse sellLinkOutput);

    @Mapping(target = "id", source = "sellLink.businessId")
    public abstract GetLocationDetailRequest getLocationDetailRequest(GetSellLinkResponse sellLinkOutput);

    @Mapping(target = "id", source = "sellLink.membershipId")
    public abstract GetMembershipRequest getMembershipRequest(GetSellLinkResponse sellLinkOutput);

    @Mapping(target = "tenant", ignore = true)
    @Mapping(target = "id", source = "sellLink.customerId")
    public abstract GetCustomerRequest getCustomerRequest(GetSellLinkResponse sellLinkOutput);

    public abstract Tenant tenant(Long companyId);

    @Mapping(target = "id", source = "membership.taxId")
    @Mapping(target = "tenant", source = "membership.companyId")
    public abstract GetTaxRuleRequest getTaxRuleRequest(GetMembershipResponse output);

    @Mapping(target = "customerMembership.customerId", source = "response.sellLink.customerId")
    @Mapping(target = "customerMembership.membershipId", source = "response.sellLink.membershipId")
    @Mapping(target = "companyId", source = "response.sellLink.companyId")
    @Mapping(target = "silent", constant = "true")
    public abstract GetSubscriptionRequest getSubscriptionRequest(
            GetSellLinkResponse response, List<SubscriptionModel.Status> statusIn);

    public GetSubscriptionRequest getNonCancelledSubscriptionRequest(GetSellLinkResponse sellLinkOutput) {
        return getSubscriptionRequest(
                sellLinkOutput, List.of(SubscriptionModel.Status.PENDING, SubscriptionModel.Status.ACTIVE));
    }

    public abstract BusinessCustomerModelPublicView customerPublicView(BusinessCustomerModel customer);

    public abstract TaxRuleModelPublicView taxPublicView(TaxRuleModel tax);

    public abstract CreditCardModelPublicView creditCardPublicView(CardDTO card);

    public abstract SubscriptionModelPublicView subscriptionPublicView(SubscriptionModel subscription);

    public abstract MembershipModelPublicView membershipPublicView(MembershipModel membership);

    public abstract LocationModelPublicView businessPublicView(LocationModel business);

    @Mapping(target = "operation", expression = "java(OperationUtil.staffOperation())")
    @Mapping(target = "companyId", expression = "java(companyId())")
    public abstract CreateSubscriptionRequest createSubscriptionRequest(SellMembershipParams request);

    public abstract UpdateSubscriptionRequest updateSubscriptionRequest(UpdateSubscriptionParams request);

    public abstract UpdateSubscriptionResult updateSubscriptionResult(UpdateSubscriptionResponse response);

    public abstract SellMembershipResult sellMembershipResult(CreateSubscriptionResponse createOutput);

    @Mapping(target = "companyId", expression = "java(companyId())")
    public abstract GetSubscriptionRequest getSubscriptionRequest(GetSubscriptionDetailParams request);

    @Mapping(target = "companyId", expression = "java(companyId())")
    public abstract GetSubscriptionRequest getSubscriptionRequest(Long id);

    @Mapping(target = "id", source = "subscription.membershipId")
    public abstract GetMembershipRequest getMembershipRequest(GetSubscriptionResponse output);

    public abstract GetSubscriptionDetailResult getSubscriptionDetailResult(
            GetSubscriptionResponse output, GetMembershipResponse membershipOutput, CardDTO card);

    @Mapping(target = "tenant", expression = "java(tenant())")
    @Mapping(target = "customerIdIn", source = "customerId")
    @Mapping(target = "pagination", defaultExpression = "java(paginationConverter.request())")
    public abstract ListSubscriptionsRequest listSubscriptionsRequest(ListSubscriptionsParams params);

    @Mapping(target = "membershipSubscriptions", source = "result.membershipSubscriptions")
    @Mapping(target = "pagination", source = "result.pagination")
    public abstract ListSubscriptionsResult listSubscriptionsResult(ListSubscriptionsResponse response);

    @Mapping(target = "tenant", expression = "java(tenant())")
    @Mapping(target = "operation", expression = "java(OperationUtil.staffOperation())")
    public abstract CancelSubscriptionRequest cancelSubscriptionRequest(CancelSubscriptionParams params);

    public abstract CancelSubscriptionResult cancelSubscriptionResult(CancelSubscriptionResponse response);

    @Mapping(target = "tenant", expression = "java(tenant())")
    @Mapping(target = "operation", expression = "java(OperationUtil.staffOperation())")
    public abstract RenewSubscriptionRequest renewSubscriptionRequest(RenewSubscriptionParams params);

    public abstract RenewSubscriptionResult renewSubscriptionResult(RenewSubscriptionResponse response);

    public abstract PauseSubscriptionRequest pauseSubscriptionRequest(PauseSubscriptionParams params);

    public abstract PauseSubscriptionResult pauseSubscriptionResult(PauseSubscriptionResponse response);

    public abstract ResumeSubscriptionRequest resumeSubscriptionRequest(ResumeSubscriptionParams params);

    public abstract ResumeSubscriptionResult resumeSubscriptionResult(ResumeSubscriptionResponse response);

    @Mapping(target = "id", constant = "1L")
    public abstract MembershipModel membershipModel(MembershipCreateDef def, Long companyId);

    @Mapping(
            target = "membership",
            expression = "java(membershipModel(request.getMembershipDef(), request.getCompanyId()))")
    public abstract CreateMembershipResponse createMembershipResponse(CreateMembershipRequest request);

    public abstract MembershipCreateDef membershipCreateDef(MembershipModel model);

    @Mapping(target = "membership.id", constant = "1L")
    public abstract GetMembershipResponse getMembershipResponse(MembershipModel membership);

    public abstract GetTaxRuleResponse getTaxRuleResponse(TaxRuleModel rule);

    public abstract MembershipModel membershipModel(GetMembershipRequest request);

    @Mapping(target = "membership", source = ".")
    public abstract GetMembershipResponse getMembershipResponse(GetMembershipRequest request);

    public abstract GetSellLinkRequest getSellLinkRequest(CreateCardParams request);

    @Mapping(target = "card", source = ".")
    public abstract CreateCardResult createCardResult(CardDTO card);

    @Mapping(target = ".", source = ".")
    public abstract ListBuyersRequest listBuyersRequest(ListBuyersParams request);

    @Mapping(target = ".", source = ".")
    public abstract ListBuyersResult listBuyersResult(ListBuyersResponse output);

    @Mapping(target = ".", source = ".")
    public abstract GetBuyerReportRequest getBuyerReportRequest(GetBuyerReportParams request);

    @Mapping(target = ".", source = ".")
    public abstract GetBuyerReportResult getBuyerReportResult(GetBuyerReportResponse output);

    @Mapping(target = ".", source = ".")
    public abstract ListPaymentHistoryRequest getListPaymentHistoryRequest(ListPaymentHistoryParams request);

    @Mapping(target = ".", source = ".")
    @Mapping(target = "paymentHistory", source = "historyViews")
    public abstract ListPaymentHistoryHistoryResult getListPaymentHistoryHistoryResult(
            ListPaymentHistoryResponse output);

    @Mapping(target = ".", source = ".")
    public abstract ListRecommendMembershipsRequest getListRecommendedMembershipsRequest(
            ListRecommendMembershipsParams request);

    @Mapping(target = ".", source = ".")
    public abstract ListRecommendMembershipsResult getListRecommendedMembershipsResult(
            ListRecommendMembershipsResponse output);

    @Mapping(target = ".", source = ".")
    public abstract ListMembershipsForCustomerRequest getListMembershipsForCustomerRequest(
            ListMembershipsForCustomerParams request);

    @Mapping(target = ".", source = ".")
    public abstract ListMembershipsForCustomerResult getListMembershipsForCustomerResult(
            ListMembershipsForCustomerResponse output);

    @Mapping(target = ".", source = ".")
    public abstract ListActivityLogsRequest listActivityLogsRequest(ListActivityLogsParams request);

    @Mapping(target = ".", source = ".")
    public abstract ListActivityLogsResult listActivityLogsResult(ListActivityLogsResponse output);

    @Mapping(target = ".", source = ".")
    public abstract RetrySubscriptionPaymentRequest retrySubscriptionPaymentRequest(
            RetrySubscriptionPaymentParams request);

    @Mapping(target = ".", source = ".")
    public abstract RetrySubscriptionPaymentResult retrySubscriptionPaymentResult(
            RetrySubscriptionPaymentResponse output);

    public int statusToSort(SubscriptionModel.Status status) {
        return switch (status) {
            case ACTIVE -> 1;
            case PENDING -> 2;
            case CANCELLED -> 3;
            default -> 4;
        };
    }
}
