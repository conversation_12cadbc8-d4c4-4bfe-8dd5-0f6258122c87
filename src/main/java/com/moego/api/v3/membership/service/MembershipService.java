/*
 * @since 2024-06-27 14:39:29
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.membership.service;

import com.moego.api.v3.shared.converter.PaginationConverter;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.membership.v1.MembershipModel;
import com.moego.idl.models.membership.v1.MembershipSubscriptionListModel;
import com.moego.idl.models.membership.v1.SubscriptionModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.membership.v1.ListCustomerSubscriptionsRequest;
import com.moego.idl.service.membership.v1.ListCustomerSubscriptionsResponse;
import com.moego.idl.service.membership.v1.ListMembershipsRequest;
import com.moego.idl.service.membership.v1.ListSubscriptionsRequest;
import com.moego.idl.service.membership.v1.MembershipServiceGrpc;
import com.moego.idl.service.membership.v1.SubscriptionServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.auth.AuthContext;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
@Slf4j
public class MembershipService {
    private final SubscriptionServiceGrpc.SubscriptionServiceBlockingStub subscriptionServiceBlockingStub;
    private final MembershipServiceGrpc.MembershipServiceBlockingStub membershipServiceBlockingStub;
    private final PaginationConverter paginationConverter;
    private static final int MAX_SUBSCRIPTIONS = 10;

    private PaginationRequest defaultPage;
    private MembershipSubscriptionListModel defaultResponse;

    @PostConstruct
    public void init() {
        defaultPage = paginationConverter.request(10, 1);
        defaultResponse = MembershipSubscriptionListModel.newBuilder()
                .setPagination(paginationConverter.response(10, 1, 0))
                .build();
    }

    public MembershipSubscriptionListModel getSubscriptions(Long customerId) {
        if (customerId == null || customerId == 0) {
            return defaultResponse;
        }
        final var req = ListSubscriptionsRequest.newBuilder()
                .setTenant(Tenant.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .build())
                .addCustomerIdIn(customerId)
                .addStatusIn(SubscriptionModel.Status.ACTIVE)
                .setPagination(defaultPage)
                .build();
        return tryCall(
                () -> subscriptionServiceBlockingStub.listSubscriptions(req).getResult(), defaultResponse);
    }

    private <T> T tryCall(Callable<T> callable, T dft) {
        try {
            return callable.call();
        } catch (Exception e) {
            log.error("Failed to call", e);
            return dft;
        }
    }

    public Map<Long, MembershipSubscriptionListModel> getAllSubscriptions(List<Long> customerIdList) {
        if (CollectionUtils.isEmpty(customerIdList)) {
            return Map.of();
        }

        final var req = ListCustomerSubscriptionsRequest.newBuilder()
                .setTenant(Tenant.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .build())
                .addAllCustomerIds(customerIdList)
                .addStatusIn(SubscriptionModel.Status.ACTIVE)
                .setMaxSubscriptions(MAX_SUBSCRIPTIONS)
                .build();

        return tryCall(
                () ->
                        subscriptionServiceBlockingStub
                                .listCustomerSubscriptions(req)
                                .getCustomerSubscriptionsList()
                                .stream()
                                .collect(Collectors.toMap(
                                        ListCustomerSubscriptionsResponse.CustomerWithSubscriptionList::getCustomerId,
                                        subscriptions -> MembershipSubscriptionListModel.newBuilder()
                                                .setPagination(paginationConverter.response(
                                                        MAX_SUBSCRIPTIONS, 1, subscriptions.getTotalCount()))
                                                .addAllMembershipSubscriptions(subscriptions.getSubscriptionsList())
                                                .build())),
                customerIdList.stream().collect(Collectors.toMap(k -> k, v -> defaultResponse)));
    }

    public Map<Long, MembershipModel> listMembershipModel(List<Long> membershipIds) {
        Map<Long, MembershipModel> resMap = new HashMap<>();
        if (CollectionUtils.isEmpty(membershipIds)) {
            return resMap;
        }
        var split = CommonUtil.splitListByItemNum(membershipIds, 1000);
        split.forEach(mIds -> {
            membershipServiceBlockingStub
                    .listMemberships(ListMembershipsRequest.newBuilder()
                            .addAllIdIn(mIds)
                            .setPagination(PaginationRequest.newBuilder()
                                    .setPageNum(1)
                                    .setPageSize(1000)
                                    .build())
                            .build())
                    .getMembershipsList()
                    .forEach(membershipModel -> {
                        resMap.put(membershipModel.getId(), membershipModel);
                    });
        });
        return resMap;
    }
}
