/*
 * @since 2024-06-18 09:47:09
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.membership.controller;

import static com.moego.idl.service.organization.v1.CompanyServiceGrpc.CompanyServiceBlockingStub;
import static com.moego.lib.common.auth.AuthType.ANONYMOUS;
import static com.moego.lib.common.auth.AuthType.COMPANY;

import com.moego.api.v3.membership.converter.MembershipConverter;
import com.moego.api.v3.membership.service.PaymentService;
import com.moego.api.v3.shared.property.CustomerProperty;
import com.moego.api.v3.shared.property.MembershipProperty;
import com.moego.idl.api.membership.v1.CancelSubscriptionParams;
import com.moego.idl.api.membership.v1.CancelSubscriptionResult;
import com.moego.idl.api.membership.v1.CreateCardParams;
import com.moego.idl.api.membership.v1.CreateCardResult;
import com.moego.idl.api.membership.v1.CreateSellLinkParams;
import com.moego.idl.api.membership.v1.CreateSellLinkResult;
import com.moego.idl.api.membership.v1.GetBuyerReportParams;
import com.moego.idl.api.membership.v1.GetBuyerReportResult;
import com.moego.idl.api.membership.v1.GetSubscriptionDetailParams;
import com.moego.idl.api.membership.v1.GetSubscriptionDetailResult;
import com.moego.idl.api.membership.v1.ImportQuantityEntitlementsParams;
import com.moego.idl.api.membership.v1.ImportQuantityEntitlementsResult;
import com.moego.idl.api.membership.v1.ImportSubscriptionsParams;
import com.moego.idl.api.membership.v1.ImportSubscriptionsResult;
import com.moego.idl.api.membership.v1.ListActivityLogsParams;
import com.moego.idl.api.membership.v1.ListActivityLogsResult;
import com.moego.idl.api.membership.v1.ListBuyersParams;
import com.moego.idl.api.membership.v1.ListBuyersResult;
import com.moego.idl.api.membership.v1.ListPaymentHistoryHistoryResult;
import com.moego.idl.api.membership.v1.ListPaymentHistoryParams;
import com.moego.idl.api.membership.v1.ListSubscriptionsParams;
import com.moego.idl.api.membership.v1.ListSubscriptionsResult;
import com.moego.idl.api.membership.v1.PauseSubscriptionParams;
import com.moego.idl.api.membership.v1.PauseSubscriptionResult;
import com.moego.idl.api.membership.v1.RenewSubscriptionParams;
import com.moego.idl.api.membership.v1.RenewSubscriptionResult;
import com.moego.idl.api.membership.v1.ResumeSubscriptionParams;
import com.moego.idl.api.membership.v1.ResumeSubscriptionResult;
import com.moego.idl.api.membership.v1.RetrySubscriptionPaymentParams;
import com.moego.idl.api.membership.v1.RetrySubscriptionPaymentResult;
import com.moego.idl.api.membership.v1.SellMembershipParams;
import com.moego.idl.api.membership.v1.SellMembershipResult;
import com.moego.idl.api.membership.v1.SubscriptionServiceGrpc.SubscriptionServiceImplBase;
import com.moego.idl.api.membership.v1.UpdateSubscriptionParams;
import com.moego.idl.api.membership.v1.UpdateSubscriptionResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.membership.v1.MembershipSubscriptionModel;
import com.moego.idl.models.membership.v1.SellLinkModel;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub;
import com.moego.idl.service.membership.v1.DataMigrationServiceGrpc.DataMigrationServiceBlockingStub;
import com.moego.idl.service.membership.v1.GetBuyerReportRequest;
import com.moego.idl.service.membership.v1.GetBuyerReportResponse;
import com.moego.idl.service.membership.v1.GetMembershipRequest;
import com.moego.idl.service.membership.v1.GetSubscriptionRequest;
import com.moego.idl.service.membership.v1.ImportQuantityEntitlementsRequest;
import com.moego.idl.service.membership.v1.ImportSubscriptionsRequest;
import com.moego.idl.service.membership.v1.ListActivityLogsRequest;
import com.moego.idl.service.membership.v1.ListActivityLogsResponse;
import com.moego.idl.service.membership.v1.ListBuyersRequest;
import com.moego.idl.service.membership.v1.ListBuyersResponse;
import com.moego.idl.service.membership.v1.ListPaymentHistoryRequest;
import com.moego.idl.service.membership.v1.ListPaymentHistoryResponse;
import com.moego.idl.service.membership.v1.MembershipServiceGrpc.MembershipServiceBlockingStub;
import com.moego.idl.service.membership.v1.RetrySubscriptionPaymentRequest;
import com.moego.idl.service.membership.v1.RetrySubscriptionPaymentResponse;
import com.moego.idl.service.membership.v1.SubscriptionServiceGrpc.SubscriptionServiceBlockingStub;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc.BusinessServiceBlockingStub;
import com.moego.idl.service.organization.v1.TaxRuleServiceGrpc.TaxRuleServiceBlockingStub;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.params.CustomerInfoIdParams;
import com.moego.server.payment.client.IPaymentCreditCardClient;
import com.moego.server.payment.params.CustomerStripInfoRequest;
import io.grpc.stub.StreamObserver;
import java.util.Comparator;
import lombok.RequiredArgsConstructor;

@RequiredArgsConstructor
@GrpcService
public class SubscriptionController extends SubscriptionServiceImplBase {
    private final MembershipConverter membershipConverter;
    private final SubscriptionServiceBlockingStub subscriptionService;
    private final CompanyServiceBlockingStub companyService;
    private final BusinessServiceBlockingStub businessService;
    private final MembershipServiceBlockingStub membershipService;
    private final BusinessCustomerServiceBlockingStub businessCustomerService;
    private final TaxRuleServiceBlockingStub taxService;
    private final DataMigrationServiceBlockingStub dataMigrationServiceBlockingStub;
    private final ICustomerCustomerClient customerClient;
    private final IPaymentCreditCardClient iPaymentCreditCardClient;
    private final CustomerProperty customerProperty;
    private final MembershipProperty membershipProperty;
    private final PaymentService paymentService;

    @Override
    @Auth(COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MEMBERSHIP, PermissionEnums.SELL_MEMBERSHIP})
    public void createSellLink(CreateSellLinkParams request, StreamObserver<CreateSellLinkResult> responseObserver) {
        paymentService.checkMoegoPayStatus(request.getBusinessId());
        customerProperty.check(request.getSellLinkDef().getCustomerId());
        membershipProperty.check(request.getSellLinkDef().getMembershipId());
        final var input = membershipConverter.createSellLinkRequest(request);
        final var output = subscriptionService.createSellLink(input);
        final var result = membershipConverter.createSellLinkResult(output);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(ANONYMOUS)
    public void createCard(CreateCardParams request, StreamObserver<CreateCardResult> responseObserver) {
        final var sellLinkInput = membershipConverter.getSellLinkRequest(request);
        final var sellLinkOutput = subscriptionService.getSellLink(sellLinkInput);
        final var cardId = saveCard(sellLinkOutput.getSellLink(), request.getExternalCardToken());
        final var card = iPaymentCreditCardClient.getByCardId(cardId);
        final var result = membershipConverter.createCardResult(card);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    private String saveCard(SellLinkModel sellLink, String token) {
        final var cardInput = new CustomerStripInfoRequest();
        cardInput.setBusinessId((int) sellLink.getBusinessId());
        cardInput.setCustomerId((int) sellLink.getCustomerId());
        cardInput.setChargeToken(token);
        cardInput.setIgnoreDuplicateCard(true);
        final var card = iPaymentCreditCardClient.saveNewCard((int) sellLink.getBusinessId(), cardInput);
        return card.getPaymentMethodId();
    }

    @Override
    @Auth(COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MEMBERSHIP, PermissionEnums.SELL_MEMBERSHIP})
    public void sellMembership(SellMembershipParams request, StreamObserver<SellMembershipResult> responseObserver) {
        paymentService.checkMoegoPayStatus(request.getBusinessId());
        final var createInput = membershipConverter.createSubscriptionRequest(request);
        final var createOutput = subscriptionService.createSubscription(createInput);
        final var result = membershipConverter.sellMembershipResult(createOutput);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void getSubscriptionDetail(
            GetSubscriptionDetailParams request, StreamObserver<GetSubscriptionDetailResult> responseObserver) {
        final var input = membershipConverter.getSubscriptionRequest(request);
        final var output = subscriptionService.getSubscription(input);
        final var membershipInput = membershipConverter.getMembershipRequest(output);
        final var membershipOutput = membershipService.getMembership(membershipInput);
        final var card =
                iPaymentCreditCardClient.getByCardId(output.getSubscription().getLatestCardOnFileId());
        final var result = membershipConverter.getSubscriptionDetailResult(output, membershipOutput, card);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void updateSubscription(
            UpdateSubscriptionParams request, StreamObserver<UpdateSubscriptionResult> responseObserver) {
        final var input = membershipConverter.updateSubscriptionRequest(request);
        final var output = subscriptionService.updateSubscription(input);
        final var result = membershipConverter.updateSubscriptionResult(output);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void listSubscriptions(
            ListSubscriptionsParams request, StreamObserver<ListSubscriptionsResult> responseObserver) {
        final var input = membershipConverter.listSubscriptionsRequest(request);
        final var output = subscriptionService.listSubscriptions(input);
        final var result = membershipConverter.listSubscriptionsResult(output);
        final var membershipSubscriptionList = result.getMembershipSubscriptionsList().stream()
                .sorted(Comparator.<MembershipSubscriptionModel>comparingInt(membershipSubscriptionModel ->
                                membershipConverter.statusToSort(membershipSubscriptionModel
                                        .getSubscription()
                                        .getStatus()))
                        .thenComparingLong(membershipSubscriptionModel -> membershipSubscriptionModel
                                .getSubscription()
                                .getCreatedAt()
                                .getSeconds()))
                .toList();
        responseObserver.onNext(result.toBuilder()
                .clearMembershipSubscriptions()
                .addAllMembershipSubscriptions(membershipSubscriptionList)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.OPERATE_MEMBERSHIP})
    public void cancelSubscription(
            CancelSubscriptionParams request, StreamObserver<CancelSubscriptionResult> responseObserver) {
        final var input = membershipConverter.cancelSubscriptionRequest(request);
        final var output = subscriptionService.cancelSubscription(input);
        final var result = membershipConverter.cancelSubscriptionResult(output);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.OPERATE_MEMBERSHIP})
    public void renewSubscription(
            RenewSubscriptionParams request, StreamObserver<RenewSubscriptionResult> responseObserver) {
        final var getSubscriptionInput = membershipConverter.getSubscriptionRequest(request.getId());
        final var getSubscriptionOutput = subscriptionService.getSubscription(getSubscriptionInput);
        paymentService.checkMoegoPayStatus(
                getSubscriptionOutput.getSubscription().getBusinessId());
        final var input = membershipConverter.renewSubscriptionRequest(request);
        final var output = subscriptionService.renewSubscription(input);
        final var result = membershipConverter.renewSubscriptionResult(output);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.OPERATE_MEMBERSHIP})
    public void pauseSubscription(
            PauseSubscriptionParams request, StreamObserver<PauseSubscriptionResult> responseObserver) {
        final var input = membershipConverter.pauseSubscriptionRequest(request);
        final var output = subscriptionService.pauseSubscription(input);
        final var result = membershipConverter.pauseSubscriptionResult(output);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.OPERATE_MEMBERSHIP})
    public void resumeSubscription(
            ResumeSubscriptionParams request, StreamObserver<ResumeSubscriptionResult> responseObserver) {
        final var input = membershipConverter.resumeSubscriptionRequest(request);
        final var output = subscriptionService.resumeSubscription(input);
        final var result = membershipConverter.resumeSubscriptionResult(output);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MEMBERSHIP})
    public void listBuyers(ListBuyersParams request, StreamObserver<ListBuyersResult> responseObserver) {
        final ListBuyersRequest input = membershipConverter.listBuyersRequest(request);
        final ListBuyersResponse output = subscriptionService.listBuyers(input);
        final ListBuyersResult result = membershipConverter.listBuyersResult(output);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MEMBERSHIP})
    public void getBuyerReport(GetBuyerReportParams request, StreamObserver<GetBuyerReportResult> responseObserver) {
        final GetBuyerReportRequest input = membershipConverter.getBuyerReportRequest(request);
        final GetBuyerReportResponse output = subscriptionService.getBuyerReport(input);
        final GetBuyerReportResult result = membershipConverter.getBuyerReportResult(output);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.ACCESS_MEMBERSHIP})
    public void listPaymentHistory(
            ListPaymentHistoryParams request, StreamObserver<ListPaymentHistoryHistoryResult> responseObserver) {
        final ListPaymentHistoryRequest input = membershipConverter.getListPaymentHistoryRequest(request);
        final ListPaymentHistoryResponse output = subscriptionService.listPaymentHistory(input);
        final ListPaymentHistoryHistoryResult result = membershipConverter.getListPaymentHistoryHistoryResult(output);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void importSubscriptions(
            ImportSubscriptionsParams request, StreamObserver<ImportSubscriptionsResult> responseObserver) {
        final var req =
                request.getData().toBuilder().setCompanyId(AuthContext.get().companyId());
        membershipService.getMembership(GetMembershipRequest.newBuilder()
                .setCompanyId(req.getCompanyId())
                .setId(req.getMembershipId())
                .build());
        final var customer = customerClient.getCustomerWithDeletedHasCompanyId(
                new CustomerInfoIdParams().setCompanyId(req.getCompanyId()).setCustomerId((int) req.getCustomerId()));
        if (customer == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "not found customer");
        }
        final var res = dataMigrationServiceBlockingStub.importSubscriptions(
                ImportSubscriptionsRequest.newBuilder().addData(req).build());
        if (res.getImportedCount() == 0) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        responseObserver.onNext(ImportSubscriptionsResult.newBuilder()
                .setImported(res.getImported(0))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void importQuantityEntitlements(
            ImportQuantityEntitlementsParams request,
            StreamObserver<ImportQuantityEntitlementsResult> responseObserver) {
        final var data = request.getData();
        subscriptionService.getSubscription(GetSubscriptionRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setId(data.getSubscriptionId())
                .build());
        final var res = dataMigrationServiceBlockingStub.importQuantityEntitlements(
                ImportQuantityEntitlementsRequest.newBuilder().addData(data).build());
        if (res.getImportedCount() == 0) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
        responseObserver.onNext(ImportQuantityEntitlementsResult.newBuilder()
                .setImported(res.getImported(0))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void listActivityLogs(
            ListActivityLogsParams request, StreamObserver<ListActivityLogsResult> responseObserver) {
        final ListActivityLogsRequest input = membershipConverter.listActivityLogsRequest(request);
        final ListActivityLogsResponse output = subscriptionService.listActivityLogs(input);
        final ListActivityLogsResult result = membershipConverter.listActivityLogsResult(output);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void retrySubscriptionPayment(
            RetrySubscriptionPaymentParams request, StreamObserver<RetrySubscriptionPaymentResult> responseObserver) {
        final RetrySubscriptionPaymentRequest input = membershipConverter.retrySubscriptionPaymentRequest(request);
        final RetrySubscriptionPaymentResponse output = subscriptionService.retrySubscriptionPayment(input);
        final RetrySubscriptionPaymentResult result = membershipConverter.retrySubscriptionPaymentResult(output);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }
}
