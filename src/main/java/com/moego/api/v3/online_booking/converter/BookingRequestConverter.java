package com.moego.api.v3.online_booking.converter;

import static com.moego.api.v3.shared.util.ProtobufUtil.toLocalDate;

import com.moego.idl.api.online_booking.v1.BoardingAddOnDetail;
import com.moego.idl.api.online_booking.v1.BoardingService;
import com.moego.idl.api.online_booking.v1.BoardingServiceDetail;
import com.moego.idl.api.online_booking.v1.BookingRequestDetail;
import com.moego.idl.api.online_booking.v1.DaycareAddOnDetail;
import com.moego.idl.api.online_booking.v1.DaycareService;
import com.moego.idl.api.online_booking.v1.DaycareServiceDetail;
import com.moego.idl.api.online_booking.v1.DogWalkingServiceDetail;
import com.moego.idl.api.online_booking.v1.EvaluationTestDetail;
import com.moego.idl.api.online_booking.v1.FeedingDetail;
import com.moego.idl.api.online_booking.v1.GroomingAddOnDetail;
import com.moego.idl.api.online_booking.v1.GroomingServiceDetail;
import com.moego.idl.api.online_booking.v1.GroupClassServiceDetail;
import com.moego.idl.api.online_booking.v1.MedicationDetail;
import com.moego.idl.api.online_booking.v1.PetToLodging;
import com.moego.idl.api.online_booking.v1.PetToService;
import com.moego.idl.api.online_booking.v1.PetToStaff;
import com.moego.idl.models.offering.v1.GroupClassInstance;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.online_booking.v1.BoardingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.DaycareAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.DogWalkingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.EvaluationTestDetailModel;
import com.moego.idl.models.online_booking.v1.FeedingModel;
import com.moego.idl.models.online_booking.v1.GroomingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.GroupClassServiceDetailModel;
import com.moego.idl.models.online_booking.v1.MedicationModel;
import com.moego.idl.models.online_booking.v1.PetToLodgingDef;
import com.moego.idl.models.online_booking.v1.PetToServiceDef;
import com.moego.idl.models.online_booking.v1.PetToStaffDef;
import com.moego.lib.common.util.JsonUtil;
import java.util.AbstractMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper(
        imports = {JsonUtil.class},
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BookingRequestConverter {

    BookingRequestConverter INSTANCE = Mappers.getMapper(BookingRequestConverter.class);

    BookingRequestDetail toBookingRequestDetail(
            BookingRequestModel model, List<ServiceItemType> serviceItemTypes, boolean noStartTime);

    GroomingServiceDetail toGroomingServiceDetail(
            GroomingServiceDetailModel model, String serviceName, String staffName);

    DogWalkingServiceDetail toDogWalkingServiceDetail(
            DogWalkingServiceDetailModel model, String serviceName, String staffName);

    GroomingAddOnDetail toGroomingAddOnDetail(GroomingAddOnDetailModel model, String serviceName, String staffName);

    BoardingServiceDetail toBoardingServiceDetail(
            BoardingServiceDetailModel model, String serviceName, String lodgingUnitName, ServicePriceUnit priceUnit);

    GroupClassServiceDetail toGroupClassServiceDetail(
            GroupClassServiceDetailModel service,
            String serviceName,
            String staffName,
            GroupClassInstance.Occurrence occurrence);

    default BoardingAddOnDetail toBoardingAddOnDetail(
            BoardingAddOnDetailModel model, Map<Long, ServiceBriefView> serviceMap) {
        var builder = BoardingAddOnDetail.newBuilder();

        builder.setId(model.getId());
        builder.setBookingRequestId(model.getBookingRequestId());
        builder.setServiceDetailId(model.getServiceDetailId());
        builder.setPetId(model.getPetId());
        builder.setAddOnId(model.getAddOnId());
        builder.setIsEveryday(model.getIsEveryday());
        builder.setServicePrice(model.getServicePrice());
        builder.setTaxId(model.getTaxId());
        builder.setDuration(model.getDuration());
        builder.setQuantityPerDay(model.getQuantityPerDay());
        builder.setDateType(model.getDateType());
        builder.addAllSpecificDates(model.getSpecificDatesList());
        builder.setServiceName(toServiceName(serviceMap, model.getAddOnId()));
        builder.setRequireDedicatedStaff(toRequireDedicatedStaff(serviceMap, model.getAddOnId()));
        if (model.hasStartDate()) {
            builder.setStartDate(toLocalDate(model.getStartDate()).toString());
        }

        return builder.build();
    }

    default List<BoardingAddOnDetail> toBoardingAddOnDetail(
            List<BoardingAddOnDetailModel> model, Map<Long, ServiceBriefView> serviceMap) {
        if (CollectionUtils.isEmpty(model)) {
            return List.of();
        }
        return model.stream().map(m -> toBoardingAddOnDetail(m, serviceMap)).toList();
    }

    FeedingDetail toFeedingDetail(FeedingModel model);

    default List<FeedingDetail> toFeedingDetail(List<FeedingModel> models) {
        if (CollectionUtils.isEmpty(models)) {
            return List.of();
        }
        return models.stream().map(this::toFeedingDetail).toList();
    }

    MedicationDetail toMedicationDetail(MedicationModel model);

    default List<MedicationDetail> toMedicationDetail(List<MedicationModel> models) {
        if (CollectionUtils.isEmpty(models)) {
            return List.of();
        }
        return models.stream().map(this::toMedicationDetail).toList();
    }

    DaycareServiceDetail toDaycareServiceDetail(DaycareServiceDetailModel model, String serviceName);

    default DaycareAddOnDetail toDaycareAddOnDetail(
            DaycareAddOnDetailModel model, Map<Long, ServiceBriefView> serviceMap) {
        var builder = DaycareAddOnDetail.newBuilder();

        builder.setId(model.getId());
        builder.setBookingRequestId(model.getBookingRequestId());
        builder.setServiceDetailId(model.getServiceDetailId());
        builder.setPetId(model.getPetId());
        builder.setAddOnId(model.getAddOnId());
        builder.setIsEveryday(model.getIsEveryday());
        builder.setServicePrice(model.getServicePrice());
        builder.setTaxId(model.getTaxId());
        builder.setDuration(model.getDuration());
        builder.setQuantityPerDay(model.getQuantityPerDay());
        builder.addAllSpecificDates(model.getSpecificDatesList());
        builder.setServiceName(toServiceName(serviceMap, model.getAddOnId()));
        builder.setRequireDedicatedStaff(toRequireDedicatedStaff(serviceMap, model.getAddOnId()));

        return builder.build();
    }

    default List<DaycareAddOnDetail> toDaycareAddOnDetail(
            List<DaycareAddOnDetailModel> models, Map<Long, ServiceBriefView> serviceMap) {
        if (CollectionUtils.isEmpty(models)) {
            return List.of();
        }
        return models.stream()
                .map(model -> toDaycareAddOnDetail(model, serviceMap))
                .toList();
    }

    EvaluationTestDetail toEvaluationTestDetail(
            EvaluationTestDetailModel model, String serviceName, String evaluationName);

    PetToLodgingDef toPetToLodgingDef(PetToLodging petToLodging);

    PetToLodging toPetToLodging(PetToLodgingDef petToLodging);

    PetToStaffDef toPetToStaffDef(PetToStaff petToStaff);

    PetToStaff toPetToStaff(PetToStaffDef petToStaff);

    PetToServiceDef toPetToServiceDef(PetToService petToservice);

    default DaycareService toDaycareService(
            DaycareServiceDetailModel service,
            List<DaycareAddOnDetailModel> addOns,
            List<FeedingModel> feedings,
            List<MedicationModel> medications,
            Map<Long, ServiceBriefView> serviceMap) {
        var builder = DaycareService.newBuilder()
                .setService(toDaycareServiceDetail(service, toServiceName(serviceMap, service.getServiceId())))
                .addAllAddons(toDaycareAddOnDetail(addOns, serviceMap))
                .addAllFeedings(toFeedingDetail(feedings))
                .addAllMedications(toMedicationDetail(medications));
        // 兼容老版本
        if (!CollectionUtils.isEmpty(feedings)) {
            builder.setFeeding(toFeedingDetail(feedings.get(0)));
        }
        if (!CollectionUtils.isEmpty(medications)) {
            builder.setMedication(toMedicationDetail(medications.get(0)));
        }
        return builder.build();
    }

    default BoardingService toBoardingService(
            BoardingServiceDetailModel service,
            List<BoardingAddOnDetailModel> addOns,
            List<FeedingModel> feedings,
            List<MedicationModel> medications,
            Map<Long, ServiceBriefView> serviceMap,
            AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>> lodgingMap) {
        var builder = BoardingService.newBuilder()
                .setService(toBoardingServiceDetail(
                        service,
                        toServiceName(serviceMap, service.getServiceId()),
                        toLodgingUnitName(lodgingMap, service.getLodgingId()),
                        toPriceUnit(serviceMap, service.getServiceId())))
                .addAllAddons(toBoardingAddOnDetail(addOns, serviceMap))
                .addAllFeedings(toFeedingDetail(feedings))
                .addAllMedications(toMedicationDetail(medications));
        // 兼容老版本
        if (!CollectionUtils.isEmpty(feedings)) {
            builder.setFeeding(toFeedingDetail(feedings.get(0)));
        }
        if (!CollectionUtils.isEmpty(medications)) {
            builder.setMedication(toMedicationDetail(medications.get(0)));
        }
        return builder.build();
    }

    default String toServiceName(Map<Long, ServiceBriefView> serviceMap, Long serviceId) {
        if (serviceId <= 0) {
            return "";
        }
        ServiceBriefView serviceBriefView = serviceMap.get(serviceId);
        if (Objects.isNull(serviceBriefView)) {
            return "";
        }
        return serviceBriefView.getName();
    }

    default boolean toRequireDedicatedStaff(Map<Long, ServiceBriefView> serviceMap, Long serviceId) {
        return Optional.ofNullable(serviceMap.get(serviceId))
                .map(ServiceBriefView::getRequireDedicatedStaff)
                .orElse(false);
    }

    default String toLodgingUnitName(
            AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>> lodgingMap,
            Long lodgingId) {
        if (lodgingId <= 0) {
            return "";
        }
        LodgingUnitModel lodgingUnitModel = lodgingMap.getKey().get(lodgingId);
        if (Objects.isNull(lodgingUnitModel)) {
            return "";
        }
        return lodgingUnitModel.getName();
    }

    default ServicePriceUnit toPriceUnit(Map<Long, ServiceBriefView> serviceMap, Long serviceId) {
        return Optional.ofNullable(serviceMap.get(serviceId))
                .map(ServiceBriefView::getPriceUnit)
                .orElse(ServicePriceUnit.SERVICE_PRICE_UNIT_UNSPECIFIED);
    }
}
