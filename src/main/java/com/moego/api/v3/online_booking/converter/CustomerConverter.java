package com.moego.api.v3.online_booking.converter;

import com.moego.idl.api.online_booking.v1.OBCheckIdentifierResponse;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.springframework.util.StringUtils;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CustomerConverter {

    String MASKED_PATTERN = "****%s";

    @Mapping(target = "maskedPhoneNumber", source = "phoneNumber", qualifiedByName = "maskPhoneNumber")
    OBCheckIdentifierResponse.PossibleClient toPossibleClient(BusinessCustomerInfoModel model);

    @Named("maskPhoneNumber")
    default String maskPhoneNumber(String phoneNumber) {
        if (!StringUtils.hasText(phoneNumber)) {
            return phoneNumber;
        }

        var last4 = phoneNumber.substring(Math.max(0, phoneNumber.length() - 4));
        return MASKED_PATTERN.formatted(last4);
    }
}
