package com.moego.api.v3.online_booking.controller;

import com.moego.api.v3.online_booking.converter.ArrivalPickUpTimeConverter;
import com.moego.api.v3.online_booking.service.BusinessCustomerService;
import com.moego.idl.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideParams;
import com.moego.idl.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideResult;
import com.moego.idl.api.online_booking.v1.BatchDeleteArrivalPickUpTimeOverrideParams;
import com.moego.idl.api.online_booking.v1.BatchDeleteArrivalPickUpTimeOverrideResult;
import com.moego.idl.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideParams;
import com.moego.idl.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideResult;
import com.moego.idl.api.online_booking.v1.CreateCapacityOverrideParams;
import com.moego.idl.api.online_booking.v1.CreateCapacityOverrideResult;
import com.moego.idl.api.online_booking.v1.DeleteCapacityOverrideParams;
import com.moego.idl.api.online_booking.v1.DeleteCapacityOverrideResult;
import com.moego.idl.api.online_booking.v1.GetBoardingServiceAvailabilityParams;
import com.moego.idl.api.online_booking.v1.GetBoardingServiceAvailabilityResult;
import com.moego.idl.api.online_booking.v1.GetDaycareServiceAvailabilityParams;
import com.moego.idl.api.online_booking.v1.GetDaycareServiceAvailabilityResult;
import com.moego.idl.api.online_booking.v1.GetEvaluationServiceAvailabilityParams;
import com.moego.idl.api.online_booking.v1.GetEvaluationServiceAvailabilityResult;
import com.moego.idl.api.online_booking.v1.GetGroomingServiceAvailabilityParams;
import com.moego.idl.api.online_booking.v1.GetGroomingServiceAvailabilityResult;
import com.moego.idl.api.online_booking.v1.ListArrivalPickUpTimeOverridesParams;
import com.moego.idl.api.online_booking.v1.ListArrivalPickUpTimeOverridesResult;
import com.moego.idl.api.online_booking.v1.OBAvailabilitySettingServiceGrpc;
import com.moego.idl.api.online_booking.v1.UpdateBoardingServiceAvailabilityParams;
import com.moego.idl.api.online_booking.v1.UpdateBoardingServiceAvailabilityResult;
import com.moego.idl.api.online_booking.v1.UpdateCapacityOverrideParams;
import com.moego.idl.api.online_booking.v1.UpdateCapacityOverrideResult;
import com.moego.idl.api.online_booking.v1.UpdateDaycareServiceAvailabilityParams;
import com.moego.idl.api.online_booking.v1.UpdateDaycareServiceAvailabilityResult;
import com.moego.idl.api.online_booking.v1.UpdateEvaluationServiceAvailabilityParams;
import com.moego.idl.api.online_booking.v1.UpdateEvaluationServiceAvailabilityResult;
import com.moego.idl.api.online_booking.v1.UpdateGroomingServiceAvailabilityParams;
import com.moego.idl.api.online_booking.v1.UpdateGroomingServiceAvailabilityResult;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.TimeRangeType;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest;
import com.moego.idl.service.online_booking.v1.BatchDeleteArrivalPickUpTimeOverrideRequest;
import com.moego.idl.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideRequest;
import com.moego.idl.service.online_booking.v1.CreateCapacityOverrideRequest;
import com.moego.idl.service.online_booking.v1.CreateCapacityOverrideResponse;
import com.moego.idl.service.online_booking.v1.DeleteCapacityOverrideRequest;
import com.moego.idl.service.online_booking.v1.DeleteCapacityOverrideResponse;
import com.moego.idl.service.online_booking.v1.GetBoardingServiceAvailabilitySettingRequest;
import com.moego.idl.service.online_booking.v1.GetDaycareServiceAvailabilitySettingRequest;
import com.moego.idl.service.online_booking.v1.GetEvaluationServiceAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.GetGroomingServiceAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.ListArrivalPickUpTimeOverridesRequest;
import com.moego.idl.service.online_booking.v1.UpdateBoardingServiceAvailabilitySettingRequest;
import com.moego.idl.service.online_booking.v1.UpdateCapacityOverrideRequest;
import com.moego.idl.service.online_booking.v1.UpdateCapacityOverrideResponse;
import com.moego.idl.service.online_booking.v1.UpdateDaycareServiceAvailabilitySettingRequest;
import com.moego.idl.service.online_booking.v1.UpdateEvaluationServiceAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.UpdateGroomingServiceAvailabilityRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class OBAvailabilitySettingController
        extends OBAvailabilitySettingServiceGrpc.OBAvailabilitySettingServiceImplBase {
    private final com.moego.idl.service.online_booking.v1.OBAvailabilitySettingServiceGrpc
                    .OBAvailabilitySettingServiceBlockingStub
            availabilitySettingService;
    private final BusinessCustomerService businessCustomerService;

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void getBoardingServiceAvailability(
            GetBoardingServiceAvailabilityParams request,
            StreamObserver<GetBoardingServiceAvailabilityResult> responseObserver) {
        var resp = availabilitySettingService.getBoardingServiceAvailabilitySetting(
                GetBoardingServiceAvailabilitySettingRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(AuthContext.get().companyId())
                                .setBusinessId(AuthContext.get().businessId())
                                .build())
                        .build());
        var businessPetTypes =
                businessCustomerService.getBusinessPetType(AuthContext.get().companyId());
        // 取 business 可用 pet type，与 OB 可用 pet type 的交集
        businessPetTypes.retainAll(resp.getBoardingServiceAvailabilitySetting().getAcceptedPetTypesList());

        var setting = resp.getBoardingServiceAvailabilitySetting().toBuilder()
                .clearAcceptedPetTypes()
                .addAllAcceptedPetTypes(businessPetTypes)
                .build();

        responseObserver.onNext(GetBoardingServiceAvailabilityResult.newBuilder()
                .setBoardingServiceAvailability(setting)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void updateBoardingServiceAvailability(
            UpdateBoardingServiceAvailabilityParams request,
            StreamObserver<UpdateBoardingServiceAvailabilityResult> responseObserver) {
        var resp = availabilitySettingService.updateBoardingServiceAvailabilitySetting(
                UpdateBoardingServiceAvailabilitySettingRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(AuthContext.get().companyId())
                                .setBusinessId(AuthContext.get().businessId())
                                .build())
                        .setBoardingServiceAvailabilitySetting(request.getBoardingServiceAvailability())
                        .setStaffId(AuthContext.get().staffId())
                        .build());

        var businessPetTypes =
                businessCustomerService.getBusinessPetType(AuthContext.get().companyId());
        // 取 business 可用 pet type，与 OB 可用 pet type 的交集
        businessPetTypes.retainAll(resp.getBoardingServiceAvailabilitySetting().getAcceptedPetTypesList());

        var setting = resp.getBoardingServiceAvailabilitySetting().toBuilder()
                .clearAcceptedPetTypes()
                .addAllAcceptedPetTypes(businessPetTypes)
                .build();

        responseObserver.onNext(UpdateBoardingServiceAvailabilityResult.newBuilder()
                .setBoardingServiceAvailability(setting)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void getDaycareServiceAvailability(
            GetDaycareServiceAvailabilityParams request,
            StreamObserver<GetDaycareServiceAvailabilityResult> responseObserver) {
        var resp = availabilitySettingService.getDaycareServiceAvailabilitySetting(
                GetDaycareServiceAvailabilitySettingRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(AuthContext.get().companyId())
                                .setBusinessId(AuthContext.get().businessId())
                                .build())
                        .build());

        var businessPetTypes =
                businessCustomerService.getBusinessPetType(AuthContext.get().companyId());
        // 取 business 可用 pet type，与 OB 可用 pet type 的交集
        businessPetTypes.retainAll(resp.getDaycareServiceAvailabilitySetting().getAcceptedPetTypesList());

        var setting = resp.getDaycareServiceAvailabilitySetting().toBuilder()
                .clearAcceptedPetTypes()
                .addAllAcceptedPetTypes(businessPetTypes)
                .build();

        responseObserver.onNext(GetDaycareServiceAvailabilityResult.newBuilder()
                .setDaycareServiceAvailability(setting)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void updateDaycareServiceAvailability(
            UpdateDaycareServiceAvailabilityParams request,
            StreamObserver<UpdateDaycareServiceAvailabilityResult> responseObserver) {
        var resp = availabilitySettingService.updateDaycareServiceAvailabilitySetting(
                UpdateDaycareServiceAvailabilitySettingRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(AuthContext.get().companyId())
                                .setBusinessId(AuthContext.get().businessId())
                                .build())
                        .setDaycareServiceAvailabilitySetting(request.getDaycareServiceAvailability())
                        .setStaffId(AuthContext.get().staffId())
                        .build());

        var businessPetTypes =
                businessCustomerService.getBusinessPetType(AuthContext.get().companyId());
        // 取 business 可用 pet type，与 OB 可用 pet type 的交集
        businessPetTypes.retainAll(resp.getDaycareServiceAvailabilitySetting().getAcceptedPetTypesList());

        var setting = resp.getDaycareServiceAvailabilitySetting().toBuilder()
                .clearAcceptedPetTypes()
                .addAllAcceptedPetTypes(businessPetTypes)
                .build();

        responseObserver.onNext(UpdateDaycareServiceAvailabilityResult.newBuilder()
                .setDaycareServiceAvailability(setting)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void getEvaluationServiceAvailability(
            GetEvaluationServiceAvailabilityParams request,
            StreamObserver<GetEvaluationServiceAvailabilityResult> responseObserver) {
        var resp = availabilitySettingService.getEvaluationServiceAvailability(
                GetEvaluationServiceAvailabilityRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(AuthContext.get().companyId())
                                .setBusinessId(AuthContext.get().businessId())
                                .build())
                        .build());

        responseObserver.onNext(GetEvaluationServiceAvailabilityResult.newBuilder()
                .setAvailability(resp.getAvailability())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void updateEvaluationServiceAvailability(
            UpdateEvaluationServiceAvailabilityParams request,
            StreamObserver<UpdateEvaluationServiceAvailabilityResult> responseObserver) {
        var resp = availabilitySettingService.updateEvaluationServiceAvailability(
                UpdateEvaluationServiceAvailabilityRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(AuthContext.get().companyId())
                                .setBusinessId(AuthContext.get().businessId())
                                .build())
                        .setAvailability(request.getAvailability())
                        .setStaffId(AuthContext.get().staffId())
                        .build());

        responseObserver.onNext(UpdateEvaluationServiceAvailabilityResult.newBuilder()
                .setAvailability(resp.getAvailability())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getGroomingServiceAvailability(
            GetGroomingServiceAvailabilityParams request,
            StreamObserver<GetGroomingServiceAvailabilityResult> responseObserver) {
        var resp = availabilitySettingService.getGroomingServiceAvailability(
                GetGroomingServiceAvailabilityRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(AuthContext.get().companyId())
                                .setBusinessId(AuthContext.get().businessId())
                                .build())
                        .build());

        responseObserver.onNext(GetGroomingServiceAvailabilityResult.newBuilder()
                .setAvailability(resp.getAvailability())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void updateGroomingServiceAvailability(
            UpdateGroomingServiceAvailabilityParams request,
            StreamObserver<UpdateGroomingServiceAvailabilityResult> responseObserver) {
        var resp = availabilitySettingService.updateGroomingServiceAvailability(
                UpdateGroomingServiceAvailabilityRequest.newBuilder()
                        .setTenant(Tenant.newBuilder()
                                .setCompanyId(AuthContext.get().companyId())
                                .setBusinessId(AuthContext.get().businessId())
                                .build())
                        .setAvailability(request.getAvailability())
                        .setStaffId(AuthContext.get().staffId())
                        .build());

        responseObserver.onNext(UpdateGroomingServiceAvailabilityResult.newBuilder()
                .setAvailability(resp.getAvailability())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void listArrivalPickUpTimeOverrides(
            ListArrivalPickUpTimeOverridesParams request,
            StreamObserver<ListArrivalPickUpTimeOverridesResult> responseObserver) {
        var resp = availabilitySettingService.listArrivalPickUpTimeOverrides(
                ListArrivalPickUpTimeOverridesRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .addAllBusinessIds(List.of(AuthContext.get().businessId()))
                        .addAllServiceItemTypes(request.getServiceItemTypesList())
                        .build());

        responseObserver.onNext(ListArrivalPickUpTimeOverridesResult.newBuilder()
                .addAllArrivalTimeOverrides(ArrivalPickUpTimeConverter.INSTANCE.buildArrivalPickUpTimeOverrideView(
                        resp.getOverridesList().stream()
                                .filter(k -> k.getType() == TimeRangeType.ARRIVAL_TIME)
                                .toList()))
                .addAllPickUpTimeOverrides(ArrivalPickUpTimeConverter.INSTANCE.buildArrivalPickUpTimeOverrideView(
                        resp.getOverridesList().stream()
                                .filter(k -> k.getType() == TimeRangeType.PICK_UP_TIME)
                                .toList()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void batchCreateArrivalPickUpTimeOverride(
            BatchCreateArrivalPickUpTimeOverrideParams request,
            StreamObserver<BatchCreateArrivalPickUpTimeOverrideResult> responseObserver) {
        if (request.getServiceItemType() == ServiceItemType.UNRECOGNIZED) {
            responseObserver.onNext(BatchCreateArrivalPickUpTimeOverrideResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        List<BatchCreateArrivalPickUpTimeOverrideRequest.CreateDef> overridesToCreate = new ArrayList<>();
        overridesToCreate.addAll(ArrivalPickUpTimeConverter.INSTANCE.buildCreateArrivalPickUpTimeOverrideRequest(
                request.getArrivalTimeOverridesList(), TimeRangeType.ARRIVAL_TIME));
        overridesToCreate.addAll(ArrivalPickUpTimeConverter.INSTANCE.buildCreateArrivalPickUpTimeOverrideRequest(
                request.getPickUpTimeOverridesList(), TimeRangeType.PICK_UP_TIME));
        var resp = availabilitySettingService.batchCreateArrivalPickUpTimeOverride(
                BatchCreateArrivalPickUpTimeOverrideRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(AuthContext.get().businessId())
                        .setServiceItemType(request.getServiceItemType())
                        .addAllOverrides(overridesToCreate)
                        .build());
        responseObserver.onNext(BatchCreateArrivalPickUpTimeOverrideResult.newBuilder()
                .addAllArrivalTimeOverrides(ArrivalPickUpTimeConverter.INSTANCE.buildArrivalPickUpTimeOverrideView(
                        resp.getOverridesList().stream()
                                .filter(k -> k.getType() == TimeRangeType.ARRIVAL_TIME)
                                .toList()))
                .addAllPickUpTimeOverrides(ArrivalPickUpTimeConverter.INSTANCE.buildArrivalPickUpTimeOverrideView(
                        resp.getOverridesList().stream()
                                .filter(k -> k.getType() == TimeRangeType.PICK_UP_TIME)
                                .toList()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void batchDeleteArrivalPickUpTimeOverride(
            BatchDeleteArrivalPickUpTimeOverrideParams request,
            StreamObserver<BatchDeleteArrivalPickUpTimeOverrideResult> responseObserver) {
        var req = BatchDeleteArrivalPickUpTimeOverrideRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .addAllIds(request.getIdsList())
                .build();
        availabilitySettingService.batchDeleteArrivalPickUpTimeOverride(req);
        responseObserver.onNext(BatchDeleteArrivalPickUpTimeOverrideResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void batchUpdateArrivalPickUpTimeOverride(
            BatchUpdateArrivalPickUpTimeOverrideParams request,
            StreamObserver<BatchUpdateArrivalPickUpTimeOverrideResult> responseObserver) {
        List<BatchUpdateArrivalPickUpTimeOverrideRequest.UpdateDef> overridesToUpdate = new ArrayList<>();
        overridesToUpdate.addAll(ArrivalPickUpTimeConverter.INSTANCE.buildUpdateArrivalPickUpTimeOverrideRequest(
                request.getArrivalTimeOverridesList()));
        overridesToUpdate.addAll(ArrivalPickUpTimeConverter.INSTANCE.buildUpdateArrivalPickUpTimeOverrideRequest(
                request.getPickUpTimeOverridesList()));
        var resp = availabilitySettingService.batchUpdateArrivalPickUpTimeOverride(
                BatchUpdateArrivalPickUpTimeOverrideRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .addAllOverrides(overridesToUpdate)
                        .build());
        responseObserver.onNext(BatchUpdateArrivalPickUpTimeOverrideResult.newBuilder()
                .addAllArrivalTimeOverrides(ArrivalPickUpTimeConverter.INSTANCE.buildArrivalPickUpTimeOverrideView(
                        resp.getOverridesList().stream()
                                .filter(k -> k.getType() == TimeRangeType.ARRIVAL_TIME)
                                .toList()))
                .addAllPickUpTimeOverrides(ArrivalPickUpTimeConverter.INSTANCE.buildArrivalPickUpTimeOverrideView(
                        resp.getOverridesList().stream()
                                .filter(k -> k.getType() == TimeRangeType.PICK_UP_TIME)
                                .toList()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void createCapacityOverride(
            CreateCapacityOverrideParams request, StreamObserver<CreateCapacityOverrideResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var businessId = request.getBusinessId();
        CreateCapacityOverrideResponse capacityOverrideResponse =
                availabilitySettingService.createCapacityOverride(CreateCapacityOverrideRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .setCapacityOverride(request.getCapacityOverride())
                        .setServiceItemType(request.getServiceItemType())
                        .build());

        responseObserver.onNext(CreateCapacityOverrideResult.newBuilder()
                .addAllCapacityOverrides((capacityOverrideResponse.getCapacityOverridesList()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void updateCapacityOverride(
            UpdateCapacityOverrideParams request, StreamObserver<UpdateCapacityOverrideResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var businessId = request.getBusinessId();
        UpdateCapacityOverrideResponse updateCapacityOverrideResponse =
                availabilitySettingService.updateCapacityOverride(UpdateCapacityOverrideRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .setCapacityOverride(request.getCapacityOverride())
                        .build());

        responseObserver.onNext(UpdateCapacityOverrideResult.newBuilder()
                .addAllCapacityOverrides(updateCapacityOverrideResponse.getCapacityOverridesList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void deleteCapacityOverride(
            DeleteCapacityOverrideParams request, StreamObserver<DeleteCapacityOverrideResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var businessId = request.getBusinessId();
        DeleteCapacityOverrideResponse deleteCapacityOverrideResponse =
                availabilitySettingService.deleteCapacityOverride(DeleteCapacityOverrideRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .setId(request.getId())
                        .build());

        responseObserver.onNext(DeleteCapacityOverrideResult.newBuilder()
                .addAllCapacityOverrides(deleteCapacityOverrideResponse.getCapacityOverridesList())
                .build());
        responseObserver.onCompleted();
    }
}
