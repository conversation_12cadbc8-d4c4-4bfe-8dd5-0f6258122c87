package com.moego.api.v3.online_booking.controller;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static com.moego.server.customer.dto.CustomerAddressDto.IsPrimary;

import com.moego.api.v3.online_booking.converter.ClientConverter;
import com.moego.api.v3.online_booking.service.BusinessCustomerService;
import com.moego.idl.api.online_booking.v1.OBAddressServiceGrpc;
import com.moego.idl.api.online_booking.v1.UpsertAddressParams;
import com.moego.idl.api.online_booking.v1.UpsertAddressResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc;
import com.moego.idl.service.business_customer.v1.CreateCustomerAddressRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerAddressRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerPrimaryAddressRequest;
import com.moego.idl.service.business_customer.v1.UpdateCustomerAddressRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.customer.api.ICustomerCustomerService;
import com.moego.server.customer.api.IProfileRequestAddressService;
import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.dto.CustomerInfoDto;
import com.moego.server.customer.dto.ProfileRequestAddressDTO;
import com.moego.server.customer.params.CustomerInfoIdParams;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.enums.OBRequestSubmittedAutoTypeEnum;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
@GrpcService
@RequiredArgsConstructor
public class OBAddressController extends OBAddressServiceGrpc.OBAddressServiceImplBase {

    private final ICustomerCustomerService customerApi;
    private final IGroomingOnlineBookingService onlineBookingApi;
    private final IProfileRequestAddressService profileRequestAddressApi;

    private final BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub
            businessCustomerAddressApi;

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void upsertAddress(UpsertAddressParams request, StreamObserver<UpsertAddressResult> responseObserver) {
        Integer businessId = onlineBookingApi
                .mustGetBusinessDTOByOBNameOrDomain(
                        new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()))
                .getBusinessId();
        Integer customerId = getCustomerId(businessId);

        // 如果 customer 在 B 端没有 primary address，直接新增 address 到 B 端
        if (handleNoPrimaryAddress(request, responseObserver, customerId)) {
            return;
        }

        // 如果 Automation 开启 `Auto-accept all requests and profile updates`，C 端操作直接写入 B 端
        if (handleAutomation(request, responseObserver, businessId, customerId)) {
            return;
        }

        ProfileRequestAddressDTO upsertBean = upsertParamToDTO(request, customerId);

        // 在没有 C address 情况下，如果是修改已有 address，判断是否和已有 address 一样，一样的话直接返回
        if (handleModifyBAddress(responseObserver, upsertBean, customerId)) {
            return;
        }

        // 新增一个 profile request address（C 端）
        // profile request address 只是一个快照，并没有真正写入 customer address（B 端）
        int id = upsertProfileRequestAddress(upsertBean);

        responseObserver.onNext(dtoToUpsertResult(profileRequestAddressApi.get(id)));
        responseObserver.onCompleted();
    }

    private Integer getCustomerId(Integer businessId) {
        Integer customerId = AuthContext.get().getCustomerId();
        if (customerId == null) {
            throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
        }
        CustomerInfoDto customer = customerApi.getCustomerWithDeletedHasCompanyId(
                new CustomerInfoIdParams().setBusinessId(businessId).setCustomerId(customerId));
        if (customer == null) {
            throw bizException(Code.CODE_CUSTOMER_NOT_FOUND_FOR_OB);
        }
        return customerId;
    }

    private boolean handleNoPrimaryAddress(
            UpsertAddressParams params, StreamObserver<UpsertAddressResult> responseObserver, Integer customerId) {
        var request = GetCustomerPrimaryAddressRequest.newBuilder()
                .setCustomerId(customerId)
                .build();
        var response = businessCustomerAddressApi.getCustomerPrimaryAddress(request);

        if (!response.hasAddress()) {
            var result = insertCustomerAddress(params, customerId);
            responseObserver.onNext(result);
            responseObserver.onCompleted();
            return true;
        }
        return false;
    }

    private boolean handleModifyBAddress(
            StreamObserver<UpsertAddressResult> responseObserver,
            ProfileRequestAddressDTO upsertBean,
            Integer customerId) {
        if (upsertBean.getCustomerAddressId() == null) {
            return false;
        }

        var getRequest = GetCustomerAddressRequest.newBuilder()
                .setId(upsertBean.getCustomerAddressId())
                .build();
        var addressModel =
                businessCustomerAddressApi.getCustomerAddress(getRequest).getAddress();

        if (!BusinessCustomerService.hasAddressUpdate(upsertBean, addressModel) && noCAddress(customerId)) {
            var result = ClientConverter.INSTANCE.toUpsertAddressResult(addressModel);
            responseObserver.onNext(result);
            responseObserver.onCompleted();
            return true;
        }
        return false;
    }

    private boolean handleAutomation(
            UpsertAddressParams request,
            StreamObserver<UpsertAddressResult> responseObserver,
            Integer businessId,
            Integer customerId) {
        if (!isAutoAcceptAll(businessId)) {
            return false;
        }

        CustomerAddressDto customerAddress = upsertParamToCustomerAddressDTO(request, customerId);

        // 对于 C 端 address 的操作，直接写入 B 端 address
        Integer bAddressId = !Boolean.TRUE.equals(customerAddress.getIsProfileRequestAddress())
                ? customerAddress.getCustomerAddressId()
                : null;

        var result = bAddressId == null
                ? insertCustomerAddress(request, customerId)
                : updateCustomerAddress(request, bAddressId);

        // 写入 B 端 address 后，删除 C 端 profile request address
        profileRequestAddressApi.deleteByCustomerIds(List.of(customerId));

        responseObserver.onNext(result);
        responseObserver.onCompleted();
        return true;
    }

    private UpsertAddressResult insertCustomerAddress(UpsertAddressParams request, Integer customerId) {
        var createDef = ClientConverter.INSTANCE.toAddressCreateDef(request);
        var createRequest = CreateCustomerAddressRequest.newBuilder()
                .setCustomerId(customerId)
                .setAddress(createDef)
                .build();
        var addressModel =
                businessCustomerAddressApi.createCustomerAddress(createRequest).getAddress();
        return ClientConverter.INSTANCE.toUpsertAddressResult(addressModel);
    }

    private UpsertAddressResult updateCustomerAddress(UpsertAddressParams request, Integer addressId) {
        var updateDef = ClientConverter.INSTANCE.toAddressUpdateDef(request);
        var updateRequest = UpdateCustomerAddressRequest.newBuilder()
                .setId(addressId)
                .setAddress(updateDef)
                .build();
        var updateResponse = businessCustomerAddressApi.updateCustomerAddress(updateRequest);
        return ClientConverter.INSTANCE.toUpsertAddressResult(updateResponse.getAddress());
    }

    private boolean isAutoAcceptAll(Integer businessId) {
        BookOnlineDTO obSetting = Optional.ofNullable(onlineBookingApi.getOBSetting(businessId))
                .orElseThrow(
                        () -> bizException(Code.CODE_PARAMS_ERROR, "OB setting not found, businessId: " + businessId));
        return OBRequestSubmittedAutoTypeEnum.isAutoAcceptConflict(obSetting.getRequestSubmittedAutoType());
    }

    private boolean noCAddress(Integer customerId) {
        Map<Integer, List<ProfileRequestAddressDTO>> customerIdToProfileRequestAddresses =
                profileRequestAddressApi.listByCustomerIds(List.of(customerId));
        return ObjectUtils.isEmpty(customerIdToProfileRequestAddresses.get(customerId));
    }

    private int upsertProfileRequestAddress(ProfileRequestAddressDTO bean) {
        if (bean.getId() == null) {
            return profileRequestAddressApi.insert(bean);
        }
        profileRequestAddressApi.update(bean);
        return bean.getId();
    }

    private static UpsertAddressResult dtoToUpsertResult(ProfileRequestAddressDTO dto) {
        UpsertAddressResult.Builder builder = UpsertAddressResult.newBuilder();
        if (dto.getCustomerId() != null) {
            builder.setCustomerId(dto.getCustomerId());
        }
        if (dto.getAddress1() != null) {
            builder.setAddress1(dto.getAddress1());
        }
        if (dto.getAddress2() != null) {
            builder.setAddress2(dto.getAddress2());
        }
        if (dto.getCity() != null) {
            builder.setCity(dto.getCity());
        }
        if (dto.getCountry() != null) {
            builder.setCountry(dto.getCountry());
        }
        if (dto.getLat() != null) {
            builder.setLat(dto.getLat());
        }
        if (dto.getLng() != null) {
            builder.setLng(dto.getLng());
        }
        if (dto.getState() != null) {
            builder.setState(dto.getState());
        }
        if (dto.getZipcode() != null) {
            builder.setZipcode(dto.getZipcode());
        }
        builder.setIsPrimary(
                Boolean.TRUE.equals(dto.getIsPrimary()) ? IsPrimary.TRUE.getValue() : IsPrimary.FALSE.getValue());

        // 这里前端需要一个 addressId，如果是 C 端新增 address，此时还没有真正添加到 B 端，还没有 addressId。
        // 因此如果是 C 端新增的 address，我们使用 profile request address 的 id 作为 addressId。
        // 并标记这个 address 是 profile request address（不是 B 端的 address）
        if (dto.getCustomerAddressId() == null) {
            builder.setId(dto.getId());
            builder.setIsProfileRequestAddress(true);
        } else {
            builder.setId(dto.getCustomerAddressId());
            builder.setIsProfileRequestAddress(false);
        }
        return builder.build();
    }

    private static ProfileRequestAddressDTO upsertParamToDTO(UpsertAddressParams request, Integer customerId) {
        ProfileRequestAddressDTO profileRequestAddress = new ProfileRequestAddressDTO();
        if (request.hasAddress1()) {
            profileRequestAddress.setAddress1(request.getAddress1());
        }
        if (request.hasAddress2()) {
            profileRequestAddress.setAddress2(request.getAddress2());
        }
        if (request.hasCity()) {
            profileRequestAddress.setCity(request.getCity());
        }
        if (request.hasState()) {
            profileRequestAddress.setState(request.getState());
        }
        if (request.hasZipcode()) {
            profileRequestAddress.setZipcode(request.getZipcode());
        }
        if (request.hasCountry()) {
            profileRequestAddress.setCountry(request.getCountry());
        }
        if (request.hasLat()) {
            profileRequestAddress.setLat(request.getLat());
        }
        if (request.hasLng()) {
            profileRequestAddress.setLng(request.getLng());
        }
        profileRequestAddress.setIsPrimary(
                Objects.equals(request.getIsPrimary(), IsPrimary.TRUE.getValue().intValue()));
        profileRequestAddress.setCustomerId(customerId);
        if (request.hasId()) {
            if (request.getIsProfileRequestAddress()) {
                profileRequestAddress.setId((int) request.getId());
            } else {
                profileRequestAddress.setCustomerAddressId((int) request.getId());
            }
        }
        return profileRequestAddress;
    }

    private static CustomerAddressDto upsertParamToCustomerAddressDTO(UpsertAddressParams request, Integer customerId) {
        CustomerAddressDto customerAddress = new CustomerAddressDto();
        if (request.hasId()) {
            customerAddress.setCustomerAddressId((int) request.getId());
        }
        if (request.hasAddress1()) {
            customerAddress.setAddress1(request.getAddress1());
        }
        if (request.hasAddress2()) {
            customerAddress.setAddress2(request.getAddress2());
        }
        if (request.hasCity()) {
            customerAddress.setCity(request.getCity());
        }
        if (request.hasCountry()) {
            customerAddress.setCountry(request.getCountry());
        }
        if (request.hasLat()) {
            customerAddress.setLat(request.getLat());
        }
        if (request.hasLng()) {
            customerAddress.setLng(request.getLng());
        }
        if (request.hasState()) {
            customerAddress.setState(request.getState());
        }
        if (request.hasZipcode()) {
            customerAddress.setZipcode(request.getZipcode());
        }
        if (request.hasIsPrimary()) {
            customerAddress.setIsPrimary((byte) request.getIsPrimary());
        }
        customerAddress.setIsProfileRequestAddress(request.getIsProfileRequestAddress());
        customerAddress.setCustomerId(customerId);
        return customerAddress;
    }
}
