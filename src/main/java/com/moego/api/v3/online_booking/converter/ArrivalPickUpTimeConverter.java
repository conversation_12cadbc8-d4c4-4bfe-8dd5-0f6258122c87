package com.moego.api.v3.online_booking.converter;

import com.moego.idl.api.online_booking.v1.ArrivalPickUpTimeOverrideView;
import com.moego.idl.api.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideParams;
import com.moego.idl.api.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideParams;
import com.moego.idl.models.online_booking.v1.ArrivalPickUpTimeOverrideModel;
import com.moego.idl.models.online_booking.v1.TimeRangeType;
import com.moego.idl.service.online_booking.v1.BatchCreateArrivalPickUpTimeOverrideRequest;
import com.moego.idl.service.online_booking.v1.BatchUpdateArrivalPickUpTimeOverrideRequest;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Mapper()
public interface ArrivalPickUpTimeConverter {

    ArrivalPickUpTimeConverter INSTANCE = Mappers.getMapper(ArrivalPickUpTimeConverter.class);

    default BatchCreateArrivalPickUpTimeOverrideRequest.CreateDef buildCreateArrivalPickUpTimeOverrideRequest(
            BatchCreateArrivalPickUpTimeOverrideParams.CreateDef params, TimeRangeType type) {
        return BatchCreateArrivalPickUpTimeOverrideRequest.CreateDef.newBuilder()
                .setType(type)
                .setStartDate(params.getStartDate())
                .setEndDate(params.getEndDate())
                .setIsAvailable(params.getIsAvailable())
                .addAllDayTimeRanges(params.getDayTimeRangesList())
                .build();
    }

    default List<BatchCreateArrivalPickUpTimeOverrideRequest.CreateDef> buildCreateArrivalPickUpTimeOverrideRequest(
            List<BatchCreateArrivalPickUpTimeOverrideParams.CreateDef> params, TimeRangeType type) {
        if (CollectionUtils.isEmpty(params)) {
            return List.of();
        }
        return params.stream()
                .map(k -> buildCreateArrivalPickUpTimeOverrideRequest(k, type))
                .toList();
    }

    default ArrivalPickUpTimeOverrideView buildArrivalPickUpTimeOverrideView(ArrivalPickUpTimeOverrideModel model) {
        return ArrivalPickUpTimeOverrideView.newBuilder()
                .setId(model.getId())
                .setStartDate(model.getStartDate())
                .setEndDate(model.getEndDate())
                .setIsAvailable(model.getIsAvailable())
                .addAllDayTimeRanges(model.getDayTimeRangesList())
                .setIsActive(model.getIsActive())
                .build();
    }

    default List<ArrivalPickUpTimeOverrideView> buildArrivalPickUpTimeOverrideView(
            List<ArrivalPickUpTimeOverrideModel> models) {
        if (CollectionUtils.isEmpty(models)) {
            return List.of();
        }
        return models.stream().map(this::buildArrivalPickUpTimeOverrideView).toList();
    }

    default BatchUpdateArrivalPickUpTimeOverrideRequest.UpdateDef buildUpdateArrivalPickUpTimeOverrideRequest(
            BatchUpdateArrivalPickUpTimeOverrideParams.UpdateDef params) {
        var builder = BatchUpdateArrivalPickUpTimeOverrideRequest.UpdateDef.newBuilder()
                .setId(params.getId());
        if (params.hasStartDate()) {
            builder.setStartDate(params.getStartDate());
        }
        if (params.hasEndDate()) {
            builder.setEndDate(params.getEndDate());
        }
        if (params.hasIsAvailable()) {
            builder.setIsAvailable(params.getIsAvailable());
        }
        if (params.hasDayTimeRanges()) {
            builder.setDayTimeRanges(params.getDayTimeRanges());
        }
        return builder.build();
    }

    default List<BatchUpdateArrivalPickUpTimeOverrideRequest.UpdateDef> buildUpdateArrivalPickUpTimeOverrideRequest(
            List<BatchUpdateArrivalPickUpTimeOverrideParams.UpdateDef> params) {
        if (CollectionUtils.isEmpty(params)) {
            return List.of();
        }
        return params.stream()
                .map(this::buildUpdateArrivalPickUpTimeOverrideRequest)
                .toList();
    }
}
