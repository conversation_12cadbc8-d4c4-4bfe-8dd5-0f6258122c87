package com.moego.api.v3.online_booking.controller;

import static com.moego.idl.api.online_booking.v1.OBLoginByVerificationCodeDef.IdentifierCase.PHONE_NUMBER;

import com.moego.api.v3.online_booking.service.BusinessCustomerService;
import com.moego.api.v3.online_booking.service.BusinessService;
import com.moego.api.v3.online_booking.service.ImpersonateService;
import com.moego.api.v3.online_booking.service.OBLoginService;
import com.moego.common.enums.BusinessConst;
import com.moego.idl.api.online_booking.v1.OBAccessServiceGrpc;
import com.moego.idl.api.online_booking.v1.OBCheckIdentifierRequest;
import com.moego.idl.api.online_booking.v1.OBCheckIdentifierResponse;
import com.moego.idl.api.online_booking.v1.OBDevModeRequest;
import com.moego.idl.api.online_booking.v1.OBDevModeResponse;
import com.moego.idl.api.online_booking.v1.OBGetVerificationSettingRequest;
import com.moego.idl.api.online_booking.v1.OBGetVerificationSettingResponse;
import com.moego.idl.api.online_booking.v1.OBLoginAdsDataDef;
import com.moego.idl.api.online_booking.v1.OBLoginByVerificationCodeDef;
import com.moego.idl.api.online_booking.v1.OBLoginRequest;
import com.moego.idl.api.online_booking.v1.OBLoginResponse;
import com.moego.idl.api.online_booking.v1.OBLogoutRequest;
import com.moego.idl.api.online_booking.v1.OBLogoutResponse;
import com.moego.idl.api.online_booking.v1.OBSendVerificationCodeRequest;
import com.moego.idl.api.online_booking.v1.OBSendVerificationCodeResponse;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.metadata.v1.OwnerType;
import com.moego.idl.models.online_booking.v1.AccessType;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.models.risk_control.v1.RecaptchaAction;
import com.moego.idl.models.risk_control.v1.RecaptchaVersion;
import com.moego.idl.service.metadata.v1.ExtractValuesRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.risk.control.recaptcha.Recaptcha;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.dto.LeadLinkAdsDataDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.dto.OBMainSessionDTO;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.message.dto.VerifyCodeDTO;
import com.moego.server.message.enums.VerificationCodeScenarioEnum;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/9/4
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class OBAccessController extends OBAccessServiceGrpc.OBAccessServiceImplBase {

    private final IGroomingOnlineBookingClient onlineBookingClient;
    private final ICustomerCustomerClient customerClient;
    private final BusinessService businessService;
    private final OBLoginService obLoginService;
    private final ImpersonateService impersonateService;
    private final BusinessCustomerService businessCustomerService;
    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void checkIdentifier(
            OBCheckIdentifierRequest request, StreamObserver<OBCheckIdentifierResponse> responseObserver) {

        var businessDTO = onlineBookingClient.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));
        var companyId = businessDTO.getCompanyId();

        // check if the identifier exists
        var tenant = Tenant.newBuilder().setCompanyId(companyId).build();
        var exist =
                switch (request.getIdentifierCase()) {
                    case PHONE_NUMBER -> businessCustomerService.checkCustomerPhoneNumberExist(
                            tenant, request.getPhoneNumber());
                    case EMAIL -> businessCustomerService.checkCustomerEmailExist(tenant, request.getEmail());
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
                };

        // if exists, no need to search possible clients
        if (exist) {
            var response = OBCheckIdentifierResponse.newBuilder().setExist(true).build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
            return;
        }

        // if not exists, and not need to search possible clients
        // 前端传了 includePossibleClients 为 true，且白名单开关打开，才需要搜 possible clients
        var enablePossibleClient =
                request.getIncludePossibleClients() && enablePossibleClientWhiteList(businessDTO.getBusinessId());
        if (!enablePossibleClient) {
            var response =
                    OBCheckIdentifierResponse.newBuilder().setExist(false).build();
            responseObserver.onNext(response);
            responseObserver.onCompleted();
            return;
        }

        // search possible clients
        var possibleClients =
                switch (request.getIdentifierCase()) {
                    case PHONE_NUMBER -> businessCustomerService.searchPossibleCustomer(
                            tenant, request.getPhoneNumber());
                        // TODO: 通过 email 搜 possible clients 暂时没有业务需要, 后续有需要再加, 这里先返回空 list
                    case EMAIL -> new ArrayList<OBCheckIdentifierResponse.PossibleClient>();
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
                };

        var response = OBCheckIdentifierResponse.newBuilder()
                .setExist(false)
                .addAllPossibleClients(possibleClients)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void getVerificationSetting(
            OBGetVerificationSettingRequest request,
            StreamObserver<OBGetVerificationSettingResponse> responseObserver) {
        var businessDTO = onlineBookingClient.mustGetBusinessDTOByOBNameOrDomain(
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));

        MoeBusinessDto businessDto = businessService.getBusinessInfo(businessDTO.getBusinessId());

        var response = OBGetVerificationSettingResponse.newBuilder()
                .setExistingClientVerificationCode(isUS(businessDto))
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Recaptcha(
            action = RecaptchaAction.RECAPTCHA_ACTION_OB_V3_SEND_CODE,
            applicableVersions = {RecaptchaVersion.RECAPTCHA_VERSION_V2, RecaptchaVersion.RECAPTCHA_VERSION_V3})
    @Auth(AuthType.ANONYMOUS)
    public void sendVerificationCode(
            OBSendVerificationCodeRequest request, StreamObserver<OBSendVerificationCodeResponse> responseObserver) {
        if (StringUtils.hasText(AuthContext.get().impersonator())) {
            impersonateService.sendVerificationCode(request, responseObserver);
            log.info(
                    "send verification code by impersonator, sessionId: [{}]",
                    AuthContext.get().sessionId());
            return;
        }
        Integer businessId = onlineBookingClient
                .mustGetBusinessDTOByOBNameOrDomain(
                        new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()))
                .getBusinessId();
        MoeBusinessDto businessDto = businessService.getBusinessInfo(businessId);

        boolean success = false;
        var token =
                switch (request.getIdentifierCase()) {
                    case ADDITIONAL_CONTACT -> {
                        var additionalContact = request.getAdditionalContact();
                        var customer = businessCustomerService.getCustomerInfoByAdditionalContact(additionalContact);

                        if (request.getAccessType() == AccessType.ACCESS_TYPE_BY_PHONE) {
                            if (isUS(businessDto)) {
                                var customerDTO = new MoeBusinessCustomerDTO();
                                customerDTO.setId((int) customer.getId());
                                customerDTO.setCustomerId((int) customer.getId());
                                customerDTO.setFirstName(customer.getFirstName());
                                customerDTO.setLastName(customer.getLastName());
                                success = true;
                                yield obLoginService.sendPhoneVerificationCode(
                                        businessDto, customerDTO, additionalContact.getPhoneNumber());
                            } else {
                                yield obLoginService.generatePhoneToken(
                                        businessDto, additionalContact.getPhoneNumber());
                            }
                        }

                        // TODO: 暂时不支持 email 验证码, 后面还有需要再加
                        throw ExceptionUtil.bizException(
                                Code.CODE_PARAMS_ERROR, "Unsupported. Please contact your service provider");
                    }
                    case POSSIBLE_CLIENT_ID -> {
                        var customerId = request.getPossibleClientId();
                        var customer = businessCustomerService.getCustomerInfo(customerId);
                        if (customer.getDeleted()) {
                            throw ExceptionUtil.bizException(Code.CODE_CUSTOMER_NOT_FOUND);
                        }

                        switch (request.getAccessType()) {
                            case ACCESS_TYPE_BY_PHONE -> {
                                var phoneNumber = customer.getPhoneNumber();
                                if (!StringUtils.hasText(phoneNumber)) {
                                    throw ExceptionUtil.bizException(
                                            Code.CODE_PARAMS_ERROR,
                                            "Phone number is empty in %s %s's profile"
                                                    .formatted(customer.getFirstName(), customer.getLastName()));
                                }

                                if (isUS(businessDto)) {
                                    var customerDTO = new MoeBusinessCustomerDTO();
                                    customerDTO.setId((int) customerId);
                                    customerDTO.setCustomerId((int) customerId);
                                    customerDTO.setFirstName(customer.getFirstName());
                                    customerDTO.setLastName(customer.getLastName());
                                    success = true;
                                    yield obLoginService.sendPhoneVerificationCode(
                                            businessDto, customerDTO, phoneNumber);
                                } else {
                                    yield obLoginService.generatePhoneToken(businessDto, phoneNumber);
                                }
                            }
                                // TODO: 暂时不支持 email 验证码, 后面还有需要再加
                            default -> throw ExceptionUtil.bizException(
                                    Code.CODE_PARAMS_ERROR, "Unsupported. Please contact your service provider");
                        }
                    }
                    case PHONE_NUMBER -> {
                        switch (request.getAccessType()) {
                            case ACCESS_TYPE_BY_PHONE -> {
                                String phoneNumber = request.getPhoneNumber();
                                MoeBusinessCustomerDTO customerDTO =
                                        customerClient.getCustomerInfoByPhoneNumberForNewOB(businessId, phoneNumber);
                                // 仅 US existing client 发送 phone 验证码，其他情况不发送但需要返回 token 用于后续验证
                                if (isUS(businessDto) && isExistingClient(customerDTO)) {
                                    success = true;
                                    yield obLoginService.sendPhoneVerificationCode(
                                            businessDto, customerDTO, phoneNumber);
                                } else {
                                    yield obLoginService.generatePhoneToken(businessDto, phoneNumber);
                                }
                            }
                            case ACCESS_TYPE_BY_EMAIL -> {
                                String ownerPhoneNumber = request.getPhoneNumber();
                                MoeBusinessCustomerDTO customerDTO =
                                        customerClient.getCustomerInfoByPhoneNumberForNewOB(
                                                businessId, ownerPhoneNumber);
                                // 仅白名单的 existing client 发送 email 验证码，其他情况均抛出异常
                                if (Objects.isNull(customerDTO) || !StringUtils.hasText(customerDTO.getEmail())) {
                                    log.error("business: [{}], invalid phone: [{}]", businessId, ownerPhoneNumber);
                                    throw ExceptionUtil.bizException(Code.CODE_FAIL_TO_SEND_CODE);
                                }
                                success = true;
                                yield obLoginService.sendEmailVerificationCode(businessDto, customerDTO);
                            }
                            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
                        }
                    }
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
                };

        var response = OBSendVerificationCodeResponse.newBuilder()
                .setToken(token)
                .setSuccess(success)
                .build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void login(OBLoginRequest request, StreamObserver<OBLoginResponse> responseObserver) {
        if (StringUtils.hasText(AuthContext.get().impersonator())) {
            log.info("login by impersonator, sessionId: [{}]", AuthContext.get().sessionId());
        }
        // check params
        OBAnonymousParams anonymousParams =
                new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName());
        OBMainSessionDTO obMainSessionDTO = obLoginService.checkOBSession(anonymousParams);
        Integer businessId = onlineBookingClient
                .mustGetBusinessDTOByOBNameOrDomain(anonymousParams)
                .getBusinessId();
        MoeBusinessDto businessDto = businessService.getBusinessInfo(businessId);

        switch (request.getLoginMethodCase()) {
            case BY_VERIFICATION_CODE -> {
                var verificationCodeDef = request.getByVerificationCode();
                switch (verificationCodeDef.getIdentifierCase()) {
                    case POSSIBLE_CLIENT_ID -> verifyTokenByPossibleClientId(
                            obMainSessionDTO, businessDto, verificationCodeDef);
                    case ADDITIONAL_CONTACT -> verifyTokenByAdditionalContact(
                            obMainSessionDTO, businessDto, verificationCodeDef);
                    case PHONE_NUMBER -> verifyTokenByPhoneNumber(obMainSessionDTO, businessDto, verificationCodeDef);
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
                }
            }
            case BY_PPP -> obLoginService.loginWithToken(
                    obMainSessionDTO, request.getByPpp().getToken());
            case LOGINMETHOD_NOT_SET -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }

        // collection ads
        try {
            collectAdsData(businessDto, request.getAdsData(), request.getByVerificationCode());
        } catch (Exception e) {
            // 非核心逻辑，不影响主流程
            log.error("OB login collectAdsData exception", e);
        }

        responseObserver.onNext(OBLoginResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private void collectAdsData(
            MoeBusinessDto businessDto, OBLoginAdsDataDef adsData, OBLoginByVerificationCodeDef byVerificationCode) {
        // check
        if (businessDto == null
                || businessDto.getId() == 0
                || adsData == null
                || !StringUtils.hasText(adsData.getGoogleAdsStr())
                || byVerificationCode == null
                || !StringUtils.hasText(byVerificationCode.getPhoneNumber())) {
            return;
        }

        // add ads data
        var customerDTO = customerClient.getCustomerInfoByPhoneNumberForNewOB(
                businessDto.getId(), byVerificationCode.getPhoneNumber());
        if (isExistingClient(customerDTO)) {
            customerClient.leadLinkAds(LeadLinkAdsDataDTO.builder()
                    .businessId(businessDto.getId())
                    .itemType(1)
                    .itemID(customerDTO.getCustomerId().toString())
                    .adsDataType(1)
                    .adsData(adsData.getGoogleAdsStr())
                    .build());
        } else {
            customerClient.leadLinkAds(LeadLinkAdsDataDTO.builder()
                    .businessId(businessDto.getId())
                    .itemType(2)
                    .itemID(byVerificationCode.getPhoneNumber())
                    .adsDataType(1)
                    .adsData(adsData.getGoogleAdsStr())
                    .build());
        }
    }

    private void verifyTokenByPossibleClientId(
            OBMainSessionDTO obMainSessionDTO, MoeBusinessDto businessDto, OBLoginByVerificationCodeDef def) {
        var customerId = def.getPossibleClientId();
        var customer = businessCustomerService.getCustomerInfo(customerId);
        if (customer.getDeleted()) {
            throw ExceptionUtil.bizException(Code.CODE_CUSTOMER_NOT_FOUND);
        }

        switch (def.getAccessType()) {
            case ACCESS_TYPE_BY_PHONE -> {
                var phoneNumber = customer.getPhoneNumber();
                if (!StringUtils.hasText(phoneNumber)) {
                    throw ExceptionUtil.bizException(
                            Code.CODE_PARAMS_ERROR,
                            "Phone number is empty in %s %s's profile"
                                    .formatted(customer.getFirstName(), customer.getLastName()));
                }

                if (isUS(businessDto)) {
                    VerifyCodeDTO verifyCodeDTO = new VerifyCodeDTO()
                            .setScenario(VerificationCodeScenarioEnum.OB_LOGIN)
                            .setBusinessId(businessDto.getId())
                            .setCustomerId((int) customerId)
                            .setAccount(phoneNumber)
                            .setCode(def.getCode())
                            .setToken(def.getToken());
                    obLoginService.verifyPhoneCode(verifyCodeDTO);
                } else {
                    VerifyCodeDTO verifyCodeDTO = new VerifyCodeDTO()
                            .setScenario(VerificationCodeScenarioEnum.OB_LOGIN)
                            .setBusinessId(businessDto.getId())
                            .setAccount(phoneNumber)
                            .setToken(def.getToken());
                    obLoginService.verifyPhoneToken(verifyCodeDTO);
                }

                // login by customer id
                obLoginService.loginWithCustomerId(obMainSessionDTO, (int) customerId);
            }
            default -> throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Unsupported. Please contact your service provider");
        }
    }

    private void verifyTokenByAdditionalContact(
            OBMainSessionDTO obMainSessionDTO, MoeBusinessDto businessDto, OBLoginByVerificationCodeDef def) {
        var additionalContact = def.getAdditionalContact();
        var customer = businessCustomerService.getCustomerInfoByAdditionalContact(additionalContact);

        if (def.getAccessType() == AccessType.ACCESS_TYPE_BY_PHONE) {
            var verifyCodeDTO = new VerifyCodeDTO()
                    .setScenario(VerificationCodeScenarioEnum.OB_LOGIN)
                    .setBusinessId(businessDto.getId())
                    .setAccount(additionalContact.getPhoneNumber())
                    .setCode(def.getCode())
                    .setToken(def.getToken());

            if (isUS(businessDto)) {
                verifyCodeDTO.setCustomerId((int) customer.getId()).setCode(def.getCode());
                obLoginService.verifyPhoneCode(verifyCodeDTO);
            } else {
                obLoginService.verifyPhoneToken(verifyCodeDTO);
            }

            // login by customer id
            obLoginService.loginWithCustomerId(obMainSessionDTO, (int) customer.getId());
        } else {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Unsupported. Please contact your service provider");
        }
    }

    private void verifyTokenByPhoneNumber(
            OBMainSessionDTO obMainSessionDTO, MoeBusinessDto businessDto, OBLoginByVerificationCodeDef def) {
        var businessId = businessDto.getId();
        switch (def.getAccessType()) {
            case ACCESS_TYPE_BY_PHONE -> {
                String phoneNumber = def.getPhoneNumber();
                // 非 US existing client 不需要验证码
                var customerDTO = customerClient.getCustomerInfoByPhoneNumberForNewOB(businessId, phoneNumber);
                // verify phone code and token
                if (isUS(businessDto) && isExistingClient(customerDTO)) {
                    VerifyCodeDTO verifyCodeDTO = new VerifyCodeDTO()
                            .setScenario(VerificationCodeScenarioEnum.OB_LOGIN)
                            .setBusinessId(businessId)
                            .setCustomerId(customerDTO.getCustomerId())
                            .setAccount(phoneNumber)
                            .setCode(def.getCode())
                            .setToken(def.getToken());
                    obLoginService.verifyPhoneCode(verifyCodeDTO);
                } else {
                    VerifyCodeDTO verifyCodeDTO = new VerifyCodeDTO()
                            .setScenario(VerificationCodeScenarioEnum.OB_LOGIN)
                            .setBusinessId(businessId)
                            .setAccount(phoneNumber)
                            .setToken(def.getToken());
                    obLoginService.verifyPhoneToken(verifyCodeDTO);
                }

                if (isExistingClient(customerDTO)) {
                    // login by customer id
                    obLoginService.loginWithCustomerId(obMainSessionDTO, customerDTO.getCustomerId());
                } else {
                    // login by phone
                    obLoginService.loginWithPhoneNumber(obMainSessionDTO, phoneNumber);
                }
            }
            case ACCESS_TYPE_BY_EMAIL -> {
                String phoneNumber = def.getPhoneNumber();
                MoeBusinessCustomerDTO customerDTO =
                        customerClient.getCustomerInfoByPhoneNumberForNewOB(businessId, phoneNumber);
                VerifyCodeDTO verifyCodeDTO = new VerifyCodeDTO()
                        .setScenario(VerificationCodeScenarioEnum.OB_LOGIN)
                        .setAccount(customerDTO.getEmail())
                        .setCode(def.getCode())
                        .setToken(def.getToken());
                obLoginService.verifyEmailCode(verifyCodeDTO);
                // login
                obLoginService.loginWithCustomerId(obMainSessionDTO, customerDTO.getCustomerId());
            }
            case ACCESS_TYPE_UNSPECIFIED -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void logout(OBLogoutRequest request, StreamObserver<OBLogoutResponse> responseObserver) {
        var context = AuthContext.get();
        // 有会话则登出；无会话则幂等返回
        if (context.sessionId() != null) {
            var obMainSession = obLoginService.checkOBSession(
                    new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()));
            obLoginService.logoutOBSubSession(obMainSession.mainSessionId(), obMainSession.obName());
        }
        responseObserver.onNext(OBLogoutResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void getDevMode(OBDevModeRequest request, StreamObserver<OBDevModeResponse> responseObserver) {
        OBDevModeResponse devModeResponse = OBDevModeResponse.newBuilder()
                .setDevMode(StringUtils.hasText(AuthContext.get().impersonator()))
                .build();
        responseObserver.onNext(devModeResponse);
        responseObserver.onCompleted();
    }

    private boolean isUS(MoeBusinessDto businessDto) {
        return businessDto.getCountry().equals(BusinessConst.COUNTRY_US)
                || businessDto.getCountry().equals(BusinessConst.COUNTRY_US2);
    }

    private boolean isExistingClient(MoeBusinessCustomerDTO customerDTO) {
        return Objects.nonNull(customerDTO) && Objects.nonNull(customerDTO.getCustomerId());
    }

    public boolean enablePossibleClientWhiteList(long businessId) {
        try {
            String keyName = "enable_ob_possible_client";

            var request = ExtractValuesRequest.newBuilder()
                    .setKeyName(keyName)
                    .putOwners(OwnerType.OWNER_TYPE_BUSINESS_VALUE, businessId)
                    .build();

            return metadataServiceBlockingStub
                    .extractValues(request)
                    .getValuesMap()
                    .getOrDefault(keyName, "false")
                    .equals("true");
        } catch (Exception e) {
            return false;
        }
    }
}
