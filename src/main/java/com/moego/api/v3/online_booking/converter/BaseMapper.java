package com.moego.api.v3.online_booking.converter;

import com.google.type.Date;
import com.moego.lib.common.util.JsonUtil;
import java.time.LocalDate;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/8/16
 */
@Mapper
public interface BaseMapper {

    @Named("string2List")
    default List<Integer> string2List(String str) {
        return StringUtils.hasText(str) ? JsonUtil.toList(str, Integer.class) : List.of();
    }

    @Named("string2Array")
    default Integer[] string2Array(String str) {
        return StringUtils.hasText(str)
                ? JsonUtil.toList(str, Integer.class).toArray(new Integer[] {})
                : new Integer[0];
    }

    @Named("stringToDate")
    default Date stringToDate(String value) {
        if (!StringUtils.hasText(value)) {
            return Date.getDefaultInstance();
        }
        LocalDate localDate = LocalDate.parse(value);
        return Date.newBuilder()
                .setYear(localDate.getYear())
                .setMonth(localDate.getMonthValue())
                .setDay(localDate.getDayOfMonth())
                .build();
    }
}
