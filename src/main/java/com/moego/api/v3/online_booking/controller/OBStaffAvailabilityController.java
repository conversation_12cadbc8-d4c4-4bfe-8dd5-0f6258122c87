package com.moego.api.v3.online_booking.controller;

import com.moego.api.v3.shared.helper.BusinessHelper;
import com.moego.idl.api.online_booking.v1.GetStaffAvailabilityParams;
import com.moego.idl.api.online_booking.v1.GetStaffAvailabilityResult;
import com.moego.idl.api.online_booking.v1.ListSlotFreeServicesParams;
import com.moego.idl.api.online_booking.v1.ListSlotFreeServicesResult;
import com.moego.idl.api.online_booking.v1.OBStaffAvailabilityServiceGrpc;
import com.moego.idl.api.online_booking.v1.UpdateSlotFreeServicesParams;
import com.moego.idl.api.online_booking.v1.UpdateSlotFreeServicesResult;
import com.moego.idl.api.online_booking.v1.UpdateStaffAvailabilityParams;
import com.moego.idl.api.online_booking.v1.UpdateStaffAvailabilityResult;
import com.moego.idl.service.online_booking.v1.GetStaffAvailabilityRequest;
import com.moego.idl.service.online_booking.v1.GetStaffAvailabilityResponse;
import com.moego.idl.service.online_booking.v1.ListSlotFreeServicesRequest;
import com.moego.idl.service.online_booking.v1.OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceBlockingStub;
import com.moego.idl.service.online_booking.v1.UpdateSlotFreeServicesRequest;
import com.moego.idl.service.online_booking.v1.UpdateStaffAvailabilityRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class OBStaffAvailabilityController extends OBStaffAvailabilityServiceGrpc.OBStaffAvailabilityServiceImplBase {
    private final OBStaffAvailabilityServiceBlockingStub obStaffAvailabilityServiceBlockingStub;
    private final BusinessHelper businessHelper;

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void getStaffAvailability(
            GetStaffAvailabilityParams request, StreamObserver<GetStaffAvailabilityResult> responseObserver) {
        var builder = GetStaffAvailabilityRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .addAllStaffIdList(request.getStaffIdListList());
        if (request.hasAvailabilityType()) {
            builder.setAvailabilityType(request.getAvailabilityType());
        }
        GetStaffAvailabilityResponse staffAvailability =
                obStaffAvailabilityServiceBlockingStub.getStaffAvailability(builder.build());

        responseObserver.onNext(GetStaffAvailabilityResult.newBuilder()
                .addAllStaffAvailabilityList(staffAvailability.getStaffAvailabilityListList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void updateStaffAvailability(
            UpdateStaffAvailabilityParams request, StreamObserver<UpdateStaffAvailabilityResult> responseObserver) {
        obStaffAvailabilityServiceBlockingStub.updateStaffAvailability(UpdateStaffAvailabilityRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .addAllStaffAvailabilityList(request.getStaffAvailabilityListList())
                .build());

        responseObserver.onNext(UpdateStaffAvailabilityResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void listSlotFreeServices(
            final ListSlotFreeServicesParams request,
            final StreamObserver<ListSlotFreeServicesResult> responseObserver) {
        var companyId = AuthContext.get().companyId();

        // check business
        businessHelper.checkBusinessCompany(companyId, request.getBusinessId());

        var response =
                obStaffAvailabilityServiceBlockingStub.listSlotFreeServices(ListSlotFreeServicesRequest.newBuilder()
                        .setBusinessId(request.getBusinessId())
                        .addAllStaffIds(request.getStaffIdsList())
                        .build());

        responseObserver.onNext(ListSlotFreeServicesResult.newBuilder()
                .addAllDefs(response.getDefsList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void updateSlotFreeServices(
            final UpdateSlotFreeServicesParams request,
            final StreamObserver<UpdateSlotFreeServicesResult> responseObserver) {
        var companyId = AuthContext.get().companyId();

        // check business
        businessHelper.checkBusinessCompany(companyId, request.getBusinessId());

        obStaffAvailabilityServiceBlockingStub.updateSlotFreeServices(UpdateSlotFreeServicesRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(request.getBusinessId())
                .addAllDefs(request.getDefsList())
                .build());

        responseObserver.onNext(UpdateSlotFreeServicesResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
