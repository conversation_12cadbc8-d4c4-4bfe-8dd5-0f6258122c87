package com.moego.api.v3.online_booking.service;

import com.moego.idl.api.online_booking.v1.OBSendVerificationCodeRequest;
import com.moego.idl.api.online_booking.v1.OBSendVerificationCodeResponse;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/9/15
 */
@Service
@RequiredArgsConstructor
public class ImpersonateService {

    private final IGroomingOnlineBookingClient onlineBookingClient;
    private final BusinessCustomerService businessCustomerService;
    private final BusinessService businessService;
    private final OBLoginService obLoginService;

    public void sendVerificationCode(
            OBSendVerificationCodeRequest request, StreamObserver<OBSendVerificationCodeResponse> responseObserver) {

        Integer businessId = onlineBookingClient
                .mustGetBusinessDTOByOBNameOrDomain(
                        new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()))
                .getBusinessId();
        MoeBusinessDto businessDto = businessService.getBusinessInfo(businessId);

        var token =
                switch (request.getIdentifierCase()) {
                    case POSSIBLE_CLIENT_ID -> {
                        var customerId = request.getPossibleClientId();
                        var customer = businessCustomerService.getCustomerInfo(customerId);
                        if (customer.getDeleted()) {
                            throw ExceptionUtil.bizException(Code.CODE_CUSTOMER_NOT_FOUND);
                        }

                        var phoneNumber = customer.getPhoneNumber();
                        if (!StringUtils.hasText(phoneNumber)) {
                            throw ExceptionUtil.bizException(
                                    Code.CODE_PARAMS_ERROR,
                                    "Phone number is empty in %s %s's profile"
                                            .formatted(customer.getFirstName(), customer.getLastName()));
                        }
                        yield obLoginService.generatePhoneToken(businessDto, phoneNumber);
                    }
                    case PHONE_NUMBER -> obLoginService.generatePhoneToken(businessDto, request.getPhoneNumber());
                    case ADDITIONAL_CONTACT -> obLoginService.generatePhoneToken(
                            businessDto, request.getAdditionalContact().getPhoneNumber());
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
                };

        responseObserver.onNext(
                OBSendVerificationCodeResponse.newBuilder().setToken(token).build());
        responseObserver.onCompleted();
    }
}
