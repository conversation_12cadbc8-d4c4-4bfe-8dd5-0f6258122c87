package com.moego.api.v3.online_booking.converter;

import com.moego.idl.api.online_booking.v1.QuestionAnswerDetail;
import com.moego.server.grooming.dto.GroomingQuestionDTO;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/7/11
 */
@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface QuestionConverter {
    QuestionConverter INSTANCE = Mappers.getMapper(QuestionConverter.class);

    default Map<String, String> mergeCustomQuestion(
            Map<String, Object> oldCustomQuestionMap, Map<String, Object> newCustomQuestionMap) {
        if (CollectionUtils.isEmpty(newCustomQuestionMap)) {
            return obj2string(oldCustomQuestionMap);
        }
        if (CollectionUtils.isEmpty(oldCustomQuestionMap)) {
            return obj2string(newCustomQuestionMap);
        }
        Map<String, Object> result = new HashMap<>();
        oldCustomQuestionMap.forEach(
                (key, oldAnswer) -> result.put(key, replaceConflict(newCustomQuestionMap.get(key), oldAnswer)));
        newCustomQuestionMap.forEach((key, newAnswer) -> {
            if (!oldCustomQuestionMap.containsKey(key)) {
                result.put(key, newAnswer);
            }
        });
        return obj2string(result);
    }

    default Object replaceConflict(Object request, Object existing) {
        return Objects.nonNull(request) && StringUtils.hasText(request.toString()) ? request : existing;
    }

    default Map<String, String> obj2string(Map<String, Object> source) {
        if (CollectionUtils.isEmpty(source)) {
            return Map.of();
        }
        return source.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, entry -> {
            if (Objects.isNull(entry.getValue())) {
                return "";
            }
            return entry.getValue().toString();
        }));
    }

    default Map<String, Object> string2obj(Map<String, String> source) {
        if (CollectionUtils.isEmpty(source)) {
            return Map.of();
        }
        return source.entrySet().stream().collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
    }

    default List<QuestionAnswerDetail> entity2QuestionAnswer(
            Map<String, Object> customQuestions, Map<String, GroomingQuestionDTO> questionMap) {
        if (CollectionUtils.isEmpty(customQuestions)) {
            return List.of();
        }
        return customQuestions.entrySet().stream()
                .map(entry -> {
                    GroomingQuestionDTO question = questionMap.get(entry.getKey());
                    if (Objects.isNull(question)) {
                        return null;
                    }
                    return QuestionAnswerDetail.newBuilder()
                            .setKey(entry.getKey())
                            .setQuestion(question.getQuestion())
                            .setAnswer(String.valueOf(entry.getValue()))
                            .build();
                })
                .filter(Objects::nonNull)
                .toList();
    }
}
