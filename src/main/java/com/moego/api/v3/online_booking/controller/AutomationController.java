package com.moego.api.v3.online_booking.controller;

import com.moego.idl.api.online_booking.v1.AutomationServiceGrpc;
import com.moego.idl.api.online_booking.v1.GetAutomationSettingParams;
import com.moego.idl.api.online_booking.v1.GetAutomationSettingResult;
import com.moego.idl.api.online_booking.v1.UpdateAutomationSettingParams;
import com.moego.idl.api.online_booking.v1.UpdateAutomationSettingResult;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.service.online_booking.v1.GetAutomationSettingRequest;
import com.moego.idl.service.online_booking.v1.UpdateAutomationSettingRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class AutomationController extends AutomationServiceGrpc.AutomationServiceImplBase {

    private final com.moego.idl.service.online_booking.v1.AutomationServiceGrpc.AutomationServiceBlockingStub
            automationService;

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void getAutomationSetting(
            GetAutomationSettingParams request, StreamObserver<GetAutomationSettingResult> responseObserver) {
        if (request.getServiceItemType() == ServiceItemType.UNRECOGNIZED) {
            responseObserver.onNext(GetAutomationSettingResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        var response = automationService.getAutomationSetting(GetAutomationSettingRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .setServiceItemType(request.getServiceItemType())
                .build());

        responseObserver.onNext(GetAutomationSettingResult.newBuilder()
                .setAutomationSetting(response.getAutomationSetting())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void updateAutomationSetting(
            UpdateAutomationSettingParams request, StreamObserver<UpdateAutomationSettingResult> responseObserver) {
        var response = automationService.updateAutomationSetting(UpdateAutomationSettingRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(request.getBusinessId())
                .setServiceItemType(request.getServiceItemType())
                .setEnableAutoAccept(request.getEnableAutoAccept())
                .setAutoAcceptCondition(request.getAutoAcceptCondition())
                .setStaffId(AuthContext.get().staffId())
                .build());

        responseObserver.onNext(UpdateAutomationSettingResult.newBuilder()
                .setAutomationSetting(response.getAutomationSetting())
                .build());
        responseObserver.onCompleted();
    }
}
