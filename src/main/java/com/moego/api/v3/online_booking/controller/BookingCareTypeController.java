package com.moego.api.v3.online_booking.controller;

import static com.moego.idl.api.online_booking.v1.BookingCareTypeServiceGrpc.BookingCareTypeServiceImplBase;

import com.moego.api.v3.online_booking.converter.BookingCareTypeConverter;
import com.moego.idl.api.online_booking.v1.CreateBookingCareTypeParams;
import com.moego.idl.api.online_booking.v1.CreateBookingCareTypeResult;
import com.moego.idl.api.online_booking.v1.DeleteBookingCareTypeParams;
import com.moego.idl.api.online_booking.v1.DeleteBookingCareTypeResult;
import com.moego.idl.api.online_booking.v1.GetBookingCareTypeParams;
import com.moego.idl.api.online_booking.v1.GetBookingCareTypeResult;
import com.moego.idl.api.online_booking.v1.ListBookingCareTypesParams;
import com.moego.idl.api.online_booking.v1.ListBookingCareTypesResult;
import com.moego.idl.api.online_booking.v1.SortBookingCareTypeParams;
import com.moego.idl.api.online_booking.v1.SortBookingCareTypeResult;
import com.moego.idl.api.online_booking.v1.UpdateBookingCareTypeParams;
import com.moego.idl.api.online_booking.v1.UpdateBookingCareTypeResult;
import com.moego.idl.models.online_booking.v1.BookingCareType;
import com.moego.idl.models.online_booking.v1.CreateBookingCareTypeDef;
import com.moego.idl.models.online_booking.v1.UpdateBookingCareTypeDef;
import com.moego.idl.service.online_booking.v1.BookingCareTypeServiceGrpc;
import com.moego.idl.service.online_booking.v1.CreateBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.CreateBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.DeleteBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.GetBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.GetBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.ListBookingCareTypesRequest;
import com.moego.idl.service.online_booking.v1.ListBookingCareTypesResponse;
import com.moego.idl.service.online_booking.v1.SortBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.SortBookingCareTypeResponse;
import com.moego.idl.service.online_booking.v1.UpdateBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.UpdateBookingCareTypeResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class BookingCareTypeController extends BookingCareTypeServiceImplBase {

    private final BookingCareTypeServiceGrpc.BookingCareTypeServiceBlockingStub bookingCareTypeService;

    @Override
    @Auth(AuthType.COMPANY)
    public void createBookingCareType(
            CreateBookingCareTypeParams request, StreamObserver<CreateBookingCareTypeResult> responseObserver) {
        long businessId = request.getBusinessId();
        Long companyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();

        CreateBookingCareTypeDef bookingCareTypeDef = request.getBookingCareType();
        BookingCareType bookingCareType =
                BookingCareTypeConverter.INSTANCE.toBookingCareType(bookingCareTypeDef, businessId, companyId, staffId);

        CreateBookingCareTypeResponse response =
                bookingCareTypeService.createBookingCareType(CreateBookingCareTypeRequest.newBuilder()
                        .setBookingCareType(bookingCareType)
                        .build());

        CreateBookingCareTypeResult result = CreateBookingCareTypeResult.newBuilder()
                .setBookingCareType(BookingCareTypeConverter.INSTANCE.modelToView(response.getBookingCareType()))
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateBookingCareType(
            UpdateBookingCareTypeParams request, StreamObserver<UpdateBookingCareTypeResult> responseObserver) {
        Long businessId = AuthContext.get().businessId();
        Long companyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();

        UpdateBookingCareTypeDef updateDef = request.getBookingCareType();
        BookingCareType bookingCareType =
                BookingCareTypeConverter.INSTANCE.toBookingCareType(updateDef, businessId, companyId, staffId);

        UpdateBookingCareTypeResponse response =
                bookingCareTypeService.updateBookingCareType(UpdateBookingCareTypeRequest.newBuilder()
                        .setBookingCareType(bookingCareType)
                        .build());

        UpdateBookingCareTypeResult result = UpdateBookingCareTypeResult.newBuilder()
                .setBookingCareType(BookingCareTypeConverter.INSTANCE.modelToView(response.getBookingCareType()))
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listBookingCareTypes(
            ListBookingCareTypesParams request, StreamObserver<ListBookingCareTypesResult> responseObserver) {
        long businessId = request.getBusinessId();
        Long companyId = AuthContext.get().companyId();

        ListBookingCareTypesRequest serviceRequest =
                BookingCareTypeConverter.INSTANCE.toListRequest(businessId, companyId);
        ListBookingCareTypesResponse response = bookingCareTypeService.listBookingCareTypes(serviceRequest);

        ListBookingCareTypesResult result = ListBookingCareTypesResult.newBuilder()
                .addAllBookingCareTypes(response.getBookingCareTypesList())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getBookingCareType(
            GetBookingCareTypeParams request, StreamObserver<GetBookingCareTypeResult> responseObserver) {
        long id = request.getId();
        long businessId = request.getBusinessId();
        Long companyId = AuthContext.get().companyId();

        GetBookingCareTypeRequest serviceRequest =
                BookingCareTypeConverter.INSTANCE.toGetRequest(id, businessId, companyId);
        GetBookingCareTypeResponse response = bookingCareTypeService.getBookingCareType(serviceRequest);

        GetBookingCareTypeResult result = GetBookingCareTypeResult.newBuilder()
                .setBookingCareType(response.getBookingCareType())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deleteBookingCareType(
            DeleteBookingCareTypeParams request, StreamObserver<DeleteBookingCareTypeResult> responseObserver) {
        long id = request.getId();
        Long businessId = AuthContext.get().businessId();
        Long companyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();

        DeleteBookingCareTypeRequest serviceRequest =
                BookingCareTypeConverter.INSTANCE.toDeleteRequest(id, businessId, companyId, staffId);
        bookingCareTypeService.deleteBookingCareType(serviceRequest);

        responseObserver.onNext(DeleteBookingCareTypeResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void sortBookingCareType(
            SortBookingCareTypeParams request, StreamObserver<SortBookingCareTypeResult> responseObserver) {
        Long businessId = AuthContext.get().businessId();
        Long companyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();

        SortBookingCareTypeRequest serviceRequest = BookingCareTypeConverter.INSTANCE.toSortRequest(
                businessId, companyId, staffId, request.getBookingCareTypeIdsList());
        SortBookingCareTypeResponse response = bookingCareTypeService.sortBookingCareType(serviceRequest);

        SortBookingCareTypeResult result = SortBookingCareTypeResult.newBuilder()
                .addAllBookingCareTypes(response.getBookingCareTypesList())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }
}
