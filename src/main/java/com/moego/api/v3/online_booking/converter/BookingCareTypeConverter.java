package com.moego.api.v3.online_booking.converter;

import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.idl.models.online_booking.v1.ApplicableServices;
import com.moego.idl.models.online_booking.v1.BookingCareType;
import com.moego.idl.models.online_booking.v1.BookingCareTypeView;
import com.moego.idl.models.online_booking.v1.CreateBookingCareTypeDef;
import com.moego.idl.models.online_booking.v1.UpdateBookingCareTypeDef;
import com.moego.idl.service.online_booking.v1.DeleteBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.GetBookingCareTypeRequest;
import com.moego.idl.service.online_booking.v1.ListBookingCareTypesRequest;
import com.moego.idl.service.online_booking.v1.SortBookingCareTypeRequest;
import com.moego.lib.common.util.JsonUtil;
import java.time.LocalDateTime;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        imports = {JsonUtil.class},
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface BookingCareTypeConverter {

    BookingCareTypeConverter INSTANCE = Mappers.getMapper(BookingCareTypeConverter.class);

    default BookingCareTypeView modelToView(BookingCareType bookingCareType) {
        return BookingCareTypeView.newBuilder()
                .setId(bookingCareType.getId())
                .setName(bookingCareType.getName())
                .setServiceType(bookingCareType.getServiceType())
                .setServiceItemType(bookingCareType.getServiceItemType())
                .setIcon(bookingCareType.getIcon())
                .setImage(bookingCareType.getImage())
                .setDescription(bookingCareType.getDescription())
                .setApplicableServices(ApplicableServices.newBuilder()
                        .setIsAllServiceApplicable(bookingCareType.getIsAllServiceApplicable())
                        .addAllSelectedServices(bookingCareType.getSelectedServicesList())
                        .build())
                .build();
    }

    default BookingCareType toBookingCareType(
            CreateBookingCareTypeDef bookingCareTypeDef, long businessId, Long companyId, Long staffId) {
        return BookingCareType.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setName(bookingCareTypeDef.getName())
                .setDescription(bookingCareTypeDef.getDescription())
                .setIcon(bookingCareTypeDef.getIcon())
                .setImage(bookingCareTypeDef.getImage())
                .setServiceType(bookingCareTypeDef.getServiceType())
                .setServiceItemType(bookingCareTypeDef.getServiceItemType())
                .setUpdatedBy(staffId)
                .setCreatedAt(DateTimeConverter.INSTANCE.toTimestamp(LocalDateTime.now()))
                .setUpdatedAt(DateTimeConverter.INSTANCE.toTimestamp(LocalDateTime.now()))
                .setIsAllServiceApplicable(
                        bookingCareTypeDef.getApplicableServices().getIsAllServiceApplicable())
                .addAllSelectedServices(
                        bookingCareTypeDef.getApplicableServices().getSelectedServicesList())
                .build();
    }

    default BookingCareType toBookingCareType(
            UpdateBookingCareTypeDef updateDef, long businessId, Long companyId, Long staffId) {
        BookingCareType.Builder builder = BookingCareType.newBuilder()
                .setId(updateDef.getId())
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setServiceType(updateDef.getServiceType())
                .setServiceItemType(updateDef.getServiceItemType())
                .setIsAllServiceApplicable(updateDef.getApplicableServices().getIsAllServiceApplicable())
                .addAllSelectedServices(updateDef.getApplicableServices().getSelectedServicesList())
                .setUpdatedBy(staffId)
                .setUpdatedAt(DateTimeConverter.INSTANCE.toTimestamp(LocalDateTime.now()));

        if (updateDef.hasName()) {
            builder.setName(updateDef.getName());
        }
        if (updateDef.hasDescription()) {
            builder.setDescription(updateDef.getDescription());
        }
        if (updateDef.hasIcon()) {
            builder.setIcon(updateDef.getIcon());
        }
        if (updateDef.hasImage()) {
            builder.setImage(updateDef.getImage());
        }

        return builder.build();
    }

    /**
     * 创建 ListBookingCareTypesRequest
     */
    default ListBookingCareTypesRequest toListRequest(long businessId, Long companyId) {
        return ListBookingCareTypesRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .build();
    }

    /**
     * 创建 GetBookingCareTypeRequest
     */
    default GetBookingCareTypeRequest toGetRequest(long id, long businessId, Long companyId) {
        return GetBookingCareTypeRequest.newBuilder()
                .setId(id)
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .build();
    }

    /**
     * 创建 DeleteBookingCareTypeRequest
     */
    default DeleteBookingCareTypeRequest toDeleteRequest(long id, long businessId, Long companyId, Long staffId) {
        return DeleteBookingCareTypeRequest.newBuilder()
                .setId(id)
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setStaffId(staffId)
                .build();
    }

    /**
     * 创建 SortBookingCareTypeRequest
     */
    default SortBookingCareTypeRequest toSortRequest(
            long businessId, Long companyId, Long staffId, java.util.List<Long> ids) {
        return SortBookingCareTypeRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setStaffId(staffId)
                .addAllBookingCareTypeIds(ids)
                .build();
    }
}
