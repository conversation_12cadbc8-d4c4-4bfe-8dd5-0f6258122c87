package com.moego.api.v3.online_booking.converter;

import com.moego.idl.api.online_booking.v1.PetCodeComposite;
import com.moego.idl.api.online_booking.v1.PetDetail;
import com.moego.idl.api.online_booking.v1.PetVaccineComposite;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeModel;
import com.moego.idl.models.customer.v1.CustomerPetOnlineBookingView;
import com.moego.idl.models.customer.v1.PetDef;
import com.moego.idl.models.customer.v1.PetGender;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.params.CustomerPetAddParams;
import java.util.List;
import java.util.Objects;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2023/7/10
 */
@Mapper(
        uses = {BaseMapper.class},
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface PetConverter {
    PetConverter INSTANCE = Mappers.getMapper(PetConverter.class);

    @Mappings({
        @Mapping(target = "breedMix", expression = "java(int2byte(pet.getBreedMix()))"),
        @Mapping(target = "businessId", ignore = true),
        @Mapping(target = "status", ignore = true),
        @Mapping(target = "lifeStatus", ignore = true),
        @Mapping(target = "expiryNotification", ignore = true),
        @Mapping(target = "petCodeIdList", ignore = true),
        @Mapping(target = "vaccineList", ignore = true),
        @Mapping(target = "rawId", ignore = true),
        @Mapping(target = "rawCreateTime", ignore = true),
        @Mapping(target = "rawUpdateTime", ignore = true),
        @Mapping(target = "customerId", ignore = true),
    })
    CustomerPetAddParams def2AddParams(PetDef pet);

    default Byte int2byte(int i) {
        return Integer.valueOf(i).byteValue();
    }

    @Mappings({
        @Mapping(target = "petId", ignore = true),
        @Mapping(target = "breedMix", expression = "java(int2byte(pet.getBreedMix()))"),
        @Mapping(target = "vaccineList", ignore = true),
        @Mapping(target = "customQuestions", ignore = true),
    })
    CustomerProfileRequestDTO.PetProfileDTO def2PetProfileDTO(PetDef pet);

    CustomerPetOnlineBookingView dto2onlineBookingView(CustomerProfileRequestDTO.PetProfileDTO pet);

    PetDetail toPetDetail(BusinessCustomerPetModel model);

    PetCodeComposite toPetCode(BusinessPetCodeModel model);

    @Mappings({
        @Mapping(target = "id", source = "petId"),
        @Mapping(target = "vetPhoneNumber", source = "vetPhone"),
        @Mapping(target = "petVaccines", ignore = true),
        @Mapping(target = "birthday", qualifiedByName = "stringToDate"),
        @Mapping(target = "coatType", source = "hairLength"),
        @Mapping(target = "emergencyContactPhoneNumber", source = "emergencyContactPhone"),
    })
    PetDetail toPetDetail(CustomerProfileRequestDTO.PetProfileDTO dto);

    @AfterMapping
    default void afterMapping(CustomerProfileRequestDTO.PetProfileDTO dto, @MappingTarget PetDetail.Builder pet) {
        if (pet.getPetVaccinesList() != null) {
            List<PetVaccineComposite> list = VaccineConverter.INSTANCE.toPetVaccineComposite(dto.getVaccineList());
            if (list != null) {
                pet.addAllPetVaccines(list);
            }
        }
        if (pet.getPetType() != null) {
            pet.setPetType(PetType.forNumber(dto.getPetTypeId()));
        }
        pet.setBreedMixed(dto.getBreedMix() == 1);
    }

    default Byte int2Byte(Integer res) {
        if (Objects.isNull(res)) {
            return null;
        }
        return res.byteValue();
    }

    @Mapping(target = "customQuestions", ignore = true)
    CustomerProfileRequestDTO.PetProfileDTO dto2PetProfileDTO(CustomerPetDetailDTO dto);

    default PetGender toPetGender(Byte value) {
        if (value == null) {
            return PetGender.PET_GENDER_UNKNOWN;
        }
        return PetGender.forNumber(value);
    }
}
