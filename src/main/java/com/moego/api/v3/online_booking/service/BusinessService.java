package com.moego.api.v3.online_booking.service;

import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.params.InfoIdParams;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @since 2023/9/4
 */
@Service
@RequiredArgsConstructor
public class BusinessService {

    private final IBusinessBusinessClient businessClient;

    public MoeBusinessDto getBusinessInfo(Integer businessId) {
        if (Objects.isNull(businessId)) {
            return null;
        }
        InfoIdParams businessIdParams = new InfoIdParams();
        businessIdParams.setInfoId(businessId);
        return businessClient.getBusinessInfo(businessIdParams);
    }
}
