package com.moego.api.v3.online_booking.service;

import com.google.protobuf.Duration;
import com.google.protobuf.Struct;
import com.google.protobuf.Value;
import com.google.protobuf.util.Structs;
import com.google.protobuf.util.Values;
import com.moego.api.v3.account.config.SessionConfig;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.account.v1.CreateSessionRequest;
import com.moego.idl.service.account.v1.DeleteSessionByIdRequest;
import com.moego.idl.service.account.v1.GetSessionRequest;
import com.moego.idl.service.account.v1.SessionServiceGrpc;
import com.moego.idl.service.account.v1.UpdateSessionRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.RequestUtils;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.dto.OBMainSessionDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.server.message.client.IOBVerificationCodeClient;
import com.moego.server.message.dto.SendEmailDTO;
import com.moego.server.message.dto.SendPhoneDTO;
import com.moego.server.message.dto.VerifyCodeDTO;
import com.moego.server.message.enums.VerificationCodeScenarioEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class OBLoginService {

    private final SessionServiceGrpc.SessionServiceBlockingStub sessionServiceBlockingStub;
    private final IOBVerificationCodeClient verificationCodeClient;
    private final SessionConfig sessionConfig;

    private static final Value NULL = Values.ofNull();
    private static final String OB_NAME_PREFIX = "ob.name.";
    public static final String CUSTOMER_ID = "customer.customer_id";
    public static final String PHONE_NUMBER = "customer.phone_number";
    public static final String SOURCE_OB = "ob";

    /**
     * 检查 OB 主会话参数，需要有 session id，account id (<-1) 和 OB name
     * @param anonymousParams ob name
     * @return OBMainSessionDTO
     */
    public OBMainSessionDTO checkOBSession(OBAnonymousParams anonymousParams) {
        var context = AuthContext.get();
        context.checkOBMainSessionValid();

        // obName is required, 优先解析 domain（自定义域名的 OB）, 然后解析 name （统一域名的 ob）
        var obName = anonymousParams.getDomain();
        if (!StringUtils.hasText(obName)) {
            obName = anonymousParams.getName();
        }
        if (!StringUtils.hasText(obName)) {
            throw ExceptionUtil.bizException(Code.CODE_BOOK_ONLINE_NAME_INVALID);
        }

        return new OBMainSessionDTO(context.sessionId(), context.accountId(), obName);
    }

    public void logoutOBSubSession(long mainSessionId, String obName) {
        var getRequest = GetSessionRequest.newBuilder().setId(mainSessionId).build();
        var mainSession = sessionServiceBlockingStub.getSession(getRequest);

        var key = buildOBSubSessionKey(obName);
        var value = mainSession.getSessionData().getFieldsOrDefault(key, NULL);
        // 没有记录子会话 id, 幂等登出不报错
        if (NULL.equals(value)) {
            return;
        }

        // 删除子会话，忽略报错
        try {
            var subSessionId = Long.parseUnsignedLong(value.getStringValue());
            sessionServiceBlockingStub.deleteSessionById(
                    DeleteSessionByIdRequest.newBuilder().setId(subSessionId).build());
        } catch (Throwable t) {
            log.error(
                    "fail to delete sub session, sebSessionId: {}, mainSessionId: {}, key: {}",
                    value,
                    mainSessionId,
                    key);
        }

        // 修改主会话的 sessionData, 删除子会话的 key
        var sessionData = Structs.of(key, NULL);
        updateMainSessionData(mainSessionId, sessionData);
    }

    private String buildOBSubSessionKey(String obName) {
        return OB_NAME_PREFIX + obName.toLowerCase();
    }

    private void updateMainSessionData(long mainSessionId, Struct sessionData) {
        var updateRequest = UpdateSessionRequest.newBuilder()
                .setId(mainSessionId)
                .setSessionData(sessionData)
                .build();
        sessionServiceBlockingStub.updateSession(updateRequest);
    }

    public void loginWithSessionData(OBMainSessionDTO obMainSessionDTO, Struct struct) {
        // impersonate 时，子会话有效期为 1 天，且不允许续期
        boolean impersonate = StringUtils.hasText(AuthContext.get().impersonator());
        var maxAge =
                impersonate ? 86400 : sessionConfig.getSessionContext().source().subMaxAge();
        var impersonator = impersonate ? AuthContext.get().impersonator() : "";
        boolean renewable = !impersonate;

        // 创建子会话
        CreateSessionRequest createRequest = CreateSessionRequest.newBuilder()
                .setAccountId(obMainSessionDTO.mainAccountId())
                .setIp(RequestUtils.getIP())
                .setUserAgent(RequestUtils.getUserAgent())
                .setDeviceId(RequestUtils.getDeviceId())
                .setRefererLink(RequestUtils.getReferer())
                .setRefererSessionId(obMainSessionDTO.mainSessionId())
                .setSessionData(struct)
                .setMaxAge(Duration.newBuilder().setSeconds(maxAge).build())
                .setSource(SOURCE_OB)
                .setImpersonator(impersonator)
                .setRenewable(renewable)
                .build();

        var createResponse = sessionServiceBlockingStub.createSession(createRequest);
        var subSessionId = createResponse.getId();

        // 修改主会话
        var mainSessionData = buildOBMainSessionData(obMainSessionDTO.obName(), subSessionId);
        updateMainSessionData(obMainSessionDTO.mainSessionId(), mainSessionData);
    }

    public void loginWithCustomerId(OBMainSessionDTO obMainSessionDTO, int customerId) {
        Struct sessionData = Structs.of(CUSTOMER_ID, Values.of(String.valueOf(customerId)));
        loginWithSessionData(obMainSessionDTO, sessionData);
    }

    public void loginWithPhoneNumber(OBMainSessionDTO obMainSessionDTO, String phoneNumber) {
        Struct sessionData = Structs.of(PHONE_NUMBER, Values.of(String.valueOf(phoneNumber)));
        loginWithSessionData(obMainSessionDTO, sessionData);
    }

    public void loginWithToken(OBMainSessionDTO obMainSessionDTO, String subSessionToken) {
        // 查出子会话
        var getRequest =
                GetSessionRequest.newBuilder().setSessionToken(subSessionToken).build();
        var subSession = sessionServiceBlockingStub.getSession(getRequest);

        // 修改主会话
        var sessionData = buildOBMainSessionData(obMainSessionDTO.obName(), subSession.getId());
        updateMainSessionData(obMainSessionDTO.mainSessionId(), sessionData);

        // 更新子会话
        var subUpdateRequest = UpdateSessionRequest.newBuilder()
                .setId(subSession.getId())
                .setAccountId(obMainSessionDTO.mainAccountId())
                .setRefererSessionId(obMainSessionDTO.mainSessionId())
                .build();
        sessionServiceBlockingStub.updateSession(subUpdateRequest);
    }

    private Struct buildOBMainSessionData(String obName, long subSessionId) {
        var key = buildOBSubSessionKey(obName);
        var value = Values.of(String.valueOf(subSessionId));
        return Structs.of(key, value);
    }

    public String sendPhoneVerificationCode(
            MoeBusinessDto businessDto, MoeBusinessCustomerDTO customerDTO, String phoneNumber) {
        SendPhoneDTO sendPhoneDTO = new SendPhoneDTO()
                .setScenario(VerificationCodeScenarioEnum.OB_LOGIN)
                .setBusinessId(businessDto.getId())
                .setBusinessName(businessDto.getBusinessName())
                .setCustomerId(customerDTO.getCustomerId())
                .setFirstName(customerDTO.getFirstName())
                .setLastName(customerDTO.getLastName())
                .setPhoneNumber(phoneNumber);
        return verificationCodeClient.sendPhoneVerificationCode(sendPhoneDTO);
    }

    public String generatePhoneToken(MoeBusinessDto businessDto, String phoneNumber) {
        SendPhoneDTO sendPhoneDTO = new SendPhoneDTO()
                .setScenario(VerificationCodeScenarioEnum.OB_LOGIN)
                .setBusinessId(businessDto.getId())
                .setPhoneNumber(phoneNumber);
        return verificationCodeClient.generatePhoneToken(sendPhoneDTO);
    }

    public String sendEmailVerificationCode(MoeBusinessDto businessDto, MoeBusinessCustomerDTO customerDTO) {
        SendEmailDTO sendEmailDTO = new SendEmailDTO()
                .setScenario(VerificationCodeScenarioEnum.OB_LOGIN)
                .setBusinessId(businessDto.getId())
                .setFromName(businessDto.getBusinessName())
                .setToEmail(customerDTO.getEmail());
        return verificationCodeClient.sendEmailVerificationCode(sendEmailDTO);
    }

    public void verifyPhoneCode(VerifyCodeDTO verifyCodeDTO) {
        verificationCodeClient.verifyPhoneCode(verifyCodeDTO);
    }

    public void verifyPhoneToken(VerifyCodeDTO verifyCodeDTO) {
        verificationCodeClient.verifyPhoneToken(verifyCodeDTO);
    }

    public void verifyEmailCode(VerifyCodeDTO verifyCodeDTO) {
        verificationCodeClient.verifyEmailCode(verifyCodeDTO);
    }
}
