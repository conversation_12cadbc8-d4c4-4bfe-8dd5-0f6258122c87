package com.moego.api.v3.online_booking.converter;

import com.moego.api.v3.shared.util.ProtobufUtil;
import com.moego.common.enums.CustomerPetEnum;
import com.moego.idl.api.online_booking.v1.CreateWaitlistParams;
import com.moego.idl.api.online_booking.v1.UpdateWaitlistParams;
import com.moego.idl.api.online_booking.v1.WaitlistView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.online_booking.v1.BoardingService;
import com.moego.idl.models.online_booking.v1.BoardingWaitlist;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.online_booking.v1.DaycareService;
import com.moego.idl.models.online_booking.v1.DaycareWaitlist;
import com.moego.idl.models.online_booking.v1.PetServiceDetail;
import com.moego.idl.models.online_booking.v1.Service;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.service.online_booking.v1.CreateBoardingServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateBoardingServiceWaitlistRequest;
import com.moego.idl.service.online_booking.v1.CreateBookingRequestRequest;
import com.moego.idl.service.online_booking.v1.CreateDaycareServiceDetailRequest;
import com.moego.idl.service.online_booking.v1.CreateDaycareServiceWaitlistRequest;
import com.moego.idl.service.online_booking.v1.ReplaceBookingRequestRequest;
import com.moego.lib.common.util.JsonUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        imports = {JsonUtil.class, CustomerPetEnum.class},
        uses = {BaseMapper.class},
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface WaitlistConverter {
    WaitlistConverter INSTANCE = Mappers.getMapper(WaitlistConverter.class);

    @Mapping(target = "isDeleted", source = "deleted")
    WaitlistView.CustomerInfo convertCustomerInfo(BusinessCustomerInfoModel infoModel);

    @Mapping(target = "petId", source = "id")
    @Mapping(target = "petTypeId", expression = "java(infoModel.getPetType().getNumber())")
    WaitlistView.PetInfo convertPetInfo(BusinessCustomerPetInfoModel infoModel);

    WaitlistView.CreateBy convertStaffInfo(StaffModel infoModel);

    default WaitlistView.ServiceInfo convertServiceInfo(ServiceBriefView infoModel) {
        return WaitlistView.ServiceInfo.newBuilder()
                .setServiceId(infoModel.getId())
                .setServiceName(infoModel.getName())
                .setServicePrice(infoModel.getPrice())
                .setServiceItemType(infoModel.getServiceItemType())
                .setPriceUnit(infoModel.getPriceUnit())
                .build();
    }

    CreateBoardingServiceDetailRequest convertServiceBoardingDetail(PetServiceDetail petServiceDetail);

    CreateBoardingServiceWaitlistRequest convertServiceDetailBoardingWaitlist(BoardingWaitlist boardingWaitlist);

    CreateDaycareServiceDetailRequest convertServiceDaycareDetail(PetServiceDetail petServiceDetail);

    CreateDaycareServiceWaitlistRequest convertServiceDetailDaycareWaitlist(DaycareWaitlist daycareWaitlist);

    static ReplaceBookingRequestRequest convertToRequest(
            UpdateWaitlistParams request, BookingRequestModel existWaitlist) {
        var requestBuilder = ReplaceBookingRequestRequest.newBuilder();
        requestBuilder.setId(request.getWaitlistId());
        requestBuilder.setStartDate(request.hasStartDate() ? request.getStartDate() : existWaitlist.getStartDate());
        requestBuilder.setEndDate(request.hasEndDate() ? request.getEndDate() : existWaitlist.getEndDate());
        if (request.getServicesCount() > 0) {
            requestBuilder.addAllServices(convertAllServiceDetail(
                    request.getServicesList(), requestBuilder.getStartDate(), requestBuilder.getEndDate()));
        }
        if (request.hasComment()) {
            requestBuilder.setComment(request.getComment());
        }
        return requestBuilder.build();
    }

    static BoardingWaitlist convertToBoardingWaitlist(String startDate, String endDate) {
        return BoardingWaitlist.newBuilder()
                .setStartDate(ProtobufUtil.toProtobufDate(startDate))
                .setEndDate(ProtobufUtil.toProtobufDate(endDate))
                .build();
    }

    static DaycareWaitlist convertToDaycareWaitlist(String startDate) {
        return DaycareWaitlist.newBuilder()
                .addSpecificDates(ProtobufUtil.toProtobufDate(startDate))
                .build();
    }

    static List<ReplaceBookingRequestRequest.Service> convertAllServiceDetail(
            List<Service> services, String startDate, String endDate) {
        var boardingWaitlist = convertToBoardingWaitlist(startDate, endDate);
        var daycareWaitlist = convertToDaycareWaitlist(startDate);
        return services.stream()
                .map(service -> {
                    if (service.hasBoarding()) {
                        return ReplaceBookingRequestRequest.Service.newBuilder()
                                .setBoarding(
                                        convertReplaceServiceBoardingService(service.getBoarding(), boardingWaitlist))
                                .build();
                    } else if (service.hasDaycare()) {
                        return ReplaceBookingRequestRequest.Service.newBuilder()
                                .setDaycare(convertReplaceServiceDaycareService(service.getDaycare(), daycareWaitlist))
                                .build();
                    }
                    return null;
                })
                .collect(Collectors.toList());
    }

    static ReplaceBookingRequestRequest.BoardingService convertReplaceServiceBoardingService(
            BoardingService boardingService, BoardingWaitlist waitlist) {
        if (boardingService == null) {
            return null;
        }
        var boardingService1 = ReplaceBookingRequestRequest.BoardingService.newBuilder();
        boardingService1.setService(
                WaitlistConverter.INSTANCE.convertServiceBoardingDetail(boardingService.getService()));
        boardingService1.setWaitlist(WaitlistConverter.INSTANCE.convertServiceDetailBoardingWaitlist(waitlist));
        return boardingService1.build();
    }

    static ReplaceBookingRequestRequest.DaycareService convertReplaceServiceDaycareService(
            DaycareService daycareService, DaycareWaitlist waitlist) {
        if (daycareService == null) {
            return null;
        }
        var daycareService1 = ReplaceBookingRequestRequest.DaycareService.newBuilder();
        daycareService1.setService(WaitlistConverter.INSTANCE.convertServiceDaycareDetail(daycareService.getService()));
        daycareService1.setWaitlist(WaitlistConverter.INSTANCE.convertServiceDetailDaycareWaitlist(waitlist));
        return daycareService1.build();
    }

    static CreateBookingRequestRequest.BoardingService buildBoardingService(
            BoardingService boardingService, String startDate, String endDate) {
        var createBoardingService = CreateBookingRequestRequest.BoardingService.newBuilder();
        if (boardingService.hasService()) {
            createBoardingService.setService(
                    WaitlistConverter.INSTANCE.convertServiceBoardingDetail(boardingService.getService()).toBuilder()
                            .setStartDate(startDate)
                            .setEndDate(endDate)
                            .build());
            createBoardingService.setWaitlist(WaitlistConverter.INSTANCE.convertServiceDetailBoardingWaitlist(
                    convertToBoardingWaitlist(startDate, endDate)));
        }
        return createBoardingService.build();
    }

    static CreateBookingRequestRequest.DaycareService buildDaycareService(
            DaycareService daycareService, String startDate) {
        var createDaycareService = CreateBookingRequestRequest.DaycareService.newBuilder();
        if (daycareService.hasService()) {
            createDaycareService.setService(
                    WaitlistConverter.INSTANCE.convertServiceDaycareDetail(daycareService.getService()).toBuilder()
                            .addSpecificDates(startDate)
                            .build());
            createDaycareService.setWaitlist(WaitlistConverter.INSTANCE.convertServiceDetailDaycareWaitlist(
                    convertToDaycareWaitlist(startDate)));
        }
        return createDaycareService.build();
    }

    static CreateBookingRequestRequest convertToRequest(long companyId, CreateWaitlistParams params) {
        var requestBuilder = CreateBookingRequestRequest.newBuilder();
        requestBuilder.setCompanyId(companyId);
        requestBuilder.setBusinessId(params.getBusinessId());
        requestBuilder.setStatus(BookingRequestStatus.WAIT_LIST_VALUE);
        requestBuilder.setSource(BookingRequestModel.Source.BUSINESS);
        requestBuilder.setCustomerId(params.getCustomerId());
        requestBuilder.addAllServices(
                convertService(params.getServicesList(), params.getStartDate(), params.getEndDate()));
        if (params.hasComment()) {
            requestBuilder.setComment(params.getComment());
        }
        return requestBuilder.build();
    }

    static List<CreateBookingRequestRequest.Service> convertService(
            List<Service> petServices, String startDate, String endDate) {
        List<CreateBookingRequestRequest.Service> serviceList = new ArrayList<>();
        for (var petService : petServices) {
            if (petService.hasBoarding()) {
                var newService = CreateBookingRequestRequest.Service.newBuilder()
                        .setBoarding(buildBoardingService(petService.getBoarding(), startDate, endDate))
                        .build();
                serviceList.add(newService);
            }
            if (petService.hasDaycare()) {
                var newDaycare = CreateBookingRequestRequest.Service.newBuilder()
                        .setDaycare(buildDaycareService(petService.getDaycare(), startDate))
                        .build();
                serviceList.add(newDaycare);
            }
        }
        return serviceList;
    }
}
