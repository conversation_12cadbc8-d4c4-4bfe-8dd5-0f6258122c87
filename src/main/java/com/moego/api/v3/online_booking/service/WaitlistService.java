package com.moego.api.v3.online_booking.service;

import com.moego.api.v3.appointment.utils.FutureService;
import com.moego.api.v3.fulfillment.QuantityUtils;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.lib.common.exception.ExceptionUtil;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class WaitlistService {
    private final FutureService futureService;

    /**
     * waitlist 只支持 单 main service
     * 计算 waitlist 的 service 总价格，不需要考虑 tax，auto rule 等其他场景的金额
     * @param companyId
     * @param serviceList
     * @return
     */
    public Double calculateTotalPrice(Long companyId, List<BookingRequestModel.Service> serviceList) {
        if (CollectionUtils.isEmpty(serviceList)) {
            return 0D;
        }
        List<Long> serviceIds = getServiceIdByWaitlistServiceList(serviceList);

        var serviceMap = futureService
                .getServiceMapByIds(companyId, serviceIds.stream().toList())
                .join();

        double totalPrice = 0;

        for (BookingRequestModel.Service service : serviceList) {
            switch (service.getServiceCase()) {
                case BOARDING -> {
                    var mainService = service.getBoarding().getService();
                    var serviceConfig = serviceMap.get(mainService.getServiceId());
                    totalPrice += mainService.getServicePrice()
                            * getServiceQuantity(
                                    serviceConfig.getPriceUnit(), mainService.getStartDate(), mainService.getEndDate());
                }
                case DAYCARE -> {
                    var mainService = service.getDaycare().getService();
                    var serviceConfig = serviceMap.get(mainService.getServiceId());
                    totalPrice += serviceConfig.getPrice() * mainService.getSpecificDatesCount();
                }
                case GROOMING, EVALUATION, DOG_WALKING, GROUP_CLASS, SERVICE_NOT_SET -> {
                    // waitlist 暂不支持这些类型
                }
            }
        }
        return totalPrice;
    }

    private static int getServiceQuantity(ServicePriceUnit priceUnit, String startDate, String endDate) {
        return switch (priceUnit) {
            case PER_SESSION -> 1;
            case PER_NIGHT -> QuantityUtils.calculateNights(startDate, endDate);
            case PER_DAY -> QuantityUtils.calculateDays(startDate, endDate);
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "price unit not found");
        };
    }

    private static List<Long> getServiceIdByWaitlistServiceList(List<BookingRequestModel.Service> serviceList) {
        Set<Long> serviceIds = new HashSet<>();
        for (BookingRequestModel.Service service : serviceList) {
            switch (service.getServiceCase()) {
                case BOARDING -> {
                    serviceIds.add(service.getBoarding().getService().getServiceId());
                }
                case DAYCARE -> {
                    serviceIds.add(service.getDaycare().getService().getServiceId());
                }
                case GROOMING, EVALUATION, DOG_WALKING, GROUP_CLASS, SERVICE_NOT_SET -> {
                    // waitlist 暂不支持这些类型
                }
            }
        }
        return serviceIds.stream().toList();
    }
}
