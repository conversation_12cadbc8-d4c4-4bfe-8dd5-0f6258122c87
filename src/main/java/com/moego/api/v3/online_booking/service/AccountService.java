package com.moego.api.v3.online_booking.service;

import com.google.common.collect.Lists;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.idl.models.account.v1.AccountModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerModel;
import com.moego.idl.service.account.v1.AccountServiceGrpc;
import com.moego.idl.service.account.v1.BatchGetAccountRequest;
import com.moego.lib.common.thread.ThreadPool;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service("onlineBookingAccountService")
@RequiredArgsConstructor
public class AccountService {
    private final AccountServiceGrpc.AccountServiceBlockingStub accountService;

    public CompletableFuture<Map<Long, AccountModel>> listAccount(
            CompletableFuture<Map<Long, BusinessCustomerModel>> customerDetailFuture) {
        return customerDetailFuture.thenApplyAsync(
                result -> {
                    var accountIds = result.values().stream()
                            .map(BusinessCustomerModel::getAccountId)
                            .filter(accountId -> !PrimitiveTypeUtil.isNullOrZero(accountId))
                            .collect(Collectors.toSet());
                    if (CollectionUtils.isEmpty(accountIds)) {
                        return Map.of();
                    }

                    // size * 2 避免 hash map 发生扩容
                    var accountMap = new HashMap<Long, AccountModel>(accountIds.size() * 2);
                    Lists.partition(new ArrayList<>(accountIds), 1000).forEach(ids -> {
                        var accounts = accountService
                                .batchGetAccount(BatchGetAccountRequest.newBuilder()
                                        .addAllIds(ids)
                                        .build())
                                .getAccountsList();
                        if (!CollectionUtils.isEmpty(accounts)) {
                            accounts.forEach(account -> accountMap.put(account.getId(), account));
                        }
                    });
                    return accountMap;
                },
                ThreadPool.getSubmitExecutor());
    }
}
