package com.moego.api.v3.online_booking.service;

import static com.moego.common.utils.CommonUtil.isNormal;

import com.moego.api.v3.appointment.utils.FutureService;
import com.moego.api.v3.appointment.utils.PackageHelper;
import com.moego.api.v3.membership.converter.MembershipConverter;
import com.moego.api.v3.membership.service.MembershipService;
import com.moego.api.v3.offering.service.ServiceService;
import com.moego.api.v3.online_booking.converter.BookingRequestConverter;
import com.moego.api.v3.online_booking.converter.ClientConverter;
import com.moego.api.v3.online_booking.converter.PetConverter;
import com.moego.api.v3.online_booking.converter.QuestionConverter;
import com.moego.api.v3.online_booking.utils.OnlineBookingFutureService;
import com.moego.api.v3.shared.converter.DateTimeConverter;
import com.moego.api.v3.shared.helper.CompanyHelper;
import com.moego.api.v3.shared.helper.EvaluationHelper;
import com.moego.common.enums.CustomerContactEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.PermissionUtil;
import com.moego.idl.api.online_booking.v1.AddressDetail;
import com.moego.idl.api.online_booking.v1.AssignRequire;
import com.moego.idl.api.online_booking.v1.BookingRequestDetail;
import com.moego.idl.api.online_booking.v1.CustomerDetail;
import com.moego.idl.api.online_booking.v1.DogWalkingService;
import com.moego.idl.api.online_booking.v1.EvaluationService;
import com.moego.idl.api.online_booking.v1.GetBookingRequestItem;
import com.moego.idl.api.online_booking.v1.GetBookingRequestResponse;
import com.moego.idl.api.online_booking.v1.GroomingAutoAssignDetail;
import com.moego.idl.api.online_booking.v1.GroomingService;
import com.moego.idl.api.online_booking.v1.GroupClassService;
import com.moego.idl.api.online_booking.v1.IncompleteDetails;
import com.moego.idl.api.online_booking.v1.OrderBookingRequestView;
import com.moego.idl.api.online_booking.v1.PayBookingRequestView;
import com.moego.idl.api.online_booking.v1.PetDetail;
import com.moego.idl.api.online_booking.v1.RoomAssignRequire;
import com.moego.idl.api.online_booking.v1.ServiceDetail;
import com.moego.idl.api.online_booking.v1.StaffAssignRequire;
import com.moego.idl.models.account.v1.AccountModel;
import com.moego.idl.models.account.v1.AccountNamespaceType;
import com.moego.idl.models.account.v1.AccountStatus;
import com.moego.idl.models.appointment.v1.AppointmentNoteModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerContactModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.membership.v1.MembershipModel;
import com.moego.idl.models.membership.v1.MembershipModelPublicView;
import com.moego.idl.models.membership.v1.MembershipSubscriptionListModel;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.BoardingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.online_booking.v1.BookingRequestModel;
import com.moego.idl.models.online_booking.v1.DaycareAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.DaycareServiceDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingAddOnDetailModel;
import com.moego.idl.models.online_booking.v1.GroomingServiceDetailModel;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.models.organization.v1.TimeZone;
import com.moego.idl.service.business_customer.v1.BatchGetPetRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.service.offering.v1.EvaluationServiceGrpc;
import com.moego.idl.service.offering.v1.GetEvaluationListWithEvaluationIdsRequest;
import com.moego.idl.service.offering.v1.GetInstanceRequest;
import com.moego.idl.service.offering.v1.GetInstanceResponse;
import com.moego.idl.service.offering.v1.GroupClassServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.permission.PermissionEnums;
import com.moego.lib.permission.PermissionHelper;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.grooming.api.IBookOnlineQuestionService;
import com.moego.server.grooming.dto.GroomingQuestionDTO;
import jakarta.annotation.Nullable;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class BookingRequestQueryService {

    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub businessCustomerPetService;
    private final IBookOnlineQuestionService questionService;
    private final PermissionHelper permissionHelper;
    private final OnlineBookingFutureService onlineBookingFutureService;
    private final BusinessCustomerService businessCustomerService;
    private final FutureService futureService;
    private final MembershipService membershipService;
    private final PackageHelper packageHelper;
    private final AccountService accountService;
    private final GroupClassServiceGrpc.GroupClassServiceBlockingStub groupClassService;
    private final EvaluationServiceGrpc.EvaluationServiceBlockingStub evaluationStub;
    private final MembershipConverter membershipConverter;
    private final ServiceService serviceService;
    private final CompanyHelper companyHelper;

    public static final String EVALUATION_SERVICE_NAME = "Evaluation";

    private static final Map<ServiceItemType, Integer> SERVICE_ITEM_TYPE_ORDER = new TreeMap<>(Map.of(
            ServiceItemType.BOARDING, 1,
            ServiceItemType.DAYCARE, 2,
            ServiceItemType.GROOMING, 3,
            ServiceItemType.EVALUATION, 4,
            ServiceItemType.DOG_WALKING, 5,
            ServiceItemType.GROUP_CLASS, 6));
    private final EvaluationHelper evaluationHelper;

    public List<GetBookingRequestItem> getBookingRequestItems(
            Long companyId, Long businessId, List<BookingRequestModel> bookingRequests, Long tokenStaffId) {
        List<Long> appointmentIds = bookingRequests.stream()
                .map(BookingRequestModel::getAppointmentId)
                .filter(id -> id > 0)
                .distinct()
                .toList();

        List<Long> bookingRequestIds = bookingRequests.stream()
                .map(BookingRequestModel::getId)
                .filter(id -> id > 0)
                .distinct()
                .toList();

        List<Long> petIds = bookingRequests.stream()
                .flatMap(bookingRequest -> bookingRequest.getServicesList().stream())
                .map(service -> switch (service.getServiceCase()) {
                    case BOARDING -> service.getBoarding().getService().getPetId();
                    case DAYCARE -> service.getDaycare().getService().getPetId();
                    case GROOMING -> service.getGrooming().getService().getPetId();
                    case EVALUATION -> service.getEvaluation().getService().getPetId();
                    case DOG_WALKING -> service.getDogWalking().getService().getPetId();
                    case GROUP_CLASS -> service.getGroupClass().getService().getPetId();
                    default -> null;
                })
                .filter(id -> Objects.nonNull(id) && id > 0)
                .distinct()
                .toList();

        List<Long> customerIds = bookingRequests.stream()
                .map(BookingRequestModel::getCustomerId)
                .filter(id -> id > 0)
                .distinct()
                .toList();

        List<Long> serviceIds = bookingRequests.stream()
                .flatMap(bookingRequest -> bookingRequest.getServicesList().stream())
                .flatMap(service -> {
                    List<Long> list = new ArrayList<>();
                    switch (service.getServiceCase()) {
                        case BOARDING -> {
                            list.add(service.getBoarding().getService().getServiceId());
                            list.addAll(service.getBoarding().getAddonsList().stream()
                                    .map(BoardingAddOnDetailModel::getAddOnId)
                                    .toList());
                        }
                        case DAYCARE -> {
                            list.add(service.getDaycare().getService().getServiceId());
                            list.addAll(service.getDaycare().getAddonsList().stream()
                                    .map(DaycareAddOnDetailModel::getAddOnId)
                                    .toList());
                        }
                        case GROOMING -> {
                            list.add(service.getGrooming().getService().getServiceId());
                            list.addAll(service.getGrooming().getAddonsList().stream()
                                    .map(GroomingAddOnDetailModel::getAddOnId)
                                    .toList());
                        }
                        case DOG_WALKING -> {
                            list.add(service.getDogWalking().getService().getServiceId());
                            // TODO dog walking add on
                        }
                        case GROUP_CLASS -> {
                            list.add(service.getGroupClass().getService().getServiceId());
                        }
                        default -> {}
                    }
                    return list.stream();
                })
                .distinct()
                .filter(id -> Objects.nonNull(id) && id > 0)
                .toList();

        List<Long> evaluationIds = bookingRequests.stream()
                .flatMap(bookingRequest -> bookingRequest.getServicesList().stream())
                .filter(BookingRequestModel.Service::hasEvaluation)
                .map(service -> service.getEvaluation().getService().getEvaluationId())
                .filter(CommonUtil::isNormal)
                .distinct()
                .toList();

        List<Long> lodgingIds = bookingRequests.stream()
                .flatMap(bookingRequest -> bookingRequest.getServicesList().stream())
                .filter(BookingRequestModel.Service::hasBoarding)
                .map(service -> service.getBoarding().getService().getLodgingId())
                .filter(id -> id > 0)
                .distinct()
                .toList();

        List<Long> membershipIds = bookingRequests.stream()
                .filter(bookingRequest -> bookingRequest.getSource().equals(BookingRequestModel.Source.MEMBERSHIP))
                .map(BookingRequestModel::getSourceId)
                .filter(sourceId -> sourceId > 0)
                .distinct()
                .toList();

        // auto assign
        CompletableFuture<Map<Long, GroomingAutoAssignDetail>> groomingAutoAssignsFuture =
                onlineBookingFutureService.getGroomingAutoAssigns(appointmentIds);

        // staff detail
        CompletableFuture<Map<Long, StaffModel>> staffDetailFuture =
                onlineBookingFutureService.getStaffMap(bookingRequests, groomingAutoAssignsFuture);

        // customer details
        CompletableFuture<Map<Long, BusinessCustomerModel>> customerDetailFuture =
                onlineBookingFutureService.getBusinessCustomerMap(companyId, customerIds);
        CompletableFuture<Map<Long, AccountModel>> accountFuture = accountService.listAccount(customerDetailFuture);
        final var membershipSubscriptionsFuture =
                futureService.supply(() -> membershipService.getAllSubscriptions(customerIds));
        final var membershipModelsFuture =
                futureService.supply(() -> membershipService.listMembershipModel(membershipIds));
        // finished appt
        CompletableFuture<List<Long>> newCustomerIdListFuture =
                onlineBookingFutureService.getNewCustomerIdList(companyId, customerIds);

        // pet detail
        CompletableFuture<Map<Long, BusinessCustomerPetModel>> petDetailFuture = CompletableFuture.supplyAsync(
                () -> {
                    if (CollectionUtils.isEmpty(petIds)) {
                        return Map.of();
                    }
                    return businessCustomerPetService
                            .batchGetPet(BatchGetPetRequest.newBuilder()
                                    .setTenant(Tenant.newBuilder()
                                            .setCompanyId(companyId)
                                            .build())
                                    .addAllIds(new ArrayList<>(petIds))
                                    .build())
                            .getPetsList()
                            .stream()
                            .collect(Collectors.toMap(BusinessCustomerPetModel::getId, Function.identity()));
                },
                ThreadPool.getSubmitExecutor());

        // customer has request update
        CompletableFuture<Map<Long, BusinessCustomerService.CustomerHasRequestDTO>> customerHasUpdateFuture =
                CompletableFuture.supplyAsync(
                        () -> {
                            if (CollectionUtils.isEmpty(customerIds)) {
                                return Map.of();
                            }
                            return businessCustomerService.listCustomerHasRequestUpdate(
                                    Math.toIntExact(businessId), customerIds);
                        },
                        ThreadPool.getSubmitExecutor());

        // grooming pay
        CompletableFuture<Map<Long, PayBookingRequestView>> groomingPayBookingRequestFuture =
                onlineBookingFutureService.getPrePayCalendarViews(businessId, appointmentIds);

        // BD pay
        CompletableFuture<Map<Long, PayBookingRequestView>> bdPayBookingRequestFuture =
                onlineBookingFutureService.getBDPrePayCalendarViews(businessId, bookingRequestIds);

        // service
        CompletableFuture<Map<Long, ServiceBriefView>> serviceMapFuture =
                onlineBookingFutureService.getServiceMap(companyId, serviceIds);

        // lodging
        CompletableFuture<AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>>>
                lodgingMapFuture = onlineBookingFutureService.getLodgingMap(lodgingIds);

        // note
        CompletableFuture<Map<Long, AppointmentNoteModel>> lastAlertNoteFuture =
                onlineBookingFutureService.getCustomersLastAlertNoteFuture(companyId, customerIds);

        // service type
        CompletableFuture<Map<Long, List<ServiceItemType>>> serviceTypeFuture =
                onlineBookingFutureService.getServiceTypeFuture(bookingRequests);

        // pet code
        CompletableFuture<Map<Long, List<BusinessPetCodeModel>>> petCodeMapFuture =
                onlineBookingFutureService.getPetCodeMap(companyId, petIds);

        // out of service area
        CompletableFuture<Map<Long, Boolean>> outOfServiceAreaFuture =
                onlineBookingFutureService.getOutOfServiceArea(businessId, customerIds);

        CompletableFuture<Map<Long, EvaluationBriefView>> evaluationMapFuture =
                CompletableFuture.supplyAsync(() -> listEvaluation(evaluationIds), ThreadPool.getSubmitExecutor());

        // timezone
        CompletableFuture<TimeZone> timezoneFuture = CompletableFuture.supplyAsync(
                () -> companyHelper.mustGetCompanyTimeZone(companyId), ThreadPool.getSubmitExecutor());

        // customer package
        var customerPackageFuture = packageHelper.listCustomerPackages(companyId, businessId, customerIds);
        var customerPackageDetailFuture = packageHelper.listCustomerPackageDetails(customerPackageFuture);

        CompletableFuture.allOf(
                        staffDetailFuture,
                        customerDetailFuture,
                        membershipSubscriptionsFuture,
                        newCustomerIdListFuture,
                        petDetailFuture,
                        customerHasUpdateFuture,
                        groomingPayBookingRequestFuture,
                        bdPayBookingRequestFuture,
                        serviceMapFuture,
                        lodgingMapFuture,
                        lastAlertNoteFuture,
                        serviceTypeFuture,
                        petCodeMapFuture,
                        groomingAutoAssignsFuture,
                        outOfServiceAreaFuture,
                        customerPackageFuture,
                        customerPackageDetailFuture,
                        evaluationMapFuture,
                        membershipModelsFuture,
                        timezoneFuture)
                .join();

        Map<Long, StaffModel> staffMap = staffDetailFuture.join();
        Map<Long, BusinessCustomerModel> customerInfoDTOMap = customerDetailFuture.join();
        Map<Long, AccountModel> accountMap = accountFuture.join();
        final var membershipSubscriptionsMap = membershipSubscriptionsFuture.join();
        List<Long> newCustomerIds = newCustomerIdListFuture.join();
        Map<Long, BusinessCustomerService.CustomerHasRequestDTO> customerHasUpdateMap = customerHasUpdateFuture.join();
        Map<Long, BusinessCustomerPetModel> petMap = petDetailFuture.join();
        Map<Long, PayBookingRequestView> groomingPayBookingRequestViewMap = groomingPayBookingRequestFuture.join();
        Map<Long, PayBookingRequestView> bdPayBookingRequestViewMap = bdPayBookingRequestFuture.join();
        Map<Long, ServiceBriefView> serviceMap = serviceMapFuture.join();
        AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>> lodgingMap =
                lodgingMapFuture.join();
        Map<Long, AppointmentNoteModel> lastAlertNoteMap = lastAlertNoteFuture.join();
        Map<Long, List<ServiceItemType>> serviceTypeMap = serviceTypeFuture.join();
        Map<Long, List<BusinessPetCodeModel>> petCodeMap = petCodeMapFuture.join();
        Map<Long, GroomingAutoAssignDetail> groomingAutoAssignMap = groomingAutoAssignsFuture.join();
        Map<Long, Boolean> outOfServiceAreaMap = outOfServiceAreaFuture.join();
        Map<Long, EvaluationBriefView> evaluationMap = evaluationMapFuture.join();
        Map<Long, MembershipModel> membershipModelMap = membershipModelsFuture.join();
        List<GetBookingRequestItem> result = new ArrayList<>();
        for (BookingRequestModel bookingRequest : bookingRequests) {
            Long bookingRequestId = bookingRequest.getId();
            Long appointmentId = bookingRequest.getAppointmentId();
            Long customerId = bookingRequest.getCustomerId();

            Set<String> specificDates = new LinkedHashSet<>();
            List<String> groomingStaffNames = new ArrayList<>();
            List<RoomAssignRequire> roomAssignRequires = new ArrayList<>();
            List<StaffAssignRequire> staffAssignRequires = new ArrayList<>();
            List<ServiceDetail> serviceDetails = bookingRequest.getServicesList().stream()
                    .collect(Collectors.groupingBy(service1 -> switch (service1.getServiceCase()) {
                        case BOARDING -> service1.getBoarding().getService().getPetId();
                        case DAYCARE -> service1.getDaycare().getService().getPetId();
                        case GROOMING -> service1.getGrooming().getService().getPetId();
                        case EVALUATION -> service1.getEvaluation().getService().getPetId();
                        case DOG_WALKING -> service1.getDogWalking()
                                .getService()
                                .getPetId();
                        case GROUP_CLASS -> service1.getGroupClass()
                                .getService()
                                .getPetId();
                        default -> 0L;
                    }))
                    .entrySet()
                    .stream()
                    .map(entry -> {
                        Long petId = entry.getKey();
                        List<BookingRequestModel.Service> services = entry.getValue();

                        var pet = petMap.get(petId);
                        if (pet == null) {
                            return null;
                        }

                        List<ServiceDetail.Service> list = services.stream()
                                .map(service -> {
                                    switch (service.getServiceCase()) {
                                        case BOARDING -> {
                                            return buildBoarding(
                                                    bookingRequest,
                                                    service.getBoarding(),
                                                    new AbstractMap.SimpleEntry<>(petId, pet.getPetName()),
                                                    serviceMap,
                                                    lodgingMap,
                                                    roomAssignRequires,
                                                    staffAssignRequires);
                                        }
                                        case DAYCARE -> {
                                            // add specific dates
                                            specificDates.addAll(service.getDaycare()
                                                    .getService()
                                                    .getSpecificDatesList());
                                            return buildDaycare(
                                                    service.getDaycare(),
                                                    new AbstractMap.SimpleEntry<>(petId, pet.getPetName()),
                                                    serviceMap,
                                                    staffAssignRequires);
                                        }
                                        case GROOMING -> {
                                            return buildGrooming(
                                                    service.getGrooming(),
                                                    serviceMap,
                                                    staffMap,
                                                    groomingStaffNames,
                                                    groomingAutoAssignMap.getOrDefault(
                                                            appointmentId,
                                                            GroomingAutoAssignDetail.getDefaultInstance()));
                                        }
                                        case EVALUATION -> {
                                            return buildEvaluation(service.getEvaluation());
                                        }
                                        case DOG_WALKING -> {
                                            return buildDogWalking(
                                                    service.getDogWalking(), serviceMap, staffMap, groomingStaffNames);
                                        }
                                        case GROUP_CLASS -> {
                                            return buildGroupClass(
                                                    service.getGroupClass(), serviceMap, staffMap, groomingStaffNames);
                                        }
                                        default -> {
                                            return null;
                                        }
                                    }
                                })
                                .filter(Objects::nonNull)
                                .sorted(Comparator.comparing(
                                        service -> SERVICE_ITEM_TYPE_ORDER.get(service.getServiceItemType())))
                                .toList();

                        PetDetail.Builder petDetailBuilder = PetConverter.INSTANCE.toPetDetail(pet).toBuilder();

                        petCodeMap
                                .getOrDefault(petId, List.of())
                                .forEach(petCode ->
                                        petDetailBuilder.addPetCodes(PetConverter.INSTANCE.toPetCode(petCode)));
                        return ServiceDetail.newBuilder()
                                .setPetDetail(petDetailBuilder.build())
                                .addAllServices(list)
                                .build();
                    })
                    .filter(Objects::nonNull)
                    .sorted(Comparator.comparing(
                            serviceDetail -> serviceDetail.getPetDetail().getId()))
                    .toList();
            // customer
            var customerInfo = customerInfoDTOMap.get(customerId);
            CustomerDetail.Builder customerBuilder = ClientConverter.INSTANCE
                    .toCustomerDetail(
                            customerInfo,
                            hasPetParentAppAccount(
                                    customerInfo == null ? null : accountMap.get(customerInfo.getAccountId())))
                    .toBuilder();
            boolean isHideInfo = needHideInfo(companyId, tokenStaffId);
            if (isHideInfo) {
                customerBuilder
                        .setPhoneNumber(PermissionUtil.phoneNumberConfusion(customerBuilder.getPhoneNumber()))
                        .setEmail(PermissionUtil.emailConfusion(customerBuilder.getEmail()));
            }
            if (Objects.nonNull(customerInfo) && customerInfo.hasBirthday()) {
                customerBuilder.setBirthday(
                        DateTimeConverter.INSTANCE.toDate(customerInfo.getBirthday(), timezoneFuture.join()));
            }
            customerBuilder
                    .setIsNewCustomer(newCustomerIds.contains(customerId))
                    .setLastAlertNote(lastAlertNoteMap
                            .getOrDefault(customerId, AppointmentNoteModel.getDefaultInstance())
                            .getNote())
                    .setCustomerId(customerId)
                    .setBusinessId(businessId)
                    .setOutOfServiceArea(outOfServiceAreaMap.getOrDefault(customerId, false));

            // has request update
            BusinessCustomerService.CustomerHasRequestDTO requestDTO = customerHasUpdateMap.get(customerId);

            // assign require
            AssignRequire assignRequire = AssignRequire.newBuilder()
                    .addAllRoomAssignRequires(roomAssignRequires)
                    .addAllStaffAssignRequire(staffAssignRequires)
                    .build();

            boolean noStartTime = !bookingRequest.hasStartTime();
            BookingRequestDetail.Builder bookingRequestDetailBuilder = BookingRequestConverter.INSTANCE
                    .toBookingRequestDetail(bookingRequest, serviceTypeMap.get(bookingRequestId), noStartTime)
                    .toBuilder();
            if (!CollectionUtils.isEmpty(groomingStaffNames)) {
                bookingRequestDetailBuilder.setStaffName(groomingStaffNames.get(0));
            }
            if (!CollectionUtils.isEmpty(specificDates)) {
                bookingRequestDetailBuilder.addAllSpecificDates(specificDates);
            }

            var pay = bdPayBookingRequestViewMap.get(bookingRequestId);
            if (pay == null || Objects.equals(pay, PayBookingRequestView.getDefaultInstance())) {
                pay = groomingPayBookingRequestViewMap.getOrDefault(
                        appointmentId, PayBookingRequestView.getDefaultInstance());
            }
            List<MembershipModelPublicView> relatedMemberships = new ArrayList<>();
            if (bookingRequest.getSource().equals(BookingRequestModel.Source.MEMBERSHIP)) {
                var membershipModel = membershipModelMap.get(bookingRequest.getSourceId());
                if (membershipModel != null) {
                    relatedMemberships.add(membershipConverter.membershipPublicView(membershipModel));
                }
            }

            result.add(GetBookingRequestItem.newBuilder()
                    .setBookingRequest(bookingRequestDetailBuilder.build())
                    .addAllServiceDetails(serviceDetails)
                    .setCustomerDetail(customerBuilder.build())
                    .setMembershipSubscriptions(membershipSubscriptionsMap.getOrDefault(
                            customerId, MembershipSubscriptionListModel.getDefaultInstance()))
                    .setPay(pay)
                    .setHasRequestUpdate(Objects.nonNull(requestDTO) && requestDTO.hasRequestUpdate())
                    .setAssignRequire(assignRequire)
                    .setIncompleteDetails(buildIncompleteDetails(bookingRequest, serviceMap, evaluationMap))
                    .addAllCustomerPackages(PackageHelper.getPackageViews(
                            customerPackageFuture.join(), customerPackageDetailFuture.join(), customerId))
                    .addAllRelatedMemberships(relatedMemberships)
                    .build());
        }
        return result;
    }

    private Map<Long, EvaluationBriefView> listEvaluation(List<Long> evaluationIds) {
        if (evaluationIds.isEmpty()) {
            return Map.of();
        }
        return evaluationStub
                .getEvaluationListWithEvaluationIds(GetEvaluationListWithEvaluationIdsRequest.newBuilder()
                        .addAllEvaluationIds(evaluationIds)
                        .build())
                .getEvaluationsList()
                .stream()
                .collect(Collectors.toMap(EvaluationBriefView::getId, Function.identity(), (o, n) -> o));
    }

    private IncompleteDetails buildIncompleteDetails(
            BookingRequestModel bookingRequest,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, EvaluationBriefView> evaluationMap) {

        var builder = IncompleteDetails.newBuilder();

        for (var service : bookingRequest.getServicesList()) {
            switch (service.getServiceCase()) {
                case GROOMING -> handleGroomingIncompleteDetails(service, serviceMap, builder);
                case BOARDING -> handleBoardingIncompleteDetails(
                        bookingRequest.getCompanyId(), service, serviceMap, builder);
                case DAYCARE -> handleDaycareIncompleteDetails(
                        bookingRequest.getCompanyId(), service, serviceMap, builder);
                case EVALUATION -> handleEvaluationIncompleteDetails(service, evaluationMap, builder);
                default -> {}
            }
        }

        return builder.build();
    }

    private static void handleGroomingIncompleteDetails(
            BookingRequestModel.Service service,
            Map<Long, ServiceBriefView> serviceMap,
            IncompleteDetails.Builder builder) {
        // 这里需要注意 grooming service detail 就算有 staff 和 time 也要返回，因为这里前端可以修改
        var groomingService = service.getGrooming().getService();
        addIncompleteGroomingService(groomingService, serviceMap, builder);

        // Check grooming addons - if requireStaff is true, requires staffId and startTime
        for (var groomingAddOn : service.getGrooming().getAddonsList()) {
            if (requireStaff(groomingAddOn.getAddOnId(), serviceMap)) {
                addIncompleteGroomingAddon(groomingAddOn, serviceMap, builder);
            }
        }
    }

    private static void addIncompleteGroomingService(
            GroomingServiceDetailModel groomingService,
            Map<Long, ServiceBriefView> serviceMap,
            IncompleteDetails.Builder builder) {
        var serviceBuilder = IncompleteDetails.GroomingService.newBuilder();
        serviceBuilder.setServiceDetail(groomingService);
        var serviceInfo = serviceMap.get(groomingService.getServiceId());
        if (serviceInfo != null) {
            serviceBuilder.setService(serviceInfo);
        }
        builder.addGroomingServices(serviceBuilder.build());
    }

    private static void addIncompleteGroomingAddon(
            GroomingAddOnDetailModel groomingAddOn,
            Map<Long, ServiceBriefView> serviceMap,
            IncompleteDetails.Builder builder) {
        var addonBuilder = IncompleteDetails.GroomingAddon.newBuilder();
        addonBuilder.setAddonDetail(groomingAddOn);
        var serviceInfo = serviceMap.get(groomingAddOn.getAddOnId());
        if (serviceInfo != null) {
            addonBuilder.setService(serviceInfo);
        }
        builder.addGroomingAddons(addonBuilder.build());
    }

    private void handleBoardingIncompleteDetails(
            long companyId,
            BookingRequestModel.Service service,
            Map<Long, ServiceBriefView> serviceMap,
            IncompleteDetails.Builder builder) {
        // Check boarding service - requires lodgingId
        var boardingService = service.getBoarding().getService();
        var missingEvaluation = getMissingEvaluationForBoarding(companyId, boardingService);
        if (!isNormal(boardingService.getLodgingId()) || missingEvaluation != null) {
            addIncompleteBoardingService(boardingService, serviceMap, missingEvaluation, builder);
        }

        // Check boarding addons - if requireStaff is true, requires staffId and startTime
        for (var boardingAddOn : service.getBoarding().getAddonsList()) {
            if (requireStaff(boardingAddOn.getAddOnId(), serviceMap)) {
                addIncompleteBoardingAddon(boardingAddOn, serviceMap, builder);
            }
        }
    }

    @Nullable
    private EvaluationBriefView getMissingEvaluationForBoarding(
            long companyId, BoardingServiceDetailModel boardingService) {
        var petIdToServiceIdToMissingEvaluations = evaluationHelper.listMissingEvaluations(
                companyId, List.of(boardingService.getPetId()), List.of(boardingService.getServiceId()));
        return petIdToServiceIdToMissingEvaluations
                .getOrDefault(boardingService.getPetId(), Map.of())
                .get(boardingService.getServiceId());
    }

    @Nullable
    private EvaluationBriefView getMissingEvaluationForDaycare(
            long companyId, DaycareServiceDetailModel daycareService) {
        var petIdToServiceIdToMissingEvaluations = evaluationHelper.listMissingEvaluations(
                companyId, List.of(daycareService.getPetId()), List.of(daycareService.getServiceId()));
        return petIdToServiceIdToMissingEvaluations
                .getOrDefault(daycareService.getPetId(), Map.of())
                .get(daycareService.getServiceId());
    }

    private static void addIncompleteBoardingService(
            BoardingServiceDetailModel boardingService,
            Map<Long, ServiceBriefView> serviceMap,
            @Nullable EvaluationBriefView missingEvaluation,
            IncompleteDetails.Builder builder) {
        var serviceBuilder = IncompleteDetails.BoardingService.newBuilder();
        serviceBuilder.setServiceDetail(boardingService);
        var serviceInfo = serviceMap.get(boardingService.getServiceId());
        if (serviceInfo != null) {
            serviceBuilder.setService(serviceInfo);
        }
        if (missingEvaluation != null) {
            serviceBuilder.setMissingEvaluation(missingEvaluation);
        }
        builder.addBoardingServices(serviceBuilder.build());
    }

    private static void addIncompleteBoardingAddon(
            BoardingAddOnDetailModel boardingAddOn,
            Map<Long, ServiceBriefView> serviceMap,
            IncompleteDetails.Builder builder) {
        var addonBuilder = IncompleteDetails.BoardingAddon.newBuilder();
        addonBuilder.setAddonDetail(boardingAddOn);
        var serviceInfo = serviceMap.get(boardingAddOn.getAddOnId());
        if (serviceInfo != null) {
            addonBuilder.setService(serviceInfo);
        }
        builder.addBoardingAddons(addonBuilder.build());
    }

    private void handleDaycareIncompleteDetails(
            long companyId,
            BookingRequestModel.Service service,
            Map<Long, ServiceBriefView> serviceMap,
            IncompleteDetails.Builder builder) {
        // Check daycare service - requires evaluation
        var daycareService = service.getDaycare().getService();
        var missingEvaluation = getMissingEvaluationForDaycare(companyId, daycareService);
        if (missingEvaluation != null) {
            addIncompleteDaycareService(daycareService, serviceMap, missingEvaluation, builder);
        }

        // Check daycare addons - if requireStaff is true, requires staffId and startTime
        for (var daycareAddOn : service.getDaycare().getAddonsList()) {
            if (requireStaff(daycareAddOn.getAddOnId(), serviceMap)) {
                addIncompleteDaycareAddon(daycareAddOn, serviceMap, builder);
            }
        }
    }

    private static void addIncompleteDaycareService(
            DaycareServiceDetailModel service,
            Map<Long, ServiceBriefView> serviceMap,
            @Nullable EvaluationBriefView missingEvaluation,
            IncompleteDetails.Builder builder) {
        var serviceBuilder = IncompleteDetails.DaycareService.newBuilder();
        serviceBuilder.setServiceDetail(service);
        var serviceInfo = serviceMap.get(service.getServiceId());
        if (serviceInfo != null) {
            serviceBuilder.setService(serviceInfo);
        }
        if (missingEvaluation != null) {
            serviceBuilder.setMissingEvaluation(missingEvaluation);
        }
        builder.addDaycareServices(serviceBuilder.build());
    }

    private static void addIncompleteDaycareAddon(
            DaycareAddOnDetailModel daycareAddOn,
            Map<Long, ServiceBriefView> serviceMap,
            IncompleteDetails.Builder builder) {
        var addonBuilder = IncompleteDetails.DaycareAddon.newBuilder();
        addonBuilder.setAddonDetail(daycareAddOn);
        var serviceInfo = serviceMap.get(daycareAddOn.getAddOnId());
        if (serviceInfo != null) {
            addonBuilder.setService(serviceInfo);
        }
        builder.addDaycareAddons(addonBuilder.build());
    }

    private static void handleEvaluationIncompleteDetails(
            BookingRequestModel.Service service,
            Map<Long, EvaluationBriefView> evaluationMap,
            IncompleteDetails.Builder builder) {
        // evaluation service requires staffId (OB cannot select)
        var evaluationService = service.getEvaluation().getService();
        var serviceBuilder = IncompleteDetails.EvaluationService.newBuilder();
        serviceBuilder.setServiceDetail(evaluationService);
        var evaluation = evaluationMap.get(evaluationService.getEvaluationId());
        if (evaluation != null) {
            serviceBuilder.setEvaluation(evaluation);
        }
        builder.addEvaluationServices(serviceBuilder.build());
    }

    private static boolean requireStaff(long addOnId, Map<Long, ServiceBriefView> serviceMap) {
        var addon = serviceMap.get(addOnId);
        return addon != null && addon.getRequireDedicatedStaff();
    }

    private static ServiceDetail.Service buildEvaluation(BookingRequestModel.EvaluationService evaluation) {
        ServiceDetail.Service.Builder serviceBuilder = ServiceDetail.Service.newBuilder();

        EvaluationService build = EvaluationService.newBuilder()
                .setService(BookingRequestConverter.INSTANCE.toEvaluationTestDetail(
                        evaluation.getService(), EVALUATION_SERVICE_NAME, EVALUATION_SERVICE_NAME))
                .build();
        return serviceBuilder
                .setEvaluation(build)
                .setServiceItemType(ServiceItemType.EVALUATION)
                .build();
    }

    private static ServiceDetail.Service buildGrooming(
            BookingRequestModel.GroomingService grooming,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, StaffModel> staffMap,
            List<String> groomingStaffNames,
            GroomingAutoAssignDetail groomingAutoAssign) {
        ServiceDetail.Service.Builder serviceBuilder = ServiceDetail.Service.newBuilder();
        String serviceName = BookingRequestConverter.INSTANCE.toServiceName(
                serviceMap, grooming.getService().getServiceId());
        String staffName = getStaffName(staffMap, grooming.getService().getStaffId());
        if (!StringUtils.hasText(staffName)) {
            // if staff is empty, use the auto assign staff
            staffName = getStaffName(staffMap, (long) groomingAutoAssign.getStaffId());
        }
        groomingStaffNames.add(staffName);

        var groomingBuilder = GroomingService.newBuilder()
                .setService(BookingRequestConverter.INSTANCE.toGroomingServiceDetail(
                        grooming.getService(), serviceName, staffName))
                .addAllAddons(grooming.getAddonsList().stream()
                        .map(addon -> {
                            String addOnServiceName =
                                    BookingRequestConverter.INSTANCE.toServiceName(serviceMap, addon.getAddOnId());
                            String addOnStaffName =
                                    getStaffName(staffMap, grooming.getService().getStaffId());
                            if (!StringUtils.hasText(addOnStaffName)) {
                                // if staff is empty, use the auto assign staff
                                addOnStaffName = getStaffName(staffMap, (long) groomingAutoAssign.getStaffId());
                            }
                            return BookingRequestConverter.INSTANCE.toGroomingAddOnDetail(
                                    addon, addOnServiceName, addOnStaffName);
                        })
                        .toList());
        if (!groomingAutoAssign.equals(GroomingAutoAssignDetail.getDefaultInstance())) {
            groomingBuilder.setAutoAssign(groomingAutoAssign);
        }
        return serviceBuilder
                .setGrooming(groomingBuilder)
                .setServiceItemType(ServiceItemType.GROOMING)
                .build();
    }

    private static ServiceDetail.Service buildDogWalking(
            BookingRequestModel.DogWalkingService dogWalking,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, StaffModel> staffMap,
            List<String> groomingStaffNames) {
        var serviceBuilder = ServiceDetail.Service.newBuilder();
        var serviceName = BookingRequestConverter.INSTANCE.toServiceName(
                serviceMap, dogWalking.getService().getServiceId());
        var staffName = getStaffName(staffMap, dogWalking.getService().getStaffId());
        groomingStaffNames.add(staffName);

        // TODO dog walking add on
        var builder = DogWalkingService.newBuilder()
                .setService(BookingRequestConverter.INSTANCE.toDogWalkingServiceDetail(
                        dogWalking.getService(), serviceName, staffName));
        return serviceBuilder
                .setDogWalking(builder)
                .setServiceItemType(ServiceItemType.DOG_WALKING)
                .build();
    }

    private ServiceDetail.Service buildGroupClass(
            BookingRequestModel.GroupClassService groupClass,
            Map<Long, ServiceBriefView> serviceMap,
            Map<Long, StaffModel> staffMap,
            List<String> groomingStaffNames) {
        var service = groupClass.getService();
        var serviceId = service.getServiceId();
        var serviceName = BookingRequestConverter.INSTANCE.toServiceName(serviceMap, serviceId);
        var staffName = getStaffName(staffMap, service.getStaffId());

        groomingStaffNames.add(staffName);

        var instance = GetInstanceResponse.getDefaultInstance();
        try {
            instance = groupClassService.getInstance(GetInstanceRequest.newBuilder()
                    .setId(service.getClassInstanceId())
                    .build());
        } catch (Exception e) {
            log.error(
                    "Failed to fetch group class instance for ID: {}, Service ID: {}",
                    service.getClassInstanceId(),
                    serviceId,
                    e);
        }

        var serviceDetailMap = serviceService.getServiceMap(null, List.of(serviceId));
        var groupClassService = serviceDetailMap.get(serviceId);
        var groupClassServiceDetail = BookingRequestConverter.INSTANCE.toGroupClassServiceDetail(
                service,
                serviceName,
                staffName,
                instance.getGroupClassInstance().getOccurrence());

        var groupClassServiceDetailBuilder = groupClassServiceDetail.toBuilder();
        if (instance.hasGroupClassInstance()) {
            groupClassServiceDetailBuilder.setInstanceName(
                    instance.getGroupClassInstance().getName());
        }
        if (groupClassService != null) {
            groupClassServiceDetailBuilder.setNumSessions(groupClassService.getNumSessions());
        }
        groupClassServiceDetail = groupClassServiceDetailBuilder.build();

        return ServiceDetail.Service.newBuilder()
                .setGroupClass(GroupClassService.newBuilder().setService(groupClassServiceDetail))
                .setServiceItemType(ServiceItemType.GROUP_CLASS)
                .build();
    }

    private static ServiceDetail.Service buildDaycare(
            BookingRequestModel.DaycareService daycare,
            AbstractMap.SimpleEntry<Long, String> pet,
            Map<Long, ServiceBriefView> serviceMap,
            List<StaffAssignRequire> staffAssignRequires) {
        ServiceDetail.Service.Builder serviceBuilder = ServiceDetail.Service.newBuilder();
        DaycareServiceDetailModel service = daycare.getService();
        // add staff assign require
        daycare.getAddonsList().forEach(addOn -> {
            ServiceBriefView addOnService = serviceMap.get(addOn.getAddOnId());
            if (Objects.nonNull(addOnService) && addOnService.getRequireDedicatedStaff()) {
                staffAssignRequires.add(StaffAssignRequire.newBuilder()
                        .setPetId(pet.getKey())
                        .setPetName(pet.getValue())
                        .setServiceId(addOn.getAddOnId())
                        .setDuration(addOnService.getDuration())
                        .setServiceName(BookingRequestConverter.INSTANCE.toServiceName(serviceMap, addOn.getAddOnId()))
                        .addAllSpecificDates(getSpecificDates(service, addOn))
                        .build());
            }
        });

        return serviceBuilder
                .setDaycare(BookingRequestConverter.INSTANCE.toDaycareService(
                        daycare.getService(),
                        daycare.getAddonsList(),
                        daycare.getFeedingsList(),
                        daycare.getMedicationsList(),
                        serviceMap))
                .setServiceItemType(ServiceItemType.DAYCARE)
                .build();
    }

    private static List<String> getSpecificDates(DaycareServiceDetailModel service, DaycareAddOnDetailModel addOn) {
        return addOn.getIsEveryday() ? service.getSpecificDatesList() : addOn.getSpecificDatesList();
    }

    private static ServiceDetail.Service buildBoarding(
            BookingRequestModel bookingRequest,
            BookingRequestModel.BoardingService boarding,
            AbstractMap.SimpleEntry<Long, String> pet,
            Map<Long, ServiceBriefView> serviceMap,
            AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>> lodgingMap,
            List<RoomAssignRequire> roomAssignRequires,
            List<StaffAssignRequire> staffAssignRequires) {
        ServiceDetail.Service.Builder serviceBuilder = ServiceDetail.Service.newBuilder();
        BoardingServiceDetailModel service = boarding.getService();

        roomAssignRequires.add(RoomAssignRequire.newBuilder()
                .setPetId(pet.getKey())
                .setPetName(pet.getValue())
                .setServiceId(service.getServiceId())
                .setStartDate(bookingRequest.getStartDate())
                .setEndDate(bookingRequest.getEndDate())
                .build());

        boarding.getAddonsList().forEach(addOn -> {
            ServiceBriefView addOnService = serviceMap.get(addOn.getAddOnId());
            if (Objects.nonNull(addOnService) && addOnService.getRequireDedicatedStaff()) {
                staffAssignRequires.add(StaffAssignRequire.newBuilder()
                        .setPetId(pet.getKey())
                        .setPetName(pet.getValue())
                        .setServiceId(addOn.getAddOnId())
                        .setDuration(addOnService.getDuration())
                        .setServiceName(BookingRequestConverter.INSTANCE.toServiceName(serviceMap, addOn.getAddOnId()))
                        .addAllSpecificDates(getSpecificDates(service, addOn))
                        .build());
            }
        });

        return serviceBuilder
                .setBoarding(BookingRequestConverter.INSTANCE.toBoardingService(
                        boarding.getService(),
                        boarding.getAddonsList(),
                        boarding.getFeedingsList(),
                        boarding.getMedicationsList(),
                        serviceMap,
                        lodgingMap))
                .setServiceItemType(ServiceItemType.BOARDING)
                .build();
    }

    private static List<String> getSpecificDates(BoardingServiceDetailModel service, BoardingAddOnDetailModel addOn) {
        return addOn.getIsEveryday()
                ? DateUtil.generateAllDatesBetween(service.getStartDate(), service.getEndDate())
                : addOn.getSpecificDatesList();
    }

    public GetBookingRequestResponse getBookingRequestResponse(
            Long companyId, Long businessId, BookingRequestModel bookingRequest, Long tokenStaffId) {
        long customerId = bookingRequest.getCustomerId();
        long appointmentId = bookingRequest.getAppointmentId();
        long bookingRequestId = bookingRequest.getId();

        List<Long> evaluationIds = bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasEvaluation)
                .map(service -> service.getEvaluation().getService().getEvaluationId())
                .filter(CommonUtil::isNormal)
                .distinct()
                .toList();

        List<Long> serviceIds = bookingRequest.getServicesList().stream()
                .flatMap(service -> {
                    List<Long> list = new ArrayList<>();
                    switch (service.getServiceCase()) {
                        case BOARDING -> {
                            list.add(service.getBoarding().getService().getServiceId());
                            list.addAll(service.getBoarding().getAddonsList().stream()
                                    .map(BoardingAddOnDetailModel::getAddOnId)
                                    .toList());
                        }
                        case DAYCARE -> {
                            list.add(service.getDaycare().getService().getServiceId());
                            list.addAll(service.getDaycare().getAddonsList().stream()
                                    .map(DaycareAddOnDetailModel::getAddOnId)
                                    .toList());
                        }
                        case GROOMING -> {
                            list.add(service.getGrooming().getService().getServiceId());
                            list.addAll(service.getGrooming().getAddonsList().stream()
                                    .map(GroomingAddOnDetailModel::getAddOnId)
                                    .toList());
                        }
                        case DOG_WALKING -> {
                            list.add(service.getDogWalking().getService().getServiceId());
                            // TODO dog walking add on
                        }
                        case GROUP_CLASS -> {
                            list.add(service.getGroupClass().getService().getServiceId());
                        }
                        default -> {}
                    }
                    return list.stream();
                })
                .filter(id -> Objects.nonNull(id) && id > 0)
                .distinct()
                .toList();

        List<Long> lodgingIds = bookingRequest.getServicesList().stream()
                .filter(BookingRequestModel.Service::hasBoarding)
                .map(service -> service.getBoarding().getService().getLodgingId())
                .filter(id -> id > 0)
                .distinct()
                .toList();

        // auto assign
        CompletableFuture<GroomingAutoAssignDetail> groomingAutoAssignFuture =
                onlineBookingFutureService.getGroomingAutoAssign(appointmentId);

        // staff detail
        CompletableFuture<Map<Long, StaffModel>> staffDetailFuture =
                onlineBookingFutureService.getStaffMap(bookingRequest, groomingAutoAssignFuture);

        // customer is new
        CompletableFuture<Boolean> isNewCustomerFuture =
                onlineBookingFutureService.getIsNewCustomer(companyId, customerId);

        // customer has request update
        CompletableFuture<Map<Long, BusinessCustomerService.CustomerHasRequestDTO>> customerHasUpdateFuture =
                CompletableFuture.supplyAsync(
                        () -> businessCustomerService.listCustomerHasRequestUpdate(
                                Math.toIntExact(businessId), List.of(customerId)),
                        ThreadPool.getSubmitExecutor());

        // grooming pay
        CompletableFuture<Map<Long, PayBookingRequestView>> groomingPayBookingRequestFuture =
                onlineBookingFutureService.getPrePayCalendarViews(businessId, List.of(appointmentId));

        // BD pay
        CompletableFuture<Map<Long, PayBookingRequestView>> bdPayBookingRequestFuture =
                onlineBookingFutureService.getBDPrePayCalendarViews(businessId, List.of(bookingRequestId));

        // service
        CompletableFuture<Map<Long, ServiceBriefView>> serviceMapFuture =
                onlineBookingFutureService.getServiceMap(companyId, serviceIds);

        CompletableFuture<Map<Long, EvaluationBriefView>> evaluationMapFuture =
                CompletableFuture.supplyAsync(() -> listEvaluation(evaluationIds), ThreadPool.getSubmitExecutor());

        // lodging
        CompletableFuture<AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>>>
                lodgingMapFuture = onlineBookingFutureService.getLodgingMap(lodgingIds);

        // note
        CompletableFuture<AppointmentNoteModel> lastAlertNoteFuture =
                onlineBookingFutureService.getCustomerLastAlertNoteFuture(companyId, customerId);

        // address
        CompletableFuture<AddressDetail> addressDetailFuture = onlineBookingFutureService.getAddressDetail(customerId);

        // discount code
        CompletableFuture<OrderBookingRequestView> orderCalendarViewFuture =
                onlineBookingFutureService.getOrderCalendarView(businessId, companyId, appointmentId, bookingRequestId);

        // service item type
        CompletableFuture<List<ServiceItemType>> serviceTypeFuture =
                onlineBookingFutureService.getServiceTypeFuture(bookingRequest);

        // out of service area
        CompletableFuture<Map<Long, Boolean>> outOfServiceAreaFuture =
                onlineBookingFutureService.getOutOfServiceArea(businessId, List.of(customerId));

        // 原来的拉 customer 太抽象了，我自己拉自己的！
        CompletableFuture<Map<Long, BusinessCustomerModel>> customerDetailFuture =
                onlineBookingFutureService.getBusinessCustomerMap(companyId, List.of(customerId));
        CompletableFuture<Map<Long, AccountModel>> accountFuture = accountService.listAccount(customerDetailFuture);

        // 300 行的代码我拉一坨应该不过分吧, 拉完customer 再拉一下contact
        CompletableFuture<List<BusinessCustomerContactModel>> customerContactsFuture =
                onlineBookingFutureService.getCustomerContact(companyId, customerId);

        CompletableFuture.allOf(
                        staffDetailFuture,
                        isNewCustomerFuture,
                        customerHasUpdateFuture,
                        groomingPayBookingRequestFuture,
                        bdPayBookingRequestFuture,
                        serviceMapFuture,
                        lodgingMapFuture,
                        lastAlertNoteFuture,
                        serviceTypeFuture,
                        evaluationMapFuture,
                        orderCalendarViewFuture,
                        addressDetailFuture,
                        groomingAutoAssignFuture,
                        outOfServiceAreaFuture,
                        customerDetailFuture,
                        accountFuture,
                        customerContactsFuture)
                .join();

        Map<Long, StaffModel> staffMap = staffDetailFuture.join();
        Boolean isNewCustomer = isNewCustomerFuture.join();
        Map<Long, BusinessCustomerService.CustomerHasRequestDTO> customerHasUpdateMap = customerHasUpdateFuture.join();
        Map<Long, PayBookingRequestView> groomingPayBookingRequestViewMap = groomingPayBookingRequestFuture.join();
        Map<Long, PayBookingRequestView> bdPayBookingRequestViewMap = bdPayBookingRequestFuture.join();
        Map<Long, ServiceBriefView> serviceMap = serviceMapFuture.join();
        Map<Long, EvaluationBriefView> evaluationMap = evaluationMapFuture.join();
        AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>> lodgingMap =
                lodgingMapFuture.join();
        AppointmentNoteModel lastAlertNote = lastAlertNoteFuture.join();
        List<ServiceItemType> serviceItemTypes = serviceTypeFuture.join();
        OrderBookingRequestView orderBookingRequestView = orderCalendarViewFuture.join();
        AddressDetail addressDetail = addressDetailFuture.join();
        GroomingAutoAssignDetail groomingAutoAssignDetail = groomingAutoAssignFuture.join();
        Map<Long, Boolean> outOfServiceAreaMap = outOfServiceAreaFuture.join();

        BusinessCustomerService.CustomerHasRequestDTO requestDTO = customerHasUpdateMap.get(customerId);
        CustomerProfileRequestDTO mergedProfile = requestDTO.mergedProfile();
        if (Objects.isNull(mergedProfile) || Objects.isNull(mergedProfile.getClient())) {
            throw ExceptionUtil.bizException(Code.CODE_CUSTOMER_NOT_FOUND);
        }
        Map<Integer, CustomerProfileRequestDTO.PetProfileDTO> petMap = mergedProfile.getPets().stream()
                .collect(Collectors.toMap(CustomerProfileRequestDTO.PetProfileDTO::getPetId, Function.identity()));
        Map<String, GroomingQuestionDTO> questionMap = questionService
                .listByCondition(IBookOnlineQuestionService.ListByConditionParam.builder()
                        .businessId(Math.toIntExact(businessId))
                        .type(null)
                        .build())
                .stream()
                .collect(Collectors.toMap(GroomingQuestionDTO::getKey, Function.identity(), (v1, v2) -> v1));

        Set<String> specificDates = new LinkedHashSet<>();
        List<String> groomingStaffNames = new ArrayList<>();
        List<RoomAssignRequire> roomAssignRequires = new ArrayList<>();
        List<StaffAssignRequire> staffAssignRequires = new ArrayList<>();
        List<ServiceDetail> serviceDetails = bookingRequest.getServicesList().stream()
                .collect(Collectors.groupingBy(service1 -> switch (service1.getServiceCase()) {
                    case BOARDING -> service1.getBoarding().getService().getPetId();
                    case DAYCARE -> service1.getDaycare().getService().getPetId();
                    case GROOMING -> service1.getGrooming().getService().getPetId();
                    case EVALUATION -> service1.getEvaluation().getService().getPetId();
                    case DOG_WALKING -> service1.getDogWalking().getService().getPetId();
                    case GROUP_CLASS -> service1.getGroupClass().getService().getPetId();
                    default -> 0L;
                }))
                .entrySet()
                .stream()
                .map(entry -> {
                    Long petId = entry.getKey();
                    List<BookingRequestModel.Service> services = entry.getValue();

                    List<ServiceDetail.Service> list = services.stream()
                            .map(service -> {
                                switch (service.getServiceCase()) {
                                    case BOARDING -> {
                                        return buildBoarding(
                                                bookingRequest,
                                                service.getBoarding(),
                                                new AbstractMap.SimpleEntry<>(
                                                        petId,
                                                        petMap.get(Math.toIntExact(petId))
                                                                .getPetName()),
                                                serviceMap,
                                                lodgingMap,
                                                roomAssignRequires,
                                                staffAssignRequires);
                                    }
                                    case DAYCARE -> {
                                        // add specific dates
                                        specificDates.addAll(service.getDaycare()
                                                .getService()
                                                .getSpecificDatesList());
                                        return buildDaycare(
                                                service.getDaycare(),
                                                new AbstractMap.SimpleEntry<>(
                                                        petId,
                                                        petMap.get(Math.toIntExact(petId))
                                                                .getPetName()),
                                                serviceMap,
                                                staffAssignRequires);
                                    }
                                    case GROOMING -> {
                                        return buildGrooming(
                                                service.getGrooming(),
                                                serviceMap,
                                                staffMap,
                                                groomingStaffNames,
                                                groomingAutoAssignDetail);
                                    }
                                    case EVALUATION -> {
                                        return buildEvaluation(service.getEvaluation());
                                    }
                                    case DOG_WALKING -> {
                                        return buildDogWalking(
                                                service.getDogWalking(), serviceMap, staffMap, groomingStaffNames);
                                    }
                                    case GROUP_CLASS -> {
                                        return buildGroupClass(
                                                service.getGroupClass(), serviceMap, staffMap, groomingStaffNames);
                                    }
                                    default -> {
                                        return null;
                                    }
                                }
                            })
                            .filter(Objects::nonNull)
                            .sorted(Comparator.comparing(
                                    service -> SERVICE_ITEM_TYPE_ORDER.get(service.getServiceItemType())))
                            .toList();

                    CustomerProfileRequestDTO.PetProfileDTO pet = petMap.get(Math.toIntExact(petId));
                    PetDetail.Builder petDetailBuilder = PetConverter.INSTANCE.toPetDetail(pet).toBuilder();
                    petDetailBuilder.addAllQuestionAnswers(
                            QuestionConverter.INSTANCE.entity2QuestionAnswer(pet.getCustomQuestions(), questionMap));
                    return ServiceDetail.newBuilder()
                            .setPetDetail(petDetailBuilder.build())
                            .addAllServices(list)
                            .build();
                })
                .sorted(Comparator.comparing(
                        serviceDetail -> serviceDetail.getPetDetail().getId()))
                .toList();

        var customerInfo = customerDetailFuture.join().get(customerId);
        Map<Long, AccountModel> accountMap = accountFuture.join();
        // customer
        CustomerDetail.Builder customerBuild = ClientConverter.INSTANCE
                .toCustomerDetail(
                        mergedProfile.getClient(), hasPetParentAppAccount(accountMap.get(customerInfo.getAccountId())))
                .toBuilder();
        customerBuild
                .setIsNewCustomer(isNewCustomer)
                .setLastAlertNote(lastAlertNote.getNote())
                .setCustomerId(customerId)
                .setBusinessId(businessId)
                .setOutOfServiceArea(outOfServiceAreaMap.getOrDefault(customerId, false));
        boolean isHideInfo = needHideInfo(companyId, tokenStaffId);
        if (isHideInfo) {
            customerBuild.setPhoneNumber(PermissionUtil.phoneNumberConfusion(customerBuild.getPhoneNumber()));
            customerBuild.setEmail(PermissionUtil.emailConfusion(customerBuild.getEmail()));
        }
        customerBuild.addAllQuestionAnswers(QuestionConverter.INSTANCE.entity2QuestionAnswer(
                mergedProfile.getClient().getCustomQuestions(), questionMap));

        boolean noStartTime = !bookingRequest.hasStartTime();
        BookingRequestDetail.Builder bookingRequestDetailBuilder =
                BookingRequestConverter.INSTANCE
                        .toBookingRequestDetail(bookingRequest, serviceItemTypes, noStartTime)
                        .toBuilder();
        if (!CollectionUtils.isEmpty(groomingStaffNames)) {
            bookingRequestDetailBuilder.setStaffName(groomingStaffNames.get(0));
        }
        if (!CollectionUtils.isEmpty(specificDates)) {
            bookingRequestDetailBuilder.addAllSpecificDates(specificDates);
        }

        // assign require
        AssignRequire assignRequire = AssignRequire.newBuilder()
                .addAllRoomAssignRequires(roomAssignRequires)
                .addAllStaffAssignRequire(staffAssignRequires)
                .build();

        var pay = bdPayBookingRequestViewMap.get(bookingRequestId);
        if (pay == null || Objects.equals(pay, PayBookingRequestView.getDefaultInstance())) {
            pay = groomingPayBookingRequestViewMap.getOrDefault(
                    appointmentId, PayBookingRequestView.getDefaultInstance());
        }

        var contacts =
                customerContactsFuture.join().stream().filter(Objects::nonNull).toList();

        var emergencyContact = contacts.stream()
                .filter(c -> Objects.equals(CustomerContactEnum.TYPE_EMERGENCY.intValue(), c.getType()))
                .findFirst()
                .map(c -> CustomerDetail.EmergencyContact.newBuilder()
                        .setPhoneNumber(c.getPhoneNumber())
                        .setFirstName(c.getFirstName())
                        .setLastName(c.getLastName())
                        .build())
                .orElse(CustomerDetail.EmergencyContact.getDefaultInstance());

        var pickupContact = contacts.stream()
                .filter(c -> Objects.equals(CustomerContactEnum.TYPE_PICKUP.intValue(), c.getType()))
                .findFirst()
                .map(c -> CustomerDetail.EmergencyContact.newBuilder()
                        .setPhoneNumber(c.getPhoneNumber())
                        .setFirstName(c.getFirstName())
                        .setLastName(c.getLastName())
                        .build())
                .orElse(CustomerDetail.EmergencyContact.getDefaultInstance());

        return GetBookingRequestResponse.newBuilder()
                .setBookingRequest(bookingRequestDetailBuilder.build())
                .addAllServiceDetails(serviceDetails)
                .setCustomerDetail(customerBuild
                        .setEmergencyContact(emergencyContact)
                        .setPickupContact(pickupContact)
                        .build())
                .setPay(pay)
                .setHasRequestUpdate(requestDTO.hasRequestUpdate())
                .setAddress(Objects.nonNull(addressDetail) ? addressDetail : AddressDetail.getDefaultInstance())
                .setAssignRequire(assignRequire)
                .setOrder(orderBookingRequestView)
                .setIncompleteDetails(buildIncompleteDetails(bookingRequest, serviceMap, evaluationMap))
                .build();
    }

    private boolean needHideInfo(Long companyId, Long tokenStaffId) {
        return !permissionHelper.hasPermission(
                companyId, tokenStaffId, PermissionEnums.ACCESS_CLIENT_EMAIL_AND_PHONE_NUMBER);
    }

    private static String getStaffName(Map<Long, StaffModel> staffMap, Long staffId) {
        if (staffId <= 0) {
            return "";
        }
        StaffModel staffModel = staffMap.get(staffId);
        if (Objects.isNull(staffModel)) {
            return "";
        }
        return staffModel.getFirstName() + " " + staffModel.getLastName();
    }

    static boolean hasPetParentAppAccount(AccountModel account) {
        return Objects.nonNull(account)
                && !Objects.equals(account.getStatus(), AccountStatus.ACCOUNT_STATUS_DELETED)
                && (Objects.equals(account.getNamespace().getType(), AccountNamespaceType.ENTERPRISE)
                        || Objects.equals(account.getNamespace().getType(), AccountNamespaceType.COMPANY));
    }
}
