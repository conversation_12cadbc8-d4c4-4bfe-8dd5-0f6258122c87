package com.moego.api.v3.online_booking.controller;

import com.moego.idl.api.online_booking.v1.CustomerAvailabilityServiceGrpc;
import com.moego.idl.api.online_booking.v1.SetCustomerBlockStatusParams;
import com.moego.idl.api.online_booking.v1.SetCustomerBlockStatusResult;
import com.moego.idl.service.online_booking.v1.SetCustomerBlockStatusRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class CustomerAvailabilityController
        extends CustomerAvailabilityServiceGrpc.CustomerAvailabilityServiceImplBase {
    private final com.moego.idl.service.online_booking.v1.CustomerAvailabilityServiceGrpc
                    .CustomerAvailabilityServiceBlockingStub
            customerAvailabilityService;

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ONLINE_BOOKING_SETTINGS)
    public void setCustomerBlockStatus(
            SetCustomerBlockStatusParams request, StreamObserver<SetCustomerBlockStatusResult> responseObserver) {
        customerAvailabilityService.setCustomerBlockStatus(SetCustomerBlockStatusRequest.newBuilder()
                .addAllServiceItemTypes(request.getServiceItemTypesList())
                .addAllCustomerIds(request.getCustomerIdsList())
                .setNeedBlock(request.getNeedBlock())
                .setCompanyId(AuthContext.get().companyId())
                .setStaffId(AuthContext.get().staffId())
                .build());

        responseObserver.onNext(SetCustomerBlockStatusResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
