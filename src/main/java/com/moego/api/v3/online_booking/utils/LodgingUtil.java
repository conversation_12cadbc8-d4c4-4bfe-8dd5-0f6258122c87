package com.moego.api.v3.online_booking.utils;

import com.moego.idl.api.online_booking.v1.AutoAssignResponse;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Nullable;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

public class LodgingUtil {

    /**
     * 收集新增宠物寄养信息
     * @return <key: date, value: petCnt>。时间信息不完整时，返回 null
     */
    @Nullable
    public static Map<String, Integer> calPetCntNeedPerDay(List<AutoAssignResponse.AssignRequire> roomAssignRequires) {
        // 计算每一天需要寄养的 pet 数量
        Map<String, Integer> petCntNeedPerDay = new HashMap<>();
        if (!CollectionUtils.isEmpty(roomAssignRequires)) {
            for (var roomRequire : roomAssignRequires) {
                String startDate = roomRequire.getStartDate();
                String endDate = roomRequire.getEndDate();
                // 时间信息不完整
                if (!StringUtils.hasText(startDate) || !StringUtils.hasText(endDate)) {
                    return null;
                }
                LocalDate start = LocalDate.parse(startDate);
                LocalDate end = LocalDate.parse(endDate);
                for (LocalDate date = start; !date.isAfter(end); date = date.plusDays(1)) {
                    petCntNeedPerDay.merge(date.toString(), 1, Integer::sum);
                }
            }
        }
        return petCntNeedPerDay;
    }
}
