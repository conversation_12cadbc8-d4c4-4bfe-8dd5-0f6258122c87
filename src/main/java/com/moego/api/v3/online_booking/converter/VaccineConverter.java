package com.moego.api.v3.online_booking.converter;

import com.moego.common.enums.CustomerPetEnum;
import com.moego.common.params.VaccineParams;
import com.moego.idl.api.online_booking.v1.PetVaccineComposite;
import com.moego.idl.models.customer.v1.PetVaccineOnlineBookingView;
import com.moego.idl.models.customer.v1.VaccineDef;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.customer.dto.VaccineBindingRecordDto;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/7/10
 */
@Mapper(
        imports = {JsonUtil.class, CustomerPetEnum.class},
        uses = {BaseMapper.class},
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface VaccineConverter {
    VaccineConverter INSTANCE = Mappers.getMapper(VaccineConverter.class);

    List<VaccineParams> def2params(List<VaccineDef> vaccines);

    @Mapping(target = "type", ignore = true)
    @Mapping(target = "vaccineDocument", ignore = true)
    VaccineParams def2params(VaccineDef vaccine);

    List<PetVaccineOnlineBookingView> dto2view(List<VaccineBindingRecordDto> dtoList);

    PetVaccineOnlineBookingView dto2view(VaccineBindingRecordDto dto);

    default List<String> string2list(String str) {
        if (!StringUtils.hasText(str)) {
            return List.of();
        }
        return JsonUtil.toList(str, String.class);
    }

    List<VaccineBindingRecordDto> def2dto(List<VaccineDef> vaccines);

    @Mapping(target = "vaccineName", ignore = true)
    VaccineBindingRecordDto def2dto(VaccineDef vaccine);

    List<PetVaccineComposite> toPetVaccineComposite(List<VaccineBindingRecordDto> list);

    @Mapping(target = "expirationDate", qualifiedByName = "stringToDate")
    PetVaccineComposite toPetVaccineComposite(VaccineBindingRecordDto vaccineBindingRecordDto);
}
