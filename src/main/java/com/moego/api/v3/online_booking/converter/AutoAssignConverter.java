package com.moego.api.v3.online_booking.converter;

import com.moego.idl.api.online_booking.v1.AutoAssignResponse;
import com.moego.idl.api.online_booking.v1.GroomingAutoAssignDetail;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.online_booking.v1.BoardingServiceDetailModel;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.service.online_booking.v1.GetAutoAssignResponse;
import com.moego.server.grooming.dto.AutoAssignDTO;
import java.util.List;
import java.util.Map;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface AutoAssignConverter {

    AutoAssignConverter INSTANCE = Mappers.getMapper(AutoAssignConverter.class);

    GroomingAutoAssignDetail toGroomingAutoAssignDetail(AutoAssignDTO autoAssignDTO);

    default AutoAssignResponse.AssignRequire buildAssignRequire(BoardingServiceDetailModel model) {
        return AutoAssignResponse.AssignRequire.newBuilder()
                .setPetId(model.getPetId())
                .setServiceId(model.getServiceId())
                .setStartDate(model.getStartDate())
                .setEndDate(model.getEndDate())
                .build();
    }

    default List<AutoAssignResponse.AssignRequire> buildAssignRequire(List<BoardingServiceDetailModel> model) {
        if (CollectionUtils.isEmpty(model)) {
            return List.of();
        }
        return model.stream().map(this::buildAssignRequire).toList();
    }

    default AutoAssignResponse.LodgingDetail buildAutoAssignLodgingDetail(
            Long LodgingUnitId,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {
        LodgingUnitModel lodgingUnit = lodgingUnitMap.get(LodgingUnitId);
        LodgingTypeModel lodgingType = lodgingUnit != null ? lodgingTypeMap.get(lodgingUnit.getLodgingTypeId()) : null;
        return AutoAssignResponse.LodgingDetail.newBuilder()
                .setLodgingId(LodgingUnitId)
                .setLodgingUnitName(lodgingUnit != null ? lodgingUnit.getName() : "")
                .setLodgingTypeName(lodgingType != null ? lodgingType.getName() : "")
                .build();
    }

    default List<AutoAssignResponse.LodgingDetail> buildAutoAssignLodgingDetail(
            List<Long> LodgingUnitIds,
            Map<Long, LodgingUnitModel> lodgingUnitMap,
            Map<Long, LodgingTypeModel> lodgingTypeMap) {
        if (CollectionUtils.isEmpty(LodgingUnitIds)) {
            return List.of();
        }
        return LodgingUnitIds.stream()
                .map(id -> buildAutoAssignLodgingDetail(id, lodgingUnitMap, lodgingTypeMap))
                .toList();
    }

    AutoAssignResponse.AssignRequire toAssignRequire(GetAutoAssignResponse.AssignRequire assignRequire);

    AutoAssignResponse.LodgingDetail toLodgingDetail(GetAutoAssignResponse.LodgingDetail lodgingDetail);

    static AutoAssignResponse.StaffDetail toAutoAssignResponseStaffDetail(StaffModel staff) {
        return AutoAssignResponse.StaffDetail.newBuilder()
                .setId(staff.getId())
                .setFirstName(staff.getFirstName())
                .setLastName(staff.getLastName())
                .build();
    }
}
