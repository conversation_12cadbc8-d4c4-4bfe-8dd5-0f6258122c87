package com.moego.api.v3.online_booking.converter;

import com.moego.idl.models.payment.v1.CreditCardModel;
import com.moego.server.payment.dto.CardDTO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2023/7/13
 */
@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CreditCardConverter {
    CreditCardConverter INSTANCE = Mappers.getMapper(CreditCardConverter.class);

    List<CreditCardModel> dto2model(List<CardDTO> dtoList);

    CreditCardModel dto2model(CardDTO dto);
}
