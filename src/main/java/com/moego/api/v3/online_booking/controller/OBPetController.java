package com.moego.api.v3.online_booking.controller;

import com.moego.api.v3.online_booking.converter.PetConverter;
import com.moego.api.v3.online_booking.converter.QuestionConverter;
import com.moego.api.v3.online_booking.converter.VaccineConverter;
import com.moego.api.v3.shared.helper.PetHelper;
import com.moego.common.response.ResponseResult;
import com.moego.idl.api.online_booking.v1.AddOBPetRequest;
import com.moego.idl.api.online_booking.v1.AddOBPetResponse;
import com.moego.idl.api.online_booking.v1.GetOBPetListRequest;
import com.moego.idl.api.online_booking.v1.GetOBPetRequest;
import com.moego.idl.api.online_booking.v1.OBPetInfoResponse;
import com.moego.idl.api.online_booking.v1.OBPetListResponse;
import com.moego.idl.api.online_booking.v1.OBPetServiceGrpc;
import com.moego.idl.api.online_booking.v1.UpdateOBPetRequest;
import com.moego.idl.api.online_booking.v1.UpdateOBPetResponse;
import com.moego.idl.models.customer.v1.PetVaccineOnlineBookingView;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.customer.api.ICustomerProfileRequestService;
import com.moego.server.customer.api.IPetService;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.params.CustomerPetAddParams;
import com.moego.server.grooming.api.IBookOnlineQuestionService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.ob.BookOnlineQuestionSaveDTO;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2023/7/9
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class OBPetController extends OBPetServiceGrpc.OBPetServiceImplBase {

    private final IPetService petService;
    private final IBookOnlineQuestionService questionService;
    private final IGroomingOnlineBookingService onlineBookingService;
    private final ICustomerProfileRequestService profileRequestService;
    private final IBusinessBusinessClient businessBusinessClient;
    private final PetHelper petHelper;

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void addOBPet(AddOBPetRequest request, StreamObserver<AddOBPetResponse> responseObserver) {
        Integer businessId = onlineBookingService
                .mustGetBusinessDTOByOBNameOrDomain(
                        new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()))
                .getBusinessId();
        Long companyId = 0L;
        try {
            companyId =
                    businessBusinessClient.getCompanyIdByBusinessId(businessId).companyId();
        } catch (Exception e) {
            log.error("getCompanyIdByBusinessId with businessId[{}] error", businessId, e);
        }
        Integer customerId = AuthContext.get().getCustomerId();
        CustomerPetAddParams petAddParams = PetConverter.INSTANCE.def2AddParams(request.getPet());

        // C 端新增/更新 vaccine 的场景，都需要经过 B 端 review，这里 set 空，避免直接将 vaccine 写入到 B 端
        petAddParams.setVaccineList(List.of());
        petAddParams.setBusinessId(businessId);
        petAddParams.setCustomerId(customerId);
        ResponseResult<Integer> result = petService.insertCustomerPet(companyId, businessId, petAddParams);
        if (!result.getSuccess()) {
            responseObserver.onNext(AddOBPetResponse.newBuilder().build());
            responseObserver.onCompleted();
            return;
        }

        Integer petId = result.getData();

        // 如果有疫苗信息，通过 profile request 机制处理，需要 B 端 review
        if (!ObjectUtils.isEmpty(request.getVaccineListList())) {
            updateProfileRequest(request, businessId, customerId, petId);
        }

        Map<String, Object> customQuestions = request.getPetQuestionAnswersMap().entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue));
        BookOnlineQuestionSaveDTO questionSaveDTO = new BookOnlineQuestionSaveDTO()
                .setBusinessId(businessId)
                .setCustomerId(customerId)
                .setCompanyId(companyId)
                .setPetCustomQuestionMap(Map.of(result.getData(), customQuestions));
        questionService.saveCustomerQuestionSave(questionSaveDTO);
        responseObserver.onNext(
                AddOBPetResponse.newBuilder().setPetId(result.getData()).build());
        responseObserver.onCompleted();
    }

    private void updateProfileRequest(AddOBPetRequest request, Integer businessId, Integer customerId, Integer petId) {
        var obSetting = onlineBookingService.getOBSetting(businessId);

        profileRequestService.updateCustomerAndProfileRequest(
                CustomerProfileRequestDTO.builder()
                        .businessId(businessId)
                        .companyId(obSetting.getCompanyId())
                        .customerId(customerId)
                        .pets(List.of(new CustomerProfileRequestDTO.PetProfileDTO()
                                .setPetId(petId)
                                .setVaccineList(VaccineConverter.INSTANCE.def2dto(request.getVaccineListList()))))
                        .build(),
                false);
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void updateOBPet(UpdateOBPetRequest request, StreamObserver<UpdateOBPetResponse> responseObserver) {
        Integer businessId = onlineBookingService
                .mustGetBusinessDTOByOBNameOrDomain(
                        new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()))
                .getBusinessId();
        Integer customerId = AuthContext.get().getCustomerId();
        Integer petId = Math.toIntExact(request.getPetId());
        CustomerPetDetailDTO petDetailDTO = petService.getPetWithVaccine(businessId, customerId, petId);
        if (Objects.isNull(petDetailDTO)) {
            throw ExceptionUtil.bizException(Code.CODE_PET_NOT_FOUND);
        }
        Map<String, Object> customQuestions = QuestionConverter.INSTANCE.string2obj(request.getPetQuestionAnswersMap());
        BookOnlineDTO obSetting = onlineBookingService.getOBSetting(businessId);
        profileRequestService.updateCustomerAndProfileRequest(
                CustomerProfileRequestDTO.builder()
                        .businessId(businessId)
                        .companyId(obSetting.getCompanyId())
                        .customerId(customerId)
                        .pets(List.of(PetConverter.INSTANCE
                                .def2PetProfileDTO(request.getPet())
                                .setPetId(petId)
                                .setVaccineList(VaccineConverter.INSTANCE.def2dto(request.getVaccineListList()))
                                .setCustomQuestions(customQuestions)))
                        .build(),
                isNewPet(petId));
        // upsert ob question
        if (!CollectionUtils.isEmpty(request.getPetQuestionAnswersMap())) {
            questionService.upsertCustomerQuestionSave(
                    new BookOnlineQuestionSaveDTO()
                            .setBusinessId(businessId)
                            .setCustomerId(customerId)
                            .setPetCustomQuestionMap(Map.of(petId, customQuestions)),
                    false);
        }
        responseObserver.onNext(
                UpdateOBPetResponse.newBuilder().setPetId(request.getPetId()).build());
        responseObserver.onCompleted();
    }

    private boolean isNewPet(Integer petId) {
        return petHelper.checkNewPets(List.of(petId.longValue())).getOrDefault(petId.longValue(), true);
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void getOBPetInfo(GetOBPetRequest request, StreamObserver<OBPetInfoResponse> responseObserver) {
        Integer businessId = onlineBookingService
                .mustGetBusinessDTOByOBNameOrDomain(
                        new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()))
                .getBusinessId();
        CustomerProfileRequestDTO.PetProfileDTO pet = profileRequestService.getPetProfileWithRequest(
                businessId, AuthContext.get().getCustomerId(), Math.toIntExact(request.getPetId()));
        Map<String, Object> petCustomQuestions = questionService
                .getCustomerLatestQuestionSave(businessId, AuthContext.get().getCustomerId())
                .getPetCustomQuestionMap()
                .get(Math.toIntExact(request.getPetId()));
        Map<String, Object> requestQuestions = pet.getCustomQuestions();
        List<PetVaccineOnlineBookingView> vaccines = VaccineConverter.INSTANCE.dto2view(pet.getVaccineList());
        responseObserver.onNext(OBPetInfoResponse.newBuilder()
                .setPet(PetConverter.INSTANCE.dto2onlineBookingView(pet))
                .addAllVaccineList(CollectionUtils.isEmpty(vaccines) ? List.of() : vaccines)
                .putAllPetQuestionAnswers(
                        QuestionConverter.INSTANCE.mergeCustomQuestion(petCustomQuestions, requestQuestions))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.OB_EXISTING_CLIENT)
    public void getOBPetList(GetOBPetListRequest request, StreamObserver<OBPetListResponse> responseObserver) {
        Integer businessId = onlineBookingService
                .mustGetBusinessDTOByOBNameOrDomain(
                        new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName()))
                .getBusinessId();
        List<CustomerProfileRequestDTO.PetProfileDTO> pets = profileRequestService.getPetsProfileWithRequest(
                businessId, AuthContext.get().getCustomerId());
        Map<Integer, Map<String, Object>> petCustomQuestionMap = questionService
                .getCustomerLatestQuestionSave(businessId, AuthContext.get().getCustomerId())
                .getPetCustomQuestionMap();

        var petIds = pets.stream()
                .map(CustomerProfileRequestDTO.PetProfileDTO::getPetId)
                .map(Integer::longValue)
                .collect(Collectors.toSet());
        var petIdToIsNew = petHelper.checkNewPets(petIds);

        responseObserver.onNext(OBPetListResponse.newBuilder()
                .addAllPets(pets.stream()
                        .map(pet -> {
                            List<PetVaccineOnlineBookingView> vaccines =
                                    VaccineConverter.INSTANCE.dto2view(pet.getVaccineList());
                            var isNewPet =
                                    petIdToIsNew.getOrDefault(pet.getPetId().longValue(), true);
                            return OBPetInfoResponse.newBuilder()
                                    .setPet(PetConverter.INSTANCE.dto2onlineBookingView(pet).toBuilder()
                                            .setIsNewPet(isNewPet)
                                            .build())
                                    .addAllVaccineList(CollectionUtils.isEmpty(vaccines) ? List.of() : vaccines)
                                    .putAllPetQuestionAnswers(QuestionConverter.INSTANCE.mergeCustomQuestion(
                                            petCustomQuestionMap.get(pet.getPetId()), pet.getCustomQuestions()))
                                    .build();
                        })
                        .toList())
                .build());
        responseObserver.onCompleted();
    }
}
