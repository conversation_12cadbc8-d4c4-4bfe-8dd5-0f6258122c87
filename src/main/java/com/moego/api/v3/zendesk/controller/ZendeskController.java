package com.moego.api.v3.zendesk.controller;

import com.auth0.jwt.JWT;
import com.auth0.jwt.algorithms.Algorithm;
import com.moego.api.v3.account.helper.AccountHelper;
import com.moego.idl.api.zendesk.v1.GetZendeskJwtRequest;
import com.moego.idl.api.zendesk.v1.GetZendeskJwtResponse;
import com.moego.idl.api.zendesk.v1.ZendeskServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.util.Env;
import io.grpc.stub.StreamObserver;
import java.util.Date;
import java.util.Map;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.env.Environment;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class ZendeskController extends ZendeskServiceGrpc.ZendeskServiceImplBase {

    private final AccountHelper accountHelper;
    private final Environment env;

    @Value("${zendesk.jwtKid}")
    private String jwtKid;

    @Value("${zendesk.jwtSecret}")
    private String jwtSecret;

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getZendeskJwt(GetZendeskJwtRequest request, StreamObserver<GetZendeskJwtResponse> responseObserver) {
        var accountId = AuthContext.get().accountId();
        var account = accountHelper.getAccountById(accountId);

        // First name and last name may be empty
        var fullName = Stream.of(account.getFirstName(), account.getLastName())
                .filter(s -> !s.isEmpty())
                .reduce((s1, s2) -> String.format("%s %s", s1, s2))
                .orElse(account.getEmail());
        var email = account.getEmail();
        // If user is not on production env:
        // - Prepend "env_name" to user's email
        // - Append the "[env_name]" to user's name (keep the behaviour in sync with intercom)
        if (!env.matchesProfiles(Env.PROD.getValue())) {
            var currentEnv = env.getActiveProfiles()[0];
            fullName = String.format("%s[%s]", fullName, currentEnv);
            email = String.format("%s-%s", currentEnv, email);
        }

        var token = "";
        try {
            // https://support.zendesk.com/hc/en-us/articles/*************-Authenticating-end-users-for-messaging
            Algorithm algorithm = Algorithm.HMAC256(jwtSecret);
            Map<String, Object> header = Map.of("kid", this.jwtKid);
            token = JWT.create()
                    .withHeader(header)
                    .withExpiresAt(new Date(System.currentTimeMillis() + 3600_000))
                    .withClaim("external_id", accountId.toString())
                    .withClaim("email", email)
                    .withClaim("email_verified", true)
                    .withClaim("name", fullName)
                    .withClaim("scope", "user")
                    .sign(algorithm);
        } catch (Exception e) {
            log.error("Error creating JWT token", e);
        }

        var response = GetZendeskJwtResponse.newBuilder().setJwt(token).build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }
}
