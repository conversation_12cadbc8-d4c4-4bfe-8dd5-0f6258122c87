package com.moego.api.v3.grooming;

import com.google.common.collect.Lists;
import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.api.v3.grooming.converter.GroomingReportConverter;
import com.moego.idl.api.grooming.v1.BatchDeleteGroomingReportCardParams;
import com.moego.idl.api.grooming.v1.BatchDeleteGroomingReportCardResult;
import com.moego.idl.api.grooming.v1.BatchSendGroomingReportCardParams;
import com.moego.idl.api.grooming.v1.BatchSendGroomingReportCardResult;
import com.moego.idl.api.grooming.v1.GroomingReportCardDef;
import com.moego.idl.api.grooming.v1.GroomingReportServiceGrpc;
import com.moego.idl.api.grooming.v1.ListGroomingReportCardParams;
import com.moego.idl.api.grooming.v1.ListGroomingReportCardResult;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.grooming.v1.GroomingReportStatus;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.GetAppointmentListRequest;
import com.moego.idl.service.business_customer.v1.BatchGetPetInfoRequest;
import com.moego.idl.service.business_customer.v1.BusinessCustomerPetServiceGrpc;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.server.grooming.client.IGroomingGroomingReportClient;
import com.moego.server.grooming.dto.groomingreport.GroomingReportDTO;
import com.moego.server.grooming.params.groomingreport.BatchSendGroomingReportParams;
import com.moego.server.grooming.params.groomingreport.GetGroomingReportCardListParams;
import com.moego.server.grooming.params.groomingreport.GroomingIdListParams;
import com.moego.server.message.api.IGroomingReportSendService;
import com.moego.server.message.dto.GroomingReportSendLogDTO;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class GroomingReportController extends GroomingReportServiceGrpc.GroomingReportServiceImplBase {

    private final IGroomingGroomingReportClient groomingGroomingReportClient;
    private final IGroomingReportSendService groomingReportSendService;
    private final BusinessCustomerPetServiceGrpc.BusinessCustomerPetServiceBlockingStub businessCustomerPetService;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentServiceBlockingStub;
    private final MigrateHelper migrateHelper;

    @Override
    @Auth(AuthType.COMPANY)
    public void listGroomingReportCard(
            ListGroomingReportCardParams request, StreamObserver<ListGroomingReportCardResult> responseObserver) {

        // report list
        var listGroomingReportCardParams = new GetGroomingReportCardListParams();
        listGroomingReportCardParams.setCompanyId(request.getCompanyId());
        listGroomingReportCardParams.setBusinessId(request.getBusinessId());
        if (request.hasStartDate() && request.hasEndDate()) {
            String startDateStr = DateTimeConverter.INSTANCE
                    .fromGoogleDate(request.getStartDate())
                    .toString();
            String endDateStr = DateTimeConverter.INSTANCE
                    .fromGoogleDate(request.getEndDate())
                    .toString();
            listGroomingReportCardParams.setStartDate(startDateStr);
            listGroomingReportCardParams.setEndDate(endDateStr);
        }
        if (request.hasPetId()) {
            listGroomingReportCardParams.setPetId(request.getPetId());
        }
        if (request.hasStatus()) {
            listGroomingReportCardParams.setStatus(
                    GroomingReportConverter.INSTANCE.convertToGroomingReportStatusEnum(request.getStatus()));
        }

        List<GroomingReportDTO> groomingReportDTOS =
                groomingGroomingReportClient.listGroomingReportCardByFilter(listGroomingReportCardParams);
        if (groomingReportDTOS.isEmpty()) {
            responseObserver.onNext(ListGroomingReportCardResult.newBuilder()
                    .setPagination(PaginationResponse.newBuilder()
                            .setTotal(0)
                            .setPageNum(request.getPagination().getPageNum())
                            .setPageSize(request.getPagination().getPageSize())
                            .build())
                    .build());
            responseObserver.onCompleted();
            return;
        }

        // pet list
        List<Long> petIds = groomingReportDTOS.stream()
                .map(groomingReportDTO -> groomingReportDTO.getPetId().longValue())
                .distinct()
                .toList();
        List<BusinessCustomerPetInfoModel> petInfoList = businessCustomerPetService
                .batchGetPetInfo(
                        BatchGetPetInfoRequest.newBuilder().addAllIds(petIds).build())
                .getPetsList();
        Map<Long, BusinessCustomerPetInfoModel> petInfoMap = petInfoList.stream()
                .collect(Collectors.toMap(BusinessCustomerPetInfoModel::getId, Function.identity()));

        // appointment list
        List<Long> appointmentIds = groomingReportDTOS.stream()
                .map(groomingReportDTO -> groomingReportDTO.getGroomingId().longValue())
                .distinct()
                .toList();
        List<AppointmentModel> appointmentsList = appointmentServiceBlockingStub
                .getAppointmentList(GetAppointmentListRequest.newBuilder()
                        .setCompanyId(request.getCompanyId())
                        .addAllAppointmentId(appointmentIds)
                        .build())
                .getAppointmentsList();
        Map<Long, AppointmentModel> appointmentMap =
                appointmentsList.stream().collect(Collectors.toMap(AppointmentModel::getId, Function.identity()));

        List<GroomingReportCardDef> reportConfigs;
        if (request.hasStatus() && request.getStatus().equals(GroomingReportStatus.GROOMING_REPORT_STATUS_SENT)) {
            // 查询report last send log，key 为 groomingId
            Map<Integer, List<GroomingReportSendLogDTO>> groomingLastReportSendLogsMap =
                    groomingReportSendService.getGroomingLastReportSendLogsMap(new GroomingIdListParams(
                            Math.toIntExact(request.getBusinessId()),
                            groomingReportDTOS.stream()
                                    .map(GroomingReportDTO::getGroomingId)
                                    .distinct()
                                    .toList()));

            List<GroomingReportSendLogDTO> allSendLogs = groomingLastReportSendLogsMap.values().stream()
                    .flatMap(List::stream)
                    .toList();

            reportConfigs = GroomingReportConverter.INSTANCE.convertToGroomingReportCardDefList(
                    groomingReportDTOS, petInfoMap, appointmentMap, allSendLogs);
        } else {
            reportConfigs = GroomingReportConverter.INSTANCE.convertToGroomingReportCardDefList(
                    groomingReportDTOS, petInfoMap, appointmentMap);
        }

        // 对reportConfigs进行排序
        reportConfigs = sortGroomingReportCardDefList(reportConfigs, request.getStatus());

        int pageSize = request.getPagination().getPageSize();
        int pageNum = request.getPagination().getPageNum();

        List<List<GroomingReportCardDef>> partition = Lists.partition(reportConfigs, pageSize);
        List<GroomingReportCardDef> itemsPage =
                (pageNum > 0 && pageNum <= partition.size()) ? partition.get(pageNum - 1) : List.of();

        responseObserver.onNext(ListGroomingReportCardResult.newBuilder()
                .addAllGroomingReportCards(itemsPage)
                .setPagination(PaginationResponse.newBuilder()
                        .setPageNum(pageNum)
                        .setPageSize(pageSize)
                        .setTotal(reportConfigs.size())
                        .build())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void batchDeleteGroomingReportCard(
            BatchDeleteGroomingReportCardParams request,
            StreamObserver<BatchDeleteGroomingReportCardResult> responseObserver) {
        groomingGroomingReportClient.batchDeleteGroomingReportCard(
                Math.toIntExact(request.getBusinessId()),
                request.getReportCardIdsList().stream().map(Long::intValue).toList());
        responseObserver.onNext(BatchDeleteGroomingReportCardResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void batchSendGroomingReportCard(
            BatchSendGroomingReportCardParams request,
            StreamObserver<BatchSendGroomingReportCardResult> responseObserver) {
        if (request.getReportCardIdsList().isEmpty()) {
            responseObserver.onNext(BatchSendGroomingReportCardResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        // 生成对应report uuid
        groomingGroomingReportClient.batchGenerateGroomingReportUuid(
                Math.toIntExact(request.getBusinessId()),
                request.getReportCardIdsList().stream().map(Long::intValue).toList());

        BatchSendGroomingReportParams batchSendRequest = new BatchSendGroomingReportParams();
        batchSendRequest.setCompanyId(Math.toIntExact(request.getCompanyId()));
        batchSendRequest.setBusinessId(Math.toIntExact(request.getBusinessId()));
        batchSendRequest.setReportIds(
                request.getReportCardIdsList().stream().map(Long::intValue).toList());
        batchSendRequest.setStaffId(Math.toIntExact(request.getStaffId()));
        batchSendRequest.setSendMethod((byte) request.getSendMethod().getNumber());
        groomingReportSendService.batchSendGroomingReportCard(batchSendRequest);
        responseObserver.onNext(BatchSendGroomingReportCardResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    private List<GroomingReportCardDef> sortGroomingReportCardDefList(
            List<GroomingReportCardDef> groomingReportCardDefs, GroomingReportStatus status) {
        return groomingReportCardDefs.stream()
                .sorted((a, b) -> {
                    if (status != null && status.equals(GroomingReportStatus.GROOMING_REPORT_STATUS_SENT)) {
                        // 按发送时间降序排序，处理空值情况
                        if (!a.hasSendTime() && !b.hasSendTime()) {
                            return 0;
                        }
                        if (!a.hasSendTime()) {
                            return 1;
                        }
                        if (!b.hasSendTime()) {
                            return -1;
                        }
                        return Long.compare(
                                b.getSendTime().getSeconds(), a.getSendTime().getSeconds());
                    } else {
                        // 按更新时间降序排序，处理空值情况
                        if (!a.hasUpdateTime() && !b.hasUpdateTime()) {
                            return 0;
                        }
                        if (!a.hasUpdateTime()) {
                            return 1;
                        }
                        if (!b.hasUpdateTime()) {
                            return -1;
                        }
                        return Long.compare(
                                b.getUpdateTime().getSeconds(),
                                a.getUpdateTime().getSeconds());
                    }
                })
                .toList();
    }
}
