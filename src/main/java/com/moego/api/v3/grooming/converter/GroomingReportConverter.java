package com.moego.api.v3.grooming.converter;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.Timestamp;
import com.moego.api.v3.appointment.converter.DateTimeConverter;
import com.moego.common.enums.groomingreport.GroomingReportStatusEnum;
import com.moego.idl.api.grooming.v1.GroomingReportCardDef;
import com.moego.idl.api.grooming.v1.ReportCardPetOverview;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.grooming.v1.GroomingReportSendMethod;
import com.moego.idl.models.grooming.v1.GroomingReportStatus;
import com.moego.server.grooming.dto.groomingreport.GroomingReportDTO;
import com.moego.server.message.dto.GroomingReportSendLogDTO;
import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;
import org.springframework.util.StringUtils;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface GroomingReportConverter {

    GroomingReportConverter INSTANCE = Mappers.getMapper(GroomingReportConverter.class);

    default List<GroomingReportCardDef> convertToGroomingReportCardDefList(
            List<GroomingReportDTO> groomingReportDTOList,
            Map<Long, BusinessCustomerPetInfoModel> petInfoMap,
            Map<Long, AppointmentModel> appointmentMap,
            List<GroomingReportSendLogDTO> groomingReportSendLogs) {

        Map<Integer, GroomingReportSendLogDTO> sendLogMap = groomingReportSendLogs.stream()
                .collect(Collectors.toMap(
                        GroomingReportSendLogDTO::getReportId,
                        Function.identity(),
                        (existing, replacement) -> existing));

        return groomingReportDTOList.stream()
                .map(groomingReportDTO -> {
                    GroomingReportSendLogDTO sendLog = sendLogMap.get(groomingReportDTO.getId());
                    if (sendLog != null) {
                        return toGroomingReportCardDef(groomingReportDTO, petInfoMap, appointmentMap, sendLog);
                    } else {
                        return toGroomingReportCardDef(groomingReportDTO, petInfoMap, appointmentMap);
                    }
                })
                .toList();
    }

    default List<GroomingReportCardDef> convertToGroomingReportCardDefList(
            List<GroomingReportDTO> groomingReportDTOList,
            Map<Long, BusinessCustomerPetInfoModel> petInfoMap,
            Map<Long, AppointmentModel> appointmentMap) {

        return groomingReportDTOList.stream()
                .map(groomingReportDTO -> toGroomingReportCardDef(groomingReportDTO, petInfoMap, appointmentMap))
                .toList();
    }

    default GroomingReportCardDef toGroomingReportCardDef(
            GroomingReportDTO groomingReportDTO,
            Map<Long, BusinessCustomerPetInfoModel> petInfoMap,
            Map<Long, AppointmentModel> appointmentMap,
            GroomingReportSendLogDTO sendLog) {

        GroomingReportCardDef baseReportCard = toGroomingReportCardDef(groomingReportDTO, petInfoMap, appointmentMap);

        GroomingReportCardDef.Builder builder = baseReportCard.toBuilder();

        if (sendLog.getSentTime() != null) {
            long sentTimeMillis = sendLog.getSentTime().getTime();
            builder.setSendTime(
                    Timestamp.newBuilder().setSeconds(sentTimeMillis / 1000).build());
        }

        if (sendLog.getSendingMethod() != null) {
            GroomingReportSendMethod sendMethod = GroomingReportSendMethod.forNumber(
                    sendLog.getSendingMethod().intValue());
            if (sendMethod != null) {
                builder.setSendMethod(sendMethod);
            }
        }

        return builder.build();
    }

    default GroomingReportCardDef toGroomingReportCardDef(
            GroomingReportDTO groomingReportDTO,
            Map<Long, BusinessCustomerPetInfoModel> petInfoMap,
            Map<Long, AppointmentModel> appointmentMap) {

        BusinessCustomerPetInfoModel petInfo =
                petInfoMap.get(groomingReportDTO.getPetId().longValue());

        ReportCardPetOverview petOverview = buildReportCardPetOverview(petInfo);

        GroomingReportCardDef.Builder reportCardBuilder = GroomingReportCardDef.newBuilder()
                .setReportCardId(groomingReportDTO.getId().longValue())
                .setAppointmentId(groomingReportDTO.getGroomingId().longValue())
                .setPetOverview(petOverview);

        if (groomingReportDTO.getUuid() != null) {
            reportCardBuilder.setUuid(groomingReportDTO.getUuid());
        }

        // set service date
        AppointmentModel appointment =
                appointmentMap.get(groomingReportDTO.getGroomingId().longValue());
        if (appointment != null) {
            reportCardBuilder.setServiceDate(
                    DateTimeConverter.INSTANCE.toGoogleDate(LocalDate.parse(appointment.getAppointmentDate())));
        }

        // 计算 media 数量
        int mediaCount = calculateMediaCount(groomingReportDTO.getContentJson());
        reportCardBuilder.setMediaCount(mediaCount);
        if (groomingReportDTO.getUpdateTime() != null) {
            reportCardBuilder.setUpdateTime(Timestamp.newBuilder()
                    .setSeconds(groomingReportDTO.getUpdateTime())
                    .build());
        }

        return reportCardBuilder.build();
    }

    default ReportCardPetOverview buildReportCardPetOverview(BusinessCustomerPetInfoModel petInfo) {
        ReportCardPetOverview.Builder petOverviewBuilder = ReportCardPetOverview.newBuilder();

        if (petInfo != null) {
            petOverviewBuilder
                    .setPetId(petInfo.getId())
                    .setPetName(petInfo.getPetName())
                    .setPetType(petInfo.getPetType())
                    .setAvatarPath(petInfo.getAvatarPath());
        }

        return petOverviewBuilder.build();
    }

    default GroomingReportStatusEnum convertToGroomingReportStatusEnum(GroomingReportStatus status) {
        if (status == null) {
            return null;
        }

        return switch (status) {
            case GROOMING_REPORT_STATUS_CREATED -> GroomingReportStatusEnum.created;
            case GROOMING_REPORT_STATUS_DRAFT -> GroomingReportStatusEnum.draft;
            case GROOMING_REPORT_STATUS_READY -> GroomingReportStatusEnum.submitted;
            case GROOMING_REPORT_STATUS_SENT -> GroomingReportStatusEnum.sent;
            case GROOMING_REPORT_STATUS_UNSPECIFIED, UNRECOGNIZED -> null;
        };
    }

    /**
     * 计算media数量（已上传的图片数量）
     * 从contentJson中解析showcase字段，统计非空字符串的数量
     */
    default int calculateMediaCount(String contentJson) {
        if (!StringUtils.hasText(contentJson)) {
            return 0;
        }

        try {
            ObjectMapper objectMapper = new ObjectMapper();
            JsonNode rootNode = objectMapper.readTree(contentJson);
            JsonNode showcaseNode = rootNode.path("showcase");

            if (showcaseNode.isArray()) {
                int count = 0;
                for (JsonNode imageNode : showcaseNode) {
                    String imageUrl = imageNode.asText();
                    // 如果不是空字符串，则计数
                    if (StringUtils.hasText(imageUrl)) {
                        count++;
                    }
                }
                return count;
            }
        } catch (JsonProcessingException e) {
            return 0;
        }
        return 0;
    }
}
