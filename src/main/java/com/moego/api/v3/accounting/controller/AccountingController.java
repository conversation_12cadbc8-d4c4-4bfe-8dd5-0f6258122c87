package com.moego.api.v3.accounting.controller;

import com.moego.api.v3.accounting.service.AccountingService;
import com.moego.idl.api.accounting.v1.AccountingServiceGrpc;
import com.moego.idl.api.accounting.v1.AddBusinessesParams;
import com.moego.idl.api.accounting.v1.AddBusinessesResult;
import com.moego.idl.api.accounting.v1.GetAuthTokenParams;
import com.moego.idl.api.accounting.v1.GetAuthTokenResult;
import com.moego.idl.api.accounting.v1.GetBusinessesParams;
import com.moego.idl.api.accounting.v1.GetBusinessesResult;
import com.moego.idl.api.accounting.v1.GetOnboardingStatusParams;
import com.moego.idl.api.accounting.v1.GetOnboardingStatusResult;
import com.moego.idl.api.accounting.v1.GetVisibilityParams;
import com.moego.idl.api.accounting.v1.GetVisibilityResult;
import com.moego.idl.api.accounting.v1.RemoveBusinessesParams;
import com.moego.idl.api.accounting.v1.RemoveBusinessesResult;
import com.moego.idl.api.accounting.v1.SetBusinessesParams;
import com.moego.idl.api.accounting.v1.SetBusinessesResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;

@GrpcService
@Slf4j
public class AccountingController extends AccountingServiceGrpc.AccountingServiceImplBase {
    @Autowired
    private AccountingService accountingService;

    @Override
    @Auth(AuthType.COMPANY)
    public void getVisibility(GetVisibilityParams request, StreamObserver<GetVisibilityResult> responseObserver) {
        var response = accountingService.getVisibility(AuthContext.get().companyId());
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getOnboardingStatus(
            GetOnboardingStatusParams request, StreamObserver<GetOnboardingStatusResult> responseObserver) {
        var response = accountingService.getOnboardingStatus(AuthContext.get().companyId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getBusinesses(GetBusinessesParams request, StreamObserver<GetBusinessesResult> responseObserver) {
        var response = accountingService.getBusinesses(AuthContext.get().companyId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void setBusinesses(SetBusinessesParams request, StreamObserver<SetBusinessesResult> responseObserver) {
        var response = accountingService.setBusinesses(AuthContext.get().companyId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void addBusinesses(AddBusinessesParams request, StreamObserver<AddBusinessesResult> responseObserver) {
        var response = accountingService.addBusinesses(AuthContext.get().companyId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void removeBusinesses(
            RemoveBusinessesParams request, StreamObserver<RemoveBusinessesResult> responseObserver) {
        var response = accountingService.removeBusinesses(AuthContext.get().companyId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getAuthToken(GetAuthTokenParams request, StreamObserver<GetAuthTokenResult> responseObserver) {
        var response = accountingService.getAuthToken(
                AuthContext.get().companyId(), AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }
}
