package com.moego.api.v3.marketing.converter;

import com.moego.idl.api.marketing.v1.ChangeStatusRequest;
import com.moego.idl.api.marketing.v1.ChangeStatusResponse;
import com.moego.idl.api.marketing.v1.CheckDiscountCodeRequest;
import com.moego.idl.api.marketing.v1.CheckDiscountCodeResponse;
import com.moego.idl.api.marketing.v1.CheckDiscountCodeValidForCustomerRequest;
import com.moego.idl.api.marketing.v1.CheckDiscountCodeValidForCustomerResponse;
import com.moego.idl.api.marketing.v1.CreateDiscountCodeRequest;
import com.moego.idl.api.marketing.v1.CreateDiscountCodeResponse;
import com.moego.idl.api.marketing.v1.EditDiscountCodeRequest;
import com.moego.idl.api.marketing.v1.EditDiscountCodeResponse;
import com.moego.idl.api.marketing.v1.GenerateDiscountCodeResponse;
import com.moego.idl.api.marketing.v1.GetBusinessDiscountCodeConfigRequest;
import com.moego.idl.api.marketing.v1.GetBusinessDiscountCodeConfigResponse;
import com.moego.idl.api.marketing.v1.GetDiscountCodeListRequest;
import com.moego.idl.api.marketing.v1.GetDiscountCodeListResponse;
import com.moego.idl.api.marketing.v1.GetDiscountCodeLogListRequest;
import com.moego.idl.api.marketing.v1.GetDiscountCodeLogListResponse;
import com.moego.idl.api.marketing.v1.GetDiscountCodeLogOverviewRequest;
import com.moego.idl.api.marketing.v1.GetDiscountCodeLogOverviewResponse;
import com.moego.idl.api.marketing.v1.GetDiscountCodeRequest;
import com.moego.idl.api.marketing.v1.GetDiscountCodeResponse;
import com.moego.idl.service.marketing.v1.ChangeStatusInput;
import com.moego.idl.service.marketing.v1.ChangeStatusOutput;
import com.moego.idl.service.marketing.v1.CheckDiscountCodeInput;
import com.moego.idl.service.marketing.v1.CheckDiscountCodeOutput;
import com.moego.idl.service.marketing.v1.CheckDiscountCodeValidForCustomerInput;
import com.moego.idl.service.marketing.v1.CheckDiscountCodeValidForCustomerOutput;
import com.moego.idl.service.marketing.v1.CreateDiscountCodeInput;
import com.moego.idl.service.marketing.v1.CreateDiscountCodeOutput;
import com.moego.idl.service.marketing.v1.EditDiscountCodeInput;
import com.moego.idl.service.marketing.v1.EditDiscountCodeOutput;
import com.moego.idl.service.marketing.v1.GenerateDiscountCodeOutput;
import com.moego.idl.service.marketing.v1.GetBusinessDiscountCodeConfigInput;
import com.moego.idl.service.marketing.v1.GetBusinessDiscountCodeConfigOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeListInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeListOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeLogListInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeLogListOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeLogOverviewInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeLogOverviewOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeOutput;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface DiscountConverter {

    DiscountConverter INSTANCE = Mappers.getMapper(DiscountConverter.class);

    GenerateDiscountCodeResponse toResponse(GenerateDiscountCodeOutput output);

    CheckDiscountCodeInput toInput(CheckDiscountCodeRequest request, Long businessId, Long companyId);

    CheckDiscountCodeResponse toResponse(CheckDiscountCodeOutput output);

    CreateDiscountCodeResponse toResponse(CreateDiscountCodeOutput output);

    CreateDiscountCodeInput toInput(CreateDiscountCodeRequest request, Long businessId, Long companyId);

    EditDiscountCodeResponse toResponse(EditDiscountCodeOutput output);

    EditDiscountCodeInput toInput(EditDiscountCodeRequest request, Long businessId, Long companyId);

    GetDiscountCodeInput toInput(GetDiscountCodeRequest request, Long businessId, Long companyId);

    GetDiscountCodeResponse toResponse(GetDiscountCodeOutput output);

    GetDiscountCodeListInput toInput(GetDiscountCodeListRequest request, Long businessId, Long companyId);

    GetDiscountCodeListResponse toResponse(GetDiscountCodeListOutput output);

    GetDiscountCodeLogOverviewInput toInput(GetDiscountCodeLogOverviewRequest request, Long businessId, Long companyId);

    GetDiscountCodeLogOverviewResponse toResponse(GetDiscountCodeLogOverviewOutput output);

    GetDiscountCodeLogListInput toInput(GetDiscountCodeLogListRequest request, Long businessId, Long companyId);

    GetDiscountCodeLogListResponse toResponse(GetDiscountCodeLogListOutput output);

    ChangeStatusInput toInput(ChangeStatusRequest request, Long businessId, Long companyId, Long staffId);

    ChangeStatusResponse toResponse(ChangeStatusOutput output);

    CheckDiscountCodeValidForCustomerInput toInput(CheckDiscountCodeValidForCustomerRequest request, Long customerId);

    CheckDiscountCodeValidForCustomerResponse toResponse(CheckDiscountCodeValidForCustomerOutput output);

    GetBusinessDiscountCodeConfigInput toInput(GetBusinessDiscountCodeConfigRequest request);

    GetBusinessDiscountCodeConfigResponse toResponse(GetBusinessDiscountCodeConfigOutput output);
}
