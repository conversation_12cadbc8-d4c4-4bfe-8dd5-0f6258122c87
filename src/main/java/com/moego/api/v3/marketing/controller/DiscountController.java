package com.moego.api.v3.marketing.controller;

import static com.moego.lib.common.auth.AuthType.COMPANY;
import static com.moego.lib.common.auth.AuthType.OB;

import com.google.protobuf.Empty;
import com.moego.api.v3.marketing.converter.DiscountConverter;
import com.moego.idl.api.marketing.v1.ChangeStatusRequest;
import com.moego.idl.api.marketing.v1.ChangeStatusResponse;
import com.moego.idl.api.marketing.v1.CheckDiscountCodeRequest;
import com.moego.idl.api.marketing.v1.CheckDiscountCodeResponse;
import com.moego.idl.api.marketing.v1.CheckDiscountCodeValidForCustomerRequest;
import com.moego.idl.api.marketing.v1.CheckDiscountCodeValidForCustomerResponse;
import com.moego.idl.api.marketing.v1.CreateDiscountCodeRequest;
import com.moego.idl.api.marketing.v1.CreateDiscountCodeResponse;
import com.moego.idl.api.marketing.v1.DiscountCodeServiceGrpc;
import com.moego.idl.api.marketing.v1.EditDiscountCodeRequest;
import com.moego.idl.api.marketing.v1.EditDiscountCodeResponse;
import com.moego.idl.api.marketing.v1.GenerateDiscountCodeResponse;
import com.moego.idl.api.marketing.v1.GetBusinessDiscountCodeConfigRequest;
import com.moego.idl.api.marketing.v1.GetBusinessDiscountCodeConfigResponse;
import com.moego.idl.api.marketing.v1.GetDiscountCodeListRequest;
import com.moego.idl.api.marketing.v1.GetDiscountCodeListResponse;
import com.moego.idl.api.marketing.v1.GetDiscountCodeLogListRequest;
import com.moego.idl.api.marketing.v1.GetDiscountCodeLogListResponse;
import com.moego.idl.api.marketing.v1.GetDiscountCodeLogOverviewRequest;
import com.moego.idl.api.marketing.v1.GetDiscountCodeLogOverviewResponse;
import com.moego.idl.api.marketing.v1.GetDiscountCodeRequest;
import com.moego.idl.api.marketing.v1.GetDiscountCodeResponse;
import com.moego.idl.service.marketing.v1.ChangeStatusInput;
import com.moego.idl.service.marketing.v1.ChangeStatusOutput;
import com.moego.idl.service.marketing.v1.CheckDiscountCodeOutput;
import com.moego.idl.service.marketing.v1.CheckDiscountCodeValidForCustomerInput;
import com.moego.idl.service.marketing.v1.CheckDiscountCodeValidForCustomerOutput;
import com.moego.idl.service.marketing.v1.CreateDiscountCodeInput;
import com.moego.idl.service.marketing.v1.CreateDiscountCodeOutput;
import com.moego.idl.service.marketing.v1.DiscountCodeServiceGrpc.DiscountCodeServiceBlockingStub;
import com.moego.idl.service.marketing.v1.EditDiscountCodeInput;
import com.moego.idl.service.marketing.v1.EditDiscountCodeOutput;
import com.moego.idl.service.marketing.v1.GenerateDiscountCodeInput;
import com.moego.idl.service.marketing.v1.GenerateDiscountCodeOutput;
import com.moego.idl.service.marketing.v1.GetBusinessDiscountCodeConfigInput;
import com.moego.idl.service.marketing.v1.GetBusinessDiscountCodeConfigOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeListInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeListOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeLogListInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeLogListOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeLogOverviewInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeLogOverviewOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeOutput;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
public class DiscountController extends DiscountCodeServiceGrpc.DiscountCodeServiceImplBase {

    @Resource
    private DiscountCodeServiceBlockingStub discountCodeClient;

    @Override
    @Auth(COMPANY)
    public void generateDiscountCode(Empty request, StreamObserver<GenerateDiscountCodeResponse> responseObserver) {
        GenerateDiscountCodeInput input = GenerateDiscountCodeInput.newBuilder()
                .setBusinessId(AuthContext.get().businessId())
                .setCompanyId(AuthContext.get().companyId())
                .build();
        GenerateDiscountCodeOutput output = discountCodeClient.generateDiscountCode(input);
        responseObserver.onNext(DiscountConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void checkDiscountCode(
            CheckDiscountCodeRequest request, StreamObserver<CheckDiscountCodeResponse> responseObserver) {
        CheckDiscountCodeOutput output = discountCodeClient.checkDiscountCode(DiscountConverter.INSTANCE.toInput(
                request, AuthContext.get().businessId(), AuthContext.get().companyId()));
        responseObserver.onNext(DiscountConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void createDiscountCode(
            CreateDiscountCodeRequest request, StreamObserver<CreateDiscountCodeResponse> responseObserver) {
        CreateDiscountCodeInput input = DiscountConverter.INSTANCE.toInput(
                request, AuthContext.get().businessId(), AuthContext.get().companyId());
        CreateDiscountCodeOutput output = discountCodeClient.createDiscountCode(input);
        responseObserver.onNext(DiscountConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void editDiscountCode(
            EditDiscountCodeRequest request, StreamObserver<EditDiscountCodeResponse> responseObserver) {
        EditDiscountCodeInput input = DiscountConverter.INSTANCE.toInput(
                request, AuthContext.get().businessId(), AuthContext.get().companyId());
        EditDiscountCodeOutput output = discountCodeClient.editDiscountCode(input);
        responseObserver.onNext(DiscountConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void getDiscountCode(
            GetDiscountCodeRequest request, StreamObserver<GetDiscountCodeResponse> responseObserver) {
        GetDiscountCodeInput input = DiscountConverter.INSTANCE.toInput(
                request, AuthContext.get().businessId(), AuthContext.get().companyId());
        GetDiscountCodeOutput output = discountCodeClient.getDiscountCode(input);
        responseObserver.onNext(DiscountConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void getDiscountCodeList(
            GetDiscountCodeListRequest request, StreamObserver<GetDiscountCodeListResponse> responseObserver) {
        GetDiscountCodeListInput input = DiscountConverter.INSTANCE.toInput(
                request, AuthContext.get().businessId(), AuthContext.get().companyId());
        GetDiscountCodeListOutput output = discountCodeClient.getDiscountCodeList(input);
        responseObserver.onNext(DiscountConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void getDiscountCodeLogOverview(
            GetDiscountCodeLogOverviewRequest request,
            StreamObserver<GetDiscountCodeLogOverviewResponse> responseObserver) {
        GetDiscountCodeLogOverviewInput input = DiscountConverter.INSTANCE.toInput(
                request, AuthContext.get().businessId(), AuthContext.get().companyId());
        GetDiscountCodeLogOverviewOutput output = discountCodeClient.getDiscountCodeLogOverview(input);
        responseObserver.onNext(DiscountConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void getDiscountCodeLogList(
            GetDiscountCodeLogListRequest request, StreamObserver<GetDiscountCodeLogListResponse> responseObserver) {
        GetDiscountCodeLogListInput input = DiscountConverter.INSTANCE.toInput(
                request, AuthContext.get().businessId(), AuthContext.get().companyId());
        GetDiscountCodeLogListOutput output = discountCodeClient.getDiscountCodeLogList(input);
        responseObserver.onNext(DiscountConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void changeStatus(ChangeStatusRequest request, StreamObserver<ChangeStatusResponse> responseObserver) {
        ChangeStatusInput input = DiscountConverter.INSTANCE.toInput(
                request,
                AuthContext.get().businessId(),
                AuthContext.get().companyId(),
                AuthContext.get().staffId());
        ChangeStatusOutput output = discountCodeClient.changeStatus(input);
        responseObserver.onNext(DiscountConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(OB)
    public void checkDiscountCodeValidForCustomer(
            CheckDiscountCodeValidForCustomerRequest request,
            StreamObserver<CheckDiscountCodeValidForCustomerResponse> responseObserver) {
        CheckDiscountCodeValidForCustomerInput input =
                DiscountConverter.INSTANCE.toInput(request, AuthContext.get().customerId());
        CheckDiscountCodeValidForCustomerOutput output = discountCodeClient.checkDiscountCodeValidForCustomer(input);
        responseObserver.onNext(DiscountConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }

    @Override
    @Auth(OB)
    public void getBusinessDiscountCodeConfig(
            GetBusinessDiscountCodeConfigRequest request,
            StreamObserver<GetBusinessDiscountCodeConfigResponse> responseObserver) {
        GetBusinessDiscountCodeConfigInput input = DiscountConverter.INSTANCE.toInput(request);
        GetBusinessDiscountCodeConfigOutput output = discountCodeClient.getBusinessDiscountCodeConfig(input);
        responseObserver.onNext(DiscountConverter.INSTANCE.toResponse(output));
        responseObserver.onCompleted();
    }
}
