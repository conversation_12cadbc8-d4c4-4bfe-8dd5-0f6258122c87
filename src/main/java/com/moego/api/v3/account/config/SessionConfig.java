package com.moego.api.v3.account.config;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.RequestUtils;
import edu.umd.cs.findbugs.annotations.SuppressFBWarnings;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.springframework.boot.context.properties.ConfigurationProperties;

@ConfigurationProperties("moego.session")
public record SessionConfig(List<Source> sources) {
    public SessionContext getSessionContext() {
        var host = RequestUtils.getHost();
        var ip = RequestUtils.getIP();
        var userAgent = RequestUtils.getUserAgent();
        var refererLink = RequestUtils.getReferer();

        return buildSessionContext(host, ip, userAgent, refererLink);
    }

    public SessionContext buildSessionContext(String host, String ip, String userAgent, String refererLink) {
        Source source = null;
        Domain domain = null;

        for (Source s : sources) {
            domain = s.getDomain(host);
            if (domain != null) {
                source = s;
                break;
            }
        }

        if (source == null) {
            throw ExceptionUtil.bizException(Code.CODE_SERVER_ERROR);
        }

        String targetDomain = domain.getTargetDomain(host);

        return new SessionContext(host, targetDomain, ip, userAgent, refererLink, source);
    }

    public record Source(
            String name,
            String cookieName,
            List<String> legacyCookieNames,
            List<Domain> domains,
            int maxAge,
            int subMaxAge) {
        public Domain getDomain(String host) {
            return domains.stream()
                    .filter(domain -> domain.match(host))
                    .findFirst()
                    .orElse(null);
        }
    }

    public record Domain(String pattern, int cookieTargetDomainLevel, Pattern cookieTargetDomainPattern) {
        private static final String COOKIE_TARGET_DOMAIN_PATTERN = "([^.]+\\.){%d}([^.]+)$";

        @SuppressFBWarnings("IP_PARAMETER_IS_DEAD_BUT_OVERWRITTEN")
        public Domain {
            if (cookieTargetDomainLevel < 2) {
                throw new IllegalArgumentException("cookie-target-domain-level must >= 2");
            }
            cookieTargetDomainPattern =
                    Pattern.compile(COOKIE_TARGET_DOMAIN_PATTERN.formatted(cookieTargetDomainLevel - 1));
        }

        public boolean match(String host) {
            return Pattern.matches(pattern, host);
        }

        public String getTargetDomain(String host) {
            Matcher matcher = cookieTargetDomainPattern.matcher(host);
            return matcher.find() ? matcher.group() : host;
        }
    }

    public record SessionContext(
            String host, String targetDomain, String ip, String userAgent, String refererLink, Source source) {}
}
