package com.moego.api.v3.account.helper;

import com.google.protobuf.Empty;
import com.google.protobuf.util.Durations;
import com.moego.api.v3.account.config.SessionConfig;
import com.moego.api.v3.account.consts.SessionDataKey;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.account.v1.CreateSessionRequest;
import com.moego.idl.service.account.v1.DeleteAllSessionsByAccountIdRequest;
import com.moego.idl.service.account.v1.DeleteSessionByIdRequest;
import com.moego.idl.service.account.v1.GetSessionRequest;
import com.moego.idl.service.account.v1.SessionServiceGrpc;
import com.moego.idl.service.account.v1.UpdateSessionRequest;
import com.moego.idl.service.organization.v1.HubspotServiceGrpc;
import com.moego.idl.service.organization.v1.InitContactRequest;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.proto.ProtoUtils;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.RequestUtils;
import com.moego.server.business.client.IBusinessAccountClient;
import com.moego.server.business.client.IBusinessSessionClient;
import com.moego.server.business.params.AccountIdParams;
import com.moego.server.business.params.InitSessionParams;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class SessionHelper {

    private final SessionServiceGrpc.SessionServiceBlockingStub sessionService;
    private final HubspotServiceGrpc.HubspotServiceBlockingStub hubspotService;
    private final IBusinessSessionClient iBusinessSessionClient;
    private final IBusinessAccountClient iBusinessAccountClient;
    private final MigrateHelper migrateHelper;

    public String forkSession(SessionConfig.SessionContext sessionContext, long sessionId) {
        var getRequest = GetSessionRequest.newBuilder().setId(sessionId).build();
        var session = sessionService.getSession(getRequest);

        var createRequest = CreateSessionRequest.newBuilder()
                // 禁止已删除或已冻结的账户 fork session
                .setAllowDeletedAccount(false)
                .setAllowFrozenAccount(false)
                // 记录设备信息
                .setIp(sessionContext.ip())
                .setUserAgent(sessionContext.userAgent())
                .setDeviceId(RequestUtils.getDeviceId())
                .setRefererLink(sessionContext.refererLink())
                .setRefererSessionId(sessionId)
                // 以下字段继承自当前会话
                .setAccountId(session.getAccountId())
                .setSource(session.getSource())
                .setMaxAge(session.getMaxAge())
                .setRenewable(session.getRenewable())
                .setSessionData(session.getSessionData())
                .setImpersonator(session.getImpersonator())
                .build();

        return sessionService.createSession(createRequest).getSessionToken();
    }

    public void checkSessionToken(SessionConfig.SessionContext sessionContext, String token) {
        var request = GetSessionRequest.newBuilder().setSessionToken(token).build();
        var session = sessionService.getSession(request);

        var source = sessionContext.source().name();
        if (!Objects.equals(source, session.getSource())) {
            throw ExceptionUtil.bizException(Code.CODE_SESSION_TOKEN_INVALID);
        }
    }

    public String createSession(SessionConfig.SessionContext sessionContext, long accountId) {
        var authContext = AuthContext.get();

        var request = CreateSessionRequest.newBuilder()
                .setAccountId(accountId)
                .setAllowDeletedAccount(false)
                .setAllowFrozenAccount(false)
                .setSource(sessionContext.source().name())
                .setIp(sessionContext.ip())
                .setUserAgent(sessionContext.userAgent())
                .setDeviceId(RequestUtils.getDeviceId())
                .setRefererLink(sessionContext.refererLink())
                .setRefererSessionId(
                        Optional.ofNullable(authContext.sessionId()).orElse(0L))
                .setMaxAge(Durations.fromSeconds(sessionContext.source().maxAge()))
                .build();

        var response = sessionService.createSession(request);

        switch (sessionContext.source().name()) {
            case "business":
                initBusinessSession(accountId, response.getId());
                if (response.getFirstSession()) {
                    log.info("first login business account, init hubspot contact. accountId: {}", accountId);
                    ThreadPool.submit(() -> {
                        hubspotService.initContact(InitContactRequest.newBuilder()
                                .setAccountId(accountId)
                                .build());
                    });
                }
                break;
            default:
                break;
        }

        return response.getSessionToken();
    }

    public Empty deleteSessionById(long sessionId) {
        var request = DeleteSessionByIdRequest.newBuilder().setId(sessionId).build();
        return sessionService.deleteSessionById(request);
    }

    public Empty deleteSessionByAccountId(long accountId) {
        iBusinessAccountClient.resetAccountTokenAndStaffToken(new AccountIdParams((int) accountId));
        var request = DeleteAllSessionsByAccountIdRequest.newBuilder()
                .setAccountId(accountId)
                .build();
        return sessionService.deleteAllSessionsByAccountId(request);
    }

    private void initBusinessSession(long accountId, long sessionId) {
        var initParams = new InitSessionParams(accountId, sessionId);
        iBusinessSessionClient.initSessionV2(initParams);
    }

    public void updateSessionData(
            Long sessionId, Long companyId, Long businessId, Long staffId, java.time.Duration maxAge) {
        var sessionData = buildBusinessSessionData(companyId, businessId, staffId);

        var builder =
                UpdateSessionRequest.newBuilder().setId(sessionId).setSessionData(ProtoUtils.mapToStruct(sessionData));
        if (maxAge != null) {
            builder.setMaxAge(Durations.fromSeconds(maxAge.getSeconds()));
        }
        sessionService.updateSession(builder.build());
    }

    public Map<String, String> buildBusinessSessionData(Long companyId, Long businessId, Long staffId) {
        Map<String, String> sessionData = new HashMap<>();
        if (migrateHelper.isMigrate(companyId)) {
            sessionData.put(SessionDataKey.SESSION_DATA_COMPANY_IS_MIGRATE, "true");
        } else {
            sessionData.put(SessionDataKey.SESSION_DATA_COMPANY_IS_MIGRATE, "false");
        }
        sessionData.put(SessionDataKey.SESSION_DATA_COMPANY_ID, String.valueOf(companyId));
        sessionData.put(SessionDataKey.SESSION_DATA_BUSINESS_ID, String.valueOf(businessId));
        sessionData.put(SessionDataKey.SESSION_DATA_STAFF_ID, String.valueOf(staffId));
        return sessionData;
    }
}
