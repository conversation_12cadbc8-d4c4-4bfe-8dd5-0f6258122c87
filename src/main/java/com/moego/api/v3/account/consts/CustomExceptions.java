package com.moego.api.v3.account.consts;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.exception.BizException;
import com.moego.lib.common.exception.ExceptionUtil;

public class CustomExceptions {

    public static final BizException EMAIL_CONFLICT = ExceptionUtil.bizException(
            Code.CODE_EMAIL_CONFLICT,
            "This account already exists in MoeGo, please login with this email or use a different email.");
}
