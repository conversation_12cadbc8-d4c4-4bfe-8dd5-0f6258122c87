package com.moego.api.v3.account.controller;

import com.google.protobuf.Empty;
import com.moego.api.v3.account.config.SessionConfig;
import com.moego.api.v3.account.helper.AccountHelper;
import com.moego.api.v3.account.helper.GrpcCookieHelper;
import com.moego.api.v3.account.helper.RiskControlHelper;
import com.moego.api.v3.account.helper.SessionHelper;
import com.moego.idl.api.account.v1.AccountAccessServiceGrpc;
import com.moego.idl.api.account.v1.AccountLoginRequest;
import com.moego.idl.api.account.v1.AccountLoginResponse;
import com.moego.idl.api.account.v1.AccountRegisterRequest;
import com.moego.idl.api.account.v1.AccountRegisterResponse;
import com.moego.idl.api.account.v1.CheckIdentifierAvailableRequest;
import com.moego.idl.api.account.v1.CheckIdentifierAvailableResponse;
import com.moego.idl.api.account.v1.ForkSessionRequest;
import com.moego.idl.api.account.v1.ForkSessionResponse;
import com.moego.idl.api.account.v1.RegisterOrLoginRequest;
import com.moego.idl.api.account.v1.RegisterOrLoginResponse;
import com.moego.idl.models.account.v1.AccountModel;
import com.moego.idl.models.account.v1.AccountNamespaceType;
import com.moego.idl.models.account.v1.NamespaceDef;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.risk_control.v1.VerificationCodeDef;
import com.moego.idl.models.risk_control.v1.VerificationCodeMethod;
import com.moego.idl.models.risk_control.v1.VerificationIdentifierDef;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@AllArgsConstructor
public class AccountAccessController extends AccountAccessServiceGrpc.AccountAccessServiceImplBase {

    private final GrpcCookieHelper cookieHelper;
    private final SessionHelper sessionHelper;
    private final AccountHelper accountHelper;
    private final RiskControlHelper riskControlHelper;
    private final SessionConfig sessionConfig;

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void checkIdentifierAvailable(
            CheckIdentifierAvailableRequest request,
            StreamObserver<CheckIdentifierAvailableResponse> responseObserver) {
        var used = accountHelper.checkIdentifier(request);

        var response =
                CheckIdentifierAvailableResponse.newBuilder().setUsed(used).build();
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void register(AccountRegisterRequest request, StreamObserver<AccountRegisterResponse> responseObserver) {
        // 0. check verification code
        if (request.hasVerification()) {
            riskControlHelper.verifyVerificationCode(request.getVerification());
        }
        // 1. create account
        var sessionContext = sessionConfig.getSessionContext();
        var account =
                accountHelper.createAccount(request, sessionContext.source().name());

        // 2. create session
        var sessionToken = sessionHelper.createSession(sessionContext, account.getId());

        // 3. set cookie
        cookieHelper.setCookie(sessionContext, sessionToken);

        // 4. build response
        var response = AccountRegisterResponse.newBuilder().build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void login(AccountLoginRequest request, StreamObserver<AccountLoginResponse> responseObserver) {
        var sessionContext = sessionConfig.getSessionContext();

        String sessionToken =
                switch (request.getLoginMethodCase()) {
                    case BY_TOKEN -> {
                        var token = request.getByToken().getToken();
                        // 检查 token 有效性，如果有效，则认为允许登录，可以被设置在 cookie 中
                        sessionHelper.checkSessionToken(sessionContext, token);
                        yield token;
                    }
                    case BY_EMAIL_PASSWORD -> {
                        var params = request.getByEmailPassword();
                        // 1. get account by email
                        AccountModel account = accountHelper.getAccountByEmail(params.getEmail());
                        if (account == null) {
                            // 如果账号不存在，将报错信息模糊成 "账号或密码错误"
                            throw ExceptionUtil.bizException(Code.CODE_ACCOUNT_OR_PASSWORD_ERROR);
                        }

                        // 2. check password
                        boolean correct = accountHelper.validatePassword(account.getId(), params.getPassword());
                        if (!correct) {
                            throw ExceptionUtil.bizException(Code.CODE_ACCOUNT_OR_PASSWORD_ERROR);
                        }

                        // 3. create session
                        yield sessionHelper.createSession(sessionContext, account.getId());
                    }
                    case LOGINMETHOD_NOT_SET -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
                };

        // set cookie
        cookieHelper.setCookie(sessionContext, sessionToken);

        // build response
        var response = AccountLoginResponse.newBuilder().build();

        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void registerOrLogin(
            RegisterOrLoginRequest request, StreamObserver<RegisterOrLoginResponse> responseObserver) {
        var namespace = request.hasNamespace()
                ? request.getNamespace()
                : NamespaceDef.newBuilder()
                        .setType(AccountNamespaceType.MOEGO)
                        .setId(0)
                        .build();

        var sessionContext = sessionConfig.getSessionContext();

        switch (request.getAccessMethodCase()) {
            case BY_PHONE_VERIFY_CODE -> {
                var params = request.getByPhoneVerifyCode();
                var phoneNumber = params.getPhoneNumber();

                var verificationIdentifierDef = VerificationIdentifierDef.newBuilder()
                        .setPhoneNumber(phoneNumber)
                        .setScenario(params.getScenario())
                        .setMethod(VerificationCodeMethod.VERIFICATION_CODE_METHOD_SIGN_UP_OR_LOGIN)
                        .build();

                VerificationCodeDef verificationCodeDef = VerificationCodeDef.newBuilder()
                        .setCode(params.getCode())
                        .setToken(params.getToken())
                        .setIdentifier(verificationIdentifierDef)
                        .build();
                riskControlHelper.verifyVerificationCode(verificationCodeDef);

                // get or create account
                boolean registered = false;
                var account = accountHelper.getAccountByPhoneNumber(phoneNumber, namespace);
                if (account == null) {
                    registered = true;
                    account = accountHelper.createPhoneNumberAccount(
                            phoneNumber, sessionContext.source().name(), namespace);
                }

                // create session
                var sessionToken = sessionHelper.createSession(sessionContext, account.getId());

                // set cookie
                cookieHelper.setCookie(sessionContext, sessionToken);

                var response = RegisterOrLoginResponse.newBuilder()
                        .setRegistered(registered)
                        .build();

                responseObserver.onNext(response);
                responseObserver.onCompleted();
            }
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR);
        }
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void logout(Empty request, StreamObserver<Empty> responseObserver) {
        var sessionId = AuthContext.get().sessionId();

        // 通过 account token 访问的请求没有 session id
        // 另一种情况是已经登出，重复调用时会变成匿名访问，需要幂等
        if (sessionId != null) {
            sessionHelper.deleteSessionById(sessionId);
        }

        // 清除 cookies
        var sessionContext = sessionConfig.getSessionContext();
        cookieHelper.removeCookie(sessionContext);
        cookieHelper.removeLegacyCookies(sessionContext);

        responseObserver.onNext(Empty.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void forkSession(ForkSessionRequest request, StreamObserver<ForkSessionResponse> responseObserver) {
        var context = AuthContext.get();
        var sessionContext = sessionConfig.getSessionContext();

        var sessionToken = sessionHelper.forkSession(sessionContext, context.sessionId());

        responseObserver.onNext(
                ForkSessionResponse.newBuilder().setToken(sessionToken).build());
        responseObserver.onCompleted();
    }
}
