package com.moego.api.v3.account.helper;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.risk_control.v1.VerificationCodeDef;
import com.moego.idl.models.risk_control.v1.VerificationIdentifierDef;
import com.moego.idl.service.risk_control.v1.CreateCodeRequest;
import com.moego.idl.service.risk_control.v1.CreateCodeResponse;
import com.moego.idl.service.risk_control.v1.VerificationCodeServiceGrpc;
import com.moego.idl.service.risk_control.v1.VerifyCodeRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.util.Env;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class RiskControlHelper {
    private final Environment env;
    private final VerificationCodeServiceGrpc.VerificationCodeServiceBlockingStub verificationService;

    // verify code and destroy it
    public void verifyVerificationCode(VerificationCodeDef codeDef) {
        // TODO 临时提审用的手机号验证码账号，后续去掉改造为演示环境
        if (Objects.equals(codeDef.getIdentifier().getPhoneNumber(), "+14133414067")) {
            return;
        }
        // 测试环境魔法验证码
        if (env.matchesProfiles(Env.TEST2.getValue()) && Objects.equals(codeDef.getCode(), "000000")) {
            return;
        }
        var result = verificationService.verify(
                VerifyCodeRequest.newBuilder().setVerification(codeDef).build());
        if (!result.getIsExist()) {
            throw ExceptionUtil.bizException(Code.CODE_VERIFICATION_CODE_NOT_MATCH);
        }
    }

    public CreateCodeResponse createVerificationCode(VerificationIdentifierDef identifierDef) {
        return verificationService.create(
                CreateCodeRequest.newBuilder().setIdentifier(identifierDef).build());
    }
}
