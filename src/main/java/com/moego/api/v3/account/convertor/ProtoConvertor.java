package com.moego.api.v3.account.convertor;

import com.moego.idl.api.account.v1.AccountRegisterRequest;
import com.moego.idl.api.account.v1.CheckIdentifierAvailableRequest;
import com.moego.idl.api.account.v1.UpdateProfileRequest;
import com.moego.idl.models.account.v1.AccountModel;
import com.moego.idl.models.account.v1.AccountModelRelevantView;
import com.moego.idl.service.account.v1.CheckIdentifierRequest;
import com.moego.idl.service.account.v1.CreateAccountRequest;
import com.moego.idl.service.account.v1.UpdateAccountRequest;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedSourcePolicy = ReportingPolicy.WARN,
        unmappedTargetPolicy = ReportingPolicy.WARN)
public interface ProtoConvertor {
    CheckIdentifierRequest toCheckIdentifierRequest(CheckIdentifierAvailableRequest request);

    CreateAccountRequest toCreateRequest(AccountRegisterRequest request, String source);

    UpdateAccountRequest toUpdateRequest(long id, UpdateProfileRequest request);

    AccountModelRelevantView toAccountModelRelevantView(AccountModel input);

    List<AccountModelRelevantView> toAccountModelRelevantViews(List<AccountModel> input);
}
