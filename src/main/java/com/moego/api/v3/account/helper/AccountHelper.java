package com.moego.api.v3.account.helper;

import static com.moego.lib.common.exception.ExceptionUtil.extractCode;

import com.moego.api.v3.account.consts.CustomExceptions;
import com.moego.api.v3.account.convertor.ProtoConvertor;
import com.moego.idl.api.account.v1.AccountRegisterRequest;
import com.moego.idl.api.account.v1.CheckIdentifierAvailableRequest;
import com.moego.idl.api.account.v1.UpdateProfileRequest;
import com.moego.idl.models.account.v1.AccountModel;
import com.moego.idl.models.account.v1.NamespaceDef;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.account.v1.AccountServiceGrpc;
import com.moego.idl.service.account.v1.CreateAccountRequest;
import com.moego.idl.service.account.v1.GetAccountRequest;
import com.moego.idl.service.account.v1.UpdateAccountRequest;
import com.moego.idl.service.account.v1.ValidatePasswordRequest;
import io.grpc.StatusRuntimeException;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@AllArgsConstructor
public class AccountHelper {

    private final AccountServiceGrpc.AccountServiceBlockingStub accountService;
    private final ProtoConvertor protoConvertor;

    public AccountModel getAccountById(long accountId) {
        var request = GetAccountRequest.newBuilder().setId(accountId).build();
        try {
            return accountService.getAccount(request);
        } catch (StatusRuntimeException e) {
            // 如果账号不存在，不报错，其他报错直接向上抛出
            if (!Code.CODE_ACCOUNT_NOT_EXIST.equals(extractCode(e))) {
                throw e;
            }
        }
        return null;
    }

    public AccountModel getAccountByEmail(String email) {
        var request = GetAccountRequest.newBuilder().setEmail(email).build();
        try {
            return accountService.getAccount(request);
        } catch (StatusRuntimeException e) {
            // 如果账号不存在，不报错，其他报错直接向上抛出
            if (!Code.CODE_ACCOUNT_NOT_EXIST.equals(extractCode(e))) {
                throw e;
            }
        }
        return null;
    }

    public AccountModel getAccountByPhoneNumber(String phoneNumber, NamespaceDef namespace) {
        var request = GetAccountRequest.newBuilder()
                .setPhoneNumber(phoneNumber)
                .setNamespace(namespace)
                .build();
        try {
            return accountService.getAccount(request);
        } catch (StatusRuntimeException e) {
            // 如果账号不存在，不报错，其他报错直接向上抛出
            if (!Code.CODE_ACCOUNT_NOT_EXIST.equals(extractCode(e))) {
                throw e;
            }
        }
        return null;
    }

    public boolean validatePassword(long accountId, String password) {
        var request = ValidatePasswordRequest.newBuilder()
                .setId(accountId)
                .setPassword(password)
                .build();
        var validateResponse = accountService.validatePassword(request);
        return validateResponse.getCorrect();
    }

    public void updatePassword(long accountId, String password) {
        var request = UpdateAccountRequest.newBuilder()
                .setId(accountId)
                .setPassword(password)
                .build();
        accountService.updateAccount(request);
    }

    public AccountModel updateProfile(long accountId, UpdateProfileRequest request) {
        var updateRequest = protoConvertor.toUpdateRequest(accountId, request);
        return accountService.updateAccount(updateRequest);
    }

    public boolean checkIdentifier(CheckIdentifierAvailableRequest request) {
        var checkRequest = protoConvertor.toCheckIdentifierRequest(request);
        var checkResponse = accountService.checkIdentifier(checkRequest);
        return checkResponse.getUsed();
    }

    public AccountModel createAccount(AccountRegisterRequest request, String source) {
        var createRequest = protoConvertor.toCreateRequest(request, source);
        try {
            return accountService.createAccount(createRequest);
        } catch (StatusRuntimeException e) {
            /*
             * email 冲突时抛出的异常，产品要求定制文案，详见：
             * https://moego.atlassian.net/browse/ERP-4031
             */
            if (Code.CODE_EMAIL_CONFLICT.equals(extractCode(e))) {
                throw CustomExceptions.EMAIL_CONFLICT;
            }
            throw e;
        }
    }

    public AccountModel createPhoneNumberAccount(String phoneNumber, String source, NamespaceDef namespace) {
        var request = CreateAccountRequest.newBuilder()
                .setPhoneNumber(phoneNumber)
                .setSource(source)
                .setNamespace(namespace)
                .build();
        return accountService.createAccount(request);
    }

    public AccountModel updateAccount(UpdateAccountRequest request) {
        return accountService.updateAccount(request);
    }
}
