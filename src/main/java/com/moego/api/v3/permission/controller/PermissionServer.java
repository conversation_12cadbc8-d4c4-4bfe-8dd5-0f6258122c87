package com.moego.api.v3.permission.controller;

import com.moego.api.v3.permission.service.CompanyService;
import com.moego.idl.api.permission.v1.CreateRoleParams;
import com.moego.idl.api.permission.v1.CreateRoleResult;
import com.moego.idl.api.permission.v1.DeleteRoleParams;
import com.moego.idl.api.permission.v1.DeleteRoleResult;
import com.moego.idl.api.permission.v1.DuplicateRoleParams;
import com.moego.idl.api.permission.v1.DuplicateRoleResult;
import com.moego.idl.api.permission.v1.EditPermissionsParams;
import com.moego.idl.api.permission.v1.EditPermissionsResult;
import com.moego.idl.api.permission.v1.GetRoleDetailParams;
import com.moego.idl.api.permission.v1.GetRoleDetailResult;
import com.moego.idl.api.permission.v1.GetRoleListParams;
import com.moego.idl.api.permission.v1.GetRoleListResult;
import com.moego.idl.api.permission.v1.PermissionServiceGrpc;
import com.moego.idl.api.permission.v1.UpdateRoleParams;
import com.moego.idl.api.permission.v1.UpdateRoleResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.metadata.v1.OwnerType;
import com.moego.idl.service.metadata.v1.ExtractValuesRequest;
import com.moego.idl.service.metadata.v1.GetKeyRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.idl.service.metadata.v1.UpdateValueRequest;
import com.moego.idl.service.organization.v1.CountStaffWithRoleRequest;
import com.moego.idl.service.permission.v1.CreateRoleRequest;
import com.moego.idl.service.permission.v1.CreateRoleResponse;
import com.moego.idl.service.permission.v1.DeleteRoleRequest;
import com.moego.idl.service.permission.v1.DeleteRoleResponse;
import com.moego.idl.service.permission.v1.DuplicateRoleRequest;
import com.moego.idl.service.permission.v1.DuplicateRoleResponse;
import com.moego.idl.service.permission.v1.EditPermissionsRequest;
import com.moego.idl.service.permission.v1.EditPermissionsResponse;
import com.moego.idl.service.permission.v1.GetRoleDetailRequest;
import com.moego.idl.service.permission.v1.GetRoleDetailResponse;
import com.moego.idl.service.permission.v1.GetRoleListRequest;
import com.moego.idl.service.permission.v1.GetRoleListResponse;
import com.moego.idl.service.permission.v1.UpdateRoleRequest;
import com.moego.idl.service.permission.v1.UpdateRoleResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcRequestContext;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.Metadata;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import org.springframework.util.StringUtils;

@GrpcService
@RequiredArgsConstructor
public class PermissionServer extends PermissionServiceGrpc.PermissionServiceImplBase {
    private final com.moego.idl.service.permission.v1.PermissionServiceGrpc.PermissionServiceBlockingStub
            permissionClient;
    private final com.moego.idl.service.organization.v1.StaffServiceGrpc.StaffServiceBlockingStub staffClient;
    private final com.moego.api.v3.permission.service.PermissionService permissionService;
    private final CompanyService companyService;
    private final MigrateHelper migrateHelper;
    private final com.moego.lib.permission.PermissionHelper permissionHelper;
    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataClient;

    @Override
    @Auth(AuthType.COMPANY)
    public void getRoleList(GetRoleListParams request, StreamObserver<GetRoleListResult> responseObserver) {
        GetRoleListResponse response = permissionClient.getRoleList(GetRoleListRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setTokenStaffId(AuthContext.get().staffId())
                .build());
        responseObserver.onNext(GetRoleListResult.newBuilder()
                .addAllRoleList(response.getRoleListList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getRoleDetail(GetRoleDetailParams request, StreamObserver<GetRoleDetailResult> responseObserver) {
        GetRoleDetailResponse response = permissionClient.getRoleDetail(GetRoleDetailRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setTokenStaffId(AuthContext.get().staffId())
                .setRoleId(request.getRoleId())
                .build());

        responseObserver.onNext(GetRoleDetailResult.newBuilder()
                .setRoleDetail(permissionService.hidePermissions(response.getRoleDetail()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.EDIT_ROLE_PERMISSIONS)
    public void createRole(CreateRoleParams request, StreamObserver<CreateRoleResult> responseObserver) {
        try {
            CreateRoleResponse response = permissionClient.createRole(CreateRoleRequest.newBuilder()
                    .setCompanyId(AuthContext.get().companyId())
                    .setTokenStaffId(AuthContext.get().staffId())
                    .setRoleName(request.getRoleName())
                    .build());
            responseObserver.onNext(CreateRoleResult.newBuilder()
                    .addAllRoleList(response.getRoleListList())
                    .setRoleId(response.getRoleId())
                    .build());
            responseObserver.onCompleted();
        } catch (StatusRuntimeException e) {
            if (e.getStatus().getCode().equals(Status.Code.ALREADY_EXISTS)) {
                throw ExceptionUtil.bizException(Code.CODE_ROLE_NAME_ALREADY_EXISTS, "Role name is already in use");
            }
        }
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.EDIT_ROLE_PERMISSIONS)
    public void updateRole(UpdateRoleParams request, StreamObserver<UpdateRoleResult> responseObserver) {
        try {
            UpdateRoleResponse response = permissionClient.updateRole(UpdateRoleRequest.newBuilder()
                    .setCompanyId(AuthContext.get().companyId())
                    .setTokenStaffId(AuthContext.get().staffId())
                    .setRoleId(request.getRoleId())
                    .setRoleName(request.getRoleName())
                    .build());
            responseObserver.onNext(UpdateRoleResult.newBuilder()
                    .addAllRoleList(response.getRoleListList())
                    .build());
            responseObserver.onCompleted();
        } catch (StatusRuntimeException e) {
            if (e.getStatus().getCode().equals(Status.Code.ALREADY_EXISTS)) {
                throw ExceptionUtil.bizException(Code.CODE_ROLE_NAME_ALREADY_EXISTS, "Role name is already in use");
            }
        }
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.EDIT_ROLE_PERMISSIONS)
    public void deleteRole(DeleteRoleParams request, StreamObserver<DeleteRoleResult> responseObserver) {
        var count = staffClient
                .countStaffWithRole(CountStaffWithRoleRequest.newBuilder()
                        .setRoleId(request.getRoleId())
                        .setCompanyId(AuthContext.get().companyId())
                        .build())
                .getStaffCount();
        if (count > 0) {
            throw ExceptionUtil.bizException(Code.CODE_DELETE_ROLE_ERROR, "there are staffs with this role");
        }
        DeleteRoleResponse response = permissionClient.deleteRole(DeleteRoleRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setTokenStaffId(AuthContext.get().staffId())
                .setRoleId(request.getRoleId())
                .build());
        responseObserver.onNext(DeleteRoleResult.newBuilder()
                .addAllRoleList(response.getRoleListList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.EDIT_ROLE_PERMISSIONS)
    public void duplicateRole(DuplicateRoleParams request, StreamObserver<DuplicateRoleResult> responseObserver) {
        try {
            DuplicateRoleResponse response = permissionClient.duplicateRole(DuplicateRoleRequest.newBuilder()
                    .setCompanyId(AuthContext.get().companyId())
                    .setTokenStaffId(AuthContext.get().staffId())
                    .setRoleId(request.getRoleId())
                    .setRoleName(request.getRoleName())
                    .build());
            responseObserver.onNext(DuplicateRoleResult.newBuilder()
                    .addAllRoleList(response.getRoleListList())
                    .build());
            responseObserver.onCompleted();
        } catch (StatusRuntimeException e) {
            if (e.getStatus().getCode().equals(Status.Code.ALREADY_EXISTS)) {
                throw ExceptionUtil.bizException(Code.CODE_ROLE_NAME_ALREADY_EXISTS, "Role name is already in use");
            }
        }
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void editPermissions(EditPermissionsParams request, StreamObserver<EditPermissionsResult> responseObserver) {
        Long staffId = AuthContext.get().staffId();
        Long companyId = AuthContext.get().companyId();
        permissionHelper.checkEditPermission(
                companyId, staffId, request.getRoleId(), request.getPermissionCategoryListList());

        String userAgent = null;
        var ctx = GrpcRequestContext.get();
        if (ctx != null) {
            Metadata headers = ctx.headers();
            userAgent = headers.get(Metadata.Key.of("user-agent", Metadata.ASCII_STRING_MARSHALLER));
        }
        if (!StringUtils.hasText(userAgent)) {
            userAgent = "";
        }
        if (!checkAppVersion(userAgent)) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR,
                    "Please update your mobile app to the latest version to change permissions");
        }

        EditPermissionsResponse response = permissionClient.editPermissions(EditPermissionsRequest.newBuilder()
                .setCompanyId(companyId)
                .setTokenStaffId(staffId)
                .setRoleId(request.getRoleId())
                .addAllPermissionCategoryList(request.getPermissionCategoryListList())
                .build());
        ThreadPool.execute(() -> markEdit(companyId, staffId));
        var categoryList = permissionService.hidePermissions(response.getPermissionCategoryListList());
        responseObserver.onNext(EditPermissionsResult.newBuilder()
                .addAllPermissionCategoryList(categoryList)
                .build());
        responseObserver.onCompleted();
    }

    private void markEdit(long companyId, long staffId) {
        metadataClient.updateValue(UpdateValueRequest.newBuilder()
                .setKeyId(metadataClient
                        .getKey(GetKeyRequest.newBuilder()
                                .setName("company_has_edited_new_permission")
                                .build())
                        .getKey()
                        .getId())
                .setOwnerId(companyId)
                .setValue("true")
                .setOperatorId(staffId)
                .build());
    }

    private boolean checkAppVersion(String userAgent) {
        String version = extractMoeGoBusinessVersion(userAgent);
        if (!StringUtils.hasText(version)) {
            return true;
        }
        var minVersion = metadataClient
                .extractValues(ExtractValuesRequest.newBuilder()
                        .setKeyName("can_edit_permission_min_app_version")
                        .putOwners(OwnerType.OWNER_TYPE_SYSTEM_VALUE, 0)
                        .build())
                .getValues()
                .getOrDefault("can_edit_permission_min_app_version", "2.13.0.113");
        return compareVersions(version, minVersion) >= 0;
    }

    public static String extractMoeGoBusinessVersion(String input) {
        if (input == null || input.isEmpty()) {
            return null;
        }

        // Check if the input starts with MoeGoBusiness/
        String prefix = "MoeGoBusiness/";
        if (input.startsWith(prefix)) {
            // Extract the version number using regex
            String regex = "^MoeGoBusiness/([\\d\\.]+)";
            java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(regex);
            java.util.regex.Matcher matcher = pattern.matcher(input);

            if (matcher.find()) {
                return matcher.group(1); // Return the version number
            }
        }
        return null;
    }
    /**
     * Compares two version strings.
     *
     * @param version1 the first version string
     * @param version2 the second version string
     * @return 1 if version1 > version2, -1 if version1 < version2, 0 if equal
     */
    public static int compareVersions(String version1, String version2) {
        String[] v1Parts = version1.split("\\.");
        String[] v2Parts = version2.split("\\.");

        int maxLength = Math.max(v1Parts.length, v2Parts.length);

        for (int i = 0; i < maxLength; i++) {
            int v1 = i < v1Parts.length ? Integer.parseInt(v1Parts[i]) : 0;
            int v2 = i < v2Parts.length ? Integer.parseInt(v2Parts[i]) : 0;

            if (v1 > v2) {
                return 1;
            } else if (v1 < v2) {
                return -1;
            }
        }

        return 0;
    }
}
