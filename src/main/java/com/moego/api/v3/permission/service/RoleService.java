package com.moego.api.v3.permission.service;

import com.moego.idl.models.permission.v1.PermissionCategoryType;
import com.moego.idl.models.permission.v1.RoleModel;
import com.moego.idl.service.permission.v1.ListRoleDetailsRequest;
import com.moego.idl.service.permission.v1.PermissionServiceGrpc.PermissionServiceBlockingStub;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @since 2024/12/4
 */
@Service
@RequiredArgsConstructor
public class RoleService {

    private final PermissionServiceBlockingStub permissionStub;

    public Map<Long, RoleModel> listRolesInfo(List<Long> roleIds, List<Long> companyIds) {
        if (CollectionUtils.isEmpty(roleIds) || CollectionUtils.isEmpty(companyIds)) {
            return Map.of();
        }
        return permissionStub
                .listRoleDetails(ListRoleDetailsRequest.newBuilder()
                        .setFilter(ListRoleDetailsRequest.Filter.newBuilder()
                                .addAllIds(roleIds)
                                .addPermissionCategoryTypes(PermissionCategoryType.MOEGO_PLATFORM)
                                .addAllCompanyIds(companyIds)
                                .build())
                        .build())
                .getRolesList()
                .stream()
                .collect(Collectors.toMap(RoleModel::getId, Function.identity()));
    }
}
