package com.moego.api.v3.permission.service;

import com.moego.idl.models.account.v1.AccountNamespaceType;
import com.moego.idl.models.metadata.v1.OwnerType;
import com.moego.idl.service.branded_app.v1.BrandedAppConfigServiceGrpc.BrandedAppConfigServiceBlockingStub;
import com.moego.idl.service.branded_app.v1.GetBrandedAppConfigRequest;
import com.moego.idl.service.metadata.v1.ExtractValuesRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetLocationListRequest;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class CompanyService {
    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessClient;
    private final BrandedAppConfigServiceBlockingStub brandedAppConfigService;
    private final FeatureFlagApi featureFlagApi;

    public boolean isInBoardingWhiteList(Long companyId) {
        try {
            String keyName = "allow_boarding_and_daycare";
            var request = ExtractValuesRequest.newBuilder()
                    .setKeyName(keyName)
                    .putOwners(OwnerType.OWNER_TYPE_COMPANY_VALUE, companyId)
                    .build();

            return metadataServiceBlockingStub
                    .extractValues(request)
                    .getValuesMap()
                    .getOrDefault(keyName, "false")
                    .equals("true");
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isInMembershipWhiteList(Long companyId) {
        return featureFlagApi.isOn(
                FeatureFlags.ENABLE_MEMBERSHIP,
                FeatureFlagContext.builder().company(companyId).build());
    }

    public boolean isInBrandedAppWhiteList(Long companyId) {
        try {
            var response = brandedAppConfigService.getBrandedAppConfig(GetBrandedAppConfigRequest.newBuilder()
                    .setBrandedEntity(GetBrandedAppConfigRequest.BrandedEntity.newBuilder()
                            .setBrandedId(companyId)
                            .setBrandedType(AccountNamespaceType.COMPANY)
                            .build())
                    .build());
            return response.hasConfig();
        } catch (Exception e) {
            return false;
        }
    }

    public boolean isMultiLocation(Long companyId) {
        return businessClient
                        .getLocationList(GetLocationListRequest.newBuilder()
                                .setTokenCompanyId(companyId)
                                .build())
                        .getLocationCount()
                > 1;
    }
}
