package com.moego.api.v3.debugging.controller;

import static com.moego.lib.common.auth.AuthType.ANONYMOUS;

import com.moego.idl.api.debugging.v1.DebuggingServiceGrpc;
import com.moego.idl.api.debugging.v1.ListHeaderParams;
import com.moego.idl.api.debugging.v1.ListHeaderResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.observability.tracing.RequestHolder;
import com.moego.lib.common.thread.ThreadContextHolder;
import com.moego.lib.common.util.Env;
import io.grpc.stub.StreamObserver;
import java.util.LinkedHashMap;
import lombok.RequiredArgsConstructor;
import org.springframework.core.env.Environment;

/**
 * <AUTHOR>
 * @since 2024/10/21
 */
@GrpcService
@RequiredArgsConstructor
public class DebuggingController extends DebuggingServiceGrpc.DebuggingServiceImplBase {

    private final Environment env;

    @Override
    @Auth(ANONYMOUS)
    public void listHeader(ListHeaderParams request, StreamObserver<ListHeaderResult> responseObserver) {

        if (!env.matchesProfiles(Env.LOCAL.getValue(), Env.TEST2.getValue(), Env.STAGING.getValue())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Current environment is not allowed to access this API.");
        }

        var context = ThreadContextHolder.getContext(RequestHolder.class);
        if (context == null) {
            responseObserver.onNext(ListHeaderResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        var headers = new LinkedHashMap<String, String>();
        for (var header : context.headers()) {
            var value = context.getHeader(header);
            headers.put(header, value);
        }

        var result = ListHeaderResult.newBuilder().putAllHeaders(headers).build();
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }
}
