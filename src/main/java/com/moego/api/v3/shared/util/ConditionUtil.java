/*
 * @since 2023-07-13 10:58:34
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.shared.util;

import java.util.function.Consumer;
import java.util.function.Supplier;

public class ConditionUtil {

    public static <T> void ifNotNull(T value, Consumer<T> consumer) {
        if (value != null) {
            consumer.accept(value);
        }
    }

    public static <T> void ifNotNull(Boolean condition, Supplier<T> value, Consumer<T> consumer) {
        if (condition) {
            consumer.accept(value.get());
        }
    }
}
