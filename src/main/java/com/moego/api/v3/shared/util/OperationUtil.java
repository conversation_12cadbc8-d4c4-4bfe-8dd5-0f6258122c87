/*
 * @since 2024-06-18 09:55:47
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.shared.util;

import static java.util.Optional.ofNullable;

import com.google.protobuf.util.Timestamps;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.utils.v2.OperationRequest;
import com.moego.lib.common.auth.AuthContext;
import java.util.List;
import java.util.function.Function;

public class OperationUtil {

    public static OperationRequest staffOperation() {
        // must be signed in, so just get info from auth context
        return OperationRequest.newBuilder()
                .setAccountId(AuthContext.get().accountId())
                .setSessionId(AuthContext.get().sessionId())
                .setStaffId(AuthContext.get().staffId())
                .setTime(Timestamps.now())
                .build();
    }

    public static OperationRequest customerOperation(long customerId) {
        // may not be signed in, need to set it carefully
        final var builder =
                OperationRequest.newBuilder().setBusinessCustomerId(customerId).setTime(Timestamps.now());
        ofNullable(AuthContext.get().accountId()).ifPresent(builder::setAccountId);
        ofNullable(AuthContext.get().sessionId()).ifPresent(builder::setSessionId);
        return builder.build();
    }

    public static <T> List<Long> extractDistinctIds(List<T> list, Function<T, Long> idExtractor) {
        return list.stream()
                .map(idExtractor)
                .filter(CommonUtil::isNormal)
                .distinct()
                .toList();
    }
}
