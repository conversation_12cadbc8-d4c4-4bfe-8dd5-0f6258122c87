/*
 * @since 2024-06-18 13:51:18
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.shared.property;

import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.business_customer.v1.BusinessCustomerServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetCustomerRequest;
import com.moego.lib.common.auth.AuthContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class CustomerProperty {

    private final BusinessCustomerServiceGrpc.BusinessCustomerServiceBlockingStub businessCustomerServiceBlockingStub;

    public void check(long customerId) {
        check(customerId, AuthContext.get().companyId());
    }

    public void check(long customerId, long companyId) {
        businessCustomerServiceBlockingStub.getCustomer(GetCustomerRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                .setId(customerId)
                .build());
    }
}
