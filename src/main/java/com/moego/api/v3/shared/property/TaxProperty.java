/*
 * @since 2024-06-18 11:28:52
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.shared.property;

import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.organization.v1.GetTaxRuleRequest;
import com.moego.idl.service.organization.v1.TaxRuleServiceGrpc;
import com.moego.lib.common.auth.AuthContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class TaxProperty {
    private final TaxRuleServiceGrpc.TaxRuleServiceBlockingStub taxRuleServiceBlockingStub;

    public void check(long taxId) {
        check(taxId, AuthContext.get().companyId());
    }

    public void check(long taxId, long companyId) {
        taxRuleServiceBlockingStub.getTaxRule(GetTaxRuleRequest.newBuilder()
                .setId(taxId)
                .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                .build());
    }
}
