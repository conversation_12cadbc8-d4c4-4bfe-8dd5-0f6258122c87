/*
 * @since 2023-07-03 15:48:55
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.shared.util;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.function.Function;
import java.util.stream.Collectors;

public class PrintUtil {

    @Nonnull
    public static String printName(@Nullable String firstName, @Nullable String lastName) {
        return printString(printString(firstName) + " " + printString(lastName));
    }

    @Nonnull
    public static String printString(@Nullable String str) {
        return str == null ? "" : str.trim();
    }

    @Nonnull
    public static <T> String printFieldList(Collection<T> input, Function<T, String> getField) {
        return input.stream().map(getField).collect(Collectors.joining(", "));
    }
}
