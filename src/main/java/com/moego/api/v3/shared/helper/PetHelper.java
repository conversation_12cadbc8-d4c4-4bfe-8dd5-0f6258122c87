package com.moego.api.v3.shared.helper;

import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.CountAppointmentForPetsRequest;
import java.util.Collection;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/2/18
 */
@Component
@RequiredArgsConstructor
public class PetHelper {

    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentStub;

    /**
     * Check given pet ids are new pets or not.
     *
     * <p> new pet: 没有 appointment 的 pet
     *
     * @param petIds pet id list
     * @return pet id -> is new pet, map 的 keys 肯定会包含所有 petIds
     */
    public Map<Long, Boolean> checkNewPets(Collection<Long> petIds) {
        if (petIds.isEmpty()) {
            return Map.of();
        }

        var ids = Set.copyOf(petIds);

        var petIdToAppointmentCount = appointmentStub
                .countAppointmentForPets(CountAppointmentForPetsRequest.newBuilder()
                        .addAllPetIds(ids)
                        .build())
                .getPetIdToAppointmentCountMap();

        return ids.stream()
                .collect(Collectors.toMap(
                        Function.identity(), petId -> petIdToAppointmentCount.getOrDefault(petId, 0) == 0));
    }
}
