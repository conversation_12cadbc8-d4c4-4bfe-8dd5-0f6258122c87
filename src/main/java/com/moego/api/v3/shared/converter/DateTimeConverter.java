package com.moego.api.v3.shared.converter;

import com.google.protobuf.Timestamp;
import com.moego.idl.models.organization.v1.TimeZone;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import org.mapstruct.Mapper;
import org.mapstruct.MappingConstants;
import org.mapstruct.factory.Mappers;

@Mapper(componentModel = MappingConstants.ComponentModel.SPRING)
public interface DateTimeConverter {
    DateTimeConverter INSTANCE = Mappers.getMapper(DateTimeConverter.class);

    default com.google.type.Date toDate(long seconds, TimeZone timezone) {
        Instant instant = Instant.ofEpochSecond(seconds);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.of(timezone.getName()));
        return com.google.type.Date.newBuilder()
                .setYear(localDateTime.getYear())
                .setMonth(localDateTime.getMonthValue())
                .setDay(localDateTime.getDayOfMonth())
                .build();
    }

    default com.google.type.Date toDate(long seconds, int nanos, TimeZone timezone) {
        Instant instant = Instant.ofEpochSecond(seconds, nanos);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.of(timezone.getName()));
        return com.google.type.Date.newBuilder()
                .setYear(localDateTime.getYear())
                .setMonth(localDateTime.getMonthValue())
                .setDay(localDateTime.getDayOfMonth())
                .build();
    }

    default com.google.type.Date toDate(Timestamp timestamp, TimeZone timezone) {
        Instant instant = Instant.ofEpochSecond(timestamp.getSeconds(), timestamp.getNanos());
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.of(timezone.getName()));
        return com.google.type.Date.newBuilder()
                .setYear(localDateTime.getYear())
                .setMonth(localDateTime.getMonthValue())
                .setDay(localDateTime.getDayOfMonth())
                .build();
    }
}
