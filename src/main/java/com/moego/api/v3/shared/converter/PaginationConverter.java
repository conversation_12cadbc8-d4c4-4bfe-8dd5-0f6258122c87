/*
 * @since 2024-06-19 19:07:14
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.shared.converter;

import static org.mapstruct.ReportingPolicy.WARN;

import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.idl.utils.v2.PaginationResponse;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE,
        unmappedTargetPolicy = WARN)
public interface PaginationConverter {
    PaginationRequest request(Integer pageSize, Integer pageNum);

    default PaginationRequest request() {
        return request(20, 1);
    }

    @Mapping(target = "total", ignore = true)
    PaginationResponse response(PaginationRequest request);

    PaginationResponse response(PaginationRequest request, Integer total);

    PaginationResponse response(Integer pageSize, Integer pageNum, Integer total);
}
