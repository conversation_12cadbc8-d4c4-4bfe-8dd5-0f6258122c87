package com.moego.api.v3.shared.helper;

import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.service.order.v1.GetOrderListRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2025/9/9
 */
@Component
@RequiredArgsConstructor
public class OrderHelper {

    private final OrderServiceGrpc.OrderServiceBlockingStub orderStub;

    /**
     * Get orders by source type and source ids.
     *
     * <p> One source id can have multiple orders (new order).
     *
     * @param sourceType source type
     * @param sourceIds  source ids
     * @return source id -> order list
     */
    public Map<Long, List<OrderDetailModel>> getOrdersBySources(
            OrderSourceType sourceType, Collection<Long> sourceIds) {
        if (ObjectUtils.isEmpty(sourceIds)) {
            return Map.of();
        }
        return orderStub
                .getOrderList(GetOrderListRequest.newBuilder()
                        .setSourceType(sourceType.name().toLowerCase())
                        .addAllSourceIds(sourceIds.stream()
                                .map(Number::longValue)
                                .distinct()
                                .toList())
                        .build())
                .getOrderListList()
                .stream()
                .collect(Collectors.groupingBy(
                        orderDetail -> orderDetail.getOrder().getSourceId()));
    }
}
