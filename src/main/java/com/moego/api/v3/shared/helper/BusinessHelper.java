package com.moego.api.v3.shared.helper;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetCompanyIdRequest;
import com.moego.idl.service.organization.v1.GetLocationDetailRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import java.time.LocalDateTime;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2025/3/12
 */
@Component
@RequiredArgsConstructor
public class BusinessHelper {

    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessStub;
    private final CompanyHelper companyHelper;

    /**
     * Get {@link LocalDateTime} in business's timezone, throw exception if business not found.
     *
     * @param businessId business id
     * @return LocalDateTime in company's timezone
     */
    public LocalDateTime mustGetBusinessDateTime(long businessId) {
        var resp = businessStub.getLocationDetail(
                GetLocationDetailRequest.newBuilder().setId(businessId).build());
        if (!resp.hasLocation()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Business not found: " + businessId);
        }
        return companyHelper.mustGetCompanyDateTime(resp.getLocation().getCompanyId());
    }

    public void checkBusinessCompany(long companyId, long businessId) {
        var resp = businessStub.getCompanyId(
                GetCompanyIdRequest.newBuilder().setBusinessId(businessId).build());
        if (resp.getCompanyId() != companyId) {
            throw bizException(Code.CODE_PARAMS_ERROR, "invalid request params");
        }
    }
}
