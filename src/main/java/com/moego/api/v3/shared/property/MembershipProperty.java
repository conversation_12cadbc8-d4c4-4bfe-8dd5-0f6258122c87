/*
 * @since 2024-06-18 14:08:59
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.shared.property;

import com.moego.idl.service.membership.v1.GetMembershipRequest;
import com.moego.idl.service.membership.v1.MembershipServiceGrpc;
import com.moego.lib.common.auth.AuthContext;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

@Component
@RequiredArgsConstructor
public class MembershipProperty {

    private final MembershipServiceGrpc.MembershipServiceBlockingStub membershipServiceBlockingStub;

    public void check(long membershipId) {
        check(membershipId, AuthContext.get().companyId());
    }

    public void check(long membershipId, long companyId) {
        membershipServiceBlockingStub.getMembership(GetMembershipRequest.newBuilder()
                .setCompanyId(companyId)
                .setId(membershipId)
                .build());
    }
}
