package com.moego.api.v3.shared.helper;

import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.ListBookingRequestIdRequest;
import com.moego.server.grooming.client.IBookOnlineDepositClient;
import com.moego.server.grooming.dto.BookOnlineDepositDTO;
import jakarta.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @since 2024/11/26
 */
@Component
@RequiredArgsConstructor
public class OBDepositHelper {

    private final IBookOnlineDepositClient bookOnlineDepositApi;
    private final BookingRequestServiceGrpc.BookingRequestServiceBlockingStub bookingRequestStub;

    /**
     * Get {@link BookOnlineDepositDTO} by businessId and appointmentId.
     *
     * <p> If not found by appointmentId, try to find by bookingRequestId.
     *
     * @param businessId    businessId
     * @param appointmentId appointmentId
     * @return found {@link BookOnlineDepositDTO} or null
     */
    @Nullable
    public BookOnlineDepositDTO getOBDeposit(long businessId, long appointmentId) {
        // 兼容老逻辑，优先使用 appointmentId
        var deposit = bookOnlineDepositApi.getOBDepositByGroomingId(
                Math.toIntExact(businessId), Math.toIntExact(appointmentId));
        if (deposit != null) {
            return deposit;
        }

        // 如果没有找到，再使用 bookingRequestId
        var bookingRequestId = getBookingRequestId(appointmentId);
        if (bookingRequestId == null) {
            return null;
        }
        return bookOnlineDepositApi.getOBDepositByBookingRequestId(Math.toIntExact(businessId), bookingRequestId);
    }

    @Nullable
    private Long getBookingRequestId(long appointmentId) {
        return bookingRequestStub
                .listBookingRequestId(ListBookingRequestIdRequest.newBuilder()
                        .addAppointmentIds(appointmentId)
                        .build())
                .getAppointmentIdToBookingRequestIdMap()
                .get(appointmentId);
    }
}
