package com.moego.api.v3.subscription.converter;

import com.moego.idl.api.subscription.v1.ProductView;

public class ProductConverter {

    public static ProductView.Builder convert2Api(com.moego.idl.models.subscription.v1.Product product) {
        return ProductView.newBuilder()
                .setId(product.getId())
                .setName(product.getName())
                .setDescription(product.getDescription())
                .setType(product.getType())
                .setSeller(product.getSeller())
                .setBusinessType(product.getBusinessType())
                .setExtra(product.getExtra());
    }
}
