package com.moego.api.v3.subscription.converter;

import com.moego.idl.api.subscription.v1.PriceView;

public class PriceConverter {

    public static PriceView.Builder convert2Api(com.moego.idl.models.subscription.v1.Price price) {

        return PriceView.newBuilder()
                .setId(price.getId())
                .setType(price.getType())
                .setUnitAmount(price.getUnitAmount())
                .setBillingCycle(price.getBillingCycle())
                .setPrequalification(price.getPrequalification());
    }
}
