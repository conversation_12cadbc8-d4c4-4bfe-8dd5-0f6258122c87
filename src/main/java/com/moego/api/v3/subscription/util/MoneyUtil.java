package com.moego.api.v3.subscription.util;

import com.google.type.Money;
import java.math.BigDecimal;
import java.math.RoundingMode;

public class MoneyUtil {

    public static BigDecimal getDiscountPercentageOff(BigDecimal part, BigDecimal total) {
        if (total.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        // 计算 part / total
        BigDecimal ratio = part.divide(total, 4, RoundingMode.HALF_UP); // 保留4位小数
        // 计算 1 - ratio
        BigDecimal result = BigDecimal.ONE.subtract(ratio);
        // 乘以 100 得到百分比
        return result.multiply(BigDecimal.valueOf(100)).setScale(2, RoundingMode.HALF_UP); // 保留2位小数
    }

    public static Money bigDecimalToMoney(BigDecimal amount, String currencyCode) {
        // 获取整数部分和小数部分
        long units = amount.longValue(); // 整数部分
        int nanos = amount.subtract(new BigDecimal(units))
                .movePointRight(9) // 将小数部分移至纳秒（9位小数）
                .intValue(); // 小数部分转换为纳秒

        // 创建 Money 对象
        return Money.newBuilder()
                .setCurrencyCode(currencyCode)
                .setUnits(units)
                .setNanos(nanos)
                .build();
    }

    public static BigDecimal moneyToBigDecimal(Money money) {
        // 获取整数部分 (units) 和纳秒部分 (nanos)
        long units = money.getUnits();
        int nanos = money.getNanos();

        // 将整数部分转换为 BigDecimal
        BigDecimal unitsBigDecimal = BigDecimal.valueOf(units);

        // 将纳秒部分转换为 BigDecimal（10^9 纳秒等于 1 单位货币）
        BigDecimal nanosBigDecimal = BigDecimal.valueOf(nanos, 9); // 9 表示小数点后 9 位

        // 将两部分相加，得到最终的 BigDecimal 值
        return unitsBigDecimal.add(nanosBigDecimal);
    }
}
