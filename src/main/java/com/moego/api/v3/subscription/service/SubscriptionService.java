package com.moego.api.v3.subscription.service;

import static com.moego.idl.models.subscription.v1.Feature.Key.CREDIT_CREDIT_POINT;

import com.google.protobuf.TextFormat;
import com.google.protobuf.Timestamp;
import com.google.type.Decimal;
import com.moego.api.v3.subscription.converter.PriceConverter;
import com.moego.api.v3.subscription.converter.ProductConverter;
import com.moego.api.v3.subscription.converter.PurchaseDetailConverter;
import com.moego.api.v3.subscription.util.MoneyUtil;
import com.moego.common.utils.RedisUtil;
import com.moego.idl.api.subscription.v1.CreateSubscriptionParams;
import com.moego.idl.api.subscription.v1.CreateSubscriptionResult;
import com.moego.idl.api.subscription.v1.GetCreditParams;
import com.moego.idl.api.subscription.v1.GetCreditResult;
import com.moego.idl.api.subscription.v1.ListCreditChangeHistoryParams;
import com.moego.idl.api.subscription.v1.ListCreditChangeHistoryResult;
import com.moego.idl.api.subscription.v1.ListEntitlementsParams;
import com.moego.idl.api.subscription.v1.ListEntitlementsResult;
import com.moego.idl.api.subscription.v1.ListProductsParams;
import com.moego.idl.api.subscription.v1.ListProductsResult;
import com.moego.idl.api.subscription.v1.ListSubscriptionsParams;
import com.moego.idl.api.subscription.v1.ListSubscriptionsResult;
import com.moego.idl.api.subscription.v1.PreviewPurchasesParams;
import com.moego.idl.api.subscription.v1.PreviewPurchasesResult;
import com.moego.idl.api.subscription.v1.PriceView;
import com.moego.idl.api.subscription.v1.ProductView;
import com.moego.idl.api.subscription.v1.PurchaseDetailView;
import com.moego.idl.api.subscription.v1.SubscriptionView;
import com.moego.idl.api.subscription.v1.UpdateCreditParams;
import com.moego.idl.api.subscription.v1.UpdateCreditResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.subscription.v1.Count;
import com.moego.idl.models.subscription.v1.Discount;
import com.moego.idl.models.subscription.v1.Entitlement;
import com.moego.idl.models.subscription.v1.Feature;
import com.moego.idl.models.subscription.v1.License;
import com.moego.idl.models.subscription.v1.Price;
import com.moego.idl.models.subscription.v1.Product;
import com.moego.idl.models.subscription.v1.Revision;
import com.moego.idl.models.subscription.v1.User;
import com.moego.idl.service.subscription.v1.CreateLicenseAndEntitlementsRequest;
import com.moego.idl.service.subscription.v1.CreateSubscriptionRequest;
import com.moego.idl.service.subscription.v1.ListDiscountsRequest;
import com.moego.idl.service.subscription.v1.ListDiscountsResponse;
import com.moego.idl.service.subscription.v1.ListEntitlementsRequest;
import com.moego.idl.service.subscription.v1.ListFeaturesRequest;
import com.moego.idl.service.subscription.v1.ListFeaturesResponse;
import com.moego.idl.service.subscription.v1.ListLicensesRequest;
import com.moego.idl.service.subscription.v1.ListPricesRequest;
import com.moego.idl.service.subscription.v1.ListPricesResponse;
import com.moego.idl.service.subscription.v1.ListProductsRequest;
import com.moego.idl.service.subscription.v1.ListProductsResponse;
import com.moego.idl.service.subscription.v1.ListPurchaseDetailsRequest;
import com.moego.idl.service.subscription.v1.ListPurchaseDetailsResponse;
import com.moego.idl.service.subscription.v1.ListRevisionsRequest;
import com.moego.idl.service.subscription.v1.ListSubscriptionsRequest;
import com.moego.idl.service.subscription.v1.PreviewPurchasesRequest;
import com.moego.idl.service.subscription.v1.SubscriptionServiceGrpc;
import com.moego.idl.service.subscription.v1.UpdateEntitlementsRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.utils.StringUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class SubscriptionService {
    @Autowired
    private SubscriptionServiceGrpc.SubscriptionServiceBlockingStub subscriptionServiceBlockingStub;

    private final RedisUtil redisUtil;

    public ListProductsResult listProducts(Long companyID, ListProductsParams listProductsRequest) {
        ListProductsResponse productsResponse =
                subscriptionServiceBlockingStub.listProducts(ListProductsRequest.newBuilder()
                        .setPagination(listProductsRequest.getPagination())
                        .setFilter(ListProductsRequest.Filter.newBuilder()
                                .addAllBusinessTypes(listProductsRequest.getBusinessTypesList())
                                .addAllIds(listProductsRequest.getIdsList())
                                .build())
                        .build());

        List<Long> featureIdList = new ArrayList<>();
        List<Long> productIdList = new ArrayList<>();
        productsResponse.getProductsList().forEach(product -> {
            featureIdList.addAll(product.getFeatureIdsList());
            productIdList.add(product.getId());
        });

        ListFeaturesResponse featuresResponse =
                subscriptionServiceBlockingStub.listFeatures(ListFeaturesRequest.newBuilder()
                        .setFilter(ListFeaturesRequest.Filter.newBuilder()
                                .addAllIds(featureIdList)
                                .build())
                        .build());
        Map<Long, Feature> featureMap = featuresResponse.getFeaturesList().stream()
                .collect(Collectors.toMap(Feature::getId, Function.identity()));

        ListPricesResponse pricesResponse = subscriptionServiceBlockingStub.listPrices(ListPricesRequest.newBuilder()
                .setBuyer(User.newBuilder().setType(User.Type.COMPANY).setId(companyID))
                .setFilter(ListPricesRequest.Filter.newBuilder()
                        .addAllProductIds(productIdList)
                        .build())
                .build());
        List<Long> priceIdList =
                pricesResponse.getPricesList().stream().map(Price::getId).toList();

        ListDiscountsResponse discountsResponse =
                subscriptionServiceBlockingStub.listDiscounts(ListDiscountsRequest.newBuilder()
                        .setFilter(ListDiscountsRequest.Filter.newBuilder()
                                .addAllPriceIds(priceIdList)
                                .build())
                        .build());

        Timestamp now = Timestamp.newBuilder()
                .setSeconds(System.currentTimeMillis() / 1000)
                .build();
        Map<Long, List<Discount>> priceIdToDiscountListMap = discountsResponse.getDiscountsList().stream()
                .filter(discount -> discount.getValidityPeriod().getStartTime().getSeconds() <= now.getSeconds()
                        && discount.getValidityPeriod().getEndTime().getSeconds() >= now.getSeconds())
                .collect(Collectors.groupingBy(Discount::getPriceId));

        Map<Long, List<Price>> productIdToPriceMap =
                pricesResponse.getPricesList().stream().collect(Collectors.groupingBy(Price::getProductId));
        Map<Long, List<PriceView>> apiProductIdToPriceMap = new HashMap<>(productIdToPriceMap.size());
        for (Map.Entry<Long, List<Price>> entry : productIdToPriceMap.entrySet()) {
            List<PriceView> apiPriceList = entry.getValue().stream()
                    .map(price -> {
                        List<Discount> discountList = priceIdToDiscountListMap.getOrDefault(price.getId(), List.of());
                        return buildPrice(price, discountList);
                    })
                    .toList();
            apiProductIdToPriceMap.put(entry.getKey(), apiPriceList);
        }

        List<ProductView> list = productsResponse.getProductsList().stream()
                .map(product -> ProductView.newBuilder()
                        .setId(product.getId())
                        .setName(product.getName())
                        .setDescription(product.getDescription())
                        .setType(product.getType())
                        .setSeller(product.getSeller())
                        .setBusinessType(product.getBusinessType())
                        .setExtra(product.getExtra())
                        .addAllFeatures(product.getFeatureIdsList().stream()
                                .map(featureMap::get)
                                .toList())
                        .addAllPrices(apiProductIdToPriceMap.get(product.getId()))
                        .build())
                .toList();

        return ListProductsResult.newBuilder()
                .setPagination(productsResponse.getPagination())
                .addAllProducts(list)
                .build();
    }

    private List<PurchaseDetailView> buildPurchaseDetailList(Long subscriptionId) {
        ListPurchaseDetailsResponse listPurchaseDetailsResponse =
                subscriptionServiceBlockingStub.listPurchaseDetails(ListPurchaseDetailsRequest.newBuilder()
                        .setFilter(ListPurchaseDetailsRequest.Filter.newBuilder()
                                .addSubscriptionIds(subscriptionId)
                                .build())
                        .build());

        return listPurchaseDetailsResponse.getPurchaseDetailsList().stream()
                .map(purchaseDetail -> {
                    // 转换price
                    PriceView price = buildPrice(purchaseDetail.getPrice(), List.of());
                    // 计算original price
                    BigDecimal originalTotalPrice = MoneyUtil.moneyToBigDecimal(price.getUnitAmount())
                            .multiply(BigDecimal.valueOf(purchaseDetail.getQuantity()));
                    // 计算优惠百分比
                    BigDecimal discountPercentageOff = MoneyUtil.getDiscountPercentageOff(
                            MoneyUtil.moneyToBigDecimal(purchaseDetail.getTotalPrice()), originalTotalPrice);

                    return PurchaseDetailView.newBuilder()
                            .setId(purchaseDetail.getId())
                            .setSubscriptionId(purchaseDetail.getSubscriptionId())
                            .setProduct(ProductConverter.convert2Api(purchaseDetail.getProduct()))
                            .setPrice(price)
                            .setQuantity(purchaseDetail.getQuantity())
                            .setTotalPrice(purchaseDetail.getTotalPrice())
                            .setOriginalTotalPrice(MoneyUtil.bigDecimalToMoney(
                                    originalTotalPrice, price.getUnitAmount().getCurrencyCode()))
                            .setDiscountPercentageOff(Decimal.newBuilder()
                                    .setValue(discountPercentageOff.toString())
                                    .build())
                            .build();
                })
                .toList();
    }

    public CreateSubscriptionResult createSubscription(
            Long companyID, CreateSubscriptionParams createSubscriptionRequest) {
        var response = subscriptionServiceBlockingStub.createSubscription(CreateSubscriptionRequest.newBuilder()
                .setBuyer(User.newBuilder()
                        .setType(User.Type.COMPANY)
                        .setId(companyID)
                        .build())
                .addAllPurchases(createSubscriptionRequest.getPurchasesList())
                .setCardOnFileId(createSubscriptionRequest.getCardOnFileId())
                .build());

        var subscription = SubscriptionView.newBuilder()
                .setId(response.getSubscription().getId())
                .setName(response.getSubscription().getName())
                .setDescription(response.getSubscription().getDescription())
                .setStatus(response.getSubscription().getStatus())
                .setValidityPeriod(response.getSubscription().getValidityPeriod())
                .setBillingCycle(response.getSubscription().getBillingCycle())
                .setGracePeriod(response.getSubscription().getGracePeriod())
                .setCancelAtPeriodEnd(response.getSubscription().getCancelAtPeriodEnd())
                .setBuyer(response.getSubscription().getBuyer())
                .setSeller(response.getSubscription().getSeller())
                .addAllPurchaseDetails(
                        buildPurchaseDetailList(response.getSubscription().getId()))
                .build();

        return CreateSubscriptionResult.newBuilder()
                .setSubscription(subscription)
                .setExternalStripeClientSecret(response.getExternalStripeClientSecret())
                .build();
    }

    private PriceView buildPrice(Price price, List<Discount> discountList) {

        PriceView.Builder builder = PriceConverter.convert2Api(price);
        BigDecimal totalDiscount = discountList.stream()
                .map(item -> MoneyUtil.moneyToBigDecimal(item.getAmountOff()))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal discountedUnitAmount =
                MoneyUtil.moneyToBigDecimal(price.getUnitAmount()).subtract(totalDiscount);
        if (discountedUnitAmount.compareTo(BigDecimal.ZERO) < 0) {
            discountedUnitAmount = new BigDecimal(0);
        }
        BigDecimal discountPercentageOff = MoneyUtil.getDiscountPercentageOff(
                discountedUnitAmount, MoneyUtil.moneyToBigDecimal(price.getUnitAmount()));

        return builder.addAllDiscounts(discountList)
                .setDiscountedUnitAmount(MoneyUtil.bigDecimalToMoney(
                        discountedUnitAmount, price.getUnitAmount().getCurrencyCode()))
                .setDiscountPercentageOff(Decimal.newBuilder()
                        .setValue(discountPercentageOff.toString())
                        .build())
                .build();
    }

    public ListSubscriptionsResult listSubscriptions(Long companyId, ListSubscriptionsParams request) {
        List<Long> productIdList = subscriptionServiceBlockingStub
                .listProducts(ListProductsRequest.newBuilder()
                        .setFilter(ListProductsRequest.Filter.newBuilder()
                                .addAllBusinessTypes(request.getPlanProductBusinessTypesList()))
                        .build())
                .getProductsList()
                .stream()
                .map(Product::getId)
                .collect(Collectors.toList());

        var response = subscriptionServiceBlockingStub.listSubscriptions(ListSubscriptionsRequest.newBuilder()
                .setPagination(request.getPagination())
                .setFilter(ListSubscriptionsRequest.Filter.newBuilder()
                        .addBuyers(User.newBuilder()
                                .setType(User.Type.COMPANY)
                                .setId(companyId)
                                .build())
                        .addAllPlanProductIds(productIdList)
                        .addAllStatuses(request.getStatusesList())
                        .addAllIds(request.getIdsList())
                        .build())
                .build());
        if (CollectionUtils.isEmpty(response.getSubscriptionsList())) {
            return ListSubscriptionsResult.newBuilder()
                    .setPagination(response.getPagination())
                    .build();
        }

        List<SubscriptionView> list = response.getSubscriptionsList().stream()
                .map(subscription -> SubscriptionView.newBuilder()
                        .setId(subscription.getId())
                        .setName(subscription.getName())
                        .setDescription(subscription.getDescription())
                        .setStatus(subscription.getStatus())
                        .setValidityPeriod(subscription.getValidityPeriod())
                        .setBillingCycle(subscription.getBillingCycle())
                        .setGracePeriod(subscription.getGracePeriod())
                        .setCancelAtPeriodEnd(subscription.getCancelAtPeriodEnd())
                        .setBuyer(subscription.getBuyer())
                        .setSeller(subscription.getSeller())
                        .addAllPurchaseDetails(buildPurchaseDetailList(subscription.getId()))
                        .build())
                .toList();

        return ListSubscriptionsResult.newBuilder()
                .setPagination(response.getPagination())
                .addAllSubscriptions(list)
                .build();
    }

    public PreviewPurchasesResult previewPurchases(Long companyId, PreviewPurchasesParams request) {
        var response = subscriptionServiceBlockingStub.previewPurchases(PreviewPurchasesRequest.newBuilder()
                .addAllPurchases(request.getPurchasesList())
                .build());

        return PreviewPurchasesResult.newBuilder()
                .addAllPurchaseDetails(PurchaseDetailConverter.convert2ApiList(response.getPurchaseDetailsList()))
                .build();
    }

    public ListEntitlementsResult listEntitlements(Long companyId, ListEntitlementsParams request) {
        var licenseResponse = subscriptionServiceBlockingStub.listLicenses(ListLicensesRequest.newBuilder()
                .setFilter(ListLicensesRequest.Filter.newBuilder()
                        .addOwners(User.newBuilder()
                                .setType(User.Type.COMPANY)
                                .setId(companyId)
                                .build())
                        .addAllStatuses(request.getLicenseStatusesList())
                        .build())
                .build());
        List<Long> licenseIdList =
                licenseResponse.getLicensesList().stream().map(License::getId).toList();

        var response = subscriptionServiceBlockingStub.listEntitlements(ListEntitlementsRequest.newBuilder()
                .setFilter(ListEntitlementsRequest.Filter.newBuilder()
                        .addAllLicenceIds(licenseIdList)
                        .addAllFeatureKeys(request.getFeatureKeysList())
                        .build())
                .build());

        return ListEntitlementsResult.newBuilder()
                .addAllEntitlements(response.getEntitlementsList())
                .build();
    }

    public UpdateCreditResult updateCredit(UpdateCreditParams params) {
        var setting = Feature.Setting.newBuilder()
                .setCount(Count.newBuilder().setTotalAmount(params.getCredit()).build())
                .build();
        var feature = Feature.newBuilder()
                .setKey(CREDIT_CREDIT_POINT)
                .setSetting(setting)
                .build();

        var response = subscriptionServiceBlockingStub.updateEntitlements(UpdateEntitlementsRequest.newBuilder()
                .setUser(params.getUser())
                .addFeatures(feature)
                .setRelated(params.toString())
                .build());
        // 其实这里没意义
        if (Objects.isNull(response)) {
            return UpdateCreditResult.newBuilder().setSuccess(false).build();
        }
        return UpdateCreditResult.newBuilder().setSuccess(response.getSuccess()).build();
    }

    public ListCreditChangeHistoryResult listCreditChangeHistory(ListCreditChangeHistoryParams params) {
        var resp = subscriptionServiceBlockingStub.listRevisions(ListRevisionsRequest.newBuilder()
                .addSubscriptionIds(0)
                .addTypes(Revision.Type.UPDATE_CREDIT)
                .putDetailKeys(
                        "updateEntitlementDetail.owner.id",
                        String.valueOf(params.getUser().getId()))
                .setPagination(params.getPagination())
                .build());
        if (Objects.isNull(resp) || resp.getRevisionsList().isEmpty()) {
            return ListCreditChangeHistoryResult.newBuilder()
                    .addAllHistories(List.of())
                    .build();
        }
        var histories = new ArrayList<ListCreditChangeHistoryResult.CreditChangeHistory>();
        for (var revision : resp.getRevisionsList()) {
            var createTime = revision.getCreatedAt();
            var detail = revision.getDetail();
            if (detail.hasUpdateEntitlementDetail()) {
                var ue = detail.getUpdateEntitlementDetail();
                try {
                    // FIXME(jett): 图省事直接序列化成UpdateCreditParams了
                    UpdateCreditParams.Builder builder = UpdateCreditParams.newBuilder();
                    TextFormat.getParser().merge(ue.getDetails().trim(), builder);
                    var ucp = builder.build();
                    var history = ListCreditChangeHistoryResult.CreditChangeHistory.newBuilder()
                            .setId(revision.getId())
                            .setCredit(ucp.getCredit())
                            .setUser(User.newBuilder()
                                    .setId(ue.getOperatorId())
                                    .setType(User.Type.STAFF)
                                    .build())
                            .setType(ucp.getType())
                            .setNote(ucp.getNote())
                            .setReason(ucp.getReason())
                            .setAssociation(ListCreditChangeHistoryResult.CreditChangeHistory.Association.newBuilder()
                                    .setInvoiceId(ucp.getInvoiceId())
                                    .setAppointmentId(ucp.getAppointmentId())
                                    .build())
                            .setCreatedTime(createTime)
                            .build();

                    histories.add(history);
                } catch (TextFormat.ParseException e) {
                    log.error("parse revision error, id: {}", revision.getId(), e);
                }
            }
        }
        return ListCreditChangeHistoryResult.newBuilder()
                .addAllHistories(histories)
                .setPagination(resp.getPagination())
                .build();
    }

    public GetCreditResult getCredit(GetCreditParams params) {
        var licenses = subscriptionServiceBlockingStub
                .listLicenses(ListLicensesRequest.newBuilder()
                        .setFilter(ListLicensesRequest.Filter.newBuilder()
                                .addOwners(params.getUser())
                                .build())
                        .build())
                .getLicensesList();
        if (licenses.isEmpty()) {
            licenses = createLicenses(params.getUser());
        }

        var entitlements = subscriptionServiceBlockingStub.listEntitlements(ListEntitlementsRequest.newBuilder()
                .setFilter(ListEntitlementsRequest.Filter.newBuilder()
                        .addAllLicenceIds(licenses.stream().map(License::getId).toList())
                        .addAllFeatureKeys(List.of(CREDIT_CREDIT_POINT))
                        .build())
                .build());

        if (Objects.isNull(entitlements) || entitlements.getEntitlementsList().isEmpty()) {
            licenses = createLicenses(params.getUser());
            entitlements = subscriptionServiceBlockingStub.listEntitlements(ListEntitlementsRequest.newBuilder()
                    .setFilter(ListEntitlementsRequest.Filter.newBuilder()
                            .addAllLicenceIds(
                                    licenses.stream().map(License::getId).toList())
                            .addAllFeatureKeys(List.of(CREDIT_CREDIT_POINT))
                            .build())
                    .build());
        }

        var countStream = entitlements.getEntitlementsList().stream()
                .map(Entitlement::getFeature)
                .map(Feature::getSetting)
                .map(Feature.Setting::getCount)
                .toList();

        var total = countStream.stream().mapToLong(Count::getTotalAmount).sum();
        var used = countStream.stream().mapToLong(Count::getUsedAmount).sum();
        // 剩余
        return GetCreditResult.newBuilder().setCredit(total - used).build();
    }

    private List<License> createLicenses(User owner) {
        var resp = subscriptionServiceBlockingStub.createLicenseAndEntitlements(
                CreateLicenseAndEntitlementsRequest.newBuilder()
                        .setBuyer(owner)
                        .addFeatureIds(getCreditID())
                        .setLicenseStatus(License.Status.VALID)
                        .build());

        log.info("create license response: {}", resp);
        var license = subscriptionServiceBlockingStub.listLicenses(ListLicensesRequest.newBuilder()
                .setFilter(ListLicensesRequest.Filter.newBuilder()
                        .addOwners(owner)
                        .addStatuses(License.Status.VALID)
                        .build())
                .build());
        return license.getLicensesList();
    }

    private Long getCreditID() {
        String CREDIT_CREDIT_POINT_FEATURE_ID = "CREDIT_CREDIT_POINT_FEATURE_ID";
        var id = redisUtil.get(CREDIT_CREDIT_POINT_FEATURE_ID);
        if (StringUtils.isBlank(id))
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "credit feature id not found");
        return Long.parseLong(id);
    }
}
