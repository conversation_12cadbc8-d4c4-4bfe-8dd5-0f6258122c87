package com.moego.api.v3.subscription.converter;

import com.google.type.Decimal;
import com.moego.api.v3.subscription.util.MoneyUtil;
import com.moego.idl.api.subscription.v1.PurchaseDetailView;
import java.math.BigDecimal;
import java.util.List;
import org.springframework.util.CollectionUtils;

public class PurchaseDetailConverter {

    public static List<PurchaseDetailView> convert2ApiList(
            List<com.moego.idl.models.subscription.v1.PurchaseDetail> purchaseDetailList) {
        if (CollectionUtils.isEmpty(purchaseDetailList)) {
            return List.of();
        }
        return purchaseDetailList.stream()
                .map(PurchaseDetailConverter::convert2Api)
                .toList();
    }

    public static PurchaseDetailView convert2Api(com.moego.idl.models.subscription.v1.PurchaseDetail purchaseDetail) {
        if (null == purchaseDetail) {
            return null;
        }

        // 计算original price
        BigDecimal originalTotalPrice = MoneyUtil.moneyToBigDecimal(
                        purchaseDetail.getPrice().getUnitAmount())
                .multiply(BigDecimal.valueOf(purchaseDetail.getQuantity()));
        // 计算优惠百分比
        BigDecimal discountPercentageOff = MoneyUtil.getDiscountPercentageOff(
                MoneyUtil.moneyToBigDecimal(purchaseDetail.getTotalPrice()), originalTotalPrice);
        return PurchaseDetailView.newBuilder()
                .setId(purchaseDetail.getId())
                .setSubscriptionId(purchaseDetail.getSubscriptionId())
                .setProduct(ProductConverter.convert2Api(purchaseDetail.getProduct()))
                .setPrice(PriceConverter.convert2Api(purchaseDetail.getPrice()))
                .setQuantity(purchaseDetail.getQuantity())
                .setTotalPrice(purchaseDetail.getTotalPrice())
                .setOriginalTotalPrice(MoneyUtil.bigDecimalToMoney(
                        originalTotalPrice,
                        purchaseDetail.getPrice().getUnitAmount().getCurrencyCode()))
                .setDiscountPercentageOff(Decimal.newBuilder()
                        .setValue(discountPercentageOff.toString())
                        .build())
                .build();
    }
}
