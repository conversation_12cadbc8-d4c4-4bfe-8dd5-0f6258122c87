package com.moego.api.v3.offering.controller.v2;

import com.moego.api.v3.offering.helper.ServiceManagementHelper;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.api.offering.v2.CalculatePricingRuleParams;
import com.moego.idl.api.offering.v2.CalculatePricingRuleResult;
import com.moego.idl.api.offering.v2.CheckConfigurationParams;
import com.moego.idl.api.offering.v2.CheckConfigurationResult;
import com.moego.idl.api.offering.v2.DeletePricingRuleParams;
import com.moego.idl.api.offering.v2.DeletePricingRuleResult;
import com.moego.idl.api.offering.v2.GetDiscountSettingParams;
import com.moego.idl.api.offering.v2.GetDiscountSettingResult;
import com.moego.idl.api.offering.v2.GetPricingRuleOverviewParams;
import com.moego.idl.api.offering.v2.GetPricingRuleOverviewResult;
import com.moego.idl.api.offering.v2.GetPricingRuleParams;
import com.moego.idl.api.offering.v2.GetPricingRuleResult;
import com.moego.idl.api.offering.v2.ListAssociatedServicesParams;
import com.moego.idl.api.offering.v2.ListAssociatedServicesResult;
import com.moego.idl.api.offering.v2.ListPricingRulesParams;
import com.moego.idl.api.offering.v2.ListPricingRulesResult;
import com.moego.idl.api.offering.v2.PreviewPricingRuleParams;
import com.moego.idl.api.offering.v2.PreviewPricingRuleResult;
import com.moego.idl.api.offering.v2.PricingRuleServiceGrpc;
import com.moego.idl.api.offering.v2.UpdateDiscountSettingParams;
import com.moego.idl.api.offering.v2.UpdateDiscountSettingResult;
import com.moego.idl.api.offering.v2.UpsertPricingRuleParams;
import com.moego.idl.api.offering.v2.UpsertPricingRuleResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.offering.v2.ListPricingRuleFilter;
import com.moego.idl.models.offering.v2.PetDetailCalculateDef;
import com.moego.idl.models.offering.v2.PetDetailCalculateResultDef;
import com.moego.idl.models.offering.v2.PreviewPetDetailCalculateResultDef;
import com.moego.idl.models.offering.v2.PricingRule;
import com.moego.idl.models.offering.v2.PricingRuleUpsertDef;
import com.moego.idl.models.offering.v2.RuleType;
import com.moego.idl.models.order.v1.ServiceCharge;
import com.moego.idl.models.order.v1.SurchargeType;
import com.moego.idl.service.appointment.v2.PricingRuleApplyServiceGrpc;
import com.moego.idl.service.appointment.v2.UpdateUpcomingAppointmentUsingPricingRuleRequest;
import com.moego.idl.service.offering.v1.ListServiceRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.offering.v2.CalculatePricingRuleRequest;
import com.moego.idl.service.offering.v2.CalculatePricingRuleResponse;
import com.moego.idl.service.offering.v2.DeletePricingRuleRequest;
import com.moego.idl.service.offering.v2.GetDiscountSettingRequest;
import com.moego.idl.service.offering.v2.GetPricingRuleRequest;
import com.moego.idl.service.offering.v2.GetPricingRuleResponse;
import com.moego.idl.service.offering.v2.ListPricingRulesRequest;
import com.moego.idl.service.offering.v2.ListPricingRulesResponse;
import com.moego.idl.service.offering.v2.UpdateDiscountSettingRequest;
import com.moego.idl.service.offering.v2.UpsertPricingRuleRequest;
import com.moego.idl.service.offering.v2.UpsertPricingRuleResponse;
import com.moego.idl.service.order.v1.GetCompanyServiceChargeListRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.utils.model.Pair;
import io.grpc.stub.StreamObserver;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class PricingRuleController extends PricingRuleServiceGrpc.PricingRuleServiceImplBase {

    private final com.moego.idl.service.offering.v2.PricingRuleServiceGrpc.PricingRuleServiceBlockingStub
            pricingRuleService;
    private final PricingRuleApplyServiceGrpc.PricingRuleApplyServiceBlockingStub pricingRuleApplyService;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementService;
    private final com.moego.idl.service.order.v1.ServiceChargeCompanyServiceGrpc.ServiceChargeCompanyServiceBlockingStub
            serviceChargeCompanyClient;
    private final ServiceManagementHelper serviceManagementHelper;

    @Override
    @Auth(AuthType.COMPANY)
    public void upsertPricingRule(
            UpsertPricingRuleParams request, StreamObserver<UpsertPricingRuleResult> responseObserver) {

        Long companyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();

        PricingRuleUpsertDef def = request.getPricingRuleDef();
        if (def.getIsActive()) {
            checkConfiguration(companyId, def);
        }

        Set<Long> needUpdateServiceIds =
                def.hasId() ? getNeedUpdateServiceIds(companyId, def.getId()) : new HashSet<>();

        UpsertPricingRuleResponse response = pricingRuleService.upsertPricingRule(UpsertPricingRuleRequest.newBuilder()
                .setPricingRuleDef(def)
                .setCompanyId(companyId)
                .setStaffId(staffId)
                .build());
        needUpdateServiceIds.addAll(
                getNeedUpdateServiceIds(companyId, response.getPricingRule().getId()));

        if (request.getApplyToUpcomingAppointments() && !CollectionUtils.isEmpty(needUpdateServiceIds)) {
            ThreadPool.execute(() -> pricingRuleApplyService.updateUpcomingAppointmentUsingPricingRule(
                    UpdateUpcomingAppointmentUsingPricingRuleRequest.newBuilder()
                            .addAllServiceIds(needUpdateServiceIds)
                            .setCompanyId(companyId)
                            .setStaffId(staffId)
                            .build()));
        }

        responseObserver.onNext(UpsertPricingRuleResult.newBuilder()
                .setPricingRule(response.getPricingRule())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getPricingRule(GetPricingRuleParams request, StreamObserver<GetPricingRuleResult> responseObserver) {

        GetPricingRuleResponse response = pricingRuleService.getPricingRule(GetPricingRuleRequest.newBuilder()
                .setId(request.getId())
                .setCompanyId(AuthContext.get().companyId())
                .build());

        responseObserver.onNext(GetPricingRuleResult.newBuilder()
                .setPricingRule(response.getPricingRule())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listPricingRules(
            ListPricingRulesParams request, StreamObserver<ListPricingRulesResult> responseObserver) {
        var companyId = AuthContext.get().companyId();
        var filter = request.getFilter();
        var pagination = request.getPagination();

        // pre filter zone rules
        List<Long> excludeIds = new ArrayList<>();
        if (request.getFilter().getRuleTypesList().contains(RuleType.ZONE)) {
            ListPricingRulesResponse response = pricingRuleService.listPricingRules(ListPricingRulesRequest.newBuilder()
                    .setFilter(filter)
                    .setPagination(pagination)
                    .setCompanyId(companyId)
                    .build());
            Set<Long> activeGroomingServiceIds = Stream.concat(
                            serviceManagementHelper.fetchAllGroomingServiceWithInactive(companyId).stream()
                                    .map(ServiceModel::getServiceId),
                            serviceManagementHelper.fetchAllAddOnWithInactive(companyId).stream()
                                    .map(ServiceModel::getServiceId))
                    .collect(Collectors.toSet());
            response.getPricingRulesList().stream()
                    .filter(rule -> {
                        if (rule.getAllGroomingApplicable() || rule.getAllAddonApplicable()) {
                            return false;
                        }
                        boolean disjointGrooming =
                                Collections.disjoint(rule.getSelectedGroomingServicesList(), activeGroomingServiceIds);
                        boolean disjointAddOn =
                                Collections.disjoint(rule.getSelectedAddonServicesList(), activeGroomingServiceIds);
                        return disjointGrooming && disjointAddOn;
                    })
                    .map(PricingRule::getId)
                    .forEach(excludeIds::add);
        }

        if (!excludeIds.isEmpty()) {
            filter = filter.toBuilder().addAllExcludeIds(excludeIds).build();

            ThreadPool.execute(() -> excludeIds.forEach(id -> {
                pricingRuleService.deletePricingRule(DeletePricingRuleRequest.newBuilder()
                        .setId(id)
                        .setCompanyId(companyId)
                        .setStaffId(AuthContext.get().staffId())
                        .build());
                log.info("Deleted pricing rule because of no active services: {}", id);
            }));
        }

        ListPricingRulesResponse response = pricingRuleService.listPricingRules(ListPricingRulesRequest.newBuilder()
                .setFilter(filter)
                .setPagination(pagination)
                .setCompanyId(companyId)
                .build());

        responseObserver.onNext(ListPricingRulesResult.newBuilder()
                .addAllPricingRules(response.getPricingRulesList())
                .setPagination(response.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void calculatePricingRule(
            CalculatePricingRuleParams request, StreamObserver<CalculatePricingRuleResult> responseObserver) {
        CalculatePricingRuleRequest.Builder builder = CalculatePricingRuleRequest.newBuilder()
                .addAllPetDetails(request.getPetDetailsList())
                .setIsPreview(false)
                .setCompanyId(AuthContext.get().companyId());
        CalculatePricingRuleResponse response = pricingRuleService.calculatePricingRule(builder.build());

        List<PricingRule> pricingRules = new ArrayList<>();
        List<Long> pricingRuleIds = response.getPetDetailsList().stream()
                .map(PetDetailCalculateResultDef::getAppliedRuleIdsList)
                .flatMap(List::stream)
                .filter(CommonUtil::isNormal)
                .distinct()
                .toList();
        if (!pricingRuleIds.isEmpty()) {
            pricingRules = pricingRuleService
                    .listPricingRules(ListPricingRulesRequest.newBuilder()
                            .setPagination(PaginationRequest.newBuilder()
                                    .setPageNum(1)
                                    .setPageSize(1000)
                                    .build())
                            .setFilter(ListPricingRuleFilter.newBuilder()
                                    .addAllIds(pricingRuleIds)
                                    .build())
                            .setCompanyId(AuthContext.get().companyId())
                            .build())
                    .getPricingRulesList();
        }

        var collect = response.getPetDetailsList().stream()
                .collect(Collectors.groupingBy(petDetail -> Pair.of(petDetail.getPetId(), petDetail.getServiceId())));
        var result = collect.entrySet().stream()
                .map(entry -> {
                    var averagePrice = entry.getValue().stream()
                            .map(PetDetailCalculateResultDef::getAdjustedPrice)
                            .mapToDouble(Number::doubleValue)
                            .average()
                            .orElse(0);
                    var ruleIds = entry.getValue().stream()
                            .map(PetDetailCalculateResultDef::getAppliedRuleIdsList)
                            .flatMap(List::stream)
                            .filter(CommonUtil::isNormal)
                            .distinct()
                            .toList();
                    return PetDetailCalculateResultDef.newBuilder()
                            .setPetId(entry.getKey().key())
                            .setServiceId(entry.getKey().value())
                            .setAdjustedPrice(averagePrice)
                            .addAllAppliedRuleIds(ruleIds)
                            .build();
                })
                .toList();

        responseObserver.onNext(CalculatePricingRuleResult.newBuilder()
                .addAllPetDetails(result)
                .addAllPricingRules(pricingRules)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void previewPricingRule(
            PreviewPricingRuleParams request, StreamObserver<PreviewPricingRuleResult> responseObserver) {
        var calculateDefs = request.getPetDetailsList().stream()
                .map(petDetail -> IntStream.range(0, petDetail.getStayLength())
                        .mapToObj(i -> LocalDate.of(2099, 1, 1).plusDays(i))
                        .map(LocalDate::toString)
                        .map(date -> PetDetailCalculateDef.newBuilder()
                                .setPetId(petDetail.getPetId())
                                .setServiceId(petDetail.getServiceId())
                                .setServicePrice(petDetail.getServicePrice())
                                .setLodgingUnitId(0)
                                .setServiceDate(date)
                                .build())
                        .toList())
                .flatMap(List::stream)
                .toList();

        CalculatePricingRuleRequest.Builder builder = CalculatePricingRuleRequest.newBuilder()
                .addAllPetDetails(calculateDefs)
                .setSetting(request.getSetting())
                .setIsPreview(true)
                .setCompanyId(AuthContext.get().companyId());
        CalculatePricingRuleResponse response = pricingRuleService.calculatePricingRule(builder.build());

        if (response.getPetDetailsCount() == 0) {
            responseObserver.onNext(
                    PreviewPricingRuleResult.newBuilder().setNoResult(true).build());
            responseObserver.onCompleted();
            return;
        }

        // with using pricing rules
        var resultDefs = response.getPetDetailsList().stream()
                .collect(Collectors.toMap(
                        service -> service.getPetId() + "_" + service.getServiceId() + "_"
                                + BigDecimal.valueOf(service.getAdjustedPrice()),
                        Function.identity(),
                        (existing, replacement) -> existing))
                .values()
                .stream()
                .map(petDetail -> PreviewPetDetailCalculateResultDef.newBuilder()
                        .setPetId(petDetail.getPetId())
                        .setServiceId(petDetail.getServiceId())
                        .setAdjustedPrice(petDetail.getAdjustedPrice())
                        .build())
                .toList();

        // without using pricing rules
        var originDefs = request.getPetDetailsList().stream()
                .collect(Collectors.toMap(
                        service -> service.getPetId() + "_" + service.getServiceId(),
                        Function.identity(),
                        (existing, replacement) -> existing))
                .values()
                .stream()
                .filter(petDetail -> resultDefs.stream()
                        .noneMatch(result -> Objects.equals(result.getPetId(), petDetail.getPetId())
                                && Objects.equals(result.getServiceId(), petDetail.getServiceId())))
                .map(petDetail -> PreviewPetDetailCalculateResultDef.newBuilder()
                        .setPetId(petDetail.getPetId())
                        .setServiceId(petDetail.getServiceId())
                        .setAdjustedPrice(petDetail.getServicePrice())
                        .build())
                .toList();

        responseObserver.onNext(PreviewPricingRuleResult.newBuilder()
                .addAllPetDetails(originDefs)
                .addAllPetDetails(resultDefs)
                .setFormula(response.getFormula())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deletePricingRule(
            DeletePricingRuleParams request, StreamObserver<DeletePricingRuleResult> responseObserver) {

        Long companyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();

        Set<Long> needUpdateServiceIds = getNeedUpdateServiceIds(companyId, request.getId());

        pricingRuleService.deletePricingRule(DeletePricingRuleRequest.newBuilder()
                .setId(request.getId())
                .setCompanyId(companyId)
                .setStaffId(staffId)
                .build());

        if (request.getApplyToUpcomingAppointments() && !CollectionUtils.isEmpty(needUpdateServiceIds)) {
            ThreadPool.execute(() -> pricingRuleApplyService.updateUpcomingAppointmentUsingPricingRule(
                    UpdateUpcomingAppointmentUsingPricingRuleRequest.newBuilder()
                            .addAllServiceIds(needUpdateServiceIds)
                            .setCompanyId(companyId)
                            .setStaffId(staffId)
                            .build()));
        }

        responseObserver.onNext(DeletePricingRuleResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    private Set<Long> getNeedUpdateServiceIds(Long companyId, long id) {
        Set<Long> needUpdateServiceIds = new HashSet<>();
        PricingRule prevPricingRule = pricingRuleService
                .getPricingRule(GetPricingRuleRequest.newBuilder()
                        .setId(id)
                        .setCompanyId(companyId)
                        .build())
                .getPricingRule();
        if (prevPricingRule.getAllBoardingApplicable() || prevPricingRule.getAllDaycareApplicable()) {
            var serviceItemTypes = new ArrayList<ServiceItemType>();
            if (prevPricingRule.getAllBoardingApplicable()) {
                serviceItemTypes.add(ServiceItemType.BOARDING);
            }
            if (prevPricingRule.getAllDaycareApplicable()) {
                serviceItemTypes.add(ServiceItemType.DAYCARE);
            }
            serviceManagementService
                    .listService(ListServiceRequest.newBuilder()
                            .addAllServiceItemTypes(serviceItemTypes)
                            .setServiceType(ServiceType.SERVICE)
                            .setPagination(PaginationRequest.newBuilder()
                                    .setPageNum(1)
                                    .setPageSize(1000)
                                    .build())
                            .setTokenCompanyId(companyId)
                            .build())
                    .getServicesList()
                    .stream()
                    .map(ServiceModel::getServiceId)
                    .forEach(needUpdateServiceIds::add);
        } else {
            needUpdateServiceIds.addAll(prevPricingRule.getSelectedBoardingServicesList());
            needUpdateServiceIds.addAll(prevPricingRule.getSelectedDaycareServicesList());
        }

        if (prevPricingRule.getAllGroomingApplicable()) {
            serviceManagementHelper.fetchAllGroomingService(companyId).stream()
                    .map(ServiceModel::getServiceId)
                    .forEach(needUpdateServiceIds::add);
        } else {
            needUpdateServiceIds.addAll(prevPricingRule.getSelectedGroomingServicesList());
        }

        if (prevPricingRule.getAllAddonApplicable()) {
            serviceManagementHelper.fetchAllAddOn(companyId).stream()
                    .map(ServiceModel::getServiceId)
                    .forEach(needUpdateServiceIds::add);
        } else {
            needUpdateServiceIds.addAll(prevPricingRule.getSelectedAddonServicesList());
        }

        return needUpdateServiceIds;
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listAssociatedServices(
            ListAssociatedServicesParams request, StreamObserver<ListAssociatedServicesResult> responseObserver) {
        // peak date rules do not check associated services
        if (Objects.equals(RuleType.PEAK_DATE, request.getType())) {
            responseObserver.onNext(ListAssociatedServicesResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        var ruleFilter = ListPricingRuleFilter.newBuilder()
                .addRuleTypes(request.getType())
                .setIsActive(true);
        if (request.hasExcludePricingRuleId()) {
            ruleFilter.addExcludeIds(request.getExcludePricingRuleId());
        }

        var pricingRules = pricingRuleService
                .listPricingRules(ListPricingRulesRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setFilter(ruleFilter.build())
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1000)
                                .build())
                        .build())
                .getPricingRulesList();

        if (CollectionUtils.isEmpty(pricingRules)) {
            responseObserver.onNext(ListAssociatedServicesResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        var builder = ListAssociatedServicesResult.newBuilder();
        for (PricingRule rule : pricingRules) {
            if (rule.getAllBoardingApplicable()) {
                builder.setAllBoardingAssociated(true);
            }
            if (!rule.getAllBoardingApplicable() && rule.getSelectedBoardingServicesCount() > 0) {
                builder.addAllAssociatedBoardingServiceIds(rule.getSelectedBoardingServicesList());
            }
            if (rule.getAllDaycareApplicable()) {
                builder.setAllDaycareAssociated(true);
            }
            if (!rule.getAllDaycareApplicable() && rule.getSelectedDaycareServicesCount() > 0) {
                builder.addAllAssociatedDaycareServiceIds(rule.getSelectedDaycareServicesList());
            }
            if (rule.getAllGroomingApplicable()) {
                builder.setAllGroomingAssociated(true);
            }
            if (!rule.getAllGroomingApplicable() && rule.getSelectedGroomingServicesCount() > 0) {
                builder.addAllAssociatedGroomingServiceIds(rule.getSelectedGroomingServicesList());
            }
            if (rule.getAllAddonApplicable()) {
                builder.setAllAddonAssociated(true);
            }
            if (!rule.getAllAddonApplicable() && rule.getSelectedAddonServicesCount() > 0) {
                builder.addAllAssociatedAddonServiceIds(rule.getSelectedAddonServicesList());
            }
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void checkConfiguration(
            CheckConfigurationParams request, StreamObserver<CheckConfigurationResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();

        String message = "";
        boolean isValid = true;
        try {
            checkConfiguration(companyId, request.getPricingRuleDef());
        } catch (Exception e) {
            message = e.getMessage();
            isValid = false;
        }

        responseObserver.onNext(CheckConfigurationResult.newBuilder()
                .setIsValid(isValid)
                .setErrorMessage(message)
                .build());
        responseObserver.onCompleted();
    }

    /**
     * Checks pricing rule configuration for conflicts with existing rules
     */
    private void checkConfiguration(Long companyId, PricingRuleUpsertDef def) {
        var pricingRules = pricingRuleService
                .listPricingRules(ListPricingRulesRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setFilter(ListPricingRuleFilter.newBuilder()
                                .addRuleTypes(def.getType())
                                .setIsActive(true)
                                .build())
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1000)
                                .build())
                        .build())
                .getPricingRulesList();

        if (pricingRules.isEmpty()) {
            return;
        }

        // check duplicate name
        checkDuplicateRuleName(def, pricingRules);

        // check duplicate services
        checkDuplicateServices(def, pricingRules);
    }

    private static void checkDuplicateServices(final PricingRuleUpsertDef def, final List<PricingRule> pricingRules) {
        if (!Objects.equals(RuleType.ZONE, def.getType())) {
            return;
        }

        var allGroomingApplicable = def.getAllGroomingApplicable();
        var selectedGroomingServicesList = def.getSelectedGroomingServicesList();
        var allAddonApplicable = def.getAllAddonApplicable();
        var selectedAddonServicesList = def.getSelectedAddonServicesList();
        pricingRules.stream()
                .filter(rule -> def.hasId() && !Objects.equals(rule.getId(), def.getId()))
                .filter(rule -> {
                    if (allGroomingApplicable
                            && (rule.getAllGroomingApplicable()
                                    || !CollectionUtils.isEmpty(rule.getSelectedGroomingServicesList()))) {
                        return true;
                    }
                    if (!allGroomingApplicable
                            && !CollectionUtils.isEmpty(selectedGroomingServicesList)
                            && (rule.getAllGroomingApplicable()
                                    || rule.getSelectedGroomingServicesList().stream()
                                            .anyMatch(selectedGroomingServicesList::contains))) {
                        return true;
                    }
                    if (allAddonApplicable
                            && (rule.getAllAddonApplicable()
                                    || !CollectionUtils.isEmpty(rule.getSelectedAddonServicesList()))) {
                        return true;
                    }
                    if (!allAddonApplicable
                            && !CollectionUtils.isEmpty(selectedAddonServicesList)
                            && (rule.getAllAddonApplicable()
                                    || rule.getSelectedAddonServicesList().stream()
                                            .anyMatch(selectedAddonServicesList::contains))) {
                        return true;
                    }
                    return false;
                })
                .findAny()
                .ifPresent(rule -> {
                    throw ExceptionUtil.bizException(
                            Code.CODE_PARAMS_ERROR,
                            "This rule cannot be activated because some or all of the services already have active rules.");
                });
    }

    private static void checkDuplicateRuleName(final PricingRuleUpsertDef def, final List<PricingRule> pricingRules) {
        var duplicateNames = pricingRules.stream()
                .filter(rule -> !Objects.equals(rule.getId(), def.getId())
                        && Objects.equals(rule.getRuleName(), def.getRuleName()))
                .count();
        if (duplicateNames > 0) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "Pricing rule name already exists: " + def.getRuleName());
        }
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getDiscountSetting(
            final GetDiscountSettingParams request, final StreamObserver<GetDiscountSettingResult> responseObserver) {
        var response = pricingRuleService.getDiscountSetting(GetDiscountSettingRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .build());

        responseObserver.onNext(GetDiscountSettingResult.newBuilder()
                .setSetting(response.getSetting())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateDiscountSetting(
            final UpdateDiscountSettingParams request,
            final StreamObserver<UpdateDiscountSettingResult> responseObserver) {
        var response = pricingRuleService.updateDiscountSetting(UpdateDiscountSettingRequest.newBuilder()
                .setSetting(request.getSetting())
                .setCompanyId(AuthContext.get().companyId())
                .setStaffId(AuthContext.get().staffId())
                .build());

        responseObserver.onNext(UpdateDiscountSettingResult.newBuilder()
                .setSetting(response.getSetting())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getPricingRuleOverview(
            final GetPricingRuleOverviewParams request,
            final StreamObserver<GetPricingRuleOverviewResult> responseObserver) {
        var companyId = AuthContext.get().companyId();

        var ruleTypeMap = pricingRuleService
                .listPricingRules(ListPricingRulesRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setFilter(ListPricingRuleFilter.newBuilder()
                                .setIsActive(true)
                                .build())
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1000)
                                .build())
                        .build())
                .getPricingRulesList()
                .stream()
                .collect(Collectors.groupingBy(PricingRule::getType, Collectors.counting()));

        var chargeTypeMap = serviceChargeCompanyClient
                .getCompanyServiceChargeList(GetCompanyServiceChargeListRequest.newBuilder()
                        .setIsActive(true)
                        .setCompanyId(companyId)
                        .build())
                .getServiceChargeList()
                .stream()
                .collect(Collectors.groupingBy(ServiceCharge::getSurchargeType, Collectors.counting()));

        responseObserver.onNext(GetPricingRuleOverviewResult.newBuilder()
                .setActiveMultiPets(ruleTypeMap.getOrDefault(RuleType.MULTIPLE_PET, 0L))
                .setActiveMultiStays(ruleTypeMap.getOrDefault(RuleType.MULTIPLE_STAY, 0L))
                .setActivePeakDate(ruleTypeMap.getOrDefault(RuleType.PEAK_DATE, 0L))
                .setActiveZone(ruleTypeMap.getOrDefault(RuleType.ZONE, 0L))
                .setActiveOffHoursFee(chargeTypeMap.getOrDefault(SurchargeType.OFF_HOURS_FEE, 0L))
                .setActiveCustomFees(chargeTypeMap.getOrDefault(SurchargeType.CUSTOM_FEE, 0L))
                .setActive24HoursCharge(chargeTypeMap.getOrDefault(SurchargeType.CHARGE_24_HOUR, 0L))
                .setActiveFeedingCharge(chargeTypeMap.getOrDefault(SurchargeType.FEEDING_FEE, 0L))
                .setActiveMedicationCharge(chargeTypeMap.getOrDefault(SurchargeType.MEDICATION_FEE, 0L))
                .build());
        responseObserver.onCompleted();
    }
}
