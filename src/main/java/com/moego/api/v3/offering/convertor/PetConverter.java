package com.moego.api.v3.offering.convertor;

import com.moego.api.v3.online_booking.converter.BaseMapper;
import com.moego.idl.api.offering.v1.GroupClassPetView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.server.customer.dto.SearchPetResult;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE,
        uses = {BaseMapper.class})
public interface PetConverter {

    PetConverter INSTANCE = Mappers.getMapper(PetConverter.class);

    @Mapping(target = "clientId", source = "customerId")
    GroupClassPetView toView(BusinessCustomerPetInfoModel model);

    @Mapping(target = "id", source = "petId")
    @Mapping(target = "petType", source = "petTypeId")
    @Mapping(target = "breed", source = "petBreed")
    @Mapping(target = "birthday", source = "petBirthday", qualifiedByName = "stringToDate")
    @Mapping(target = "avatarPath", source = "petAvatarPath")
    GroupClassPetView toView(SearchPetResult.PetWithCustomerDTO dto);

    default PetType toPetType(int petTypeId) {
        return PetType.forNumber(petTypeId);
    }
}
