package com.moego.api.v3.offering.service;

import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.service.offering.v1.EvaluationServiceGrpc;
import com.moego.idl.service.offering.v1.GetEvaluationListWithEvaluationIdsRequest;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class EvaluationService {
    private final EvaluationServiceGrpc.EvaluationServiceBlockingStub evaluationClient;

    public Map<Long, EvaluationBriefView> getEvaluationMapByIds(List<Long> evaluationIds) {
        evaluationIds = evaluationIds.stream().filter(k -> k > 0).distinct().toList();
        if (CollectionUtils.isEmpty(evaluationIds)) {
            return Map.of();
        }
        return evaluationClient
                .getEvaluationListWithEvaluationIds(GetEvaluationListWithEvaluationIdsRequest.newBuilder()
                        .addAllEvaluationIds(evaluationIds)
                        .build())
                .getEvaluationsList()
                .stream()
                .collect(Collectors.toMap(EvaluationBriefView::getId, Function.identity(), (a, b) -> a));
    }

    public EvaluationBriefView getEvaluationById(Long id) {
        return getEvaluationMapByIds(List.of(id)).get(id);
    }
}
