package com.moego.api.v3.offering.controller;

import com.moego.idl.api.offering.v1.CreatePlaygroupParams;
import com.moego.idl.api.offering.v1.CreatePlaygroupResult;
import com.moego.idl.api.offering.v1.DeletePlaygroupParams;
import com.moego.idl.api.offering.v1.DeletePlaygroupResult;
import com.moego.idl.api.offering.v1.ListPlaygroupParams;
import com.moego.idl.api.offering.v1.ListPlaygroupResult;
import com.moego.idl.api.offering.v1.PlaygroupServiceGrpc;
import com.moego.idl.api.offering.v1.SortPlaygroupParams;
import com.moego.idl.api.offering.v1.SortPlaygroupResult;
import com.moego.idl.api.offering.v1.UpdatePlaygroupParams;
import com.moego.idl.api.offering.v1.UpdatePlaygroupResult;
import com.moego.idl.service.offering.v1.CreatePlaygroupRequest;
import com.moego.idl.service.offering.v1.CreatePlaygroupResponse;
import com.moego.idl.service.offering.v1.DeletePlaygroupRequest;
import com.moego.idl.service.offering.v1.ListPlaygroupRequest;
import com.moego.idl.service.offering.v1.ListPlaygroupResponse;
import com.moego.idl.service.offering.v1.PlaygroupServiceGrpc.PlaygroupServiceBlockingStub;
import com.moego.idl.service.offering.v1.SortPlaygroupRequest;
import com.moego.idl.service.offering.v1.UpdatePlaygroupRequest;
import com.moego.idl.service.offering.v1.UpdatePlaygroupResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class PlaygroupServer extends PlaygroupServiceGrpc.PlaygroupServiceImplBase {

    private final PlaygroupServiceBlockingStub playgroupServiceStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void listPlaygroup(ListPlaygroupParams request, StreamObserver<ListPlaygroupResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();

        ListPlaygroupResponse listPlaygroupResponse =
                playgroupServiceStub.listPlaygroup(ListPlaygroupRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setPagination(request.getPagination())
                        .build());

        responseObserver.onNext(ListPlaygroupResult.newBuilder()
                .addAllPlaygroups(listPlaygroupResponse.getPlaygroupsList())
                .setPagination(listPlaygroupResponse.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void createPlaygroup(CreatePlaygroupParams request, StreamObserver<CreatePlaygroupResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();

        CreatePlaygroupResponse createPlaygroupResponse =
                playgroupServiceStub.createPlaygroup(CreatePlaygroupRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setStaffId(staffId)
                        .setPlaygroup(request.getPlaygroup())
                        .build());

        responseObserver.onNext(CreatePlaygroupResult.newBuilder()
                .setPlaygroup(createPlaygroupResponse.getPlaygroup())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updatePlaygroup(UpdatePlaygroupParams request, StreamObserver<UpdatePlaygroupResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();

        UpdatePlaygroupResponse updatePlaygroupResponse =
                playgroupServiceStub.updatePlaygroup(UpdatePlaygroupRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setStaffId(staffId)
                        .setPlaygroup(request.getPlaygroup())
                        .build());

        responseObserver.onNext(UpdatePlaygroupResult.newBuilder()
                .setPlaygroup(updatePlaygroupResponse.getPlaygroup())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deletePlaygroup(DeletePlaygroupParams request, StreamObserver<DeletePlaygroupResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();

        playgroupServiceStub.deletePlaygroup(DeletePlaygroupRequest.newBuilder()
                .setCompanyId(companyId)
                .setStaffId(staffId)
                .setId(request.getId())
                .build());

        responseObserver.onNext(DeletePlaygroupResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void sortPlaygroup(SortPlaygroupParams request, StreamObserver<SortPlaygroupResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();

        playgroupServiceStub.sortPlaygroup(SortPlaygroupRequest.newBuilder()
                .setCompanyId(companyId)
                .setStaffId(staffId)
                .addAllIds(request.getIdsList())
                .build());

        responseObserver.onNext(SortPlaygroupResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
