package com.moego.api.v3.offering.service;

import static com.moego.lib.common.thread.ThreadPool.getSubmitExecutor;
import static com.moego.lib.common.thread.ThreadPool.supplyAsync;

import com.google.protobuf.Duration;
import com.google.type.DateTime;
import com.moego.api.v3.business_customer.service.BusinessCustomerService;
import com.moego.api.v3.business_customer.service.BusinessPetCodeService;
import com.moego.api.v3.business_customer.service.BusinessPetIncidentService;
import com.moego.api.v3.business_customer.service.BusinessPetNoteService;
import com.moego.api.v3.business_customer.service.BusinessPetService;
import com.moego.api.v3.business_customer.service.BusinessPetVaccineService;
import com.moego.api.v3.fulfillment.consts.FulfillmentStatusConst;
import com.moego.api.v3.offering.convertor.CustomerConverter;
import com.moego.api.v3.offering.convertor.GroupClassInstanceConverter;
import com.moego.api.v3.offering.convertor.PetConverter;
import com.moego.api.v3.organization.service.StaffService;
import com.moego.api.v3.shared.helper.OrderHelper;
import com.moego.idl.api.offering.v1.CountInstancesGroupByClassParams;
import com.moego.idl.api.offering.v1.CountInstancesGroupByClassResult;
import com.moego.idl.api.offering.v1.CountInstancesGroupByStatusParams;
import com.moego.idl.api.offering.v1.CountInstancesGroupByStatusResult;
import com.moego.idl.api.offering.v1.CreateInstanceParams;
import com.moego.idl.api.offering.v1.CreateInstanceResult;
import com.moego.idl.api.offering.v1.DeleteInstanceParams;
import com.moego.idl.api.offering.v1.DeleteInstanceResult;
import com.moego.idl.api.offering.v1.GetInstanceParams;
import com.moego.idl.api.offering.v1.GetInstanceResult;
import com.moego.idl.api.offering.v1.GroupClassClientView;
import com.moego.idl.api.offering.v1.GroupClassInstanceView;
import com.moego.idl.api.offering.v1.GroupClassPetView;
import com.moego.idl.api.offering.v1.GroupClassTrainerView;
import com.moego.idl.api.offering.v1.ListInstancesParams;
import com.moego.idl.api.offering.v1.ListInstancesResult;
import com.moego.idl.api.offering.v1.ListSessionsParams;
import com.moego.idl.api.offering.v1.ListSessionsResult;
import com.moego.idl.api.offering.v1.UpdateInstanceParams;
import com.moego.idl.api.offering.v1.UpdateInstanceResult;
import com.moego.idl.api.offering.v1.UpdateSessionParams;
import com.moego.idl.api.offering.v1.UpdateSessionResult;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeBindingModel;
import com.moego.idl.models.business_customer.v1.BusinessPetCodeModel;
import com.moego.idl.models.business_customer.v1.BusinessPetIncidentReportModel;
import com.moego.idl.models.business_customer.v1.BusinessPetNoteModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRecordBindingModel;
import com.moego.idl.models.fulfillment.v1.FulfillmentModel;
import com.moego.idl.models.fulfillment.v1.GroupClassAttendanceModel;
import com.moego.idl.models.fulfillment.v1.GroupClassDetailModel;
import com.moego.idl.models.offering.v1.GroupClassInstance;
import com.moego.idl.models.offering.v1.GroupClassModel;
import com.moego.idl.models.offering.v1.GroupClassSession;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.order.v1.OrderModel;
import com.moego.idl.models.order.v1.OrderSourceType;
import com.moego.idl.models.order.v1.OrderStatus;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.service.fulfillment.v1.FulfillmentServiceGrpc;
import com.moego.idl.service.fulfillment.v1.GroupClassAttendanceServiceGrpc;
import com.moego.idl.service.fulfillment.v1.GroupClassDetailServiceGrpc;
import com.moego.idl.service.fulfillment.v1.ListAttendancesRequest;
import com.moego.idl.service.fulfillment.v1.ListFulfillmentsRequest;
import com.moego.idl.service.fulfillment.v1.ListGroupClassDetailsRequest;
import com.moego.idl.service.offering.v1.CountInstancesGroupByClassRequest;
import com.moego.idl.service.offering.v1.CountInstancesGroupByClassResponse;
import com.moego.idl.service.offering.v1.CountInstancesGroupByStatusRequest;
import com.moego.idl.service.offering.v1.CreateInstanceAndSessionsRequest;
import com.moego.idl.service.offering.v1.DeleteInstanceAndSessionsRequest;
import com.moego.idl.service.offering.v1.GetInstanceRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.GroupClassServiceGrpc;
import com.moego.idl.service.offering.v1.ListInstancesRequest;
import com.moego.idl.service.offering.v1.ListInstancesResponse;
import com.moego.idl.service.offering.v1.ListSessionsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.offering.v1.UpdateInstanceAndSessionsRequest;
import com.moego.idl.service.offering.v1.UpdateSessionRequest;
import com.moego.lib.utils.model.Pair;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Service
@Slf4j
@RequiredArgsConstructor
public class GroupClassService {
    private final GroupClassServiceGrpc.GroupClassServiceBlockingStub groupClassServiceBlockingStub;
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub
            serviceManagementServiceBlockingStub;
    private final FulfillmentServiceGrpc.FulfillmentServiceBlockingStub fulfillmentStub;
    private final GroupClassDetailServiceGrpc.GroupClassDetailServiceBlockingStub groupClassDetailStub;
    private final GroupClassAttendanceServiceGrpc.GroupClassAttendanceServiceBlockingStub groupClassAttendanceStub;
    private final StaffService staffService;
    private final BusinessPetService businessPetService;
    private final BusinessPetCodeService petCodeService;
    private final BusinessPetVaccineService petVaccineService;
    private final BusinessPetNoteService petNoteService;
    private final BusinessPetIncidentService petIncidentService;
    private final BusinessCustomerService businessCustomerService;
    private final OrderHelper orderHelper;

    public CreateInstanceResult createGroupClassInstance(Long companyID, CreateInstanceParams params) {
        var request = CreateInstanceAndSessionsRequest.newBuilder()
                .setCompanyId(companyID)
                .setBusinessId(params.getBusinessId())
                .setGroupClassId(params.getTrainingGroupClassId())
                .setStaffId(params.getStaffId())
                .setStartTime(DateTime.newBuilder()
                        .setYear(params.getStartDate().getYear())
                        .setMonth(params.getStartDate().getMonth())
                        .setDay(params.getStartDate().getDay())
                        .setHours((int) (params.getStartTimeOfDayMinutes() / 60))
                        .setMinutes((int) (params.getStartTimeOfDayMinutes() % 60))
                        .setTimeZone(params.getTimeZone()))
                .setOccurrence(params.getOccurrence())
                .build();
        var response = groupClassServiceBlockingStub.createInstanceAndSessions(request);
        var instance = response.getInstance();
        var instanceViews = enrichInstance(companyID, instance.getBusinessId(), List.of(instance));

        return CreateInstanceResult.newBuilder()
                .setGroupClassInstance(
                        CollectionUtils.isEmpty(instanceViews)
                                ? GroupClassInstanceView.getDefaultInstance()
                                : instanceViews.get(0))
                .build();
    }

    public CountInstancesGroupByStatusResult countGroupClassInstanceStatus(
            Long companyID, CountInstancesGroupByStatusParams params) {
        var request = CountInstancesGroupByStatusRequest.newBuilder()
                .setCompanyId(companyID)
                .setBusinessId(params.getBusinessId())
                .addAllStaffIds(params.getStaffIdsList())
                .build();
        var response = groupClassServiceBlockingStub.countInstancesGroupByStatus(request);

        return CountInstancesGroupByStatusResult.newBuilder()
                .addAllCounts(response.getCountsList().stream()
                        .map(count -> CountInstancesGroupByStatusResult.Count.newBuilder()
                                .setCount(count.getCount())
                                .setStatus(count.getStatus())
                                .build())
                        .toList())
                .build();
    }

    public CountInstancesGroupByClassResult countGroupClassInstancesByStatus(
            Long companyID, CountInstancesGroupByClassParams params) {
        var request = CountInstancesGroupByClassRequest.newBuilder()
                .setCompanyId(companyID)
                .setBusinessId(params.getBusinessId())
                .addAllStaffIds(params.getStaffIdsList())
                .setStatus(params.getStatus())
                .build();
        var response = groupClassServiceBlockingStub.countInstancesGroupByClass(request);

        var setting = serviceManagementServiceBlockingStub
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .setCompanyId(companyID)
                        .addAllServiceIds(response.getCountsList().stream()
                                .map(CountInstancesGroupByClassResponse.Count::getGroupClassId)
                                .toList())
                        .build())
                .getServicesList()
                .stream()
                .collect(Collectors.toMap(ServiceBriefView::getId, service -> service, (s1, s2) -> s1));

        return CountInstancesGroupByClassResult.newBuilder()
                .addAllCounts(response.getCountsList().stream()
                        .map(count -> CountInstancesGroupByClassResult.Count.newBuilder()
                                .setCount(count.getCount())
                                .setGroupClass(GroupClassModel.newBuilder()
                                        .setId(count.getGroupClassId())
                                        .setName(setting.get(count.getGroupClassId())
                                                .getName())
                                        .setDescription(setting.get(count.getGroupClassId())
                                                .getDescription())
                                        .setPrice(setting.get(count.getGroupClassId())
                                                .getPrice())
                                        .setNumSessions(setting.get(count.getGroupClassId())
                                                .getNumSessions())
                                        .setDurationSessionMin(setting.get(count.getGroupClassId())
                                                .getDurationSessionMin())
                                        .build())
                                .build())
                        .toList())
                .build();
    }

    public GetInstanceResult getInstance(Long companyId, GetInstanceParams params) {
        var instance = groupClassServiceBlockingStub
                .getInstance(
                        GetInstanceRequest.newBuilder().setId(params.getId()).build())
                .getGroupClassInstance();
        var instanceViews = enrichInstance(companyId, instance.getBusinessId(), List.of(instance));

        return GetInstanceResult.newBuilder()
                .setGroupClassInstance(
                        CollectionUtils.isEmpty(instanceViews)
                                ? GroupClassInstanceView.getDefaultInstance()
                                : instanceViews.get(0))
                .build();
    }

    private ListInstancesResponse getGroupClassInstances(Long companyId, ListInstancesParams params) {
        var request = ListInstancesRequest.newBuilder()
                .setPagination(params.getPagination())
                .setCompanyId(companyId)
                .addBusinessIds(params.getBusinessId())
                .addGroupClassIds(params.getGroupClassId())
                .addAllStaffIds(params.getStaffIdsList())
                .setStatus(params.getStatus())
                .build();
        return groupClassServiceBlockingStub.listInstances(request);
    }

    private List<GroupClassSession> getGroupClassInstanceToSessions(long companyId, List<Long> groupClassInstanceIds) {
        return groupClassServiceBlockingStub
                .listSessions(ListSessionsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllGroupClassInstanceId(groupClassInstanceIds)
                        .build())
                .getSessionsList();
    }

    private Map<Long, StaffModel> getStaffIdToInfo(List<GroupClassInstance> groupClassInstances) {
        return staffService.getStaffMap(
                groupClassInstances.stream().map(GroupClassInstance::getStaffId).toList());
    }

    public ListInstancesResult listGroupClassInstances(Long companyId, ListInstancesParams params) {
        var response = getGroupClassInstances(companyId, params);
        var instances = response.getGroupClassInstancesList();
        var instanceViews = enrichInstance(companyId, params.getBusinessId(), instances);

        return ListInstancesResult.newBuilder()
                .setPagination(response.getPagination())
                .addAllGroupClassInstances(instanceViews)
                .build();
    }

    private List<GroupClassInstanceView> enrichInstance(
            long companyId, long businessId, List<GroupClassInstance> instances) {
        if (CollectionUtils.isEmpty(instances)) {
            return List.of();
        }

        var instanceIds = instances.stream().map(GroupClassInstance::getId).toList();
        var sessions = getGroupClassInstanceToSessions(companyId, instanceIds);

        // enrolled pet
        var groupClassDetails = getValidGroupClassDetails(companyId, businessId, instanceIds);

        var petIds = groupClassDetails.stream()
                .map(GroupClassDetailModel::getPetId)
                .distinct()
                .toList();

        var petInfoFuture = supplyAsync(() -> businessPetService.listPetsInfo(petIds));
        var petCodesFuture = supplyAsync(() -> petCodeService.listPetCodes(companyId, petIds));
        var petVaccinesFuture = supplyAsync(() -> petVaccineService.listPetVaccines(companyId, petIds));
        var petNotesFuture = supplyAsync(() -> petNoteService.listPetNotes(petIds));
        var petIncidentFuture = supplyAsync(() -> petIncidentService.listPetIncidents(companyId, petIds));
        var customerFuture = petInfoFuture.thenApplyAsync(
                petIdToInfo -> {
                    var customerIds = petIdToInfo.values().stream()
                            .map(BusinessCustomerPetInfoModel::getCustomerId)
                            .distinct()
                            .toList();
                    return businessCustomerService.listBusinessCustomerInfos(customerIds);
                },
                getSubmitExecutor());
        var orderFuture = supplyAsync(() ->
                orderHelper.getOrdersBySources(OrderSourceType.FULFILLMENT, extractFulfillmentIds(groupClassDetails)));

        CompletableFuture.allOf(
                        petInfoFuture,
                        petCodesFuture,
                        petVaccinesFuture,
                        petNotesFuture,
                        petIncidentFuture,
                        customerFuture,
                        orderFuture)
                .join();

        var petIdToInfo = petInfoFuture.join();
        var petIdToCodes = petCodesFuture.join();
        var petIdToVaccines = petVaccinesFuture.join();
        var petIdToNotes = petNotesFuture.join();
        var petIdToIncidents = petIncidentFuture.join();
        var customers = customerFuture.join();
        var orders = orderFuture.join();

        // checked in pet
        var sessionIds = sessions.stream().map(GroupClassSession::getId).toList();
        var sessionIdToPetIds = getSessionCheckedInPets(sessionIds);

        var instanceIdToSessions =
                sessions.stream().collect(Collectors.groupingBy(GroupClassSession::getGroupClassInstanceId));
        var instanceIdToGroupClassDetails = groupClassDetails.stream()
                .collect(Collectors.groupingBy(GroupClassDetailModel::getGroupClassInstanceId));
        var staffsMap = getStaffIdToInfo(instances);

        return instances.stream()
                .map(instance -> {
                    var view = GroupClassInstanceConverter.toGroupClassInstanceView(
                            instance, instanceIdToSessions.getOrDefault(instance.getId(), List.of()));
                    var staff = buildGroupClassTrainerView(instance.getStaffId(), staffsMap);
                    var enrolledPets = buildGroupClassPetViews(
                            instanceIdToGroupClassDetails,
                            instance.getId(),
                            petIdToInfo,
                            petIdToCodes,
                            petIdToVaccines,
                            petIdToNotes,
                            petIdToIncidents,
                            orders);
                    var clients = buildGroupClassClientViews(enrolledPets, customers);
                    var sessionsWithPets = view.getSessionsList().stream()
                            .map(session -> session.toBuilder()
                                    .addAllCheckedInPets(sessionIdToPetIds.getOrDefault(session.getId(), List.of()))
                                    .build())
                            .toList();
                    return view.toBuilder()
                            .setTrainer(staff)
                            .addAllEnrolledPets(enrolledPets)
                            .addAllClients(clients)
                            .clearSessions()
                            .addAllSessions(sessionsWithPets)
                            .build();
                })
                .toList();
    }

    private static List<Long> extractFulfillmentIds(List<GroupClassDetailModel> groupClassDetails) {
        return groupClassDetails.stream()
                .map(GroupClassDetailModel::getFulfillmentId)
                .distinct()
                .toList();
    }

    private static GroupClassTrainerView buildGroupClassTrainerView(
            long staffId, Map<Long, StaffModel> staffIdToModel) {
        var staff = staffIdToModel.getOrDefault(staffId, StaffModel.getDefaultInstance());
        return GroupClassTrainerView.newBuilder()
                .setId(staffId)
                .setFirstName(staff.getFirstName())
                .setLastName(staff.getLastName())
                .setAvatarPath(staff.getAvatarPath())
                .build();
    }

    static List<GroupClassPetView> buildGroupClassPetViews(
            Map<Long, List<GroupClassDetailModel>> instanceIdToGroupClassDetails,
            long instanceId,
            Map<Long, BusinessCustomerPetInfoModel> petIdToInfo,
            Pair<List<BusinessPetCodeBindingModel>, List<BusinessPetCodeModel>> petCodes,
            Pair<List<BusinessPetVaccineRecordBindingModel>, List<BusinessPetVaccineModel>> petVaccines,
            Map<Long, List<BusinessPetNoteModel>> petIdToNotes,
            Map<Long, List<BusinessPetIncidentReportModel>> petIdToIncidents,
            Map<Long, List<OrderDetailModel>> fulfillmentIdToOrders) {
        var petIdToCodeBindings = petCodes.key().stream()
                .collect(Collectors.groupingBy(BusinessPetCodeBindingModel::getPetId, Collectors.toList()));
        var petIdToVaccineRecords = petVaccines.key().stream()
                .collect(Collectors.toMap(
                        BusinessPetVaccineRecordBindingModel::getPetId,
                        BusinessPetVaccineRecordBindingModel::getRecordsList));
        var codeIdToCode = petCodes.value().stream()
                .collect(Collectors.toMap(BusinessPetCodeModel::getId, code -> code, (c1, c2) -> c1));
        var vaccineIdToVaccine = petVaccines.value().stream()
                .collect(Collectors.toMap(BusinessPetVaccineModel::getId, vaccine -> vaccine, (v1, v2) -> v1));

        return instanceIdToGroupClassDetails.getOrDefault(instanceId, List.of()).stream()
                .map(groupClassDetail -> {
                    var petId = groupClassDetail.getPetId();
                    // 获取宠物基本信息
                    var petViewBuilder = PetConverter.INSTANCE
                            .toView(petIdToInfo.getOrDefault(petId, BusinessCustomerPetInfoModel.getDefaultInstance()))
                            .toBuilder();

                    // 当前 pet 所有的 code
                    var petCodeBindings = petIdToCodeBindings.getOrDefault(petId, List.of());
                    var codes = petCodeBindings.stream()
                            .flatMap(binding -> binding.getPetCodeIdsList().stream())
                            .map(codeIdToCode::get)
                            .filter(Objects::nonNull)
                            .toList();
                    // 当前 pet 所有的 vaccine
                    var vaccineRecords = petIdToVaccineRecords.getOrDefault(petId, List.of());
                    var vaccines = vaccineRecords.stream()
                            .map(record -> vaccineIdToVaccine.get(record.getVaccineId()))
                            .filter(Objects::nonNull)
                            .toList();

                    var orders = fulfillmentIdToOrders.get(groupClassDetail.getFulfillmentId());
                    if (!ObjectUtils.isEmpty(orders)) {
                        var order = orders.get(0).getOrder(); // group class 只会有一个 order
                        petViewBuilder.setOrderId(order.getId());
                        petViewBuilder.setPaymentStatus(getPaymentStatus(order));
                    }

                    return petViewBuilder
                            .addAllCodes(codes)
                            .addAllNotes(petIdToNotes.getOrDefault(petId, List.of()))
                            .addAllVaccineRecords(vaccineRecords)
                            .addAllVaccines(vaccines)
                            .addAllIncidentReports(petIdToIncidents.getOrDefault(petId, List.of()))
                            .build();
                })
                .toList();
    }

    private static GroupClassPetView.PaymentStatus getPaymentStatus(OrderModel order) {
        var status = OrderStatus.forNumber(order.getStatus());
        if (status == null) {
            return GroupClassPetView.PaymentStatus.UNPAID;
        }
        return switch (status) {
            case CREATED, REMOVED, UNRECOGNIZED -> GroupClassPetView.PaymentStatus.UNPAID;
            case PROCESSING -> GroupClassPetView.PaymentStatus.PROCESSING;
            case COMPLETED -> GroupClassPetView.PaymentStatus.PAID;
        };
    }

    private List<GroupClassClientView> buildGroupClassClientViews(
            List<GroupClassPetView> enrolledPets, List<BusinessCustomerInfoModel> customers) {
        var enrolledClientIds =
                enrolledPets.stream().map(GroupClassPetView::getClientId).collect(Collectors.toSet());
        return customers.stream()
                .filter(customer -> enrolledClientIds.contains(customer.getId()))
                .map(CustomerConverter.INSTANCE::toView)
                .toList();
    }

    public List<GroupClassDetailModel> getValidGroupClassDetails(
            long companyId, long businessId, List<Long> groupClassInstanceIds) {
        var groupClassDetails = groupClassDetailStub
                .listGroupClassDetails(ListGroupClassDetailsRequest.newBuilder()
                        .setFilter(ListGroupClassDetailsRequest.Filter.newBuilder()
                                .addAllGroupClassInstanceIds(groupClassInstanceIds))
                        .build())
                .getGroupClassDetailsList();
        var activeFulfillmentIds = fulfillmentStub
                .listFulfillments(ListFulfillmentsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addBusinessIds(businessId)
                        .setFilter(ListFulfillmentsRequest.Filter.newBuilder()
                                .addAllFulfillmentIds(extractFulfillmentIds(groupClassDetails))
                                .addAllStatuses(FulfillmentStatusConst.PRE_PAYMENT_MODE_ACTIVE_STATUSES)
                                .build())
                        .build())
                .getFulfillmentsList()
                .stream()
                .map(FulfillmentModel::getId)
                .collect(Collectors.toSet());
        return groupClassDetails.stream()
                .filter(detail -> activeFulfillmentIds.contains(detail.getFulfillmentId()))
                .toList();
    }

    public Map<Long, List<Long>> getSessionCheckedInPets(List<Long> groupClassSessionIds) {
        var attendances = groupClassAttendanceStub
                .listAttendances(ListAttendancesRequest.newBuilder()
                        .addAllGroupClassSessionId(groupClassSessionIds)
                        .build())
                .getAttendancesList();
        return attendances.stream()
                .collect(Collectors.groupingBy(
                        GroupClassAttendanceModel::getGroupClassSessionId,
                        Collectors.mapping(GroupClassAttendanceModel::getPetId, Collectors.toList())));
    }

    public UpdateInstanceResult updateGroupClassInstance(Long companyID, UpdateInstanceParams params) {
        var request = UpdateInstanceAndSessionsRequest.newBuilder()
                .setId(params.getId())
                .setStaffId(params.getStaffId())
                .setStartTime(DateTime.newBuilder()
                        .setYear(params.getStartDate().getYear())
                        .setMonth(params.getStartDate().getMonth())
                        .setDay(params.getStartDate().getDay())
                        .setHours((int) (params.getStartTimeOfDayMinutes() / 60))
                        .setMinutes((int) (params.getStartTimeOfDayMinutes() % 60))
                        .setTimeZone(params.getTimeZone()))
                .setOccurrence(params.getOccurrence())
                .build();
        var response = groupClassServiceBlockingStub.updateInstanceAndSessions(request);
        return UpdateInstanceResult.newBuilder()
                .setInstance(GroupClassInstanceConverter.toGroupClassInstanceView(
                        response.getInstance(), response.getSessionsList()))
                .build();
    }

    public DeleteInstanceResult deleteGroupClassInstance(Long companyID, DeleteInstanceParams params) {
        groupClassServiceBlockingStub.deleteInstanceAndSessions(DeleteInstanceAndSessionsRequest.newBuilder()
                .setId(params.getId())
                .build());

        return DeleteInstanceResult.newBuilder().build();
    }

    public UpdateSessionResult updateGroupClassSessions(Long companyID, UpdateSessionParams params) {
        var response = groupClassServiceBlockingStub.updateSession(UpdateSessionRequest.newBuilder()
                .setId(params.getId())
                .setStartTime(params.getStartTime())
                .setDuration(Duration.newBuilder().setSeconds(params.getDurationSessionMinutes() * 60L))
                .build());
        return UpdateSessionResult.newBuilder()
                .setSession(GroupClassInstanceConverter.toGroupClassSessionView(response.getSession()))
                .build();
    }

    public ListSessionsResult listGroupClassSessions(Long companyID, ListSessionsParams params) {
        var response = groupClassServiceBlockingStub.listSessions(ListSessionsRequest.newBuilder()
                .setCompanyId(companyID)
                .addGroupClassInstanceId(params.getGroupClassInstanceId())
                .build());
        return ListSessionsResult.newBuilder()
                .addAllSessions(response.getSessionsList().stream()
                        .map(GroupClassInstanceConverter::toGroupClassSessionView)
                        .toList())
                .build();
    }
}
