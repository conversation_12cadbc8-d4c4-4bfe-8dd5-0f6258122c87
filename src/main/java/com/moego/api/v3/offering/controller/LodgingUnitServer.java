package com.moego.api.v3.offering.controller;

import com.moego.api.v3.appointment.service.LodgingUsingService;
import com.moego.api.v3.offering.convertor.LodgingUnitConvertor;
import com.moego.api.v3.offering.service.LodgingService;
import com.moego.idl.api.offering.v1.CreateLodgingUnitParams;
import com.moego.idl.api.offering.v1.CreateLodgingUnitResult;
import com.moego.idl.api.offering.v1.DeleteLodgingUnitParams;
import com.moego.idl.api.offering.v1.DeleteLodgingUnitResult;
import com.moego.idl.api.offering.v1.GetLodgingUnitListParams;
import com.moego.idl.api.offering.v1.GetLodgingUnitListResult;
import com.moego.idl.api.offering.v1.LodgingUnitServiceGrpc.LodgingUnitServiceImplBase;
import com.moego.idl.api.offering.v1.SortLodgingUnitByIdsParams;
import com.moego.idl.api.offering.v1.SortLodgingUnitByIdsResult;
import com.moego.idl.api.offering.v1.UpdateLodgingUnitParams;
import com.moego.idl.api.offering.v1.UpdateLodgingUnitResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.models.organization.v1.VisibilityType;
import com.moego.idl.service.offering.v1.BatchCreateLodgingUnitRequest;
import com.moego.idl.service.offering.v1.BatchCreateLodgingUnitResponse;
import com.moego.idl.service.offering.v1.CreateLodgingUnitRequest;
import com.moego.idl.service.offering.v1.GetLodgingUnitListRequest;
import com.moego.idl.service.offering.v1.GetLodgingUnitListResponse;
import com.moego.idl.service.offering.v1.LodgingTypeIdList;
import com.moego.idl.service.offering.v1.LodgingUnitServiceGrpc.LodgingUnitServiceBlockingStub;
import com.moego.idl.service.offering.v1.SortLodgingUnitByIdsRequest;
import com.moego.idl.service.offering.v1.UpdateLodgingUnitRequest;
import com.moego.idl.service.offering.v1.UpdateLodgingUnitResponse;
import com.moego.idl.service.organization.v1.CameraServiceGrpc;
import com.moego.idl.service.organization.v1.UpdateCameraRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.util.CollectionUtils;

@GrpcService
@AllArgsConstructor
public class LodgingUnitServer extends LodgingUnitServiceImplBase {

    private final LodgingUsingService lodgingUsingService;
    private final LodgingUnitServiceBlockingStub lodgingUnitClient;
    private final LodgingService lodgingService;
    private final CameraServiceGrpc.CameraServiceBlockingStub cameraServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_LODGING_SETTING)
    public void createLodgingUnit(
            CreateLodgingUnitParams params, StreamObserver<CreateLodgingUnitResult> responseObserver) {
        AuthContext authContext = AuthContext.get();
        Long companyId = authContext.companyId();
        Long tokenStaffId = authContext.staffId();

        List<CreateLodgingUnitRequest> creates = new ArrayList<>();
        for (String name : params.getNameList()) {
            var build = CreateLodgingUnitRequest.newBuilder();
            if (params.hasCameraId()) {
                build.setCameraId(params.getCameraId());
            }
            creates.add(build.setCompanyId(companyId)
                    .setBusinessId(params.getBusinessId())
                    .setTokenStaffId(tokenStaffId)
                    .setBusinessId(params.getBusinessId())
                    .setLodgingTypeId(params.getLodgingTypeId())
                    .setName(name)
                    .build());
        }
        if (creates.isEmpty()) {
            responseObserver.onNext(CreateLodgingUnitResult.newBuilder().build());
            responseObserver.onCompleted();
            return;
        }
        BatchCreateLodgingUnitRequest request = BatchCreateLodgingUnitRequest.newBuilder()
                .addAllLodgingUnitParamsList(creates)
                .build();
        BatchCreateLodgingUnitResponse result = lodgingUnitClient.batchCreateLodgingUnit(request);
        responseObserver.onNext(CreateLodgingUnitResult.newBuilder()
                .addAllLodgingUnitList(result.getLodgingUnitListList().stream()
                        .map(LodgingUnitConvertor.INSTANCE::toApiModel)
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_LODGING_SETTING)
    public void updateLodgingUnit(
            UpdateLodgingUnitParams params, StreamObserver<UpdateLodgingUnitResult> responseObserver) {
        if (params.hasCameraId()) {
            setCameraPrivate(AuthContext.get().companyId(), params.getCameraId());
        }
        UpdateLodgingUnitRequest request = LodgingUnitConvertor.INSTANCE.toUpdateLodgingUnitRequest(params);
        request = request.toBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setTokenStaffId(AuthContext.get().staffId())
                .build();
        UpdateLodgingUnitResponse result = lodgingUnitClient.updateLodgingUnit(request);
        responseObserver.onNext(UpdateLodgingUnitResult.newBuilder()
                .setLodgingUnit(LodgingUnitConvertor.INSTANCE.toApiModel(result.getLodgingUnit()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_LODGING_SETTING)
    public void deleteLodgingUnit(
            DeleteLodgingUnitParams params, StreamObserver<DeleteLodgingUnitResult> responseObserver) {
        long companyId = AuthContext.get().companyId();
        int upcomingAppointmentCnt =
                lodgingUsingService.getUpcomingAppointmentCnt(companyId, params.getBusinessId(), params.getId());
        if (upcomingAppointmentCnt > 0) {
            throw ExceptionUtil.bizException(Code.CODE_LODGING_UNIT_IN_USE, "lodging unit in use");
        }
        lodgingService.deleteLodgingUnits(companyId, AuthContext.get().staffId(), List.of(params.getId()));
        responseObserver.onNext(DeleteLodgingUnitResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getLodgingUnitList(
            GetLodgingUnitListParams params, StreamObserver<GetLodgingUnitListResult> responseObserver) {
        var requestBuilder = GetLodgingUnitListRequest.newBuilder();
        requestBuilder.setCompanyId(AuthContext.get().companyId());
        if (params.hasBusinessId()) {
            requestBuilder.setBusinessId(params.getBusinessId());
        }
        if (!CollectionUtils.isEmpty(params.getLodgingTypeIdListList())) {
            requestBuilder.setTypeIdList(LodgingTypeIdList.newBuilder()
                    .addAllIdList(params.getLodgingTypeIdListList())
                    .build());
        }
        GetLodgingUnitListResponse result = lodgingUnitClient.getLodgingUnitList(requestBuilder.build());
        responseObserver.onNext(GetLodgingUnitListResult.newBuilder()
                .addAllLodgingUnitList(result.getLodgingUnitListList().stream()
                        .map(LodgingUnitConvertor.INSTANCE::toApiModel)
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    private void setCameraPrivate(Long companyId, Long cameraId) {
        cameraServiceBlockingStub.updateCamera(UpdateCameraRequest.newBuilder()
                .setTenant(Tenant.newBuilder().setCompanyId(companyId).build())
                .setCameraId(cameraId)
                .setVisibilityType(VisibilityType.PRIVATE)
                .build());
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_LODGING_SETTING)
    public void sortLodgingUnitByIds(
            SortLodgingUnitByIdsParams request, StreamObserver<SortLodgingUnitByIdsResult> responseObserver) {
        lodgingUnitClient.sortLodgingUnitByIds(SortLodgingUnitByIdsRequest.newBuilder()
                .addAllIds(request.getIdsList())
                .setCompanyId(AuthContext.get().companyId())
                .setStaffId(AuthContext.get().staffId())
                .build());
        responseObserver.onNext(SortLodgingUnitByIdsResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
