package com.moego.api.v3.offering.convertor;

import com.moego.idl.api.offering.v1.GroupClassClientView;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.server.customer.dto.SearchPetResult;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2025/4/7
 */
@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        unmappedSourcePolicy = ReportingPolicy.IGNORE)
public interface CustomerConverter {

    CustomerConverter INSTANCE = Mappers.getMapper(CustomerConverter.class);

    GroupClassClientView toView(BusinessCustomerInfoModel model);

    @Mapping(target = "id", source = "customerId")
    @Mapping(target = "firstName", source = "customerFirstName")
    @Mapping(target = "lastName", source = "customerLastName")
    GroupClassClientView toView(SearchPetResult.PetWithCustomerDTO dto);
}
