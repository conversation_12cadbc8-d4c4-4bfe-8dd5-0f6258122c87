package com.moego.api.v3.offering.controller;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.idl.api.offering.v1.CreateEvaluationParams;
import com.moego.idl.api.offering.v1.CreateEvaluationResult;
import com.moego.idl.api.offering.v1.DeleteEvaluationParams;
import com.moego.idl.api.offering.v1.DeleteEvaluationResult;
import com.moego.idl.api.offering.v1.EvaluationServiceGrpc;
import com.moego.idl.api.offering.v1.GetApplicableEvaluationListParams;
import com.moego.idl.api.offering.v1.GetApplicableEvaluationListResult;
import com.moego.idl.api.offering.v1.GetBusinessListWithApplicableEvaluationParams;
import com.moego.idl.api.offering.v1.GetBusinessListWithApplicableEvaluationResult;
import com.moego.idl.api.offering.v1.GetEvaluationListParams;
import com.moego.idl.api.offering.v1.GetEvaluationListResult;
import com.moego.idl.api.offering.v1.GetEvaluationParams;
import com.moego.idl.api.offering.v1.GetEvaluationResult;
import com.moego.idl.api.offering.v1.UpdateEvaluationParams;
import com.moego.idl.api.offering.v1.UpdateEvaluationResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.EvaluationModel;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.service.appointment.v1.AppointmentCountServiceGrpc;
import com.moego.idl.service.appointment.v1.BatchGetUpcomingAppointmentCountRequest;
import com.moego.idl.service.offering.v1.CreateEvaluationRequest;
import com.moego.idl.service.offering.v1.DeleteEvaluationRequest;
import com.moego.idl.service.offering.v1.GetApplicableEvaluationListRequest;
import com.moego.idl.service.offering.v1.GetEvaluationRequest;
import com.moego.idl.service.offering.v1.UpdateEvaluationRequest;
import com.moego.idl.service.online_booking.v1.BookingRequestServiceGrpc;
import com.moego.idl.service.online_booking.v1.CountBookingRequestByFilterRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetLocationListRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import java.util.Comparator;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@GrpcService
@RequiredArgsConstructor
@Slf4j
public class EvaluationServer extends EvaluationServiceGrpc.EvaluationServiceImplBase {
    private final com.moego.idl.service.offering.v1.EvaluationServiceGrpc.EvaluationServiceBlockingStub
            evaluationServiceBlockingStub;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;
    private final AppointmentCountServiceGrpc.AppointmentCountServiceBlockingStub appointmentCountServiceBlockingStub;
    private final BookingRequestServiceGrpc.BookingRequestServiceBlockingStub bookingRequestServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_SERVICE_SETTINGS)
    public void createEvaluation(
            CreateEvaluationParams request, StreamObserver<CreateEvaluationResult> responseObserver) {
        var resp = evaluationServiceBlockingStub.createEvaluation(CreateEvaluationRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setEvaluationDef(request.getEvaluationDef())
                .build());
        responseObserver.onNext(CreateEvaluationResult.newBuilder()
                .setEvaluationModel(resp.getEvaluationModel())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_SERVICE_SETTINGS)
    public void updateEvaluation(
            UpdateEvaluationParams request, StreamObserver<UpdateEvaluationResult> responseObserver) {
        var resp = evaluationServiceBlockingStub.updateEvaluation(UpdateEvaluationRequest.newBuilder()
                .setId(request.getId())
                .setEvaluationDef(request.getEvaluationDef())
                .setTenant(Tenant.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .build())
                .build());
        responseObserver.onNext(UpdateEvaluationResult.newBuilder()
                .setEvaluationModel(resp.getEvaluationModel())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getEvaluationList(
            GetEvaluationListParams request, StreamObserver<GetEvaluationListResult> responseObserver) {
        var resp = evaluationServiceBlockingStub.getEvaluationList(
                com.moego.idl.service.offering.v1.GetEvaluationListRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .build());
        responseObserver.onNext(GetEvaluationListResult.newBuilder()
                .addAllEvaluations(resp.getEvaluationsList().stream()
                        .sorted(Comparator.comparing(EvaluationModel::getId))
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getApplicableEvaluationList(
            GetApplicableEvaluationListParams request,
            StreamObserver<GetApplicableEvaluationListResult> responseObserver) {
        var builder = GetApplicableEvaluationListRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId());
        if (request.hasBusinessId()) {
            builder.setBusinessId(request.getBusinessId());
        }
        if (request.hasServiceItemType()) {
            builder.setServiceItemType(request.getServiceItemType());
        }
        var resp = evaluationServiceBlockingStub.getApplicableEvaluationList(builder.build());
        responseObserver.onNext(GetApplicableEvaluationListResult.newBuilder()
                .addAllEvaluations(resp.getEvaluationsList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_SERVICE_SETTINGS)
    public void deleteEvaluation(
            DeleteEvaluationParams request, StreamObserver<DeleteEvaluationResult> responseObserver) {
        var evaluationId = request.getId();
        // 1. 检查是否有 upcoming evaluation，如果有则返回无法删除
        var upcomingAppointmentResponse = appointmentCountServiceBlockingStub.batchGetUpcomingAppointmentCount(
                BatchGetUpcomingAppointmentCountRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .addEvaluationIds(evaluationId)
                        .build());

        if (upcomingAppointmentResponse.getEvaluationCountMap().getOrDefault(evaluationId, 0) > 0) {
            throw bizException(
                    Code.CODE_PARAMS_ERROR, "Cannot delete evaluation because it is used in booking requests");
        }

        // 2. 检查是否有 booking request，如果有则返回无法删除
        var bookingRequestResponse = bookingRequestServiceBlockingStub.countBookingRequestByFilter(
                CountBookingRequestByFilterRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .addEvaluationIds(evaluationId)
                        .build());

        if (bookingRequestResponse.getEvaluationInUseMap().getOrDefault(evaluationId, 0) > 0) {
            throw bizException(
                    Code.CODE_PARAMS_ERROR, "Cannot delete evaluation because it is used in booking requests");
        }

        // 3. 删除 evaluation
        evaluationServiceBlockingStub.deleteEvaluation(DeleteEvaluationRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setId(evaluationId)
                .build());
        responseObserver.onNext(DeleteEvaluationResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getBusinessListWithApplicableEvaluation(
            GetBusinessListWithApplicableEvaluationParams request,
            StreamObserver<GetBusinessListWithApplicableEvaluationResult> responseObserver) {
        var evaluationResponse = evaluationServiceBlockingStub.getBusinessListWithApplicableEvaluation(
                com.moego.idl.service.offering.v1.GetBusinessListWithApplicableEvaluationRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .build());

        var locations = businessServiceBlockingStub
                .getLocationList(GetLocationListRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setTokenStaffId(AuthContext.get().staffId())
                        .build())
                .getLocationList();

        var workingLocations = locations.stream()
                .filter(LocationBriefView::getIsWorkingLocation)
                .toList();

        if (evaluationResponse.getIsAllLocation()) {
            responseObserver.onNext(GetBusinessListWithApplicableEvaluationResult.newBuilder()
                    .addAllBusinesses(workingLocations)
                    .build());
        } else {
            responseObserver.onNext(GetBusinessListWithApplicableEvaluationResult.newBuilder()
                    .addAllBusinesses(workingLocations.stream()
                            .filter(location ->
                                    evaluationResponse.getBusinessIdsList().contains(location.getId()))
                            .toList())
                    .build());
        }

        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getEvaluation(GetEvaluationParams request, StreamObserver<GetEvaluationResult> responseObserver) {
        var response = evaluationServiceBlockingStub.getEvaluation(
                GetEvaluationRequest.newBuilder().setId(request.getId()).build());
        if (response.getEvaluationModel().getCompanyId() != AuthContext.get().companyId()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Evaluation not found");
        }
        responseObserver.onNext(GetEvaluationResult.newBuilder()
                .setEvaluation(response.getEvaluationModel())
                .build());
        responseObserver.onCompleted();
    }
}
