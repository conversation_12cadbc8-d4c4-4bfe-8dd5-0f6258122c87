package com.moego.api.v3.offering.convertor;

import com.moego.idl.api.offering.v1.ListEvaluationStaffsResult;
import com.moego.idl.models.organization.v1.StaffBasicView;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper()
public interface ServiceStaffConvertor {

    ServiceStaffConvertor INSTANCE = Mappers.getMapper(ServiceStaffConvertor.class);

    static ListEvaluationStaffsResult.StaffView toListEvaluationStaffsResultStaffView(StaffBasicView staff) {
        return ListEvaluationStaffsResult.StaffView.newBuilder()
                .setId(staff.getId())
                .setFirstName(staff.getFirstName())
                .setLastName(staff.getLastName())
                .setAvatarPath(staff.getAvatarPath())
                .build();
    }
}
