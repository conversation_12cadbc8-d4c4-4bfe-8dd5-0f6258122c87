package com.moego.api.v3.offering.controller;

import static java.util.Comparator.comparing;

import com.moego.idl.api.offering.v1.ListServiceStaffOverrideRuleParams;
import com.moego.idl.api.offering.v1.ListServiceStaffOverrideRuleResult;
import com.moego.idl.api.offering.v1.ServiceStaffOverrideRuleServiceGrpc;
import com.moego.idl.service.offering.v1.ListServiceStaffOverrideRuleRequest;
import com.moego.idl.service.offering.v1.ListServiceStaffOverrideRuleResponse;
import com.moego.idl.service.offering.v1.ServiceStaffOverrideRuleServiceGrpc.ServiceStaffOverrideRuleServiceBlockingStub;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2024/8/21
 */
@GrpcService
@RequiredArgsConstructor
public class ServiceStaffOverrideRuleServer
        extends ServiceStaffOverrideRuleServiceGrpc.ServiceStaffOverrideRuleServiceImplBase {

    private final ServiceStaffOverrideRuleServiceBlockingStub serviceStaffOverrideRuleStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void listServiceStaffOverrideRule(
            ListServiceStaffOverrideRuleParams request,
            StreamObserver<ListServiceStaffOverrideRuleResult> responseObserver) {

        if (request.getServiceIdListList().isEmpty()) {
            responseObserver.onNext(ListServiceStaffOverrideRuleResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        var serviceStaffOverrideRuleList = serviceStaffOverrideRuleStub
                .listServiceStaffOverrideRule(ListServiceStaffOverrideRuleRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(request.getBusinessId())
                        .addAllServiceIds(request.getServiceIdListList())
                        .build())
                .getRulesList()
                .stream()
                .sorted(comparing(ListServiceStaffOverrideRuleResponse.ServiceStaffOverrideRule::getStaffId))
                .toList();

        var paginatedList = serviceStaffOverrideRuleList.stream()
                .skip((long) (request.getPagination().getPageNum() - 1)
                        * request.getPagination().getPageSize())
                .limit(request.getPagination().getPageSize())
                .toList();

        var builder = ListServiceStaffOverrideRuleResult.newBuilder();
        for (var rule : paginatedList) {
            var b = ListServiceStaffOverrideRuleResult.ServiceStaffOverrideRule.newBuilder();
            b.setServiceId(rule.getServiceId());
            b.setStaffId(rule.getStaffId());
            if (rule.hasDuration()) {
                b.setDuration(rule.getDuration());
            }
            if (rule.hasPrice()) {
                b.setPrice(rule.getPrice());
            }
            builder.addServiceStaffOverrideRuleList(b.build());
        }

        builder.setPagination(PaginationResponse.newBuilder()
                .setPageNum(request.getPagination().getPageNum())
                .setPageSize(request.getPagination().getPageSize())
                .setTotal(serviceStaffOverrideRuleList.size())
                .build());

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }
}
