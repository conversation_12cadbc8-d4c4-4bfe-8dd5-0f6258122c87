package com.moego.api.v3.offering.convertor;

import com.moego.idl.api.offering.v1.UpdateLodgingUnitParams;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.models.offering.v1.LodgingUnitView;
import com.moego.idl.service.offering.v1.UpdateLodgingUnitRequest;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LodgingUnitConvertor {

    LodgingUnitConvertor INSTANCE = Mappers.getMapper(LodgingUnitConvertor.class);

    UpdateLodgingUnitRequest toUpdateLodgingUnitRequest(UpdateLodgingUnitParams params);

    LodgingUnitView toApiModel(LodgingUnitModel serviceModel);
}
