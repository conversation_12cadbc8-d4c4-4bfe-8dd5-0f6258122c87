package com.moego.api.v3.offering.controller;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;
import static java.util.Comparator.comparing;
import static java.util.Comparator.reverseOrder;
import static java.util.stream.Collectors.toMap;

import com.google.type.LatLng;
import com.moego.api.v3.offering.convertor.ServiceStaffConvertor;
import com.moego.api.v3.offering.service.EvaluationService;
import com.moego.api.v3.organization.service.StaffService;
import com.moego.idl.api.offering.v1.ListEvaluationStaffsParams;
import com.moego.idl.api.offering.v1.ListEvaluationStaffsResult;
import com.moego.idl.api.offering.v1.ListServiceStaffsParams;
import com.moego.idl.api.offering.v1.ListServiceStaffsResult;
import com.moego.idl.api.offering.v1.ServiceStaffServiceGrpc;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.StaffBasicView;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc;
import com.moego.idl.service.business_customer.v1.GetCustomerPrimaryAddressRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.ListAvailableStaffIdRequest;
import com.moego.idl.service.offering.v1.ListAvailableStaffIdResponse;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import io.grpc.stub.StreamObserver;
import jakarta.annotation.Nullable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 * @since 2024/8/21
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class ServiceStaffServer extends ServiceStaffServiceGrpc.ServiceStaffServiceImplBase {

    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;
    private final EvaluationService evaluationService;
    private final StaffService staffService;
    private final BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub
            customerAddressServiceBlockingStub;
    private final FeatureFlagApi featureFlagApi;

    @Override
    @Auth(AuthType.COMPANY)
    public void listServiceStaffs(
            ListServiceStaffsParams request, StreamObserver<ListServiceStaffsResult> responseObserver) {

        // 1. 获取 business 可用的 staff
        var availableStaffsForBusiness =
                staffService.listStaffForBusiness(AuthContext.get().companyId(), request.getBusinessId(), true);

        if (ObjectUtils.isEmpty(availableStaffsForBusiness)) {
            responseObserver.onNext(ListServiceStaffsResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        // 2. 获取 service 可用的 staff
        var availableStaffsForService =
                listStaffIdForService(AuthContext.get().companyId(), request.getBusinessId(), request.getServiceId());

        // 3. 获取所有 staff 的 customized service
        var conditionAndServiceList = batchGetCustomizedService(
                request,
                availableStaffsForBusiness.stream().map(StaffBasicView::getId).toList());

        var result = buildListServiceStaffsResult(
                request, conditionAndServiceList, availableStaffsForBusiness, availableStaffsForService);

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listEvaluationStaffs(
            ListEvaluationStaffsParams request, StreamObserver<ListEvaluationStaffsResult> responseObserver) {
        long companyId = AuthContext.get().companyId();
        long businessId = request.getBusinessId();
        long pageSize = request.getPagination().getPageSize();
        long pageNum = request.getPagination().getPageNum();

        // 1. 获取 business 可用的 staff
        var staffs = staffService.listStaffForBusiness(companyId, businessId, true);

        // 2. 获取 evaluation 详情
        var evaluation = evaluationService.getEvaluationById(request.getEvaluationId());
        if (evaluation == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "Evaluation not found");
        }

        // 3. 按 evaluation 配置过滤 staff
        if (!evaluation.getIsAllStaff()) {
            staffs = staffs.stream()
                    .filter(k -> evaluation.getAllowedStaffListList().contains(k.getId()))
                    .toList();
        }

        var result = ListEvaluationStaffsResult.newBuilder()
                .setPagination(PaginationResponse.newBuilder()
                        .setPageNum(request.getPagination().getPageNum())
                        .setPageSize(request.getPagination().getPageSize())
                        .setTotal(staffs.size())
                        .build())
                .addAllStaffs(staffs.stream()
                        .sorted(comparing(StaffBasicView::getSort, reverseOrder()))
                        .skip(pageSize * (pageNum - 1))
                        .limit(pageSize)
                        .map(ServiceStaffConvertor::toListEvaluationStaffsResultStaffView)
                        .toList())
                .build();

        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    private ListAvailableStaffIdResponse.StaffIds listStaffIdForService(
            long companyId, long businessId, long serviceId) {
        return serviceStub
                .listAvailableStaffId(ListAvailableStaffIdRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .addServiceIds(serviceId)
                        .build())
                .getServiceIdToStaffIdsMap()
                .getOrDefault(serviceId, ListAvailableStaffIdResponse.StaffIds.getDefaultInstance());
    }

    private static ListServiceStaffsResult buildListServiceStaffsResult(
            ListServiceStaffsParams request,
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> conditionAndServiceList,
            List<StaffBasicView> availableStaffsForBusiness,
            ListAvailableStaffIdResponse.StaffIds availableStaffsForService) {

        var all = new ArrayList<ListServiceStaffsResult.StaffWithServicePriceAndDurationView>();

        var staffCustomizedServiceMap = conditionAndServiceList.stream()
                .collect(toMap(
                        e -> e.getQueryCondition().getStaffId(),
                        BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService,
                        (o, n) -> o));

        // 按照 staff 的 sort 排序返回
        availableStaffsForBusiness = availableStaffsForBusiness.stream()
                .sorted(comparing(StaffBasicView::getSort, reverseOrder()))
                .toList();
        for (var staff : availableStaffsForBusiness) {
            var service = staffCustomizedServiceMap.get(staff.getId());
            if (service == null) {
                continue;
            }

            var staffBuilder = ListServiceStaffsResult.StaffWithServicePriceAndDurationView.newBuilder();
            staffBuilder.setStaffId(staff.getId());
            staffBuilder.setFirstName(staff.getFirstName());
            staffBuilder.setLastName(staff.getLastName());
            staffBuilder.setAvatarPath(staff.getAvatarPath());
            staffBuilder.setServicePrice(service.getPrice());
            staffBuilder.setPriceOverrideType(service.getPriceOverrideType());
            staffBuilder.setServiceDuration(service.getDuration());
            staffBuilder.setDurationOverrideType(service.getDurationOverrideType());
            if (availableStaffsForService.getIsAllStaff()) {
                staffBuilder.setIsAvailable(true);
            } else {
                staffBuilder.setIsAvailable(
                        availableStaffsForService.getStaffIdsList().contains(staff.getId()));
            }

            all.add(staffBuilder.build());
        }

        var resultBuilder = ListServiceStaffsResult.newBuilder();
        resultBuilder.setPagination(PaginationResponse.newBuilder()
                .setPageNum(request.getPagination().getPageNum())
                .setPageSize(request.getPagination().getPageSize())
                .setTotal(all.size())
                .build());
        all.stream()
                .skip((long) (request.getPagination().getPageNum() - 1)
                        * request.getPagination().getPageSize())
                .limit(request.getPagination().getPageSize())
                .forEach(resultBuilder::addStaffs);

        return resultBuilder.build();
    }

    private List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> batchGetCustomizedService(
            ListServiceStaffsParams request, List<Long> staffIds) {

        var builder = BatchGetCustomizedServiceRequest.newBuilder();

        builder.setCompanyId(AuthContext.get().companyId());

        Optional<String> zipcode = Optional.empty();
        Optional<LatLng> coordinate = Optional.empty();
        if (enableVaryPricingByZone(request.getBusinessId()) && request.hasCustomerId()) {
            var address = getPrimaryAddress(request.getCustomerId());
            if (Objects.nonNull(address) && address.hasCoordinate()) {
                zipcode = Optional.of(address.getZipcode());
                coordinate = Optional.of(address.getCoordinate());
            }
        }

        for (var staffId : staffIds) {
            var conditionBuilder = CustomizedServiceQueryCondition.newBuilder();
            conditionBuilder.setServiceId(request.getServiceId());
            conditionBuilder.setBusinessId(request.getBusinessId());
            if (request.hasPetId()) {
                conditionBuilder.setPetId(request.getPetId());
            }
            conditionBuilder.setStaffId(staffId);

            if (zipcode.isPresent()) {
                conditionBuilder.setZipcode(zipcode.get()).setCoordinate(coordinate.get());
            }

            builder.addQueryConditionList(conditionBuilder.build());
        }

        return serviceStub.batchGetCustomizedService(builder.build()).getCustomizedServiceListList();
    }

    @Nullable
    private BusinessCustomerAddressModel getPrimaryAddress(Long customerId) {
        try {
            var request = GetCustomerPrimaryAddressRequest.newBuilder()
                    .setCustomerId(customerId)
                    .build();
            var response = customerAddressServiceBlockingStub.getCustomerPrimaryAddress(request);
            if (response.hasAddress()) {
                return response.getAddress();
            }
        } catch (Exception e) {
            log.error("Failed to get primary address, customerId {}", customerId, e);
        }
        return null;
    }

    private boolean enableVaryPricingByZone(Long businessId) {
        try {
            return featureFlagApi.isOn(
                    FeatureFlags.ENABLE_VARY_PRICING_BY_ZONE,
                    FeatureFlagContext.builder().business(businessId).build());
        } catch (Exception e) {
            log.error("Failed to get vary pricing by zone feature flag: {}", businessId, e);
            return false;
        }
    }
}
