package com.moego.api.v3.offering.service;

import com.google.type.Date;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.AppointmentType;
import com.moego.idl.models.offering.v1.LocationOverrideRule;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.service.appointment.v1.AppointmentCountServiceGrpc;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.GetAppointmentCountByServiceRequest;
import com.moego.idl.service.appointment.v1.GetAppointmentListRequest;
import com.moego.idl.service.appointment.v1.ListAppointmentForPetsRequest;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.service.appointment.v1.UpdateUpcomingAppointmentsRequest;
import com.moego.idl.service.appointment.v1.UpdateUpcomingPetDetailsRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetLocationListRequest;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service("offeringAppointmentService")
@RequiredArgsConstructor
@Slf4j
public class AppointmentService {
    private final PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailServiceClient;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceClient;
    private final AppointmentCountServiceGrpc.AppointmentCountServiceBlockingStub appointmentCountServiceClient;
    private final AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentServiceClient;

    public boolean isServiceUsedInAppointment(Long companyId, Long serviceId) {
        return appointmentCountServiceClient
                        .getAppointmentCountByService(GetAppointmentCountByServiceRequest.newBuilder()
                                .setCompanyId(companyId)
                                .setServiceId(serviceId)
                                .build())
                        .getCount()
                > 0;
    }

    public List<AppointmentModel> batchGetAppointmentModel(Long companyId, List<Long> appointmentIds) {
        return appointmentServiceClient
                .getAppointmentList(GetAppointmentListRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllAppointmentId(appointmentIds)
                        .build())
                .getAppointmentsList();
    }

    public boolean isServiceUsedInUpcomingAppointment(Long companyId, Long businessId, Long serviceId) {
        return appointmentCountServiceClient
                        .getAppointmentCountByService(GetAppointmentCountByServiceRequest.newBuilder()
                                .setCompanyId(companyId)
                                .setBusinessId(businessId)
                                .setServiceId(serviceId)
                                .setAppointmentType(AppointmentType.UPCOMING)
                                .build())
                        .getCount()
                > 0;
    }

    public void applyServiceChangeToUpcomingAppointments(
            Long companyId, Long staffId, ServiceModel oldService, ServiceModel newService) {
        List<Long> businessIds = businessServiceClient
                .getLocationList(GetLocationListRequest.newBuilder()
                        .setTokenCompanyId(companyId)
                        .build())
                .getLocationList()
                .stream()
                .map(LocationBriefView::getId)
                .toList();

        List<LocationOverrideRule> overrideRules = new ArrayList<>();
        businessIds.forEach(businessId -> {
            Double oldPrice = getServicePrice(businessId, oldService);
            Double newPrice = getServicePrice(businessId, newService);
            Integer oldDuration = getServiceDuration(businessId, oldService);
            Integer newDuration = getServiceDuration(businessId, newService);
            Long oldTaxId = getServiceTaxId(businessId, oldService);
            Long newTaxId = getServiceTaxId(businessId, newService);

            if (Objects.equals(oldPrice, newPrice)
                    && Objects.equals(oldDuration, newDuration)
                    && Objects.equals(oldTaxId, newTaxId)) {
                return;
            }

            LocationOverrideRule.Builder builder =
                    LocationOverrideRule.newBuilder().setBusinessId(businessId);
            if (!Objects.equals(oldPrice, newPrice)) {
                builder.setPrice(newPrice);
            }

            if (!Objects.equals(oldDuration, newDuration)) {
                builder.setDuration(newDuration);
            }

            if (!Objects.equals(oldTaxId, newTaxId)) {
                builder.setTaxId(newTaxId);
            }

            overrideRules.add(builder.build());
        });

        if (!CollectionUtils.isEmpty(overrideRules)) {
            // TODO: 改成异步执行
            var resp = petDetailServiceClient.updateUpcomingPetDetails(UpdateUpcomingPetDetailsRequest.newBuilder()
                    .setCompanyId(companyId)
                    .setServiceId(oldService.getServiceId())
                    .setStaffId(staffId)
                    .addAllLocationOverrideRule(overrideRules)
                    .build());
            log.debug("affected {} appointments", resp.getAffectedApptCount());
        }
    }

    public void applyUpcomingAppointments(ServiceModel oldService) {
        List<Long> businessIds = businessServiceClient
                .getLocationList(GetLocationListRequest.newBuilder()
                        .setTokenCompanyId(oldService.getCompanyId())
                        .build())
                .getLocationList()
                .stream()
                .map(LocationBriefView::getId)
                .toList();

        petDetailServiceClient.updateUpcomingAppointments(UpdateUpcomingAppointmentsRequest.newBuilder()
                .addAllBusinessIds(businessIds)
                .setOldService(oldService)
                .build());
    }

    /**
     * getServicePrice 返回 service 在某个 business 下的价格：
     * 如果存在 location override，则以 override 后的价格为准
     * 否则，使用 company level 的 base rate
     */
    private Double getServicePrice(Long businessId, ServiceModel service) {
        return service.getLocationOverrideListList().stream()
                .filter(locationOverrideRule ->
                        locationOverrideRule.getBusinessId() == businessId && locationOverrideRule.hasPrice())
                .findFirst()
                .map(LocationOverrideRule::getPrice)
                .orElse(service.getPrice());
    }

    /**
     * getServiceDuration 返回 service 在某个 business 下的时长：
     * 如果存在 location override，则以 override 后的时长为准
     * 否则，使用 company level 的 duration
     */
    private Integer getServiceDuration(Long businessId, ServiceModel service) {
        if (Objects.equals(service.getServiceItemType(), ServiceItemType.DAYCARE)) {
            // daycare 用 max duration 替代 duration
            return service.getLocationOverrideListList().stream()
                    .filter(locationOverrideRule ->
                            locationOverrideRule.getBusinessId() == businessId && locationOverrideRule.hasMaxDuration())
                    .findFirst()
                    .map(LocationOverrideRule::getMaxDuration)
                    .orElse(service.getMaxDuration());
        }

        return service.getLocationOverrideListList().stream()
                .filter(locationOverrideRule ->
                        locationOverrideRule.getBusinessId() == businessId && locationOverrideRule.hasDuration())
                .findFirst()
                .map(LocationOverrideRule::getDuration)
                .orElse(service.getDuration());
    }

    /**
     * getServiceTaxId 返回 service 在某个 business 下的 TaxId
     * 如果存在 location override，则以 override 后的 TaxId 为准
     * 否则，使用 company level 的 TaxId
     */
    private Long getServiceTaxId(Long businessId, ServiceModel service) {
        return service.getLocationOverrideListList().stream()
                .filter(locationOverrideRule ->
                        locationOverrideRule.getBusinessId() == businessId && locationOverrideRule.hasTaxId())
                .findFirst()
                .map(LocationOverrideRule::getTaxId)
                .orElse(service.getTaxId());
    }

    /**
     * Batch check whether the pets have specific service in specific date
     */
    public Map<Long /* pet id */, Boolean /* whether the pet has specific service in specific date */>
            batchCheckPetWithServiceAndDate(
                    Long companyId, Long businessId, Long serviceId, List<Long> petIds, Date date) {
        ListAppointmentForPetsRequest.Filter.Builder filterBuilder = ListAppointmentForPetsRequest.Filter.newBuilder()
                .setDate(date)
                .setServiceId(serviceId)
                .addAllStatuses(List.of(
                        AppointmentStatus.UNCONFIRMED,
                        AppointmentStatus.CONFIRMED,
                        AppointmentStatus.CHECKED_IN,
                        AppointmentStatus.READY,
                        AppointmentStatus.FINISHED));
        Optional.ofNullable(businessId).ifPresent(filterBuilder::setBusinessId);
        var petIdToAppointmentId = appointmentServiceClient
                .listAppointmentForPets(ListAppointmentForPetsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .addAllPetIds(petIds)
                        .setFilter(filterBuilder.build())
                        .build())
                .getPetIdToAppointmentIdListMap();

        return petIdToAppointmentId.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> !entry.getValue().getValuesList().isEmpty()));
    }
}
