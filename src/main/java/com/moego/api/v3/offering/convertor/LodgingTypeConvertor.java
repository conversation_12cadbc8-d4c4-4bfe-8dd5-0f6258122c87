package com.moego.api.v3.offering.convertor;

import com.moego.idl.api.offering.v1.CreateLodgingTypeParams;
import com.moego.idl.api.offering.v1.UpdateLodgingTypeParams;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingTypeView;
import com.moego.idl.service.offering.v1.CreateLodgingTypeRequest;
import com.moego.idl.service.offering.v1.UpdateLodgingTypeRequest;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        unmappedTargetPolicy = ReportingPolicy.IGNORE,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED)
public interface LodgingTypeConvertor {

    LodgingTypeConvertor INSTANCE = Mappers.getMapper(LodgingTypeConvertor.class);

    CreateLodgingTypeRequest toCreateLodgingTypeRequest(CreateLodgingTypeParams params);

    UpdateLodgingTypeRequest toUpdateLodgingTypeRequest(UpdateLodgingTypeParams params);

    LodgingTypeView toApiModel(LodgingTypeModel serviceModel);
}
