package com.moego.api.v3.offering.controller;

import com.moego.api.v3.permission.service.CompanyService;
import com.moego.idl.api.offering.v1.CustomizeCareTypeServiceGrpc;
import com.moego.idl.api.offering.v1.ListCareTypesParams;
import com.moego.idl.api.offering.v1.ListCareTypesResult;
import com.moego.idl.api.offering.v1.UpdateCareTypeNameParams;
import com.moego.idl.api.offering.v1.UpdateCareTypeNameResult;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.idl.service.offering.v1.ListCareTypesRequest;
import com.moego.idl.service.offering.v1.ListCareTypesResponse;
import com.moego.idl.service.offering.v1.UpdateCareTypeNameRequest;
import com.moego.idl.service.offering.v1.WhiteListFilter;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.featureflag.FeatureFlagApi;
import com.moego.lib.featureflag.FeatureFlagContext;
import com.moego.lib.featureflag.features.FeatureFlags;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class CustomizeCareTypeServer extends CustomizeCareTypeServiceGrpc.CustomizeCareTypeServiceImplBase {

    private final com.moego.idl.service.offering.v1.CustomizeCareTypeServiceGrpc.CustomizeCareTypeServiceBlockingStub
            customizeCareTypeService;
    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;
    private final CompanyService companyService;
    private final FeatureFlagApi featureFlagApi;

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.EDIT_CARE_TYPE_NAME)
    public void updateCareTypeName(
            UpdateCareTypeNameParams request, StreamObserver<UpdateCareTypeNameResult> responseObserver) {

        Long companyId = AuthContext.get().companyId();
        Long staffId = AuthContext.get().staffId();
        // 白名单过滤
        WhiteListFilter whiteListFilter = buildWhiteListFilter(companyId);
        customizeCareTypeService.updateCareTypeName(UpdateCareTypeNameRequest.newBuilder()
                .setServiceItemType(request.getServiceItemType())
                .setName(request.getName())
                .setCompanyId(companyId)
                .setStaffId(staffId)
                .setWhiteListFilter(whiteListFilter)
                .build());

        responseObserver.onNext(UpdateCareTypeNameResult.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listCareTypes(ListCareTypesParams request, StreamObserver<ListCareTypesResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();
        // 白名单过滤
        WhiteListFilter whiteListFilter = buildWhiteListFilter(companyId);
        ListCareTypesResponse response = customizeCareTypeService.listCareTypes(ListCareTypesRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllServiceItemTypes(request.getServiceItemTypesList())
                .setWhiteListFilter(whiteListFilter)
                .build());

        responseObserver.onNext(ListCareTypesResult.newBuilder()
                .addAllCareTypes(response.getCareTypesList())
                .build());
        responseObserver.onCompleted();
    }

    private WhiteListFilter buildWhiteListFilter(Long companyId) {
        boolean isAllowBoardingAndDaycare = companyService.isInBoardingWhiteList(companyId);
        boolean isAllowGroupClass = featureFlagApi.isOn(
                FeatureFlags.ALLOW_GROUP_CLASS,
                FeatureFlagContext.builder().company(companyId).build());
        boolean isAllowDogWalking = featureFlagApi.isOn(
                FeatureFlags.ALLOW_DOG_WALKING,
                FeatureFlagContext.builder().company(companyId).build());
        return WhiteListFilter.newBuilder()
                .setIsAllowBoardingAndDaycare(isAllowBoardingAndDaycare)
                .setIsAllowDogWalking(isAllowDogWalking)
                .setIsAllowGroupClass(isAllowGroupClass)
                .build();
    }
}
