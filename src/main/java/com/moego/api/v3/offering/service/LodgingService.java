package com.moego.api.v3.offering.service;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.LodgingTypeModel;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.service.offering.v1.BatchDeleteLodgingUnitRequest;
import com.moego.idl.service.offering.v1.DeleteLodgingTypeRequest;
import com.moego.idl.service.offering.v1.GetLodgingUnitListRequest;
import com.moego.idl.service.offering.v1.LodgingTypeIdList;
import com.moego.idl.service.offering.v1.LodgingTypeServiceGrpc;
import com.moego.idl.service.offering.v1.LodgingUnitServiceGrpc;
import com.moego.idl.service.offering.v1.MGetLodgingTypeRequest;
import com.moego.idl.service.offering.v1.MGetLodgingUnitRequest;
import com.moego.idl.service.offering.v1.MGetLodgingUnitResponse;
import com.moego.lib.common.exception.ExceptionUtil;
import java.util.AbstractMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Nullable;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class LodgingService {
    private final LodgingTypeServiceGrpc.LodgingTypeServiceBlockingStub lodgingTypeClient;
    private final LodgingUnitServiceGrpc.LodgingUnitServiceBlockingStub lodgingUnitServiceClient;

    // 按 id 查询 lodging type，包含软删除记录
    public List<LodgingTypeModel> getLodgingTypeByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return List.of();
        }
        return lodgingTypeClient
                .mGetLodgingType(
                        MGetLodgingTypeRequest.newBuilder().addAllIdList(idList).build())
                .getLodgingTypeListList();
    }

    public List<LodgingTypeModel> getLodgingTypeByUnits(List<LodgingUnitModel> unitList) {
        if (CollectionUtils.isEmpty(unitList)) {
            return List.of();
        }
        return getLodgingTypeByIds(
                unitList.stream().map(LodgingUnitModel::getLodgingTypeId).toList());
    }

    public void deleteLodgingType(long companyId, long tokenStaffId, long id) {
        DeleteLodgingTypeRequest request = DeleteLodgingTypeRequest.newBuilder()
                .setCompanyId(companyId)
                .setTokenStaffId(tokenStaffId)
                .setId(id)
                .build();
        lodgingTypeClient.deleteLodgingType(request);
    }

    public List<LodgingUnitModel> getLodgingUnit(
            Long companyId, @Nullable Long businessId, @Nullable List<Long> lodgingTypeIds) {
        if (companyId == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "companyId is required");
        }
        GetLodgingUnitListRequest.Builder getLodgingUnitBuilder =
                GetLodgingUnitListRequest.newBuilder().setCompanyId(companyId);
        if (businessId != null) {
            getLodgingUnitBuilder.setBusinessId(businessId);
        }
        if (!CollectionUtils.isEmpty(lodgingTypeIds)) {
            getLodgingUnitBuilder.setTypeIdList(LodgingTypeIdList.newBuilder()
                    .addAllIdList(lodgingTypeIds.stream().distinct().toList())
                    .build());
        }
        return lodgingUnitServiceClient
                .getLodgingUnitList(getLodgingUnitBuilder.build())
                .getLodgingUnitListList();
    }

    public List<LodgingUnitModel> getAllLodgingUnitInBusiness(Long companyId, Long businessId) {
        return getLodgingUnit(companyId, businessId, null);
    }

    public Map<Long, List<LodgingUnitModel>> getLodgingUnitByLodgingTypes(Long companyId, List<Long> lodgingTypeIds) {
        if (CollectionUtils.isEmpty(lodgingTypeIds)) {
            return Map.of();
        }
        return getLodgingUnit(companyId, null, lodgingTypeIds).stream()
                .collect(Collectors.groupingBy(LodgingUnitModel::getLodgingTypeId));
    }

    public List<LodgingUnitModel> getLodgingUnitByLodgingType(Long companyId, Long lodgingTypeId) {
        return getLodgingUnitByLodgingTypes(companyId, List.of(lodgingTypeId)).getOrDefault(lodgingTypeId, List.of());
    }

    public Long getLodgingTypeId(Long lodgingUnitId) {
        var resp = lodgingUnitServiceClient.mGetLodgingUnit(MGetLodgingUnitRequest.newBuilder()
                .addAllIdList(List.of(lodgingUnitId))
                .build());
        if (resp.getLodgingUnitListList().isEmpty()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "lodging not found");
        }
        return resp.getLodgingUnitList(0).getLodgingTypeId();
    }

    public AbstractMap.SimpleEntry<Map<Long, LodgingUnitModel>, Map<Long, LodgingTypeModel>> getLodgingMap(
            List<Long> lodgingIds) {
        lodgingIds =
                lodgingIds.stream().filter(k -> k != null && k > 0).distinct().toList();
        if (CollectionUtils.isEmpty(lodgingIds)) {
            return new AbstractMap.SimpleEntry<>(Map.of(), Map.of());
        }

        MGetLodgingUnitResponse lodgingUnitResponse = lodgingUnitServiceClient.mGetLodgingUnit(
                MGetLodgingUnitRequest.newBuilder().addAllIdList(lodgingIds).build());
        Map<Long, LodgingUnitModel> lodgingUnitMap = lodgingUnitResponse.getLodgingUnitListList().stream()
                .collect(Collectors.toMap(LodgingUnitModel::getId, Function.identity()));
        Map<Long, LodgingTypeModel> lodgingTypeMap = lodgingUnitResponse.getLodgingTypeListList().stream()
                .collect(Collectors.toMap(LodgingTypeModel::getId, Function.identity()));
        return new AbstractMap.SimpleEntry<>(lodgingUnitMap, lodgingTypeMap);
    }

    public void deleteLodgingUnits(long companyId, long tokenStaffId, List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        BatchDeleteLodgingUnitRequest request = BatchDeleteLodgingUnitRequest.newBuilder()
                .setTokenStaffId(tokenStaffId)
                .setCompanyId(companyId)
                .addAllIds(ids.stream().distinct().toList())
                .build();
        lodgingUnitServiceClient.batchDeleteLodgingUnit(request);
    }

    public Map<Long, LodgingUnitModel> listLodgingUnits(List<Long> lodgingUnitIds) {
        if (CollectionUtils.isEmpty(lodgingUnitIds)) {
            return Map.of();
        }
        return lodgingUnitServiceClient
                .mGetLodgingUnit(MGetLodgingUnitRequest.newBuilder()
                        .addAllIdList(lodgingUnitIds)
                        .build())
                .getLodgingUnitListList()
                .stream()
                .collect(Collectors.toMap(LodgingUnitModel::getId, Function.identity()));
    }
}
