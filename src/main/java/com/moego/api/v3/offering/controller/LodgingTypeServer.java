package com.moego.api.v3.offering.controller;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.api.v3.appointment.service.LodgingUsingService;
import com.moego.api.v3.offering.convertor.LodgingTypeConvertor;
import com.moego.api.v3.offering.service.LodgingService;
import com.moego.idl.api.offering.v1.CreateLodgingTypeParams;
import com.moego.idl.api.offering.v1.CreateLodgingTypeResult;
import com.moego.idl.api.offering.v1.DeleteLodgingTypeParams;
import com.moego.idl.api.offering.v1.DeleteLodgingTypeResult;
import com.moego.idl.api.offering.v1.GetLodgingTypeListParams;
import com.moego.idl.api.offering.v1.GetLodgingTypeListResult;
import com.moego.idl.api.offering.v1.LodgingTypeServiceGrpc.LodgingTypeServiceImplBase;
import com.moego.idl.api.offering.v1.SortLodgingTypeByIdsParams;
import com.moego.idl.api.offering.v1.SortLodgingTypeByIdsResult;
import com.moego.idl.api.offering.v1.UpdateLodgingTypeParams;
import com.moego.idl.api.offering.v1.UpdateLodgingTypeResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.LodgingUnitModel;
import com.moego.idl.service.offering.v1.CreateLodgingTypeRequest;
import com.moego.idl.service.offering.v1.CreateLodgingTypeResponse;
import com.moego.idl.service.offering.v1.GetLodgingTypeListRequest;
import com.moego.idl.service.offering.v1.GetLodgingTypeListResponse;
import com.moego.idl.service.offering.v1.LodgingTypeServiceGrpc.LodgingTypeServiceBlockingStub;
import com.moego.idl.service.offering.v1.PhotoList;
import com.moego.idl.service.offering.v1.RemoveServiceFilterRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub;
import com.moego.idl.service.offering.v1.SortLodgingTypeByIdsRequest;
import com.moego.idl.service.offering.v1.UpdateLodgingTypeRequest;
import com.moego.idl.service.offering.v1.UpdateLodgingTypeResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Map;
import lombok.AllArgsConstructor;

@GrpcService
@AllArgsConstructor
public class LodgingTypeServer extends LodgingTypeServiceImplBase {

    private final LodgingTypeServiceBlockingStub lodgingTypeClient;
    private final ServiceManagementServiceBlockingStub serviceManagementClient;
    private final LodgingService lodgingService;
    private final LodgingUsingService lodgingUsingService;

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_LODGING_SETTING)
    public void createLodgingType(
            CreateLodgingTypeParams params, StreamObserver<CreateLodgingTypeResult> responseObserver) {
        CreateLodgingTypeRequest request = LodgingTypeConvertor.INSTANCE.toCreateLodgingTypeRequest(params).toBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setTokenStaffId(AuthContext.get().staffId())
                .build();
        CreateLodgingTypeResponse result = lodgingTypeClient.createLodgingType(request);
        responseObserver.onNext(CreateLodgingTypeResult.newBuilder()
                .setLodgingType(LodgingTypeConvertor.INSTANCE.toApiModel(result.getLodgingType()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_LODGING_SETTING)
    public void updateLodgingType(
            UpdateLodgingTypeParams params, StreamObserver<UpdateLodgingTypeResult> responseObserver) {
        UpdateLodgingTypeRequest request = LodgingTypeConvertor.INSTANCE.toUpdateLodgingTypeRequest(params).toBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setTokenStaffId(AuthContext.get().staffId())
                .setPhotoList(PhotoList.newBuilder()
                        .addAllPhotoList(params.getPhotoListList())
                        .build())
                .build();
        UpdateLodgingTypeResponse result = lodgingTypeClient.updateLodgingType(request);
        responseObserver.onNext(UpdateLodgingTypeResult.newBuilder()
                .setLodgingType(LodgingTypeConvertor.INSTANCE.toApiModel(result.getLodgingType()))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_LODGING_SETTING)
    public void deleteLodgingType(
            DeleteLodgingTypeParams params, StreamObserver<DeleteLodgingTypeResult> responseObserver) {
        long companyId = AuthContext.get().companyId();
        long tokenStaffId = AuthContext.get().staffId();
        List<LodgingUnitModel> lodgingUnits = lodgingService.getLodgingUnitByLodgingType(companyId, params.getId());
        if (!lodgingUnits.isEmpty()) {
            List<Long> lodgingUnitIds =
                    lodgingUnits.stream().map(LodgingUnitModel::getId).toList();
            Map<Long, List<Long>> lodgingUpcomingAppts =
                    lodgingUsingService.getUpcomingAppointments(companyId, null, lodgingUnitIds);
            if (lodgingUpcomingAppts.values().stream().anyMatch(v -> !v.isEmpty())) {
                throw bizException(Code.CODE_LODGING_TYPE_IN_USE, "lodging type in use");
            }
            lodgingService.deleteLodgingUnits(companyId, tokenStaffId, lodgingUnitIds);
        }

        lodgingService.deleteLodgingType(companyId, tokenStaffId, params.getId());
        serviceManagementClient.removeServiceFilter(RemoveServiceFilterRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setLodgingTypeId(params.getId())
                .build());
        responseObserver.onNext(DeleteLodgingTypeResult.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getLodgingTypeList(
            GetLodgingTypeListParams params, StreamObserver<GetLodgingTypeListResult> responseObserver) {
        GetLodgingTypeListRequest request = GetLodgingTypeListRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .build();
        GetLodgingTypeListResponse result = lodgingTypeClient.getLodgingTypeList(request);
        responseObserver.onNext(GetLodgingTypeListResult.newBuilder()
                .addAllLodgingTypeList(result.getLodgingTypeListList().stream()
                        .map(LodgingTypeConvertor.INSTANCE::toApiModel)
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = PermissionEnums.ACCESS_LODGING_SETTING)
    public void sortLodgingTypeByIds(
            SortLodgingTypeByIdsParams request, StreamObserver<SortLodgingTypeByIdsResult> responseObserver) {
        lodgingTypeClient.sortLodgingTypeByIds(SortLodgingTypeByIdsRequest.newBuilder()
                .addAllIds(request.getIdsList())
                .setCompanyId(AuthContext.get().companyId())
                .setStaffId(AuthContext.get().staffId())
                .build());
        responseObserver.onNext(SortLodgingTypeByIdsResult.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
