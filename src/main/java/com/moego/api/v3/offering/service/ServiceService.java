package com.moego.api.v3.offering.service;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.metadata.v1.OwnerType;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.models.organization.v1.StaffEmployeeCategory;
import com.moego.idl.service.metadata.v1.ExtractValuesRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.CompanyServiceGrpc;
import com.moego.idl.service.organization.v1.GetLocationListRequest;
import com.moego.idl.service.organization.v1.GetStaffDetailRequest;
import com.moego.idl.service.organization.v1.QueryCompaniesByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import jakarta.annotation.Nullable;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@RequiredArgsConstructor
public class ServiceService {
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementServiceClient;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceClient;
    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;
    private final CompanyServiceGrpc.CompanyServiceBlockingStub companyServiceBlockingStub;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub;

    public List<Long> getAvailableLocationIds(Long companyId, boolean isAllLocation, List<Long> availableLocationIds) {
        if (isAllLocation) {
            return businessServiceClient
                    .getLocationList(GetLocationListRequest.newBuilder()
                            .setTokenCompanyId(companyId)
                            .build())
                    .getLocationList()
                    .stream()
                    .map(LocationBriefView::getId)
                    .toList();
        }
        return availableLocationIds;
    }

    public void checkEnterpriseBlockServiceEdit(Long companyId, Long staffId) {
        var company = companyServiceBlockingStub
                .queryCompaniesByIds(QueryCompaniesByIdsRequest.newBuilder()
                        .addCompanyIds(companyId)
                        .build())
                .getCompanyIdToCompanyMap()
                .get(companyId);
        if (company == null) {
            throw ExceptionUtil.bizException(Code.CODE_COMPANY_NOT_FOUND);
        }
        if (company.getEnterpriseId() == 0) {
            return;
        }
        var isBlock = metadataServiceBlockingStub
                .extractValues(ExtractValuesRequest.newBuilder()
                        .setKeyName("block_franchisee_edit_service_setting")
                        .putOwners(OwnerType.OWNER_TYPE_ENTERPRISE_VALUE, company.getEnterpriseId())
                        .build())
                .getValues()
                .getOrDefault("block_franchisee_edit_service_setting", "false")
                .equals("true");
        if (isBlock) {
            var staff = staffServiceBlockingStub
                    .getStaffDetail(GetStaffDetailRequest.newBuilder()
                            .setCompanyId(companyId)
                            .setId(staffId)
                            .build())
                    .getStaff();
            // enterprise staff won't be blocked
            if (!staff.getEmployeeCategory().equals(StaffEmployeeCategory.ENTERPRISE_OWNER)
                    && !staff.getEmployeeCategory().equals(StaffEmployeeCategory.ENTERPRISE_STAFF)) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "You are not allowed to edit service setting");
            }
        }
    }

    public Map<Long, ServiceBriefView> getServiceMap(@Nullable Long companyId, List<Long> serviceIds) {
        if (CollectionUtils.isEmpty(serviceIds)) {
            return Map.of();
        }
        var builder = GetServiceListByIdsRequest.newBuilder()
                .addAllServiceIds(serviceIds.stream().distinct().toList());
        if (companyId != null) {
            builder.setCompanyId(companyId);
        }
        List<ServiceBriefView> serviceList =
                serviceManagementServiceClient.getServiceListByIds(builder.build()).getServicesList().stream()
                        .toList();
        return serviceList.stream().collect(Collectors.toMap(ServiceBriefView::getId, Function.identity()));
    }
}
