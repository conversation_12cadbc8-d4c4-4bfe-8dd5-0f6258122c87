package com.moego.api.v3.offering.controller;

import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.idl.api.offering.v1.CalculatePricingRuleParams;
import com.moego.idl.api.offering.v1.CalculatePricingRuleResult;
import com.moego.idl.api.offering.v1.CheckConfigurationParams;
import com.moego.idl.api.offering.v1.CheckConfigurationResult;
import com.moego.idl.api.offering.v1.CheckRuleNameParams;
import com.moego.idl.api.offering.v1.CheckRuleNameResult;
import com.moego.idl.api.offering.v1.DeletePricingRuleParams;
import com.moego.idl.api.offering.v1.DeletePricingRuleResult;
import com.moego.idl.api.offering.v1.GetPricingRuleParams;
import com.moego.idl.api.offering.v1.GetPricingRuleResult;
import com.moego.idl.api.offering.v1.ListAssociatedServicesParams;
import com.moego.idl.api.offering.v1.ListAssociatedServicesResult;
import com.moego.idl.api.offering.v1.ListPricingRulesParams;
import com.moego.idl.api.offering.v1.ListPricingRulesResult;
import com.moego.idl.api.offering.v1.PricingRuleServiceGrpc;
import com.moego.idl.api.offering.v1.UpsertPricingRuleParams;
import com.moego.idl.api.offering.v1.UpsertPricingRuleResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ListPricingRuleFilter;
import com.moego.idl.models.offering.v1.PetDetailCalculateResultDef;
import com.moego.idl.models.offering.v1.PricingRuleConfigurationDef;
import com.moego.idl.models.offering.v1.PricingRuleModel;
import com.moego.idl.models.offering.v1.PricingRuleUpsertDef;
import com.moego.idl.service.offering.v1.CalculatePricingRuleRequest;
import com.moego.idl.service.offering.v1.CalculatePricingRuleResponse;
import com.moego.idl.service.offering.v1.CheckRuleNameRequest;
import com.moego.idl.service.offering.v1.CheckRuleNameResponse;
import com.moego.idl.service.offering.v1.GetPricingRuleRequest;
import com.moego.idl.service.offering.v1.GetPricingRuleResponse;
import com.moego.idl.service.offering.v1.ListAssociatedServicesRequest;
import com.moego.idl.service.offering.v1.ListAssociatedServicesResponse;
import com.moego.idl.service.offering.v1.ListPricingRulesRequest;
import com.moego.idl.service.offering.v1.ListPricingRulesResponse;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@RequiredArgsConstructor
public class PricingRuleServer extends PricingRuleServiceGrpc.PricingRuleServiceImplBase {

    private final com.moego.idl.service.offering.v1.PricingRuleServiceGrpc.PricingRuleServiceBlockingStub
            pricingRuleServiceBlockingStub;

    @Override
    @Auth(AuthType.COMPANY)
    public void upsertPricingRule(
            UpsertPricingRuleParams request, StreamObserver<UpsertPricingRuleResult> responseObserver) {

        throw ExceptionUtil.bizException(Code.CODE_SUCCESS, "System upgrading, please try again later.");
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getPricingRule(GetPricingRuleParams request, StreamObserver<GetPricingRuleResult> responseObserver) {

        GetPricingRuleResponse response =
                pricingRuleServiceBlockingStub.getPricingRule(GetPricingRuleRequest.newBuilder()
                        .setId(request.getId())
                        .setCompanyId(AuthContext.get().companyId())
                        .build());

        responseObserver.onNext(GetPricingRuleResult.newBuilder()
                .setPricingRule(response.getPricingRule())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listPricingRules(
            ListPricingRulesParams request, StreamObserver<ListPricingRulesResult> responseObserver) {
        ListPricingRulesResponse response =
                pricingRuleServiceBlockingStub.listPricingRules(ListPricingRulesRequest.newBuilder()
                        .setFilter(request.getFilter())
                        .setPagination(request.getPagination())
                        .setCompanyId(AuthContext.get().companyId())
                        .build());

        responseObserver.onNext(ListPricingRulesResult.newBuilder()
                .addAllPricingRules(response.getPricingRulesList())
                .setPagination(response.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void calculatePricingRule(
            CalculatePricingRuleParams request, StreamObserver<CalculatePricingRuleResult> responseObserver) {
        CalculatePricingRuleRequest.Builder builder = CalculatePricingRuleRequest.newBuilder()
                .addAllPetDetails(request.getPetDetailsList())
                .setIsPreview(request.getIsPreview())
                .setCompanyId(AuthContext.get().companyId())
                .setBusinessId(AuthContext.get().businessId());
        if (request.hasPricingRuleUpsertDef()) {
            builder.setPricingRuleUpsertDef(request.getPricingRuleUpsertDef());
        }
        CalculatePricingRuleResponse response = pricingRuleServiceBlockingStub.calculatePricingRule(builder.build());

        List<PricingRuleModel> pricingRules = new ArrayList<>();
        List<Long> pricingRuleIds = response.getPetDetailsList().stream()
                .map(PetDetailCalculateResultDef::getAppliedRuleIdsList)
                .flatMap(List::stream)
                .filter(CommonUtil::isNormal)
                .distinct()
                .toList();
        if (!pricingRuleIds.isEmpty()) {
            pricingRules = pricingRuleServiceBlockingStub
                    .listPricingRules(ListPricingRulesRequest.newBuilder()
                            .setPagination(PaginationRequest.newBuilder()
                                    .setPageNum(1)
                                    .setPageSize(1000)
                                    .build())
                            .addAllIds(pricingRuleIds)
                            .setCompanyId(AuthContext.get().companyId())
                            .build())
                    .getPricingRulesList();
        }

        responseObserver.onNext(CalculatePricingRuleResult.newBuilder()
                .addAllPetDetails(response.getPetDetailsList())
                .setFormula(response.getFormula())
                .addAllPricingRules(pricingRules)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void deletePricingRule(
            DeletePricingRuleParams request, StreamObserver<DeletePricingRuleResult> responseObserver) {

        throw ExceptionUtil.bizException(Code.CODE_SUCCESS, "System upgrading, please try again later.");
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listAssociatedServices(
            ListAssociatedServicesParams request, StreamObserver<ListAssociatedServicesResult> responseObserver) {
        ListAssociatedServicesRequest.Filter.Builder filterBuilder = ListAssociatedServicesRequest.Filter.newBuilder();
        if (request.hasFilter()) {
            ListAssociatedServicesParams.Filter filter = request.getFilter();
            if (filter.hasExcludePricingRuleId()) {
                filterBuilder.setExcludePricingRuleId(filter.getExcludePricingRuleId());
            }
            if (filter.hasRuleGroupType()) {
                filterBuilder.setRuleGroupType(filter.getRuleGroupType());
            }
        }
        ListAssociatedServicesResponse response =
                pricingRuleServiceBlockingStub.listAssociatedServices(ListAssociatedServicesRequest.newBuilder()
                        .setServiceItemType(request.getServiceItemType())
                        .setServiceType(request.getServiceType())
                        .setFilter(filterBuilder.build())
                        .setCompanyId(AuthContext.get().companyId())
                        .build());

        responseObserver.onNext(ListAssociatedServicesResult.newBuilder()
                .addAllAssociatedServiceIds(response.getAssociatedServiceIdsList())
                .setAllServiceAssociated(response.getAllServiceAssociated())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void checkRuleName(CheckRuleNameParams request, StreamObserver<CheckRuleNameResult> responseObserver) {
        CheckRuleNameResponse response = pricingRuleServiceBlockingStub.checkRuleName(CheckRuleNameRequest.newBuilder()
                .setRuleName(request.getRuleName())
                .setExcludePricingRuleId(request.getExcludePricingRuleId())
                .setCompanyId(AuthContext.get().companyId())
                .build());

        responseObserver.onNext(CheckRuleNameResult.newBuilder()
                .setIsExist(response.getIsExist())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void checkConfiguration(
            CheckConfigurationParams request, StreamObserver<CheckConfigurationResult> responseObserver) {
        Long companyId = AuthContext.get().companyId();

        String message = "";
        boolean isValid = true;
        try {
            checkConfiguration(companyId, request.getPricingRuleUpsertDef());
        } catch (Exception e) {
            message = e.getMessage();
            isValid = false;
        }

        responseObserver.onNext(CheckConfigurationResult.newBuilder()
                .setIsValid(isValid)
                .setErrorMessage(message)
                .build());
        responseObserver.onCompleted();
    }

    /**
     * Checks pricing rule configuration for conflicts with existing rules
     */
    private void checkConfiguration(Long companyId, PricingRuleUpsertDef def) {
        var builder = ListPricingRuleFilter.newBuilder()
                .setServiceItemType(def.getServiceItemType())
                .setServiceType(def.getServiceType())
                .setIsActive(true);
        if (def.hasRuleGroupType()) {
            builder.setRuleGroupType(def.getRuleGroupType());
        }

        var pricingRules = pricingRuleServiceBlockingStub
                .listPricingRules(ListPricingRulesRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setFilter(builder.build())
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1000)
                                .build())
                        .build())
                .getPricingRulesList();

        if (pricingRules.isEmpty()) {
            return;
        }

        // Check service date range for peak date
        var currentServiceToPeakDateMap = getServiceToDateDefMap(def);

        for (var pricingRule : pricingRules) {
            // Skip the current rule
            if (def.hasId() && Objects.equals(pricingRule.getId(), def.getId())) {
                continue;
            }

            var existingServiceDates = getServiceToDateModelMap(pricingRule);

            for (var entry : existingServiceDates.entrySet()) {
                var serviceId = entry.getKey();
                var existingDates = entry.getValue();

                var currentDates = currentServiceToPeakDateMap.get(serviceId);
                if (currentDates == null || existingDates.isEmpty()) {
                    continue;
                }

                var intersectionDates = new HashSet<>(currentDates);
                intersectionDates.retainAll(existingDates);

                if (!intersectionDates.isEmpty()) {
                    throw ExceptionUtil.bizException(
                            Code.CODE_PARAMS_ERROR,
                            "Service date range conflict with existing rule: " + pricingRule.getRuleName());
                }
            }
        }
    }

    /**
     * Gets a map of service IDs to their peak dates from a pricing rule definition
     */
    private static Map<Long, Set<String>> getServiceToDateDefMap(PricingRuleUpsertDef def) {
        if (def.getIsAllServiceApplicable()) {
            return Map.of(0L, extractPeakDates(def));
        }

        return def.getSelectedServicesList().stream()
                .distinct()
                .collect(Collectors.toMap(
                        serviceId -> serviceId,
                        serviceId -> extractPeakDates(def),
                        (existing, replacement) -> existing));
    }

    /**
     * Gets a map of service IDs to their peak dates from a pricing rule model
     */
    private static Map<Long, Set<String>> getServiceToDateModelMap(PricingRuleModel model) {
        if (model.getIsAllServiceApplicable()) {
            return Map.of(0L, extractPeakDates(model));
        }

        return model.getSelectedServicesList().stream()
                .distinct()
                .collect(Collectors.toMap(
                        serviceId -> serviceId,
                        serviceId -> extractPeakDates(model),
                        (existing, replacement) -> existing));
    }

    /**
     * Extracts peak dates from a pricing rule definition
     */
    private static Set<String> extractPeakDates(PricingRuleUpsertDef def) {
        return def.getRuleConfiguration().getPricingRuleItemDefsList().stream()
                .map(PricingRuleConfigurationDef.PricingRuleItemDef::getPeakDateDef)
                .flatMap(peakDateDef -> peakDateDef.getItemDefsList().stream())
                .flatMap(item -> DateUtil.generateAllDatesBetween(item.getStartDate(), item.getEndDate()).stream())
                .collect(Collectors.toSet());
    }

    /**
     * Extracts peak dates from a pricing rule model
     */
    private static Set<String> extractPeakDates(PricingRuleModel model) {
        return model.getRuleConfiguration().getPricingRuleItemDefsList().stream()
                .map(PricingRuleConfigurationDef.PricingRuleItemDef::getPeakDateDef)
                .flatMap(peakDateDef -> peakDateDef.getItemDefsList().stream())
                .flatMap(item -> DateUtil.generateAllDatesBetween(item.getStartDate(), item.getEndDate()).stream())
                .collect(Collectors.toSet());
    }
}
