package com.moego.api.v3.capital.service;

import com.google.protobuf.Empty;
import com.moego.idl.api.capital.v1.AdminSyncOfferRequest;
import com.moego.idl.api.capital.v1.CreateLinkRequest;
import com.moego.idl.api.capital.v1.CreateLinkResponse;
import com.moego.idl.api.capital.v1.DismissOfferNotableUpdateRequest;
import com.moego.idl.api.capital.v1.DismissOfferNotableUpdateResponse;
import com.moego.idl.api.capital.v1.GetLoanEligibilityFlagsResponse;
import com.moego.idl.api.capital.v1.GetLoanEligibilityResponse;
import com.moego.idl.api.capital.v1.GetNotableUpdatesResponse;
import com.moego.idl.api.capital.v1.GetOfferDetailRequest;
import com.moego.idl.api.capital.v1.GetOfferDetailResponse;
import com.moego.idl.api.capital.v1.GetOfferNotableUpdatesResponse;
import com.moego.idl.api.capital.v1.GetOnboardingStatusRequest;
import com.moego.idl.api.capital.v1.GetOnboardingStatusResponse;
import com.moego.idl.api.capital.v1.GetRepaymentIntervalsRequest;
import com.moego.idl.api.capital.v1.GetRepaymentIntervalsResponse;
import com.moego.idl.api.capital.v1.GetRepaymentsRequest;
import com.moego.idl.api.capital.v1.GetRepaymentsResponse;
import com.moego.idl.api.capital.v1.ListAllBusinessesOffersResponse;
import com.moego.idl.api.capital.v1.ListAllBusinessesOffersV2Request;
import com.moego.idl.api.capital.v1.ListAllBusinessesOffersV2Response;
import com.moego.idl.api.capital.v1.ListOffersResponse;
import com.moego.idl.api.capital.v1.ListOffersV2Response;
import com.moego.idl.models.capital.v1.LoanChannel;
import com.moego.idl.models.capital.v1.LoanOfferIntervalModel;
import com.moego.idl.models.capital.v1.LoanOfferModel;
import com.moego.idl.models.organization.v1.LocationBriefView;
import com.moego.idl.service.capital.v1.BatchGetRepaymentIntervalsRequest;
import com.moego.idl.service.capital.v1.DismissNotableUpdatesRequest;
import com.moego.idl.service.capital.v1.GetLoanEligibilityFlagsRequest;
import com.moego.idl.service.capital.v1.GetLoanEligibilityRequest;
import com.moego.idl.service.capital.v1.GetNotableUpdatesRequest;
import com.moego.idl.service.capital.v1.GetOfferListRequest;
import com.moego.idl.service.capital.v1.GetRepaymentListRequest;
import com.moego.idl.service.capital.v1.SyncOfferAndTransactionListRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetLocationListRequest;
import com.moego.idl.utils.v2.PaginationRequest;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.Executors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class LoanService {
    private final com.moego.idl.service.capital.v1.LoanServiceGrpc.LoanServiceBlockingStub loanServiceBlockingStub;
    private final BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub;

    public GetLoanEligibilityResponse getLoanEligibility(Long businessId) {
        var response = loanServiceBlockingStub.getLoanEligibility(
                GetLoanEligibilityRequest.newBuilder().setBusinessId(businessId).build());
        return GetLoanEligibilityResponse.newBuilder()
                .setIsEligible(response.getIsEligible())
                .setIneligibleReason(response.getIneligibleReason())
                .build();
    }

    public GetLoanEligibilityFlagsResponse getLoanEligibilityFlags(Long businessId) {
        var response = loanServiceBlockingStub.getLoanEligibilityFlags(GetLoanEligibilityFlagsRequest.newBuilder()
                .setBusinessId(businessId)
                .build());
        return GetLoanEligibilityFlagsResponse.newBuilder()
                .setIsMgpSetup(response.getIsMgpSetup())
                .setIsMgpPrimary(response.getIsMgpPrimary())
                .setIsMgpPrimaryLongEnough(response.getIsMgpPrimaryLongEnough())
                .build();
    }

    /**
     * This method is deprecated and is used only for compatibility. The old frontend clients should
     * not receive any offers other than Stripe offers.
     */
    @Deprecated
    public ListOffersResponse listOffers(Long businessId) {
        var offers = listOffersOfBusiness(businessId).stream()
                .filter(o -> o.getChannelName() == LoanChannel.STRIPE)
                .toList();
        return ListOffersResponse.newBuilder().addAllOffers(offers).build();
    }

    public ListOffersV2Response listOffersV2(Long businessId) {
        var offers = listOffersOfBusiness(businessId);
        var intervals = loanServiceBlockingStub
                .batchGetRepaymentIntervals(BatchGetRepaymentIntervalsRequest.newBuilder()
                        .setBusinessId(businessId)
                        .addAllOfferIds(
                                offers.stream().map(LoanOfferModel::getOfferId).toList())
                        .build())
                .getIntervalsList();
        var totalMap = new HashMap<String, Integer>();
        var unfinishedMap = new HashMap<String, Integer>();
        var nextIntervalMap = new HashMap<String, LoanOfferIntervalModel>();
        intervals.forEach(it -> {
            var offerId = it.getOfferId();
            totalMap.put(offerId, totalMap.getOrDefault(offerId, 0) + 1);
            var isUnfinished = checkUnfinishedInterval(it);
            if (isUnfinished) {
                unfinishedMap.put(offerId, unfinishedMap.getOrDefault(offerId, 0) + 1);
                var nextBefore = nextIntervalMap.get(offerId);
                if (nextBefore == null || it.getSequence() < nextBefore.getSequence()) {
                    nextIntervalMap.put(offerId, it);
                }
            }
        });
        var details = offers.stream()
                .map(o -> {
                    var id = o.getOfferId();
                    var builder = ListOffersV2Response.OfferAndDetails.newBuilder()
                            .setOffer(o)
                            .setTotalTerms(totalMap.getOrDefault(id, 0))
                            .setUnfinishedTerms(unfinishedMap.getOrDefault(id, 0));
                    var nextInterval = nextIntervalMap.get(id);
                    if (nextInterval != null) {
                        builder.setNextUnfinishedInterval(nextInterval);
                    }
                    return builder.build();
                })
                .toList();
        return ListOffersV2Response.newBuilder().addAllOffers(details).build();
    }

    public GetOfferDetailResponse getOfferDetail(Long businessId, GetOfferDetailRequest request) {
        var offer = loanServiceBlockingStub
                .getOfferDetail(com.moego.idl.service.capital.v1.GetOfferDetailRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setOfferId(request.getOfferId())
                        .build())
                .getOffer();
        var intervals = loanServiceBlockingStub
                .getRepaymentIntervals(com.moego.idl.service.capital.v1.GetRepaymentIntervalsRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setOfferId(request.getOfferId())
                        .build())
                .getIntervalsList();
        var unfinishedIntervals =
                intervals.stream().filter(this::checkUnfinishedInterval).toList();
        var builder = GetOfferDetailResponse.newBuilder()
                .setOffer(offer)
                .setTotalTerms(intervals.size())
                .setUnfinishedTerms(unfinishedIntervals.size());
        if (!unfinishedIntervals.isEmpty()) {
            builder.setNextUnfinishedInterval(unfinishedIntervals.get(0));
        }
        return builder.build();
    }

    private boolean checkUnfinishedInterval(LoanOfferIntervalModel interval) {
        return interval.getPaidAmount() < interval.getMinimumAmount();
    }

    private List<Long> getLocationIds(Long companyId) {
        // Get the business list, following the pagination API
        final var pageSize = 1000;
        var pageNum = 1;
        var locations = new ArrayList<LocationBriefView>();
        boolean hasMorePages;
        do {
            var locationListResponse = businessServiceBlockingStub.getLocationList(GetLocationListRequest.newBuilder()
                    .setTokenCompanyId(companyId)
                    .setPagination(PaginationRequest.newBuilder()
                            .setPageNum(pageNum)
                            .setPageSize(pageSize)
                            .build())
                    .build());
            locations.addAll(locationListResponse.getLocationList());
            hasMorePages = locationListResponse.getPagination().getTotal() > pageNum * pageSize;
            pageNum++;
        } while (hasMorePages);
        return locations.stream().map(LocationBriefView::getId).toList();
    }

    /**
     * This method is deprecated and is used only for compatibility. The old frontend clients should
     * not receive any offers other than Stripe offers.
     */
    @Deprecated
    public ListAllBusinessesOffersResponse listAllBusinessesOffers(Long companyId) {
        var locationIds = getLocationIds(companyId);
        var offers = locationIds.parallelStream()
                .flatMap(id -> listOffersOfBusiness(id).stream())
                .filter(o -> o.getChannelName() == LoanChannel.STRIPE)
                .toList();
        return ListAllBusinessesOffersResponse.newBuilder().addAllOffers(offers).build();
    }

    public ListAllBusinessesOffersV2Response listAllBusinessesOffersV2(
            Long companyId, ListAllBusinessesOffersV2Request request) {
        var locationIds = getLocationIds(companyId);
        var filterChannelSet = request.getChannelsCount() == 0 ? null : new HashSet<>(request.getChannelsList());
        var offers = locationIds.parallelStream()
                .flatMap(id -> listOffersOfBusiness(id).stream())
                .filter(o -> filterChannelSet == null || filterChannelSet.contains(o.getChannelName()))
                .toList();
        return ListAllBusinessesOffersV2Response.newBuilder()
                .addAllOffers(offers)
                .build();
    }

    private List<LoanOfferModel> listOffersOfBusiness(Long businessId) {
        return loanServiceBlockingStub
                .getOfferList(GetOfferListRequest.newBuilder()
                        .setBusinessId(businessId)
                        .build())
                .getOffersList();
    }

    public GetRepaymentsResponse getRepayments(Long businessId, GetRepaymentsRequest request) {
        var response = loanServiceBlockingStub.getRepaymentList(GetRepaymentListRequest.newBuilder()
                .setOfferId(request.getOfferId())
                .setBusinessId(businessId)
                .setPagination(request.getPagination())
                .build());
        return GetRepaymentsResponse.newBuilder()
                .addAllRepayments(response.getRepaymentsList())
                .setPagination(response.getPagination())
                .build();
    }

    public GetRepaymentIntervalsResponse getRepaymentIntervals(Long businessId, GetRepaymentIntervalsRequest request) {
        var response = loanServiceBlockingStub.getRepaymentIntervals(
                com.moego.idl.service.capital.v1.GetRepaymentIntervalsRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setOfferId(request.getOfferId())
                        .build());
        return GetRepaymentIntervalsResponse.newBuilder()
                .addAllIntervals(response.getIntervalsList())
                .build();
    }

    public CreateLinkResponse createLink(Long businessId, CreateLinkRequest request) {
        var reqBuilder = com.moego.idl.service.capital.v1.CreateLinkRequest.newBuilder()
                .setType(request.getType())
                .setBusinessId(businessId)
                .setReturnUrl(request.getReturnUrl())
                .setRefreshUrl(request.getRefreshUrl());
        if (request.hasOfferId()) {
            reqBuilder.setOfferId(request.getOfferId());
        }
        var response = loanServiceBlockingStub.createLink(reqBuilder.build());
        var builder = CreateLinkResponse.newBuilder().setUrl(response.getUrl()).setExpireAt(response.getExpireAt());
        if (response.hasConnectToken()) {
            builder.setConnectToken(response.getConnectToken());
        }
        return builder.build();
    }

    public GetNotableUpdatesResponse getNotableUpdates(Long businessId) {
        var response = loanServiceBlockingStub.getNotableUpdates(
                GetNotableUpdatesRequest.newBuilder().setBusinessId(businessId).build());
        return GetNotableUpdatesResponse.newBuilder()
                .setHaveUpdates(response.getHaveUpdates())
                .build();
    }

    public void dismissNotableUpdates(Long businessId) {
        loanServiceBlockingStub.dismissNotableUpdates(DismissNotableUpdatesRequest.newBuilder()
                .setBusinessId(businessId)
                .build());
    }

    public GetOfferNotableUpdatesResponse getOfferNotableUpdates(Long businessId) {
        var response = loanServiceBlockingStub.getNotableUpdates(
                GetNotableUpdatesRequest.newBuilder().setBusinessId(businessId).build());
        return GetOfferNotableUpdatesResponse.newBuilder()
                .addAllNotableOfferUpdates(response.getNotableOfferUpdatesList())
                .build();
    }

    public DismissOfferNotableUpdateResponse dismissOfferNotableUpdate(
            Long businessId, DismissOfferNotableUpdateRequest request) {
        loanServiceBlockingStub.dismissOfferNotableUpdate(
                com.moego.idl.service.capital.v1.DismissOfferNotableUpdateRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setOfferId(request.getOfferId())
                        .build());
        return DismissOfferNotableUpdateResponse.newBuilder().build();
    }

    public GetOnboardingStatusResponse getOnboardingStatus(Long businessId, GetOnboardingStatusRequest request) {
        var response = loanServiceBlockingStub.getOnboardingStatus(
                com.moego.idl.service.capital.v1.GetOnboardingStatusRequest.newBuilder()
                        .setBusinessId(businessId)
                        .setChannelName(request.getChannelName())
                        .build());
        return GetOnboardingStatusResponse.newBuilder()
                .setOnboardingStatus(response.getOnboardingStatus())
                .setOfferType(response.getOfferType())
                .build();
    }

    public void adminSyncOffer(AdminSyncOfferRequest request) {
        var syncMode = request.getSyncMode() == AdminSyncOfferRequest.SyncMode.SYNC_MODE_UNSPECIFIED
                ? AdminSyncOfferRequest.SyncMode.SYNC_SINGLE_BY_ID
                : request.getSyncMode();
        switch (syncMode) {
            case SYNC_SINGLE_BY_ID -> loanServiceBlockingStub.syncOfferAndTransactionList(
                    SyncOfferAndTransactionListRequest.newBuilder()
                            .setOfferId(request.getOfferId())
                            .build());
            case SYNC_ALL -> {
                Executors.newCachedThreadPool().submit(() -> {
                    loanServiceBlockingStub.saveAllOffer(Empty.newBuilder().build());
                    loanServiceBlockingStub.syncAllOfferAndTransactionList(
                            Empty.newBuilder().build());
                });
                log.info("SYNC_ALL tasks started, check the log for result.");
            }
            default -> log.warn("Sync mode not supported: {}", request.getSyncMode());
        }
    }
}
