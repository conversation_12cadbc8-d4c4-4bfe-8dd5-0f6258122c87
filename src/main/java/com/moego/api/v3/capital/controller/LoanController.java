package com.moego.api.v3.capital.controller;

import static com.moego.lib.common.auth.AuthType.BUSINESS;

import com.google.protobuf.Empty;
import com.moego.api.v3.capital.service.LoanService;
import com.moego.idl.api.capital.v1.AdminSyncOfferRequest;
import com.moego.idl.api.capital.v1.CreateLinkRequest;
import com.moego.idl.api.capital.v1.CreateLinkResponse;
import com.moego.idl.api.capital.v1.DismissOfferNotableUpdateRequest;
import com.moego.idl.api.capital.v1.DismissOfferNotableUpdateResponse;
import com.moego.idl.api.capital.v1.GetLoanEligibilityFlagsRequest;
import com.moego.idl.api.capital.v1.GetLoanEligibilityFlagsResponse;
import com.moego.idl.api.capital.v1.GetLoanEligibilityResponse;
import com.moego.idl.api.capital.v1.GetNotableUpdatesResponse;
import com.moego.idl.api.capital.v1.GetOfferDetailRequest;
import com.moego.idl.api.capital.v1.GetOfferDetailResponse;
import com.moego.idl.api.capital.v1.GetOfferNotableUpdatesRequest;
import com.moego.idl.api.capital.v1.GetOfferNotableUpdatesResponse;
import com.moego.idl.api.capital.v1.GetOnboardingStatusRequest;
import com.moego.idl.api.capital.v1.GetOnboardingStatusResponse;
import com.moego.idl.api.capital.v1.GetRepaymentIntervalsRequest;
import com.moego.idl.api.capital.v1.GetRepaymentIntervalsResponse;
import com.moego.idl.api.capital.v1.GetRepaymentsRequest;
import com.moego.idl.api.capital.v1.GetRepaymentsResponse;
import com.moego.idl.api.capital.v1.ListAllBusinessesOffersResponse;
import com.moego.idl.api.capital.v1.ListAllBusinessesOffersV2Request;
import com.moego.idl.api.capital.v1.ListAllBusinessesOffersV2Response;
import com.moego.idl.api.capital.v1.ListOffersResponse;
import com.moego.idl.api.capital.v1.ListOffersV2Request;
import com.moego.idl.api.capital.v1.ListOffersV2Response;
import com.moego.idl.api.capital.v1.LoanServiceGrpc;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.organization.v1.StaffEmployeeCategory;
import com.moego.idl.service.account.v1.AccountServiceGrpc;
import com.moego.idl.service.account.v1.GetAccountRequest;
import com.moego.idl.service.organization.v1.QueryStaffByIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@GrpcService
@Slf4j
@AllArgsConstructor
// TODO(Perqin, P2): Implement annotation @StaffLevel(Owner)
public class LoanController extends LoanServiceGrpc.LoanServiceImplBase {
    private final LoanService loanService;
    private final StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub;
    private final AccountServiceGrpc.AccountServiceBlockingStub accountServiceBlockingStub;

    @Override
    @Auth(BUSINESS)
    public void getLoanEligibility(Empty request, StreamObserver<GetLoanEligibilityResponse> responseObserver) {
        ensureStaffIsOwner("getLoanEligibility");
        var response = loanService.getLoanEligibility(AuthContext.get().businessId());
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void getLoanEligibilityFlags(
            GetLoanEligibilityFlagsRequest request, StreamObserver<GetLoanEligibilityFlagsResponse> responseObserver) {
        ensureStaffIsOwner("getLoanEligibilityFlags");
        var response = loanService.getLoanEligibilityFlags(AuthContext.get().businessId());
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void listOffers(Empty request, StreamObserver<ListOffersResponse> responseObserver) {
        ensureStaffIsOwner("listOffers");
        var response = loanService.listOffers(AuthContext.get().businessId());
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void listOffersV2(ListOffersV2Request request, StreamObserver<ListOffersV2Response> responseObserver) {
        ensureStaffIsOwner("listOffersV2");
        var response = loanService.listOffersV2(AuthContext.get().businessId());
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void getOfferDetail(GetOfferDetailRequest request, StreamObserver<GetOfferDetailResponse> responseObserver) {
        ensureStaffIsOwner("getOfferDetail");
        var response = loanService.getOfferDetail(AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void listAllBusinessesOffers(
            Empty request, StreamObserver<ListAllBusinessesOffersResponse> responseObserver) {
        ensureStaffIsOwner("listAllBusinessesOffers");
        var response = loanService.listAllBusinessesOffers(AuthContext.get().companyId());
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void listAllBusinessesOffersV2(
            ListAllBusinessesOffersV2Request request,
            StreamObserver<ListAllBusinessesOffersV2Response> responseObserver) {
        ensureStaffIsOwner("listAllBusinessesOffersV2");
        var response = loanService.listAllBusinessesOffersV2(AuthContext.get().companyId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void getRepaymentIntervals(
            GetRepaymentIntervalsRequest request, StreamObserver<GetRepaymentIntervalsResponse> responseObserver) {
        var response = loanService.getRepaymentIntervals(AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void createLink(CreateLinkRequest request, StreamObserver<CreateLinkResponse> responseObserver) {
        ensureStaffIsOwner("createLink");
        var response = loanService.createLink(AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void getRepayments(GetRepaymentsRequest request, StreamObserver<GetRepaymentsResponse> responseObserver) {
        ensureStaffIsOwner("getRepayments");
        var response = loanService.getRepayments(AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void getNotableUpdates(Empty request, StreamObserver<GetNotableUpdatesResponse> responseObserver) {
        ensureStaffIsOwner("getNotableUpdates");
        var response = loanService.getNotableUpdates(AuthContext.get().businessId());
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void dismissNotableUpdates(Empty request, StreamObserver<Empty> responseObserver) {
        ensureStaffIsOwner("dismissNotableUpdates");
        loanService.dismissNotableUpdates(AuthContext.get().businessId());
        responseObserver.onNext(Empty.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void getOfferNotableUpdates(
            GetOfferNotableUpdatesRequest request, StreamObserver<GetOfferNotableUpdatesResponse> responseObserver) {
        ensureStaffIsOwner("getOfferNotableUpdates");
        var response = loanService.getOfferNotableUpdates(AuthContext.get().businessId());
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void dismissOfferNotableUpdate(
            DismissOfferNotableUpdateRequest request,
            StreamObserver<DismissOfferNotableUpdateResponse> responseObserver) {
        ensureStaffIsOwner("dismissOfferNotableUpdate");
        var response = loanService.dismissOfferNotableUpdate(AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void getOnboardingStatus(
            GetOnboardingStatusRequest request, StreamObserver<GetOnboardingStatusResponse> responseObserver) {
        ensureStaffIsOwner("getOnboardingStatus");
        var response = loanService.getOnboardingStatus(AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void adminSyncOffer(AdminSyncOfferRequest request, StreamObserver<Empty> responseObserver) {
        var accountId = AuthContext.get().accountId();
        var accountResponse = accountServiceBlockingStub.getAccount(
                GetAccountRequest.newBuilder().setId(accountId).build());
        final var email = accountResponse.getEmail();
        if (!email.endsWith("@moego.pet")) {
            throw ExceptionUtil.bizException(
                    Code.CODE_FINANCE_NO_ACCESS_INTERNAL_API, "This API is only available for internal users");
        }
        var syncMode = request.getSyncMode();
        var offerId = request.getOfferId();
        log.info(
                "adminSyncOffer: Operator is {} ({}), sync mode is {}, offer ID is {}",
                accountId,
                email,
                syncMode,
                offerId);
        loanService.adminSyncOffer(request);
        responseObserver.onNext(Empty.newBuilder().build());
        responseObserver.onCompleted();
    }

    private void ensureStaffIsOwner(String method) {
        var staffId = AuthContext.get().staffId();
        var companyId = AuthContext.get().companyId();
        var staffDetails = staffServiceBlockingStub.queryStaffByIds(
                QueryStaffByIdsRequest.newBuilder().addStaffIds(staffId).build());
        if (staffDetails.getStaffsList().isEmpty()) {
            log.warn("{}: Current staff {} is not found", method, staffId);
            throw ExceptionUtil.bizException(Code.CODE_STAFF_NOT_FOUND);
        }
        if (staffDetails.getStaffsList().get(0).getEmployeeCategory() != StaffEmployeeCategory.COMPANY_OWNER) {
            log.warn("{}: Current staff {} is not the company owner of the company {}", method, staffId, companyId);
            throw ExceptionUtil.bizException(Code.CODE_CAPITAL_OFFER_NO_ACCESS, "You must be the business owner");
        }
    }
}
