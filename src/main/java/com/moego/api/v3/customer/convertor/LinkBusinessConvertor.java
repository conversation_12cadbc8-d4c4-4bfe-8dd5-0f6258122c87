package com.moego.api.v3.customer.convertor;

import com.moego.idl.api.customer.v1.ClientPortalLinkBusinessResponse;
import com.moego.idl.service.customer.v1.ClientPortalLinkBusinessOutput;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface LinkBusinessConvertor {
    LinkBusinessConvertor INSTANCE = Mappers.getMapper(LinkBusinessConvertor.class);

    List<ClientPortalLinkBusinessResponse> toResponse(List<ClientPortalLinkBusinessOutput> output);

    ClientPortalLinkBusinessResponse toResponse(ClientPortalLinkBusinessOutput output);
}
