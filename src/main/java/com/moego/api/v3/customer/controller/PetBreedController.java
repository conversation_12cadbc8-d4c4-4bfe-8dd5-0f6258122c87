package com.moego.api.v3.customer.controller;

import com.google.protobuf.Empty;
import com.moego.idl.api.customer.v1.PetBreedListResponse;
import com.moego.idl.api.customer.v1.PetBreedServiceGrpc.PetBreedServiceImplBase;
import com.moego.idl.models.customer.v1.PetBreedModel;
import com.moego.idl.service.customer.v1.PetBreedListOutput;
import com.moego.idl.service.customer.v1.PetBreedServiceGrpc.PetBreedServiceBlockingStub;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
@GrpcService
@AllArgsConstructor
public class PetBreedController extends PetBreedServiceImplBase {

    private final PetBreedServiceBlockingStub petBreedServiceBlockingStub;

    @Auth(AuthType.ACCOUNT)
    @Override
    public void getPetBreedList(Empty request, StreamObserver<PetBreedListResponse> responseObserver) {
        PetBreedListOutput output =
                petBreedServiceBlockingStub.getPetBreedList(Empty.newBuilder().build());
        List<PetBreedModel> petBreedList = output.getPetBreedListList();
        responseObserver.onNext(PetBreedListResponse.newBuilder()
                .addAllPetBreedList(petBreedList)
                .build());
        responseObserver.onCompleted();
    }
}
