package com.moego.api.v3.customer.controller;

import com.google.protobuf.Empty;
import com.moego.api.v3.customer.convertor.PetConvertor;
import com.moego.idl.api.customer.v1.AddPetRequest;
import com.moego.idl.api.customer.v1.GetPetListResponse;
import com.moego.idl.api.customer.v1.GetPetResponse;
import com.moego.idl.api.customer.v1.PetServiceGrpc.PetServiceImplBase;
import com.moego.idl.api.customer.v1.UpdatePetRequest;
import com.moego.idl.service.customer.v1.AddPetInput;
import com.moego.idl.service.customer.v1.DeletePetInput;
import com.moego.idl.service.customer.v1.GetPetInput;
import com.moego.idl.service.customer.v1.GetPetListInput;
import com.moego.idl.service.customer.v1.GetPetListOutput;
import com.moego.idl.service.customer.v1.GetPetOutput;
import com.moego.idl.service.customer.v1.PetServiceGrpc.PetServiceBlockingStub;
import com.moego.idl.service.customer.v1.UpdatePetInput;
import com.moego.idl.utils.v1.Id;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@AllArgsConstructor
public class PetController extends PetServiceImplBase {

    private final PetServiceBlockingStub petServiceBlockingStub;

    @Auth(AuthType.ACCOUNT)
    @Override
    public void getPetList(Empty request, StreamObserver<GetPetListResponse> responseObserver) {
        GetPetListInput input = GetPetListInput.newBuilder()
                .setAccountId(AuthContext.get().accountId())
                .build();
        GetPetListOutput output = petServiceBlockingStub.getPetList(input);

        List<GetPetResponse> petList = output.getPetListList().stream()
                .map(petDTO -> GetPetResponse.newBuilder()
                        .setPetModel(petDTO.getPetModel())
                        .addAllVaccineList(petDTO.getVaccineListList())
                        .build())
                .toList();

        responseObserver.onNext(
                GetPetListResponse.newBuilder().addAllPetList(petList).build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.ACCOUNT)
    @Override
    public void addPet(AddPetRequest request, StreamObserver<Id> responseObserver) {
        AddPetInput input = AddPetInput.newBuilder()
                .setPet(PetConvertor.INSTANCE.toInput(request.getPet()))
                .setAccountId(AuthContext.get().accountId())
                .addAllVaccineList(PetConvertor.INSTANCE.toInput(request.getVaccineListList()))
                .build();

        responseObserver.onNext(petServiceBlockingStub.addPet(input));
        responseObserver.onCompleted();
    }

    @Auth(AuthType.ACCOUNT)
    @Override
    public void updatePet(UpdatePetRequest request, StreamObserver<Empty> responseObserver) {
        UpdatePetInput input = UpdatePetInput.newBuilder()
                .setId(request.getId())
                .setAccountId(AuthContext.get().accountId())
                .setPet(PetConvertor.INSTANCE.toInput(request.getPet()))
                .addAllVaccineList(PetConvertor.INSTANCE.toInput(request.getVaccineListList()))
                .build();

        responseObserver.onNext(petServiceBlockingStub.updatePet(input));
        responseObserver.onCompleted();
    }

    @Auth(AuthType.ACCOUNT)
    @Override
    public void deletePet(Id request, StreamObserver<Empty> responseObserver) {
        DeletePetInput input = DeletePetInput.newBuilder()
                .setAccountId(AuthContext.get().accountId())
                .setPetId(request.getId())
                .build();

        responseObserver.onNext(petServiceBlockingStub.deletePet(input));
        responseObserver.onCompleted();
    }

    @Auth(AuthType.ACCOUNT)
    @Override
    public void getPet(Id request, StreamObserver<GetPetResponse> responseObserver) {
        GetPetInput input = GetPetInput.newBuilder()
                .setAccountId(AuthContext.get().accountId())
                .setPetId(request.getId())
                .build();

        GetPetOutput output = petServiceBlockingStub.getPet(input);

        responseObserver.onNext(GetPetResponse.newBuilder()
                .setPetModel(output.getPetModel())
                .addAllVaccineList(output.getVaccineListList())
                .build());
        responseObserver.onCompleted();
    }
}
