package com.moego.api.v3.customer.controller;

import com.google.protobuf.Empty;
import com.moego.api.v3.customer.convertor.LinkBusinessConvertor;
import com.moego.idl.api.customer.v1.ClientPortalLinkBusinessListResponse;
import com.moego.idl.api.customer.v1.ClientPortalLinkBusinessResponse;
import com.moego.idl.api.customer.v1.LinkBusinessServiceGrpc.LinkBusinessServiceImplBase;
import com.moego.idl.api.customer.v1.LinkCustomerRequest;
import com.moego.idl.service.customer.v1.ClientPortalLinkBusinessListOutput;
import com.moego.idl.service.customer.v1.GetLinkBusinessInput;
import com.moego.idl.service.customer.v1.LinkBusinessServiceGrpc.LinkBusinessServiceBlockingStub;
import com.moego.idl.service.customer.v1.LinkCustomerInput;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@AllArgsConstructor
public class LinkBusinessController extends LinkBusinessServiceImplBase {

    private final LinkBusinessServiceBlockingStub linkBusinessServiceBlockingStub;

    @Auth(AuthType.ACCOUNT)
    @Override
    public void linkCustomerAndPet(LinkCustomerRequest request, StreamObserver<Empty> responseObserver) {
        LinkCustomerInput input = LinkCustomerInput.newBuilder()
                .setCustomerId(request.getCustomerId())
                .setCustomerCode(request.getCustomerCode())
                .setAccountId(AuthContext.get().accountId())
                .build();

        responseObserver.onNext(linkBusinessServiceBlockingStub.linkCustomerAndPet(input));
        responseObserver.onCompleted();
    }

    @Auth(AuthType.ACCOUNT)
    @Override
    public void getLinkBusiness(Empty request, StreamObserver<ClientPortalLinkBusinessListResponse> responseObserver) {
        GetLinkBusinessInput getLinkBusinessInput = GetLinkBusinessInput.newBuilder()
                .setAccountId(AuthContext.get().accountId())
                .build();
        ClientPortalLinkBusinessListOutput businessListOutput =
                linkBusinessServiceBlockingStub.getLinkBusiness(getLinkBusinessInput);
        List<ClientPortalLinkBusinessResponse> businessResponseList =
                LinkBusinessConvertor.INSTANCE.toResponse(businessListOutput.getLinkBusinessList());
        responseObserver.onNext(ClientPortalLinkBusinessListResponse.newBuilder()
                .addAllLinkBusiness(businessResponseList)
                .build());
        responseObserver.onCompleted();
    }
}
