package com.moego.api.v3.customer.convertor;

import com.moego.idl.api.customer.v1.PetRequest;
import com.moego.idl.api.customer.v1.PetVaccineRequest;
import com.moego.idl.service.customer.v1.PetInput;
import com.moego.idl.service.customer.v1.PetVaccineInput;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PetConvertor {
    PetConvertor INSTANCE = Mappers.getMapper(PetConvertor.class);

    PetInput toInput(PetRequest request);

    List<PetVaccineInput> toInput(List<PetVaccineRequest> request);

    default PetVaccineInput toInput(PetVaccineRequest petVaccineRequest) {
        if (petVaccineRequest == null) {
            return null;
        }

        PetVaccineInput.Builder petVaccineInput = PetVaccineInput.newBuilder();

        petVaccineInput.setVaccineMetadataId(petVaccineRequest.getVaccineMetadataId());
        petVaccineInput.setExpirationDate(petVaccineRequest.getExpirationDate());
        petVaccineInput.addAllDocumentUrlList(petVaccineRequest.getDocumentUrlListList());

        return petVaccineInput.build();
    }
}
