package com.moego.api.v3.customer.controller;

import com.google.protobuf.Empty;
import com.moego.api.v3.customer.convertor.AddressConvertor;
import com.moego.idl.api.customer.v1.AddAddressRequest;
import com.moego.idl.api.customer.v1.AddressListResponse;
import com.moego.idl.api.customer.v1.AddressServiceGrpc.AddressServiceImplBase;
import com.moego.idl.api.customer.v1.UpdateAddressRequest;
import com.moego.idl.models.customer.v1.CustomerAddressModel;
import com.moego.idl.service.customer.v1.AddAddressInput;
import com.moego.idl.service.customer.v1.AddressListOutput;
import com.moego.idl.service.customer.v1.AddressServiceGrpc.AddressServiceBlockingStub;
import com.moego.idl.service.customer.v1.DeleteAddressInput;
import com.moego.idl.service.customer.v1.GetAddressListInput;
import com.moego.idl.service.customer.v1.UpdateAddressInput;
import com.moego.idl.utils.v1.Id;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@AllArgsConstructor
public class AddressController extends AddressServiceImplBase {

    private final AddressServiceBlockingStub addressServiceBlockingStub;

    @Auth(AuthType.ACCOUNT)
    @Override
    public void getAddressList(Empty request, StreamObserver<AddressListResponse> responseObserver) {
        GetAddressListInput input = GetAddressListInput.newBuilder()
                .setAccountId(AuthContext.get().accountId())
                .build();
        AddressListOutput output = addressServiceBlockingStub.getAddressList(input);
        List<CustomerAddressModel> addressList = output.getAddressListList();

        responseObserver.onNext(
                AddressListResponse.newBuilder().addAllAddressList(addressList).build());
        responseObserver.onCompleted();
    }

    @Auth(AuthType.ACCOUNT)
    @Override
    public void addAddress(AddAddressRequest request, StreamObserver<Id> responseObserver) {
        AddAddressInput input = AddAddressInput.newBuilder()
                .setAddress(AddressConvertor.INSTANCE.toInput(request.getAddress()))
                .setAccountId(AuthContext.get().accountId())
                .build();

        responseObserver.onNext(addressServiceBlockingStub.addAddress(input));
        responseObserver.onCompleted();
    }

    @Auth(AuthType.ACCOUNT)
    @Override
    public void updateAddress(UpdateAddressRequest request, StreamObserver<Empty> responseObserver) {
        UpdateAddressInput input = UpdateAddressInput.newBuilder()
                .setId(request.getId())
                .setAddress(AddressConvertor.INSTANCE.toInput(request.getAddress()))
                .setAccountId(AuthContext.get().accountId())
                .build();

        responseObserver.onNext(addressServiceBlockingStub.updateAddress(input));
        responseObserver.onCompleted();
    }

    @Auth(AuthType.ACCOUNT)
    @Override
    public void deleteAddress(Id request, StreamObserver<Empty> responseObserver) {
        DeleteAddressInput input = DeleteAddressInput.newBuilder()
                .setAddressId(request.getId())
                .setAccountId(AuthContext.get().accountId())
                .build();

        responseObserver.onNext(addressServiceBlockingStub.deleteAddress(input));
        responseObserver.onCompleted();
    }
}
