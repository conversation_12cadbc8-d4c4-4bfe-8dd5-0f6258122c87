package com.moego.api.v3.customer.controller;

import com.moego.idl.api.customer.v1.CustomerPackageServiceGrpc.CustomerPackageServiceImplBase;
import com.moego.idl.api.customer.v1.UpdateCustomerPackageParams;
import com.moego.idl.api.customer.v1.UpdateCustomerPackageResponse;
import com.moego.idl.api.customer.v1.UpdateCustomerPackageServiceParams;
import com.moego.idl.api.customer.v1.UpdateCustomerPackageServiceResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.permission.Permission;
import com.moego.lib.permission.PermissionEnums;
import com.moego.server.grooming.api.IGroomingPackageService;
import com.moego.server.grooming.params.UpdatePackageParams;
import com.moego.server.grooming.params.UpdatePackageServiceParams;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@Slf4j
@GrpcService
@AllArgsConstructor
public class PackageController extends CustomerPackageServiceImplBase {

    private final IGroomingPackageService groomingPackageService;

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.EDIT_CLIENT_PACKAGE})
    public void updateCustomerPackage(
            UpdateCustomerPackageParams request, StreamObserver<UpdateCustomerPackageResponse> responseObserver) {
        groomingPackageService.updatePackage(UpdatePackageParams.builder()
                .packageId(request.getPackageId())
                .businessId(AuthContext.get().businessId())
                .staffId(AuthContext.get().staffId())
                .extendValidityDay(request.getExtendValidityDay())
                .build());

        responseObserver.onNext(UpdateCustomerPackageResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    @Permission(permissionsNeedToCheck = {PermissionEnums.EDIT_CLIENT_PACKAGE})
    public void updateCustomerPackageService(
            UpdateCustomerPackageServiceParams request,
            StreamObserver<UpdateCustomerPackageServiceResponse> responseObserver) {

        groomingPackageService.updatePackageService(UpdatePackageServiceParams.builder()
                .packageServiceId(request.getPackageServiceId())
                .businessId(AuthContext.get().businessId())
                .staffId(AuthContext.get().staffId())
                .remainingQuantity(request.getRemainingQuantity())
                .build());
        responseObserver.onNext(UpdateCustomerPackageServiceResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
