package com.moego.api.v3.customer.convertor;

import com.moego.idl.api.customer.v1.AddressRequest;
import com.moego.idl.service.customer.v1.AddressInput;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface AddressConvertor {
    AddressConvertor INSTANCE = Mappers.getMapper(AddressConvertor.class);

    AddressInput toInput(AddressRequest request);
}
