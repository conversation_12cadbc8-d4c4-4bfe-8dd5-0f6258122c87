/*
 * @since 2023-07-18 17:56:54
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.metadata.controller;

import com.moego.api.v3.metadata.converter.MetadataConverter;
import com.moego.api.v3.metadata.service.MetadataService;
import com.moego.idl.api.metadata.v2.DescribeMetadataParams;
import com.moego.idl.api.metadata.v2.DescribeMetadataResult;
import com.moego.idl.api.metadata.v2.MetadataServiceGrpc.MetadataServiceImplBase;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class MetadataController extends MetadataServiceImplBase {

    private final MetadataConverter metadataConverter;
    private final MetadataService metadataService;
    private final MetadataServiceGrpc.MetadataServiceBlockingStub metadataServiceBlockingStub;

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void describeMetadata(
            DescribeMetadataParams request, StreamObserver<DescribeMetadataResult> responseObserver) {
        var owners = metadataService.getOwners(AuthContext.get());
        var vo = metadataConverter.describeMetadataRequest(request, owners);
        var dto = metadataServiceBlockingStub.describeMetadata(vo);
        var result = metadataConverter.describeMetadataResult(dto);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }
}
