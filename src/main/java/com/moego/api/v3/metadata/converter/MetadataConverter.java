/*
 * @since 2023-04-12 10:55:30
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.metadata.converter;

import com.moego.idl.api.metadata.v1.DescribeMetadataParams;
import com.moego.idl.api.metadata.v1.DescribeMetadataResult;
import com.moego.idl.api.metadata.v1.UpdateMetadataParams;
import com.moego.idl.service.metadata.v1.DescribeMetadataRequest;
import com.moego.idl.service.metadata.v1.DescribeMetadataResponse;
import com.moego.idl.service.metadata.v1.ExtractValuesResponse;
import java.util.Map;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingConstants;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;

@Mapper(
        componentModel = MappingConstants.ComponentModel.SPRING,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedSourcePolicy = ReportingPolicy.WARN,
        unmappedTargetPolicy = ReportingPolicy.WARN)
public interface MetadataConverter {
    DescribeMetadataParams toDescribeMetadataParams(String group, String key);

    @Mapping(source = "values", target = "values")
    ExtractValuesResponse toExtractValuesResponse(Integer dummy, Map<String, String> values);

    @Mapping(source = "values", target = "values")
    DescribeMetadataResult toDescribeMetadataResult(Integer dummy, Map<String, String> values);

    UpdateMetadataParams toUpdateMetadataParams(String key, String value);

    @Mapping(target = "keyName", source = "request.key")
    DescribeMetadataRequest describeMetadataRequest(
            com.moego.idl.api.metadata.v2.DescribeMetadataParams request, Map<Long, Long> owners);

    com.moego.idl.api.metadata.v2.DescribeMetadataResult describeMetadataResult(DescribeMetadataResponse dto);
}
