/*
 * @since 2023-04-08 18:52:48
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.metadata.service;

import static com.moego.api.v3.shared.util.ConditionUtil.ifNotNull;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_ACCOUNT_VALUE;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_BUSINESS_VALUE;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_COMPANY_VALUE;
import static com.moego.idl.models.metadata.v1.OwnerType.OWNER_TYPE_STAFF_VALUE;
import static com.moego.idl.models.metadata.v1.PermissionLevel.PERMISSION_LEVEL_OWNER;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.metadata.v1.KeyModel;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.business.client.IBusinessStaffClient;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@AllArgsConstructor
public class MetadataService {

    private final IBusinessBusinessClient iBusinessBusinessClient;
    private final IBusinessStaffClient iBusinessStaffClient;

    public Map<Long, Long> getOwners(AuthContext auth) {
        var map = new HashMap<Long, Long>();
        ifNotNull(auth.businessId(), v -> map.put((long) OWNER_TYPE_BUSINESS_VALUE, v));
        ifNotNull(auth.staffId(), v -> map.put((long) OWNER_TYPE_STAFF_VALUE, v));
        ifNotNull(auth.accountId(), v -> map.put((long) OWNER_TYPE_ACCOUNT_VALUE, v));
        ifNotNull(
                auth.businessId(),
                v -> map.put(
                        (long) OWNER_TYPE_COMPANY_VALUE,
                        iBusinessBusinessClient
                                .getCompanyByBusinessId(v.intValue())
                                .getId()
                                .longValue()));
        return map;
    }

    public long extractOwnerId(AuthContext authContext, KeyModel keyModel) {
        Long ownerId =
                switch (keyModel.getOwnerType()) {
                    case OWNER_TYPE_BUSINESS -> {
                        // is a business
                        authContext.checkValid(AuthType.BUSINESS);
                        yield switch (keyModel.getPermissionLevel()) {
                            case PERMISSION_LEVEL_BUSINESS_ANY_STAFF -> authContext.businessId();
                            case PERMISSION_LEVEL_OWNER -> Objects.equals(
                                            iBusinessStaffClient.getOwnerStaffId(authContext.getBusinessId()),
                                            authContext.getStaffId())
                                    ? authContext.businessId()
                                    : null;
                            default -> null;
                        };
                    }
                    case OWNER_TYPE_STAFF -> {
                        // is a staff, and allow owner modify it
                        authContext.checkValid(AuthType.BUSINESS);
                        yield keyModel.getPermissionLevel().equals(PERMISSION_LEVEL_OWNER)
                                ? authContext.staffId()
                                : null;
                    }
                    case OWNER_TYPE_ACCOUNT -> {
                        // is a account
                        authContext.checkValid(AuthType.ACCOUNT);
                        yield keyModel.getPermissionLevel().equals(PERMISSION_LEVEL_OWNER)
                                ? authContext.accountId()
                                : null;
                    }
                    case OWNER_TYPE_COMPANY -> {
                        authContext.checkValid(AuthType.BUSINESS);
                        var company = iBusinessBusinessClient.getCompanyByBusinessId(
                                authContext.businessId().intValue());
                        var companyId = company.getId().longValue();
                        yield switch (keyModel.getPermissionLevel()) {
                            case PERMISSION_LEVEL_COMPANY_ANY_BUSINESS_OWNER -> iBusinessStaffClient
                                            .getOwnerStaffId(
                                                    authContext.businessId().intValue())
                                            .equals(authContext.staffId().intValue())
                                    ? companyId
                                    : null;
                            case PERMISSION_LEVEL_COMPANY_ANY_STAFF -> companyId;
                            case PERMISSION_LEVEL_OWNER -> Objects.equals(
                                            company.getAccountId(), authContext.getAccountId())
                                    ? companyId
                                    : null;
                            default -> null;
                        };
                    }
                        // system is forbidden
                    case OWNER_TYPE_SYSTEM -> null;
                    default -> null;
                };
        if (ownerId == null) {
            throw bizException(Code.CODE_UNAUTHORIZED_ERROR);
        }
        return ownerId;
    }
}
