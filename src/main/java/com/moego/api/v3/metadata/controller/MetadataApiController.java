/*
 * @since 2023-04-07 18:32:53
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.api.v3.metadata.controller;

import static com.moego.api.v3.shared.util.ConditionUtil.ifNotNull;

import com.google.protobuf.Empty;
import com.moego.api.v3.metadata.service.MetadataService;
import com.moego.idl.api.metadata.v1.DescribeMetadataParams;
import com.moego.idl.api.metadata.v1.DescribeMetadataResult;
import com.moego.idl.api.metadata.v1.MetadataApiServiceGrpc.MetadataApiServiceImplBase;
import com.moego.idl.api.metadata.v1.UpdateMetadataParams;
import com.moego.idl.service.metadata.v1.ExtractValuesRequest;
import com.moego.idl.service.metadata.v1.GetKeyRequest;
import com.moego.idl.service.metadata.v1.MetadataServiceGrpc.MetadataServiceBlockingStub;
import com.moego.idl.service.metadata.v1.UpdateValueRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class MetadataApiController extends MetadataApiServiceImplBase {

    private final MetadataService metadataService;
    private final MetadataServiceBlockingStub metadataServiceBlockingStub;

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void describeMetadata(
            DescribeMetadataParams request, StreamObserver<DescribeMetadataResult> responseObserver) {
        var builder = ExtractValuesRequest.newBuilder();
        if (request.hasGroup()) {
            builder.setGroup(request.getGroup());
        } else if (request.hasKey()) {
            builder.setKeyName(request.getKey());
        }
        builder.putAllOwners(metadataService.getOwners(AuthContext.get()));
        var res = metadataServiceBlockingStub.extractValues(builder.build());
        responseObserver.onNext(DescribeMetadataResult.newBuilder()
                .putAllValues(res.getValuesMap())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void updateMetadata(UpdateMetadataParams request, StreamObserver<Empty> responseObserver) {
        var auth = AuthContext.get();
        var input = GetKeyRequest.newBuilder().setName(request.getKey()).build();
        var key = metadataServiceBlockingStub.getKey(input).getKey();
        var ownerId = metadataService.extractOwnerId(auth, key);
        var builder = UpdateValueRequest.newBuilder().setKeyId(key.getId()).setOwnerId(ownerId);
        ifNotNull(auth.accountId(), builder::setOperatorId);
        ifNotNull(request.hasValue(), request::getValue, builder::setValue);
        metadataServiceBlockingStub.updateValue(builder.build());
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
