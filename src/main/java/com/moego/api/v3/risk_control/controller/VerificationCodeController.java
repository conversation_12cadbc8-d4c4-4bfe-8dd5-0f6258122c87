package com.moego.api.v3.risk_control.controller;

import com.moego.api.v3.account.helper.RiskControlHelper;
import com.moego.common.utils.PhoneUtil;
import com.moego.idl.api.risk_control.v1.SendVerificationCodeRequest;
import com.moego.idl.api.risk_control.v1.SendVerificationCodeResponse;
import com.moego.idl.api.risk_control.v1.VerificationCodeServiceGrpc;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.risk_control.v1.VerificationCodeMethod;
import com.moego.idl.service.risk_control.v1.CreateCodeResponse;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.message.client.IMoeGoVerificationCodeClient;
import com.moego.server.message.params.VerificationCodeParams;
import io.grpc.stub.StreamObserver;
import java.util.Set;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @since 2023/11/27
 */
@GrpcService
@RequiredArgsConstructor
public class VerificationCodeController extends VerificationCodeServiceGrpc.VerificationCodeServiceImplBase {

    private final RiskControlHelper riskControlHelper;
    private final IMoeGoVerificationCodeClient verificationCodeClient;
    private static final Set<VerificationCodeMethod> ANONYMOUS_METHODS = Set.of(
            VerificationCodeMethod.VERIFICATION_CODE_METHOD_REGISTER,
            VerificationCodeMethod.VERIFICATION_CODE_METHOD_LOGIN);

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void sendVerificationCode(
            SendVerificationCodeRequest request, StreamObserver<SendVerificationCodeResponse> responseObserver) {
        // 0. check session
        if (!ANONYMOUS_METHODS.contains(request.getIdentifier().getMethod())) {
            AuthContext.get().checkValid(AuthType.ACCOUNT);
        }
        // 1. generate verification code
        CreateCodeResponse response = riskControlHelper.createVerificationCode(request.getIdentifier());
        // 2. send verification code
        VerificationCodeParams params = new VerificationCodeParams()
                .setPhoneNumber(request.getIdentifier().getPhoneNumber())
                .setEmail(request.getIdentifier().getEmail())
                .setVerificationCode(response.getVerification().getCode())
                .setExpirationSeconds(response.getExpirationSeconds());
        if (request.getIdentifier().hasPhoneNumber()) {
            String countryCode =
                    PhoneUtil.getISOTwoLetterRegionCode(request.getIdentifier().getPhoneNumber());
            var twilioNumberDTO = verificationCodeClient.getMoeGoPhoneNumberRandomly(countryCode);
            if (twilioNumberDTO == null) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PHONE_INVALID, "Failed to send code. Unsupported region for your phone number.");
            }
            params.setFromTwilioNumber(twilioNumberDTO);
        }

        verificationCodeClient.sendVerificationCode(params);
        responseObserver.onNext(SendVerificationCodeResponse.newBuilder()
                .setToken(response.getVerification().getToken())
                .build());
        responseObserver.onCompleted();
    }
}
