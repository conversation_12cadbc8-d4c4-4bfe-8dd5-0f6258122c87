package com.moego.api.v3.risk_control.controller;

import com.google.protobuf.util.Structs;
import com.google.protobuf.util.Values;
import com.moego.common.utils.RedisUtil;
import com.moego.idl.api.risk_control.v1.RecaptchaChallengeRequest;
import com.moego.idl.api.risk_control.v1.RecaptchaChallengeResponse;
import com.moego.idl.api.risk_control.v1.RecaptchaServiceGrpc.RecaptchaServiceImplBase;
import com.moego.idl.models.account.v1.SessionModel;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.service.account.v1.SessionServiceGrpc.SessionServiceBlockingStub;
import com.moego.idl.service.account.v1.UpdateSessionRequest;
import com.moego.idl.service.risk_control.v1.RecaptchaChallengeInput;
import com.moego.idl.service.risk_control.v1.RecaptchaChallengeOutput;
import com.moego.idl.service.risk_control.v1.RecaptchaServiceGrpc.RecaptchaServiceBlockingStub;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.risk.control.config.RiskControlConstant;
import io.grpc.stub.StreamObserver;
import java.util.Objects;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/9/5
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class RecaptchaController extends RecaptchaServiceImplBase {

    private final RecaptchaServiceBlockingStub recaptchaServiceBlockingStub;
    private final SessionServiceBlockingStub sessionServiceBlockingStub;
    private final RedisUtil redisUtil;

    @Override
    @Auth(AuthType.ANONYMOUS)
    public void challenge(
            RecaptchaChallengeRequest request, StreamObserver<RecaptchaChallengeResponse> responseObserver) {
        String enable = redisUtil.get(RiskControlConstant.RISK_CONTROL_SWITCH_KEY);
        if (!StringUtils.hasText(enable) || !Boolean.parseBoolean(enable)) {
            responseObserver.onNext(RecaptchaChallengeResponse.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        if (Objects.isNull(AuthContext.get().sessionId())) {
            throw ExceptionUtil.bizException(Code.CODE_UNAUTHORIZED_ERROR);
        }
        RecaptchaChallengeInput input = RecaptchaChallengeInput.newBuilder()
                .setRecaptchaDef(request.getRecaptcha())
                .setSessionId(AuthContext.get().sessionId())
                .build();
        RecaptchaChallengeOutput output = recaptchaServiceBlockingStub.challenge(input);
        // 保存 risk control token 到 session 里，用于对应 action 做验证
        UpdateSessionRequest updateSessionRequest = UpdateSessionRequest.newBuilder()
                .setId(AuthContext.get().sessionId())
                .setSessionData(
                        Structs.of(RiskControlConstant.RISK_CONTROL_TOKEN, Values.of(output.getRiskControlToken())))
                .build();
        SessionModel sessionModel = sessionServiceBlockingStub.updateSession(updateSessionRequest);

        log.debug(
                "save risk control token to session, sessionId: [{}], before: {}, after: {}",
                AuthContext.get().sessionId(),
                AuthContext.get().sessionData(),
                sessionModel.getSessionData());
        responseObserver.onNext(RecaptchaChallengeResponse.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
