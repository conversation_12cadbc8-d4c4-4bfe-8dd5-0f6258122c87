package com.moego.api.v3.payment.service;

import com.moego.idl.api.payment.v2.ConfirmOnboardParams;
import com.moego.idl.api.payment.v2.ConfirmOnboardResult;
import com.moego.idl.api.payment.v2.GetChannelAccountDetailParams;
import com.moego.idl.api.payment.v2.GetChannelAccountDetailResult;
import com.moego.idl.api.payment.v2.GetOnboardParams;
import com.moego.idl.api.payment.v2.GetOnboardResult;
import com.moego.idl.api.payment.v2.ProceedOnboardParams;
import com.moego.idl.api.payment.v2.ProceedOnboardResult;
import com.moego.idl.service.payment.v2.ConfirmOnboardRequest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class PaymentOnboardService {
    private final com.moego.idl.service.payment.v2.PaymentOnboardServiceGrpc.PaymentOnboardServiceBlockingStub
            onboardServiceBlockingStub;

    public GetOnboardResult getOnboard(Long companyId, Long businessId, GetOnboardParams request) {
        var serviceRequest = com.moego.idl.service.payment.v2.GetOnboardRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setChannelType(request.getChannelType())
                .build();
        var serviceResponse = onboardServiceBlockingStub.getOnboard(serviceRequest);
        return GetOnboardResult.newBuilder()
                .setStatus(serviceResponse.getStatus())
                .addAllSteps(serviceResponse.getStepsList())
                .setCurrentStep(serviceResponse.getCurrentStep())
                .build();
    }

    public ProceedOnboardResult proceedOnboard(Long companyId, Long businessId, ProceedOnboardParams request) {
        var serviceRequestBuilder = com.moego.idl.service.payment.v2.ProceedOnboardRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setChannelType(request.getChannelType());
        if (request.hasReturnUrl()) {
            serviceRequestBuilder.setReturnUrl(request.getReturnUrl());
        }
        if (request.getParamsCase() == ProceedOnboardParams.ParamsCase.ADYEN_PRE_CONFIG) {
            serviceRequestBuilder.setAdyenPreConfig(request.getAdyenPreConfig());
        }
        var serviceResponse = onboardServiceBlockingStub.proceedOnboard(serviceRequestBuilder.build());
        var responseBuilder = ProceedOnboardResult.newBuilder();
        if (serviceResponse.hasUrl()) {
            responseBuilder.setUrl(serviceResponse.getUrl());
        }
        return responseBuilder.build();
    }

    public GetChannelAccountDetailResult getChannelAccountDetail(
            Long companyId, Long businessId, GetChannelAccountDetailParams request) {
        var serviceRequest = com.moego.idl.service.payment.v2.GetChannelAccountDetailRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setChannelType(request.getChannelType())
                .build();
        var serviceResponse = onboardServiceBlockingStub.getChannelAccountDetail(serviceRequest);
        return GetChannelAccountDetailResult.newBuilder()
                .setChannelAccount(serviceResponse.getChannelAccount())
                .addAllVerifications(serviceResponse.getVerificationsList())
                .addAllBankAccounts(serviceResponse.getBankAccountsList())
                .build();
    }

    public ConfirmOnboardResult confirmOnboardFinished(Long companyId, Long businessId, ConfirmOnboardParams request) {
        var serviceRequest = ConfirmOnboardRequest.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setChannelType(request.getChannelType())
                .build();
        onboardServiceBlockingStub.confirmOnboard(serviceRequest);
        return ConfirmOnboardResult.newBuilder().build();
    }
}
