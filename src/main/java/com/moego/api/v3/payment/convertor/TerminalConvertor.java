package com.moego.api.v3.payment.convertor;

import com.moego.idl.models.payment.v2.Terminal;
import com.moego.idl.models.payment.v2.TerminalView;

public final class TerminalConvertor {
    public static TerminalView convertModel2View(Terminal terminal) {
        if (null == terminal) {
            return null;
        }

        return TerminalView.newBuilder()
                .setId(terminal.getId())
                .setTerminalType(terminal.getTerminalType())
                .setChannelType(terminal.getChannelType())
                .setChannelTerminalId(terminal.getChannelTerminalId())
                .setState(terminal.getState())
                .setLastTransactionId(terminal.getLastTransactionId())
                .build();
    }
}
