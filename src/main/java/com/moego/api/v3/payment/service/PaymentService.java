package com.moego.api.v3.payment.service;

import com.moego.api.v3.payment.convertor.RecurringMethodConvertor;
import com.moego.idl.api.payment.v2.AddRecurringPaymentMethodParams;
import com.moego.idl.api.payment.v2.AddRecurringPaymentMethodResult;
import com.moego.idl.api.payment.v2.DeleteRecurringPaymentMethodResult;
import com.moego.idl.api.payment.v2.GetPayDataParams;
import com.moego.idl.api.payment.v2.GetPayDataResult;
import com.moego.idl.api.payment.v2.GetPaymentResult;
import com.moego.idl.api.payment.v2.GetPaymentSettingResult;
import com.moego.idl.api.payment.v2.GetPaymentVersionParams;
import com.moego.idl.api.payment.v2.GetPaymentVersionResult;
import com.moego.idl.api.payment.v2.ListRecurringPaymentMethodsResult;
import com.moego.idl.api.payment.v2.SetRecurringPaymentMethodPrimaryResult;
import com.moego.idl.api.payment.v2.SubmitActionDetailParams;
import com.moego.idl.api.payment.v2.SubmitActionDetailResult;
import com.moego.idl.api.payment.v2.UpdatePaymentSettingParams;
import com.moego.idl.api.payment.v2.UpdatePaymentSettingResult;
import com.moego.idl.models.payment.v2.EntityType;
import com.moego.idl.models.payment.v2.User;
import com.moego.idl.service.payment.v2.AddRecurringPaymentMethodRequest;
import com.moego.idl.service.payment.v2.AddRecurringPaymentMethodResponse;
import com.moego.idl.service.payment.v2.DeleteRecurringPaymentMethodRequest;
import com.moego.idl.service.payment.v2.GetPayDataRequest;
import com.moego.idl.service.payment.v2.GetPayDataResponse;
import com.moego.idl.service.payment.v2.GetPaymentSettingRequest;
import com.moego.idl.service.payment.v2.GetPaymentVersionRequest;
import com.moego.idl.service.payment.v2.GetPaymentViewRequest;
import com.moego.idl.service.payment.v2.GetPaymentViewResponse;
import com.moego.idl.service.payment.v2.ListRecurringPaymentMethodsRequest;
import com.moego.idl.service.payment.v2.ListRecurringPaymentMethodsResponse;
import com.moego.idl.service.payment.v2.PaymentServiceGrpc;
import com.moego.idl.service.payment.v2.SetRecurringPaymentMethodPrimaryRequest;
import com.moego.idl.service.payment.v2.SetRecurringPaymentMethodPrimaryResponse;
import com.moego.idl.service.payment.v2.SubmitActionDetailRequest;
import com.moego.idl.service.payment.v2.SubmitActionDetailResponse;
import com.moego.idl.service.payment.v2.UpdatePaymentSettingRequest;
import com.moego.server.grooming.api.IGroomingInvoiceService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service("paymentV3Service")
@Slf4j
@RequiredArgsConstructor
public class PaymentService {
    private final PaymentServiceGrpc.PaymentServiceBlockingStub paymentServiceBlockingStub;
    private final IGroomingInvoiceService iGroomingInvoiceService;

    public GetPaymentVersionResult getPaymentVersion(Long businessId, GetPaymentVersionParams request) {
        var response = paymentServiceBlockingStub.getPaymentVersion(GetPaymentVersionRequest.newBuilder()
                .setPayee(User.newBuilder()
                        .setEntityType(EntityType.BUSINESS)
                        .setEntityId(businessId)
                        .build())
                .build());
        return GetPaymentVersionResult.newBuilder()
                .setPaymentVersion(response.getPaymentVersion())
                .setChannelType(response.getChannelType())
                .build();
    }

    public GetPayDataResult getPayData(Long businessId, GetPayDataParams request) {
        var response = paymentServiceBlockingStub.getPayData(GetPayDataRequest.newBuilder()
                .setBusinessId(businessId)
                .setChannelType(request.getChannelType())
                .build());

        var resultBuilder = GetPayDataResult.newBuilder().setChannelType(response.getChannelType());
        if (response.getDataCase() == GetPayDataResponse.DataCase.ADYEN_DATA) {
            resultBuilder.setAdyenData(GetPayDataResult.AdyenData.newBuilder()
                    .setData(response.getAdyenData().getData())
                    .build());
        }
        return resultBuilder.build();
    }

    public GetPaymentResult getPayment(Long paymentId) {

        GetPaymentViewResponse resp = paymentServiceBlockingStub.getPaymentView(
                GetPaymentViewRequest.newBuilder().setId(paymentId).build());

        return GetPaymentResult.newBuilder().setPayment(resp.getPayment()).build();
    }

    public SubmitActionDetailResult submitPayDetail(SubmitActionDetailParams params) {

        SubmitActionDetailResponse resp =
                paymentServiceBlockingStub.submitActionDetail(SubmitActionDetailRequest.newBuilder()
                        .setChannelType(params.getChannelType())
                        .setActionResult(params.getRawActionResult())
                        .build());
        return SubmitActionDetailResult.newBuilder()
                .setMsg(resp.getMsg())
                .setRawChannelResponse(resp.getChannelResponse())
                .build();
    }

    public ListRecurringPaymentMethodsResult listRecurringPaymentMethods(Long businessId, Long customerId) {
        // TODO: 鉴权：检查 businessId 和 customerId 的归属关系
        ListRecurringPaymentMethodsResponse resp =
                paymentServiceBlockingStub.listRecurringPaymentMethods(ListRecurringPaymentMethodsRequest.newBuilder()
                        .setFilter(ListRecurringPaymentMethodsRequest.Filter.newBuilder()
                                .addUsers(User.newBuilder()
                                        .setEntityType(EntityType.CUSTOMER)
                                        .setEntityId(customerId)
                                        .build())
                                .build())
                        .build());

        return ListRecurringPaymentMethodsResult.newBuilder()
                .addAllRecurringPaymentMethodViews(resp.getPaymentMethodsList().stream()
                        .map(RecurringMethodConvertor::convertModel2View)
                        .toList())
                .build();
    }

    public AddRecurringPaymentMethodResult addRecurringPaymentMethod(
            Long businessId, AddRecurringPaymentMethodParams request) {
        AddRecurringPaymentMethodResponse resp =
                paymentServiceBlockingStub.addRecurringPaymentMethod(AddRecurringPaymentMethodRequest.newBuilder()
                        .setCustomerId(request.getCustomerId())
                        .setPayee(User.newBuilder()
                                .setEntityId(businessId)
                                .setEntityType(EntityType.BUSINESS)
                                .build())
                        .setChannelType(request.getChannelType())
                        .setPaymentMethodType(request.getPaymentMethodType())
                        .setDetail(request.getDetail())
                        .setExtra(request.getExtra())
                        .build());
        return AddRecurringPaymentMethodResult.newBuilder()
                .setRecurringPaymentMethodView(
                        RecurringMethodConvertor.convertModel2View(resp.getRecurringPaymentMethod()))
                .setChannelResponse(resp.getChannelResponse())
                .build();
    }

    // TODO: 鉴权：检查 businessId 和 recurringPaymentMethodId 的归属关系
    public DeleteRecurringPaymentMethodResult deleteRecurringPaymentMethod(
            Long businessId, Long recurringPaymentMethodId) {
        paymentServiceBlockingStub.deleteRecurringPaymentMethod(DeleteRecurringPaymentMethodRequest.newBuilder()
                .setPaymentMethodId(recurringPaymentMethodId)
                .build());
        return DeleteRecurringPaymentMethodResult.newBuilder().build();
    }

    public SetRecurringPaymentMethodPrimaryResult setRecurringPaymentMethodPrimary(
            Long businessId, Long recurringPaymentMethodId) {
        SetRecurringPaymentMethodPrimaryResponse resp = paymentServiceBlockingStub.setRecurringPaymentMethodPrimary(
                SetRecurringPaymentMethodPrimaryRequest.newBuilder()
                        .setPaymentMethodId(recurringPaymentMethodId)
                        .build());
        return SetRecurringPaymentMethodPrimaryResult.newBuilder()
                .setRecurringPaymentMethodView(
                        RecurringMethodConvertor.convertModel2View(resp.getRecurringPaymentMethod()))
                .build();
    }

    public GetPaymentSettingResult getPaymentSetting(Long businessId) {
        var response = paymentServiceBlockingStub.getPaymentSetting(GetPaymentSettingRequest.newBuilder()
                .setUser(User.newBuilder()
                        .setEntityType(EntityType.BUSINESS)
                        .setEntityId(businessId)
                        .build())
                .build());
        return GetPaymentSettingResult.newBuilder()
                .setPaymentSetting(response.getPaymentSetting())
                .build();
    }

    public UpdatePaymentSettingResult updatePaymentSetting(Long businessId, UpdatePaymentSettingParams params) {
        var response = paymentServiceBlockingStub.updatePaymentSetting(UpdatePaymentSettingRequest.newBuilder()
                .setUser(User.newBuilder().setEntityType(EntityType.BUSINESS).setEntityId(businessId))
                .setConvenienceFeeConfig(params.getConvenienceFeeConfig())
                .setTipsConfig(params.getTipsConfig())
                .build());
        return UpdatePaymentSettingResult.newBuilder()
                .setPaymentSetting(response.getPaymentSetting())
                .build();
    }
}
