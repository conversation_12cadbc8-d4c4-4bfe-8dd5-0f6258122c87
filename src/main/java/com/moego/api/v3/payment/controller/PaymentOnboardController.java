package com.moego.api.v3.payment.controller;

import static com.moego.lib.common.auth.AuthType.COMPANY;

import com.moego.api.v3.payment.service.PaymentOnboardService;
import com.moego.idl.api.payment.v2.ConfirmOnboardParams;
import com.moego.idl.api.payment.v2.ConfirmOnboardResult;
import com.moego.idl.api.payment.v2.GetChannelAccountDetailParams;
import com.moego.idl.api.payment.v2.GetChannelAccountDetailResult;
import com.moego.idl.api.payment.v2.GetOnboardParams;
import com.moego.idl.api.payment.v2.GetOnboardResult;
import com.moego.idl.api.payment.v2.PaymentOnboardServiceGrpc;
import com.moego.idl.api.payment.v2.ProceedOnboardParams;
import com.moego.idl.api.payment.v2.ProceedOnboardResult;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@GrpcService
@Slf4j
@AllArgsConstructor
public class PaymentOnboardController extends PaymentOnboardServiceGrpc.PaymentOnboardServiceImplBase {
    private final PaymentOnboardService onboardService;

    @Override
    @Auth(COMPANY)
    public void getOnboard(GetOnboardParams request, StreamObserver<GetOnboardResult> responseObserver) {
        var response = onboardService.getOnboard(
                AuthContext.get().companyId(), AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void proceedOnboard(ProceedOnboardParams request, StreamObserver<ProceedOnboardResult> responseObserver) {
        var response = onboardService.proceedOnboard(
                AuthContext.get().companyId(), AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void getChannelAccountDetail(
            GetChannelAccountDetailParams request, StreamObserver<GetChannelAccountDetailResult> responseObserver) {
        var response = onboardService.getChannelAccountDetail(
                AuthContext.get().companyId(), AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void confirmOnboard(ConfirmOnboardParams request, StreamObserver<ConfirmOnboardResult> responseObserver) {
        var response = onboardService.confirmOnboardFinished(
                AuthContext.get().companyId(), AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }
}
