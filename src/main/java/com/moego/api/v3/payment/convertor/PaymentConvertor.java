package com.moego.api.v3.payment.convertor;

import com.moego.idl.models.payment.v2.PaymentModel;
import com.moego.idl.models.payment.v2.PaymentView;
import com.moego.lib.common.proto.MoneyUtils;
import java.math.BigDecimal;

/**
 * Payment 模型转换器
 */
public final class PaymentConvertor {

    public static PaymentView convertModel2View(PaymentModel paymentModel) {
        if (null == paymentModel) {
            return null;
        }

        BigDecimal refundedAmount =
                MoneyUtils.fromGoogleMoney(paymentModel.getRefund().getRefundAmount());
        BigDecimal paymentAmount = MoneyUtils.fromGoogleMoney(paymentModel.getAmount());
        return PaymentView.newBuilder()
                .setId(paymentModel.getId())
                .setPayer(paymentModel.getPayer())
                .setPayee(paymentModel.getPayee())
                .setExternalType(paymentModel.getExternalType())
                .setExternalId(paymentModel.getExternalId())
                .setChannelType(paymentModel.getChannelType())
                .setChannelPaymentId(paymentModel.getChannelPaymentId())
                .setAmount(paymentModel.getAmount())
                .setTipsAmount(paymentModel.getTipsAmount())
                .setProcessingFee(paymentModel.getProcessingFee())
                .setConvenienceFee(paymentModel.getConvenienceFee())
                .setDiscountAmount(paymentModel.getDiscountAmount())
                .setPaymentType(paymentModel.getPaymentType())
                .setMethodType(paymentModel.getMethodType())
                .setStatus(paymentModel.getStatus())
                .setRefund(paymentModel.getRefund())
                .setModule(paymentModel.getModule())
                .setModuleId(paymentModel.getModuleId())
                .setInvoiceId(paymentModel.getInvoiceId())
                .setRefundableAmount(MoneyUtils.toGoogleMoney(
                        paymentAmount.subtract(refundedAmount),
                        paymentModel.getAmount().getCurrencyCode()))
                .build();
    }
}
