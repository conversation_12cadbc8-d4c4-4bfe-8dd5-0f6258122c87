package com.moego.api.v3.payment.controller;

import static com.moego.lib.common.auth.AuthType.BUSINESS;

import com.moego.api.v3.payment.service.PaymentTerminalService;
import com.moego.idl.api.payment.v2.GetTerminalParams;
import com.moego.idl.api.payment.v2.GetTerminalResult;
import com.moego.idl.api.payment.v2.PaymentTerminalServiceGrpc;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@GrpcService
@Slf4j
@AllArgsConstructor
public class PaymentTerminalController extends PaymentTerminalServiceGrpc.PaymentTerminalServiceImplBase {
    private final PaymentTerminalService paymentTerminalService;

    //    @Override
    //    @Auth(BUSINESS)
    //    public void retrieveSdkData(RetrieveSdkDataParams request, StreamObserver<RetrieveSdkDataResult>
    // responseObserver) {
    //        var response = paymentTerminalService.retrieveSdkData(
    //                AuthContext.get().companyId(), AuthContext.get().businessId(), request);
    //        responseObserver.onNext(response);
    //        responseObserver.onCompleted();
    //    }
    @Override
    @Auth(BUSINESS)
    public void getTerminal(GetTerminalParams params, StreamObserver<GetTerminalResult> responseObserver) {
        var response = paymentTerminalService.getTerminal(AuthContext.get().businessId(), params);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void listTerminals(
            com.moego.idl.api.payment.v2.ListTerminalsParams params,
            StreamObserver<com.moego.idl.api.payment.v2.ListTerminalsResult> responseObserver) {
        var response = paymentTerminalService.listTerminals(AuthContext.get().businessId(), params);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }
}
