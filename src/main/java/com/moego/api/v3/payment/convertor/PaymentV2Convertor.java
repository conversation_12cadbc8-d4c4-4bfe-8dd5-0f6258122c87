package com.moego.api.v3.payment.convertor;

import com.moego.idl.api.payment.v2.ListPaymentByTransactionParams;
import com.moego.idl.api.payment.v2.ListTransactionParams;
import com.moego.idl.service.payment.v2.ListPaymentByTransactionRequest;
import com.moego.idl.service.payment.v2.ListTransactionRequest;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 * @since 2025/4/8
 */
@Mapper(
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface PaymentV2Convertor {
    PaymentV2Convertor INSTANCE = Mappers.getMapper(PaymentV2Convertor.class);

    ListTransactionRequest toRequest(ListTransactionParams params);

    ListPaymentByTransactionRequest toRequest(ListPaymentByTransactionParams params);
}
