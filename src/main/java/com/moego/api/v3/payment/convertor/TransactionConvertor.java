package com.moego.api.v3.payment.convertor;

import com.moego.idl.models.payment.v2.PaymentTransactionModel;
import com.moego.idl.models.payment.v2.PaymentTransactionView;

public final class TransactionConvertor {

    public static PaymentTransactionView convertModel2View(PaymentTransactionModel model) {
        if (model == null) {
            return null;
        }

        return PaymentTransactionView.newBuilder()
                .setId(model.getId())
                .setPayer(model.getPayer())
                .setPayee(model.getPayee())
                .setExternalType(model.getExternalType())
                .setExternalId(model.getExternalId())
                .setChannelType(model.getChannelType())
                .setChannelTransactionId(model.getChannelTransactionId())
                .setAmount(model.getAmount())
                .setTipsAmount(model.getTipsAmount())
                .setProcessingFee(model.getProcessingFee())
                .setConvenienceFee(model.getConvenienceFee())
                .setStatus(model.getStatus())
                .setCreateTime(model.getCreateTime())
                .setUpdateTime(model.getUpdateTime())
                .build();
    }
}
