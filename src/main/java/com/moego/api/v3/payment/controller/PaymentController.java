package com.moego.api.v3.payment.controller;

import static com.moego.lib.common.auth.AuthType.BUSINESS;
import static com.moego.lib.common.auth.AuthType.COMPANY;

import com.moego.api.v3.payment.convertor.PaymentV2Convertor;
import com.moego.api.v3.payment.service.PaymentService;
import com.moego.idl.api.payment.v2.AddRecurringPaymentMethodParams;
import com.moego.idl.api.payment.v2.AddRecurringPaymentMethodResult;
import com.moego.idl.api.payment.v2.DeleteRecurringPaymentMethodParams;
import com.moego.idl.api.payment.v2.DeleteRecurringPaymentMethodResult;
import com.moego.idl.api.payment.v2.ExportTransactionListParams;
import com.moego.idl.api.payment.v2.ExportTransactionListResult;
import com.moego.idl.api.payment.v2.GetPayDataParams;
import com.moego.idl.api.payment.v2.GetPayDataResult;
import com.moego.idl.api.payment.v2.GetPaymentParams;
import com.moego.idl.api.payment.v2.GetPaymentResult;
import com.moego.idl.api.payment.v2.GetPaymentSettingParams;
import com.moego.idl.api.payment.v2.GetPaymentSettingResult;
import com.moego.idl.api.payment.v2.GetPaymentVersionParams;
import com.moego.idl.api.payment.v2.GetPaymentVersionResult;
import com.moego.idl.api.payment.v2.GetTransactionParams;
import com.moego.idl.api.payment.v2.GetTransactionResult;
import com.moego.idl.api.payment.v2.ListPaymentByTransactionParams;
import com.moego.idl.api.payment.v2.ListPaymentByTransactionResult;
import com.moego.idl.api.payment.v2.ListRecurringPaymentMethodsParams;
import com.moego.idl.api.payment.v2.ListRecurringPaymentMethodsResult;
import com.moego.idl.api.payment.v2.ListTransactionParams;
import com.moego.idl.api.payment.v2.ListTransactionResult;
import com.moego.idl.api.payment.v2.PaymentServiceGrpc;
import com.moego.idl.api.payment.v2.SetRecurringPaymentMethodPrimaryParams;
import com.moego.idl.api.payment.v2.SetRecurringPaymentMethodPrimaryResult;
import com.moego.idl.api.payment.v2.SubmitActionDetailParams;
import com.moego.idl.api.payment.v2.SubmitActionDetailResult;
import com.moego.idl.api.payment.v2.UpdatePaymentSettingParams;
import com.moego.idl.api.payment.v2.UpdatePaymentSettingResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.payment.v2.PaymentTransactionView;
import com.moego.idl.models.payment.v2.PaymentView;
import com.moego.idl.service.payment.v2.ExportTransactionListRequest;
import com.moego.idl.service.payment.v2.ExportTransactionListResponse;
import com.moego.idl.service.payment.v2.GetTransactionRequest;
import com.moego.idl.service.payment.v2.GetTransactionResponse;
import com.moego.idl.service.payment.v2.ListPaymentByTransactionRequest;
import com.moego.idl.service.payment.v2.ListPaymentByTransactionResponse;
import com.moego.idl.service.payment.v2.ListTransactionRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import io.grpc.stub.StreamObserver;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;

@GrpcService
@Slf4j
@AllArgsConstructor
public class PaymentController extends PaymentServiceGrpc.PaymentServiceImplBase {
    private final PaymentService paymentService;
    private final com.moego.idl.service.payment.v2.PaymentServiceGrpc.PaymentServiceBlockingStub
            paymentServiceBlockingStub;

    @Override
    @Auth(BUSINESS)
    public void getPaymentVersion(
            GetPaymentVersionParams request, StreamObserver<GetPaymentVersionResult> responseObserver) {
        var response = paymentService.getPaymentVersion(AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void getPayData(GetPayDataParams request, StreamObserver<GetPayDataResult> responseObserver) {
        var response = paymentService.getPayData(AuthContext.get().businessId(), request);
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void getPayment(GetPaymentParams request, StreamObserver<GetPaymentResult> responseObserver) {
        GetPaymentResult result = paymentService.getPayment(request.getPaymentId());
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void submitActionDetail(
            SubmitActionDetailParams request, StreamObserver<SubmitActionDetailResult> responseObserver) {
        SubmitActionDetailResult result = paymentService.submitPayDetail(request);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void listRecurringPaymentMethods(
            ListRecurringPaymentMethodsParams request,
            StreamObserver<ListRecurringPaymentMethodsResult> responseObserver) {
        ListRecurringPaymentMethodsResult result =
                paymentService.listRecurringPaymentMethods(AuthContext.get().businessId(), request.getCustomerId());
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void addRecurringPaymentMethod(
            AddRecurringPaymentMethodParams request, StreamObserver<AddRecurringPaymentMethodResult> responseObserver) {
        AddRecurringPaymentMethodResult result =
                paymentService.addRecurringPaymentMethod(AuthContext.get().businessId(), request);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void deleteRecurringPaymentMethod(
            DeleteRecurringPaymentMethodParams request,
            StreamObserver<DeleteRecurringPaymentMethodResult> responseObserver) {
        var response = paymentService.deleteRecurringPaymentMethod(
                AuthContext.get().businessId(), request.getPaymentMethodId());
        responseObserver.onNext(response);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void setRecurringPaymentMethodPrimary(
            SetRecurringPaymentMethodPrimaryParams request,
            StreamObserver<SetRecurringPaymentMethodPrimaryResult> responseObserver) {
        SetRecurringPaymentMethodPrimaryResult result = paymentService.setRecurringPaymentMethodPrimary(
                AuthContext.get().businessId(), request.getPaymentMethodId());
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void listTransaction(ListTransactionParams params, StreamObserver<ListTransactionResult> responseObserver) {

        var response = paymentServiceBlockingStub.listTransaction(ListTransactionRequest.newBuilder()
                .setPaginationRequest(params.getPagination())
                .setFilter(params.getFilter())
                .addAllOrderBys(params.getOrderBysList())
                .setCompanyId(AuthContext.get().companyId())
                .build());
        responseObserver.onNext(ListTransactionResult.newBuilder()
                .addAllTransactions(response.getTransactionsList())
                .setPagination(response.getPaginationRequest())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void listPaymentByTransaction(
            ListPaymentByTransactionParams params, StreamObserver<ListPaymentByTransactionResult> responseObserver) {
        var response =
                paymentServiceBlockingStub.listPaymentByTransaction(PaymentV2Convertor.INSTANCE.toRequest(params));
        responseObserver.onNext(ListPaymentByTransactionResult.newBuilder()
                .addAllPayments(response.getPaymentsList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void getTransaction(GetTransactionParams request, StreamObserver<GetTransactionResult> responseObserver) {
        GetTransactionResponse response = paymentServiceBlockingStub.getTransaction(GetTransactionRequest.newBuilder()
                .setId(request.getTransactionId())
                .build());

        ListPaymentByTransactionResponse paymentsResp =
                paymentServiceBlockingStub.listPaymentByTransaction(ListPaymentByTransactionRequest.newBuilder()
                        .setTransactionId(request.getTransactionId())
                        .build());
        List<PaymentView> paymentList = paymentsResp.getPaymentsList();
        PaymentTransactionView transaction = response.getTransaction();
        PaymentTransactionView.Builder builder = transaction.toBuilder().addAllPaymentViews(paymentList);
        responseObserver.onNext(GetTransactionResult.newBuilder()
                .setTransactionView(builder.build())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(COMPANY)
    public void exportTransactionList(
            ExportTransactionListParams request, StreamObserver<ExportTransactionListResult> responseObserver) {
        if (AuthContext.get().getCompanyId() == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "company id null");
        }
        if (AuthContext.get().getStaffId() == null) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "staff id null");
        }
        ExportTransactionListResponse resp =
                paymentServiceBlockingStub.exportTransactionList(ExportTransactionListRequest.newBuilder()
                        .setFilter(request.getFilter())
                        .addAllOrderBys(request.getOrderBysList())
                        .setCompanyId(AuthContext.get().getCompanyId())
                        .setStaffId(AuthContext.get().getStaffId())
                        .setBusinessId(request.getBusinessId())
                        .setFileName(request.getFileName())
                        .setFileType(request.getFileType())
                        .build());
        responseObserver.onNext(ExportTransactionListResult.newBuilder()
                .setFileId(resp.getFileId())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void getPaymentSetting(
            GetPaymentSettingParams request, StreamObserver<GetPaymentSettingResult> responseObserver) {
        GetPaymentSettingResult result =
                paymentService.getPaymentSetting(AuthContext.get().businessId());
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }

    @Override
    @Auth(BUSINESS)
    public void updatePaymentSetting(
            UpdatePaymentSettingParams params, StreamObserver<UpdatePaymentSettingResult> responseObserver) {
        UpdatePaymentSettingResult result =
                paymentService.updatePaymentSetting(AuthContext.get().businessId(), params);
        responseObserver.onNext(result);
        responseObserver.onCompleted();
    }
}
