package com.moego.api.v3.payment.service;

import com.moego.api.v3.payment.convertor.TerminalConvertor;
import com.moego.idl.api.payment.v2.GetTerminalParams;
import com.moego.idl.api.payment.v2.GetTerminalResult;
import com.moego.idl.api.payment.v2.ListTerminalsParams;
import com.moego.idl.api.payment.v2.ListTerminalsResult;
import com.moego.idl.models.payment.v2.EntityType;
import com.moego.idl.models.payment.v2.User;
import com.moego.idl.service.payment.v2.GetTerminalRequest;
import com.moego.idl.service.payment.v2.ListTerminalsRequest;
import com.moego.idl.service.payment.v2.PaymentTerminalServiceGrpc;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@RequiredArgsConstructor
public class PaymentTerminalService {
    private final PaymentTerminalServiceGrpc.PaymentTerminalServiceBlockingStub paymentTerminalServiceBlockingStub;

    //    public RetrieveSdkDataResult retrieveSdkData(Long companyId, Long businessId, RetrieveSdkDataParams request) {
    //        var requestBuilder = RetrieveSdkDataRequest.newBuilder()
    //                .setCompanyId(companyId)
    //                .setBusinessId(businessId)
    //                .setChannelType(request.getChannelType());
    //        if (request.getParamsCase() == RetrieveSdkDataParams.ParamsCase.ADYEN_PARAMS) {
    //            requestBuilder.setAdyenParams(RetrieveSdkDataRequest.AdyenParams.newBuilder()
    //                    .setSetupToken(request.getAdyenParams().getSetupToken()));
    //        }
    //        var response = paymentTerminalServiceBlockingStub.retrieveSdkData(requestBuilder.build());
    //        return RetrieveSdkDataResult.newBuilder()
    //                .setAdyenData(RetrieveSdkDataResult.AdyenData.newBuilder()
    //                        .setSdkData(response.getAdyenData().getSdkData()))
    //                .build();
    //    }

    public GetTerminalResult getTerminal(Long businessID, GetTerminalParams params) {
        var request = GetTerminalRequest.newBuilder().setId(params.getId()).build();
        var response = paymentTerminalServiceBlockingStub.getTerminal(request);
        return GetTerminalResult.newBuilder()
                .setTerminal(TerminalConvertor.convertModel2View(response.getTerminal()))
                .build();
    }

    public ListTerminalsResult listTerminals(Long businessID, ListTerminalsParams params) {
        var request = ListTerminalsRequest.newBuilder()
                .setFilter(ListTerminalsRequest.Filter.newBuilder()
                        .setUser(User.newBuilder()
                                .setEntityType(EntityType.BUSINESS)
                                .setEntityId(businessID)
                                .build()))
                .setPagination(params.getPagination())
                .build();
        var response = paymentTerminalServiceBlockingStub.listTerminals(request);
        return ListTerminalsResult.newBuilder()
                .addAllTerminals(response.getTerminalsList().stream()
                        .map(TerminalConvertor::convertModel2View)
                        .toList())
                .setPagination(response.getPagination())
                .build();
    }
}
