package com.moego.api.v3.payment.convertor;

import com.moego.idl.models.payment.v2.RecurringPaymentMethodModel;
import com.moego.idl.models.payment.v2.RecurringPaymentMethodView;

public final class RecurringMethodConvertor {

    public static RecurringPaymentMethodView convertModel2View(
            RecurringPaymentMethodModel recurringPaymentMethodModel) {
        if (null == recurringPaymentMethodModel) {
            return null;
        }

        return RecurringPaymentMethodView.newBuilder()
                .setId(recurringPaymentMethodModel.getId())
                .setChannelType(recurringPaymentMethodModel.getChannelType())
                .setUser(recurringPaymentMethodModel.getUser())
                .setChannelCustomerId(recurringPaymentMethodModel.getChannelCustomerId())
                .setChannelPaymentMethod(recurringPaymentMethodModel.getChannelPaymentMethod())
                .setMethodType(recurringPaymentMethodModel.getMethodType())
                .setIsPrimary(recurringPaymentMethodModel.getIsPrimary())
                .setExtra(recurringPaymentMethodModel.getExtra())
                .setCreatedAt(recurringPaymentMethodModel.getCreatedAt())
                .build();
    }
}
