package com.moego.svc.googlepartner.entity;

import jakarta.annotation.Generated;
import java.util.Date;

/**
 * Database Table Remarks:
 *   Google Reserve 集成
 *
 * This class was generated by MyBatis Generator.
 * This class corresponds to the database table google_reserve_integration
 */
public class GoogleReserveIntegration {
    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.id")
    private Long id;

    /**
     * Database Column Remarks:
     *   business id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.business_id")
    private Integer businessId;

    /**
     * Database Column Remarks:
     *   是否开启 Google Reserve
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.enabled")
    private Boolean enabled;

    /**
     * Database Column Remarks:
     *   create time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.create_time")
    private Date createTime;

    /**
     * Database Column Remarks:
     *   update time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.update_time")
    private Date updateTime;

    /**
     * Database Column Remarks:
     *   集成状态(GoogleReserveIntegrationStatus), 0-configured, 1-unmatched, 2-matched
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.status")
    private Integer status;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.id")
    public Long getId() {
        return id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.id")
    public void setId(Long id) {
        this.id = id;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.business_id")
    public Integer getBusinessId() {
        return businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.business_id")
    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.enabled")
    public Boolean getEnabled() {
        return enabled;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.enabled")
    public void setEnabled(Boolean enabled) {
        this.enabled = enabled;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.create_time")
    public Date getCreateTime() {
        return createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.create_time")
    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.update_time")
    public Date getUpdateTime() {
        return updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.update_time")
    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.status")
    public Integer getStatus() {
        return status;
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.status")
    public void setStatus(Integer status) {
        this.status = status;
    }
}