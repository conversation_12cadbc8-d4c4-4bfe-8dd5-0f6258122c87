package com.moego.svc.googlepartner.service.param;

import jakarta.annotation.Nullable;
import jakarta.validation.constraints.Min;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @see <a href="https://developers.google.com/maps-booking/reference/maps-booking-api/rest/v1alpha/inventory.partners.merchants.status/list#query-parameters">Query Parameters</a>
 */
@Data
@Accessors(chain = true)
public class ListMerchantStatusParam {
    @Min(1)
    private Integer pageSize = 50;

    @Nullable
    private String pageToken;

    @Nullable
    private BookingInventoryStatus bookingInventoryStatusRestrict;

    @Nullable
    private WaitlistInventoryStatus waitlistInventoryStatusRestrict;

    @Nullable
    private GeoMatchingStatus geoMatchRestrict;

    public enum BookingInventoryStatus {
        BOOKING_INVENTORY_STATUS_UNSPECIFIED,
        NO_VALID_FUTURE_INVENTORY,
        HAS_VALID_FUTURE_INVENTORY
    }

    public enum WaitlistInventoryStatus {
        WAITLIST_INVENTORY_STATUS_UNSPECIFIED,
        NO_VALID_WAITLIST_SERVICE,
        HAS_VALID_WAITLIST_SERVICE
    }

    public enum GeoMatchingStatus {
        GEO_MATCHING_STATUS_UNSPECIFIED,
        GEO_UNMATCHED,
        GEO_MATCHED
    }

    /**
     * Copy a new instance of {@link ListMerchantStatusParam} from the given {@link ListMerchantStatusParam}.
     */
    public static ListMerchantStatusParam of(ListMerchantStatusParam param) {
        return new ListMerchantStatusParam()
                .setPageSize(param.getPageSize())
                .setPageToken(param.getPageToken())
                .setBookingInventoryStatusRestrict(param.getBookingInventoryStatusRestrict())
                .setWaitlistInventoryStatusRestrict(param.getWaitlistInventoryStatusRestrict())
                .setGeoMatchRestrict(param.getGeoMatchRestrict());
    }
}
