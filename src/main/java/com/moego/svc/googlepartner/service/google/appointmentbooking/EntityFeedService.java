package com.moego.svc.googlepartner.service.google.appointmentbooking;

import static java.util.stream.Collectors.toMap;

import com.moego.common.utils.CommonUtil;
import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.svc.googlepartner.model.google.appointmentbooking.feed.EntityFeed;
import com.moego.svc.googlepartner.util.Opt;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.util.UriUtils;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class EntityFeedService {

    private static final String DEFAULT_URL = "https://www.moego.pet";

    private final IBusinessBusinessService businessApi;
    private final IGroomingOnlineBookingService bookOnlineApi;

    @Value("${moego.landing-page.url}")
    private String obUrlFormat;

    /**
     * Send entity feeds for specified business ids.
     *
     * @param businessIds business ids
     */
    public EntityFeed getEntityFeeds(Set<Integer> businessIds) {
        return generateEntityFeeds(businessIds);
    }

    private EntityFeed generateEntityFeeds(Set<Integer> businessIds) {
        Map<Integer, String> businessToLandingPageUrl = bookOnlineApi.listOBSetting(List.copyOf(businessIds)).stream()
                .collect(toMap(
                        BookOnlineDTO::getBusinessId,
                        bookOnline -> getLandingPageUrl(obUrlFormat, bookOnline.getBookOnlineName()),
                        (o, n) -> o));

        EntityFeed feed = new EntityFeed();
        List<EntityFeed.Entity> entities = new ArrayList<>();
        businessApi.getOnlyBusinessInfoBatch(List.copyOf(businessIds)).forEach((id, business) -> {
            EntityFeed.Entity entity = new EntityFeed.Entity();
            entity.setEntityId(String.valueOf(business.getId()));
            entity.setName(business.getBusinessName());
            entity.setTelephone(business.getCountryCode() + business.getPhoneNumber());
            if (StringUtils.hasText(business.getWebsite())) {
                entity.setUrl(business.getWebsite());
            } else {
                entity.setUrl(businessToLandingPageUrl.getOrDefault(business.getId(), DEFAULT_URL));
            }

            EntityFeed.GeoCoordinates location = new EntityFeed.GeoCoordinates();
            Opt.doOnNotEmpty(business.getAddressLat(), lat -> location.setLatitude(Double.valueOf(lat)));
            Opt.doOnNotEmpty(business.getAddressLng(), lng -> location.setLongitude(Double.valueOf(lng)));

            setAddress(location, business);

            entity.setLocation(location);

            entities.add(entity);
        });
        feed.setData(entities);
        return feed;
    }

    /*private*/ static String getLandingPageUrl(String urlTemplate, String bookOnlineName) {
        var name = UriUtils.encodeQueryParam(bookOnlineName, StandardCharsets.UTF_8);
        return String.format(urlTemplate, name);
    }

    private static void setAddress(EntityFeed.GeoCoordinates location, MoeBusinessDto business) {
        String address = business.getAddress();
        if (StringUtils.hasText(address)) {
            location.setUnstructuredAddress(address);
        } else {
            location.setUnstructuredAddress(CommonUtil.getFullAddress(
                    business.getAddress1(),
                    business.getAddress2(),
                    business.getAddressCity(),
                    business.getAddressState(),
                    business.getAddressCountry(),
                    business.getAddressZipcode()));
        }
    }
}
