package com.moego.svc.googlepartner.service.google.appointmentbooking;

import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.svc.googlepartner.model.JsonFile;
import com.moego.svc.googlepartner.model.JsonGzipFile;
import com.moego.svc.googlepartner.model.JsonSplitResult;
import com.moego.svc.googlepartner.model.google.appointmentbooking.feed.ActionFeed;
import com.moego.svc.googlepartner.model.google.appointmentbooking.feed.EntityFeed;
import com.moego.svc.googlepartner.model.google.appointmentbooking.feed.FilesetDescriptor;
import com.moego.svc.googlepartner.model.google.appointmentbooking.feed.ServiceFeed;
import com.moego.svc.googlepartner.properties.SshProperties;
import com.moego.svc.googlepartner.util.JsonSplitter;
import com.moego.svc.googlepartner.util.SnakeCaseJsonUtil;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import net.schmizz.sshj.SSHClient;
import net.schmizz.sshj.common.Factory;
import net.schmizz.sshj.common.SSHException;
import net.schmizz.sshj.sftp.SFTPClient;
import net.schmizz.sshj.transport.verification.PromiscuousVerifier;
import net.schmizz.sshj.userauth.keyprovider.FileKeyProvider;
import net.schmizz.sshj.userauth.keyprovider.KeyFormat;
import net.schmizz.sshj.userauth.keyprovider.KeyProviderUtil;
import net.schmizz.sshj.userauth.password.PasswordFinder;
import net.schmizz.sshj.xfer.LocalSourceFile;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.unit.DataSize;

/**
 * Integration with <a href="https://developers.google.com/maps-booking/verticals/appointment-booking/overview">Google Booking</a>.
 *
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class GoogleBookingService {
    private static final String JSON_GZ = ".json.gz";

    private final EntityFeedService entityFeedService;
    private final ActionFeedService actionFeedService;
    private final ServiceFeedService serviceFeedService;
    private final SshProperties sshProperties;
    private final ResourceLoader resourceLoader;

    /**
     * Send entity feeds to sandbox environment.
     *
     * @param businessIds business ids
     */
    public void sendFeedsToSandbox(Set<Integer> businessIds) {
        if (CollectionUtils.isEmpty(businessIds)) {
            log.info("businessIds is empty, will not send any feeds");
            return;
        }
        EntityFeed entityFeed = entityFeedService.getEntityFeeds(businessIds);
        ActionFeed actionFeed = actionFeedService.getActionFeeds(businessIds);
        ServiceFeed serviceFeed = serviceFeedService.getServiceFeeds(businessIds);

        try (SSHClient ssh = buildSshClient(sshProperties.getSandbox());
                SFTPClient sftp = buildSftpClient(ssh)) {
            sendEntityFeeds(sftp, entityFeed);
            sendActionFeeds(sftp, actionFeed);
            sendServiceFeeds(sftp, serviceFeed);
        } catch (IOException e) {
            throw new IllegalStateException("Cannot send feeds to sandbox", e);
        }
    }

    /**
     * Send entity feeds to prod environment.
     *
     * @param businessIds business ids
     */
    public void sendFeedsToProd(Set<Integer> businessIds) {
        if (CollectionUtils.isEmpty(businessIds)) {
            log.info("businessIds is empty, will not send any feeds");
            return;
        }
        EntityFeed entityFeed = entityFeedService.getEntityFeeds(businessIds);
        ActionFeed actionFeed = actionFeedService.getActionFeeds(businessIds);
        ServiceFeed serviceFeed = serviceFeedService.getServiceFeeds(businessIds);

        try (SSHClient ssh = buildSshClient(sshProperties.getProd());
                SFTPClient sftp = buildSftpClient(ssh)) {
            sendEntityFeeds(sftp, entityFeed);
            sendActionFeeds(sftp, actionFeed);
            sendServiceFeeds(sftp, serviceFeed);
        } catch (IOException e) {
            throw new IllegalStateException("Cannot send feeds to prod", e);
        }
    }

    private static void sendEntityFeeds(SFTPClient sftpClient, EntityFeed entityFeed) {
        long ts = System.currentTimeMillis() / 1000;
        String entityFeedFileName = "entity-" + ts + JSON_GZ;
        JsonSplitResult jsr = JsonSplitter.splitJsonArray(
                entityFeedFileName, SnakeCaseJsonUtil.toJson(entityFeed.getData()), DataSize.ofMegabytes(200));
        List<String> fileNames =
                jsr.getPieces().stream().map(JsonSplitResult.Piece::getFileName).toList();
        FilesetDescriptor entityFd = new FilesetDescriptor()
                .setName(FilesetDescriptor.ENTITY)
                .setGenerationTimestamp(ts)
                .setDataFile(fileNames);

        uploadJsonFile(
                sftpClient,
                FilesetDescriptor.ENTITY + "-" + entityFd.getGenerationTimestamp() + FilesetDescriptor.FILE_EXTENSION,
                SnakeCaseJsonUtil.toJson(entityFd));

        for (JsonSplitResult.Piece piece : jsr.getPieces()) {
            String jsonArr = String.format("{\"data\":%s}", piece.getJsonValue());
            uploadJsonGzipFile(sftpClient, piece.getFileName(), jsonArr);
        }
    }

    private static void sendActionFeeds(SFTPClient sftpClient, ActionFeed actionFeed) {
        long ts = System.currentTimeMillis() / 1000;
        String actionFeedFileName = "action-" + ts + JSON_GZ;
        JsonSplitResult jsr = JsonSplitter.splitJsonArray(
                actionFeedFileName, SnakeCaseJsonUtil.toJson(actionFeed.getData()), DataSize.ofMegabytes(200));
        List<String> fileNames =
                jsr.getPieces().stream().map(JsonSplitResult.Piece::getFileName).toList();
        FilesetDescriptor actionFd = new FilesetDescriptor()
                .setName(FilesetDescriptor.ACTION)
                .setGenerationTimestamp(ts)
                .setDataFile(fileNames);

        uploadJsonFile(
                sftpClient,
                FilesetDescriptor.ACTION + "-" + actionFd.getGenerationTimestamp() + FilesetDescriptor.FILE_EXTENSION,
                SnakeCaseJsonUtil.toJson(actionFd));

        for (JsonSplitResult.Piece piece : jsr.getPieces()) {
            String jsonArr = String.format("{\"data\":%s}", piece.getJsonValue());
            uploadJsonGzipFile(sftpClient, piece.getFileName(), jsonArr);
        }
    }

    private static void sendServiceFeeds(SFTPClient sftpClient, ServiceFeed serviceFeed) {
        long ts = System.currentTimeMillis() / 1000;
        String serviceFeedFileName = "service-" + ts + JSON_GZ;
        JsonSplitResult jsr = JsonSplitter.splitJsonArray(
                serviceFeedFileName, SnakeCaseJsonUtil.toJson(serviceFeed.getData()), DataSize.ofMegabytes(200));
        List<String> fileNames =
                jsr.getPieces().stream().map(JsonSplitResult.Piece::getFileName).toList();
        FilesetDescriptor serviceFd = new FilesetDescriptor()
                .setName(FilesetDescriptor.SERVICE)
                .setGenerationTimestamp(ts)
                .setDataFile(fileNames);

        uploadJsonFile(
                sftpClient,
                FilesetDescriptor.SERVICE + "-" + serviceFd.getGenerationTimestamp() + FilesetDescriptor.FILE_EXTENSION,
                SnakeCaseJsonUtil.toJson(serviceFd));

        for (JsonSplitResult.Piece piece : jsr.getPieces()) {
            String jsonArr = String.format("{\"data\":%s}", piece.getJsonValue());
            uploadJsonGzipFile(sftpClient, piece.getFileName(), jsonArr);
        }
    }

    private static void uploadJsonFile(SFTPClient sftpClient, String fileName, String jsonValue) {
        try {
            LocalSourceFile file = new JsonFile(fileName, jsonValue);
            sftpClient.put(file, file.getName());
            log.info("Send file {} to sftp server, size: {}", file.getName(), file.getLength());

            // save file to local for debug
            ThreadPool.execute(() -> saveFile(new JsonFile(fileName, jsonValue)));
        } catch (IOException e) {
            throw bizException(Code.CODE_SERVER_ERROR, e, "Failed to send .json file: {}" + fileName);
        }
    }

    private static void uploadJsonGzipFile(SFTPClient sftpClient, String fileName, String jsonValue) {
        try {
            LocalSourceFile file = new JsonGzipFile(fileName, jsonValue);
            sftpClient.put(file, file.getName());
            log.info("Send file {} to sftp server, size: {}", file.getName(), file.getLength());

            // save file to local for debug
            ThreadPool.execute(() -> saveFile(new JsonGzipFile(fileName, jsonValue)));
        } catch (IOException e) {
            throw bizException(Code.CODE_SERVER_ERROR, e, "Failed to send .json.gz file: {}" + fileName);
        }
    }

    @SneakyThrows
    private static void saveFile(LocalSourceFile file) {
        Files.copy(file.getInputStream(), Paths.get(file.getName()), StandardCopyOption.REPLACE_EXISTING);
    }

    @SneakyThrows
    private SSHClient buildSshClient(SshProperties.Ssh sshConfig) {
        SSHClient ssh = new SSHClient();

        ssh.addHostKeyVerifier(new PromiscuousVerifier());

        ssh.connect(sshConfig.getHost(), sshConfig.getPort());

        Resource resource = resourceLoader.getResource(sshConfig.getPrivateKeyLocation());
        ssh.authPublickey(sshConfig.getUsername(), getKeyProvider(ssh, resource));

        return ssh;
    }

    @SneakyThrows
    private static SFTPClient buildSftpClient(SSHClient sshClient) {
        return sshClient.newSFTPClient();
    }

    /**
     * @see SSHClient#loadKeys(String, PasswordFinder)
     */
    private static FileKeyProvider getKeyProvider(SSHClient ssh, Resource resource) throws IOException {
        final KeyFormat format = KeyProviderUtil.detectKeyFileFormat(
                new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8), true);
        final FileKeyProvider fkp = Factory.Named.Util.create(
                ssh.getTransport().getConfig().getFileKeyProviderFactories(), format.toString());
        if (fkp == null) {
            throw new SSHException("No provider available for " + format + " key file");
        }

        StringBuilder sb = new StringBuilder();
        try (BufferedReader br =
                new BufferedReader(new InputStreamReader(resource.getInputStream(), StandardCharsets.UTF_8))) {
            String line;
            while ((line = br.readLine()) != null) {
                sb.append(line).append('\n');
            }
        }

        fkp.init(sb.toString(), null);
        return fkp;
    }
}
