package com.moego.svc.googlepartner.service.google.appointmentbooking;

import static com.moego.svc.googlepartner.service.google.appointmentbooking.EntityFeedService.getLandingPageUrl;

import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.svc.googlepartner.model.google.appointmentbooking.feed.ActionFeed;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ActionFeedService {

    private final IGroomingOnlineBookingService bookOnlineApi;

    @Value("${moego.landing-page.url}")
    private String obUrlFormat;

    /**
     * Send action feeds for specified business ids.
     *
     * @param businessIds business ids
     */
    public ActionFeed getActionFeeds(Set<Integer> businessIds) {
        return generateActionFeeds(businessIds);
    }

    private ActionFeed generateActionFeeds(Set<Integer> businessIds) {
        ActionFeed actionFeed = new ActionFeed();
        List<ActionFeed.ActionDetail> details = new ArrayList<>();
        bookOnlineApi.listOBSetting(List.copyOf(businessIds)).forEach(bookOnline -> {
            ActionFeed.ActionDetail detail = new ActionFeed.ActionDetail();
            detail.setEntityId(String.valueOf(bookOnline.getBusinessId()));
            detail.setActions(List.of(new ActionFeed.Action()
                    .setAppointmentInfo(new ActionFeed.AppointmentInfo()
                            .setUrl(getLandingPageUrl(obUrlFormat, bookOnline.getBookOnlineName()) + "&source=gr"))));

            details.add(detail);
        });
        actionFeed.setData(details);
        return actionFeed;
    }
}
