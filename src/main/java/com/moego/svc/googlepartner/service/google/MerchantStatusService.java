package com.moego.svc.googlepartner.service.google;

import com.google.auth.oauth2.AccessToken;
import com.google.auth.oauth2.GoogleCredentials;
import com.moego.svc.googlepartner.model.google.MerchantStatus;
import com.moego.svc.googlepartner.model.google.MerchantStatusList;
import com.moego.svc.googlepartner.properties.GoogleReserveProperties;
import com.moego.svc.googlepartner.service.param.ListMerchantStatusParam;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import lombok.SneakyThrows;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.RequestEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.client.RestTemplate;

/**
 * <AUTHOR>
 * @see <a href="https://developers.google.com/actions-center/verticals/appointments/e2e/reference/real-time-updates-rest/code-samples">Code Samples</a>
 */
@Service
public class MerchantStatusService {

    private static final String ENDPOINT =
            //                    "https://partnerdev-mapsbooking.googleapis.com"; // for sandbox
            "https://mapsbooking.googleapis.com"; // for prod

    private final GoogleReserveProperties googleReserveProperties;
    private final ResourceLoader resourceLoader;
    private final RestTemplate rest;

    public MerchantStatusService(
            GoogleReserveProperties googleReserveProperties,
            ResourceLoader resourceLoader,
            RestTemplateBuilder restTemplateBuilder) {
        this.googleReserveProperties = googleReserveProperties;
        this.resourceLoader = resourceLoader;
        this.rest = restTemplateBuilder.build();
    }

    /**
     * List {@link MerchantStatus}.
     *
     * @return {@link MerchantStatusList}
     * @see <a href="https://developers.google.com/maps-booking/reference/maps-booking-api/rest/v1alpha/inventory.partners.merchants.status/list">List Merchant Status</a>
     */
    public List<MerchantStatus> listMerchantStatus(ListMerchantStatusParam param) {
        List<MerchantStatus> result = new ArrayList<>();

        while (true) {
            MerchantStatusList body = rest.exchange(getRequestEntity(param), MerchantStatusList.class)
                    .getBody();
            if (body == null) {
                return result;
            }

            result.addAll(Optional.ofNullable(body.getMerchantStatuses()).orElseGet(List::of));

            if (!StringUtils.hasText(body.getNextPageToken())) {
                return result;
            }

            param = ListMerchantStatusParam.of(param).setPageToken(body.getNextPageToken());
        }
    }

    private RequestEntity<Void> getRequestEntity(ListMerchantStatusParam param) {
        return RequestEntity.get(String.format(
                        "%s/v1alpha/inventory/partners/%s/merchants/status?%s",
                        ENDPOINT, googleReserveProperties.getPartnerId(), getQuery(param)))
                .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_JSON_VALUE)
                .header(
                        HttpHeaders.AUTHORIZATION,
                        String.format("Bearer %s", getAccessToken().getTokenValue()))
                .build();
    }

    private static String getQuery(ListMerchantStatusParam param) {
        StringBuilder queryBuilder = new StringBuilder();
        queryBuilder.append(String.format("pageSize=%d", param.getPageSize()));
        if (param.getGeoMatchRestrict() != null) {
            queryBuilder.append(String.format("&geoMatchRestrict=%s", param.getGeoMatchRestrict()));
        }
        if (param.getWaitlistInventoryStatusRestrict() != null) {
            queryBuilder.append(
                    String.format("&waitlistInventoryStatusRestrict=%s", param.getWaitlistInventoryStatusRestrict()));
        }
        if (param.getBookingInventoryStatusRestrict() != null) {
            queryBuilder.append(
                    String.format("&bookingInventoryStatusRestrict=%s", param.getBookingInventoryStatusRestrict()));
        }
        if (param.getPageToken() != null) {
            queryBuilder.append(String.format("&pageToken=%s", param.getPageToken()));
        }
        return queryBuilder.toString();
    }

    @SneakyThrows
    private AccessToken getAccessToken() {
        Resource credentialFile = resourceLoader.getResource(
                googleReserveProperties.getMerchantStatusQuery().getCredentialsPath());
        GoogleCredentials credentials = GoogleCredentials.fromStream(credentialFile.getInputStream())
                .createScoped(List.of("https://www.googleapis.com/auth/mapsbooking"));
        credentials.refreshIfExpired();
        return credentials.getAccessToken();
    }
}
