package com.moego.svc.googlepartner.service.google.appointmentbooking;

import static com.moego.svc.googlepartner.service.google.appointmentbooking.EntityFeedService.getLandingPageUrl;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;

import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.api.IGroomingServiceCategoryService;
import com.moego.server.grooming.api.IGroomingServiceService;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.GroomingServiceDTO;
import com.moego.server.grooming.dto.ServiceCategoryDTO;
import com.moego.svc.googlepartner.model.google.appointmentbooking.feed.ServiceFeed;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class ServiceFeedService {

    private static final Byte SHOW_FIXED_PRICE = 1;
    private static final Byte SHOW_START_AT_PRICE = 2;

    private final IGroomingServiceService serviceApi;
    private final IGroomingServiceCategoryService serviceCategoryApi;
    private final IGroomingOnlineBookingService bookOnlineApi;

    @Value("${moego.landing-page.url}")
    private String obUrlFormat;

    /**
     * Send service feeds for specified business ids.
     *
     * @param businessIds business ids
     */
    public ServiceFeed getServiceFeeds(Set<Integer> businessIds) {
        return generateServiceFeeds(businessIds);
    }

    private ServiceFeed generateServiceFeeds(Set<Integer> businessIds) {
        ServiceFeed serviceFeed = new ServiceFeed();
        List<ServiceFeed.Service> services = new ArrayList<>();

        Map<Integer, List<GroomingServiceDTO>> businessToServices = serviceApi.getServices(businessIds);
        Map<Integer, String> categoryIdToName =
                serviceCategoryApi.getServiceCategories(getCategoryIds(businessToServices)).stream()
                        .collect(toMap(ServiceCategoryDTO::getId, ServiceCategoryDTO::getName, (o, n) -> o));

        Map<Integer, String> businessToServiceUrl = bookOnlineApi.listOBSetting(List.copyOf(businessIds)).stream()
                .collect(toMap(
                        BookOnlineDTO::getBusinessId,
                        // e.g. https://booking.moego.pet/ol/landing?name=RainyDayGrooming&source=gr&service=1076491
                        bookOnline -> getLandingPageUrl(obUrlFormat, bookOnline.getBookOnlineName()) + "&source=gr",
                        (o, n) -> o));

        businessToServices.forEach((id, serviceList) -> serviceList.stream()
                // skip if service is not enabled for OB
                .filter(s -> Objects.equals(s.getBookOnlineAvailable(), (byte) 1))
                .filter(s -> StringUtils.hasText(s.getName()) && !containsNewline(s.getName()))
                // distinct same service name and category for same business
                .collect(Collectors.toMap(
                        // Services must have a unique name within a merchant.
                        GroomingServiceDTO::getName, Function.identity(), (o, n) -> o))
                .values()
                .forEach(svc -> {
                    ServiceFeed.Service service = new ServiceFeed.Service();
                    service.setMerchantId(String.valueOf(id));
                    service.setServiceId(String.valueOf(svc.getId()));
                    // service name is required
                    service.setLocalizedServiceName(new ServiceFeed.Text().setValue(svc.getName()));
                    // service category is required
                    service.setLocalizedServiceCategory(new ServiceFeed.Text()
                            .setValue(categoryIdToName.getOrDefault(svc.getCategoryId(), "Others")));
                    // description is optional
                    if (StringUtils.hasText(svc.getDescription())) {
                        service.setLocalizedServiceDescription(new ServiceFeed.Text().setValue(svc.getDescription()));
                    } else {
                        service.setLocalizedServiceDescription(
                                new ServiceFeed.Text().setValue("Your pet will have a wonderful time here!"));
                    }

                    // service price is required
                    setPrice(svc, service);

                    // service duration is required
                    Integer durationInMinute = svc.getDuration();
                    if (durationInMinute == null) {
                        service.setServiceDuration(new ServiceFeed.ServiceDuration()
                                .setDurationInterpretation(
                                        ServiceFeed.RangeInterpretation.INTERPRETATION_NOT_DISPLAYED));
                    } else {
                        if (durationInMinute == 0) {
                            durationInMinute = 10;
                        }
                        durationInMinute = Math.min(durationInMinute, 160 * 60);
                        service.setServiceDuration(new ServiceFeed.ServiceDuration()
                                .setDurationInterpretation(ServiceFeed.RangeInterpretation.INTERPRETATION_STARTS_AT)
                                .setMinDurationSec(durationInMinute * 60L));
                    }

                    // action link is optional, but warning if not provided :)
                    // so we just set it to the same as the landing page
                    Optional.ofNullable(businessToServiceUrl.get(id))
                            .map(url -> new ServiceFeed.ActionLink().setUrl(url + "&service=" + svc.getId()))
                            .map(List::of)
                            .ifPresent(service::setActionLink);

                    // ranking hint is optional
                    Integer sort = Optional.ofNullable(svc.getSort()).orElse(0);
                    service.setRankingHint(new ServiceFeed.ServiceRankingHint().setScore(Double.valueOf(sort)));

                    services.add(service);
                }));
        serviceFeed.setData(services);
        return serviceFeed;
    }

    static boolean containsNewline(String str) {
        return str.contains("\n");
    }

    private static void setPrice(GroomingServiceDTO svc, ServiceFeed.Service service) {
        Byte showBasePrice = svc.getShowBasePrice();
        if (Objects.equals(showBasePrice, SHOW_FIXED_PRICE)) {
            service.setServicePrice(new ServiceFeed.ServicePrice()
                    .setPriceInterpretation(ServiceFeed.RangeInterpretation.INTERPRETATION_EXACT)
                    .setMinPrice(new ServiceFeed.Price()
                            .setCurrencyCode("USD")
                            .setPriceMicros(svc.getPrice()
                                    .multiply(BigDecimal.valueOf(1000000))
                                    .longValue())));
        } else if (Objects.equals(showBasePrice, SHOW_START_AT_PRICE)) {
            service.setServicePrice(new ServiceFeed.ServicePrice()
                    .setPriceInterpretation(ServiceFeed.RangeInterpretation.INTERPRETATION_STARTS_AT)
                    .setMinPrice(new ServiceFeed.Price()
                            .setCurrencyCode("USD")
                            .setPriceMicros(svc.getPrice()
                                    .multiply(BigDecimal.valueOf(1000000))
                                    .longValue())));
        } else {
            service.setServicePrice(new ServiceFeed.ServicePrice()
                    .setPriceInterpretation(ServiceFeed.RangeInterpretation.INTERPRETATION_NOT_DISPLAYED));
        }
    }

    private static Set<Integer> getCategoryIds(Map<Integer, List<GroomingServiceDTO>> businessToServices) {
        return businessToServices.values().stream()
                .flatMap(Collection::stream)
                .map(GroomingServiceDTO::getCategoryId)
                .collect(toSet());
    }
}
