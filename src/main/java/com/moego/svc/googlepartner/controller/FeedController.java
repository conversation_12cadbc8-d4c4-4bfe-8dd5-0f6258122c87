package com.moego.svc.googlepartner.controller;

import com.google.protobuf.Empty;
import com.moego.idl.service.google_partner.v1.FeedServiceGrpc;
import com.moego.idl.service.google_partner.v1.SendFeedsRequest;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.svc.googlepartner.service.google.appointmentbooking.GoogleBookingService;
import io.grpc.stub.StreamObserver;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 */
@GrpcService
@RequiredArgsConstructor
public class FeedController extends FeedServiceGrpc.FeedServiceImplBase {

    private final GoogleBookingService googleBookingService;
    private final IBusinessBusinessService businessApi;

    @Override
    public void sendFeedsToSandbox(SendFeedsRequest request, StreamObserver<Empty> responseObserver) {
        Set<Integer> businessIds =
                request.getBusinessIdsList().stream().map(Long::intValue).collect(Collectors.toSet());
        ThreadPool.execute(() -> googleBookingService.sendFeedsToSandbox(businessIds));
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void sendFeedsToProd(SendFeedsRequest request, StreamObserver<Empty> responseObserver) {
        Set<Integer> businessIds =
                request.getBusinessIdsList().stream().map(Long::intValue).collect(Collectors.toSet());
        ThreadPool.execute(() -> googleBookingService.sendFeedsToProd(businessIds));
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void sendFeedsToSandboxForAll(Empty request, StreamObserver<Empty> responseObserver) {
        ThreadPool.execute(() -> googleBookingService.sendFeedsToSandbox(Set.copyOf(businessApi.getAllBusinessIds2())));
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }

    @Override
    public void sendFeedsToProdForAll(Empty request, StreamObserver<Empty> responseObserver) {
        ThreadPool.execute(() -> googleBookingService.sendFeedsToProd(Set.copyOf(businessApi.getAllBusinessIds2())));
        responseObserver.onNext(Empty.getDefaultInstance());
        responseObserver.onCompleted();
    }
}
