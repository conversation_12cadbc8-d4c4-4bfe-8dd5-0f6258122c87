package com.moego.svc.googlepartner.controller;

import static com.moego.idl.models.google_partner.v1.GoogleReserveIntegrationStatus.GOOGLE_RESERVE_INTEGRATION_STATUS_MATCHED_VALUE;
import static com.moego.idl.models.google_partner.v1.GoogleReserveIntegrationStatus.GOOGLE_RESERVE_INTEGRATION_STATUS_UNMATCHED_VALUE;
import static com.moego.svc.googlepartner.mapper.GoogleReserveIntegrationDynamicSqlSupport.businessId;
import static com.moego.svc.googlepartner.mapper.GoogleReserveIntegrationDynamicSqlSupport.enabled;
import static com.moego.svc.googlepartner.mapper.GoogleReserveIntegrationDynamicSqlSupport.status;
import static com.moego.svc.googlepartner.mapstruct.GoogleReserveIntegrationMapper.INSTANCE;
import static io.grpc.Status.NOT_FOUND;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isIn;
import static org.mybatis.dynamic.sql.SqlBuilder.isNotEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isTrue;

import com.google.protobuf.Empty;
import com.moego.idl.models.google_partner.v1.GoogleReserveIntegrationModel;
import com.moego.idl.service.google_partner.v1.DeleteGoogleReserveIntegrationRequest;
import com.moego.idl.service.google_partner.v1.DeleteGoogleReserveIntegrationResponse;
import com.moego.idl.service.google_partner.v1.GetGoogleReserveIntegrationRequest;
import com.moego.idl.service.google_partner.v1.GoogleReserveIntegrationServiceGrpc;
import com.moego.idl.service.google_partner.v1.InsertGoogleReserveIntegrationRequest;
import com.moego.idl.service.google_partner.v1.ListGoogleReserveIntegrationResponse;
import com.moego.idl.service.google_partner.v1.RefreshGoogleReserveIntegrationStatusRequest;
import com.moego.idl.service.google_partner.v1.RefreshGoogleReserveIntegrationStatusResponse;
import com.moego.idl.service.google_partner.v1.UpdateGoogleReserveIntegrationRequest;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.message.api.INotificationService;
import com.moego.server.message.params.notification.NotificationGoogleReserveGeoMatchedParams;
import com.moego.server.message.params.notification.NotificationGoogleReserveGeoUnmatchedParams;
import com.moego.svc.googlepartner.entity.GoogleReserveIntegration;
import com.moego.svc.googlepartner.mapper.GoogleReserveIntegrationDynamicSqlSupport;
import com.moego.svc.googlepartner.mapper.GoogleReserveIntegrationMapper;
import com.moego.svc.googlepartner.model.google.MerchantStatus;
import com.moego.svc.googlepartner.service.google.MerchantStatusService;
import com.moego.svc.googlepartner.service.param.ListMerchantStatusParam;
import io.grpc.StatusRuntimeException;
import io.grpc.stub.StreamObserver;
import jakarta.annotation.Nullable;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class GoogleReserveIntegrationController
        extends GoogleReserveIntegrationServiceGrpc.GoogleReserveIntegrationServiceImplBase {

    private final GoogleReserveIntegrationMapper googleReserveIntegrationMapper;
    private final MerchantStatusService merchantStatusService;
    private final INotificationService notificationApi;

    @Override
    public void getGoogleReserveIntegration(
            GetGoogleReserveIntegrationRequest request,
            StreamObserver<GoogleReserveIntegrationModel> responseObserver) {
        responseObserver.onNext(INSTANCE.entityToModel(getGoogleReserveIntegration(request.getBusinessId())));
        responseObserver.onCompleted();
    }

    @Override
    public void insertGoogleReserveIntegration(
            InsertGoogleReserveIntegrationRequest request,
            StreamObserver<GoogleReserveIntegrationModel> responseObserver) {
        int bid = request.getBusinessId();
        GoogleReserveIntegration saveEntity = new GoogleReserveIntegration();
        saveEntity.setBusinessId(bid);
        if (request.hasStatus()) {
            saveEntity.setStatus(request.getStatusValue());
        }
        if (request.hasEnabled()) {
            saveEntity.setEnabled(request.getEnabled());
        }

        try {
            googleReserveIntegrationMapper.insertSelective(saveEntity);
        } catch (DuplicateKeyException e) {
            log.warn("DuplicateKeyException when insert google_reserve_integration, businessId: {}", bid);
        }

        responseObserver.onNext(INSTANCE.entityToModel(getGoogleReserveIntegration(bid)));
        responseObserver.onCompleted();
    }

    @Override
    public void updateGoogleReserveIntegration(
            UpdateGoogleReserveIntegrationRequest request,
            StreamObserver<GoogleReserveIntegrationModel> responseObserver) {
        int bid = request.getBusinessId();
        GoogleReserveIntegration updateEntity = new GoogleReserveIntegration();
        if (request.hasEnabled()) {
            updateEntity.setEnabled(request.getEnabled());
        }
        if (request.hasStatus()) {
            updateEntity.setStatus(request.getStatusValue());
        }
        updateEntity.setUpdateTime(new Date());

        googleReserveIntegrationMapper.update(
                c -> GoogleReserveIntegrationMapper.updateSelectiveColumns(updateEntity, c)
                        .where(businessId, isEqualTo(bid)));

        responseObserver.onNext(INSTANCE.entityToModel(getGoogleReserveIntegration(bid)));
        responseObserver.onCompleted();
    }

    @Override
    public void refreshGoogleReserveIntegrationStatus(
            RefreshGoogleReserveIntegrationStatusRequest request,
            StreamObserver<RefreshGoogleReserveIntegrationStatusResponse> responseObserver) {
        Set<Integer> businessIds = googleReserveIntegrationMapper.select(c -> c.where(enabled, isTrue())).stream()
                .map(GoogleReserveIntegration::getBusinessId)
                .collect(Collectors.toSet());

        List<Integer> unmatchedBusinessIds = merchantStatusService
                .listMerchantStatus(new ListMerchantStatusParam()
                        .setGeoMatchRestrict(ListMerchantStatusParam.GeoMatchingStatus.GEO_UNMATCHED))
                .stream()
                .map(GoogleReserveIntegrationController::getBusinessId)
                .filter(Objects::nonNull)
                .filter(businessIds::contains)
                .toList();

        if (!ObjectUtils.isEmpty(unmatchedBusinessIds)) {
            ThreadPool.execute(() -> updateStatusToUnmatched(unmatchedBusinessIds));
        }

        List<Integer> matchedBusinessIds = merchantStatusService
                .listMerchantStatus(new ListMerchantStatusParam()
                        .setGeoMatchRestrict(ListMerchantStatusParam.GeoMatchingStatus.GEO_MATCHED))
                .stream()
                .map(GoogleReserveIntegrationController::getBusinessId)
                .filter(Objects::nonNull)
                .filter(businessIds::contains)
                .toList();

        if (!ObjectUtils.isEmpty(matchedBusinessIds)) {
            ThreadPool.execute(() -> updateStatusToMatched(unmatchedBusinessIds));
        }

        responseObserver.onNext(RefreshGoogleReserveIntegrationStatusResponse.newBuilder()
                .addAllMatchedBusinessIds(matchedBusinessIds)
                .addAllUnmatchedBusinessIds(unmatchedBusinessIds)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void listGoogleReserveIntegration(
            Empty request, StreamObserver<ListGoogleReserveIntegrationResponse> responseObserver) {
        List<GoogleReserveIntegrationModel> models = googleReserveIntegrationMapper.select(c -> c).stream()
                .map(INSTANCE::entityToModel)
                .toList();
        responseObserver.onNext(ListGoogleReserveIntegrationResponse.newBuilder()
                .addAllModels(models)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void deleteGoogleReserveIntegration(
            DeleteGoogleReserveIntegrationRequest request,
            StreamObserver<DeleteGoogleReserveIntegrationResponse> responseObserver) {
        List<Integer> businessIds = request.getBusinessIdList();
        int count = !ObjectUtils.isEmpty(businessIds)
                ? googleReserveIntegrationMapper.delete(c -> c.where(businessId, isIn(businessIds)))
                : 0;
        responseObserver.onNext(DeleteGoogleReserveIntegrationResponse.newBuilder()
                .setCount(count)
                .build());
        responseObserver.onCompleted();
    }

    private GoogleReserveIntegration getGoogleReserveIntegration(int businessId) {
        return googleReserveIntegrationMapper
                .selectOne(c -> c.where(GoogleReserveIntegrationDynamicSqlSupport.businessId, isEqualTo(businessId)))
                .orElseThrow(() -> new StatusRuntimeException(NOT_FOUND));
    }

    /**
     * @see <a href="https://developers.google.com/actions-center/verticals/appointments/redirect/partner-portal/inventory/merchant-status-query">Merchant Status Query</a>
     */
    @Nullable
    static Integer getBusinessId(MerchantStatus merchantStatus) {
        if (!StringUtils.hasText(merchantStatus.getName())) {
            return null;
        }
        String[] parts = merchantStatus.getName().split("/");
        if (parts.length < 2) {
            return null;
        }
        return Integer.parseInt(parts[parts.length - 2]);
    }

    /** for testing */
    void updateStatusToUnmatched(List<Integer> businessIds) {
        for (Integer bid : businessIds) {
            int affected = googleReserveIntegrationMapper.update(c -> c.set(status)
                    .equalTo(GOOGLE_RESERVE_INTEGRATION_STATUS_UNMATCHED_VALUE)
                    .where(businessId, isEqualTo(bid))
                    .and(enabled, isTrue())
                    .and(status, isNotEqualTo(GOOGLE_RESERVE_INTEGRATION_STATUS_UNMATCHED_VALUE))); // 防止下次更新时重复发送通知
            if (affected > 0) {
                NotificationGoogleReserveGeoUnmatchedParams param = new NotificationGoogleReserveGeoUnmatchedParams();
                param.setBusinessId(bid);
                notificationApi.sendGoogleReserveGeoUnmatchedNotification(param);
            }
        }
    }

    /** for testing */
    void updateStatusToMatched(List<Integer> businessIds) {
        for (Integer bid : businessIds) {
            int affected = googleReserveIntegrationMapper.update(c -> c.set(status)
                    .equalTo(GOOGLE_RESERVE_INTEGRATION_STATUS_MATCHED_VALUE)
                    .where(businessId, isEqualTo(bid))
                    .and(enabled, isTrue())
                    .and(status, isNotEqualTo(GOOGLE_RESERVE_INTEGRATION_STATUS_MATCHED_VALUE))); // 防止下次更新时重复发送通知
            if (affected > 0) {
                NotificationGoogleReserveGeoMatchedParams param = new NotificationGoogleReserveGeoMatchedParams();
                param.setBusinessId(bid);
                notificationApi.sendGoogleReserveGeoMatchedNotification(param);
            }
        }
    }
}
