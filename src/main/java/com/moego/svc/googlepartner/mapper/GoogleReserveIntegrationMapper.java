package com.moego.svc.googlepartner.mapper;

import static com.moego.svc.googlepartner.mapper.GoogleReserveIntegrationDynamicSqlSupport.*;
import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;

import com.moego.svc.googlepartner.entity.GoogleReserveIntegration;
import jakarta.annotation.Generated;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.apache.ibatis.annotations.InsertProvider;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Options;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.ResultMap;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.SelectProvider;
import org.apache.ibatis.type.JdbcType;
import org.mybatis.dynamic.sql.BasicColumn;
import org.mybatis.dynamic.sql.delete.DeleteDSLCompleter;
import org.mybatis.dynamic.sql.insert.render.InsertStatementProvider;
import org.mybatis.dynamic.sql.select.CountDSLCompleter;
import org.mybatis.dynamic.sql.select.SelectDSLCompleter;
import org.mybatis.dynamic.sql.select.render.SelectStatementProvider;
import org.mybatis.dynamic.sql.update.UpdateDSL;
import org.mybatis.dynamic.sql.update.UpdateDSLCompleter;
import org.mybatis.dynamic.sql.update.UpdateModel;
import org.mybatis.dynamic.sql.util.SqlProviderAdapter;
import org.mybatis.dynamic.sql.util.mybatis3.CommonCountMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonDeleteMapper;
import org.mybatis.dynamic.sql.util.mybatis3.CommonUpdateMapper;
import org.mybatis.dynamic.sql.util.mybatis3.MyBatis3Utils;

@Mapper
public interface GoogleReserveIntegrationMapper extends CommonCountMapper, CommonDeleteMapper, CommonUpdateMapper {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    BasicColumn[] selectList = BasicColumn.columnList(id, businessId, enabled, createTime, updateTime, status);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    @InsertProvider(type=SqlProviderAdapter.class, method="insert")
    @Options(useGeneratedKeys=true,keyProperty="row.id")
    int insert(InsertStatementProvider<GoogleReserveIntegration> insertStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    @InsertProvider(type=SqlProviderAdapter.class, method="insertMultipleWithGeneratedKeys")
    @Options(useGeneratedKeys=true,keyProperty="records.id")
    int insertMultiple(@Param("insertStatement") String insertStatement, @Param("records") List<GoogleReserveIntegration> records);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @Results(id="GoogleReserveIntegrationResult", value = {
        @Result(column="id", property="id", jdbcType=JdbcType.BIGINT, id=true),
        @Result(column="business_id", property="businessId", jdbcType=JdbcType.INTEGER),
        @Result(column="enabled", property="enabled", jdbcType=JdbcType.BIT),
        @Result(column="create_time", property="createTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="update_time", property="updateTime", jdbcType=JdbcType.TIMESTAMP),
        @Result(column="status", property="status", jdbcType=JdbcType.INTEGER)
    })
    List<GoogleReserveIntegration> selectMany(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    @SelectProvider(type=SqlProviderAdapter.class, method="select")
    @ResultMap("GoogleReserveIntegrationResult")
    Optional<GoogleReserveIntegration> selectOne(SelectStatementProvider selectStatement);

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    default long count(CountDSLCompleter completer) {
        return MyBatis3Utils.countFrom(this::count, googleReserveIntegration, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    default int delete(DeleteDSLCompleter completer) {
        return MyBatis3Utils.deleteFrom(this::delete, googleReserveIntegration, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    default int deleteByPrimaryKey(Long id_) {
        return delete(c -> 
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    default int insert(GoogleReserveIntegration row) {
        return MyBatis3Utils.insert(this::insert, row, googleReserveIntegration, c ->
            c.map(businessId).toProperty("businessId")
            .map(enabled).toProperty("enabled")
            .map(createTime).toProperty("createTime")
            .map(updateTime).toProperty("updateTime")
            .map(status).toProperty("status")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    default int insertMultiple(Collection<GoogleReserveIntegration> records) {
        return MyBatis3Utils.insertMultipleWithGeneratedKeys(this::insertMultiple, records, googleReserveIntegration, c ->
            c.map(businessId).toProperty("businessId")
            .map(enabled).toProperty("enabled")
            .map(createTime).toProperty("createTime")
            .map(updateTime).toProperty("updateTime")
            .map(status).toProperty("status")
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    default int insertSelective(GoogleReserveIntegration row) {
        return MyBatis3Utils.insert(this::insert, row, googleReserveIntegration, c ->
            c.map(businessId).toPropertyWhenPresent("businessId", row::getBusinessId)
            .map(enabled).toPropertyWhenPresent("enabled", row::getEnabled)
            .map(createTime).toPropertyWhenPresent("createTime", row::getCreateTime)
            .map(updateTime).toPropertyWhenPresent("updateTime", row::getUpdateTime)
            .map(status).toPropertyWhenPresent("status", row::getStatus)
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    default Optional<GoogleReserveIntegration> selectOne(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectOne(this::selectOne, selectList, googleReserveIntegration, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    default List<GoogleReserveIntegration> select(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectList(this::selectMany, selectList, googleReserveIntegration, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    default List<GoogleReserveIntegration> selectDistinct(SelectDSLCompleter completer) {
        return MyBatis3Utils.selectDistinct(this::selectMany, selectList, googleReserveIntegration, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    default Optional<GoogleReserveIntegration> selectByPrimaryKey(Long id_) {
        return selectOne(c ->
            c.where(id, isEqualTo(id_))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    default int update(UpdateDSLCompleter completer) {
        return MyBatis3Utils.update(this::update, googleReserveIntegration, completer);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    static UpdateDSL<UpdateModel> updateAllColumns(GoogleReserveIntegration row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(businessId).equalTo(row::getBusinessId)
                .set(enabled).equalTo(row::getEnabled)
                .set(createTime).equalTo(row::getCreateTime)
                .set(updateTime).equalTo(row::getUpdateTime)
                .set(status).equalTo(row::getStatus);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    static UpdateDSL<UpdateModel> updateSelectiveColumns(GoogleReserveIntegration row, UpdateDSL<UpdateModel> dsl) {
        return dsl.set(businessId).equalToWhenPresent(row::getBusinessId)
                .set(enabled).equalToWhenPresent(row::getEnabled)
                .set(createTime).equalToWhenPresent(row::getCreateTime)
                .set(updateTime).equalToWhenPresent(row::getUpdateTime)
                .set(status).equalToWhenPresent(row::getStatus);
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    default int updateByPrimaryKey(GoogleReserveIntegration row) {
        return update(c ->
            c.set(businessId).equalTo(row::getBusinessId)
            .set(enabled).equalTo(row::getEnabled)
            .set(createTime).equalTo(row::getCreateTime)
            .set(updateTime).equalTo(row::getUpdateTime)
            .set(status).equalTo(row::getStatus)
            .where(id, isEqualTo(row::getId))
        );
    }

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    default int updateByPrimaryKeySelective(GoogleReserveIntegration row) {
        return update(c ->
            c.set(businessId).equalToWhenPresent(row::getBusinessId)
            .set(enabled).equalToWhenPresent(row::getEnabled)
            .set(createTime).equalToWhenPresent(row::getCreateTime)
            .set(updateTime).equalToWhenPresent(row::getUpdateTime)
            .set(status).equalToWhenPresent(row::getStatus)
            .where(id, isEqualTo(row::getId))
        );
    }
}