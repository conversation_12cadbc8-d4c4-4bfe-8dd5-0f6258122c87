package com.moego.svc.googlepartner.mapper;

import jakarta.annotation.Generated;
import java.sql.JDBCType;
import java.util.Date;
import org.mybatis.dynamic.sql.AliasableSqlTable;
import org.mybatis.dynamic.sql.SqlColumn;

public final class GoogleReserveIntegrationDynamicSqlSupport {
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    public static final GoogleReserveIntegration googleReserveIntegration = new GoogleReserveIntegration();

    /**
     * Database Column Remarks:
     *   id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.id")
    public static final SqlColumn<Long> id = googleReserveIntegration.id;

    /**
     * Database Column Remarks:
     *   business id
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.business_id")
    public static final SqlColumn<Integer> businessId = googleReserveIntegration.businessId;

    /**
     * Database Column Remarks:
     *   是否开启 Google Reserve
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.enabled")
    public static final SqlColumn<Boolean> enabled = googleReserveIntegration.enabled;

    /**
     * Database Column Remarks:
     *   create time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.create_time")
    public static final SqlColumn<Date> createTime = googleReserveIntegration.createTime;

    /**
     * Database Column Remarks:
     *   update time
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.update_time")
    public static final SqlColumn<Date> updateTime = googleReserveIntegration.updateTime;

    /**
     * Database Column Remarks:
     *   集成状态(GoogleReserveIntegrationStatus), 0-configured, 1-unmatched, 2-matched
     */
    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source field: google_reserve_integration.status")
    public static final SqlColumn<Integer> status = googleReserveIntegration.status;

    @Generated(value="org.mybatis.generator.api.MyBatisGenerator", comments="Source Table: google_reserve_integration")
    public static final class GoogleReserveIntegration extends AliasableSqlTable<GoogleReserveIntegration> {
        public final SqlColumn<Long> id = column("id", JDBCType.BIGINT);

        public final SqlColumn<Integer> businessId = column("business_id", JDBCType.INTEGER);

        public final SqlColumn<Boolean> enabled = column("enabled", JDBCType.BIT);

        public final SqlColumn<Date> createTime = column("create_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Date> updateTime = column("update_time", JDBCType.TIMESTAMP);

        public final SqlColumn<Integer> status = column("status", JDBCType.INTEGER);

        public GoogleReserveIntegration() {
            super("google_reserve_integration", GoogleReserveIntegration::new);
        }
    }
}