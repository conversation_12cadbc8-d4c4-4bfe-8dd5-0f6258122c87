package com.moego.svc.googlepartner.config;

import com.moego.svc.googlepartner.properties.SshProperties;
import net.schmizz.sshj.SSHClient;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnClass(SSHClient.class)
@ConditionalOnProperty(prefix = SshProperties.PREFIX, name = "enabled", matchIfMissing = true)
@EnableConfigurationProperties(SshProperties.class)
public class SshConfiguration {}
