package com.moego.svc.googlepartner.util;

import java.io.ByteArrayOutputStream;
import java.util.zip.GZIPOutputStream;
import lombok.experimental.UtilityClass;

/**
 * Utility class for compressing operations.
 *
 * <AUTHOR>
 * @since 2023/6/25
 */
@UtilityClass
public class CompressUtil {

    /**
     * Compress bytes with gzip.
     *
     * @param bytes bytes
     * @return compressed bytes
     */
    public static byte[] gzip(byte[] bytes) {
        if (bytes == null || bytes.length == 0) {
            return new byte[0];
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (GZIPOutputStream gzip = new GZIPOutputStream(out)) {
            gzip.write(bytes);
        } catch (Exception e) {
            throw new IllegalStateException("gzip compress error", e);
        }
        return out.toByteArray();
    }
}
