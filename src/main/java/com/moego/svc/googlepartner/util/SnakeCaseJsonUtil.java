package com.moego.svc.googlepartner.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.ObjectWriter;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import com.moego.svc.googlepartner.model.google.appointmentbooking.feed.Feed;
import lombok.experimental.UtilityClass;
import org.springframework.beans.BeanUtils;

/**
 * It is mainly used for JSON serialization of {@link Feed}, which needs to convert attribute names from camel case to underscore, and remove null value attributes.
 *
 * <AUTHOR>
 */
@UtilityClass
public class SnakeCaseJsonUtil {

    private static final ObjectMapper om = new ObjectMapper()
            .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE)
            .setDefaultPropertyInclusion(JsonInclude.Include.NON_NULL);

    private static final ObjectWriter ow = om.writerWithDefaultPrettyPrinter();

    /**
     * Write object to JSON string.
     *
     * @param obj object
     * @return JSON string
     */
    public static String toJson(Object obj) {
        return toJson(obj, false);
    }

    /**
     * Write object to JSON string.
     *
     * @param obj         object
     * @param prettyPrint pretty print
     * @return JSON string
     */
    public static String toJson(Object obj, boolean prettyPrint) {
        if (obj == null) {
            return null;
        }
        if (BeanUtils.isSimpleValueType(obj.getClass())) {
            return String.valueOf(obj);
        }
        try {
            if (prettyPrint) {
                return ow.writeValueAsString(obj);
            } else {
                return om.writeValueAsString(obj);
            }
        } catch (Exception e) {
            throw new IllegalArgumentException(e);
        }
    }
}
