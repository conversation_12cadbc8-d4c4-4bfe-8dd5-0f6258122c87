package com.moego.svc.googlepartner.util;

import java.lang.reflect.Array;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.function.Consumer;
import lombok.experimental.UtilityClass;
import org.springframework.util.ObjectUtils;

/**
 * <AUTHOR>
 */
@UtilityClass
public class Opt {

    /**
     * Consume object if it is not 'empty'.
     *
     * <p> 'empty' object includes:
     * <ul>
     *     <li>{@code null}</li>
     *     <li>empty {@link String}</li>
     *     <li>empty {@link Collection}</li>
     *     <li>empty {@link Array}</li>
     *     <li>empty {@link Map}</li>
     *     <li>empty {@link Optional}</li>
     * </ul>
     *
     * @param t        object
     * @param consumer consumer
     * @param <T>      object type
     * @see ObjectUtils#isEmpty(Object)
     */
    public static <T> void doOnNotEmpty(T t, Consumer<T> consumer) {
        if (!ObjectUtils.isEmpty(t)) {
            consumer.accept(t);
        }
    }
}
