package com.moego.svc.googlepartner.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.moego.svc.googlepartner.model.JsonSplitResult;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import lombok.experimental.UtilityClass;
import org.springframework.util.unit.DataSize;

/**
 * Utility class for splitting JSON into multiple pieces.
 *
 * <AUTHOR>
 */
@UtilityClass
public class JsonSplitter {
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Split the JSON array into multiple pieces.
     *
     * @param fileName        file name
     * @param jsonArrayString JSON array string
     * @param maxSize         maximum size of each piece
     * @return {@link JsonSplitResult}
     */
    public static JsonSplitResult splitJsonArray(String fileName, String jsonArrayString, DataSize maxSize) {
        ArrayNode arrayNode = parseJsonArray(jsonArrayString);

        JsonSplitResult splitResult = new JsonSplitResult();
        splitResult.setRawFileName(fileName);
        splitResult.setPieces(new ArrayList<>());

        int arraySize = arrayNode.size();
        long maxBytes = maxSize.toBytes();

        // Split the JSON array into multiple pieces
        int currentBytes = 0;
        ArrayNode subArrayNode = objectMapper.createArrayNode();
        List<String> subArrayJsonList = new ArrayList<>();

        for (int i = 0; i < arraySize; i++) {
            ObjectNode elementNode = (ObjectNode) arrayNode.get(i);
            String elementJson = toJsonString(elementNode);
            int elementBytes = elementJson.getBytes(StandardCharsets.UTF_8).length;

            // If adding the current element exceeds the maximum bytes, create a new piece
            if (currentBytes + elementBytes > maxBytes) {
                String subArrayJson = toJsonString(subArrayNode);
                String subFileName =
                        getSubFileName(fileName, splitResult.getPieces().size());

                // Create a new piece with the sub file name and JSON value
                JsonSplitResult.Piece piece = new JsonSplitResult.Piece();
                piece.setFileName(subFileName);
                piece.setJsonValue(subArrayJson);

                splitResult.getPieces().add(piece);

                // Reset the sub array node and current bytes count
                subArrayNode = objectMapper.createArrayNode();
                subArrayJsonList.clear();
                currentBytes = 0;
            }

            subArrayNode.add(elementNode);
            subArrayJsonList.add(elementJson);
            currentBytes += elementBytes;
        }

        // Handle the remaining elements as the last piece
        if (!subArrayJsonList.isEmpty()) {
            String subArrayJson = toJsonString(subArrayNode);
            String subFileName =
                    getSubFileName(fileName, splitResult.getPieces().size());

            // Create the last piece with the remaining elements
            JsonSplitResult.Piece piece = new JsonSplitResult.Piece();
            piece.setFileName(subFileName);
            piece.setJsonValue(subArrayJson);

            splitResult.getPieces().add(piece);
        }

        return splitResult;
    }

    private static ArrayNode parseJsonArray(String jsonArrayString) {
        try {
            return objectMapper.readValue(jsonArrayString, ArrayNode.class);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid JSON array string", e);
        }
    }

    private static String toJsonString(Object node) {
        try {
            return objectMapper.writeValueAsString(node);
        } catch (Exception e) {
            throw new IllegalArgumentException("Invalid JSON node", e);
        }
    }

    private static String getSubFileName(String fileName, int index) {
        String extension = "." + getFileExtension(fileName);
        return fileName.substring(0, fileName.length() - extension.length()) + "_" + index + extension;
    }

    private static String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf(".json");
        return (dotIndex == -1) ? "" : fileName.substring(dotIndex + 1);
    }
}
