package com.moego.svc.googlepartner.model.google;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @see <a href="https://developers.google.com/actions-center/verticals/appointments/redirect/partner-portal/inventory/merchant-status-query">Merchant Status Query</a>
 */
@Data
public class MerchantStatus {
    private String name;
    private String merchantName;
    private InputGeoInfo inputGeoInfo;
    private String processingStatus;
    private BookingStatus bookingStatus;
    private WaitlistStatus waitlistStatus;
    private GeoMatch geoMatch;
    private List<DirectUrl> directUrls;

    @Data
    public static class InputGeoInfo {
        @JsonProperty("unstructured_address")
        private String unstructuredAddress;
    }

    @Data
    public static class BookingStatus {
        private boolean hasValidFutureInventory;
    }

    @Data
    public static class WaitlistStatus {
        private boolean hasValidWaitlistService;
    }

    @Data
    public static class GeoMatch {
        private String name;
        private String formattedAddress;
        private String placeId;
    }

    @Data
    public static class DirectUrl {
        private String type;
        private String url;
    }
}
