package com.moego.svc.googlepartner.model;

import com.moego.svc.googlepartner.util.CompressUtil;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import lombok.Getter;
import net.schmizz.sshj.xfer.InMemorySourceFile;

/**
 * Json Gzip file implementation of {@link InMemorySourceFile}.
 *
 * <AUTHOR>
 */
public class JsonGzipFile extends InMemorySourceFile {

    private final String fileName;

    @Getter
    private final long rawLength;

    private final long compressedLength;
    private final InputStream inputStream;

    public JsonGzipFile(String fileName, String jsonString) {
        this.fileName = fileName.endsWith(".gz") ? fileName : (fileName + ".gz");
        byte[] bytes = jsonString.getBytes(StandardCharsets.UTF_8);
        this.rawLength = bytes.length;
        byte[] compressedBytes = CompressUtil.gzip(bytes);
        this.inputStream = new ByteArrayInputStream(compressedBytes);
        this.compressedLength = compressedBytes.length;
    }

    @Override
    public String getName() {
        return fileName;
    }

    @Override
    public long getLength() {
        return compressedLength;
    }

    @Override
    public InputStream getInputStream() {
        return inputStream;
    }
}
