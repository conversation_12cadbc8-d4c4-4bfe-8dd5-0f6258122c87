package com.moego.svc.googlepartner.model.google.appointmentbooking.feed;

import java.util.List;
import lombok.Data;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @see <a href="https://developers.google.com/maps-booking/verticals/appointment-booking/reference/feed-specifications/services-feed">services feed</a>
 */
@Data
@Accessors(chain = true)
public class ServiceFeed implements Feed {

    private List<Service> data;

    @Data
    @Accessors(chain = true)
    public static class Service {
        private String merchantId;
        private String serviceId;
        private Text localizedServiceName;
        private Text localizedServiceCategory;
        private Text localizedServiceDescription;
        private ServicePrice servicePrice;
        private List<ActionLink> actionLink;
        private ServiceDuration serviceDuration;
        private ServiceRankingHint rankingHint;
    }

    @Data
    @Accessors(chain = true)
    public static class Text {
        private String value;
        private List<LocalizedString> localizedValue;
    }

    @Getter
    @RequiredArgsConstructor
    public enum RangeInterpretation {
        INTERPRETATION_NOT_DISPLAYED(0),
        INTERPRETATION_EXACT(1),
        INTERPRETATION_STARTS_AT(2),
        INTERPRETATION_RANGE(3),
        ;

        private final int value;
    }

    @Data
    @Accessors(chain = true)
    public static class ServicePrice {
        private RangeInterpretation priceInterpretation;
        private Price minPrice;
        private Price maxPrice;
    }

    @Data
    @Accessors(chain = true)
    public static class Price {
        private Long priceMicros;
        private String currencyCode;
        private String pricingOptionTag;
    }

    @Data
    @Accessors(chain = true)
    public static class ActionLink {
        private String url;
    }

    @Data
    @Accessors(chain = true)
    public static class ServiceDuration {
        private RangeInterpretation durationInterpretation;
        private Long minDurationSec;
        private Long maxDurationSec;
    }

    @Data
    @Accessors(chain = true)
    public static class ServiceRankingHint {
        private Double score;
    }

    @Data
    @Accessors(chain = true)
    public static class LocalizedString {
        private String locale;
        private String value;
    }
}
