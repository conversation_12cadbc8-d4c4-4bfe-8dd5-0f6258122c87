package com.moego.svc.googlepartner.model;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import net.schmizz.sshj.xfer.InMemorySourceFile;

/**
 * Json file implementation of {@link InMemorySourceFile}.
 *
 * <AUTHOR>
 */
public class JsonFile extends InMemorySourceFile {

    private final String fileName;
    private final long length;
    private final InputStream inputStream;

    public JsonFile(String fileName, String jsonString) {
        this.fileName = fileName;
        byte[] bytes = jsonString.getBytes(StandardCharsets.UTF_8);
        this.length = bytes.length;
        this.inputStream = new ByteArrayInputStream(bytes);
    }

    @Override
    public String getName() {
        return fileName;
    }

    @Override
    public long getLength() {
        return length;
    }

    @Override
    public InputStream getInputStream() {
        return inputStream;
    }
}
