package com.moego.svc.googlepartner.model.google.appointmentbooking.feed;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @see <a href="https://developers.google.com/maps-booking/verticals/appointment-booking/reference/feed-specifications/action-feed">action feed</a>
 */
@Data
@Accessors(chain = true)
public class ActionFeed implements Feed {

    private List<ActionDetail> data;

    @Data
    @Accessors(chain = true)
    public static class ActionDetail {
        private String entityId;
        private List<Action> actions;
    }

    @Data
    @Accessors(chain = true)
    public static class Action {
        private AppointmentInfo appointmentInfo;
    }

    @Data
    @Accessors(chain = true)
    public static class AppointmentInfo {
        private String url;
    }
}
