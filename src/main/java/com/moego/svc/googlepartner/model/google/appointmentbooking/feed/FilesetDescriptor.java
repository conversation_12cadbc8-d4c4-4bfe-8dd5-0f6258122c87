package com.moego.svc.googlepartner.model.google.appointmentbooking.feed;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @see <a href="https://developers.google.com/maps-booking/verticals/appointment-booking/samples/appointment-booking-feed-samples/appointment-booking-entity#descriptor-file">descriptor file</a>
 * @see <a href="https://developers.google.com/maps-booking/verticals/healthcare/guides/tutorials/tutorial-generic-dropbox#structuring-the-descriptor-field">tutorial</a>
 */
@Data
@Accessors(chain = true)
public class FilesetDescriptor {
    public static final String FILE_EXTENSION = ".filesetdesc.json";
    public static final String ENTITY = "reservewithgoogle.entity";
    public static final String ACTION = "reservewithgoogle.action";
    public static final String SERVICE = "glam.service.v0";

    /**
     * Timestamp in seconds.
     */
    private Long generationTimestamp;

    private String name;
    private List<String> dataFile;
}
