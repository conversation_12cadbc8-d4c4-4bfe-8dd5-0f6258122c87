package com.moego.svc.googlepartner.model.google.appointmentbooking.feed;

import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @see <a href="https://developers.google.com/maps-booking/verticals/appointment-booking/reference/feed-specifications/entity-feed">entity feed</a>
 */
@Data
@Accessors(chain = true)
public class EntityFeed implements Feed {

    private List<Entity> data;

    @Data
    @Accessors(chain = true)
    public static class Entity {
        private String entityId;
        private String name;
        private String telephone;
        private String url;
        private GeoCoordinates location;
    }

    @Data
    @Accessors(chain = true)
    public static class GeoCoordinates {
        private Double latitude;
        private Double longitude;
        private PostalAddress address;
        private String unstructuredAddress;
    }

    @Data
    @Accessors(chain = true)
    public static class PostalAddress {
        private String country;
        private String locality;
        private String region;
        private String postalCode;
        private String streetAddress;
    }
}
