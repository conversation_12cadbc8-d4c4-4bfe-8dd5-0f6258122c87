package com.moego.svc.googlepartner.properties;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * <AUTHOR>
 */
@Data
@Validated
@ConfigurationProperties(GoogleReserveProperties.PREFIX)
public class GoogleReserveProperties {
    public static final String PREFIX = "moego.google-reserve";

    @NotBlank
    private String partnerId;

    @Valid
    private MerchantStatusQuery merchantStatusQuery = new MerchantStatusQuery();

    @Data
    public static class MerchantStatusQuery {
        @NotBlank
        private String credentialsPath;
    }
}
