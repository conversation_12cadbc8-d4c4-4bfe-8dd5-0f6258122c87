package com.moego.svc.googlepartner.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(SshProperties.PREFIX)
public class SshProperties {
    public static final String PREFIX = "moego.ssh";

    /**
     * Whether to enable SSH client autoconfiguration.
     */
    private boolean enabled = true;
    /**
     * Sandbox SSH connection configuration.
     */
    private Ssh sandbox = new Ssh();
    /**
     * Production SSH connection configuration.
     */
    private Ssh prod = new Ssh();

    @Data
    public static class Ssh {
        /**
         * Whether to enable SSH client autoconfiguration.
         */
        private boolean enabled = true;
        /**
         * Username to use.
         */
        private String username;
        /**
         * Hostname of the server.
         */
        private String host;
        /**
         * Port of the server, default is {@code 22}.
         */
        private int port = 22;
        /**
         * Private key location.
         *
         * <p> Examples:
         * <ul>
         *     <li>load from absolute path: {@code file:${user.home}/.ssh/id_rsa}</li>
         *     <li>load from classpath: {@code classpath:key/id_rsa}</li>
         * </ul>
         *
         * @see org.springframework.core.io.ResourceLoader#getResource(String)
         */
        private String privateKeyLocation;
    }
}
