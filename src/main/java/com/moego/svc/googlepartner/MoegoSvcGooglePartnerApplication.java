package com.moego.svc.googlepartner;

import static java.time.ZoneOffset.UTC;

import com.moego.server.business.client.IBusinessBusinessClient;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.grooming.client.IGroomingServiceCategoryClient;
import com.moego.server.grooming.client.IGroomingServiceClient;
import com.moego.server.message.client.INotificationClient;
import com.moego.server.payment.client.IPaymentPlanClient;
import com.moego.svc.googlepartner.properties.GoogleReserveProperties;
import java.util.TimeZone;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication
@EnableFeignClients(
        clients = {
            IBusinessBusinessClient.class,
            IGroomingServiceClient.class,
            IGroomingServiceCategoryClient.class,
            IGroomingOnlineBookingClient.class,
            IPaymentPlanClient.class,
            INotificationClient.class,
        })
@EnableConfigurationProperties({GoogleReserveProperties.class})
public class MoegoSvcGooglePartnerApplication {

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone(UTC));

        SpringApplication.run(MoegoSvcGooglePartnerApplication.class, args);
    }
}
