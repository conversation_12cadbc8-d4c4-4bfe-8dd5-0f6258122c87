package com.moego.svc.googlepartner.mapstruct;

import com.moego.idl.models.google_partner.v1.GoogleReserveIntegrationModel;
import com.moego.svc.googlepartner.entity.GoogleReserveIntegration;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

/**
 * <AUTHOR>
 */
@Mapper(unmappedTargetPolicy = ReportingPolicy.IGNORE)
public interface GoogleReserveIntegrationMapper {

    GoogleReserveIntegrationMapper INSTANCE = Mappers.getMapper(GoogleReserveIntegrationMapper.class);

    @Mapping(
            target = "createTime",
            expression = "java(com.google.protobuf.util.Timestamps.fromDate(entity.getCreateTime()))")
    @Mapping(
            target = "updateTime",
            expression = "java(com.google.protobuf.util.Timestamps.fromDate(entity.getUpdateTime()))")
    @Mapping(
            target = "status",
            expression =
                    "java(com.moego.idl.models.google_partner.v1.GoogleReserveIntegrationStatus.forNumber(entity.getStatus()))")
    GoogleReserveIntegrationModel entityToModel(GoogleReserveIntegration entity);
}
