package com.moego.svc.order.repository.mapper;

import com.moego.svc.order.repository.entity.DepositChangeLog;

public interface DepositChangeLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table deposit_change_log
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table deposit_change_log
     *
     * @mbg.generated
     */
    int insert(DepositChangeLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table deposit_change_log
     *
     * @mbg.generated
     */
    int insertSelective(DepositChangeLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table deposit_change_log
     *
     * @mbg.generated
     */
    DepositChangeLog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table deposit_change_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(DepositChangeLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table deposit_change_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(DepositChangeLog record);

    /**
     * Select latest DepositChangeLog (by create time) of a deposit order
     * @param depositOrderId Deposit order ID
     * @return DepositChangeLog
     */
    DepositChangeLog selectLatestByDepositOrderId(Long depositOrderId);
}