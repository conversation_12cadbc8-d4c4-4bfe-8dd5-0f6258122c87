package com.moego.svc.order.repository.mapper;

import com.moego.svc.order.repository.entity.ServiceCharge;
import com.moego.svc.order.repository.entity.ServiceChargeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface ServiceChargeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_charge
     *
     * @mbg.generated
     */
    long countByExample(ServiceChargeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_charge
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_charge
     *
     * @mbg.generated
     */
    int insert(ServiceCharge record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_charge
     *
     * @mbg.generated
     */
    int insertSelective(ServiceCharge record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_charge
     *
     * @mbg.generated
     */
    List<ServiceCharge> selectByExample(ServiceChargeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_charge
     *
     * @mbg.generated
     */
    ServiceCharge selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_charge
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") ServiceCharge record, @Param("example") ServiceChargeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_charge
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") ServiceCharge record, @Param("example") ServiceChargeExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_charge
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(ServiceCharge record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table service_charge
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(ServiceCharge record);

    //>>>>>>>>>> service charge  company update

    List<ServiceCharge> selectByBusinessIdAndIsActive(
            @Param("businessId") Long businessId,
            @Param("isActive") Boolean isActive,
            @Param("isMandatory") Boolean isMandatory,
            @Param("includedDeleted") Boolean includedDeleted);


    List<ServiceCharge> selectByFinalBusinessIdAndIsActive(
            @Param("companyId") Long comapnyId,
            @Param("businessId") Long businessId,
            @Param("isActive") Boolean isActive,
            @Param("isMandatory") Boolean isMandatory,
            @Param("includedDeleted") Boolean includedDeleted);

    ServiceCharge selectFinalByBid(
            @Param("companyId") Long companyId,
            @Param("businessId") Long businessId,
            @Param("serviceChargeId") Long serviceChargeId
    );

    List<ServiceCharge> selectByCidBidsAndIsActive(
            @Param("companyId") Long comapnyId,
            @Param("businessIds") List<Long> businessIds,
            @Param("isActive") Boolean isActive,
            @Param("isMandatory") Boolean isMandatory,
            @Param("includedDeleted") Boolean includedDeleted);


    List<ServiceCharge> selectByCidBidsAndIsActiveAndSurchargeType(
            @Param("companyId") Long companyId,
            @Param("businessIds") List<Long> businessIds,
            @Param("isActive") Boolean isActive,
            @Param("isMandatory") Boolean isMandatory,
            @Param("includedDeleted") Boolean includedDeleted,
            @Param("surchargeType") Integer surchargeType);

    Integer countByBusinessIdAndName(
            @Param("businessId") Long businessId, @Param("name") String name, @Param("exceptId") Long exceptId);

    Integer countByCompanyIdAndName(
            @Param("companyId") Long companyId, @Param("name") String name, @Param("exceptId") Long exceptId);

    List<ServiceCharge> selectByBusinessIdAndIds(@Param("businessId") Long businessId, @Param("ids") List<Long> ids);

    List<ServiceCharge> selectByCidBidAndIds(@Param("companyId") Long companyId, @Param("businessId") Long businessId, @Param("ids") List<Long> ids);


    Integer batchDeleteByBusinessIdAndId(
            @Param("businessId") Long businessId,
            @Param("updatedBy") Long updatedBy,
            @Param("deleteIdList") List<Long> deleteIdList);

    Integer batchDeleteByBidCidAndId(
            @Param("companyId") Long companyId,
            @Param("businessId") Long businessId,
            @Param("updatedBy") Long updatedBy,
            @Param("deleteIdList") List<Long> deleteIdList);


    //service charge  company update <<<<<<<<<<

    Integer batchUpdateSortById(@Param("updateList") List<ServiceCharge> updateList);

    int updateSetBusinessZero(@Param("companyId") Long companyId);
}