package com.moego.svc.order.repository.mapper;

import com.moego.svc.order.repository.entity.OrderStaffSplitDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderStaffSplitDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_staff_split_detail
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_staff_split_detail
     *
     * @mbg.generated
     */
    int insert(OrderStaffSplitDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_staff_split_detail
     *
     * @mbg.generated
     */
    int insertSelective(OrderStaffSplitDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_staff_split_detail
     *
     * @mbg.generated
     */
    OrderStaffSplitDetail selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_staff_split_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(OrderStaffSplitDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_staff_split_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(OrderStaffSplitDetail record);

    /**
     * 根据order id和order item id查询所有staff split 详情
     *
     * @param orderId     Long
     * @param orderItemId Long
     * @return List<OrderStaffSplitDetail>
     */
    List<OrderStaffSplitDetail> selectByOrderIdAndItemId(@Param("orderId") Long orderId, @Param("orderItemId") Long orderItemId, @Param("type") String type);

    /**
     * 根据order id 查询所有staff split 详情
     *
     * @param orderId Long
     * @return List<OrderStaffSplitDetail>
     */
    List<OrderStaffSplitDetail> selectByOrderId(@Param("orderId")Long orderId);
}