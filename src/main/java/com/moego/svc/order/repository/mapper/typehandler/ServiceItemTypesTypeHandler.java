package com.moego.svc.order.repository.mapper.typehandler;

import com.moego.idl.models.offering.v1.ServiceItemType;
import java.sql.Array;
import java.sql.CallableStatement;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

@MappedJdbcTypes(JdbcType.ARRAY)
@MappedTypes(List.class)
public class ServiceItemTypesTypeHandler extends BaseTypeHandler<List<ServiceItemType>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<ServiceItemType> parameter, JdbcType jdbcType)
            throws SQLException {
        Connection conn = ps.getConnection();

        // 将ServiceItemType转换为Integer数组
        Integer[] numbers = parameter.stream().map(ServiceItemType::getNumber).toArray(Integer[]::new);

        // 创建PostgreSQL数组
        Array array = conn.createArrayOf("smallint", numbers);
        ps.setArray(i, array);
    }

    @Override
    public List<ServiceItemType> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        Array array = rs.getArray(columnName);
        return arrayToList(array);
    }

    @Override
    public List<ServiceItemType> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        Array array = rs.getArray(columnIndex);
        return arrayToList(array);
    }

    @Override
    public List<ServiceItemType> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        Array array = cs.getArray(columnIndex);
        return arrayToList(array);
    }

    private List<ServiceItemType> arrayToList(Array array) throws SQLException {
        if (array == null) {
            return null;
        }

        Object[] numbers = (Object[]) array.getArray();
        return Arrays.stream(numbers)
                .map(obj -> ((Number) obj).intValue())
                .map(ServiceItemType::forNumber)
                .filter(type -> type != null && type != ServiceItemType.UNRECOGNIZED)
                .collect(Collectors.toList());
    }
}
