package com.moego.svc.order.repository.mapper;

import com.moego.svc.order.repository.entity.OrderGroomingDetailRel;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderGroomingDetailRelMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_grooming_detail_rel
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_grooming_detail_rel
     *
     * @mbg.generated
     */
    int insert(OrderGroomingDetailRel record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_grooming_detail_rel
     *
     * @mbg.generated
     */
    int insertSelective(OrderGroomingDetailRel record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_grooming_detail_rel
     *
     * @mbg.generated
     */
    OrderGroomingDetailRel selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_grooming_detail_rel
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(OrderGroomingDetailRel record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_grooming_detail_rel
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(OrderGroomingDetailRel record);

    /**
     * 根据order id 查询所有关联关系
     *
     * @param orderIdList order id list
     * @return List<OrderGroomingDetailRel>
     */
    List<OrderGroomingDetailRel> selectByOrderIdList(@Param("list") List<Long> orderIdList);
    OrderGroomingDetailRel selectByOrderIdAndDetailId(@Param("orderId") Long orderId, @Param("petDetailId") Long petDetailId);
}