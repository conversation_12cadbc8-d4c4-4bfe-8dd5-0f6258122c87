package com.moego.svc.order.repository.mapper;

import com.moego.svc.order.repository.entity.OrderLineExtraFee;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OrderLineExtraFeeMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_extra_fee
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_extra_fee
     *
     * @mbg.generated
     */
    int insert(OrderLineExtraFee record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_extra_fee
     *
     * @mbg.generated
     */
    int insertSelective(OrderLineExtraFee record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_extra_fee
     *
     * @mbg.generated
     */
    OrderLineExtraFee selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_extra_fee
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(OrderLineExtraFee record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_extra_fee
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(OrderLineExtraFee record);

    List<OrderLineExtraFee> selectByOrderId(@Param("orderId") Long orderId);

    int batchInsertRecords(@Param("records") List<OrderLineExtraFee> records);
}
