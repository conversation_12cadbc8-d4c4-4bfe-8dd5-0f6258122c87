package com.moego.svc.order.repository.mapper.typehandler;

import com.google.protobuf.ProtocolMessageEnum;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;

/**
 * 将 Protobuf 枚举（ProtocolMessageEnum）转换为数据库中的整数类型。
 *
 * <p> 参考实现：
 * <ul>
 *   <li> {@link com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler} </li>
 *   <li> {@link com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler} </li>
 * </ul>
 *
 * <AUTHOR>
 * @since 2024/9/24
 */
public abstract class ProtocolMessageEnumTypeHandler<T extends ProtocolMessageEnum> extends BaseTypeHandler<T> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {
        ps.setInt(i, parameter.getNumber());
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return fromInt(rs.getInt(columnName), rs.wasNull());
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return fromInt(rs.getInt(columnIndex), rs.wasNull());
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return fromInt(cs.getInt(columnIndex), cs.wasNull());
    }

    /**
     * Get the enum value from the number.
     *
     * @param number the number of the enum
     * @param wasNull whether the value was null
     * @return the enum value
     */
    public abstract T fromInt(int number, boolean wasNull);
}
