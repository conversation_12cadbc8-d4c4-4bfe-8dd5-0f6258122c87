package com.moego.svc.order.repository.mapper;

import com.moego.svc.order.repository.entity.OrderTipSplitRecord;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OrderTipSplitRecordMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_tip_split_record
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_tip_split_record
     *
     * @mbg.generated
     */
    int insert(OrderTipSplitRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_tip_split_record
     *
     * @mbg.generated
     */
    int insertSelective(OrderTipSplitRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_tip_split_record
     *
     * @mbg.generated
     */
    OrderTipSplitRecord selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_tip_split_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(OrderTipSplitRecord record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_tip_split_record
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(OrderTipSplitRecord record);

    OrderTipSplitRecord selectByBusinessIdAndOrderId(
            @Param("businessId") Long businessId, @Param("orderId") Long orderId);

    List<OrderTipSplitRecord> selectByBusinessIdsAndOrderIds(
            @Param("businessIds") List<Long> businessIds, @Param("orderIds") List<Long> orderIds);

    int updateByBusinessIdAndOrderId(OrderTipSplitRecord record);
}