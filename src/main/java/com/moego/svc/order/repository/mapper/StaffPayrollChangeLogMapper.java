package com.moego.svc.order.repository.mapper;

import com.moego.svc.order.repository.entity.StaffPayrollChangeLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface StaffPayrollChangeLogMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.staff_payroll_change_log
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.staff_payroll_change_log
     *
     * @mbg.generated
     */
    int insert(StaffPayrollChangeLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.staff_payroll_change_log
     *
     * @mbg.generated
     */
    int insertSelective(StaffPayrollChangeLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.staff_payroll_change_log
     *
     * @mbg.generated
     */
    StaffPayrollChangeLog selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.staff_payroll_change_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(StaffPayrollChangeLog record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.staff_payroll_change_log
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(StaffPayrollChangeLog record);

    /**
     * 批量新增change log
     * @param recordList change log 列表
     * @return effect count
     */
    int batchInsert(@Param("list") List<StaffPayrollChangeLog> recordList);
}