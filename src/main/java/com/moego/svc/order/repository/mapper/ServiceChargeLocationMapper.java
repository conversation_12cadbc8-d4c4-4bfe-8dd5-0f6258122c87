package com.moego.svc.order.repository.mapper;

import com.moego.svc.order.repository.entity.ServiceChargeLocation;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ServiceChargeLocationMapper {
    int deleteByPrimaryKey(Long id);

    int insert(ServiceChargeLocation record);

    int insertSelective(ServiceChargeLocation record);

    ServiceChargeLocation selectByPrimaryKey(Long id);

    int updateByPrimaryKeySelective(ServiceChargeLocation record);

    int updateByPrimaryKey(ServiceChargeLocation record);


    List<ServiceChargeLocation> selectByCidAndServiceChargeIds(
            @Param("companyId") Long companyId,
            @Param("serviceChargeIds") List<Long> serviceChargeIds);

    List<ServiceChargeLocation> selectAllWithDeletedByServiceChargeId(@Param("companyId") Long companyId, @Param("serviceChargeId") Long serviceChargeId);
    ServiceChargeLocation selectOneWithDeletedByServiceChargeId(@Param("companyId") Long companyId, @Param("businessId") Long businessId, @Param("serviceChargeId") Long serviceChargeId);

    void updateSetDeletedByUniqueIndex(@Param("companyId") Long companyId, @Param("businessId") Long businessId, @Param("serviceChargeId") Long serviceChargeId);
}