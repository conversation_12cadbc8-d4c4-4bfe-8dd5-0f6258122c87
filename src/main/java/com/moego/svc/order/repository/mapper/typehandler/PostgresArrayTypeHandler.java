package com.moego.svc.order.repository.mapper.typehandler;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;

@MappedTypes(List.class)
public class PostgresArrayTypeHandler extends BaseTypeHandler<List<Long>> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, List<Long> parameter, JdbcType jdbcType)
            throws SQLException {
        ps.setArray(i, ps.getConnection().createArrayOf("bigint", parameter.toArray()));
    }

    @Override
    public List<Long> getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return arrayToList(rs.getArray(columnName));
    }

    @Override
    public List<Long> getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return arrayToList(rs.getArray(columnIndex));
    }

    @Override
    public List<Long> getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return arrayToList(cs.getArray(columnIndex));
    }

    private List<Long> arrayToList(java.sql.Array array) throws SQLException {
        if (array == null) {
            return new ArrayList<>();
        }

        Object[] elements = (Object[]) array.getArray();
        List<Long> result = new ArrayList<>(elements.length);

        for (Object element : elements) {
            if (element != null) {
                result.add(((Number) element).longValue());
            }
        }

        return result;
    }
}
