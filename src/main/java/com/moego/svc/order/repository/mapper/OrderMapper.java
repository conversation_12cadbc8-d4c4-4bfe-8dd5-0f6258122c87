package com.moego.svc.order.repository.mapper;

import com.moego.svc.order.model.dto.CustomerPaymentSummaryDTO;
import com.moego.svc.order.model.dto.OrderExtraOrderCountDTO;
import com.moego.svc.order.model.params.GetCustomerItemsParams;
import com.moego.svc.order.model.params.GetCustomerOrdersParams;
import com.moego.svc.order.repository.entity.Order;
import com.moego.svc.order.repository.entity.OrderLineItem;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order
     *
     * @mbg.generated
     */
    int insert(Order record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order
     *
     * @mbg.generated
     */
    int insertSelective(Order record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order
     *
     * @mbg.generated
     */
    Order selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(Order record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(Order record);

    List<Order> selectByIds(@Param("businessId") Long businessId, @Param("ids") List<Long> id);

    List<Order> selectByIdsAndStatus(
            @Param("businessId") Long businessId,
            @Param("ids") List<Long> id,
            @Param("statusList") List<Integer> statusList);

    List<Order> selectOrdersPagination(
            @Param("businessIds") List<Long> businessIds,
            @Param("ids") List<Long> ids,
            @Param("statusList") List<Integer> statusList,
            @Param("pageSize") int pageSize,
            @Param("pageNum") int pageNum,
            @Param("lastUpdatedStartTime") Long lastUpdatedStartTime,
            @Param("lastUpdatedEndTime") Long lastUpdatedEndTime
    );

    Integer selectOrdersCount(
            @Param("businessIds") List<Long> businessIds,
            @Param("statusList") List<Integer> statusList,
            @Param("ids") List<Long> ids,
            @Param("lastUpdatedStartTime") Long lastUpdatedStartTime,
            @Param("lastUpdatedEndTime") Long lastUpdatedEndTime
    );

    //fixed: 新增 order type 后可以查出多条，增加order type ！= EXTRA 查询条件
    Order selectBySourceTypeAndSourceId(
            @Param("sourceType") String sourceType,
            @Param("sourceId") Long sourceId);
    Order selectLatestOrderBySourceTypeAndSourceId(
            @Param("sourceType") String sourceType,
            @Param("sourceId") Long sourceId);

    //新增 order type 后是否需要查出 extra order: 旧方法屏蔽掉
    List<Order> selectBySourceTypeAndSourceIds(
            @Param("businessId") Long businessId,
            @Param("sourceType") String sourceType,
            @Param("sourceIds") List<Long> sourceId,
            @Param("includeExtraOrder") boolean includeExtraOrder);

    Order selectByGuid(@Param("businessId") Long businessId, @Param("guid") String guid);

    int updateByBusinessIdAndId(Order record);

    List<Order> selectCustomerOrders(@Param("params") GetCustomerOrdersParams params);

    List<OrderLineItem> selectWithItems(@Param("params") GetCustomerItemsParams params);

    List<CustomerPaymentSummaryDTO> selectCustomerPaymentSummaries(
            @Param("businessId") Long businessId,
            @Param("customerIds") List<Long> customerIds,
            @Param("sourceTypes") List<String> sourceTypes,
            @Param("status") Integer status);

    List<CustomerPaymentSummaryDTO> selectCustomerRemainAmount(
            @Param("businessId") Long businessId,
            @Param("sourceIds") List<Long> sourceIds,
            @Param("sourceTypes") List<String> sourceTypes,
            @Param("status") Integer status);

    Integer selectCountByTypesAndKeyword(
            @Param("businessId") Long businessId,
            @Param("customerId") Long customerId,
            @Param("lineItemTypes") List<Integer> lineItemTypes,
            @Param("keyword") String keyword);

    List<Order> selectPageByTypesAndKeyword(
            @Param("businessId") Long businessId,
            @Param("customerId") Long customerId,
            @Param("lineItemTypes") List<Integer> lineItemTypes,
            @Param("keyword") String keyword,
            @Param("sortBy") String sortBy,
            @Param("orderBy") String orderBy,
            @Param("offset") Integer offset,
            @Param("size") Integer size);

    Integer selectCountByCreateTime(
            @Param("businessId") Long businessId,
            @Param("customerId") Long customerId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("lineItemTypes") List<Integer> lineItemTypes);

    List<Order> selectByCreateTime(
            @Param("businessId") Long businessId,
            @Param("customerId") Long customerId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime,
            @Param("lineItemTypes") List<Integer> lineItemTypes);

    Integer selectTipsOrderCount(
            @Param("businessId") Long businessId,
            @Param("sourceIds") List<Long> sourceIds,
            @Param("sourceType") String sourceType);

    Integer selectTipsOrderCountV2(
            @Param("businessIds") List<Long> businessIds,
            @Param("sourceIds") List<Long> sourceIds,
            @Param("sourceType") String sourceType);

    List<Order> selectPageTipsOrder(
            @Param("businessId") Long businessId,
            @Param("sourceIds") List<Long> sourceIds,
            @Param("sourceType") String sourceType,
            @Param("offset") Integer offset,
            @Param("size") Integer size);

    List<Order> selectPageTipsOrderV2(
            @Param("businessIds") List<Long> businessIds,
            @Param("sourceIds") List<Long> sourceIds,
            @Param("sourceType") String sourceType,
            @Param("offset") Integer offset,
            @Param("size") Integer size);

    List<Long> getOrderIdsBySourceIdsAndType(
            @Param("businessId") Long businessId,
            @Param("sourceIds") List<Long> sourceIds,
            @Param("sourceType") String sourceType);

    /**
     * 根据原单id查询
     *
     * @param originOrderId 原单id
     * @return List<Order>
     */
    List<Order> selectByBusinessIdAndOriginId(@Param("businessId") Long businessId,
                                              @Param("originOrderId") long originOrderId);

    /**
     * 根据order id 统计 extra order 数量
     *
     * @param orderIdList order id 列表
     * @return List<OrderExtraOrderCountDTO>
     */
    List<OrderExtraOrderCountDTO> selectExtraOrderCountByOrderIdList(@Param("list") List<Long> orderIdList);

    /**
     * 根据origin order id查询所有的extra order id
     *
     * @param originOrderId origin order id
     * @return List<Long>
     */
    List<Long> selectExtraOrderIdListByOriginId(@Param("originOrderId") Long originOrderId);

    /**
     * 批量更新全部oldCustomerList 到 newCustomerId
     * @param oldCustomerIdList
     * @param newCustomerId
     * @param limit
     * @return
     */
    int updateCustomerId(@Param("oldCustomerIdList") List<Long> oldCustomerIdList,
                        @Param("newCustomerId") Long newCustomerId, @Param("limit") int limit);

    /**
     * 根据business id 和 create time 范围查询order id
     *
     * @param businessId business id
     * @param startTime start create time
     * @param endTime end create time
     * @return order id list
     */
    List<Long> selectIdByBizIdAndCreateTime(
            @Param("businessId") Long businessId,
            @Param("startTime") Long startTime,
            @Param("endTime") Long endTime
    );

    /**
     * 根据 source type 和 source id 查询有效的 deposit order（可能支付或未支付）。
     * @param sourceType Source type
     * @param sourceId Source ID
     * @return Order or null
     */
    Order selectValidDepositOrderBySource(@Param("sourceType") String sourceType, @Param("sourceId") Long sourceId);

    /**
     * 查找 appointment 下面关联的所有的 order，用于查找头单填充到新单的 ref 里。
     * @param businessId Business ID
     * @param sourceId Source ID
     * @return order list
     */
    List<Order> selectByAppointmentForUpdate(@Param("businessId") Long businessId, @Param("sourceId") Long sourceId);
}
