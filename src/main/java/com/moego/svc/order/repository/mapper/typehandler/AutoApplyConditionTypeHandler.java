package com.moego.svc.order.repository.mapper.typehandler;

import com.moego.idl.models.order.v1.ServiceCharge;

/**
 * <AUTHOR>
 * @since 2024/9/24
 */
public class AutoApplyConditionTypeHandler extends ProtocolMessageEnumTypeHandler<ServiceCharge.AutoApplyCondition> {

    @Override
    public ServiceCharge.AutoApplyCondition fromInt(int number, boolean wasNull) {
        if (number == 0 && wasNull) {
            return ServiceCharge.AutoApplyCondition.AUTO_APPLY_CONDITION_UNSPECIFIED;
        }
        var type = ServiceCharge.AutoApplyCondition.forNumber(number);
        return type != null ? type : ServiceCharge.AutoApplyCondition.UNRECOGNIZED;
    }
}
