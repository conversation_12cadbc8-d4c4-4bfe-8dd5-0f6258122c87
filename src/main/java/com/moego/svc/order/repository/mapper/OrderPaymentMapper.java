package com.moego.svc.order.repository.mapper;

import com.moego.svc.order.repository.entity.OrderPayment;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderPaymentMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_payment
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_payment
     *
     * @mbg.generated
     */
    int insert(OrderPayment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_payment
     *
     * @mbg.generated
     */
    int insertSelective(OrderPayment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_payment
     *
     * @mbg.generated
     */
    OrderPayment selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_payment
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(OrderPayment record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_payment
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(OrderPayment record);

    List<OrderPayment> selectByPaymentStatusAndLimit(@Param("paymentStatus") String paymentStatus,
                                                     @Param("limit") Integer limit,
                                                     @Param("beforeMinutes") Integer beforeMinutes);

    OrderPayment selectByOrderIdAndPaymentId(@Param("orderId") Long orderId, @Param("paymentId") Long paymentId);

}