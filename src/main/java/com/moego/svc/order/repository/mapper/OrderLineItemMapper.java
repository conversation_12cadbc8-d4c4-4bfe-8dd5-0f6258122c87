package com.moego.svc.order.repository.mapper;

import com.moego.svc.order.repository.entity.OrderLineItem;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OrderLineItemMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_item
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_item
     *
     * @mbg.generated
     */
    int insert(OrderLineItem record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_item
     *
     * @mbg.generated
     */
    int insertSelective(OrderLineItem record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_item
     *
     * @mbg.generated
     */
    OrderLineItem selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_item
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(OrderLineItem record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_item
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(OrderLineItem record);

    List<OrderLineItem> selectByOrderId(@Param("orderId") Long orderId);

    List<OrderLineItem> selectByOrderItemIds(@Param("orderItemIds") List<Long> orderItemIds);

    List<OrderLineItem> selectByOrderIdsAndType(@Param("orderIds") List<Long> orderIds, @Param("type") String type);

    List<OrderLineItem> selectByObjectIdAndType(
            @Param("orderId") Long orderId,
            @Param("objectIds") List<Long> objectIds,
            @Param("type") String type);

}