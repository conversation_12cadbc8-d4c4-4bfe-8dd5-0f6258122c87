package com.moego.svc.order.repository.mapper;

import com.moego.svc.order.repository.entity.OrderTipsSplitDetail;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface OrderTipsSplitDetailMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_tips_split_detail
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_tips_split_detail
     *
     * @mbg.generated
     */
    int insert(OrderTipsSplitDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_tips_split_detail
     *
     * @mbg.generated
     */
    int insertSelective(OrderTipsSplitDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_tips_split_detail
     *
     * @mbg.generated
     */
    OrderTipsSplitDetail selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_tips_split_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(OrderTipsSplitDetail record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table public.order_tips_split_detail
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(OrderTipsSplitDetail record);

    /**
     * 根据订单id查询tips拆分详情
     *
     * @param orderId 订单id
     * @return List<OrderTipsSplitDetail>
     */
    List<OrderTipsSplitDetail> selectByOrderId(@Param("orderId") Long orderId);
}