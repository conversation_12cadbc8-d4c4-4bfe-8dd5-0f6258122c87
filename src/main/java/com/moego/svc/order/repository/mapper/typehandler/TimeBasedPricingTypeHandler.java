package com.moego.svc.order.repository.mapper.typehandler;

import com.moego.idl.models.order.v1.ServiceCharge;

public class TimeBasedPricingTypeHandler extends ProtocolMessageEnumTypeHandler<ServiceCharge.TimeBasedPricingType> {

    @Override
    public ServiceCharge.TimeBasedPricingType fromInt(int number, boolean wasNull) {
        if (number == 0 && wasNull) {
            return ServiceCharge.TimeBasedPricingType.FLAT_RATE;
        }
        var type = ServiceCharge.TimeBasedPricingType.forNumber(number);
        return type != null ? type : ServiceCharge.TimeBasedPricingType.UNRECOGNIZED;
    }
}
