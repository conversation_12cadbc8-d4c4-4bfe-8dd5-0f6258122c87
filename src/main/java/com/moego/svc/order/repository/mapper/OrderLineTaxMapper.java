package com.moego.svc.order.repository.mapper;

import com.moego.svc.order.repository.entity.OrderLineTax;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface OrderLineTaxMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_tax
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_tax
     *
     * @mbg.generated
     */
    int insert(OrderLineTax record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_tax
     *
     * @mbg.generated
     */
    int insertSelective(OrderLineTax record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_tax
     *
     * @mbg.generated
     */
    OrderLineTax selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_tax
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(OrderLineTax record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_tax
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(OrderLineTax record);

    List<OrderLineTax> selectByOrderId(@Param("orderId") Long orderId);

    List<OrderLineTax> selectByOrderItemIds(@Param("orderItemIds") List<Long> orderItemIds);

    int batchInsertRecords(@Param("records") List<OrderLineTax> records);

}