package com.moego.svc.order.repository.mapper;

import com.moego.svc.order.repository.entity.MessageDelivery;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MessageDeliveryMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_delivery
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_delivery
     *
     * @mbg.generated
     */
    int insert(MessageDelivery record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_delivery
     *
     * @mbg.generated
     */
    int insertSelective(MessageDelivery record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_delivery
     *
     * @mbg.generated
     */
    MessageDelivery selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_delivery
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(MessageDelivery record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table message_delivery
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(MessageDelivery record);

    /**
     * 根据message 类型和关联id查询
     *
     * @param messageType message类型
     * @param referenceId 关联id
     * @return MessageDelivery
     */
    MessageDelivery selectByMsgTypeAndRefId(@Param("messageType") String messageType, @Param("referenceId") String referenceId);

    /**
     * 根据主键加重试次数
     *
     * @param messageType message类型
     * @param referenceId 关联id
     * @return 影响行数
     */
    int addRetryCountByMsgTypeAndRefId(@Param("messageType") String messageType, @Param("referenceId") String referenceId);

    /**
     * 根据状态查询
     *
     * @param status 状态
     * @return List<MessageDelivery>
     */
    List<MessageDelivery> selectByStatus(@Param("status") String status);
}