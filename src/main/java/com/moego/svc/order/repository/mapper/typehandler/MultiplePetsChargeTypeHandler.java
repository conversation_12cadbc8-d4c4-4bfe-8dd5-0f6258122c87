package com.moego.svc.order.repository.mapper.typehandler;

import com.moego.idl.models.order.v1.ServiceCharge;

public class MultiplePetsChargeTypeHandler
        extends ProtocolMessageEnumTypeHandler<ServiceCharge.MultiplePetsChargeType> {

    @Override
    public ServiceCharge.MultiplePetsChargeType fromInt(int number, boolean wasNull) {
        if (number == 0 && wasNull) {
            return ServiceCharge.MultiplePetsChargeType.SAME_CHARGE_PER_PET;
        }
        var type = ServiceCharge.MultiplePetsChargeType.forNumber(number);
        return type != null ? type : ServiceCharge.MultiplePetsChargeType.UNRECOGNIZED;
    }
}
