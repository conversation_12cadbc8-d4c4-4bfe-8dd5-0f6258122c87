package com.moego.svc.order.repository.mapper;

import com.moego.svc.order.repository.entity.OrderLineDiscount;
import java.util.List;
import java.util.Map;

import org.apache.ibatis.annotations.Param;

public interface OrderLineDiscountMapper {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_discount
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_discount
     *
     * @mbg.generated
     */
    int insert(OrderLineDiscount record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_discount
     *
     * @mbg.generated
     */
    int insertSelective(OrderLineDiscount record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_discount
     *
     * @mbg.generated
     */
    OrderLineDiscount selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_discount
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(OrderLineDiscount record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table order_line_discount
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(OrderLineDiscount record);

    List<OrderLineDiscount> selectByOrderId(@Param("orderId") Long orderId);

    int batchInsertRecords(@Param("records") List<OrderLineDiscount> records);

    int updateDiscountCodeId(@Param("discountCodeIdMap") Map<Long, Long> discountCodeIdMap);

    int batchUpdateByPrimaryKeySelective(@Param("list") List<OrderLineDiscount> record);
}
