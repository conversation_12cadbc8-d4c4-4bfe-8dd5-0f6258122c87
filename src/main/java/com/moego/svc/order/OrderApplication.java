package com.moego.svc.order;

import com.moego.lib.common.util.ThreadPoolUtil;
import java.time.Duration;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.ThreadPoolExecutor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;

@EnableFeignClients({
    "com.moego.server.business.client",
    "com.moego.server.grooming.client",
    "com.moego.server.payment.client"
})
@SpringBootApplication
@MapperScan("com.moego.svc.order.repository.mapper")
public class OrderApplication {

    public static void main(String[] args) {
        SpringApplication.run(OrderApplication.class, args);
    }

    @Bean(name = "orderExecutorService")
    public ExecutorService orderExecutorService() {
        return ThreadPoolUtil.newExecutorService(
                10, 100, Duration.ofSeconds(60), 100, "order-batch-query-", new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
