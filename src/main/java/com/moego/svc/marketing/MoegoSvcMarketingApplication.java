package com.moego.svc.marketing;

import static java.time.ZoneOffset.UTC;

import com.moego.svc.marketing.service.DiscountCodeService;
import java.util.TimeZone;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;

@EnableFeignClients({
    "com.moego.server.customer.client",
    "com.moego.server.business.client",
    "com.moego.server.grooming.client",
    "com.moego.server.retail.client",
})
@SpringBootApplication
@MapperScan("com.moego.svc.marketing.mapper")
public class MoegoSvcMarketingApplication {

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone(UTC));

        SpringApplication.run(MoegoSvcMarketingApplication.class, args);
    }

    @Bean
    public ApplicationRunner runner(DiscountCodeService discountCodeService) {
        return args -> {
            //            discountCodeService.refreshDiscountCodeStatus();
        };
    }
}
