package com.moego.svc.marketing.mapper;

import com.moego.svc.marketing.dto.DiscountSalesResult;
import com.moego.svc.marketing.entity.DiscountCodeLog;
import com.moego.svc.marketing.entity.DiscountCodeLogExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DiscountCodeLogMapper {
    long countByExample(DiscountCodeLogExample example);

    int deleteByExample(DiscountCodeLogExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DiscountCodeLog record);

    int insertOrUpdate(DiscountCodeLog record);

    int insertOrUpdateSelective(DiscountCodeLog record);

    int insertSelective(DiscountCodeLog record);

    List<DiscountCodeLog> selectByExample(DiscountCodeLogExample example);

    DiscountCodeLog selectByPrimaryKey(Long id);

    int updateByExampleSelective(
            @Param("record") DiscountCodeLog record, @Param("example") DiscountCodeLogExample example);

    int updateByExample(@Param("record") DiscountCodeLog record, @Param("example") DiscountCodeLogExample example);

    int updateByPrimaryKeySelective(DiscountCodeLog record);

    int updateByPrimaryKey(DiscountCodeLog record);

    DiscountSalesResult countByDiscountIdAndBusinessId(
            @Param("discountId") Long discountId, @Param("businessId") Long businessId);

    DiscountSalesResult countByDiscountIdAndCompanyId(
            @Param("discountId") Long discountId, @Param("companyId") Long companyId);

    void batchInsert(@Param("list") List<DiscountCodeLog> discountCodeLogList);

    List<DiscountCodeLog> selectByRedeemId(Long redeemId);

    List<DiscountCodeLog> selectByOrderId(Long orderId);

    List<DiscountCodeLog> selectByCompanyIdWithPage(
            @Param("companyId") Long companyId,
            @Param("clientIds") List<Long> clientIds,
            @Param("limit") Integer limit,
            @Param("offset") Integer offset);
}
