package com.moego.svc.marketing.mapper;

import com.moego.svc.marketing.entity.DiscountCode;
import com.moego.svc.marketing.entity.DiscountCodeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface DiscountCodeMapper {
    long countByExample(DiscountCodeExample example);

    int deleteByExample(DiscountCodeExample example);

    int deleteByPrimaryKey(Long id);

    int insert(DiscountCode record);

    int insertSelective(DiscountCode record);

    List<DiscountCode> selectByExample(DiscountCodeExample example);

    DiscountCode selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") DiscountCode record, @Param("example") DiscountCodeExample example);

    int updateByExample(@Param("record") DiscountCode record, @Param("example") DiscountCodeExample example);

    int updateByPrimaryKeySelective(DiscountCode record);

    int updateByPrimaryKey(DiscountCode record);

    List<Long> selectActiveAndInactiveBusinessId();

    List<Long> selectActiveAndInactiveCompanyId();

    int updateDiscountCodesBatch(@Param("list") List<DiscountCode> list);

    void batchUpdateUsage(@Param("list") List<DiscountCode> codeList);

    List<DiscountCode> selectInactiveDiscountCodeList(@Param("list") List<DiscountCode> list);

    List<DiscountCode> selectByIds(@Param("list") List<Long> list);

    List<DiscountCode> selectByCompanyIdWithPage(
            @Param("companyId") Long companyId, @Param("limit") Integer limit, @Param("offset") Integer offset);
}
