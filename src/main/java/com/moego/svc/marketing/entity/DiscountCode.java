package com.moego.svc.marketing.entity;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import lombok.Data;

@Data
public class DiscountCode {
    /**
     * id
     */
    private Long id;

    /**
     * business id
     */
    private Long businessId;

    /**
     * discount code
     */
    private String discountCode;

    /**
     * description
     */
    private String description;

    /**
     * amount
     */
    private BigDecimal amount;

    /**
     * type, 1-percent, 2-fixed
     */
    private Integer type;

    /**
     * start date
     */
    private String startDate;

    /**
     * end date
     */
    private String endDate;

    /**
     * auto apply association
     */
    private Boolean autoApplyAssociation;

    /**
     * allowed all thing
     */
    private Boolean allowedAllThing;

    /**
     * allowed all services
     */
    private Boolean allowedAllServices;

    /**
     * service ids
     */
    private String serviceIds;

    /**
     * add on ids
     */
    private String addOnIds;

    /**
     * allowed all products
     */
    private Boolean allowedAllProducts;

    /**
     * product ids
     */
    private String productIds;

    /**
     * allowed all clients
     */
    private Boolean allowedAllClients;

    /**
     * allowed new clients
     */
    private Boolean allowedNewClients;

    /**
     * client group
     */
    private String clientsGroup;

    /**
     * client ids
     */
    private String clientIds;

    /**
     * limit usage
     */
    private Integer limitUsage;

    /**
     * limit number per client
     */
    private Integer limitNumberPerClient;

    /**
     * limit budget
     */
    private Integer limitBudget;

    /**
     * discount sales
     */
    private BigDecimal discountSales;

    /**
     * total usage
     */
    private Integer totalUsage;

    /**
     * status, 1-active, 2-inactive, 3-archived, 4-deleted
     */
    private Integer status;

    /**
     * create by
     */
    private Long createBy;

    /**
     * update by
     */
    private Long updateBy;

    /**
     * create time
     */
    private OffsetDateTime createTime;

    /**
     * update time
     */
    private OffsetDateTime updateTime;

    /**
     * company id
     */
    private Long companyId;

    /**
     * enable online booking
     */
    private Boolean enableOnlineBooking;

    /**
     * location ids
     */
    private String locationIds;

    private Integer expiryType;

    private Long expiryTime;

    private Integer source;
}
