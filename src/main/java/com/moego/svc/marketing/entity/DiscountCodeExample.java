package com.moego.svc.marketing.entity;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

public class DiscountCodeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DiscountCodeExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Long value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Long value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Long value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Long value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Long value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Long value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Long> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Long> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Long value1, Long value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Long value1, Long value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeIsNull() {
            addCriterion("discount_code is null");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeIsNotNull() {
            addCriterion("discount_code is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeEqualTo(String value) {
            addCriterion("discount_code =", value, "discountCode");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeNotEqualTo(String value) {
            addCriterion("discount_code <>", value, "discountCode");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeGreaterThan(String value) {
            addCriterion("discount_code >", value, "discountCode");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeGreaterThanOrEqualTo(String value) {
            addCriterion("discount_code >=", value, "discountCode");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeLessThan(String value) {
            addCriterion("discount_code <", value, "discountCode");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeLessThanOrEqualTo(String value) {
            addCriterion("discount_code <=", value, "discountCode");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeLike(String value) {
            addCriterion("discount_code like", value, "discountCode");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeNotLike(String value) {
            addCriterion("discount_code not like", value, "discountCode");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeIn(List<String> values) {
            addCriterion("discount_code in", values, "discountCode");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeNotIn(List<String> values) {
            addCriterion("discount_code not in", values, "discountCode");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeBetween(String value1, String value2) {
            addCriterion("discount_code between", value1, value2, "discountCode");
            return (Criteria) this;
        }

        public Criteria andDiscountCodeNotBetween(String value1, String value2) {
            addCriterion("discount_code not between", value1, value2, "discountCode");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(BigDecimal value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(BigDecimal value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(BigDecimal value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(BigDecimal value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<BigDecimal> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<BigDecimal> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("\"type\" is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("\"type\" is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("\"type\" =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("\"type\" <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("\"type\" >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"type\" >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("\"type\" <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("\"type\" <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("\"type\" in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("\"type\" not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("\"type\" between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("\"type\" not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNull() {
            addCriterion("start_date is null");
            return (Criteria) this;
        }

        public Criteria andStartDateIsNotNull() {
            addCriterion("start_date is not null");
            return (Criteria) this;
        }

        public Criteria andStartDateEqualTo(String value) {
            addCriterion("start_date =", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotEqualTo(String value) {
            addCriterion("start_date <>", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThan(String value) {
            addCriterion("start_date >", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateGreaterThanOrEqualTo(String value) {
            addCriterion("start_date >=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThan(String value) {
            addCriterion("start_date <", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLessThanOrEqualTo(String value) {
            addCriterion("start_date <=", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateLike(String value) {
            addCriterion("start_date like", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotLike(String value) {
            addCriterion("start_date not like", value, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateIn(List<String> values) {
            addCriterion("start_date in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotIn(List<String> values) {
            addCriterion("start_date not in", values, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateBetween(String value1, String value2) {
            addCriterion("start_date between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andStartDateNotBetween(String value1, String value2) {
            addCriterion("start_date not between", value1, value2, "startDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNull() {
            addCriterion("end_date is null");
            return (Criteria) this;
        }

        public Criteria andEndDateIsNotNull() {
            addCriterion("end_date is not null");
            return (Criteria) this;
        }

        public Criteria andEndDateEqualTo(String value) {
            addCriterion("end_date =", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotEqualTo(String value) {
            addCriterion("end_date <>", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThan(String value) {
            addCriterion("end_date >", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateGreaterThanOrEqualTo(String value) {
            addCriterion("end_date >=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThan(String value) {
            addCriterion("end_date <", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLessThanOrEqualTo(String value) {
            addCriterion("end_date <=", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateLike(String value) {
            addCriterion("end_date like", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotLike(String value) {
            addCriterion("end_date not like", value, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateIn(List<String> values) {
            addCriterion("end_date in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotIn(List<String> values) {
            addCriterion("end_date not in", values, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateBetween(String value1, String value2) {
            addCriterion("end_date between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andEndDateNotBetween(String value1, String value2) {
            addCriterion("end_date not between", value1, value2, "endDate");
            return (Criteria) this;
        }

        public Criteria andAutoApplyAssociationIsNull() {
            addCriterion("auto_apply_association is null");
            return (Criteria) this;
        }

        public Criteria andAutoApplyAssociationIsNotNull() {
            addCriterion("auto_apply_association is not null");
            return (Criteria) this;
        }

        public Criteria andAutoApplyAssociationEqualTo(Boolean value) {
            addCriterion("auto_apply_association =", value, "autoApplyAssociation");
            return (Criteria) this;
        }

        public Criteria andAutoApplyAssociationNotEqualTo(Boolean value) {
            addCriterion("auto_apply_association <>", value, "autoApplyAssociation");
            return (Criteria) this;
        }

        public Criteria andAutoApplyAssociationGreaterThan(Boolean value) {
            addCriterion("auto_apply_association >", value, "autoApplyAssociation");
            return (Criteria) this;
        }

        public Criteria andAutoApplyAssociationGreaterThanOrEqualTo(Boolean value) {
            addCriterion("auto_apply_association >=", value, "autoApplyAssociation");
            return (Criteria) this;
        }

        public Criteria andAutoApplyAssociationLessThan(Boolean value) {
            addCriterion("auto_apply_association <", value, "autoApplyAssociation");
            return (Criteria) this;
        }

        public Criteria andAutoApplyAssociationLessThanOrEqualTo(Boolean value) {
            addCriterion("auto_apply_association <=", value, "autoApplyAssociation");
            return (Criteria) this;
        }

        public Criteria andAutoApplyAssociationIn(List<Boolean> values) {
            addCriterion("auto_apply_association in", values, "autoApplyAssociation");
            return (Criteria) this;
        }

        public Criteria andAutoApplyAssociationNotIn(List<Boolean> values) {
            addCriterion("auto_apply_association not in", values, "autoApplyAssociation");
            return (Criteria) this;
        }

        public Criteria andAutoApplyAssociationBetween(Boolean value1, Boolean value2) {
            addCriterion("auto_apply_association between", value1, value2, "autoApplyAssociation");
            return (Criteria) this;
        }

        public Criteria andAutoApplyAssociationNotBetween(Boolean value1, Boolean value2) {
            addCriterion("auto_apply_association not between", value1, value2, "autoApplyAssociation");
            return (Criteria) this;
        }

        public Criteria andAllowedAllThingIsNull() {
            addCriterion("allowed_all_thing is null");
            return (Criteria) this;
        }

        public Criteria andAllowedAllThingIsNotNull() {
            addCriterion("allowed_all_thing is not null");
            return (Criteria) this;
        }

        public Criteria andAllowedAllThingEqualTo(Boolean value) {
            addCriterion("allowed_all_thing =", value, "allowedAllThing");
            return (Criteria) this;
        }

        public Criteria andAllowedAllThingNotEqualTo(Boolean value) {
            addCriterion("allowed_all_thing <>", value, "allowedAllThing");
            return (Criteria) this;
        }

        public Criteria andAllowedAllThingGreaterThan(Boolean value) {
            addCriterion("allowed_all_thing >", value, "allowedAllThing");
            return (Criteria) this;
        }

        public Criteria andAllowedAllThingGreaterThanOrEqualTo(Boolean value) {
            addCriterion("allowed_all_thing >=", value, "allowedAllThing");
            return (Criteria) this;
        }

        public Criteria andAllowedAllThingLessThan(Boolean value) {
            addCriterion("allowed_all_thing <", value, "allowedAllThing");
            return (Criteria) this;
        }

        public Criteria andAllowedAllThingLessThanOrEqualTo(Boolean value) {
            addCriterion("allowed_all_thing <=", value, "allowedAllThing");
            return (Criteria) this;
        }

        public Criteria andAllowedAllThingIn(List<Boolean> values) {
            addCriterion("allowed_all_thing in", values, "allowedAllThing");
            return (Criteria) this;
        }

        public Criteria andAllowedAllThingNotIn(List<Boolean> values) {
            addCriterion("allowed_all_thing not in", values, "allowedAllThing");
            return (Criteria) this;
        }

        public Criteria andAllowedAllThingBetween(Boolean value1, Boolean value2) {
            addCriterion("allowed_all_thing between", value1, value2, "allowedAllThing");
            return (Criteria) this;
        }

        public Criteria andAllowedAllThingNotBetween(Boolean value1, Boolean value2) {
            addCriterion("allowed_all_thing not between", value1, value2, "allowedAllThing");
            return (Criteria) this;
        }

        public Criteria andAllowedAllServicesIsNull() {
            addCriterion("allowed_all_services is null");
            return (Criteria) this;
        }

        public Criteria andAllowedAllServicesIsNotNull() {
            addCriterion("allowed_all_services is not null");
            return (Criteria) this;
        }

        public Criteria andAllowedAllServicesEqualTo(Boolean value) {
            addCriterion("allowed_all_services =", value, "allowedAllServices");
            return (Criteria) this;
        }

        public Criteria andAllowedAllServicesNotEqualTo(Boolean value) {
            addCriterion("allowed_all_services <>", value, "allowedAllServices");
            return (Criteria) this;
        }

        public Criteria andAllowedAllServicesGreaterThan(Boolean value) {
            addCriterion("allowed_all_services >", value, "allowedAllServices");
            return (Criteria) this;
        }

        public Criteria andAllowedAllServicesGreaterThanOrEqualTo(Boolean value) {
            addCriterion("allowed_all_services >=", value, "allowedAllServices");
            return (Criteria) this;
        }

        public Criteria andAllowedAllServicesLessThan(Boolean value) {
            addCriterion("allowed_all_services <", value, "allowedAllServices");
            return (Criteria) this;
        }

        public Criteria andAllowedAllServicesLessThanOrEqualTo(Boolean value) {
            addCriterion("allowed_all_services <=", value, "allowedAllServices");
            return (Criteria) this;
        }

        public Criteria andAllowedAllServicesIn(List<Boolean> values) {
            addCriterion("allowed_all_services in", values, "allowedAllServices");
            return (Criteria) this;
        }

        public Criteria andAllowedAllServicesNotIn(List<Boolean> values) {
            addCriterion("allowed_all_services not in", values, "allowedAllServices");
            return (Criteria) this;
        }

        public Criteria andAllowedAllServicesBetween(Boolean value1, Boolean value2) {
            addCriterion("allowed_all_services between", value1, value2, "allowedAllServices");
            return (Criteria) this;
        }

        public Criteria andAllowedAllServicesNotBetween(Boolean value1, Boolean value2) {
            addCriterion("allowed_all_services not between", value1, value2, "allowedAllServices");
            return (Criteria) this;
        }

        public Criteria andServiceIdsIsNull() {
            addCriterion("service_ids is null");
            return (Criteria) this;
        }

        public Criteria andServiceIdsIsNotNull() {
            addCriterion("service_ids is not null");
            return (Criteria) this;
        }

        public Criteria andServiceIdsEqualTo(String value) {
            addCriterion("service_ids =", value, "serviceIds");
            return (Criteria) this;
        }

        public Criteria andServiceIdsNotEqualTo(String value) {
            addCriterion("service_ids <>", value, "serviceIds");
            return (Criteria) this;
        }

        public Criteria andServiceIdsGreaterThan(String value) {
            addCriterion("service_ids >", value, "serviceIds");
            return (Criteria) this;
        }

        public Criteria andServiceIdsGreaterThanOrEqualTo(String value) {
            addCriterion("service_ids >=", value, "serviceIds");
            return (Criteria) this;
        }

        public Criteria andServiceIdsLessThan(String value) {
            addCriterion("service_ids <", value, "serviceIds");
            return (Criteria) this;
        }

        public Criteria andServiceIdsLessThanOrEqualTo(String value) {
            addCriterion("service_ids <=", value, "serviceIds");
            return (Criteria) this;
        }

        public Criteria andServiceIdsLike(String value) {
            addCriterion("service_ids like", value, "serviceIds");
            return (Criteria) this;
        }

        public Criteria andServiceIdsNotLike(String value) {
            addCriterion("service_ids not like", value, "serviceIds");
            return (Criteria) this;
        }

        public Criteria andServiceIdsIn(List<String> values) {
            addCriterion("service_ids in", values, "serviceIds");
            return (Criteria) this;
        }

        public Criteria andServiceIdsNotIn(List<String> values) {
            addCriterion("service_ids not in", values, "serviceIds");
            return (Criteria) this;
        }

        public Criteria andServiceIdsBetween(String value1, String value2) {
            addCriterion("service_ids between", value1, value2, "serviceIds");
            return (Criteria) this;
        }

        public Criteria andServiceIdsNotBetween(String value1, String value2) {
            addCriterion("service_ids not between", value1, value2, "serviceIds");
            return (Criteria) this;
        }

        public Criteria andAddOnIdsIsNull() {
            addCriterion("add_on_ids is null");
            return (Criteria) this;
        }

        public Criteria andAddOnIdsIsNotNull() {
            addCriterion("add_on_ids is not null");
            return (Criteria) this;
        }

        public Criteria andAddOnIdsEqualTo(String value) {
            addCriterion("add_on_ids =", value, "addOnIds");
            return (Criteria) this;
        }

        public Criteria andAddOnIdsNotEqualTo(String value) {
            addCriterion("add_on_ids <>", value, "addOnIds");
            return (Criteria) this;
        }

        public Criteria andAddOnIdsGreaterThan(String value) {
            addCriterion("add_on_ids >", value, "addOnIds");
            return (Criteria) this;
        }

        public Criteria andAddOnIdsGreaterThanOrEqualTo(String value) {
            addCriterion("add_on_ids >=", value, "addOnIds");
            return (Criteria) this;
        }

        public Criteria andAddOnIdsLessThan(String value) {
            addCriterion("add_on_ids <", value, "addOnIds");
            return (Criteria) this;
        }

        public Criteria andAddOnIdsLessThanOrEqualTo(String value) {
            addCriterion("add_on_ids <=", value, "addOnIds");
            return (Criteria) this;
        }

        public Criteria andAddOnIdsLike(String value) {
            addCriterion("add_on_ids like", value, "addOnIds");
            return (Criteria) this;
        }

        public Criteria andAddOnIdsNotLike(String value) {
            addCriterion("add_on_ids not like", value, "addOnIds");
            return (Criteria) this;
        }

        public Criteria andAddOnIdsIn(List<String> values) {
            addCriterion("add_on_ids in", values, "addOnIds");
            return (Criteria) this;
        }

        public Criteria andAddOnIdsNotIn(List<String> values) {
            addCriterion("add_on_ids not in", values, "addOnIds");
            return (Criteria) this;
        }

        public Criteria andAddOnIdsBetween(String value1, String value2) {
            addCriterion("add_on_ids between", value1, value2, "addOnIds");
            return (Criteria) this;
        }

        public Criteria andAddOnIdsNotBetween(String value1, String value2) {
            addCriterion("add_on_ids not between", value1, value2, "addOnIds");
            return (Criteria) this;
        }

        public Criteria andAllowedAllProductsIsNull() {
            addCriterion("allowed_all_products is null");
            return (Criteria) this;
        }

        public Criteria andAllowedAllProductsIsNotNull() {
            addCriterion("allowed_all_products is not null");
            return (Criteria) this;
        }

        public Criteria andAllowedAllProductsEqualTo(Boolean value) {
            addCriterion("allowed_all_products =", value, "allowedAllProducts");
            return (Criteria) this;
        }

        public Criteria andAllowedAllProductsNotEqualTo(Boolean value) {
            addCriterion("allowed_all_products <>", value, "allowedAllProducts");
            return (Criteria) this;
        }

        public Criteria andAllowedAllProductsGreaterThan(Boolean value) {
            addCriterion("allowed_all_products >", value, "allowedAllProducts");
            return (Criteria) this;
        }

        public Criteria andAllowedAllProductsGreaterThanOrEqualTo(Boolean value) {
            addCriterion("allowed_all_products >=", value, "allowedAllProducts");
            return (Criteria) this;
        }

        public Criteria andAllowedAllProductsLessThan(Boolean value) {
            addCriterion("allowed_all_products <", value, "allowedAllProducts");
            return (Criteria) this;
        }

        public Criteria andAllowedAllProductsLessThanOrEqualTo(Boolean value) {
            addCriterion("allowed_all_products <=", value, "allowedAllProducts");
            return (Criteria) this;
        }

        public Criteria andAllowedAllProductsIn(List<Boolean> values) {
            addCriterion("allowed_all_products in", values, "allowedAllProducts");
            return (Criteria) this;
        }

        public Criteria andAllowedAllProductsNotIn(List<Boolean> values) {
            addCriterion("allowed_all_products not in", values, "allowedAllProducts");
            return (Criteria) this;
        }

        public Criteria andAllowedAllProductsBetween(Boolean value1, Boolean value2) {
            addCriterion("allowed_all_products between", value1, value2, "allowedAllProducts");
            return (Criteria) this;
        }

        public Criteria andAllowedAllProductsNotBetween(Boolean value1, Boolean value2) {
            addCriterion("allowed_all_products not between", value1, value2, "allowedAllProducts");
            return (Criteria) this;
        }

        public Criteria andProductIdsIsNull() {
            addCriterion("product_ids is null");
            return (Criteria) this;
        }

        public Criteria andProductIdsIsNotNull() {
            addCriterion("product_ids is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdsEqualTo(String value) {
            addCriterion("product_ids =", value, "productIds");
            return (Criteria) this;
        }

        public Criteria andProductIdsNotEqualTo(String value) {
            addCriterion("product_ids <>", value, "productIds");
            return (Criteria) this;
        }

        public Criteria andProductIdsGreaterThan(String value) {
            addCriterion("product_ids >", value, "productIds");
            return (Criteria) this;
        }

        public Criteria andProductIdsGreaterThanOrEqualTo(String value) {
            addCriterion("product_ids >=", value, "productIds");
            return (Criteria) this;
        }

        public Criteria andProductIdsLessThan(String value) {
            addCriterion("product_ids <", value, "productIds");
            return (Criteria) this;
        }

        public Criteria andProductIdsLessThanOrEqualTo(String value) {
            addCriterion("product_ids <=", value, "productIds");
            return (Criteria) this;
        }

        public Criteria andProductIdsLike(String value) {
            addCriterion("product_ids like", value, "productIds");
            return (Criteria) this;
        }

        public Criteria andProductIdsNotLike(String value) {
            addCriterion("product_ids not like", value, "productIds");
            return (Criteria) this;
        }

        public Criteria andProductIdsIn(List<String> values) {
            addCriterion("product_ids in", values, "productIds");
            return (Criteria) this;
        }

        public Criteria andProductIdsNotIn(List<String> values) {
            addCriterion("product_ids not in", values, "productIds");
            return (Criteria) this;
        }

        public Criteria andProductIdsBetween(String value1, String value2) {
            addCriterion("product_ids between", value1, value2, "productIds");
            return (Criteria) this;
        }

        public Criteria andProductIdsNotBetween(String value1, String value2) {
            addCriterion("product_ids not between", value1, value2, "productIds");
            return (Criteria) this;
        }

        public Criteria andAllowedAllClientsIsNull() {
            addCriterion("allowed_all_clients is null");
            return (Criteria) this;
        }

        public Criteria andAllowedAllClientsIsNotNull() {
            addCriterion("allowed_all_clients is not null");
            return (Criteria) this;
        }

        public Criteria andAllowedAllClientsEqualTo(Boolean value) {
            addCriterion("allowed_all_clients =", value, "allowedAllClients");
            return (Criteria) this;
        }

        public Criteria andAllowedAllClientsNotEqualTo(Boolean value) {
            addCriterion("allowed_all_clients <>", value, "allowedAllClients");
            return (Criteria) this;
        }

        public Criteria andAllowedAllClientsGreaterThan(Boolean value) {
            addCriterion("allowed_all_clients >", value, "allowedAllClients");
            return (Criteria) this;
        }

        public Criteria andAllowedAllClientsGreaterThanOrEqualTo(Boolean value) {
            addCriterion("allowed_all_clients >=", value, "allowedAllClients");
            return (Criteria) this;
        }

        public Criteria andAllowedAllClientsLessThan(Boolean value) {
            addCriterion("allowed_all_clients <", value, "allowedAllClients");
            return (Criteria) this;
        }

        public Criteria andAllowedAllClientsLessThanOrEqualTo(Boolean value) {
            addCriterion("allowed_all_clients <=", value, "allowedAllClients");
            return (Criteria) this;
        }

        public Criteria andAllowedAllClientsIn(List<Boolean> values) {
            addCriterion("allowed_all_clients in", values, "allowedAllClients");
            return (Criteria) this;
        }

        public Criteria andAllowedAllClientsNotIn(List<Boolean> values) {
            addCriterion("allowed_all_clients not in", values, "allowedAllClients");
            return (Criteria) this;
        }

        public Criteria andAllowedAllClientsBetween(Boolean value1, Boolean value2) {
            addCriterion("allowed_all_clients between", value1, value2, "allowedAllClients");
            return (Criteria) this;
        }

        public Criteria andAllowedAllClientsNotBetween(Boolean value1, Boolean value2) {
            addCriterion("allowed_all_clients not between", value1, value2, "allowedAllClients");
            return (Criteria) this;
        }

        public Criteria andAllowedNewClientsIsNull() {
            addCriterion("allowed_new_clients is null");
            return (Criteria) this;
        }

        public Criteria andAllowedNewClientsIsNotNull() {
            addCriterion("allowed_new_clients is not null");
            return (Criteria) this;
        }

        public Criteria andAllowedNewClientsEqualTo(Boolean value) {
            addCriterion("allowed_new_clients =", value, "allowedNewClients");
            return (Criteria) this;
        }

        public Criteria andAllowedNewClientsNotEqualTo(Boolean value) {
            addCriterion("allowed_new_clients <>", value, "allowedNewClients");
            return (Criteria) this;
        }

        public Criteria andAllowedNewClientsGreaterThan(Boolean value) {
            addCriterion("allowed_new_clients >", value, "allowedNewClients");
            return (Criteria) this;
        }

        public Criteria andAllowedNewClientsGreaterThanOrEqualTo(Boolean value) {
            addCriterion("allowed_new_clients >=", value, "allowedNewClients");
            return (Criteria) this;
        }

        public Criteria andAllowedNewClientsLessThan(Boolean value) {
            addCriterion("allowed_new_clients <", value, "allowedNewClients");
            return (Criteria) this;
        }

        public Criteria andAllowedNewClientsLessThanOrEqualTo(Boolean value) {
            addCriterion("allowed_new_clients <=", value, "allowedNewClients");
            return (Criteria) this;
        }

        public Criteria andAllowedNewClientsIn(List<Boolean> values) {
            addCriterion("allowed_new_clients in", values, "allowedNewClients");
            return (Criteria) this;
        }

        public Criteria andAllowedNewClientsNotIn(List<Boolean> values) {
            addCriterion("allowed_new_clients not in", values, "allowedNewClients");
            return (Criteria) this;
        }

        public Criteria andAllowedNewClientsBetween(Boolean value1, Boolean value2) {
            addCriterion("allowed_new_clients between", value1, value2, "allowedNewClients");
            return (Criteria) this;
        }

        public Criteria andAllowedNewClientsNotBetween(Boolean value1, Boolean value2) {
            addCriterion("allowed_new_clients not between", value1, value2, "allowedNewClients");
            return (Criteria) this;
        }

        public Criteria andClientsGroupIsNull() {
            addCriterion("clients_group is null");
            return (Criteria) this;
        }

        public Criteria andClientsGroupIsNotNull() {
            addCriterion("clients_group is not null");
            return (Criteria) this;
        }

        public Criteria andClientsGroupEqualTo(String value) {
            addCriterion("clients_group =", value, "clientsGroup");
            return (Criteria) this;
        }

        public Criteria andClientsGroupNotEqualTo(String value) {
            addCriterion("clients_group <>", value, "clientsGroup");
            return (Criteria) this;
        }

        public Criteria andClientsGroupGreaterThan(String value) {
            addCriterion("clients_group >", value, "clientsGroup");
            return (Criteria) this;
        }

        public Criteria andClientsGroupGreaterThanOrEqualTo(String value) {
            addCriterion("clients_group >=", value, "clientsGroup");
            return (Criteria) this;
        }

        public Criteria andClientsGroupLessThan(String value) {
            addCriterion("clients_group <", value, "clientsGroup");
            return (Criteria) this;
        }

        public Criteria andClientsGroupLessThanOrEqualTo(String value) {
            addCriterion("clients_group <=", value, "clientsGroup");
            return (Criteria) this;
        }

        public Criteria andClientsGroupLike(String value) {
            addCriterion("clients_group like", value, "clientsGroup");
            return (Criteria) this;
        }

        public Criteria andClientsGroupNotLike(String value) {
            addCriterion("clients_group not like", value, "clientsGroup");
            return (Criteria) this;
        }

        public Criteria andClientsGroupIn(List<String> values) {
            addCriterion("clients_group in", values, "clientsGroup");
            return (Criteria) this;
        }

        public Criteria andClientsGroupNotIn(List<String> values) {
            addCriterion("clients_group not in", values, "clientsGroup");
            return (Criteria) this;
        }

        public Criteria andClientsGroupBetween(String value1, String value2) {
            addCriterion("clients_group between", value1, value2, "clientsGroup");
            return (Criteria) this;
        }

        public Criteria andClientsGroupNotBetween(String value1, String value2) {
            addCriterion("clients_group not between", value1, value2, "clientsGroup");
            return (Criteria) this;
        }

        public Criteria andClientIdsIsNull() {
            addCriterion("client_ids is null");
            return (Criteria) this;
        }

        public Criteria andClientIdsIsNotNull() {
            addCriterion("client_ids is not null");
            return (Criteria) this;
        }

        public Criteria andClientIdsEqualTo(String value) {
            addCriterion("client_ids =", value, "clientIds");
            return (Criteria) this;
        }

        public Criteria andClientIdsNotEqualTo(String value) {
            addCriterion("client_ids <>", value, "clientIds");
            return (Criteria) this;
        }

        public Criteria andClientIdsGreaterThan(String value) {
            addCriterion("client_ids >", value, "clientIds");
            return (Criteria) this;
        }

        public Criteria andClientIdsGreaterThanOrEqualTo(String value) {
            addCriterion("client_ids >=", value, "clientIds");
            return (Criteria) this;
        }

        public Criteria andClientIdsLessThan(String value) {
            addCriterion("client_ids <", value, "clientIds");
            return (Criteria) this;
        }

        public Criteria andClientIdsLessThanOrEqualTo(String value) {
            addCriterion("client_ids <=", value, "clientIds");
            return (Criteria) this;
        }

        public Criteria andClientIdsLike(String value) {
            addCriterion("client_ids like", value, "clientIds");
            return (Criteria) this;
        }

        public Criteria andClientIdsNotLike(String value) {
            addCriterion("client_ids not like", value, "clientIds");
            return (Criteria) this;
        }

        public Criteria andClientIdsIn(List<String> values) {
            addCriterion("client_ids in", values, "clientIds");
            return (Criteria) this;
        }

        public Criteria andClientIdsNotIn(List<String> values) {
            addCriterion("client_ids not in", values, "clientIds");
            return (Criteria) this;
        }

        public Criteria andClientIdsBetween(String value1, String value2) {
            addCriterion("client_ids between", value1, value2, "clientIds");
            return (Criteria) this;
        }

        public Criteria andClientIdsNotBetween(String value1, String value2) {
            addCriterion("client_ids not between", value1, value2, "clientIds");
            return (Criteria) this;
        }

        public Criteria andLimitUsageIsNull() {
            addCriterion("limit_usage is null");
            return (Criteria) this;
        }

        public Criteria andLimitUsageIsNotNull() {
            addCriterion("limit_usage is not null");
            return (Criteria) this;
        }

        public Criteria andLimitUsageEqualTo(Integer value) {
            addCriterion("limit_usage =", value, "limitUsage");
            return (Criteria) this;
        }

        public Criteria andLimitUsageNotEqualTo(Integer value) {
            addCriterion("limit_usage <>", value, "limitUsage");
            return (Criteria) this;
        }

        public Criteria andLimitUsageGreaterThan(Integer value) {
            addCriterion("limit_usage >", value, "limitUsage");
            return (Criteria) this;
        }

        public Criteria andLimitUsageGreaterThanOrEqualTo(Integer value) {
            addCriterion("limit_usage >=", value, "limitUsage");
            return (Criteria) this;
        }

        public Criteria andLimitUsageLessThan(Integer value) {
            addCriterion("limit_usage <", value, "limitUsage");
            return (Criteria) this;
        }

        public Criteria andLimitUsageLessThanOrEqualTo(Integer value) {
            addCriterion("limit_usage <=", value, "limitUsage");
            return (Criteria) this;
        }

        public Criteria andLimitUsageIn(List<Integer> values) {
            addCriterion("limit_usage in", values, "limitUsage");
            return (Criteria) this;
        }

        public Criteria andLimitUsageNotIn(List<Integer> values) {
            addCriterion("limit_usage not in", values, "limitUsage");
            return (Criteria) this;
        }

        public Criteria andLimitUsageBetween(Integer value1, Integer value2) {
            addCriterion("limit_usage between", value1, value2, "limitUsage");
            return (Criteria) this;
        }

        public Criteria andLimitUsageNotBetween(Integer value1, Integer value2) {
            addCriterion("limit_usage not between", value1, value2, "limitUsage");
            return (Criteria) this;
        }

        public Criteria andLimitNumberPerClientIsNull() {
            addCriterion("limit_number_per_client is null");
            return (Criteria) this;
        }

        public Criteria andLimitNumberPerClientIsNotNull() {
            addCriterion("limit_number_per_client is not null");
            return (Criteria) this;
        }

        public Criteria andLimitNumberPerClientEqualTo(Integer value) {
            addCriterion("limit_number_per_client =", value, "limitNumberPerClient");
            return (Criteria) this;
        }

        public Criteria andLimitNumberPerClientNotEqualTo(Integer value) {
            addCriterion("limit_number_per_client <>", value, "limitNumberPerClient");
            return (Criteria) this;
        }

        public Criteria andLimitNumberPerClientGreaterThan(Integer value) {
            addCriterion("limit_number_per_client >", value, "limitNumberPerClient");
            return (Criteria) this;
        }

        public Criteria andLimitNumberPerClientGreaterThanOrEqualTo(Integer value) {
            addCriterion("limit_number_per_client >=", value, "limitNumberPerClient");
            return (Criteria) this;
        }

        public Criteria andLimitNumberPerClientLessThan(Integer value) {
            addCriterion("limit_number_per_client <", value, "limitNumberPerClient");
            return (Criteria) this;
        }

        public Criteria andLimitNumberPerClientLessThanOrEqualTo(Integer value) {
            addCriterion("limit_number_per_client <=", value, "limitNumberPerClient");
            return (Criteria) this;
        }

        public Criteria andLimitNumberPerClientIn(List<Integer> values) {
            addCriterion("limit_number_per_client in", values, "limitNumberPerClient");
            return (Criteria) this;
        }

        public Criteria andLimitNumberPerClientNotIn(List<Integer> values) {
            addCriterion("limit_number_per_client not in", values, "limitNumberPerClient");
            return (Criteria) this;
        }

        public Criteria andLimitNumberPerClientBetween(Integer value1, Integer value2) {
            addCriterion("limit_number_per_client between", value1, value2, "limitNumberPerClient");
            return (Criteria) this;
        }

        public Criteria andLimitNumberPerClientNotBetween(Integer value1, Integer value2) {
            addCriterion("limit_number_per_client not between", value1, value2, "limitNumberPerClient");
            return (Criteria) this;
        }

        public Criteria andLimitBudgetIsNull() {
            addCriterion("limit_budget is null");
            return (Criteria) this;
        }

        public Criteria andLimitBudgetIsNotNull() {
            addCriterion("limit_budget is not null");
            return (Criteria) this;
        }

        public Criteria andLimitBudgetEqualTo(Integer value) {
            addCriterion("limit_budget =", value, "limitBudget");
            return (Criteria) this;
        }

        public Criteria andLimitBudgetNotEqualTo(Integer value) {
            addCriterion("limit_budget <>", value, "limitBudget");
            return (Criteria) this;
        }

        public Criteria andLimitBudgetGreaterThan(Integer value) {
            addCriterion("limit_budget >", value, "limitBudget");
            return (Criteria) this;
        }

        public Criteria andLimitBudgetGreaterThanOrEqualTo(Integer value) {
            addCriterion("limit_budget >=", value, "limitBudget");
            return (Criteria) this;
        }

        public Criteria andLimitBudgetLessThan(Integer value) {
            addCriterion("limit_budget <", value, "limitBudget");
            return (Criteria) this;
        }

        public Criteria andLimitBudgetLessThanOrEqualTo(Integer value) {
            addCriterion("limit_budget <=", value, "limitBudget");
            return (Criteria) this;
        }

        public Criteria andLimitBudgetIn(List<Integer> values) {
            addCriterion("limit_budget in", values, "limitBudget");
            return (Criteria) this;
        }

        public Criteria andLimitBudgetNotIn(List<Integer> values) {
            addCriterion("limit_budget not in", values, "limitBudget");
            return (Criteria) this;
        }

        public Criteria andLimitBudgetBetween(Integer value1, Integer value2) {
            addCriterion("limit_budget between", value1, value2, "limitBudget");
            return (Criteria) this;
        }

        public Criteria andLimitBudgetNotBetween(Integer value1, Integer value2) {
            addCriterion("limit_budget not between", value1, value2, "limitBudget");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesIsNull() {
            addCriterion("discount_sales is null");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesIsNotNull() {
            addCriterion("discount_sales is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesEqualTo(BigDecimal value) {
            addCriterion("discount_sales =", value, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesNotEqualTo(BigDecimal value) {
            addCriterion("discount_sales <>", value, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesGreaterThan(BigDecimal value) {
            addCriterion("discount_sales >", value, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_sales >=", value, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesLessThan(BigDecimal value) {
            addCriterion("discount_sales <", value, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_sales <=", value, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesIn(List<BigDecimal> values) {
            addCriterion("discount_sales in", values, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesNotIn(List<BigDecimal> values) {
            addCriterion("discount_sales not in", values, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_sales between", value1, value2, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_sales not between", value1, value2, "discountSales");
            return (Criteria) this;
        }

        public Criteria andTotalUsageIsNull() {
            addCriterion("total_usage is null");
            return (Criteria) this;
        }

        public Criteria andTotalUsageIsNotNull() {
            addCriterion("total_usage is not null");
            return (Criteria) this;
        }

        public Criteria andTotalUsageEqualTo(Integer value) {
            addCriterion("total_usage =", value, "totalUsage");
            return (Criteria) this;
        }

        public Criteria andTotalUsageNotEqualTo(Integer value) {
            addCriterion("total_usage <>", value, "totalUsage");
            return (Criteria) this;
        }

        public Criteria andTotalUsageGreaterThan(Integer value) {
            addCriterion("total_usage >", value, "totalUsage");
            return (Criteria) this;
        }

        public Criteria andTotalUsageGreaterThanOrEqualTo(Integer value) {
            addCriterion("total_usage >=", value, "totalUsage");
            return (Criteria) this;
        }

        public Criteria andTotalUsageLessThan(Integer value) {
            addCriterion("total_usage <", value, "totalUsage");
            return (Criteria) this;
        }

        public Criteria andTotalUsageLessThanOrEqualTo(Integer value) {
            addCriterion("total_usage <=", value, "totalUsage");
            return (Criteria) this;
        }

        public Criteria andTotalUsageIn(List<Integer> values) {
            addCriterion("total_usage in", values, "totalUsage");
            return (Criteria) this;
        }

        public Criteria andTotalUsageNotIn(List<Integer> values) {
            addCriterion("total_usage not in", values, "totalUsage");
            return (Criteria) this;
        }

        public Criteria andTotalUsageBetween(Integer value1, Integer value2) {
            addCriterion("total_usage between", value1, value2, "totalUsage");
            return (Criteria) this;
        }

        public Criteria andTotalUsageNotBetween(Integer value1, Integer value2) {
            addCriterion("total_usage not between", value1, value2, "totalUsage");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("\"status\" is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("\"status\" is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("\"status\" =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("\"status\" <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("\"status\" >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"status\" >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("\"status\" <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("\"status\" <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("\"status\" in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("\"status\" not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("\"status\" between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("\"status\" not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(Long value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(Long value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(Long value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(Long value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(Long value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(Long value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<Long> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<Long> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(Long value1, Long value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(Long value1, Long value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNull() {
            addCriterion("update_by is null");
            return (Criteria) this;
        }

        public Criteria andUpdateByIsNotNull() {
            addCriterion("update_by is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateByEqualTo(Long value) {
            addCriterion("update_by =", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotEqualTo(Long value) {
            addCriterion("update_by <>", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThan(Long value) {
            addCriterion("update_by >", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByGreaterThanOrEqualTo(Long value) {
            addCriterion("update_by >=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThan(Long value) {
            addCriterion("update_by <", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByLessThanOrEqualTo(Long value) {
            addCriterion("update_by <=", value, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByIn(List<Long> values) {
            addCriterion("update_by in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotIn(List<Long> values) {
            addCriterion("update_by not in", values, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByBetween(Long value1, Long value2) {
            addCriterion("update_by between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andUpdateByNotBetween(Long value1, Long value2) {
            addCriterion("update_by not between", value1, value2, "updateBy");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(OffsetDateTime value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(OffsetDateTime value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(OffsetDateTime value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(OffsetDateTime value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(OffsetDateTime value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(OffsetDateTime value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<OffsetDateTime> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<OffsetDateTime> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(OffsetDateTime value1, OffsetDateTime value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(OffsetDateTime value1, OffsetDateTime value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNull() {
            addCriterion("update_time is null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIsNotNull() {
            addCriterion("update_time is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeEqualTo(OffsetDateTime value) {
            addCriterion("update_time =", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotEqualTo(OffsetDateTime value) {
            addCriterion("update_time <>", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThan(OffsetDateTime value) {
            addCriterion("update_time >", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeGreaterThanOrEqualTo(OffsetDateTime value) {
            addCriterion("update_time >=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThan(OffsetDateTime value) {
            addCriterion("update_time <", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeLessThanOrEqualTo(OffsetDateTime value) {
            addCriterion("update_time <=", value, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeIn(List<OffsetDateTime> values) {
            addCriterion("update_time in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotIn(List<OffsetDateTime> values) {
            addCriterion("update_time not in", values, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeBetween(OffsetDateTime value1, OffsetDateTime value2) {
            addCriterion("update_time between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andUpdateTimeNotBetween(OffsetDateTime value1, OffsetDateTime value2) {
            addCriterion("update_time not between", value1, value2, "updateTime");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andEnableOnlineBookingIsNull() {
            addCriterion("enable_online_booking is null");
            return (Criteria) this;
        }

        public Criteria andEnableOnlineBookingIsNotNull() {
            addCriterion("enable_online_booking is not null");
            return (Criteria) this;
        }

        public Criteria andEnableOnlineBookingEqualTo(Boolean value) {
            addCriterion("enable_online_booking =", value, "enableOnlineBooking");
            return (Criteria) this;
        }

        public Criteria andEnableOnlineBookingNotEqualTo(Boolean value) {
            addCriterion("enable_online_booking <>", value, "enableOnlineBooking");
            return (Criteria) this;
        }

        public Criteria andEnableOnlineBookingGreaterThan(Boolean value) {
            addCriterion("enable_online_booking >", value, "enableOnlineBooking");
            return (Criteria) this;
        }

        public Criteria andEnableOnlineBookingGreaterThanOrEqualTo(Boolean value) {
            addCriterion("enable_online_booking >=", value, "enableOnlineBooking");
            return (Criteria) this;
        }

        public Criteria andEnableOnlineBookingLessThan(Boolean value) {
            addCriterion("enable_online_booking <", value, "enableOnlineBooking");
            return (Criteria) this;
        }

        public Criteria andEnableOnlineBookingLessThanOrEqualTo(Boolean value) {
            addCriterion("enable_online_booking <=", value, "enableOnlineBooking");
            return (Criteria) this;
        }

        public Criteria andEnableOnlineBookingIn(List<Boolean> values) {
            addCriterion("enable_online_booking in", values, "enableOnlineBooking");
            return (Criteria) this;
        }

        public Criteria andEnableOnlineBookingNotIn(List<Boolean> values) {
            addCriterion("enable_online_booking not in", values, "enableOnlineBooking");
            return (Criteria) this;
        }

        public Criteria andEnableOnlineBookingBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_online_booking between", value1, value2, "enableOnlineBooking");
            return (Criteria) this;
        }

        public Criteria andEnableOnlineBookingNotBetween(Boolean value1, Boolean value2) {
            addCriterion("enable_online_booking not between", value1, value2, "enableOnlineBooking");
            return (Criteria) this;
        }

        public Criteria andLocationIdsIsNull() {
            addCriterion("location_ids is null");
            return (Criteria) this;
        }

        public Criteria andLocationIdsIsNotNull() {
            addCriterion("location_ids is not null");
            return (Criteria) this;
        }

        public Criteria andLocationIdsEqualTo(String value) {
            addCriterion("location_ids =", value, "locationIds");
            return (Criteria) this;
        }

        public Criteria andLocationIdsNotEqualTo(String value) {
            addCriterion("location_ids <>", value, "locationIds");
            return (Criteria) this;
        }

        public Criteria andLocationIdsGreaterThan(String value) {
            addCriterion("location_ids >", value, "locationIds");
            return (Criteria) this;
        }

        public Criteria andLocationIdsGreaterThanOrEqualTo(String value) {
            addCriterion("location_ids >=", value, "locationIds");
            return (Criteria) this;
        }

        public Criteria andLocationIdsLessThan(String value) {
            addCriterion("location_ids <", value, "locationIds");
            return (Criteria) this;
        }

        public Criteria andLocationIdsLessThanOrEqualTo(String value) {
            addCriterion("location_ids <=", value, "locationIds");
            return (Criteria) this;
        }

        public Criteria andLocationIdsLike(String value) {
            addCriterion("location_ids like", value, "locationIds");
            return (Criteria) this;
        }

        public Criteria andLocationIdsNotLike(String value) {
            addCriterion("location_ids not like", value, "locationIds");
            return (Criteria) this;
        }

        public Criteria andLocationIdsIn(List<String> values) {
            addCriterion("location_ids in", values, "locationIds");
            return (Criteria) this;
        }

        public Criteria andLocationIdsNotIn(List<String> values) {
            addCriterion("location_ids not in", values, "locationIds");
            return (Criteria) this;
        }

        public Criteria andLocationIdsBetween(String value1, String value2) {
            addCriterion("location_ids between", value1, value2, "locationIds");
            return (Criteria) this;
        }

        public Criteria andLocationIdsNotBetween(String value1, String value2) {
            addCriterion("location_ids not between", value1, value2, "locationIds");
            return (Criteria) this;
        }

        public Criteria andExpiryTypeIsNull() {
            addCriterion("expiry_type is null");
            return (Criteria) this;
        }

        public Criteria andExpiryTypeIsNotNull() {
            addCriterion("expiry_type is not null");
            return (Criteria) this;
        }

        public Criteria andExpiryTypeEqualTo(Integer value) {
            addCriterion("expiry_type =", value, "expiryType");
            return (Criteria) this;
        }

        public Criteria andExpiryTypeNotEqualTo(Integer value) {
            addCriterion("expiry_type <>", value, "expiryType");
            return (Criteria) this;
        }

        public Criteria andExpiryTypeGreaterThan(Integer value) {
            addCriterion("expiry_type >", value, "expiryType");
            return (Criteria) this;
        }

        public Criteria andExpiryTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("expiry_type >=", value, "expiryType");
            return (Criteria) this;
        }

        public Criteria andExpiryTypeLessThan(Integer value) {
            addCriterion("expiry_type <", value, "expiryType");
            return (Criteria) this;
        }

        public Criteria andExpiryTypeLessThanOrEqualTo(Integer value) {
            addCriterion("expiry_type <=", value, "expiryType");
            return (Criteria) this;
        }

        public Criteria andExpiryTypeIn(List<Integer> values) {
            addCriterion("expiry_type in", values, "expiryType");
            return (Criteria) this;
        }

        public Criteria andExpiryTypeNotIn(List<Integer> values) {
            addCriterion("expiry_type not in", values, "expiryType");
            return (Criteria) this;
        }

        public Criteria andExpiryTypeBetween(Integer value1, Integer value2) {
            addCriterion("expiry_type between", value1, value2, "expiryType");
            return (Criteria) this;
        }

        public Criteria andExpiryTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("expiry_type not between", value1, value2, "expiryType");
            return (Criteria) this;
        }

        public Criteria andExpiryTimeIsNull() {
            addCriterion("expiry_time is null");
            return (Criteria) this;
        }

        public Criteria andExpiryTimeIsNotNull() {
            addCriterion("expiry_time is not null");
            return (Criteria) this;
        }

        public Criteria andExpiryTimeEqualTo(Long value) {
            addCriterion("expiry_time =", value, "expiryTime");
            return (Criteria) this;
        }

        public Criteria andExpiryTimeNotEqualTo(Long value) {
            addCriterion("expiry_time <>", value, "expiryTime");
            return (Criteria) this;
        }

        public Criteria andExpiryTimeGreaterThan(Long value) {
            addCriterion("expiry_time >", value, "expiryTime");
            return (Criteria) this;
        }

        public Criteria andExpiryTimeGreaterThanOrEqualTo(Long value) {
            addCriterion("expiry_time >=", value, "expiryTime");
            return (Criteria) this;
        }

        public Criteria andExpiryTimeLessThan(Long value) {
            addCriterion("expiry_time <", value, "expiryTime");
            return (Criteria) this;
        }

        public Criteria andExpiryTimeLessThanOrEqualTo(Long value) {
            addCriterion("expiry_time <=", value, "expiryTime");
            return (Criteria) this;
        }

        public Criteria andExpiryTimeIn(List<Long> values) {
            addCriterion("expiry_time in", values, "expiryTime");
            return (Criteria) this;
        }

        public Criteria andExpiryTimeNotIn(List<Long> values) {
            addCriterion("expiry_time not in", values, "expiryTime");
            return (Criteria) this;
        }

        public Criteria andExpiryTimeBetween(Long value1, Long value2) {
            addCriterion("expiry_time between", value1, value2, "expiryTime");
            return (Criteria) this;
        }

        public Criteria andExpiryTimeNotBetween(Long value1, Long value2) {
            addCriterion("expiry_time not between", value1, value2, "expiryTime");
            return (Criteria) this;
        }

        public Criteria andSourceIsNull() {
            addCriterion("\"source\" is null");
            return (Criteria) this;
        }

        public Criteria andSourceIsNotNull() {
            addCriterion("\"source\" is not null");
            return (Criteria) this;
        }

        public Criteria andSourceEqualTo(Integer value) {
            addCriterion("\"source\" =", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotEqualTo(Integer value) {
            addCriterion("\"source\" <>", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThan(Integer value) {
            addCriterion("\"source\" >", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("\"source\" >=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThan(Integer value) {
            addCriterion("\"source\" <", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceLessThanOrEqualTo(Integer value) {
            addCriterion("\"source\" <=", value, "source");
            return (Criteria) this;
        }

        public Criteria andSourceIn(List<Integer> values) {
            addCriterion("\"source\" in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotIn(List<Integer> values) {
            addCriterion("\"source\" not in", values, "source");
            return (Criteria) this;
        }

        public Criteria andSourceBetween(Integer value1, Integer value2) {
            addCriterion("\"source\" between", value1, value2, "source");
            return (Criteria) this;
        }

        public Criteria andSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("\"source\" not between", value1, value2, "source");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
