package com.moego.svc.marketing.entity;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import lombok.Data;

@Data
public class DiscountCodeLog {
    /**
     * id
     */
    private Long id;

    /**
     * business id
     */
    private Long businessId;

    /**
     * client id
     */
    private Long clientId;

    /**
     * code id
     */
    private Long codeId;

    /**
     * pet ids
     */
    private String petIds;

    /**
     * redeem type, 1-appointment, 2-online booking, 3-product
     */
    private Integer redeemType;

    /**
     * redeem time
     */
    private OffsetDateTime redeemTime;

    /**
     * discount sales
     */
    private BigDecimal discountSales;

    /**
     * invoice sales
     */
    private BigDecimal invoiceSales;

    /**
     * redeem id
     */
    private Long redeemId;

    /**
     * create by
     */
    private Long redeemBy;

    /**
     * company id
     */
    private Long companyId;

    /**
     * order id
     */
    private Long orderId;
}
