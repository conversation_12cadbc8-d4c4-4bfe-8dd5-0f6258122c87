package com.moego.svc.marketing.entity;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

public class DiscountCodeLogExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public DiscountCodeLogExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNull() {
            addCriterion("business_id is null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIsNotNull() {
            addCriterion("business_id is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessIdEqualTo(Long value) {
            addCriterion("business_id =", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotEqualTo(Long value) {
            addCriterion("business_id <>", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThan(Long value) {
            addCriterion("business_id >", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdGreaterThanOrEqualTo(Long value) {
            addCriterion("business_id >=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThan(Long value) {
            addCriterion("business_id <", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdLessThanOrEqualTo(Long value) {
            addCriterion("business_id <=", value, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdIn(List<Long> values) {
            addCriterion("business_id in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotIn(List<Long> values) {
            addCriterion("business_id not in", values, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdBetween(Long value1, Long value2) {
            addCriterion("business_id between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andBusinessIdNotBetween(Long value1, Long value2) {
            addCriterion("business_id not between", value1, value2, "businessId");
            return (Criteria) this;
        }

        public Criteria andClientIdIsNull() {
            addCriterion("client_id is null");
            return (Criteria) this;
        }

        public Criteria andClientIdIsNotNull() {
            addCriterion("client_id is not null");
            return (Criteria) this;
        }

        public Criteria andClientIdEqualTo(Long value) {
            addCriterion("client_id =", value, "clientId");
            return (Criteria) this;
        }

        public Criteria andClientIdNotEqualTo(Long value) {
            addCriterion("client_id <>", value, "clientId");
            return (Criteria) this;
        }

        public Criteria andClientIdGreaterThan(Long value) {
            addCriterion("client_id >", value, "clientId");
            return (Criteria) this;
        }

        public Criteria andClientIdGreaterThanOrEqualTo(Long value) {
            addCriterion("client_id >=", value, "clientId");
            return (Criteria) this;
        }

        public Criteria andClientIdLessThan(Long value) {
            addCriterion("client_id <", value, "clientId");
            return (Criteria) this;
        }

        public Criteria andClientIdLessThanOrEqualTo(Long value) {
            addCriterion("client_id <=", value, "clientId");
            return (Criteria) this;
        }

        public Criteria andClientIdIn(List<Long> values) {
            addCriterion("client_id in", values, "clientId");
            return (Criteria) this;
        }

        public Criteria andClientIdNotIn(List<Long> values) {
            addCriterion("client_id not in", values, "clientId");
            return (Criteria) this;
        }

        public Criteria andClientIdBetween(Long value1, Long value2) {
            addCriterion("client_id between", value1, value2, "clientId");
            return (Criteria) this;
        }

        public Criteria andClientIdNotBetween(Long value1, Long value2) {
            addCriterion("client_id not between", value1, value2, "clientId");
            return (Criteria) this;
        }

        public Criteria andCodeIdIsNull() {
            addCriterion("code_id is null");
            return (Criteria) this;
        }

        public Criteria andCodeIdIsNotNull() {
            addCriterion("code_id is not null");
            return (Criteria) this;
        }

        public Criteria andCodeIdEqualTo(Long value) {
            addCriterion("code_id =", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdNotEqualTo(Long value) {
            addCriterion("code_id <>", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdGreaterThan(Long value) {
            addCriterion("code_id >", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdGreaterThanOrEqualTo(Long value) {
            addCriterion("code_id >=", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdLessThan(Long value) {
            addCriterion("code_id <", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdLessThanOrEqualTo(Long value) {
            addCriterion("code_id <=", value, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdIn(List<Long> values) {
            addCriterion("code_id in", values, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdNotIn(List<Long> values) {
            addCriterion("code_id not in", values, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdBetween(Long value1, Long value2) {
            addCriterion("code_id between", value1, value2, "codeId");
            return (Criteria) this;
        }

        public Criteria andCodeIdNotBetween(Long value1, Long value2) {
            addCriterion("code_id not between", value1, value2, "codeId");
            return (Criteria) this;
        }

        public Criteria andPetIdsIsNull() {
            addCriterion("pet_ids is null");
            return (Criteria) this;
        }

        public Criteria andPetIdsIsNotNull() {
            addCriterion("pet_ids is not null");
            return (Criteria) this;
        }

        public Criteria andPetIdsEqualTo(String value) {
            addCriterion("pet_ids =", value, "petIds");
            return (Criteria) this;
        }

        public Criteria andPetIdsNotEqualTo(String value) {
            addCriterion("pet_ids <>", value, "petIds");
            return (Criteria) this;
        }

        public Criteria andPetIdsGreaterThan(String value) {
            addCriterion("pet_ids >", value, "petIds");
            return (Criteria) this;
        }

        public Criteria andPetIdsGreaterThanOrEqualTo(String value) {
            addCriterion("pet_ids >=", value, "petIds");
            return (Criteria) this;
        }

        public Criteria andPetIdsLessThan(String value) {
            addCriterion("pet_ids <", value, "petIds");
            return (Criteria) this;
        }

        public Criteria andPetIdsLessThanOrEqualTo(String value) {
            addCriterion("pet_ids <=", value, "petIds");
            return (Criteria) this;
        }

        public Criteria andPetIdsLike(String value) {
            addCriterion("pet_ids like", value, "petIds");
            return (Criteria) this;
        }

        public Criteria andPetIdsNotLike(String value) {
            addCriterion("pet_ids not like", value, "petIds");
            return (Criteria) this;
        }

        public Criteria andPetIdsIn(List<String> values) {
            addCriterion("pet_ids in", values, "petIds");
            return (Criteria) this;
        }

        public Criteria andPetIdsNotIn(List<String> values) {
            addCriterion("pet_ids not in", values, "petIds");
            return (Criteria) this;
        }

        public Criteria andPetIdsBetween(String value1, String value2) {
            addCriterion("pet_ids between", value1, value2, "petIds");
            return (Criteria) this;
        }

        public Criteria andPetIdsNotBetween(String value1, String value2) {
            addCriterion("pet_ids not between", value1, value2, "petIds");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeIsNull() {
            addCriterion("redeem_type is null");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeIsNotNull() {
            addCriterion("redeem_type is not null");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeEqualTo(Integer value) {
            addCriterion("redeem_type =", value, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeNotEqualTo(Integer value) {
            addCriterion("redeem_type <>", value, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeGreaterThan(Integer value) {
            addCriterion("redeem_type >", value, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("redeem_type >=", value, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeLessThan(Integer value) {
            addCriterion("redeem_type <", value, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeLessThanOrEqualTo(Integer value) {
            addCriterion("redeem_type <=", value, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeIn(List<Integer> values) {
            addCriterion("redeem_type in", values, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeNotIn(List<Integer> values) {
            addCriterion("redeem_type not in", values, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeBetween(Integer value1, Integer value2) {
            addCriterion("redeem_type between", value1, value2, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("redeem_type not between", value1, value2, "redeemType");
            return (Criteria) this;
        }

        public Criteria andRedeemTimeIsNull() {
            addCriterion("redeem_time is null");
            return (Criteria) this;
        }

        public Criteria andRedeemTimeIsNotNull() {
            addCriterion("redeem_time is not null");
            return (Criteria) this;
        }

        public Criteria andRedeemTimeEqualTo(OffsetDateTime value) {
            addCriterion("redeem_time =", value, "redeemTime");
            return (Criteria) this;
        }

        public Criteria andRedeemTimeNotEqualTo(OffsetDateTime value) {
            addCriterion("redeem_time <>", value, "redeemTime");
            return (Criteria) this;
        }

        public Criteria andRedeemTimeGreaterThan(OffsetDateTime value) {
            addCriterion("redeem_time >", value, "redeemTime");
            return (Criteria) this;
        }

        public Criteria andRedeemTimeGreaterThanOrEqualTo(OffsetDateTime value) {
            addCriterion("redeem_time >=", value, "redeemTime");
            return (Criteria) this;
        }

        public Criteria andRedeemTimeLessThan(OffsetDateTime value) {
            addCriterion("redeem_time <", value, "redeemTime");
            return (Criteria) this;
        }

        public Criteria andRedeemTimeLessThanOrEqualTo(OffsetDateTime value) {
            addCriterion("redeem_time <=", value, "redeemTime");
            return (Criteria) this;
        }

        public Criteria andRedeemTimeIn(List<OffsetDateTime> values) {
            addCriterion("redeem_time in", values, "redeemTime");
            return (Criteria) this;
        }

        public Criteria andRedeemTimeNotIn(List<OffsetDateTime> values) {
            addCriterion("redeem_time not in", values, "redeemTime");
            return (Criteria) this;
        }

        public Criteria andRedeemTimeBetween(OffsetDateTime value1, OffsetDateTime value2) {
            addCriterion("redeem_time between", value1, value2, "redeemTime");
            return (Criteria) this;
        }

        public Criteria andRedeemTimeNotBetween(OffsetDateTime value1, OffsetDateTime value2) {
            addCriterion("redeem_time not between", value1, value2, "redeemTime");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesIsNull() {
            addCriterion("discount_sales is null");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesIsNotNull() {
            addCriterion("discount_sales is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesEqualTo(BigDecimal value) {
            addCriterion("discount_sales =", value, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesNotEqualTo(BigDecimal value) {
            addCriterion("discount_sales <>", value, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesGreaterThan(BigDecimal value) {
            addCriterion("discount_sales >", value, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_sales >=", value, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesLessThan(BigDecimal value) {
            addCriterion("discount_sales <", value, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("discount_sales <=", value, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesIn(List<BigDecimal> values) {
            addCriterion("discount_sales in", values, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesNotIn(List<BigDecimal> values) {
            addCriterion("discount_sales not in", values, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_sales between", value1, value2, "discountSales");
            return (Criteria) this;
        }

        public Criteria andDiscountSalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount_sales not between", value1, value2, "discountSales");
            return (Criteria) this;
        }

        public Criteria andRedeemIdIsNull() {
            addCriterion("redeem_id is null");
            return (Criteria) this;
        }

        public Criteria andRedeemIdIsNotNull() {
            addCriterion("redeem_id is not null");
            return (Criteria) this;
        }

        public Criteria andRedeemIdEqualTo(Long value) {
            addCriterion("redeem_id =", value, "redeemId");
            return (Criteria) this;
        }

        public Criteria andRedeemIdNotEqualTo(Long value) {
            addCriterion("redeem_id <>", value, "redeemId");
            return (Criteria) this;
        }

        public Criteria andRedeemIdGreaterThan(Long value) {
            addCriterion("redeem_id >", value, "redeemId");
            return (Criteria) this;
        }

        public Criteria andRedeemIdGreaterThanOrEqualTo(Long value) {
            addCriterion("redeem_id >=", value, "redeemId");
            return (Criteria) this;
        }

        public Criteria andRedeemIdLessThan(Long value) {
            addCriterion("redeem_id <", value, "redeemId");
            return (Criteria) this;
        }

        public Criteria andRedeemIdLessThanOrEqualTo(Long value) {
            addCriterion("redeem_id <=", value, "redeemId");
            return (Criteria) this;
        }

        public Criteria andRedeemIdIn(List<Long> values) {
            addCriterion("redeem_id in", values, "redeemId");
            return (Criteria) this;
        }

        public Criteria andRedeemIdNotIn(List<Long> values) {
            addCriterion("redeem_id not in", values, "redeemId");
            return (Criteria) this;
        }

        public Criteria andRedeemIdBetween(Long value1, Long value2) {
            addCriterion("redeem_id between", value1, value2, "redeemId");
            return (Criteria) this;
        }

        public Criteria andRedeemIdNotBetween(Long value1, Long value2) {
            addCriterion("redeem_id not between", value1, value2, "redeemId");
            return (Criteria) this;
        }

        public Criteria andRedeemByIsNull() {
            addCriterion("redeem_by is null");
            return (Criteria) this;
        }

        public Criteria andRedeemByIsNotNull() {
            addCriterion("redeem_by is not null");
            return (Criteria) this;
        }

        public Criteria andRedeemByEqualTo(Long value) {
            addCriterion("redeem_by =", value, "redeemBy");
            return (Criteria) this;
        }

        public Criteria andRedeemByNotEqualTo(Long value) {
            addCriterion("redeem_by <>", value, "redeemBy");
            return (Criteria) this;
        }

        public Criteria andRedeemByGreaterThan(Long value) {
            addCriterion("redeem_by >", value, "redeemBy");
            return (Criteria) this;
        }

        public Criteria andRedeemByGreaterThanOrEqualTo(Long value) {
            addCriterion("redeem_by >=", value, "redeemBy");
            return (Criteria) this;
        }

        public Criteria andRedeemByLessThan(Long value) {
            addCriterion("redeem_by <", value, "redeemBy");
            return (Criteria) this;
        }

        public Criteria andRedeemByLessThanOrEqualTo(Long value) {
            addCriterion("redeem_by <=", value, "redeemBy");
            return (Criteria) this;
        }

        public Criteria andRedeemByIn(List<Long> values) {
            addCriterion("redeem_by in", values, "redeemBy");
            return (Criteria) this;
        }

        public Criteria andRedeemByNotIn(List<Long> values) {
            addCriterion("redeem_by not in", values, "redeemBy");
            return (Criteria) this;
        }

        public Criteria andRedeemByBetween(Long value1, Long value2) {
            addCriterion("redeem_by between", value1, value2, "redeemBy");
            return (Criteria) this;
        }

        public Criteria andRedeemByNotBetween(Long value1, Long value2) {
            addCriterion("redeem_by not between", value1, value2, "redeemBy");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNull() {
            addCriterion("company_id is null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIsNotNull() {
            addCriterion("company_id is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyIdEqualTo(Long value) {
            addCriterion("company_id =", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotEqualTo(Long value) {
            addCriterion("company_id <>", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThan(Long value) {
            addCriterion("company_id >", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdGreaterThanOrEqualTo(Long value) {
            addCriterion("company_id >=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThan(Long value) {
            addCriterion("company_id <", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdLessThanOrEqualTo(Long value) {
            addCriterion("company_id <=", value, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdIn(List<Long> values) {
            addCriterion("company_id in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotIn(List<Long> values) {
            addCriterion("company_id not in", values, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdBetween(Long value1, Long value2) {
            addCriterion("company_id between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andCompanyIdNotBetween(Long value1, Long value2) {
            addCriterion("company_id not between", value1, value2, "companyId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(Long value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(Long value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(Long value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(Long value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(Long value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(Long value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<Long> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<Long> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(Long value1, Long value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(Long value1, Long value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andInvoiceSalesIsNull() {
            addCriterion("invoice_sales is null");
            return (Criteria) this;
        }

        public Criteria andInvoiceSalesIsNotNull() {
            addCriterion("invoice_sales is not null");
            return (Criteria) this;
        }

        public Criteria andInvoiceSalesEqualTo(BigDecimal value) {
            addCriterion("invoice_sales =", value, "invoiceSales");
            return (Criteria) this;
        }

        public Criteria andInvoiceSalesNotEqualTo(BigDecimal value) {
            addCriterion("invoice_sales <>", value, "invoiceSales");
            return (Criteria) this;
        }

        public Criteria andInvoiceSalesGreaterThan(BigDecimal value) {
            addCriterion("invoice_sales >", value, "invoiceSales");
            return (Criteria) this;
        }

        public Criteria andInvoiceSalesGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("invoice_sales >=", value, "invoiceSales");
            return (Criteria) this;
        }

        public Criteria andInvoiceSalesLessThan(BigDecimal value) {
            addCriterion("invoice_sales <", value, "invoiceSales");
            return (Criteria) this;
        }

        public Criteria andInvoiceSalesLessThanOrEqualTo(BigDecimal value) {
            addCriterion("invoice_sales <=", value, "invoiceSales");
            return (Criteria) this;
        }

        public Criteria andInvoiceSalesIn(List<BigDecimal> values) {
            addCriterion("invoice_sales in", values, "invoiceSales");
            return (Criteria) this;
        }

        public Criteria andInvoiceSalesNotIn(List<BigDecimal> values) {
            addCriterion("invoice_sales not in", values, "invoiceSales");
            return (Criteria) this;
        }

        public Criteria andInvoiceSalesBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invoice_sales between", value1, value2, "invoiceSales");
            return (Criteria) this;
        }

        public Criteria andInvoiceSalesNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("invoice_sales not between", value1, value2, "invoiceSales");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}
