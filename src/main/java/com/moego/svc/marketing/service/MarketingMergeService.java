package com.moego.svc.marketing.service;

import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.dto.CustomerPetMergeRelationDTO;
import com.moego.svc.marketing.entity.DiscountCode;
import com.moego.svc.marketing.entity.DiscountCodeLog;
import com.moego.svc.marketing.mapper.DiscountCodeLogMapper;
import com.moego.svc.marketing.mapper.DiscountCodeMapper;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class MarketingMergeService {

    private final DiscountCodeMapper discountCodeMapper;
    private final DiscountCodeLogMapper discountCodeLogMapper;

    private static final int BATCH_SIZE = 500;

    public void mergeDiscountCodeData(CustomerPetMergeRelationDTO mergedCustomerPet) {
        // discount code
        // 修改 discount_code 修改字段：client_ids 索引：business_id,company_id
        // customer update
        int offset = 0; // 起始位置

        List<DiscountCode> discountCodes;
        List<Long> longMergedCustomerIds = mergedCustomerPet.getSourceCustomerIds().stream()
                .map(Integer::longValue)
                .toList();

        do {
            // query
            discountCodes =
                    discountCodeMapper.selectByCompanyIdWithPage(mergedCustomerPet.getCompanyId(), BATCH_SIZE, offset);
            // find and update
            for (DiscountCode code : discountCodes) {
                List<Long> clientIds = JsonUtil.toList(code.getClientIds(), Long.class);
                if (CollectionUtils.isEmpty(clientIds)) {
                    continue;
                }
                if (longMergedCustomerIds.stream().anyMatch(clientIds::contains)) {
                    clientIds.removeAll(longMergedCustomerIds);
                    clientIds.add(mergedCustomerPet.getTargetCustomerId().longValue());
                    DiscountCode record = new DiscountCode();
                    record.setId(code.getId());
                    record.setClientIds(
                            JsonUtil.toJson(clientIds.stream().distinct().toList()));
                    discountCodeMapper.updateByPrimaryKeySelective(record);
                }
            }
            offset += BATCH_SIZE;

        } while (discountCodes.size() == BATCH_SIZE);

        // 修改 discount_code_log 修改字段：client_id,pet_ids 索引：company_id,code_id,client_id
        offset = 0; // 起始位置
        var clientIds = Stream.concat(
                        longMergedCustomerIds.stream(),
                        Stream.of(mergedCustomerPet.getTargetCustomerId().longValue()))
                .toList();
        List<DiscountCodeLog> discountCodeLogs;
        Map<Long, List<Long>> petMergeRelationsMap = Map.of();
        if (!CollectionUtils.isEmpty(mergedCustomerPet.getPetMergeRelations())) {
            petMergeRelationsMap = mergedCustomerPet.getPetMergeRelations().stream()
                    .collect(Collectors.toMap(
                            pet -> pet.getTargetPetId().longValue(), // 将 targetPetId 转换为 Long
                            pet -> pet.getSourcePetIds().stream()
                                    .map(Integer::longValue) // 将 mergedPetIds 转换为 List<Long>
                                    .collect(Collectors.toList())));
        }
        do {
            // query
            discountCodeLogs = discountCodeLogMapper.selectByCompanyIdWithPage(
                    mergedCustomerPet.getCompanyId(), clientIds, BATCH_SIZE, offset);
            // find and update
            for (DiscountCodeLog codeLog : discountCodeLogs) {
                List<Long> petIds = JsonUtil.toList(codeLog.getPetIds(), Long.class);
                // 不需要替换 customerId,且 petIds 为空
                if (!longMergedCustomerIds.contains(codeLog.getClientId()) && CollectionUtils.isEmpty(petIds)) {
                    continue;
                }
                DiscountCodeLog codeLogRecord = new DiscountCodeLog();
                codeLogRecord.setId(codeLog.getId());
                codeLogRecord.setClientId(
                        mergedCustomerPet.getTargetCustomerId().longValue());
                // pet update
                for (var petMerge : petMergeRelationsMap.entrySet()) {
                    if (petMerge.getValue().stream().anyMatch(petIds::contains)) {
                        petIds.removeAll(petMerge.getValue());
                        petIds.add(petMerge.getKey());
                    }
                }
                codeLogRecord.setPetIds(
                        JsonUtil.toJson(petIds.stream().distinct().toList()));
                discountCodeLogMapper.updateByPrimaryKeySelective(codeLogRecord);
            }
            offset += BATCH_SIZE;

        } while (discountCodeLogs.size() == BATCH_SIZE);
    }
}
