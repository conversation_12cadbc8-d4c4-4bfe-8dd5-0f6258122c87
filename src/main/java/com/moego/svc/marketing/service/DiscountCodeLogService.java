package com.moego.svc.marketing.service;

import com.moego.common.utils.Pagination;
import com.moego.idl.models.marketing.v1.RedeemType;
import com.moego.idl.service.marketing.v1.GetDiscountCodeLogOverviewOutput;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.api.IBusinessBusinessService;
import com.moego.server.business.client.IBusinessStaffClient;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.params.DescribeBusinessesParams;
import com.moego.server.business.params.DescribeStaffsParams;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerBasicDTO;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.customer.params.CustomerIdListParams;
import com.moego.svc.marketing.dto.DiscountCodeLogComposite;
import com.moego.svc.marketing.dto.DiscountSalesResult;
import com.moego.svc.marketing.dto.LogData;
import com.moego.svc.marketing.entity.DiscountCode;
import com.moego.svc.marketing.entity.DiscountCodeLog;
import com.moego.svc.marketing.entity.DiscountCodeLogExample;
import com.moego.svc.marketing.mapper.DiscountCodeLogMapper;
import com.moego.svc.marketing.mapper.DiscountCodeMapper;
import com.moego.svc.marketing.utils.PageInfo;
import com.moego.svc.marketing.utils.PageUtils;
import com.moego.svc.marketing.utils.Pair;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Service
@RequiredArgsConstructor
public class DiscountCodeLogService {

    private final DiscountCodeMapper discountCodeMapper;
    private final DiscountCodeLogMapper discountCodeLogMapper;
    private final IBusinessStaffClient businessStaffClient;
    private final ICustomerCustomerClient customerCustomerClient;
    private final IPetClient petClient;
    private final MigrateHelper migrateHelper;
    private final IBusinessBusinessService businessClient;

    public GetDiscountCodeLogOverviewOutput getDiscountCodeLogOverview(Long id, Long businessId, Long companyId) {
        if (Objects.equals(0L, companyId)) {
            companyId = businessClient
                    .getCompanyIdByBusinessId(Math.toIntExact(businessId))
                    .companyId();
        }

        GetDiscountCodeLogOverviewOutput.Builder builder = GetDiscountCodeLogOverviewOutput.newBuilder();

        DiscountCode discountCode = discountCodeMapper.selectByPrimaryKey(id);
        if (Objects.isNull(discountCode) || !Objects.equals(companyId, discountCode.getCompanyId())) {
            return builder.build();
        }

        DiscountSalesResult discountSalesResult;
        if (migrateHelper.isMigrate(companyId)) {
            discountSalesResult = discountCodeLogMapper.countByDiscountIdAndCompanyId(id, companyId);
        } else {
            discountSalesResult = discountCodeLogMapper.countByDiscountIdAndBusinessId(id, businessId);
        }

        if (Objects.isNull(discountSalesResult)) {
            return builder.build();
        }
        builder.setTotalUsage(discountSalesResult.getTotalUsage());
        builder.setTotalClient(discountSalesResult.getTotalClients());
        builder.setDiscountSales(discountSalesResult.getDiscountSales().doubleValue());
        builder.setInvoiceSales(discountSalesResult.getInvoiceSales().doubleValue());

        return builder.build();
    }

    public Pair<List<DiscountCodeLogComposite>, PageInfo> getDiscountCodeLogList(
            Long id, Long businessId, Long companyId, PageInfo pageInfo) {
        if (Objects.equals(0L, companyId)) {
            companyId = businessClient
                    .getCompanyIdByBusinessId(Math.toIntExact(businessId))
                    .companyId();
        }

        DiscountCodeLogExample example = new DiscountCodeLogExample();
        DiscountCodeLogExample.Criteria criteria =
                example.createCriteria().andCodeIdEqualTo(id).andCompanyIdEqualTo(companyId);
        if (!migrateHelper.isMigrate(companyId)) {
            criteria.andBusinessIdEqualTo(businessId);
        }
        example.setOrderByClause("id desc");

        Pair<List<DiscountCodeLog>, PageInfo> listPageInfoPair =
                PageUtils.selectPage(pageInfo, () -> discountCodeLogMapper.selectByExample(example));
        List<DiscountCodeLog> discountCodeLogList = listPageInfoPair.first();
        if (CollectionUtils.isEmpty(discountCodeLogList)) {
            return Pair.of(List.of(), listPageInfoPair.second());
        }

        LogData logData = getLogData(discountCodeLogList);
        List<DiscountCodeLogComposite> result = discountCodeLogList.stream()
                .map(discountCodeLog -> {
                    DiscountCodeLogComposite discountCodeLogComposite = new DiscountCodeLogComposite();
                    discountCodeLogComposite.setId(discountCodeLog.getId());
                    discountCodeLogComposite.setRedeemTime(discountCodeLog.getRedeemTime());
                    discountCodeLogComposite.setRedeemType(RedeemType.forNumber(discountCodeLog.getRedeemType()));
                    discountCodeLogComposite.setRedeemId(discountCodeLog.getRedeemId());
                    discountCodeLogComposite.setBusinessId(discountCodeLog.getBusinessId());
                    MoeStaffDto staffDto =
                            logData.getStaffNameMap().get(Math.toIntExact(discountCodeLog.getRedeemBy()));
                    if (Objects.nonNull(staffDto)) {
                        discountCodeLogComposite.setStaffName(staffDto.getFirstName() + " " + staffDto.getLastName());
                    }
                    MoeBusinessCustomerDTO customerDTO =
                            logData.getClientNameMap().get(Math.toIntExact(discountCodeLog.getClientId()));
                    if (Objects.nonNull(customerDTO)) {
                        discountCodeLogComposite.setClientName(
                                customerDTO.getFirstName() + " " + customerDTO.getLastName());
                    }
                    String petName = JsonUtil.toList(discountCodeLog.getPetIds(), Long.class).stream()
                            .map(Math::toIntExact)
                            .map(petId -> logData.getPetIdMap().getOrDefault(petId, null))
                            .filter(Objects::nonNull)
                            .map(CustomerPetDetailDTO::getPetName)
                            .collect(Collectors.joining(", "));
                    discountCodeLogComposite.setPetName(petName);
                    MoeBusinessDto businessInfo = logData.getBusinessMap()
                            .getOrDefault(Math.toIntExact(discountCodeLog.getBusinessId()), null);
                    if (Objects.nonNull(businessInfo)) {
                        discountCodeLogComposite.setLocationName(businessInfo.getBusinessName());
                    }
                    discountCodeLogComposite.setOrderId(discountCodeLog.getOrderId());
                    return discountCodeLogComposite;
                })
                .toList();
        return Pair.of(result, listPageInfoPair.second());
    }

    private LogData getLogData(List<DiscountCodeLog> discountCodeLogList) {
        List<Integer> staffIdList = discountCodeLogList.stream()
                .map(DiscountCodeLog::getRedeemBy)
                .distinct()
                .map(Math::toIntExact)
                .toList();
        List<Integer> clientIdList = discountCodeLogList.stream()
                .map(DiscountCodeLog::getClientId)
                .distinct()
                .map(Math::toIntExact)
                .toList();
        List<Integer> petIdList = discountCodeLogList.stream()
                .map(DiscountCodeLog::getPetIds)
                .flatMap(petId -> {
                    if (StringUtils.hasText(petId)) {
                        return JsonUtil.toList(petId, Long.class).stream();
                    }
                    return Stream.of();
                })
                .distinct()
                .map(Math::toIntExact)
                .toList();
        Set<Integer> businessIdList = discountCodeLogList.stream()
                .map(DiscountCodeLog::getBusinessId)
                .map(Math::toIntExact)
                .collect(Collectors.toSet());

        LogData logData = new LogData();
        List<CompletableFuture<Void>> completableFutureList = new ArrayList<>();
        completableFutureList.add(CompletableFuture.runAsync(
                () -> {
                    Map<Integer, MoeStaffDto> staffMap = businessStaffClient
                            .describeStaffs(DescribeStaffsParams.builder()
                                    .ids(new HashSet<>(staffIdList))
                                    .pagination(Pagination.ALL)
                                    .includeDeleted(true)
                                    .build())
                            .staffs()
                            .stream()
                            .collect(Collectors.toMap(MoeStaffDto::getId, Function.identity(), (a, b) -> b));
                    logData.setStaffNameMap(staffMap);
                },
                ThreadPool.getSubmitExecutor()));
        completableFutureList.add(CompletableFuture.runAsync(
                () -> {
                    logData.setClientNameMap(
                            customerCustomerClient
                                    .queryCustomerListWithDeleted(new CustomerIdListParams(clientIdList))
                                    .stream()
                                    .collect(Collectors.toMap(
                                            CustomerBasicDTO::getCustomerId, Function.identity(), (a, b) -> b)));
                },
                ThreadPool.getSubmitExecutor()));
        completableFutureList.add(CompletableFuture.runAsync(
                () -> {
                    logData.setPetIdMap(petClient.getCustomerPetListByIdList(petIdList).stream()
                            .collect(Collectors.toMap(
                                    CustomerPetDetailDTO::getPetId, Function.identity(), (a, b) -> b)));
                },
                ThreadPool.getSubmitExecutor()));
        completableFutureList.add(CompletableFuture.runAsync(
                () -> {
                    logData.setBusinessMap(businessClient
                            .describeBusinesses(DescribeBusinessesParams.builder()
                                    .ids(businessIdList)
                                    .pagination(Pagination.ALL)
                                    .build())
                            .businesses()
                            .stream()
                            .collect(Collectors.toMap(MoeBusinessDto::getId, Function.identity(), (a, b) -> b)));
                },
                ThreadPool.getSubmitExecutor()));
        CompletableFuture.allOf(completableFutureList.toArray(new CompletableFuture[0]))
                .join();
        return logData;
    }

    public Map<Long, Long> getLogMap(List<Long> discountCodeIdList, Long businessId, Long companyId, Long clientId) {
        DiscountCodeLogExample codeLogExample = new DiscountCodeLogExample();
        DiscountCodeLogExample.Criteria criteria =
                codeLogExample.createCriteria().andCompanyIdEqualTo(companyId);
        if (!migrateHelper.isMigrate(companyId)) {
            criteria.andBusinessIdEqualTo(businessId);
        }
        if (!CollectionUtils.isEmpty(discountCodeIdList)) {
            criteria.andCodeIdIn(discountCodeIdList);
        }
        if (Objects.nonNull(clientId) && clientId > 0) {
            criteria.andClientIdEqualTo(clientId);
        }
        return discountCodeLogMapper.selectByExample(codeLogExample).stream()
                .collect(Collectors.groupingBy(DiscountCodeLog::getCodeId, Collectors.counting()));
    }

    public boolean checkLogExist(Long id) {
        DiscountCodeLogExample example = new DiscountCodeLogExample();
        example.createCriteria().andCodeIdEqualTo(id);

        return discountCodeLogMapper.countByExample(example) > 0;
    }

    public List<DiscountCodeLog> getLogByCodeId(Long codeId) {
        DiscountCodeLogExample example = new DiscountCodeLogExample();
        example.createCriteria().andCodeIdEqualTo(codeId);

        return discountCodeLogMapper.selectByExample(example);
    }

    public List<DiscountCodeLog> getLogByRedeemId(Long redeemId, List<Long> idList) {
        if (redeemId <= 0) {
            return List.of();
        }
        DiscountCodeLogExample example = new DiscountCodeLogExample();
        DiscountCodeLogExample.Criteria criteria = example.createCriteria().andRedeemIdEqualTo(redeemId);
        if (!CollectionUtils.isEmpty(idList)) {
            criteria.andCodeIdIn(idList);
        }

        return discountCodeLogMapper.selectByExample(example);
    }

    public void deleteLog(Long redeemId, List<Long> idList) {
        if (redeemId <= 0) {
            return;
        }
        DiscountCodeLogExample example = new DiscountCodeLogExample();
        DiscountCodeLogExample.Criteria criteria = example.createCriteria().andRedeemIdEqualTo(redeemId);
        if (!CollectionUtils.isEmpty(idList)) {
            criteria.andCodeIdIn(idList);
        }

        discountCodeLogMapper.deleteByExample(example);
    }

    public void deleteLogByOrderId(Long orderId) {
        if (orderId <= 0) {
            return;
        }
        DiscountCodeLogExample example = new DiscountCodeLogExample();
        example.createCriteria().andOrderIdEqualTo(orderId);

        discountCodeLogMapper.deleteByExample(example);
    }

    public void batchInsert(List<DiscountCodeLog> discountCodeLogList) {
        discountCodeLogMapper.batchInsert(discountCodeLogList);
    }

    public List<DiscountCodeLog> getLogByRedeemId(Long redeemId) {
        if (redeemId <= 0) {
            return List.of();
        }
        return discountCodeLogMapper.selectByRedeemId(redeemId);
    }

    public List<DiscountCodeLog> getLogByOrderId(Long orderId) {
        if (orderId <= 0) {
            return List.of();
        }
        return discountCodeLogMapper.selectByOrderId(orderId);
    }
}
