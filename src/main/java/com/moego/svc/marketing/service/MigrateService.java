package com.moego.svc.marketing.service;

import com.moego.idl.models.marketing.v1.DiscountCodeStatus;
import com.moego.idl.service.order.v1.MigrateDiscountCodeInput;
import com.moego.idl.service.order.v1.OrderDiscountCodeServiceGrpc;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.grooming.api.IBookOnlineDepositService;
import com.moego.svc.marketing.entity.DiscountCode;
import com.moego.svc.marketing.entity.DiscountCodeExample;
import com.moego.svc.marketing.entity.DiscountCodeLog;
import com.moego.svc.marketing.entity.DiscountCodeLogExample;
import com.moego.svc.marketing.mapper.DiscountCodeLogMapper;
import com.moego.svc.marketing.mapper.DiscountCodeMapper;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class MigrateService {

    private final DiscountCodeMapper discountCodeMapper;
    private final DiscountCodeLogMapper discountCodeLogMapper;
    private final IBookOnlineDepositService bookOnlineDepositService;
    private final OrderDiscountCodeServiceGrpc.OrderDiscountCodeServiceBlockingStub orderDiscountCodeServiceClient;

    @Transactional
    public void migrateDiscountCode(
            Long companyId,
            Map<Long, Long> staffIdMap,
            Map<Long, Long> serviceIdMap,
            Map<Long, Long> clientIdMap,
            Map<Long, Long> petIdMap) {
        log.info("migrate discount code, companyId: {}", companyId);
        log.info("migrate discount code, staffIdMap: {}", staffIdMap);
        log.info("migrate discount code, serviceIdMap: {}", serviceIdMap);
        log.info("migrate discount code, clientIdMap: {}", clientIdMap);
        log.info("migrate discount code, petIdMap: {}", petIdMap);

        DiscountCodeExample example = new DiscountCodeExample();
        example.createCriteria().andCompanyIdEqualTo(companyId);
        List<DiscountCode> discountCodes = discountCodeMapper.selectByExample(example);

        Map<String, List<DiscountCode>> collect =
                discountCodes.stream().collect(Collectors.groupingBy(DiscountCode::getDiscountCode));
        collect.forEach((key, value) -> {
            if (value.size() > 1) {
                handleMultipleDiscountCodes(staffIdMap, serviceIdMap, clientIdMap, petIdMap, value);
            } else {
                handleSingleDiscountCode(staffIdMap, serviceIdMap, clientIdMap, petIdMap, value);
            }
        });
    }

    private void handleMultipleDiscountCodes(
            Map<Long, Long> staffIdMap,
            Map<Long, Long> serviceIdMap,
            Map<Long, Long> clientIdMap,
            Map<Long, Long> petIdMap,
            List<DiscountCode> value) {
        Map<Long, Long> discountCodeIdMap = new HashMap<>();
        Set<Long> orderIds = new HashSet<>();

        // 优先使用 active 状态的 discount code 为主，如果状态相同，则使用 totalUsage 最大的 discount code 为主
        List<DiscountCode> list = value.stream()
                .sorted(Comparator.comparingInt((DiscountCode code) ->
                                DiscountCodeStatus.DISCOUNT_CODE_STATUS_ACTIVE_VALUE == code.getStatus() ? 1 : 2)
                        .thenComparing(DiscountCode::getTotalUsage, Comparator.reverseOrder()))
                .collect(Collectors.toList());
        DiscountCode mainDiscountCode = list.get(0);
        list.remove(0);

        List<Long> serviceIds = JsonUtil.toList(mainDiscountCode.getServiceIds(), Long.class);
        List<Long> addOnIds = JsonUtil.toList(mainDiscountCode.getAddOnIds(), Long.class);
        List<Long> clientIds = JsonUtil.toList(mainDiscountCode.getClientIds(), Long.class);
        List<Long> productIds = JsonUtil.toList(mainDiscountCode.getProductIds(), Long.class);
        BigDecimal discountSales = mainDiscountCode.getDiscountSales();
        Integer totalUsage = mainDiscountCode.getTotalUsage();

        for (DiscountCode discountCode : list) {
            discountCodeIdMap.put(discountCode.getId(), mainDiscountCode.getId());
            DiscountCodeLogExample logExample = new DiscountCodeLogExample();
            logExample.createCriteria().andCodeIdIn(List.of(discountCode.getId(), mainDiscountCode.getId()));
            List<DiscountCodeLog> discountCodeLogs = discountCodeLogMapper.selectByExample(logExample);
            discountCodeLogs.forEach(discountCodeLog -> {
                orderIds.add(discountCodeLog.getOrderId());
                // set codeId
                discountCodeLog.setCodeId(mainDiscountCode.getId());
                // set clientId
                discountCodeLog.setClientId(
                        clientIdMap.getOrDefault(discountCodeLog.getClientId(), discountCodeLog.getClientId()));
                // set petIds
                if (StringUtils.hasText(discountCodeLog.getPetIds())) {
                    List<Long> petIds = new ArrayList<>();
                    JsonUtil.toList(discountCodeLog.getPetIds(), Long.class).forEach(petId -> {
                        petIds.add(petIdMap.getOrDefault(petId, petId));
                    });
                    discountCodeLog.setPetIds(JsonUtil.toJson(petIds));
                }
                // set staffId
                discountCodeLog.setRedeemBy(
                        staffIdMap.getOrDefault(discountCodeLog.getRedeemBy(), discountCodeLog.getRedeemBy()));
                discountCodeLogMapper.updateByPrimaryKey(discountCodeLog);
            });

            serviceIds.addAll(JsonUtil.toList(discountCode.getServiceIds(), Long.class));
            addOnIds.addAll(JsonUtil.toList(discountCode.getAddOnIds(), Long.class));
            productIds.addAll(JsonUtil.toList(discountCode.getProductIds(), Long.class));
            clientIds.addAll(JsonUtil.toList(discountCode.getClientIds(), Long.class));
            discountSales = discountSales.add(discountCode.getDiscountSales());
            totalUsage += discountCode.getTotalUsage();

            discountCode.setStatus(DiscountCodeStatus.DISCOUNT_CODE_STATUS_DELETED_VALUE);
            discountCode.setBusinessId(0L);
            discountCodeMapper.updateByPrimaryKey(discountCode);
        }

        serviceIds = serviceIds.stream()
                .map(serviceId -> serviceIdMap.getOrDefault(serviceId, serviceId))
                .distinct()
                .toList();
        addOnIds = addOnIds.stream()
                .map(addOnId -> serviceIdMap.getOrDefault(addOnId, addOnId))
                .distinct()
                .toList();
        clientIds = clientIds.stream()
                .map(clientId -> clientIdMap.getOrDefault(clientId, clientId))
                .distinct()
                .toList();
        productIds = productIds.stream().distinct().toList();

        mainDiscountCode.setServiceIds(JsonUtil.toJson(serviceIds));
        mainDiscountCode.setAddOnIds(JsonUtil.toJson(addOnIds));
        mainDiscountCode.setProductIds(JsonUtil.toJson(productIds));
        mainDiscountCode.setClientIds(JsonUtil.toJson(clientIds));
        mainDiscountCode.setDiscountSales(discountSales);
        mainDiscountCode.setTotalUsage(totalUsage);
        mainDiscountCode.setCreateBy(
                staffIdMap.getOrDefault(mainDiscountCode.getCreateBy(), mainDiscountCode.getCreateBy()));
        mainDiscountCode.setUpdateBy(
                staffIdMap.getOrDefault(mainDiscountCode.getUpdateBy(), mainDiscountCode.getUpdateBy()));
        mainDiscountCode.setBusinessId(0L);
        discountCodeMapper.updateByPrimaryKey(mainDiscountCode);

        if (CollectionUtils.isEmpty(discountCodeIdMap)) {
            return;
        }
        log.info("migrate discount code id map: {}", discountCodeIdMap);
        log.info("migrate order ids: {}", orderIds);
        orderDiscountCodeServiceClient.migrateDiscountCodeId(MigrateDiscountCodeInput.newBuilder()
                .putAllDiscountCodeIdMap(discountCodeIdMap)
                .build());
        bookOnlineDepositService.migrateDiscountCodeIds(discountCodeIdMap);
    }

    private void handleSingleDiscountCode(
            Map<Long, Long> staffIdMap,
            Map<Long, Long> serviceIdMap,
            Map<Long, Long> clientIdMap,
            Map<Long, Long> petIdMap,
            List<DiscountCode> value) {
        DiscountCode mainDiscountCode = value.get(0);
        DiscountCodeLogExample logExample = new DiscountCodeLogExample();
        logExample.createCriteria().andCodeIdEqualTo(mainDiscountCode.getId());
        List<DiscountCodeLog> discountCodeLogs = discountCodeLogMapper.selectByExample(logExample);
        discountCodeLogs.forEach(discountCodeLog -> {
            // set clientId
            discountCodeLog.setClientId(
                    clientIdMap.getOrDefault(discountCodeLog.getClientId(), discountCodeLog.getClientId()));
            // set petIds
            if (StringUtils.hasText(discountCodeLog.getPetIds())) {
                List<Long> petIds = new ArrayList<>();
                JsonUtil.toList(discountCodeLog.getPetIds(), Long.class).forEach(petId -> {
                    petIds.add(petIdMap.getOrDefault(petId, petId));
                });
                discountCodeLog.setPetIds(JsonUtil.toJson(petIds));
            }
            // set staffId
            discountCodeLog.setRedeemBy(
                    staffIdMap.getOrDefault(discountCodeLog.getRedeemBy(), discountCodeLog.getRedeemBy()));
            discountCodeLogMapper.updateByPrimaryKey(discountCodeLog);
        });

        List<Long> serviceIds = JsonUtil.toList(mainDiscountCode.getServiceIds(), Long.class);
        List<Long> addOnIds = JsonUtil.toList(mainDiscountCode.getAddOnIds(), Long.class);
        List<Long> clientIds = JsonUtil.toList(mainDiscountCode.getClientIds(), Long.class);
        serviceIds = serviceIds.stream()
                .map(serviceId -> serviceIdMap.getOrDefault(serviceId, serviceId))
                .distinct()
                .toList();
        addOnIds = addOnIds.stream()
                .map(addOnId -> serviceIdMap.getOrDefault(addOnId, addOnId))
                .distinct()
                .toList();
        clientIds = clientIds.stream()
                .map(clientId -> clientIdMap.getOrDefault(clientId, clientId))
                .distinct()
                .toList();

        mainDiscountCode.setServiceIds(JsonUtil.toJson(serviceIds));
        mainDiscountCode.setAddOnIds(JsonUtil.toJson(addOnIds));
        mainDiscountCode.setClientIds(JsonUtil.toJson(clientIds));
        mainDiscountCode.setCreateBy(
                staffIdMap.getOrDefault(mainDiscountCode.getCreateBy(), mainDiscountCode.getCreateBy()));
        mainDiscountCode.setUpdateBy(
                staffIdMap.getOrDefault(mainDiscountCode.getUpdateBy(), mainDiscountCode.getUpdateBy()));
        mainDiscountCode.setBusinessId(0L);
        discountCodeMapper.updateByPrimaryKey(mainDiscountCode);
    }
}
