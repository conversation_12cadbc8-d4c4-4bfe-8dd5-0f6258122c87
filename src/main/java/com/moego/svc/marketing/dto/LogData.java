package com.moego.svc.marketing.dto;

import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import java.util.Map;
import lombok.Data;

@Data
public class LogData {

    Map<Integer, MoeStaffDto> staffNameMap;

    Map<Integer, MoeBusinessCustomerDTO> clientNameMap;

    Map<Integer, CustomerPetDetailDTO> petIdMap;

    Map<Integer, MoeBusinessDto> businessMap;
}
