package com.moego.svc.marketing.dto;

import com.moego.idl.models.marketing.v1.RedeemType;
import java.time.OffsetDateTime;
import lombok.Data;

@Data
public class DiscountCodeLogComposite {

    /**
     * the unique id
     */
    Long id;

    /**
     * redeem type
     */
    RedeemType redeemType;

    /**
     * client name
     */
    String clientName;

    /**
     * pet name
     */
    String petName;

    /**
     * staff name
     */
    String staffName;

    /**
     * redeem time
     */
    OffsetDateTime redeemTime;

    /**
     * redeem id
     */
    Long redeemId;

    /**
     * location name
     */
    String locationName;

    /**
     * business id
     */
    Long businessId;

    /**
     * order id
     */
    Long orderId;
}
