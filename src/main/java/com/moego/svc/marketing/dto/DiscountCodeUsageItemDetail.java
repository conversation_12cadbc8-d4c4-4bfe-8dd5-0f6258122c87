package com.moego.svc.marketing.dto;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class DiscountCodeUsageItemDetail {

    /**
     * object id
     */
    private List<Long> objectIds;

    /**
     * item type
     */
    private String type;

    /**
     * discount sales amount
     */
    private BigDecimal discountSalesAmount;

    /**
     * discount code id
     */
    private Long discountCodeId;
}
