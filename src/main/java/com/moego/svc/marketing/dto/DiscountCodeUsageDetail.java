package com.moego.svc.marketing.dto;

import com.moego.idl.models.marketing.v1.RedeemType;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class DiscountCodeUsageDetail {

    /**
     * business id
     */
    Long businessId;

    /**
     * company id
     */
    Long companyId;

    /**
     * customer id
     */
    Long customerId;

    /**
     * appointment id
     */
    Long sourceId;

    /**
     * order id
     */
    Long orderId;

    /**
     * staff id
     */
    Long staffId;

    /**
     * redeem type
     */
    RedeemType redeemType;

    /**
     * invoice sales
     */
    BigDecimal invoiceSales;

    /**
     * order item detail list
     */
    List<DiscountCodeUsageItemDetail> discountCodeUsageItemDetailList;
}
