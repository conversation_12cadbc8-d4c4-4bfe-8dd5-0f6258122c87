package com.moego.svc.marketing.dto;

import java.math.BigDecimal;
import java.time.OffsetDateTime;
import java.util.List;
import lombok.Data;

@Data
public class DiscountCodeComposite {

    /**
     * id
     */
    private Long id;

    /**
     * business id
     */
    private Long businessId;

    /**
     * company id
     */
    private Long companyId;

    /**
     * discount code
     */
    private String discountCode;

    /**
     * description
     */
    private String description;

    /**
     * amount
     */
    private BigDecimal amount;

    /**
     * type, 1-percent, 2-fixed
     */
    private Integer type;

    /**
     * start date
     */
    private String startDate;

    /**
     * end date
     */
    private String endDate;

    /**
     * allowed all thing
     */
    private Boolean allowedAllThing;

    /**
     * allowed all services
     */
    private Boolean allowedAllServices;

    /**
     * service ids
     */
    private String serviceIds;

    /**
     * add on ids
     */
    private String addOnIds;

    /**
     * allowed all products
     */
    private Boolean allowedAllProducts;

    /**
     * product ids
     */
    private String productIds;

    /**
     * allowed all clients
     */
    private Boolean allowedAllClients;

    /**
     * allowed new clients
     */
    private Boolean allowedNewClients;

    /**
     * client group
     */
    private String clientsGroup;

    /**
     * client ids
     */
    private String clientIds;

    /**
     * location ids
     */
    private String locationIds;

    /**
     * limit usage
     */
    private Integer limitUsage;

    /**
     * limit number per client
     */
    private Integer limitNumberPerClient;

    /**
     * limit budget
     */
    private Integer limitBudget;

    /**
     * auto apply association
     */
    private Boolean autoApplyAssociation;

    /**
     * enable online booking
     */
    private Boolean enableOnlineBooking;

    /**
     * discount sales
     */
    private BigDecimal discountSales;

    /**
     * total usage
     */
    private Integer totalUsage;

    /**
     * status, 1-active, 2-inactive, 3-archived, 4-deleted
     */
    private Integer status;

    /**
     * create by
     */
    private Integer createBy;

    /**
     * update by
     */
    private Integer updateBy;

    /**
     * create time
     */
    private OffsetDateTime createTime;

    /**
     * update time
     */
    private OffsetDateTime updateTime;

    /**
     * service name list
     */
    private List<String> serviceNameList;

    /**
     * product name list
     */
    private List<String> productNameList;
    /**
     * expiry type
     */
    private Integer expiryType;
    /**
     * expiry time
     */
    private Long expiryTime;
    /**
     * source
     */
    private Integer source;
}
