package com.moego.svc.marketing.converter;

import com.google.protobuf.ProtocolMessageEnum;
import com.google.protobuf.Timestamp;
import com.moego.idl.models.marketing.v1.DiscountCodeCompositeView;
import com.moego.idl.models.marketing.v1.DiscountCodeLogModel;
import com.moego.idl.models.marketing.v1.DiscountCodeModel;
import com.moego.idl.models.marketing.v1.DiscountCodeModelOnlineBookingView;
import com.moego.idl.models.marketing.v1.DiscountCodeStatus;
import com.moego.idl.models.marketing.v1.DiscountCodeType;
import com.moego.idl.models.marketing.v1.ExpiryType;
import com.moego.idl.models.marketing.v1.RedeemType;
import com.moego.idl.service.marketing.v1.AutoApplyDiscountCodeInput;
import com.moego.idl.service.marketing.v1.CreateDiscountCodeInput;
import com.moego.idl.service.marketing.v1.EditDiscountCodeInput;
import com.moego.idl.service.marketing.v1.GetAvailableDiscountListForExistingInvoiceInput;
import com.moego.idl.service.marketing.v1.GetAvailableDiscountListForExistingInvoiceOutput;
import com.moego.idl.service.marketing.v1.GetAvailableDiscountListInput;
import com.moego.idl.service.marketing.v1.GetAvailableDiscountListOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeListOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeLogListOutput;
import com.moego.idl.service.marketing.v1.UseDiscountCodeInput;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.marketing.dto.DiscountCodeComposite;
import com.moego.svc.marketing.dto.DiscountCodeLogComposite;
import com.moego.svc.marketing.dto.DiscountCodeUsageDetail;
import com.moego.svc.marketing.dto.OrderInfoDetail;
import com.moego.svc.marketing.entity.DiscountCode;
import com.moego.svc.marketing.entity.DiscountCodeLog;
import com.moego.svc.marketing.utils.PageInfo;
import java.time.Instant;
import java.time.OffsetDateTime;
import java.util.List;
import org.mapstruct.AfterMapping;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN,
        uses = {PageConverter.class, StructConverter.class, TimeConverter.class})
public interface DiscountConverter {
    DiscountConverter INSTANCE = Mappers.getMapper(DiscountConverter.class);

    @Mapping(source = "pageInfo", target = "pagination", qualifiedByName = "toResponse")
    @Mapping(source = "list", target = "discountCodeCompositeViews")
    GetDiscountCodeListOutput toOutput(List<DiscountCodeComposite> list, PageInfo pageInfo);

    @Mapping(target = "serviceIds", ignore = true)
    @Mapping(target = "addOnIds", ignore = true)
    @Mapping(target = "productIds", ignore = true)
    @Mapping(target = "clientIds", ignore = true)
    @Mapping(target = "locationIds", ignore = true)
    DiscountCodeModel toModel(DiscountCode discountCode);

    @Mapping(target = "serviceIds", ignore = true)
    @Mapping(target = "addOnIds", ignore = true)
    @Mapping(target = "productIds", ignore = true)
    @Mapping(target = "clientIds", ignore = true)
    @Mapping(target = "locationIds", ignore = true)
    DiscountCodeCompositeView toModel(DiscountCodeComposite discountCode);

    @Mapping(target = "orderItemDetailList", source = "items")
    OrderInfoDetail toOrderInfoDetail(AutoApplyDiscountCodeInput input);

    @Mapping(target = "orderItemDetailList", source = "items")
    OrderInfoDetail toOrderInfoDetail(GetAvailableDiscountListInput input);

    @Mapping(target = "orderItemDetailList", source = "items")
    OrderInfoDetail toOrderInfoDetail(GetAvailableDiscountListForExistingInvoiceInput input);

    @Mapping(target = "discountCodeUsageItemDetailList", source = "discountCodeUsages")
    DiscountCodeUsageDetail toDiscountCodeUsageDetail(UseDiscountCodeInput input);

    @AfterMapping
    default void afterMapping(DiscountCode discountCode, @MappingTarget DiscountCodeModel.Builder discountCodeModel) {
        if (discountCodeModel.getServiceIdsList() != null) {
            List<Long> list = stringToList(discountCode.getServiceIds());
            discountCodeModel.addAllServiceIds(list);
        }
        if (discountCodeModel.getAddOnIdsList() != null) {
            List<Long> list = stringToList(discountCode.getAddOnIds());
            discountCodeModel.addAllAddOnIds(list);
        }
        if (discountCodeModel.getProductIdsList() != null) {
            List<Long> list = stringToList(discountCode.getProductIds());
            discountCodeModel.addAllProductIds(list);
        }
        if (discountCodeModel.getClientIdsList() != null) {
            List<Long> list = stringToList(discountCode.getClientIds());
            discountCodeModel.addAllClientIds(list);
        }
        if (discountCodeModel.getLocationIdsList() != null) {
            List<Long> list = stringToList(discountCode.getLocationIds());
            discountCodeModel.addAllLocationIds(list);
        }
    }

    @AfterMapping
    default void afterMapping(
            DiscountCodeComposite discountCodeComposite,
            @MappingTarget DiscountCodeCompositeView.Builder discountCodeCompositeView) {
        if (discountCodeCompositeView.getServiceIdsList() != null) {
            List<Long> list = stringToList(discountCodeComposite.getServiceIds());
            discountCodeCompositeView.addAllServiceIds(list);
        }
        if (discountCodeCompositeView.getAddOnIdsList() != null) {
            List<Long> list = stringToList(discountCodeComposite.getAddOnIds());
            discountCodeCompositeView.addAllAddOnIds(list);
        }
        if (discountCodeCompositeView.getProductIdsList() != null) {
            List<Long> list = stringToList(discountCodeComposite.getProductIds());
            discountCodeCompositeView.addAllProductIds(list);
        }
        if (discountCodeCompositeView.getClientIdsList() != null) {
            List<Long> list = stringToList(discountCodeComposite.getClientIds());
            discountCodeCompositeView.addAllClientIds(list);
        }
        if (discountCodeCompositeView.getLocationIdsList() != null) {
            List<Long> list = stringToList(discountCodeComposite.getLocationIds());
            discountCodeCompositeView.addAllLocationIds(list);
        }
        if (discountCodeCompositeView.getServiceNamesList() != null) {
            discountCodeCompositeView.addAllServiceNames(discountCodeComposite.getServiceNameList());
        }
        if (discountCodeCompositeView.getProductNamesList() != null) {
            discountCodeCompositeView.addAllProductNames(discountCodeComposite.getProductNameList());
        }
    }

    @Mapping(target = "serviceIds", source = "serviceIds", qualifiedByName = "listToString")
    @Mapping(target = "addOnIds", source = "addOnIds", qualifiedByName = "listToString")
    @Mapping(target = "productIds", source = "productIds", qualifiedByName = "listToString")
    @Mapping(target = "clientIds", source = "clientIds", qualifiedByName = "listToString")
    @Mapping(target = "locationIds", source = "locationIds", qualifiedByName = "listToString")
    @Mapping(target = "expiryType", source = "expiryDef.type")
    @Mapping(target = "expiryTime", source = "expiryDef.time")
    DiscountCode toDiscount(CreateDiscountCodeInput input);

    @Mapping(target = "serviceIds", source = "serviceIds", qualifiedByName = "listToString")
    @Mapping(target = "addOnIds", source = "addOnIds", qualifiedByName = "listToString")
    @Mapping(target = "productIds", source = "productIds", qualifiedByName = "listToString")
    @Mapping(target = "clientIds", source = "clientIds", qualifiedByName = "listToString")
    @Mapping(target = "locationIds", source = "locationIds", qualifiedByName = "listToString")
    @Mapping(target = "expiryType", source = "expiryDef.type")
    @Mapping(target = "expiryTime", source = "expiryDef.time")
    DiscountCode toDiscount(EditDiscountCodeInput input);

    @Mapping(source = "pageInfo", target = "pagination")
    @Mapping(source = "list", target = "discountCodeLogCompositeViews")
    GetDiscountCodeLogListOutput toLogOutput(List<DiscountCodeLogComposite> list, PageInfo pageInfo);

    @Mapping(target = "petIds", ignore = true)
    DiscountCodeLogModel toModel(DiscountCodeLog discountCodeLog);

    void update(DiscountCode newDiscountCode, @MappingTarget DiscountCode originDiscountCode);

    @AfterMapping
    default void afterMapping(
            DiscountCodeLog discountCodeLog, @MappingTarget DiscountCodeLogModel.Builder discountCodeLogModel) {
        if (discountCodeLogModel.getPetIdsList() != null) {
            List<Long> list = stringToList(discountCodeLog.getPetIds());
            discountCodeLogModel.addAllPetIds(list);
        }
    }

    @Mapping(source = "pageInfo", target = "pagination")
    @Mapping(source = "list", target = "discountCodeCompositeViews")
    GetAvailableDiscountListOutput toAvailableOutput(List<DiscountCodeComposite> list, PageInfo pageInfo);

    @Mapping(source = "list", target = "discountCodeModels")
    GetAvailableDiscountListForExistingInvoiceOutput toAvailableOutput(List<DiscountCode> list, Integer dummy);

    default DiscountCodeType mapType(Integer value) {
        return DiscountCodeType.forNumber(value);
    }

    default Integer mapType(DiscountCodeType value) {
        return value.getNumber();
    }

    default DiscountCodeStatus mapStatus(Integer value) {
        return DiscountCodeStatus.forNumber(value);
    }

    default Integer mapStatus(DiscountCodeStatus value) {
        return value.getNumber();
    }

    default RedeemType mapRedeemType(Integer value) {
        return RedeemType.forNumber(value);
    }

    default Integer mapRedeemType(RedeemType value) {
        return value.getNumber();
    }

    @Named("listToString")
    default String listToString(List<Long> value) {
        return JsonUtil.toJson(value);
    }

    default List<Long> stringToList(String value) {
        return JsonUtil.toList(value, Long.class);
    }

    default Timestamp map(OffsetDateTime value) {
        Instant instant = value.toInstant();
        return Timestamp.newBuilder().setSeconds(instant.getEpochSecond()).build();
    }

    DiscountCodeModelOnlineBookingView toDiscountCodeModelOnlineBookingView(DiscountCode discountCode);

    List<DiscountCodeComposite> toDiscountCodeComposite(List<DiscountCode> discountCode);

    @Mapping(target = "serviceNameList", ignore = true)
    @Mapping(target = "productNameList", ignore = true)
    DiscountCodeComposite toDiscountCodeComposite(DiscountCode discountCode);

    default Integer fromExpiryType(ExpiryType v) {
        return v.getNumber();
    }

    default ExpiryType toExpiryType(Integer v) {
        return ExpiryType.forNumber(v);
    }

    default Long fromTimestamp(Timestamp v) {
        return v.getSeconds();
    }

    default Timestamp toTimestamp(Long v) {
        return Timestamp.newBuilder().setSeconds(v).build();
    }

    default Integer fromProtoEnum(ProtocolMessageEnum v) {
        return v.getNumber();
    }

    default DiscountCodeModel.Source fromSource(Integer v) {
        return DiscountCodeModel.Source.forNumber(v);
    }
}
