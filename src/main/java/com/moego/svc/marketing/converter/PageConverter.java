/*
 * @since 2023-05-29 15:23:19
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.svc.marketing.converter;

import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.idl.utils.v2.PaginationResponse;
import com.moego.svc.marketing.utils.PageInfo;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface PageConverter {
    PageConverter INSTANCE = Mappers.getMapper(PageConverter.class);

    default PageInfo toPageInfo(PaginationRequest request) {
        var pageSize = request.hasPageSize() ? request.getPageSize() : 20;
        var pageNum = request.hasPageNum() ? request.getPageNum() : 1;
        return new PageInfo(pageNum, pageSize, 0);
    }

    default PageInfo toPageInfo(PaginationResponse response) {
        var pageSize = response.getPageSize();
        var pageNum = response.getPageNum();
        return new PageInfo(pageNum < 1 ? 1 : 0, pageSize, 0);
    }

    PaginationRequest toRequest(PageInfo pageInfo);

    @Named("toResponse")
    PaginationResponse toResponse(PageInfo pageInfo);
}
