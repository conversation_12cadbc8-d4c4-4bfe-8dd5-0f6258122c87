/*
 * @since 2023-05-29 15:23:19
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.svc.marketing.converter;

import com.moego.idl.utils.v1.Int64ListValue;
import com.moego.idl.utils.v1.StringListValue;
import java.util.Arrays;
import java.util.List;
import org.mapstruct.CollectionMappingStrategy;
import org.mapstruct.Mapper;
import org.mapstruct.NullValueCheckStrategy;
import org.mapstruct.NullValuePropertyMappingStrategy;
import org.mapstruct.ReportingPolicy;
import org.mapstruct.factory.Mappers;

@Mapper(
        collectionMappingStrategy = CollectionMappingStrategy.ADDER_PREFERRED,
        nullValueCheckStrategy = NullValueCheckStrategy.ALWAYS,
        nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE,
        unmappedTargetPolicy = ReportingPolicy.WARN,
        unmappedSourcePolicy = ReportingPolicy.WARN)
public interface StructConverter {
    StructConverter INSTANCE = Mappers.getMapper(StructConverter.class);

    default List<Long> toList(Int64ListValue value) {
        if (value == null) {
            return null;
        }
        return value.getValuesList();
    }

    default Int64ListValue toInt64ListValue(List<Long> value) {
        if (value == null) {
            return null;
        }
        return Int64ListValue.newBuilder().addAllValues(value).build();
    }

    default Int64ListValue toInt64ListValue(Long... value) {
        return toInt64ListValue(Arrays.asList(value));
    }

    default List<String> toList(StringListValue value) {
        if (value == null) {
            return null;
        }
        return value.getValuesList();
    }

    default StringListValue toStringListValue(List<String> value) {
        if (value == null) {
            return null;
        }
        return StringListValue.newBuilder().addAllValues(value).build();
    }

    default StringListValue toStringListValue(String... value) {
        return toStringListValue(Arrays.asList(value));
    }
}
