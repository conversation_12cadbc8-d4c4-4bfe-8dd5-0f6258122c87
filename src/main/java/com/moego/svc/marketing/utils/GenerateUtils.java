package com.moego.svc.marketing.utils;

import com.moego.lib.utils.RandomUtils;
import java.util.List;
import lombok.experimental.UtilityClass;

@UtilityClass
public class GenerateUtils {

    private static final List<String> MEANINGFUL_CODE = List.of(
            "FurTastic",
            "PetGroomJoy",
            "Purrfection",
            "BarkNBubbles",
            "ShineAndGroom",
            "MeowMagic",
            "WagYourTail",
            "Fluffy<PERSON>ove",
            "FurryVIP",
            "PetGlowUp",
            "HappyPawDay",
            "PawsomeDeals",
            "GroomingBliss",
            "WhiskerWhimsy",
            "PetPamperMe",
            "ShineOnPets",
            "Barktastic",
            "PurrfectlyPretty",
            "Pawfection",
            "PawPrints",
            "LoveYourPets",
            "TailWagTreat",
            "GroomingGlee",
            "MoeGo<PERSON>ily",
            "PetPerfection",
            "FuzzyDelight",
            "Petalicious",
            "GlamourPaws",
            "ShineAndShimmer",
            "CozyPawsRUs",
            "PetSmiles",
            "TailWagDeluxe",
            "FluffnBuff",
            "PetGroomMagic",
            "PamperYourFur",
            "GroomingGalore",
            "WhiskerWonder",
            "PetalGlowUp",
            "PurrfectGroom",
            "ShineBrightPaws",
            "PawSerenity",
            "LuxuryPetGroom",
            "PetalPamper",
            "TailWagBliss",
            "GroomingGoddess",
            "GlamPetGlow",
            "UltimateFurLove",
            "SparkleMyPaws",
            "PetGroomVIP",
            "FurryFriendBliss");

    public static String generateRandomString() {
        int index = RandomUtils.randomInt(70);
        if (index < MEANINGFUL_CODE.size()) {
            return MEANINGFUL_CODE.get(index);
        }

        int length = RandomUtils.randomInt(6) + 4;
        return RandomUtils.randomUpperCharAndNumString(length);
    }
}
