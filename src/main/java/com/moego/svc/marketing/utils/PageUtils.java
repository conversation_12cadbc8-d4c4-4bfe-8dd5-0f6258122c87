/*
 * @since 2023-04-09 19:08:45
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.svc.marketing.utils;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import java.util.List;
import java.util.function.Supplier;

public class PageUtils {

    public static <T> Pair<List<T>, PageInfo> selectPage(PageInfo pageInfo, Supplier<List<T>> supplier) {
        try (Page<T> page = PageHelper.startPage(pageInfo.pageNum(), pageInfo.pageSize())) {
            page.doSelectPage(supplier::get);
            return new Pair<>(page, new PageInfo(pageInfo.pageNum(), pageInfo.pageSize(), (int) page.getTotal()));
        }
    }
}
