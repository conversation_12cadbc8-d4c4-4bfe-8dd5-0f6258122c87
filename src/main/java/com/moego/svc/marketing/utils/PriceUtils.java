package com.moego.svc.marketing.utils;

import com.moego.idl.models.marketing.v1.DiscountCodeType;
import com.moego.svc.marketing.entity.DiscountCode;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

public class PriceUtils {

    /**
     * get discount price
     *
     * @param price
     * @param discountCode
     * @return Pair<price after discount, discount amount>
     */
    public static Pair<BigDecimal, BigDecimal> getDiscountPrice(BigDecimal price, DiscountCode discountCode) {
        BigDecimal discountAmount = discountCode.getAmount();
        Integer type = discountCode.getType();
        if (Objects.equals(type, DiscountCodeType.DISCOUNT_CODE_TYPE_FIXED_AMOUNT.getNumber())) {
            if (price.compareTo(discountAmount) < 0) {
                discountCode.setAmount(discountAmount.subtract(price));
                return new Pair<>(BigDecimal.ZERO, price);
            }
            discountCode.setAmount(BigDecimal.ZERO);
            return new Pair<>(price.subtract(discountAmount), discountAmount);
        }
        if (Objects.equals(type, DiscountCodeType.DISCOUNT_CODE_TYPE_PERCENTAGE.getNumber())) {
            BigDecimal afterDiscountAmount =
                    price.multiply(discountAmount).divide(new BigDecimal(100), RoundingMode.HALF_UP);
            return new Pair<>(price.subtract(afterDiscountAmount), afterDiscountAmount);
        }

        return null;
    }
}
