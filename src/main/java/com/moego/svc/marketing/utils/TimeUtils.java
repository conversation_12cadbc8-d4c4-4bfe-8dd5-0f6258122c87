/*
 * @since 2023-04-14 15:41:19
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.svc.marketing.utils;

import java.time.LocalDateTime;
import java.util.function.Consumer;
import java.util.function.Supplier;
import org.springframework.stereotype.Component;

@Component
public class TimeUtils {

    /**
     * Wrap {@link LocalDateTime#now()} for unit test.
     * <p>
     * The linux system get nanoseconds precision is 1,
     * but the postgresql store precision is 1000 (microseconds).
     * So we cannot compare the time created in memory
     * and selected from database.
     *
     * @return time current LocalDateTime
     */
    public LocalDateTime now() {
        return LocalDateTime.now();
    }

    public void fill(Supplier<LocalDateTime> getter, Consumer<LocalDateTime> setter) {
        if (getter.get() == null) {
            setter.accept(now());
        }
    }
}
