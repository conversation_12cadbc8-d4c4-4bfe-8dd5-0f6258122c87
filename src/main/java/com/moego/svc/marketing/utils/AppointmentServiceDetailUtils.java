package com.moego.svc.marketing.utils;

import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.appointment.v1.PetDetailModel;
import com.moego.idl.service.appointment.v1.GetPetDetailListResponse;
import java.util.ArrayList;
import java.util.List;
import org.springframework.util.CollectionUtils;

public class AppointmentServiceDetailUtils {
    public static boolean isServiceDetailEmpty(GetPetDetailListResponse resp) {
        return CollectionUtils.isEmpty(resp.getPetDetailsList())
                && CollectionUtils.isEmpty(resp.getPetEvaluationsList());
    }

    public static List<Long> getPetIds(GetPetDetailListResponse resp) {
        List<Long> petIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(resp.getPetDetailsList())) {
            petIds.addAll(resp.getPetDetailsList().stream()
                    .map(PetDetailModel::getPetId)
                    .toList());
        }
        if (!CollectionUtils.isEmpty(resp.getPetEvaluationsList())) {
            petIds.addAll(resp.getPetEvaluationsList().stream()
                    .map(EvaluationServiceModel::getPetId)
                    .toList());
        }
        return petIds;
    }
}
