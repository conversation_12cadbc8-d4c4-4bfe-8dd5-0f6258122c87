package com.moego.svc.marketing.controller;

import com.google.protobuf.Empty;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.marketing.v1.DiscountCodeModel;
import com.moego.idl.models.marketing.v1.DiscountCodeModelOnlineBookingView;
import com.moego.idl.service.marketing.v1.AutoApplyDiscountCodeInput;
import com.moego.idl.service.marketing.v1.AutoApplyDiscountCodeOutput;
import com.moego.idl.service.marketing.v1.ChangeStatusInput;
import com.moego.idl.service.marketing.v1.ChangeStatusOutput;
import com.moego.idl.service.marketing.v1.CheckDiscountCodeInput;
import com.moego.idl.service.marketing.v1.CheckDiscountCodeOutput;
import com.moego.idl.service.marketing.v1.CheckDiscountCodeValidForCustomerInput;
import com.moego.idl.service.marketing.v1.CheckDiscountCodeValidForCustomerOutput;
import com.moego.idl.service.marketing.v1.CreateDiscountCodeInput;
import com.moego.idl.service.marketing.v1.CreateDiscountCodeOutput;
import com.moego.idl.service.marketing.v1.DeleteDiscountCodeLogInput;
import com.moego.idl.service.marketing.v1.DiscountCodeServiceGrpc;
import com.moego.idl.service.marketing.v1.EditDiscountCodeInput;
import com.moego.idl.service.marketing.v1.EditDiscountCodeOutput;
import com.moego.idl.service.marketing.v1.GenerateDiscountCodeInput;
import com.moego.idl.service.marketing.v1.GenerateDiscountCodeOutput;
import com.moego.idl.service.marketing.v1.GetAvailableDiscountListForExistingInvoiceInput;
import com.moego.idl.service.marketing.v1.GetAvailableDiscountListForExistingInvoiceOutput;
import com.moego.idl.service.marketing.v1.GetAvailableDiscountListInput;
import com.moego.idl.service.marketing.v1.GetAvailableDiscountListOutput;
import com.moego.idl.service.marketing.v1.GetBusinessDiscountCodeConfigInput;
import com.moego.idl.service.marketing.v1.GetBusinessDiscountCodeConfigOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeByCodeInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeByCodeOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeConfigInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeConfigOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeListInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeListOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeLogListInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeLogListOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeLogOverviewInput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeLogOverviewOutput;
import com.moego.idl.service.marketing.v1.GetDiscountCodeOutput;
import com.moego.idl.service.marketing.v1.MigrateDiscountCodeInput;
import com.moego.idl.service.marketing.v1.MigrateDiscountCodeInputOutput;
import com.moego.idl.service.marketing.v1.UseDiscountCodeInput;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.params.ob.OBAnonymousParams;
import com.moego.svc.marketing.converter.DiscountConverter;
import com.moego.svc.marketing.converter.PageConverter;
import com.moego.svc.marketing.dto.DiscountCodeComposite;
import com.moego.svc.marketing.dto.DiscountCodeLogComposite;
import com.moego.svc.marketing.entity.DiscountCode;
import com.moego.svc.marketing.service.DiscountCodeLogService;
import com.moego.svc.marketing.service.DiscountCodeService;
import com.moego.svc.marketing.service.MigrateService;
import com.moego.svc.marketing.utils.PageInfo;
import com.moego.svc.marketing.utils.Pair;
import io.grpc.stub.StreamObserver;
import java.util.List;
import java.util.Objects;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class DiscountController extends DiscountCodeServiceGrpc.DiscountCodeServiceImplBase {

    private final DiscountCodeService discountCodeService;
    private final DiscountCodeLogService discountCodeLogService;
    private final MigrateService migrateService;
    private final IGroomingOnlineBookingService onlineBookingService;

    @Override
    public void generateDiscountCode(
            GenerateDiscountCodeInput request, StreamObserver<GenerateDiscountCodeOutput> responseObserver) {
        String code = discountCodeService.generateDiscountCode(request.getBusinessId(), request.getCompanyId());
        responseObserver.onNext(
                GenerateDiscountCodeOutput.newBuilder().setDiscountCode(code).build());
        responseObserver.onCompleted();
    }

    @Override
    public void checkDiscountCode(
            CheckDiscountCodeInput request, StreamObserver<CheckDiscountCodeOutput> responseObserver) {
        boolean result = discountCodeService.checkDiscountCodeDuplicate(
                request.getBusinessId(), request.getCompanyId(), request.getDiscountCode());
        responseObserver.onNext(
                CheckDiscountCodeOutput.newBuilder().setIsDuplicate(result).build());
        responseObserver.onCompleted();
    }

    @Override
    public void createDiscountCode(
            CreateDiscountCodeInput request, StreamObserver<CreateDiscountCodeOutput> responseObserver) {
        DiscountCode discountCode =
                discountCodeService.createDiscountCode(DiscountConverter.INSTANCE.toDiscount(request));
        responseObserver.onNext(CreateDiscountCodeOutput.newBuilder()
                .setId(discountCode.getId())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void editDiscountCode(
            EditDiscountCodeInput request, StreamObserver<EditDiscountCodeOutput> responseObserver) {
        DiscountCode discountCode = discountCodeService.editDiscountCode(
                DiscountConverter.INSTANCE.toDiscount(request),
                AuthContext.get().staffId());
        responseObserver.onNext(
                EditDiscountCodeOutput.newBuilder().setId(discountCode.getId()).build());
        responseObserver.onCompleted();
    }

    @Override
    public void getDiscountCode(GetDiscountCodeInput request, StreamObserver<GetDiscountCodeOutput> responseObserver) {
        DiscountCode discountCode =
                discountCodeService.getDiscountCode(request.getId(), request.getBusinessId(), request.getCompanyId());
        if (Objects.equals(discountCode.getEndDate(), DiscountCodeService.ON_GOING_DATE)) {
            discountCode.setEndDate(null);
        }

        GetDiscountCodeOutput output = GetDiscountCodeOutput.newBuilder()
                .setDiscountCodeModel(DiscountConverter.INSTANCE.toModel(discountCode))
                .setDiscountCodeSummaryDef(discountCodeService.getDiscountCodeSummary(discountCode))
                .build();
        responseObserver.onNext(output);
        responseObserver.onCompleted();
    }

    @Override
    public void getDiscountCodeByCode(
            GetDiscountCodeByCodeInput request, StreamObserver<GetDiscountCodeByCodeOutput> responseObserver) {
        DiscountCode discountCode = discountCodeService.getDiscountCode(
                request.getDiscountCode(), request.getBusinessId(), request.getCompanyId());

        GetDiscountCodeByCodeOutput.Builder builder = GetDiscountCodeByCodeOutput.newBuilder();
        if (Objects.nonNull(discountCode)) {
            builder.setDiscountCodeModel(DiscountConverter.INSTANCE.toModel(discountCode));
        }

        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    public void getDiscountCodeList(
            GetDiscountCodeListInput request, StreamObserver<GetDiscountCodeListOutput> responseObserver) {
        PageInfo pageInfo = PageConverter.INSTANCE.toPageInfo(request.getPagination());
        Pair<List<DiscountCodeComposite>, PageInfo> result = discountCodeService.getDiscountCodeCompositeListByPage(
                pageInfo,
                request.getStatusList(),
                request.getDiscountCode(),
                request.getIdsList(),
                request.getBusinessId(),
                request.getCompanyId());
        result.first().forEach(discountCode -> {
            if (Objects.equals(discountCode.getEndDate(), DiscountCodeService.ON_GOING_DATE)) {
                discountCode.setEndDate(null);
            }
        });
        responseObserver.onNext(DiscountConverter.INSTANCE.toOutput(result.first(), result.second()));
        responseObserver.onCompleted();
    }

    @Override
    public void getDiscountCodeLogOverview(
            GetDiscountCodeLogOverviewInput request,
            StreamObserver<GetDiscountCodeLogOverviewOutput> responseObserver) {
        GetDiscountCodeLogOverviewOutput output = discountCodeLogService.getDiscountCodeLogOverview(
                request.getId(), request.getBusinessId(), request.getCompanyId());
        responseObserver.onNext(output);
        responseObserver.onCompleted();
    }

    @Override
    public void getDiscountCodeLogList(
            GetDiscountCodeLogListInput request, StreamObserver<GetDiscountCodeLogListOutput> responseObserver) {
        PageInfo pageInfo = PageConverter.INSTANCE.toPageInfo(request.getPagination());
        Pair<List<DiscountCodeLogComposite>, PageInfo> result = discountCodeLogService.getDiscountCodeLogList(
                request.getId(), request.getBusinessId(), request.getCompanyId(), pageInfo);
        responseObserver.onNext(DiscountConverter.INSTANCE.toLogOutput(result.first(), result.second()));
        responseObserver.onCompleted();
    }

    @Override
    public void changeStatus(ChangeStatusInput request, StreamObserver<ChangeStatusOutput> responseObserver) {
        boolean result = discountCodeService.changeStatus(
                request.getId(),
                request.getCompanyId(),
                request.getStaffId(),
                request.getStatus(),
                request.hasExpiryDef() ? request.getExpiryDef() : null);
        responseObserver.onNext(
                ChangeStatusOutput.newBuilder().setSuccess(result).build());
        responseObserver.onCompleted();
    }

    @Override
    public void checkDiscountCodeValidForCustomer(
            CheckDiscountCodeValidForCustomerInput request,
            StreamObserver<CheckDiscountCodeValidForCustomerOutput> responseObserver) {
        Integer businessId;
        if (request.hasBusinessId()) {
            businessId = Math.toIntExact(request.getBusinessId());
        } else {
            OBAnonymousParams anonymousParams =
                    new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName());
            businessId = onlineBookingService.getBusinessIdByOBNameOrDomain(anonymousParams);
        }

        DiscountCode discountCode = discountCodeService.checkDiscountCodeValidForCustomer(
                request.getCodeName(),
                businessId.longValue(),
                request.getCustomerId(),
                request.getServiceIdsList(),
                request.getAppointmentDate());
        DiscountCodeModelOnlineBookingView view =
                DiscountConverter.INSTANCE.toDiscountCodeModelOnlineBookingView(discountCode);
        responseObserver.onNext(CheckDiscountCodeValidForCustomerOutput.newBuilder()
                .setDiscountCodeModelOnlineBookingView(view)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getAvailableDiscountList(
            GetAvailableDiscountListInput request, StreamObserver<GetAvailableDiscountListOutput> responseObserver) {
        PageInfo pageInfo = PageConverter.INSTANCE.toPageInfo(request.getPagination());
        Pair<List<DiscountCodeComposite>, PageInfo> result =
                discountCodeService.getAvailableDiscountCompositeListByPage(
                        pageInfo,
                        DiscountConverter.INSTANCE.toOrderInfoDetail(request),
                        request.getCodeName(),
                        request.getUsedDiscountCodeIdsList());
        result.first().forEach(discountCode -> {
            if (Objects.equals(discountCode.getEndDate(), DiscountCodeService.ON_GOING_DATE)) {
                discountCode.setEndDate(null);
            }
        });
        responseObserver.onNext(DiscountConverter.INSTANCE.toAvailableOutput(result.first(), result.second()));
        responseObserver.onCompleted();
    }

    @Override
    public void getAvailableDiscountListForExistingInvoice(
            GetAvailableDiscountListForExistingInvoiceInput request,
            StreamObserver<GetAvailableDiscountListForExistingInvoiceOutput> responseObserver) {
        List<DiscountCode> result = discountCodeService.getAvailableDiscountListForExistingInvoice(
                DiscountConverter.INSTANCE.toOrderInfoDetail(request),
                request.getWillUseDiscountCodeIdsList(),
                request.getUsedDiscountCodeIdsList(),
                request.getAppointmentDate());

        responseObserver.onNext(DiscountConverter.INSTANCE.toAvailableOutput(result, 0));
        responseObserver.onCompleted();
    }

    @Override
    public void autoApplyDiscountCode(
            AutoApplyDiscountCodeInput request, StreamObserver<AutoApplyDiscountCodeOutput> responseObserver) {
        DiscountCode discountCode = discountCodeService.autoApplyDiscountCode(
                DiscountConverter.INSTANCE.toOrderInfoDetail(request), request.getAppointmentDate());
        if (Objects.isNull(discountCode)) {
            responseObserver.onNext(
                    AutoApplyDiscountCodeOutput.newBuilder().setSuccess(false).build());
            responseObserver.onCompleted();
            return;
        }

        DiscountCodeModel discountCodeModel = DiscountConverter.INSTANCE.toModel(discountCode);
        responseObserver.onNext(AutoApplyDiscountCodeOutput.newBuilder()
                .setSuccess(true)
                .setDiscountCodeModel(discountCodeModel)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void deleteDiscountCodeLog(DeleteDiscountCodeLogInput request, StreamObserver<Empty> responseObserver) {
        discountCodeService.deleteDiscountCodeLog(request.getRedeemId(), request.getIdsList());
        responseObserver.onNext(Empty.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    public void useDiscountCode(UseDiscountCodeInput request, StreamObserver<Empty> responseObserver) {
        discountCodeService.useDiscountCode(DiscountConverter.INSTANCE.toDiscountCodeUsageDetail(request));

        responseObserver.onNext(Empty.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    public void refreshDiscountCodeStatus(Empty request, StreamObserver<Empty> responseObserver) {
        discountCodeService.refreshDiscountCodeStatus();

        responseObserver.onNext(Empty.newBuilder().build());
        responseObserver.onCompleted();
    }

    @Override
    public void getBusinessDiscountCodeConfig(
            GetBusinessDiscountCodeConfigInput request,
            StreamObserver<GetBusinessDiscountCodeConfigOutput> responseObserver) {
        Long businessId;
        if (request.hasBusinessId()) {
            businessId = request.getBusinessId();
        } else {
            OBAnonymousParams anonymousParams =
                    new OBAnonymousParams().setDomain(request.getDomain()).setName(request.getName());
            businessId = Long.valueOf(onlineBookingService.getBusinessIdByOBNameOrDomain(anonymousParams));
        }

        boolean result = discountCodeService.getBusinessDiscountCodeConfig(businessId);
        responseObserver.onNext(GetBusinessDiscountCodeConfigOutput.newBuilder()
                .setHasValidDiscountCode(result)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void getDiscountCodeConfig(
            GetDiscountCodeConfigInput request, StreamObserver<GetDiscountCodeConfigOutput> responseObserver) {
        Long businessId =
                switch (request.getAnonymousCase()) {
                    case BUSINESS_ID -> request.getBusinessId();
                    case DOMAIN, NAME -> {
                        OBAnonymousParams anonymousParams = new OBAnonymousParams()
                                .setDomain(request.getDomain())
                                .setName(request.getName());
                        yield Long.valueOf(onlineBookingService.getBusinessIdByOBNameOrDomain(anonymousParams));
                    }
                    default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "anonymous params error");
                };

        boolean result = discountCodeService.getBusinessDiscountCodeConfig(businessId);
        responseObserver.onNext(GetDiscountCodeConfigOutput.newBuilder()
                .setHasValidDiscountCode(result)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    public void migrateDiscountCode(
            MigrateDiscountCodeInput request, StreamObserver<MigrateDiscountCodeInputOutput> responseObserver) {
        migrateService.migrateDiscountCode(
                request.getCompanyId(),
                request.getStaffIdMapMap(),
                request.getServiceIdMapMap(),
                request.getClientIdMapMap(),
                request.getPetIdMapMap());
        responseObserver.onNext(MigrateDiscountCodeInputOutput.newBuilder().build());
        responseObserver.onCompleted();
    }
}
