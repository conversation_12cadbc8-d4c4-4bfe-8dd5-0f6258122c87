package com.moego.svc.marketing.controller;

import com.moego.idl.service.marketing.v1.MarketingMergeServiceGrpc;
import com.moego.idl.service.marketing.v1.MergeCustomerMarketingRecordRequest;
import com.moego.idl.service.marketing.v1.MergeCustomerMarketingRecordResponse;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.server.grooming.dto.CustomerPetMergeRelationDTO;
import com.moego.server.grooming.dto.PetMergeRelationDTO;
import com.moego.svc.marketing.service.MarketingMergeService;
import io.grpc.stub.StreamObserver;
import lombok.RequiredArgsConstructor;

@GrpcService
@RequiredArgsConstructor
public class MarketingMergeController extends MarketingMergeServiceGrpc.MarketingMergeServiceImplBase {

    private final MarketingMergeService marketingMergeService;

    @Override
    public void mergeCustomerMarketingRecord(
            MergeCustomerMarketingRecordRequest request,
            StreamObserver<MergeCustomerMarketingRecordResponse> responseObserver) {
        var mergeRelation = request.getMergeRelation();
        // 已确定下面的规则：
        // 一定会有 customer merge, pet merge 不会单独存在
        // pet merge 都是发生在 customer 之间
        CustomerPetMergeRelationDTO mergedCustomerPetDTO = new CustomerPetMergeRelationDTO();
        mergedCustomerPetDTO.setCompanyId(request.getCompanyId());
        mergedCustomerPetDTO.setTargetCustomerId(
                Math.toIntExact(mergeRelation.getCustomerMergeRelation().getTargetId()));
        mergedCustomerPetDTO.setSourceCustomerIds(mergeRelation.getCustomerMergeRelation().getSourceIdsList().stream()
                .map(Long::intValue)
                .toList());
        if (mergeRelation.getPetMergeRelationsCount() > 0) {
            mergedCustomerPetDTO.setPetMergeRelations(mergeRelation.getPetMergeRelationsList().stream()
                    .map(petMergeRelation -> {
                        PetMergeRelationDTO mergedPet = new PetMergeRelationDTO();
                        mergedPet.setTargetPetId(Math.toIntExact(petMergeRelation.getTargetId()));
                        mergedPet.setSourcePetIds(petMergeRelation.getSourceIdsList().stream()
                                .map(Long::intValue)
                                .toList());
                        return mergedPet;
                    })
                    .toList());
        }

        marketingMergeService.mergeDiscountCodeData(mergedCustomerPetDTO);
        responseObserver.onNext(
                MergeCustomerMarketingRecordResponse.newBuilder().build());
        responseObserver.onCompleted();
    }
}
