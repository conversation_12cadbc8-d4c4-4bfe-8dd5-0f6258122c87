spring:
  config:
    import:
      - "aws-secretsmanager:moego/testing/redis?prefix=secret.redis."
      - "aws-secretsmanager:moego/testing/zendesk?prefix=secret.zendesk."
      - "aws-secretsmanager:moego/testing/growthbook?prefix=secret.growthbook."

moego:
  session:
    sources:
      - name: customer
        cookie-name: MGSID-C-T2
        legacy-cookie-names:
          - MGSID-T2
        domains:
          - pattern: '^([^.]+-grey-)?my\.t2\.moego\.(pet|dev)$'
            cookie-target-domain-level: 3
        max-age: 2592000
      - name: ob
        cookie-name: MGSID-OB-T2
        legacy-cookie-names:
          - Customer-Token
        domains:
          - pattern: '^([^.]+-grey-)?(form|booking)\.t2\.moego\.(pet|dev)$'
            cookie-target-domain-level: 3
            # xxx-grey-xxx.t2.moego.online
          - pattern: '^[^.]+\.t2\.moego\.online$'
            cookie-target-domain-level: 4
        max-age: 2592000
        sub-max-age: 2592000
      - name: mis
        cookie-name: MGSID-MIS-T2
        domains:
          - pattern: '^([^.]+-grey-)?mis\.t2\.moego\.(pet|dev)$'
            cookie-target-domain-level: 3
        max-age: 2592000
      - name: business
        cookie-name: MGSID-B-T2
        domains:
          - pattern: '^.*$'
            cookie-target-domain-level: 3
        max-age: 2592000
logging:
  level:
    com.moego.api.v3.risk_control: DEBUG
