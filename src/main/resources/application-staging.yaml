spring:
  config:
    import:
      - "aws-secretsmanager:moego/staging/redis?prefix=secret.redis."
      - "aws-secretsmanager:moego/staging/zendesk?prefix=secret.zendesk."
      - "aws-secretsmanager:moego/staging/growthbook?prefix=secret.growthbook."

moego:
  session:
    sources:
      - name: customer
        cookie-name: MGSID-C-S1
        legacy-cookie-names:
          - MGSID-S1
        domains:
          - pattern: '^my\.s1\.moego\.(pet|dev)$'
            cookie-target-domain-level: 3
        max-age: 2592000
      - name: ob
        cookie-name: MGSID-OB-S1
        legacy-cookie-names:
          - Customer-Token
        domains:
          - pattern: '^(form|booking)\.s1\.moego\.(pet|dev)$'
            cookie-target-domain-level: 3
            # *.s1.moego.online
          - pattern: '^[^.]+\.s1\.moego\.online$'
            cookie-target-domain-level: 4
        max-age: 2592000
        sub-max-age: 2592000
      - name: mis
        cookie-name: MGSID-MIS-S1
        domains:
          - pattern: '^mis\.s1\.moego\.(pet|dev)$'
            cookie-target-domain-level: 3
        max-age: 2592000
      - name: business
        cookie-name: MGSID-B-S1
        domains:
          - pattern: '^.*$'
            cookie-target-domain-level: 3
        max-age: 2592000
