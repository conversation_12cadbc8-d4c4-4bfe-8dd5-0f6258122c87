spring:
  datasource:
    driver-class-name: org.postgresql.Driver
    url: *********************************************************
    username: moego_developer_240310_eff7a0dc
    password: G0MxI7NM_jX_f7Ky73vnrwej97xg1tly
  data:
    redis:
      host: redis.t2.moego.dev
      password: iMoReGoTdesstingeCache250310_7fec987d
      port: 40179
      ssl:
        enabled: true
      timeout: 60000
      key:
        delimiter: ':'
        prefix: local

moego:
  feature-flag:
    growth-book:
      api-host: https://growthbook.moego.pet/growthbook-api
      client-key: sdk-qygeRRneunZQJxf
  event-bus:
    brokers:
      - name: default
        addresses:
          - kafka.kafka.svc.cluster.local:9092

logging:
  level:
    com.moego.svc.order.repository: debug
