spring:
  data:
    redis:
      host: redis.t2.moego.dev
      password: iMoReGoTdesstingeCache250310_7fec987d
      port: 40179
      ssl:
        enabled: true
zendesk:
  jwtKid: kid
  jwtSecret: key

moego:
  session:
    sources:
      - name: customer
        cookie-name: MGSID-C-T2
        legacy-cookie-names:
          - MGSID-T2
        domains:
          - pattern: '^([^.]+-grey-)?my\.t2\.moego\.dev$'
            cookie-target-domain-level: 3
        max-age: 2592000
      - name: ob
        cookie-name: MGSID-OB-T2
        legacy-cookie-names:
          - Customer-Token
        domains:
          - pattern: '^([^.]+-grey-)?(form|booking)\.t2\.moego\.dev$'
            cookie-target-domain-level: 3
            # xxx-grey-xxx.t2.moego.online
          - pattern: '^[^.]+\.t2\.moego\.online$'
            cookie-target-domain-level: 4
        max-age: 2592000
        sub-max-age: 2592000
      - name: mis
        cookie-name: MGSID-MIS-T2
        domains:
          - pattern: '^([^.]+-grey-)?mis\.t2\.moego\.dev$'
            cookie-target-domain-level: 3
        max-age: 2592000
      - name: business
        cookie-name: MGSID-B-T2
        domains:
          - pattern: '^.*$'
            cookie-target-domain-level: 3
        max-age: 2592000
  grpc:
    server:
      observability:
        metrics:
          port: 0
  feature-flag:
    growth-book:
      api-host: https://growthbook.moego.pet/growthbook-api
      client-key: sdk-qygeRRneunZQJxf
