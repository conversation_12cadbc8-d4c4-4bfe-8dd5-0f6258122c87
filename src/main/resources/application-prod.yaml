spring:
  config:
    import:
      - "aws-secretsmanager:moego/production/redis?prefix=secret.redis."
      - "aws-secretsmanager:moego/production/zendesk?prefix=secret.zendesk."
      - "aws-secretsmanager:moego/production/growthbook?prefix=secret.growthbook."
  data:
    redis:
      key:
        prefix: apiv2

moego:
  session:
    sources:
      - name: customer
        cookie-name: MGSID-C
        legacy-cookie-names:
          - MGSID
        domains:
          - pattern: '^my\.moego\.pet$'
            cookie-target-domain-level: 2
        max-age: 2592000
      - name: ob
        cookie-name: MGSID-OB
        legacy-cookie-names:
          - Customer-Token
        domains:
          - pattern: '^(form|booking)\.moego\.pet$'
            cookie-target-domain-level: 2
            # xxx.moego.online
          - pattern: '^[^.]+\.moego\.online$'
            cookie-target-domain-level: 3
        max-age: 2592000
        sub-max-age: 2592000
      - name: mis
        cookie-name: MGSID-MIS
        domains:
          - pattern: '^mis\.moego\.pet$'
            cookie-target-domain-level: 2
        max-age: 2592000
      - name: business
        cookie-name: MGSID-B
        domains:
          - pattern: '^.*$'
            cookie-target-domain-level: 2
        max-age: 2592000
