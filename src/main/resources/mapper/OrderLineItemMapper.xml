<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.order.repository.mapper.OrderLineItemMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.order.repository.entity.OrderLineItem">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="object_id" jdbcType="BIGINT" property="objectId" />
    <result column="type" jdbcType="VARCHAR" property="type" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="unit_price" jdbcType="NUMERIC" property="unitPrice" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="purchased_quantity" jdbcType="INTEGER" property="purchasedQuantity" />
    <result column="tips_amount" jdbcType="NUMERIC" property="tipsAmount" />
    <result column="tax_amount" jdbcType="NUMERIC" property="taxAmount" />
    <result column="discount_amount" jdbcType="NUMERIC" property="discountAmount" />
    <result column="extra_fee_amount" jdbcType="NUMERIC" property="extraFeeAmount" />
    <result column="sub_total_amount" jdbcType="NUMERIC" property="subTotalAmount" />
    <result column="total_amount" jdbcType="NUMERIC" property="totalAmount" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="pet_id" jdbcType="BIGINT" property="petId" />
    <result column="tax_id" jdbcType="BIGINT" property="taxId" />
    <result column="tax_name" jdbcType="VARCHAR" property="taxName" />
    <result column="tax_rate" jdbcType="NUMERIC" property="taxRate" />
    <result column="currency_code" jdbcType="VARCHAR" property="currencyCode" />
    <result column="refunded_quantity" jdbcType="INTEGER" property="refundedQuantity" />
    <result column="refunded_amount" jdbcType="NUMERIC" property="refundedAmount" />
    <result column="refunded_tax_amount" jdbcType="NUMERIC" property="refundedTaxAmount" />
    <result column="refunded_discount_amount" jdbcType="NUMERIC" property="refundedDiscountAmount" />
    <result column="refunded_convenience_fee" jdbcType="NUMERIC" property="refundedConvenienceFee" />
    <result column="external_uuid" jdbcType="VARCHAR" property="externalUuid" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, staff_id, order_id, object_id, type, is_deleted, name, description, 
    unit_price, quantity, purchased_quantity, tips_amount, tax_amount, discount_amount, 
    extra_fee_amount, sub_total_amount, total_amount, create_time, update_time, pet_id, 
    tax_id, tax_name, tax_rate, currency_code, refunded_quantity, refunded_amount, refunded_tax_amount, 
    refunded_discount_amount, refunded_convenience_fee, external_uuid
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_line_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from order_line_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderLineItem" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_line_item (business_id, staff_id, order_id, 
      object_id, type, is_deleted, 
      name, description, unit_price, 
      quantity, purchased_quantity, tips_amount, 
      tax_amount, discount_amount, extra_fee_amount, 
      sub_total_amount, total_amount, create_time, 
      update_time, pet_id, tax_id, 
      tax_name, tax_rate, currency_code, 
      refunded_quantity, refunded_amount, refunded_tax_amount, 
      refunded_discount_amount, refunded_convenience_fee, 
      external_uuid
      )
    values (#{businessId,jdbcType=BIGINT}, #{staffId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, 
      #{objectId,jdbcType=BIGINT}, #{type,jdbcType=VARCHAR}, #{isDeleted,jdbcType=BIT}, 
      #{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{unitPrice,jdbcType=NUMERIC}, 
      #{quantity,jdbcType=INTEGER}, #{purchasedQuantity,jdbcType=INTEGER}, #{tipsAmount,jdbcType=NUMERIC}, 
      #{taxAmount,jdbcType=NUMERIC}, #{discountAmount,jdbcType=NUMERIC}, #{extraFeeAmount,jdbcType=NUMERIC}, 
      #{subTotalAmount,jdbcType=NUMERIC}, #{totalAmount,jdbcType=NUMERIC}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{petId,jdbcType=BIGINT}, #{taxId,jdbcType=BIGINT}, 
      #{taxName,jdbcType=VARCHAR}, #{taxRate,jdbcType=NUMERIC}, #{currencyCode,jdbcType=VARCHAR}, 
      #{refundedQuantity,jdbcType=INTEGER}, #{refundedAmount,jdbcType=NUMERIC}, #{refundedTaxAmount,jdbcType=NUMERIC}, 
      #{refundedDiscountAmount,jdbcType=NUMERIC}, #{refundedConvenienceFee,jdbcType=NUMERIC}, 
      #{externalUuid,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderLineItem" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_line_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="objectId != null">
        object_id,
      </if>
      <if test="type != null">
        type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="unitPrice != null">
        unit_price,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="purchasedQuantity != null">
        purchased_quantity,
      </if>
      <if test="tipsAmount != null">
        tips_amount,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="discountAmount != null">
        discount_amount,
      </if>
      <if test="extraFeeAmount != null">
        extra_fee_amount,
      </if>
      <if test="subTotalAmount != null">
        sub_total_amount,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="petId != null">
        pet_id,
      </if>
      <if test="taxId != null">
        tax_id,
      </if>
      <if test="taxName != null">
        tax_name,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="currencyCode != null">
        currency_code,
      </if>
      <if test="refundedQuantity != null">
        refunded_quantity,
      </if>
      <if test="refundedAmount != null">
        refunded_amount,
      </if>
      <if test="refundedTaxAmount != null">
        refunded_tax_amount,
      </if>
      <if test="refundedDiscountAmount != null">
        refunded_discount_amount,
      </if>
      <if test="refundedConvenienceFee != null">
        refunded_convenience_fee,
      </if>
      <if test="externalUuid != null">
        external_uuid,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="objectId != null">
        #{objectId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        #{type,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="unitPrice != null">
        #{unitPrice,jdbcType=NUMERIC},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="purchasedQuantity != null">
        #{purchasedQuantity,jdbcType=INTEGER},
      </if>
      <if test="tipsAmount != null">
        #{tipsAmount,jdbcType=NUMERIC},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=NUMERIC},
      </if>
      <if test="discountAmount != null">
        #{discountAmount,jdbcType=NUMERIC},
      </if>
      <if test="extraFeeAmount != null">
        #{extraFeeAmount,jdbcType=NUMERIC},
      </if>
      <if test="subTotalAmount != null">
        #{subTotalAmount,jdbcType=NUMERIC},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="petId != null">
        #{petId,jdbcType=BIGINT},
      </if>
      <if test="taxId != null">
        #{taxId,jdbcType=BIGINT},
      </if>
      <if test="taxName != null">
        #{taxName,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=NUMERIC},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="refundedQuantity != null">
        #{refundedQuantity,jdbcType=INTEGER},
      </if>
      <if test="refundedAmount != null">
        #{refundedAmount,jdbcType=NUMERIC},
      </if>
      <if test="refundedTaxAmount != null">
        #{refundedTaxAmount,jdbcType=NUMERIC},
      </if>
      <if test="refundedDiscountAmount != null">
        #{refundedDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="refundedConvenienceFee != null">
        #{refundedConvenienceFee,jdbcType=NUMERIC},
      </if>
      <if test="externalUuid != null">
        #{externalUuid,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.order.repository.entity.OrderLineItem">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_line_item
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="objectId != null">
        object_id = #{objectId,jdbcType=BIGINT},
      </if>
      <if test="type != null">
        type = #{type,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="unitPrice != null">
        unit_price = #{unitPrice,jdbcType=NUMERIC},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="purchasedQuantity != null">
        purchased_quantity = #{purchasedQuantity,jdbcType=INTEGER},
      </if>
      <if test="tipsAmount != null">
        tips_amount = #{tipsAmount,jdbcType=NUMERIC},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=NUMERIC},
      </if>
      <if test="discountAmount != null">
        discount_amount = #{discountAmount,jdbcType=NUMERIC},
      </if>
      <if test="extraFeeAmount != null">
        extra_fee_amount = #{extraFeeAmount,jdbcType=NUMERIC},
      </if>
      <if test="subTotalAmount != null">
        sub_total_amount = #{subTotalAmount,jdbcType=NUMERIC},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=NUMERIC},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="petId != null">
        pet_id = #{petId,jdbcType=BIGINT},
      </if>
      <if test="taxId != null">
        tax_id = #{taxId,jdbcType=BIGINT},
      </if>
      <if test="taxName != null">
        tax_name = #{taxName,jdbcType=VARCHAR},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=NUMERIC},
      </if>
      <if test="currencyCode != null">
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="refundedQuantity != null">
        refunded_quantity = #{refundedQuantity,jdbcType=INTEGER},
      </if>
      <if test="refundedAmount != null">
        refunded_amount = #{refundedAmount,jdbcType=NUMERIC},
      </if>
      <if test="refundedTaxAmount != null">
        refunded_tax_amount = #{refundedTaxAmount,jdbcType=NUMERIC},
      </if>
      <if test="refundedDiscountAmount != null">
        refunded_discount_amount = #{refundedDiscountAmount,jdbcType=NUMERIC},
      </if>
      <if test="refundedConvenienceFee != null">
        refunded_convenience_fee = #{refundedConvenienceFee,jdbcType=NUMERIC},
      </if>
      <if test="externalUuid != null">
        external_uuid = #{externalUuid,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.order.repository.entity.OrderLineItem">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_line_item
    set business_id = #{businessId,jdbcType=BIGINT},
      staff_id = #{staffId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      object_id = #{objectId,jdbcType=BIGINT},
      type = #{type,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIT},
      name = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      unit_price = #{unitPrice,jdbcType=NUMERIC},
      quantity = #{quantity,jdbcType=INTEGER},
      purchased_quantity = #{purchasedQuantity,jdbcType=INTEGER},
      tips_amount = #{tipsAmount,jdbcType=NUMERIC},
      tax_amount = #{taxAmount,jdbcType=NUMERIC},
      discount_amount = #{discountAmount,jdbcType=NUMERIC},
      extra_fee_amount = #{extraFeeAmount,jdbcType=NUMERIC},
      sub_total_amount = #{subTotalAmount,jdbcType=NUMERIC},
      total_amount = #{totalAmount,jdbcType=NUMERIC},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      pet_id = #{petId,jdbcType=BIGINT},
      tax_id = #{taxId,jdbcType=BIGINT},
      tax_name = #{taxName,jdbcType=VARCHAR},
      tax_rate = #{taxRate,jdbcType=NUMERIC},
      currency_code = #{currencyCode,jdbcType=VARCHAR},
      refunded_quantity = #{refundedQuantity,jdbcType=INTEGER},
      refunded_amount = #{refundedAmount,jdbcType=NUMERIC},
      refunded_tax_amount = #{refundedTaxAmount,jdbcType=NUMERIC},
      refunded_discount_amount = #{refundedDiscountAmount,jdbcType=NUMERIC},
      refunded_convenience_fee = #{refundedConvenienceFee,jdbcType=NUMERIC},
      external_uuid = #{externalUuid,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <sql id="Base_Column_List_With_Alias">
    ${alias}.id, ${alias}.business_id, ${alias}.staff_id, ${alias}.order_id, ${alias}.object_id, ${alias}.type,
    ${alias}.is_deleted, ${alias}.name, ${alias}.description, ${alias}.unit_price, ${alias}.quantity,
    ${alias}.purchased_quantity, ${alias}.tips_amount, ${alias}.tax_amount, ${alias}.discount_amount,
    ${alias}.extra_fee_amount, ${alias}.sub_total_amount, ${alias}.total_amount, ${alias}.create_time,
    ${alias}.update_time
  </sql>

  <select id="selectByOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "order_line_item"
    where order_id = #{orderId,jdbcType=BIGINT}
    and is_deleted = false
  </select>

  <select id="selectByOrderItemIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "order_line_item"
    where id in
    <foreach close=")" collection="orderItemIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    and is_deleted = false
  </select>

  <select id="selectByOrderIdsAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "order_line_item"
    where order_id in
    <foreach close=")" collection="orderIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    <if test="type != null and type != ''">
      and type = #{type}
    </if>
    and is_deleted = false
  </select>

  <select id="selectByObjectIdAndType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "order_line_item"
    where order_id = #{orderId}
    and object_id in
    <foreach close=")" collection="objectIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    <if test="type != null and type != ''">
      and type = #{type}
    </if>
    and is_deleted = false
  </select>
</mapper>