<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.marketing.mapper.DiscountCodeMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.marketing.entity.DiscountCode">
    <!--@mbg.generated-->
    <!--@Table discount_code-->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="discount_code" jdbcType="VARCHAR" property="discountCode" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="amount" jdbcType="NUMERIC" property="amount" />
    <result column="type" jdbcType="SMALLINT" property="type" />
    <result column="start_date" jdbcType="VARCHAR" property="startDate" />
    <result column="end_date" jdbcType="VARCHAR" property="endDate" />
    <result column="auto_apply_association" jdbcType="BOOLEAN" property="autoApplyAssociation" />
    <result column="allowed_all_thing" jdbcType="BOOLEAN" property="allowedAllThing" />
    <result column="allowed_all_services" jdbcType="BOOLEAN" property="allowedAllServices" />
    <result column="service_ids" jdbcType="VARCHAR" property="serviceIds" />
    <result column="add_on_ids" jdbcType="VARCHAR" property="addOnIds" />
    <result column="allowed_all_products" jdbcType="BOOLEAN" property="allowedAllProducts" />
    <result column="product_ids" jdbcType="VARCHAR" property="productIds" />
    <result column="allowed_all_clients" jdbcType="BOOLEAN" property="allowedAllClients" />
    <result column="allowed_new_clients" jdbcType="BOOLEAN" property="allowedNewClients" />
    <result column="clients_group" jdbcType="VARCHAR" property="clientsGroup" />
    <result column="client_ids" jdbcType="VARCHAR" property="clientIds" />
    <result column="limit_usage" jdbcType="INTEGER" property="limitUsage" />
    <result column="limit_number_per_client" jdbcType="INTEGER" property="limitNumberPerClient" />
    <result column="limit_budget" jdbcType="INTEGER" property="limitBudget" />
    <result column="discount_sales" jdbcType="NUMERIC" property="discountSales" />
    <result column="total_usage" jdbcType="INTEGER" property="totalUsage" />
    <result column="status" jdbcType="SMALLINT" property="status" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="enable_online_booking" jdbcType="BOOLEAN" property="enableOnlineBooking" />
    <result column="location_ids" jdbcType="VARCHAR" property="locationIds" />
    <result column="expiry_type" jdbcType="INTEGER" property="expiryType" />
    <result column="expiry_time" jdbcType="BIGINT" property="expiryTime" />
    <result column="source" jdbcType="INTEGER" property="source" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, business_id, discount_code, description, amount, "type", start_date, end_date,
    auto_apply_association, allowed_all_thing, allowed_all_services, service_ids, add_on_ids,
    allowed_all_products, product_ids, allowed_all_clients, allowed_new_clients, clients_group,
    client_ids, limit_usage, limit_number_per_client, limit_budget, discount_sales, total_usage,
    "status", create_by, update_by, create_time, update_time, company_id, enable_online_booking,
    location_ids, expiry_type, expiry_time, "source"
  </sql>
  <select id="selectByExample" parameterType="com.moego.svc.marketing.entity.DiscountCodeExample" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from discount_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List" />
    from discount_code
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete from discount_code
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.marketing.entity.DiscountCodeExample">
    <!--@mbg.generated-->
    delete from discount_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.marketing.entity.DiscountCode" useGeneratedKeys="true">
    insert into discount_code (business_id, discount_code, description,
                               amount, "type", start_date,
                               end_date, auto_apply_association, allowed_all_thing,
                               allowed_all_services, service_ids, add_on_ids,
                               allowed_all_products, product_ids, allowed_all_clients,
                               allowed_new_clients, clients_group, client_ids,
                               limit_usage, limit_number_per_client, limit_budget,
                               discount_sales, total_usage, "status",
                               create_by, update_by, create_time,
                               update_time, company_id, enable_online_booking,
                               location_ids, expiry_type, expiry_time,
                               "source")
    values (#{businessId,jdbcType=BIGINT}, #{discountCode,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR},
            #{amount,jdbcType=NUMERIC}, #{type,jdbcType=SMALLINT}, #{startDate,jdbcType=VARCHAR},
            #{endDate,jdbcType=VARCHAR}, #{autoApplyAssociation,jdbcType=BOOLEAN}, #{allowedAllThing,jdbcType=BOOLEAN},
            #{allowedAllServices,jdbcType=BOOLEAN}, #{serviceIds,jdbcType=VARCHAR}, #{addOnIds,jdbcType=VARCHAR},
            #{allowedAllProducts,jdbcType=BOOLEAN}, #{productIds,jdbcType=VARCHAR}, #{allowedAllClients,jdbcType=BOOLEAN},
            #{allowedNewClients,jdbcType=BOOLEAN}, #{clientsGroup,jdbcType=VARCHAR}, #{clientIds,jdbcType=VARCHAR},
            #{limitUsage,jdbcType=INTEGER}, #{limitNumberPerClient,jdbcType=INTEGER}, #{limitBudget,jdbcType=INTEGER},
            #{discountSales,jdbcType=NUMERIC}, #{totalUsage,jdbcType=INTEGER}, #{status,jdbcType=SMALLINT},
            #{createBy,jdbcType=BIGINT}, #{updateBy,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP},
            #{updateTime,jdbcType=TIMESTAMP}, #{companyId,jdbcType=BIGINT}, #{enableOnlineBooking,jdbcType=BOOLEAN},
            #{locationIds,jdbcType=VARCHAR}, #{expiryType,jdbcType=INTEGER}, #{expiryTime,jdbcType=BIGINT},
            #{source,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.marketing.entity.DiscountCode" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into discount_code
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="discountCode != null and discountCode != ''">
        discount_code,
      </if>
      <if test="description != null and description != ''">
        description,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="type != null">
        "type",
      </if>
      <if test="startDate != null and startDate != ''">
        start_date,
      </if>
      <if test="endDate != null and endDate != ''">
        end_date,
      </if>
      <if test="autoApplyAssociation != null">
        auto_apply_association,
      </if>
      <if test="allowedAllThing != null">
        allowed_all_thing,
      </if>
      <if test="allowedAllServices != null">
        allowed_all_services,
      </if>
      <if test="serviceIds != null and serviceIds != ''">
        service_ids,
      </if>
      <if test="addOnIds != null and addOnIds != ''">
        add_on_ids,
      </if>
      <if test="allowedAllProducts != null">
        allowed_all_products,
      </if>
      <if test="productIds != null and productIds != ''">
        product_ids,
      </if>
      <if test="allowedAllClients != null">
        allowed_all_clients,
      </if>
      <if test="allowedNewClients != null">
        allowed_new_clients,
      </if>
      <if test="clientsGroup != null and clientsGroup != ''">
        clients_group,
      </if>
      <if test="clientIds != null and clientIds != ''">
        client_ids,
      </if>
      <if test="limitUsage != null">
        limit_usage,
      </if>
      <if test="limitNumberPerClient != null">
        limit_number_per_client,
      </if>
      <if test="limitBudget != null">
        limit_budget,
      </if>
      <if test="discountSales != null">
        discount_sales,
      </if>
      <if test="totalUsage != null">
        total_usage,
      </if>
      <if test="status != null">
        "status",
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="enableOnlineBooking != null">
        enable_online_booking,
      </if>
      <if test="locationIds != null and locationIds != ''">
        location_ids,
      </if>
      <if test="expiryType != null">
        expiry_type,
      </if>
      <if test="expiryTime != null">
        expiry_time,
      </if>
      <if test="source != null">
        "source",
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="discountCode != null and discountCode != ''">
        #{discountCode,jdbcType=VARCHAR},
      </if>
      <if test="description != null and description != ''">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=NUMERIC},
      </if>
      <if test="type != null">
        #{type,jdbcType=SMALLINT},
      </if>
      <if test="startDate != null and startDate != ''">
        #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null and endDate != ''">
        #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="autoApplyAssociation != null">
        #{autoApplyAssociation,jdbcType=BOOLEAN},
      </if>
      <if test="allowedAllThing != null">
        #{allowedAllThing,jdbcType=BOOLEAN},
      </if>
      <if test="allowedAllServices != null">
        #{allowedAllServices,jdbcType=BOOLEAN},
      </if>
      <if test="serviceIds != null and serviceIds != ''">
        #{serviceIds,jdbcType=VARCHAR},
      </if>
      <if test="addOnIds != null and addOnIds != ''">
        #{addOnIds,jdbcType=VARCHAR},
      </if>
      <if test="allowedAllProducts != null">
        #{allowedAllProducts,jdbcType=BOOLEAN},
      </if>
      <if test="productIds != null and productIds != ''">
        #{productIds,jdbcType=VARCHAR},
      </if>
      <if test="allowedAllClients != null">
        #{allowedAllClients,jdbcType=BOOLEAN},
      </if>
      <if test="allowedNewClients != null">
        #{allowedNewClients,jdbcType=BOOLEAN},
      </if>
      <if test="clientsGroup != null and clientsGroup != ''">
        #{clientsGroup,jdbcType=VARCHAR},
      </if>
      <if test="clientIds != null and clientIds != ''">
        #{clientIds,jdbcType=VARCHAR},
      </if>
      <if test="limitUsage != null">
        #{limitUsage,jdbcType=INTEGER},
      </if>
      <if test="limitNumberPerClient != null">
        #{limitNumberPerClient,jdbcType=INTEGER},
      </if>
      <if test="limitBudget != null">
        #{limitBudget,jdbcType=INTEGER},
      </if>
      <if test="discountSales != null">
        #{discountSales,jdbcType=NUMERIC},
      </if>
      <if test="totalUsage != null">
        #{totalUsage,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=SMALLINT},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="enableOnlineBooking != null">
        #{enableOnlineBooking,jdbcType=BOOLEAN},
      </if>
      <if test="locationIds != null and locationIds != ''">
        #{locationIds,jdbcType=VARCHAR},
      </if>
      <if test="expiryType != null">
        #{expiryType,jdbcType=INTEGER},
      </if>
      <if test="expiryTime != null">
        #{expiryTime,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        #{source,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.marketing.entity.DiscountCodeExample" resultType="java.lang.Long">
    <!--@mbg.generated-->
    select count(*) from discount_code
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated-->
    update discount_code
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.discountCode != null">
        discount_code = #{record.discountCode,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.amount != null">
        amount = #{record.amount,jdbcType=NUMERIC},
      </if>
      <if test="record.type != null">
        "type" = #{record.type,jdbcType=SMALLINT},
      </if>
      <if test="record.startDate != null">
        start_date = #{record.startDate,jdbcType=VARCHAR},
      </if>
      <if test="record.endDate != null">
        end_date = #{record.endDate,jdbcType=VARCHAR},
      </if>
      <if test="record.autoApplyAssociation != null">
        auto_apply_association = #{record.autoApplyAssociation,jdbcType=BOOLEAN},
      </if>
      <if test="record.allowedAllThing != null">
        allowed_all_thing = #{record.allowedAllThing,jdbcType=BOOLEAN},
      </if>
      <if test="record.allowedAllServices != null">
        allowed_all_services = #{record.allowedAllServices,jdbcType=BOOLEAN},
      </if>
      <if test="record.serviceIds != null">
        service_ids = #{record.serviceIds,jdbcType=VARCHAR},
      </if>
      <if test="record.addOnIds != null">
        add_on_ids = #{record.addOnIds,jdbcType=VARCHAR},
      </if>
      <if test="record.allowedAllProducts != null">
        allowed_all_products = #{record.allowedAllProducts,jdbcType=BOOLEAN},
      </if>
      <if test="record.productIds != null">
        product_ids = #{record.productIds,jdbcType=VARCHAR},
      </if>
      <if test="record.allowedAllClients != null">
        allowed_all_clients = #{record.allowedAllClients,jdbcType=BOOLEAN},
      </if>
      <if test="record.allowedNewClients != null">
        allowed_new_clients = #{record.allowedNewClients,jdbcType=BOOLEAN},
      </if>
      <if test="record.clientsGroup != null">
        clients_group = #{record.clientsGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.clientIds != null">
        client_ids = #{record.clientIds,jdbcType=VARCHAR},
      </if>
      <if test="record.limitUsage != null">
        limit_usage = #{record.limitUsage,jdbcType=INTEGER},
      </if>
      <if test="record.limitNumberPerClient != null">
        limit_number_per_client = #{record.limitNumberPerClient,jdbcType=INTEGER},
      </if>
      <if test="record.limitBudget != null">
        limit_budget = #{record.limitBudget,jdbcType=INTEGER},
      </if>
      <if test="record.discountSales != null">
        discount_sales = #{record.discountSales,jdbcType=NUMERIC},
      </if>
      <if test="record.totalUsage != null">
        total_usage = #{record.totalUsage,jdbcType=INTEGER},
      </if>
      <if test="record.status != null">
        "status" = #{record.status,jdbcType=SMALLINT},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=BIGINT},
      </if>
      <if test="record.updateBy != null">
        update_by = #{record.updateBy,jdbcType=BIGINT},
      </if>
      <if test="record.createTime != null">
        create_time = #{record.createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateTime != null">
        update_time = #{record.updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.enableOnlineBooking != null">
        enable_online_booking = #{record.enableOnlineBooking,jdbcType=BOOLEAN},
      </if>
      <if test="record.locationIds != null">
        location_ids = #{record.locationIds,jdbcType=VARCHAR},
      </if>
      <if test="record.expiryType != null">
        expiry_type = #{record.expiryType,jdbcType=INTEGER},
      </if>
      <if test="record.expiryTime != null">
        expiry_time = #{record.expiryTime,jdbcType=BIGINT},
      </if>
      <if test="record.source != null">
        "source" = #{record.source,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated-->
    update discount_code
    set id = #{record.id,jdbcType=BIGINT},
    business_id = #{record.businessId,jdbcType=BIGINT},
    discount_code = #{record.discountCode,jdbcType=VARCHAR},
    description = #{record.description,jdbcType=VARCHAR},
    amount = #{record.amount,jdbcType=NUMERIC},
    "type" = #{record.type,jdbcType=SMALLINT},
    start_date = #{record.startDate,jdbcType=VARCHAR},
    end_date = #{record.endDate,jdbcType=VARCHAR},
    auto_apply_association = #{record.autoApplyAssociation,jdbcType=BOOLEAN},
    allowed_all_thing = #{record.allowedAllThing,jdbcType=BOOLEAN},
    allowed_all_services = #{record.allowedAllServices,jdbcType=BOOLEAN},
    service_ids = #{record.serviceIds,jdbcType=VARCHAR},
    add_on_ids = #{record.addOnIds,jdbcType=VARCHAR},
    allowed_all_products = #{record.allowedAllProducts,jdbcType=BOOLEAN},
    product_ids = #{record.productIds,jdbcType=VARCHAR},
    allowed_all_clients = #{record.allowedAllClients,jdbcType=BOOLEAN},
    allowed_new_clients = #{record.allowedNewClients,jdbcType=BOOLEAN},
    clients_group = #{record.clientsGroup,jdbcType=VARCHAR},
    client_ids = #{record.clientIds,jdbcType=VARCHAR},
    limit_usage = #{record.limitUsage,jdbcType=INTEGER},
    limit_number_per_client = #{record.limitNumberPerClient,jdbcType=INTEGER},
    limit_budget = #{record.limitBudget,jdbcType=INTEGER},
    discount_sales = #{record.discountSales,jdbcType=NUMERIC},
    total_usage = #{record.totalUsage,jdbcType=INTEGER},
    "status" = #{record.status,jdbcType=SMALLINT},
    create_by = #{record.createBy,jdbcType=BIGINT},
    update_by = #{record.updateBy,jdbcType=BIGINT},
    create_time = #{record.createTime,jdbcType=TIMESTAMP},
    update_time = #{record.updateTime,jdbcType=TIMESTAMP},
    company_id = #{record.companyId,jdbcType=BIGINT},
    enable_online_booking = #{record.enableOnlineBooking,jdbcType=BOOLEAN},
    location_ids = #{record.locationIds,jdbcType=VARCHAR},
    expiry_type = #{record.expiryType,jdbcType=INTEGER},
    expiry_time = #{record.expiryTime,jdbcType=BIGINT},
    "source" = #{record.source,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.marketing.entity.DiscountCode">
    <!--@mbg.generated-->
    update discount_code
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="discountCode != null and discountCode != ''">
        discount_code = #{discountCode,jdbcType=VARCHAR},
      </if>
      <if test="description != null and description != ''">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=NUMERIC},
      </if>
      <if test="type != null">
        "type" = #{type,jdbcType=SMALLINT},
      </if>
      <if test="startDate != null and startDate != ''">
        start_date = #{startDate,jdbcType=VARCHAR},
      </if>
      <if test="endDate != null and endDate != ''">
        end_date = #{endDate,jdbcType=VARCHAR},
      </if>
      <if test="autoApplyAssociation != null">
        auto_apply_association = #{autoApplyAssociation,jdbcType=BOOLEAN},
      </if>
      <if test="allowedAllThing != null">
        allowed_all_thing = #{allowedAllThing,jdbcType=BOOLEAN},
      </if>
      <if test="allowedAllServices != null">
        allowed_all_services = #{allowedAllServices,jdbcType=BOOLEAN},
      </if>
      <if test="serviceIds != null and serviceIds != ''">
        service_ids = #{serviceIds,jdbcType=VARCHAR},
      </if>
      <if test="addOnIds != null and addOnIds != ''">
        add_on_ids = #{addOnIds,jdbcType=VARCHAR},
      </if>
      <if test="allowedAllProducts != null">
        allowed_all_products = #{allowedAllProducts,jdbcType=BOOLEAN},
      </if>
      <if test="productIds != null and productIds != ''">
        product_ids = #{productIds,jdbcType=VARCHAR},
      </if>
      <if test="allowedAllClients != null">
        allowed_all_clients = #{allowedAllClients,jdbcType=BOOLEAN},
      </if>
      <if test="allowedNewClients != null">
        allowed_new_clients = #{allowedNewClients,jdbcType=BOOLEAN},
      </if>
      <if test="clientsGroup != null and clientsGroup != ''">
        clients_group = #{clientsGroup,jdbcType=VARCHAR},
      </if>
      <if test="clientIds != null and clientIds != ''">
        client_ids = #{clientIds,jdbcType=VARCHAR},
      </if>
      <if test="limitUsage != null">
        limit_usage = #{limitUsage,jdbcType=INTEGER},
      </if>
      <if test="limitNumberPerClient != null">
        limit_number_per_client = #{limitNumberPerClient,jdbcType=INTEGER},
      </if>
      <if test="limitBudget != null">
        limit_budget = #{limitBudget,jdbcType=INTEGER},
      </if>
      <if test="discountSales != null">
        discount_sales = #{discountSales,jdbcType=NUMERIC},
      </if>
      <if test="totalUsage != null">
        total_usage = #{totalUsage,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        "status" = #{status,jdbcType=SMALLINT},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="enableOnlineBooking != null">
        enable_online_booking = #{enableOnlineBooking,jdbcType=BOOLEAN},
      </if>
      <if test="locationIds != null and locationIds != ''">
        location_ids = #{locationIds,jdbcType=VARCHAR},
      </if>
      <if test="expiryType != null">
        expiry_type = #{expiryType,jdbcType=INTEGER},
      </if>
      <if test="expiryTime != null">
        expiry_time = #{expiryTime,jdbcType=BIGINT},
      </if>
      <if test="source != null">
        "source" = #{source,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.marketing.entity.DiscountCode">
    <!--@mbg.generated-->
    update discount_code
    set business_id = #{businessId,jdbcType=BIGINT},
    discount_code = #{discountCode,jdbcType=VARCHAR},
    description = #{description,jdbcType=VARCHAR},
    amount = #{amount,jdbcType=NUMERIC},
    "type" = #{type,jdbcType=SMALLINT},
    start_date = #{startDate,jdbcType=VARCHAR},
    end_date = #{endDate,jdbcType=VARCHAR},
    auto_apply_association = #{autoApplyAssociation,jdbcType=BOOLEAN},
    allowed_all_thing = #{allowedAllThing,jdbcType=BOOLEAN},
    allowed_all_services = #{allowedAllServices,jdbcType=BOOLEAN},
    service_ids = #{serviceIds,jdbcType=VARCHAR},
    add_on_ids = #{addOnIds,jdbcType=VARCHAR},
    allowed_all_products = #{allowedAllProducts,jdbcType=BOOLEAN},
    product_ids = #{productIds,jdbcType=VARCHAR},
    allowed_all_clients = #{allowedAllClients,jdbcType=BOOLEAN},
    allowed_new_clients = #{allowedNewClients,jdbcType=BOOLEAN},
    clients_group = #{clientsGroup,jdbcType=VARCHAR},
    client_ids = #{clientIds,jdbcType=VARCHAR},
    limit_usage = #{limitUsage,jdbcType=INTEGER},
    limit_number_per_client = #{limitNumberPerClient,jdbcType=INTEGER},
    limit_budget = #{limitBudget,jdbcType=INTEGER},
    discount_sales = #{discountSales,jdbcType=NUMERIC},
    total_usage = #{totalUsage,jdbcType=INTEGER},
    "status" = #{status,jdbcType=SMALLINT},
    create_by = #{createBy,jdbcType=BIGINT},
    update_by = #{updateBy,jdbcType=BIGINT},
    create_time = #{createTime,jdbcType=TIMESTAMP},
    update_time = #{updateTime,jdbcType=TIMESTAMP},
    company_id = #{companyId,jdbcType=BIGINT},
    enable_online_booking = #{enableOnlineBooking,jdbcType=BOOLEAN},
    location_ids = #{locationIds,jdbcType=VARCHAR},
    expiry_type = #{expiryType,jdbcType=INTEGER},
    expiry_time = #{expiryTime,jdbcType=BIGINT},
    "source" = #{source,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectActiveAndInactiveBusinessId" resultType="java.lang.Long">
    select distinct business_id
    from discount_code
    where status in (1, 2)
  </select>

  <select id="selectActiveAndInactiveCompanyId" resultType="java.lang.Long">
    select distinct company_id
    from discount_code
    where status in (1, 2)
  </select>

  <update id="updateDiscountCodesBatch">
    <foreach collection="list" item="item" separator=";">
      update discount_code
      set status = 1
      where status = 2
      and ((limit_usage != 0 and total_usage &lt; limit_usage) or limit_usage = 0)
      and start_date &lt;= #{item.startDate,jdbcType=VARCHAR}
      and end_date &gt;= #{item.endDate,jdbcType=VARCHAR}
      and company_id = #{item.companyId,jdbcType=BIGINT};

      update discount_code
      set status = 2
      where status = 1
      and (start_date &gt; #{item.startDate,jdbcType=VARCHAR} or end_date &lt; #{item.endDate,jdbcType=VARCHAR})
      and company_id = #{item.companyId,jdbcType=BIGINT}
    </foreach>
  </update>

  <update id="batchUpdateUsage">
    <foreach collection="list" index="index" item="item" separator=";">
      update discount_code
      set discount_sales = #{item.discountSales,jdbcType=NUMERIC},
      total_usage    = #{item.totalUsage,jdbcType=INTEGER}
      where id = #{item.id}
    </foreach>
  </update>

  <select id="selectInactiveDiscountCodeList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from discount_code
    where status = 1
    <foreach close=")" collection="list" item="item" open="and (" separator="or">
      (end_date &lt; #{item.endDate,jdbcType=VARCHAR} and company_id = #{item.companyId,jdbcType=BIGINT})
    </foreach>
  </select>

  <select id="selectByIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from discount_code
    where id in
    <foreach collection="list" item="id" open="(" close=")" separator=",">
      #{id}
    </foreach>
  </select>

  <select id="selectByCompanyIdWithPage" parameterType="com.moego.svc.marketing.entity.DiscountCodeExample" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from discount_code
    where
    company_id = #{companyId}
    limit #{limit} offset #{offset}
  </select>
</mapper>
