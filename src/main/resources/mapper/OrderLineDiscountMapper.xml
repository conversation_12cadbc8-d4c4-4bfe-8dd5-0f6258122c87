<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.order.repository.mapper.OrderLineDiscountMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.order.repository.entity.OrderLineDiscount">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_item_id" jdbcType="BIGINT" property="orderItemId" />
    <result column="apply_type" jdbcType="VARCHAR" property="applyType" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="discount_type" jdbcType="VARCHAR" property="discountType" />
    <result column="discount_amount" jdbcType="NUMERIC" property="discountAmount" />
    <result column="discount_rate" jdbcType="NUMERIC" property="discountRate" />
    <result column="apply_by" jdbcType="BIGINT" property="applyBy" />
    <result column="apply_sequence" jdbcType="INTEGER" property="applySequence" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="discount_code_id" jdbcType="BIGINT" property="discountCodeId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, order_id, order_item_id, apply_type, is_deleted, discount_type, 
    discount_amount, discount_rate, apply_by, apply_sequence, create_time, update_time, 
    discount_code_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_line_discount
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from order_line_discount
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderLineDiscount" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_line_discount (business_id, order_id, order_item_id, 
      apply_type, is_deleted, discount_type, 
      discount_amount, discount_rate, apply_by, 
      apply_sequence, create_time, update_time, 
      discount_code_id)
    values (#{businessId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{orderItemId,jdbcType=BIGINT}, 
      #{applyType,jdbcType=VARCHAR}, #{isDeleted,jdbcType=BIT}, #{discountType,jdbcType=VARCHAR}, 
      #{discountAmount,jdbcType=NUMERIC}, #{discountRate,jdbcType=NUMERIC}, #{applyBy,jdbcType=BIGINT}, 
      #{applySequence,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{discountCodeId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderLineDiscount" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_line_discount
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderItemId != null">
        order_item_id,
      </if>
      <if test="applyType != null">
        apply_type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="discountType != null">
        discount_type,
      </if>
      <if test="discountAmount != null">
        discount_amount,
      </if>
      <if test="discountRate != null">
        discount_rate,
      </if>
      <if test="applyBy != null">
        apply_by,
      </if>
      <if test="applySequence != null">
        apply_sequence,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="discountCodeId != null">
        discount_code_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderItemId != null">
        #{orderItemId,jdbcType=BIGINT},
      </if>
      <if test="applyType != null">
        #{applyType,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="discountType != null">
        #{discountType,jdbcType=VARCHAR},
      </if>
      <if test="discountAmount != null">
        #{discountAmount,jdbcType=NUMERIC},
      </if>
      <if test="discountRate != null">
        #{discountRate,jdbcType=NUMERIC},
      </if>
      <if test="applyBy != null">
        #{applyBy,jdbcType=BIGINT},
      </if>
      <if test="applySequence != null">
        #{applySequence,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="discountCodeId != null">
        #{discountCodeId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.order.repository.entity.OrderLineDiscount">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_line_discount
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderItemId != null">
        order_item_id = #{orderItemId,jdbcType=BIGINT},
      </if>
      <if test="applyType != null">
        apply_type = #{applyType,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="discountType != null">
        discount_type = #{discountType,jdbcType=VARCHAR},
      </if>
      <if test="discountAmount != null">
        discount_amount = #{discountAmount,jdbcType=NUMERIC},
      </if>
      <if test="discountRate != null">
        discount_rate = #{discountRate,jdbcType=NUMERIC},
      </if>
      <if test="applyBy != null">
        apply_by = #{applyBy,jdbcType=BIGINT},
      </if>
      <if test="applySequence != null">
        apply_sequence = #{applySequence,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="discountCodeId != null">
        discount_code_id = #{discountCodeId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.order.repository.entity.OrderLineDiscount">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_line_discount
    set business_id = #{businessId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      order_item_id = #{orderItemId,jdbcType=BIGINT},
      apply_type = #{applyType,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIT},
      discount_type = #{discountType,jdbcType=VARCHAR},
      discount_amount = #{discountAmount,jdbcType=NUMERIC},
      discount_rate = #{discountRate,jdbcType=NUMERIC},
      apply_by = #{applyBy,jdbcType=BIGINT},
      apply_sequence = #{applySequence,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      discount_code_id = #{discountCodeId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "order_line_discount"
    where order_id = #{orderId,jdbcType=BIGINT}
    and is_deleted = false
  </select>

  <insert id="batchInsertRecords" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderLineDiscount" useGeneratedKeys="true">
    insert into "order_line_discount" (business_id, order_id, order_item_id, apply_type,
    is_deleted, discount_type, discount_amount, discount_rate, apply_by,
    apply_sequence, discount_code_id, create_time, update_time)
    values
    <foreach collection="records" index="index" item="record" separator=",">
      (#{record.businessId}, #{record.orderId}, #{record.orderItemId}, #{record.applyType},
      #{record.isDeleted}, #{record.discountType}, #{record.discountAmount},
      #{record.discountRate}, #{record.applyBy}, #{record.applySequence}, #{record.discountCodeId},
      <choose>
        <when test="record.createTime != null">
          #{record.createTime,jdbcType=TIMESTAMP},
        </when>
        <otherwise>
          NOW(),
        </otherwise>
      </choose>
      <choose>
        <when test="record.updateTime != null">
          #{record.updateTime,jdbcType=TIMESTAMP}
        </when>
        <otherwise>
          NOW()
        </otherwise>
      </choose>
      )
    </foreach>
  </insert>

  <update id="updateDiscountCodeId" parameterType="map">
      <foreach collection="discountCodeIdMap.entrySet()" index="key" item="value" separator=";">
          update "order_line_discount"
          set discount_code_id = #{value}
          where discount_code_id = #{key}
      </foreach>
  </update>

  <update id="batchUpdateByPrimaryKeySelective">
    <foreach collection="list" index="index" item="item" separator=";">
      update "order_line_discount"
      <set>
        <if test="item.applyType != null">
          apply_type = #{item.applyType,jdbcType=CHAR},
        </if>
        <if test="item.isDeleted != null">
          is_deleted = #{item.isDeleted,jdbcType=BOOLEAN},
        </if>
        <if test="item.discountType != null">
          discount_type = #{item.discountType,jdbcType=CHAR},
        </if>
        <if test="item.discountAmount != null">
          discount_amount = #{item.discountAmount,jdbcType=NUMERIC},
        </if>
        <if test="item.discountRate != null">
          discount_rate = #{item.discountRate,jdbcType=NUMERIC},
        </if>
        <if test="item.applyBy != null">
          apply_by = #{item.applyBy,jdbcType=BIGINT},
        </if>
        <if test="item.applySequence != null">
          apply_sequence = #{item.applySequence,jdbcType=INTEGER},
        </if>
        <if test="item.discountCodeId != null">
          discount_code_id = #{item.discountCodeId,jdbcType=BIGINT},
        </if>
        update_time = NOW(),
      </set>
      where id = #{item.id,jdbcType=BIGINT}
    </foreach>
  </update>
</mapper>