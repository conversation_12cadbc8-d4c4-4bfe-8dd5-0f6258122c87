<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.order.repository.mapper.ServiceChargeMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.order.repository.entity.ServiceCharge">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="price" jdbcType="NUMERIC" property="price" />
    <result column="tax_id" jdbcType="INTEGER" property="taxId" />
    <result column="sort" jdbcType="INTEGER" property="sort" />
    <result column="is_mandatory" jdbcType="BIT" property="isMandatory" />
    <result column="is_active" jdbcType="BIT" property="isActive" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="created_by" jdbcType="BIGINT" property="createdBy" />
    <result column="updated_by" jdbcType="BIGINT" property="updatedBy" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="is_all_location" jdbcType="BIT" property="isAllLocation" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="auto_apply_status" jdbcType="SMALLINT" property="autoApplyStatus" typeHandler="com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler" />
    <result column="auto_apply_condition" jdbcType="SMALLINT" property="autoApplyCondition" typeHandler="com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler" />
    <result column="auto_apply_time" jdbcType="INTEGER" property="autoApplyTime" />
    <result column="auto_apply_time_type" jdbcType="SMALLINT" property="autoApplyTimeType" typeHandler="com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler" />
    <result column="service_item_types" jdbcType="ARRAY" property="serviceItemTypes" typeHandler="com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler" />
    <result column="apply_type" jdbcType="INTEGER" property="applyType" typeHandler="com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler" />
    <result column="surcharge_type" jdbcType="SMALLINT" property="surchargeType" />
    <result column="charge_method" jdbcType="SMALLINT" property="chargeMethod" typeHandler="com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler" />
    <result column="food_source_ids" jdbcType="ARRAY" property="foodSourceIds" typeHandler="com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler" />
    <result column="is_all_food_source" jdbcType="BIT" property="isAllFoodSource" />
    <result column="time_based_pricing_type" jdbcType="SMALLINT" property="timeBasedPricingType" typeHandler="com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler" />
    <result column="multiple_pets_charge_type" jdbcType="SMALLINT" property="multiplePetsChargeType" typeHandler="com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler" />
    <result column="hourly_exceed_rules" jdbcType="OTHER" property="hourlyExceedRules" typeHandler="com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler" />
    <result column="enable_service_filter" jdbcType="BIT" property="enableServiceFilter" />
    <result column="service_filter_rules" jdbcType="OTHER" property="serviceFilterRules" typeHandler="com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler" />
    <result column="source" jdbcType="SMALLINT" property="source" typeHandler="com.moego.svc.order.repository.mapper.typehandler.SourceHandler" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.autoApplyStatusCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.autoApplyConditionCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.autoApplyTimeTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.serviceItemTypesCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.applyTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.chargeMethodCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.foodSourceIdsCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.timeBasedPricingTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.multiplePetsChargeTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.hourlyExceedRulesCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.serviceFilterRulesCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.sourceCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.SourceHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.SourceHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.SourceHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.SourceHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.autoApplyStatusCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.autoApplyConditionCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.autoApplyTimeTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.serviceItemTypesCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.applyTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.chargeMethodCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.foodSourceIdsCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.timeBasedPricingTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.multiplePetsChargeTypeCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.hourlyExceedRulesCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.serviceFilterRulesCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
            <foreach collection="criteria.sourceCriteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.SourceHandler}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value,typeHandler=com.moego.svc.order.repository.mapper.typehandler.SourceHandler} and #{criterion.secondValue,typeHandler=com.moego.svc.order.repository.mapper.typehandler.SourceHandler}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem,typeHandler=com.moego.svc.order.repository.mapper.typehandler.SourceHandler}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, name, description, price, tax_id, sort, is_mandatory, is_active, 
    is_deleted, created_by, updated_by, created_at, updated_at, is_all_location, company_id, 
    auto_apply_status, auto_apply_condition, auto_apply_time, auto_apply_time_type, service_item_types, 
    apply_type, surcharge_type, charge_method, food_source_ids, is_all_food_source, time_based_pricing_type, 
    multiple_pets_charge_type, hourly_exceed_rules, enable_service_filter, service_filter_rules, 
    source
  </sql>
  <select id="selectByExample" parameterType="com.moego.svc.order.repository.entity.ServiceChargeExample" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from service_charge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from service_charge
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from service_charge
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.ServiceCharge" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into service_charge (business_id, name, description, 
      price, tax_id, sort, 
      is_mandatory, is_active, is_deleted, 
      created_by, updated_by, created_at, 
      updated_at, is_all_location, company_id, 
      auto_apply_status, 
      auto_apply_condition, 
      auto_apply_time, auto_apply_time_type, 
      service_item_types, 
      apply_type, 
      surcharge_type, charge_method, 
      food_source_ids, 
      is_all_food_source, time_based_pricing_type, 
      multiple_pets_charge_type, 
      hourly_exceed_rules, 
      enable_service_filter, service_filter_rules, 
      source
      )
    values (#{businessId,jdbcType=BIGINT}, #{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{price,jdbcType=NUMERIC}, #{taxId,jdbcType=INTEGER}, #{sort,jdbcType=INTEGER}, 
      #{isMandatory,jdbcType=BIT}, #{isActive,jdbcType=BIT}, #{isDeleted,jdbcType=BIT}, 
      #{createdBy,jdbcType=BIGINT}, #{updatedBy,jdbcType=BIGINT}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedAt,jdbcType=TIMESTAMP}, #{isAllLocation,jdbcType=BIT}, #{companyId,jdbcType=BIGINT}, 
      #{autoApplyStatus,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler}, 
      #{autoApplyCondition,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler}, 
      #{autoApplyTime,jdbcType=INTEGER}, #{autoApplyTimeType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler}, 
      #{serviceItemTypes,jdbcType=ARRAY,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler}, 
      #{applyType,jdbcType=INTEGER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler}, 
      #{surchargeType,jdbcType=SMALLINT}, #{chargeMethod,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler}, 
      #{foodSourceIds,jdbcType=ARRAY,typeHandler=com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler}, 
      #{isAllFoodSource,jdbcType=BIT}, #{timeBasedPricingType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler}, 
      #{multiplePetsChargeType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler}, 
      #{hourlyExceedRules,jdbcType=OTHER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler}, 
      #{enableServiceFilter,jdbcType=BIT}, #{serviceFilterRules,jdbcType=OTHER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler}, 
      #{source,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.SourceHandler}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.ServiceCharge" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into service_charge
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="taxId != null">
        tax_id,
      </if>
      <if test="sort != null">
        sort,
      </if>
      <if test="isMandatory != null">
        is_mandatory,
      </if>
      <if test="isActive != null">
        is_active,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createdBy != null">
        created_by,
      </if>
      <if test="updatedBy != null">
        updated_by,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="isAllLocation != null">
        is_all_location,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="autoApplyStatus != null">
        auto_apply_status,
      </if>
      <if test="autoApplyCondition != null">
        auto_apply_condition,
      </if>
      <if test="autoApplyTime != null">
        auto_apply_time,
      </if>
      <if test="autoApplyTimeType != null">
        auto_apply_time_type,
      </if>
      <if test="serviceItemTypes != null">
        service_item_types,
      </if>
      <if test="applyType != null">
        apply_type,
      </if>
      <if test="surchargeType != null">
        surcharge_type,
      </if>
      <if test="chargeMethod != null">
        charge_method,
      </if>
      <if test="foodSourceIds != null">
        food_source_ids,
      </if>
      <if test="isAllFoodSource != null">
        is_all_food_source,
      </if>
      <if test="timeBasedPricingType != null">
        time_based_pricing_type,
      </if>
      <if test="multiplePetsChargeType != null">
        multiple_pets_charge_type,
      </if>
      <if test="hourlyExceedRules != null">
        hourly_exceed_rules,
      </if>
      <if test="enableServiceFilter != null">
        enable_service_filter,
      </if>
      <if test="serviceFilterRules != null">
        service_filter_rules,
      </if>
      <if test="source != null">
        source,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        #{price,jdbcType=NUMERIC},
      </if>
      <if test="taxId != null">
        #{taxId,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        #{sort,jdbcType=INTEGER},
      </if>
      <if test="isMandatory != null">
        #{isMandatory,jdbcType=BIT},
      </if>
      <if test="isActive != null">
        #{isActive,jdbcType=BIT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="createdBy != null">
        #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isAllLocation != null">
        #{isAllLocation,jdbcType=BIT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="autoApplyStatus != null">
        #{autoApplyStatus,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler},
      </if>
      <if test="autoApplyCondition != null">
        #{autoApplyCondition,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler},
      </if>
      <if test="autoApplyTime != null">
        #{autoApplyTime,jdbcType=INTEGER},
      </if>
      <if test="autoApplyTimeType != null">
        #{autoApplyTimeType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler},
      </if>
      <if test="serviceItemTypes != null">
        #{serviceItemTypes,jdbcType=ARRAY,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler},
      </if>
      <if test="applyType != null">
        #{applyType,jdbcType=INTEGER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler},
      </if>
      <if test="surchargeType != null">
        #{surchargeType,jdbcType=SMALLINT},
      </if>
      <if test="chargeMethod != null">
        #{chargeMethod,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler},
      </if>
      <if test="foodSourceIds != null">
        #{foodSourceIds,jdbcType=ARRAY,typeHandler=com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler},
      </if>
      <if test="isAllFoodSource != null">
        #{isAllFoodSource,jdbcType=BIT},
      </if>
      <if test="timeBasedPricingType != null">
        #{timeBasedPricingType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler},
      </if>
      <if test="multiplePetsChargeType != null">
        #{multiplePetsChargeType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler},
      </if>
      <if test="hourlyExceedRules != null">
        #{hourlyExceedRules,jdbcType=OTHER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler},
      </if>
      <if test="enableServiceFilter != null">
        #{enableServiceFilter,jdbcType=BIT},
      </if>
      <if test="serviceFilterRules != null">
        #{serviceFilterRules,jdbcType=OTHER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler},
      </if>
      <if test="source != null">
        #{source,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.SourceHandler},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.order.repository.entity.ServiceChargeExample" resultType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select count(*) from service_charge
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update service_charge
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.price != null">
        price = #{record.price,jdbcType=NUMERIC},
      </if>
      <if test="record.taxId != null">
        tax_id = #{record.taxId,jdbcType=INTEGER},
      </if>
      <if test="record.sort != null">
        sort = #{record.sort,jdbcType=INTEGER},
      </if>
      <if test="record.isMandatory != null">
        is_mandatory = #{record.isMandatory,jdbcType=BIT},
      </if>
      <if test="record.isActive != null">
        is_active = #{record.isActive,jdbcType=BIT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=BIT},
      </if>
      <if test="record.createdBy != null">
        created_by = #{record.createdBy,jdbcType=BIGINT},
      </if>
      <if test="record.updatedBy != null">
        updated_by = #{record.updatedBy,jdbcType=BIGINT},
      </if>
      <if test="record.createdAt != null">
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null">
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isAllLocation != null">
        is_all_location = #{record.isAllLocation,jdbcType=BIT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.autoApplyStatus != null">
        auto_apply_status = #{record.autoApplyStatus,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler},
      </if>
      <if test="record.autoApplyCondition != null">
        auto_apply_condition = #{record.autoApplyCondition,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler},
      </if>
      <if test="record.autoApplyTime != null">
        auto_apply_time = #{record.autoApplyTime,jdbcType=INTEGER},
      </if>
      <if test="record.autoApplyTimeType != null">
        auto_apply_time_type = #{record.autoApplyTimeType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler},
      </if>
      <if test="record.serviceItemTypes != null">
        service_item_types = #{record.serviceItemTypes,jdbcType=ARRAY,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler},
      </if>
      <if test="record.applyType != null">
        apply_type = #{record.applyType,jdbcType=INTEGER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler},
      </if>
      <if test="record.surchargeType != null">
        surcharge_type = #{record.surchargeType,jdbcType=SMALLINT},
      </if>
      <if test="record.chargeMethod != null">
        charge_method = #{record.chargeMethod,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler},
      </if>
      <if test="record.foodSourceIds != null">
        food_source_ids = #{record.foodSourceIds,jdbcType=ARRAY,typeHandler=com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler},
      </if>
      <if test="record.isAllFoodSource != null">
        is_all_food_source = #{record.isAllFoodSource,jdbcType=BIT},
      </if>
      <if test="record.timeBasedPricingType != null">
        time_based_pricing_type = #{record.timeBasedPricingType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler},
      </if>
      <if test="record.multiplePetsChargeType != null">
        multiple_pets_charge_type = #{record.multiplePetsChargeType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler},
      </if>
      <if test="record.hourlyExceedRules != null">
        hourly_exceed_rules = #{record.hourlyExceedRules,jdbcType=OTHER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler},
      </if>
      <if test="record.enableServiceFilter != null">
        enable_service_filter = #{record.enableServiceFilter,jdbcType=BIT},
      </if>
      <if test="record.serviceFilterRules != null">
        service_filter_rules = #{record.serviceFilterRules,jdbcType=OTHER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.SourceHandler},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update service_charge
    set id = #{record.id,jdbcType=BIGINT},
      business_id = #{record.businessId,jdbcType=BIGINT},
      name = #{record.name,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      price = #{record.price,jdbcType=NUMERIC},
      tax_id = #{record.taxId,jdbcType=INTEGER},
      sort = #{record.sort,jdbcType=INTEGER},
      is_mandatory = #{record.isMandatory,jdbcType=BIT},
      is_active = #{record.isActive,jdbcType=BIT},
      is_deleted = #{record.isDeleted,jdbcType=BIT},
      created_by = #{record.createdBy,jdbcType=BIGINT},
      updated_by = #{record.updatedBy,jdbcType=BIGINT},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      is_all_location = #{record.isAllLocation,jdbcType=BIT},
      company_id = #{record.companyId,jdbcType=BIGINT},
      auto_apply_status = #{record.autoApplyStatus,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler},
      auto_apply_condition = #{record.autoApplyCondition,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler},
      auto_apply_time = #{record.autoApplyTime,jdbcType=INTEGER},
      auto_apply_time_type = #{record.autoApplyTimeType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler},
      service_item_types = #{record.serviceItemTypes,jdbcType=ARRAY,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler},
      apply_type = #{record.applyType,jdbcType=INTEGER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler},
      surcharge_type = #{record.surchargeType,jdbcType=SMALLINT},
      charge_method = #{record.chargeMethod,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler},
      food_source_ids = #{record.foodSourceIds,jdbcType=ARRAY,typeHandler=com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler},
      is_all_food_source = #{record.isAllFoodSource,jdbcType=BIT},
      time_based_pricing_type = #{record.timeBasedPricingType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler},
      multiple_pets_charge_type = #{record.multiplePetsChargeType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler},
      hourly_exceed_rules = #{record.hourlyExceedRules,jdbcType=OTHER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler},
      enable_service_filter = #{record.enableServiceFilter,jdbcType=BIT},
      service_filter_rules = #{record.serviceFilterRules,jdbcType=OTHER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler},
      source = #{record.source,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.SourceHandler}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.order.repository.entity.ServiceCharge">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update service_charge
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=NUMERIC},
      </if>
      <if test="taxId != null">
        tax_id = #{taxId,jdbcType=INTEGER},
      </if>
      <if test="sort != null">
        sort = #{sort,jdbcType=INTEGER},
      </if>
      <if test="isMandatory != null">
        is_mandatory = #{isMandatory,jdbcType=BIT},
      </if>
      <if test="isActive != null">
        is_active = #{isActive,jdbcType=BIT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="createdBy != null">
        created_by = #{createdBy,jdbcType=BIGINT},
      </if>
      <if test="updatedBy != null">
        updated_by = #{updatedBy,jdbcType=BIGINT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="isAllLocation != null">
        is_all_location = #{isAllLocation,jdbcType=BIT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="autoApplyStatus != null">
        auto_apply_status = #{autoApplyStatus,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler},
      </if>
      <if test="autoApplyCondition != null">
        auto_apply_condition = #{autoApplyCondition,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler},
      </if>
      <if test="autoApplyTime != null">
        auto_apply_time = #{autoApplyTime,jdbcType=INTEGER},
      </if>
      <if test="autoApplyTimeType != null">
        auto_apply_time_type = #{autoApplyTimeType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler},
      </if>
      <if test="serviceItemTypes != null">
        service_item_types = #{serviceItemTypes,jdbcType=ARRAY,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler},
      </if>
      <if test="applyType != null">
        apply_type = #{applyType,jdbcType=INTEGER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler},
      </if>
      <if test="surchargeType != null">
        surcharge_type = #{surchargeType,jdbcType=SMALLINT},
      </if>
      <if test="chargeMethod != null">
        charge_method = #{chargeMethod,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler},
      </if>
      <if test="foodSourceIds != null">
        food_source_ids = #{foodSourceIds,jdbcType=ARRAY,typeHandler=com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler},
      </if>
      <if test="isAllFoodSource != null">
        is_all_food_source = #{isAllFoodSource,jdbcType=BIT},
      </if>
      <if test="timeBasedPricingType != null">
        time_based_pricing_type = #{timeBasedPricingType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler},
      </if>
      <if test="multiplePetsChargeType != null">
        multiple_pets_charge_type = #{multiplePetsChargeType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler},
      </if>
      <if test="hourlyExceedRules != null">
        hourly_exceed_rules = #{hourlyExceedRules,jdbcType=OTHER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler},
      </if>
      <if test="enableServiceFilter != null">
        enable_service_filter = #{enableServiceFilter,jdbcType=BIT},
      </if>
      <if test="serviceFilterRules != null">
        service_filter_rules = #{serviceFilterRules,jdbcType=OTHER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler},
      </if>
      <if test="source != null">
        source = #{source,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.SourceHandler},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.order.repository.entity.ServiceCharge">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update service_charge
    set business_id = #{businessId,jdbcType=BIGINT},
      name = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      price = #{price,jdbcType=NUMERIC},
      tax_id = #{taxId,jdbcType=INTEGER},
      sort = #{sort,jdbcType=INTEGER},
      is_mandatory = #{isMandatory,jdbcType=BIT},
      is_active = #{isActive,jdbcType=BIT},
      is_deleted = #{isDeleted,jdbcType=BIT},
      created_by = #{createdBy,jdbcType=BIGINT},
      updated_by = #{updatedBy,jdbcType=BIGINT},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      is_all_location = #{isAllLocation,jdbcType=BIT},
      company_id = #{companyId,jdbcType=BIGINT},
      auto_apply_status = #{autoApplyStatus,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler},
      auto_apply_condition = #{autoApplyCondition,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler},
      auto_apply_time = #{autoApplyTime,jdbcType=INTEGER},
      auto_apply_time_type = #{autoApplyTimeType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler},
      service_item_types = #{serviceItemTypes,jdbcType=ARRAY,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler},
      apply_type = #{applyType,jdbcType=INTEGER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler},
      surcharge_type = #{surchargeType,jdbcType=SMALLINT},
      charge_method = #{chargeMethod,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler},
      food_source_ids = #{foodSourceIds,jdbcType=ARRAY,typeHandler=com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler},
      is_all_food_source = #{isAllFoodSource,jdbcType=BIT},
      time_based_pricing_type = #{timeBasedPricingType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler},
      multiple_pets_charge_type = #{multiplePetsChargeType,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler},
      hourly_exceed_rules = #{hourlyExceedRules,jdbcType=OTHER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler},
      enable_service_filter = #{enableServiceFilter,jdbcType=BIT},
      service_filter_rules = #{serviceFilterRules,jdbcType=OTHER,typeHandler=com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler},
      source = #{source,jdbcType=SMALLINT,typeHandler=com.moego.svc.order.repository.mapper.typehandler.SourceHandler}
    where id = #{id,jdbcType=BIGINT}
  </update>


  <sql id="Base_Column_List_With_Override">
    <!--
        from "service_charge" sc
            LEFT JOIN service_charge_location scl
                ON scl.service_charge_id = sc.id AND scl.business_id = #{businessId} and scl.deleted_at is NULL
    -->
    sc.id, sc.business_id, sc."name", sc.description, sc.sort, sc.is_mandatory, sc.is_active,
    sc.is_deleted, sc.created_by, sc.updated_by, sc.created_at, sc.updated_at, sc.is_all_location,sc.company_id,
    sc.auto_apply_status, sc.auto_apply_condition, sc.auto_apply_time, sc.auto_apply_time_type, sc.service_item_types, sc.apply_type,
    CASE WHEN scl.tax_id is not NULL THEN scl.tax_id ELSE sc.tax_id END as tax_id,
    CASE WHEN scl.price is not NULL THEN scl.price ELSE sc.price END as price,
    sc.surcharge_type,sc.charge_method,sc.food_source_ids,sc.is_all_food_source,sc.time_based_pricing_type,sc.multiple_pets_charge_type,sc.hourly_exceed_rules,
    sc.enable_service_filter,sc.service_filter_rules
  </sql>

  <select id="selectByBusinessIdAndIsActive" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "service_charge"
    where business_id = #{businessId,jdbcType=BIGINT}
    <if test="isActive != null">
      and is_active = #{isActive,jdbcType=BOOLEAN}
    </if>
    <if test="isMandatory != null">
      and is_mandatory = #{isMandatory,jdbcType=BOOLEAN}
    </if>
    <if test="includedDeleted == null or !includedDeleted">
      and is_deleted = false
    </if>
    order by sort desc, id desc
  </select>


  <select id="selectByFinalBusinessIdAndIsActive" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List_With_Override" />
    from "service_charge" sc
      LEFT JOIN service_charge_location scl
      ON scl.service_charge_id = sc.id AND scl.business_id = #{businessId} AND scl.deleted_at is NULL
    where
        sc.company_id = #{companyId,jdbcType=BIGINT}
    <if test="isActive != null">
      and sc.is_active = #{isActive,jdbcType=BOOLEAN}
    </if>
    <if test="isMandatory != null">
      and sc.is_mandatory = #{isMandatory,jdbcType=BOOLEAN}
    </if>
    <if test="includedDeleted == null or !includedDeleted">
      and sc.is_deleted = false
    </if>
    AND (
        sc.is_all_location = true
        OR (
            sc.is_all_location = false
            AND sc.id in (
                select service_charge_id from service_charge_location
                where
                company_id = #{companyId,jdbcType=BIGINT}
                AND
                business_id = #{businessId}
                AND deleted_at is null
              )
            )
          )
    order by sort desc, id desc
  </select>
  <select id="selectFinalByBid" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List_With_Override" />
    from "service_charge" sc
      LEFT JOIN service_charge_location scl
      ON scl.service_charge_id = sc.id AND scl.business_id = #{businessId} AND scl.deleted_at is NULL
    where
        sc.company_id = #{companyId,jdbcType=BIGINT}
    and sc.id = #{serviceChargeId,jdbcType=BIGINT};
  </select>

  <select id="selectByCidBidsAndIsActive" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "service_charge" sc
    where
        sc.company_id = #{companyId,jdbcType=BIGINT}
    <if test="isActive != null">
      and sc.is_active = #{isActive,jdbcType=BOOLEAN}
    </if>
    <if test="isMandatory != null">
      and sc.is_mandatory = #{isMandatory,jdbcType=BOOLEAN}
    </if>
    <if test="includedDeleted == null or !includedDeleted">
      and sc.is_deleted = false
    </if>
    <if test="businessIds != null and businessIds.size() &gt; 0">
      AND (
        sc.is_all_location = true
        OR (
            sc.is_all_location = false
            AND sc.id in (
                select service_charge_id from service_charge_location
                where
                company_id = #{companyId,jdbcType=BIGINT}
                AND
                business_id IN
                <foreach close=")" collection="businessIds" item="item" open="(" separator=",">
                  #{item}
                </foreach>
                AND deleted_at is null
                    )
            )
          )
    </if>
    order by sort desc, id desc
  </select>

  <select id="selectByCidBidsAndIsActiveAndSurchargeType" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "service_charge" sc
    where
        sc.company_id = #{companyId,jdbcType=BIGINT}
    <if test="isActive != null">
      and sc.is_active = #{isActive,jdbcType=BOOLEAN}
    </if>
    <if test="isMandatory != null">
      and sc.is_mandatory = #{isMandatory,jdbcType=BOOLEAN}
    </if>
    <if test="includedDeleted == null or !includedDeleted">
      and sc.is_deleted = false
    </if>
    <if test="businessIds != null and businessIds.size() &gt; 0">
      AND (
        sc.is_all_location = true
        OR (
            sc.is_all_location = false
            AND sc.id in (
                select service_charge_id from service_charge_location
                where
                company_id = #{companyId,jdbcType=BIGINT}
                AND
                business_id IN
                <foreach close=")" collection="businessIds" item="item" open="(" separator=",">
                  #{item}
                </foreach>
                AND deleted_at is null
                    )
            )
          )
    </if>
    <if test="surchargeType != null and surchargeType != 0">
            AND surcharge_type = #{surchargeType,jdbcType=INTEGER}
    </if>
    order by sort desc, id desc
  </select>

  <select id="countByBusinessIdAndName" resultType="integer">
    select count(*)
    from "service_charge"
    where business_id = #{businessId,jdbcType=BIGINT}
    and name = #{name,jdbcType=VARCHAR}
    <if test="exceptId != null">
      and id != #{exceptId,jdbcType=BIGINT}
    </if>
    and is_deleted = false
  </select>
  <select id="countByCompanyIdAndName" resultType="integer">
    select count(*)
    from "service_charge"
    where company_id = #{companyId,jdbcType=BIGINT}
    and name = #{name,jdbcType=VARCHAR}
    <if test="exceptId != null">
      and id != #{exceptId,jdbcType=BIGINT}
    </if>
    and is_deleted = false
  </select>

  <select id="selectByBusinessIdAndIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "service_charge"
    where business_id = #{businessId,jdbcType=BIGINT}
    <if test="ids != null and ids.size() &gt; 0">
      and id in
      <foreach close=")" collection="ids" item="id" open="(" separator=",">
        #{id}
      </foreach>
    </if>
  </select>

  <select id="selectByCidBidAndIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List_With_Override" />
    from "service_charge" sc
    LEFT JOIN service_charge_location scl
    ON scl.service_charge_id = sc.id AND scl.business_id = #{businessId} and scl.deleted_at is NULL
    where sc.company_id = #{companyId,jdbcType=BIGINT}
    <if test="ids != null and ids.size() &gt; 0">
      and sc.id in
      <foreach close=")" collection="ids" item="id" open="(" separator=",">
        #{id}
      </foreach>
    </if>
  </select>

  <update id="batchUpdateSortById" parameterType="java.util.List">
    update "service_charge"
    <trim prefix="set" suffixOverrides=",">
      <trim prefix="sort = case" suffix="end,">
        <foreach collection="updateList" index="index" item="item">
          when id = #{item.id} then #{item.sort}
        </foreach>
      </trim>
      <trim prefix="updated_by = case" suffix="end,">
        <foreach collection="updateList" index="index" item="item">
          when id = #{item.id} then #{item.updatedBy}
        </foreach>
      </trim>
    </trim>
    where id in
    <foreach close=")" collection="updateList" index="index" item="item" open="(" separator=",">
      #{item.id}
    </foreach>
  </update>

  <update id="batchDeleteByBusinessIdAndId" parameterType="java.util.List">
    update "service_charge"
    set is_deleted = true,
        updated_by = #{updatedBy,jdbcType=BIGINT}
    where business_id = #{businessId,jdbcType=BIGINT}
    and id in
    <foreach close=")" collection="deleteIdList" index="index" item="id" open="(" separator=",">
      #{id}
    </foreach>
  </update>
  <update id="batchDeleteByBidCidAndId" parameterType="java.util.List">
    update "service_charge"
    set is_deleted = true,
        updated_by = #{updatedBy,jdbcType=BIGINT}
    where company_id = #{companyId,jdbcType=BIGINT}
    and id in
    <foreach close=")" collection="deleteIdList" index="index" item="id" open="(" separator=",">
      #{id}
    </foreach>
  </update>
  <update id="updateSetBusinessZero">
    update "service_charge"
    set business_id = 0
    where company_id = #{companyId}
  </update>
</mapper>