<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.order.repository.mapper.OrderMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.order.repository.entity.Order">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="payment_status" jdbcType="VARCHAR" property="paymentStatus" />
    <result column="guid" jdbcType="VARCHAR" property="guid" />
    <result column="source_type" jdbcType="VARCHAR" property="sourceType" />
    <result column="source_id" jdbcType="BIGINT" property="sourceId" />
    <result column="line_item_types" jdbcType="INTEGER" property="lineItemTypes" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="tips_amount" jdbcType="NUMERIC" property="tipsAmount" />
    <result column="tax_amount" jdbcType="NUMERIC" property="taxAmount" />
    <result column="discount_amount" jdbcType="NUMERIC" property="discountAmount" />
    <result column="deposit_amount" jdbcType="NUMERIC" property="depositAmount" />
    <result column="extra_fee_amount" jdbcType="NUMERIC" property="extraFeeAmount" />
    <result column="sub_total_amount" jdbcType="NUMERIC" property="subTotalAmount" />
    <result column="tips_based_amount" jdbcType="NUMERIC" property="tipsBasedAmount" />
    <result column="total_amount" jdbcType="NUMERIC" property="totalAmount" />
    <result column="paid_amount" jdbcType="NUMERIC" property="paidAmount" />
    <result column="remain_amount" jdbcType="NUMERIC" property="remainAmount" />
    <result column="refunded_amount" jdbcType="NUMERIC" property="refundedAmount" />
    <result column="title" jdbcType="VARCHAR" property="title" />
    <result column="create_by" jdbcType="BIGINT" property="createBy" />
    <result column="update_by" jdbcType="BIGINT" property="updateBy" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="fulfillment_status" jdbcType="VARCHAR" property="fulfillmentStatus" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="order_type" jdbcType="VARCHAR" property="orderType" />
    <result column="order_ref_id" jdbcType="BIGINT" property="orderRefId" />
    <result column="extra_charge_reason" jdbcType="VARCHAR" property="extraChargeReason" />
    <result column="order_version" jdbcType="SMALLINT" property="orderVersion" />
    <result column="currency_code" jdbcType="VARCHAR" property="currencyCode" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="tax_round_mod" jdbcType="SMALLINT" property="taxRoundMod" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, status, payment_status, guid, source_type, source_id, line_item_types, 
    version, customer_id, tips_amount, tax_amount, discount_amount, deposit_amount, extra_fee_amount,
    sub_total_amount, tips_based_amount, total_amount, paid_amount, remain_amount, refunded_amount, 
    title, create_by, update_by, create_time, update_time, description, fulfillment_status, 
    complete_time, order_type, order_ref_id, extra_charge_reason, order_version, currency_code, 
    company_id, tax_round_mod
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from public.order
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from public.order
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.Order" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into public.order (business_id, status, payment_status, 
      guid, source_type, source_id, 
      line_item_types, version, customer_id, 
      tips_amount, tax_amount, discount_amount, deposit_amount,
      extra_fee_amount, sub_total_amount, tips_based_amount, 
      total_amount, paid_amount, remain_amount, 
      refunded_amount, title, create_by, 
      update_by, create_time, update_time, 
      description, fulfillment_status, complete_time, 
      order_type, order_ref_id, extra_charge_reason, 
      order_version, currency_code, company_id, 
      tax_round_mod)
    values (#{businessId,jdbcType=BIGINT}, #{status,jdbcType=INTEGER}, #{paymentStatus,jdbcType=VARCHAR}, 
      #{guid,jdbcType=VARCHAR}, #{sourceType,jdbcType=VARCHAR}, #{sourceId,jdbcType=BIGINT}, 
      #{lineItemTypes,jdbcType=INTEGER}, #{version,jdbcType=INTEGER}, #{customerId,jdbcType=BIGINT}, 
      #{tipsAmount,jdbcType=NUMERIC}, #{taxAmount,jdbcType=NUMERIC}, #{discountAmount,jdbcType=NUMERIC}, #{depositAmount,jdbcType=NUMERIC}
      #{extraFeeAmount,jdbcType=NUMERIC}, #{subTotalAmount,jdbcType=NUMERIC}, #{tipsBasedAmount,jdbcType=NUMERIC}, 
      #{totalAmount,jdbcType=NUMERIC}, #{paidAmount,jdbcType=NUMERIC}, #{remainAmount,jdbcType=NUMERIC}, 
      #{refundedAmount,jdbcType=NUMERIC}, #{title,jdbcType=VARCHAR}, #{createBy,jdbcType=BIGINT}, 
      #{updateBy,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{description,jdbcType=VARCHAR}, #{fulfillmentStatus,jdbcType=VARCHAR}, #{completeTime,jdbcType=TIMESTAMP}, 
      #{orderType,jdbcType=VARCHAR}, #{orderRefId,jdbcType=BIGINT}, #{extraChargeReason,jdbcType=VARCHAR}, 
      #{orderVersion,jdbcType=SMALLINT}, #{currencyCode,jdbcType=VARCHAR}, #{companyId,jdbcType=BIGINT}, 
      #{taxRoundMod,jdbcType=SMALLINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.Order" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into public.order
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="paymentStatus != null">
        payment_status,
      </if>
      <if test="guid != null">
        guid,
      </if>
      <if test="sourceType != null">
        source_type,
      </if>
      <if test="sourceId != null">
        source_id,
      </if>
      <if test="lineItemTypes != null">
        line_item_types,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="tipsAmount != null">
        tips_amount,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="discountAmount != null">
        discount_amount,
      </if>
      <if test="depositAmount != null">
        deposit_amount,
      </if>
      <if test="extraFeeAmount != null">
        extra_fee_amount,
      </if>
      <if test="subTotalAmount != null">
        sub_total_amount,
      </if>
      <if test="tipsBasedAmount != null">
        tips_based_amount,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="paidAmount != null">
        paid_amount,
      </if>
      <if test="remainAmount != null">
        remain_amount,
      </if>
      <if test="refundedAmount != null">
        refunded_amount,
      </if>
      <if test="title != null">
        title,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="fulfillmentStatus != null">
        fulfillment_status,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="orderType != null">
        order_type,
      </if>
      <if test="orderRefId != null">
        order_ref_id,
      </if>
      <if test="extraChargeReason != null">
        extra_charge_reason,
      </if>
      <if test="orderVersion != null">
        order_version,
      </if>
      <if test="currencyCode != null">
        currency_code,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="taxRoundMod != null">
        tax_round_mod,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="paymentStatus != null">
        #{paymentStatus,jdbcType=VARCHAR},
      </if>
      <if test="guid != null">
        #{guid,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="lineItemTypes != null">
        #{lineItemTypes,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="tipsAmount != null">
        #{tipsAmount,jdbcType=NUMERIC},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=NUMERIC},
      </if>
      <if test="discountAmount != null">
        #{discountAmount,jdbcType=NUMERIC},
      </if>
      <if test="depositAmount != null">
        #{depositAmount,jdbcType=NUMERIC},
      </if>
      <if test="extraFeeAmount != null">
        #{extraFeeAmount,jdbcType=NUMERIC},
      </if>
      <if test="subTotalAmount != null">
        #{subTotalAmount,jdbcType=NUMERIC},
      </if>
      <if test="tipsBasedAmount != null">
        #{tipsBasedAmount,jdbcType=NUMERIC},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=NUMERIC},
      </if>
      <if test="paidAmount != null">
        #{paidAmount,jdbcType=NUMERIC},
      </if>
      <if test="remainAmount != null">
        #{remainAmount,jdbcType=NUMERIC},
      </if>
      <if test="refundedAmount != null">
        #{refundedAmount,jdbcType=NUMERIC},
      </if>
      <if test="title != null">
        #{title,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="fulfillmentStatus != null">
        #{fulfillmentStatus,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="orderRefId != null">
        #{orderRefId,jdbcType=BIGINT},
      </if>
      <if test="extraChargeReason != null">
        #{extraChargeReason,jdbcType=VARCHAR},
      </if>
      <if test="orderVersion != null">
        #{orderVersion,jdbcType=SMALLINT},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="taxRoundMod != null">
        #{taxRoundMod,jdbcType=SMALLINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.order.repository.entity.Order">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update public.order
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="paymentStatus != null">
        payment_status = #{paymentStatus,jdbcType=VARCHAR},
      </if>
      <if test="guid != null">
        guid = #{guid,jdbcType=VARCHAR},
      </if>
      <if test="sourceType != null">
        source_type = #{sourceType,jdbcType=VARCHAR},
      </if>
      <if test="sourceId != null">
        source_id = #{sourceId,jdbcType=BIGINT},
      </if>
      <if test="lineItemTypes != null">
        line_item_types = #{lineItemTypes,jdbcType=INTEGER},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="tipsAmount != null">
        tips_amount = #{tipsAmount,jdbcType=NUMERIC},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=NUMERIC},
      </if>
      <if test="discountAmount != null">
        discount_amount = #{discountAmount,jdbcType=NUMERIC},
      </if>
      <if test="depositAmount != null">
        deposit_amount = #{depositAmount,jdbcType=NUMERIC},
      </if>
      <if test="extraFeeAmount != null">
        extra_fee_amount = #{extraFeeAmount,jdbcType=NUMERIC},
      </if>
      <if test="subTotalAmount != null">
        sub_total_amount = #{subTotalAmount,jdbcType=NUMERIC},
      </if>
      <if test="tipsBasedAmount != null">
        tips_based_amount = #{tipsBasedAmount,jdbcType=NUMERIC},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=NUMERIC},
      </if>
      <if test="paidAmount != null">
        paid_amount = #{paidAmount,jdbcType=NUMERIC},
      </if>
      <if test="remainAmount != null">
        remain_amount = #{remainAmount,jdbcType=NUMERIC},
      </if>
      <if test="refundedAmount != null">
        refunded_amount = #{refundedAmount,jdbcType=NUMERIC},
      </if>
      <if test="title != null">
        title = #{title,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=BIGINT},
      </if>
      <if test="updateBy != null">
        update_by = #{updateBy,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="fulfillmentStatus != null">
        fulfillment_status = #{fulfillmentStatus,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderType != null">
        order_type = #{orderType,jdbcType=VARCHAR},
      </if>
      <if test="orderRefId != null">
        order_ref_id = #{orderRefId,jdbcType=BIGINT},
      </if>
      <if test="extraChargeReason != null">
        extra_charge_reason = #{extraChargeReason,jdbcType=VARCHAR},
      </if>
      <if test="orderVersion != null">
        order_version = #{orderVersion,jdbcType=SMALLINT},
      </if>
      <if test="currencyCode != null">
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="taxRoundMod != null">
        tax_round_mod = #{taxRoundMod,jdbcType=SMALLINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.order.repository.entity.Order">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update public.order
    set business_id = #{businessId,jdbcType=BIGINT},
      status = #{status,jdbcType=INTEGER},
      payment_status = #{paymentStatus,jdbcType=VARCHAR},
      guid = #{guid,jdbcType=VARCHAR},
      source_type = #{sourceType,jdbcType=VARCHAR},
      source_id = #{sourceId,jdbcType=BIGINT},
      line_item_types = #{lineItemTypes,jdbcType=INTEGER},
      version = #{version,jdbcType=INTEGER},
      customer_id = #{customerId,jdbcType=BIGINT},
      tips_amount = #{tipsAmount,jdbcType=NUMERIC},
      tax_amount = #{taxAmount,jdbcType=NUMERIC},
      discount_amount = #{discountAmount,jdbcType=NUMERIC},
      deposit_amount = #{depositAmount,jdbcType=NUMERIC},
      extra_fee_amount = #{extraFeeAmount,jdbcType=NUMERIC},
      sub_total_amount = #{subTotalAmount,jdbcType=NUMERIC},
      tips_based_amount = #{tipsBasedAmount,jdbcType=NUMERIC},
      total_amount = #{totalAmount,jdbcType=NUMERIC},
      paid_amount = #{paidAmount,jdbcType=NUMERIC},
      remain_amount = #{remainAmount,jdbcType=NUMERIC},
      refunded_amount = #{refundedAmount,jdbcType=NUMERIC},
      title = #{title,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=BIGINT},
      update_by = #{updateBy,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      description = #{description,jdbcType=VARCHAR},
      fulfillment_status = #{fulfillmentStatus,jdbcType=VARCHAR},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      order_type = #{orderType,jdbcType=VARCHAR},
      order_ref_id = #{orderRefId,jdbcType=BIGINT},
      extra_charge_reason = #{extraChargeReason,jdbcType=VARCHAR},
      order_version = #{orderVersion,jdbcType=SMALLINT},
      currency_code = #{currencyCode,jdbcType=VARCHAR},
      company_id = #{companyId,jdbcType=BIGINT},
      tax_round_mod = #{taxRoundMod,jdbcType=SMALLINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

    <resultMap id="CustomerPaymentSummaryMap" type="com.moego.svc.order.model.dto.CustomerPaymentSummaryDTO">
        <result column="customer_id" jdbcType="BIGINT" property="customerId" />
        <result column="total_payment_amount" jdbcType="DECIMAL" property="totalPaymentAmount" />
        <result column="total_paid_amount" jdbcType="DECIMAL" property="totalPaidAmount" />
        <result column="total_remain_amount" jdbcType="DECIMAL" property="totalRemainAmount" />
    </resultMap>

    <sql id="Base_Column_List_With_Alias">
        ${alias}.id,
        ${alias}.business_id,
        ${alias}.status,
        ${alias}.payment_status,
        ${alias}.fulfillment_status,
        ${alias}.guid,
        ${alias}.source_type,
        ${alias}.source_id,
        ${alias}.line_item_types,
        ${alias}.version,
        ${alias}.customer_id,
        ${alias}.tips_amount,
        ${alias}.tax_amount,
        ${alias}.discount_amount,
        ${alias}.deposit_amount,
        ${alias}.extra_fee_amount,
        ${alias}.sub_total_amount,
        ${alias}.tips_based_amount,
        ${alias}.total_amount,
        ${alias}.paid_amount,
        ${alias}.remain_amount,
        ${alias}.refunded_amount,
        ${alias}.title,
        ${alias}.description,
        ${alias}.create_by,
        ${alias}.update_by,
        ${alias}.create_time,
        ${alias}.update_time,
        ${alias}.complete_time,
        ${alias}.order_type,
        ${alias}.order_ref_id,
        ${alias}.extra_charge_reason,
        ${alias}.currency_code,
        ${alias}.company_id,
        ${alias}.order_version
    </sql>

    <select id="selectByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from "order"
        where id in
        <foreach close=")" collection="ids" item="item" open="(" separator=",">
            #{item}
        </foreach>
        <if test="businessId != null">
            and business_id = #{businessId}
        </if>
    </select>

    <select id="selectByIdsAndStatus" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from "order"
        where id in
        <foreach close=")" collection="ids" item="item" open="(" separator=",">
            #{item}
        </foreach>
        <if test="businessId != null">
            and business_id = #{businessId}
        </if>
        <if test="statusList != null and statusList.size() &gt; 0">
            and status in
            <foreach close=")" collection="statusList" item="status" open="(" separator=",">
                #{status}
            </foreach>
        </if>
    </select>

    <select id="selectBySourceTypeAndSourceId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from "order"
        where source_type = #{sourceType}
          and source_id = #{sourceId}
          and order_type = 'ORIGIN'
    </select>

  <select id="selectLatestOrderBySourceTypeAndSourceId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "order"
    where source_type = #{sourceType}
    and source_id = #{sourceId}
<!--    <if test="sourceType == 'appointment'">-->
<!--      and order_type in ('EXTRA', 'ORIGIN')-->
<!--    </if>-->
    order by id desc limit 1
  </select>

    <select id="selectBySourceTypeAndSourceIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from "order"
        where source_id in
        <foreach close=")" collection="sourceIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        <if test="sourceType != null">
            and source_type = #{sourceType}
        </if>
        <if test="businessId != null">
            and business_id = #{businessId}
        </if>
        <if test="!includeExtraOrder">
          and order_type = 'ORIGIN'
        </if>
    </select>

    <select id="selectByGuid" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from "order"
        where guid = #{guid}
        <if test="businessId != null">
            and business_id = #{businessId}
        </if>
    </select>

    <update id="updateByBusinessIdAndId" parameterType="com.moego.svc.order.repository.entity.Order">
        update "order"
        <set>
            <if test="status != null">
                status = #{status,jdbcType=SMALLINT},
            </if>
            <if test="paymentStatus != null">
                payment_status = #{paymentStatus,jdbcType=VARCHAR},
            </if>
            <if test="fulfillmentStatus != null">
                fulfillment_status = #{fulfillmentStatus,jdbcType=VARCHAR},
            </if>
            <if test="guid != null">
                guid = #{guid,jdbcType=VARCHAR},
            </if>
            <if test="sourceType != null">
                source_type = #{sourceType,jdbcType=VARCHAR},
            </if>
            <if test="sourceId != null">
                source_id = #{sourceId,jdbcType=BIGINT},
            </if>
            <if test="lineItemTypes != null">
                line_item_types = #{lineItemTypes,jdbcType=SMALLINT},
            </if>
            <if test="customerId != null">
                customer_id = #{customerId,jdbcType=BIGINT},
            </if>
            <if test="tipsAmount != null">
                tips_amount = #{tipsAmount,jdbcType=DECIMAL},
            </if>
            <if test="taxAmount != null">
                tax_amount = #{taxAmount,jdbcType=DECIMAL},
            </if>
            <if test="discountAmount != null">
                discount_amount = #{discountAmount,jdbcType=DECIMAL},
            </if>
            <if test="depositAmount != null">
                deposit_amount = #{depositAmount,jdbcType=DECIMAL},
            </if>
            <if test="extraFeeAmount != null">
                extra_fee_amount = #{extraFeeAmount,jdbcType=DECIMAL},
            </if>
            <if test="subTotalAmount != null">
                sub_total_amount = #{subTotalAmount,jdbcType=DECIMAL},
            </if>
            <if test="tipsBasedAmount != null">
                tips_based_amount = #{tipsBasedAmount,jdbcType=DECIMAL},
            </if>
            <if test="totalAmount != null">
                total_amount = #{totalAmount,jdbcType=DECIMAL},
            </if>
            <if test="paidAmount != null">
                paid_amount = #{paidAmount,jdbcType=DECIMAL},
            </if>
            <if test="remainAmount != null">
                remain_amount = #{remainAmount,jdbcType=DECIMAL},
            </if>
            <if test="refundedAmount != null">
                refunded_amount = #{refundedAmount,jdbcType=DECIMAL},
            </if>
            <if test="title != null">
                title = #{title,jdbcType=VARCHAR},
            </if>
            <if test="description != null">
                description = #{description,jdbcType=VARCHAR},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=BIGINT},
            </if>
            <if test="extraChargeReason != null">
                extra_charge_reason = #{extraChargeReason,jdbcType=VARCHAR},
            </if>
            <if test="completeTime != null">
              complete_time = #{completeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="orderVersion != null">
              order_version = #{orderVersion,jdbcType=SMALLINT},
            </if>
            version     = version + 1,
            update_time = NOW(),
        </set>
        where id = #{id,jdbcType=BIGINT}
          and business_id = #{businessId, jdbcType=BIGINT}
    </update>

    <select id="selectCustomerOrders" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List_With_Alias">
            <property name="alias" value="o" />
        </include>
        from "order" o
                 inner join "order_line_item" oli on o.id = oli.order_id and oli.is_deleted = false
        where o.customer_id = #{params.customerId}
        <if test="params.businessId != null">
            and o.business_id = #{params.businessId}
        </if>
        <if test="params.status != null">
            and o.status in
            <foreach close=")" collection="params.status" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="params.sourceTypes != null">
            and o.source_type in
            <foreach close=")" collection="params.sourceTypes" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="params.sinceTime != null">
            and o.create_time &gt;= to_timestamp(#{params.sinceTime})
        </if>
        <if test="params.objectIds != null and params.objectIds.size() &gt; 0">
            and oli.object_id in
            <foreach close=")" collection="params.objectIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        order by o.id desc
        <if test="params.count != null">
            limit #{params.count}
        </if>
    </select>

    <select id="selectWithItems" resultMap="com.moego.svc.order.repository.mapper.OrderLineItemMapper.BaseResultMap">
        select
        <include refid="com.moego.svc.order.repository.mapper.OrderLineItemMapper.Base_Column_List_With_Alias">
            <property name="alias" value="oli" />
        </include>
        from "order" o
                 inner join "order_line_item" oli on o.id = oli.order_id and oli.is_deleted = false
        where o.customer_id = #{params.customerId}
          and o.status = 2
        <if test="params.businessId != null">
            and o.business_id = #{params.businessId}
        </if>
        <if test="params.lineItemTypes != null and params.lineItemTypes != 0">
            and o.line_item_types &amp; #{params.lineItemTypes} = #{params.lineItemTypes}
        </if>
        <if test="params.sinceTime != null">
            and o.create_time &gt;= to_timestamp(#{params.sinceTime})
        </if>
        <if test="params.objectIds != null and params.objectIds.size() &gt; 0">
            and oli.object_id in
            <foreach close=")" collection="params.objectIds" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        order by oli.id desc
        <if test="params.count != null">
            limit #{params.count}
        </if>
    </select>

    <select id="selectCustomerPaymentSummaries" resultMap="CustomerPaymentSummaryMap">
        select customer_id,
               sum(total_amount)  as total_payment_amount,
               sum(paid_amount)   as total_paid_amount,
               sum(remain_amount) as total_remain_amount
        from "order"
        where customer_id in
        <foreach close=")" collection="customerIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        <if test="sourceTypes != null and sourceTypes.size() &gt; 0">
            and source_type in
            <foreach close=")" collection="sourceTypes" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="status != null">
                and status = #{status}
            </when>
            <otherwise>
                and status &lt; 3
            </otherwise>
        </choose>
        group by customer_id
    </select>

    <select id="selectCustomerRemainAmount" resultMap="CustomerPaymentSummaryMap">
        select customer_id, 0 as total_payment_amount, 0 as total_paid_amount, sum(remain_amount) as total_remain_amount
        from "order"
        where source_id in
        <foreach close=")" collection="sourceIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        <if test="sourceTypes != null and sourceTypes.size() &gt; 0">
            and source_type in
            <foreach close=")" collection="sourceTypes" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <choose>
            <when test="status != null">
                and status = #{status}
            </when>
            <otherwise>
                and status &lt; 3
            </otherwise>
        </choose>
        group by customer_id
    </select>

    <select id="selectCountByTypesAndKeyword" resultType="integer">
        select count(*)
        from "order"
        where business_id = #{businessId}
        <if test="customerId != null">
            and customer_id = #{customerId}
        </if>
        <if test="lineItemTypes != null and lineItemTypes.size() &gt; 0">
            and
            <foreach close=")" collection="lineItemTypes" item="item" open="(" separator="or">
                line_item_types &amp; #{item} = #{item}
            </foreach>
        </if>
        <if test="keyword != null and keyword != ''">
            and (LOWER(title) like concat('%', LOWER(#{keyword}), '%') or
             LOWER(description) like concat('%', LOWER(#{keyword}), '%')
                )
        </if>
        and status != 3
    </select>

    <select id="selectPageByTypesAndKeyword" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from "order"
        where business_id = #{businessId}
        <if test="customerId != null">
            and customer_id = #{customerId}
        </if>
        <if test="lineItemTypes != null and lineItemTypes.size() &gt; 0">
            and
            <foreach close=")" collection="lineItemTypes" item="item" open="(" separator="or">
                line_item_types &amp; #{item} = #{item}
            </foreach>
        </if>
        <if test="keyword != null and keyword != ''">
            and (LOWER(title) like concat('%', LOWER(#{keyword}), '%') or
          LOWER(description) like concat('%', LOWER(#{keyword}), '%')
                )
        </if>
        and status != 3
        order by
        <choose>
            <when test="sortBy != null and orderBy != null">
                ${sortBy} ${orderBy}
            </when>
            <otherwise>
                create_time desc
            </otherwise>
        </choose>
        limit #{size} offset #{offset}
    </select>

    <select id="selectCountByCreateTime" resultType="integer">
        select count(*)
        from "order"
        where business_id = #{businessId}
        <if test="customerId != null">
            and customer_id = #{customerId}
        </if>
        <if test="lineItemTypes != null and lineItemTypes.size() &gt; 0">
            and
            <foreach close=")" collection="lineItemTypes" item="item" open="(" separator="or">
                line_item_types &amp; #{item} = #{item}
            </foreach>
        </if>
        and create_time &gt;= to_timestamp(#{startTime})
        and create_time &lt;= to_timestamp(#{endTime})
        and status != 3
    </select>

    <select id="selectByCreateTime" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from "order"
        where business_id = #{businessId}
        <if test="customerId != null">
            and customer_id = #{customerId}
        </if>
        <if test="lineItemTypes != null and lineItemTypes.size() &gt; 0">
            and
            <foreach close=")" collection="lineItemTypes" item="item" open="(" separator="or">
                line_item_types &amp; #{item} = #{item}
            </foreach>
        </if>
        and create_time &gt;= to_timestamp(#{startTime})
        and create_time &lt;= to_timestamp(#{endTime})
        and status != 3
        order by create_time desc
    </select>

    <select id="selectTipsOrderCount" resultType="integer">
        select count(*)
        from "order"
        where business_id = #{businessId}
          and source_id in
        <foreach close=")" collection="sourceIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        <if test="sourceType != null">
            and source_type = #{sourceType}
        </if>
        and tips_amount &gt; 0
    </select>

    <select id="selectTipsOrderCountV2" resultType="integer">
        select count(*)
        from "order"
        where business_id in
        <foreach close=")" collection="businessIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        and source_id in
        <foreach close=")" collection="sourceIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        <if test="sourceType != null">
            and source_type = #{sourceType}
        </if>
        and tips_amount &gt; 0
    </select>

    <select id="selectPageTipsOrder" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from "order"
        where business_id = #{businessId}
          and source_id in
        <foreach close=")" collection="sourceIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        <if test="sourceType != null">
            and source_type = #{sourceType}
        </if>
        and tips_amount &gt; 0
        order by create_time desc
        <if test="size != null and offset != null">
            limit #{size} offset #{offset}
        </if>
    </select>

    <select id="selectPageTipsOrderV2" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from "order"
        where business_id in
        <foreach close=")" collection="businessIds" item="businessId" open="(" separator=",">
            #{businessId}
        </foreach>
        and source_id in
        <foreach close=")" collection="sourceIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        <if test="sourceType != null">
            and source_type = #{sourceType}
        </if>
        and tips_amount &gt; 0
        order by create_time desc
        <if test="size != null and offset != null">
            limit #{size} offset #{offset}
        </if>
    </select>

    <select id="getOrderIdsBySourceIdsAndType" resultType="java.lang.Long">
        select id
        from "order"
        where business_id = #{businessId}
          and source_id in
        <foreach close=")" collection="sourceIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        and source_type = #{sourceType}
    </select>

    <select id="selectOrdersPagination" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List" />
        from "order"
        where business_id in
        <foreach close=")" collection="businessIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        <if test="ids != null and ids.size() &gt; 0">
            and id in
            <foreach close=")" collection="ids" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="statusList != null and statusList.size() &gt; 0">
            and status in
            <foreach close=")" collection="statusList" item="status" open="(" separator=",">
                #{status}
            </foreach>
        </if>
        <if test="lastUpdatedStartTime != null">
            and update_time &gt;= to_timestamp(#{lastUpdatedStartTime})
        </if>
        <if test="lastUpdatedEndTime != null">
            and update_time &lt; to_timestamp(#{lastUpdatedEndTime})
        </if>
        <if test="pageSize != null and pageSize &gt; 0 and pageNum != null and pageNum &gt; 0">
            limit #{pageSize} offset (#{pageNum} - 1) * #{pageSize}
        </if>
    </select>

    <select id="selectOrdersCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM "order"
        WHERE business_id IN
        <foreach close=")" collection="businessIds" item="item" open="(" separator=",">
            #{item}
        </foreach>
        <if test="ids != null and ids.size() &gt; 0">
            AND id IN
            <foreach close=")" collection="ids" item="item" open="(" separator=",">
                #{item}
            </foreach>
        </if>
        <if test="lastUpdatedStartTime != null">
            and update_time &gt;= to_timestamp(#{lastUpdatedStartTime})
        </if>
        <if test="lastUpdatedEndTime != null">
            and update_time &lt; to_timestamp(#{lastUpdatedEndTime})
        </if>
    </select>
    <select id="selectByBusinessIdAndOriginId" parameterType="java.lang.Long" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from public.order
      where business_id = #{businessId} and order_ref_id = #{originOrderId} and order_type = 'EXTRA'
      order by id desc
    </select>
  <select id="selectExtraOrderCountByOrderIdList" resultType="com.moego.svc.order.model.dto.OrderExtraOrderCountDTO">
    SELECT order_ref_id AS orderId, COUNT(*) AS count
    FROM "order"
    WHERE order_type != 'ORIGIN'
    AND order_ref_id IN
    <foreach close=")" collection="list" item="item" open="(" separator=",">
      #{item}
    </foreach>
    GROUP BY order_ref_id;
  </select>

  <select id="selectExtraOrderIdListByOriginId" resultType="java.lang.Long">
    SELECT id
    FROM "order"
    WHERE order_type = 'EXTRA'
    AND order_ref_id = #{originOrderId}
  </select>

  <select id="selectIdByBizIdAndCreateTime" resultType="java.lang.Long">
    select id
    from "order"
    where business_id = #{businessId}
    and create_time &gt;= to_timestamp(#{startTime})
    and create_time &lt;= to_timestamp(#{endTime})
    and status != 3
  </select>

  <select id="selectValidDepositOrderBySource" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "order"
    where source_type = #{sourceType}
      and source_id = #{sourceId}
      and order_type = 'DEPOSIT'
      and status != 3
  </select>

  <update id="updateCustomerId">
    UPDATE "order"
    SET customer_id = #{newCustomerId},
        update_time = NOW()
    WHERE id IN (
      SELECT id
      FROM "order"
      WHERE customer_id IN
      <foreach close=")" collection="oldCustomerIdList" item="item" open="(" separator=",">
        #{item}
      </foreach>
      LIMIT #{limit}
    )
  </update>

  <select id="selectByAppointmentForUpdate" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "order"
    where business_id = #{businessId}
      and source_id = #{sourceId}
      and source_type in ('appointment', 'noshow', 'booking_request')
      for update
  </select>
</mapper>