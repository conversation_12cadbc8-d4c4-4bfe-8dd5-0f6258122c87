<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.order.repository.mapper.OrderGroomingDetailRelMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.order.repository.entity.OrderGroomingDetailRel">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="grooming_id" jdbcType="BIGINT" property="groomingId" />
    <result column="pet_detail_id" jdbcType="BIGINT" property="petDetailId" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, order_id, grooming_id, pet_detail_id, created_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_grooming_detail_rel
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from order_grooming_detail_rel
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderGroomingDetailRel" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_grooming_detail_rel (order_id, grooming_id, pet_detail_id, 
      created_at)
    values (#{orderId,jdbcType=BIGINT}, #{groomingId,jdbcType=BIGINT}, #{petDetailId,jdbcType=BIGINT}, 
      #{createdAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderGroomingDetailRel" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_grooming_detail_rel
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="groomingId != null">
        grooming_id,
      </if>
      <if test="petDetailId != null">
        pet_detail_id,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="groomingId != null">
        #{groomingId,jdbcType=BIGINT},
      </if>
      <if test="petDetailId != null">
        #{petDetailId,jdbcType=BIGINT},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.order.repository.entity.OrderGroomingDetailRel">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_grooming_detail_rel
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="groomingId != null">
        grooming_id = #{groomingId,jdbcType=BIGINT},
      </if>
      <if test="petDetailId != null">
        pet_detail_id = #{petDetailId,jdbcType=BIGINT},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.order.repository.entity.OrderGroomingDetailRel">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_grooming_detail_rel
    set order_id = #{orderId,jdbcType=BIGINT},
      grooming_id = #{groomingId,jdbcType=BIGINT},
      pet_detail_id = #{petDetailId,jdbcType=BIGINT},
      created_at = #{createdAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByOrderIdList" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_grooming_detail_rel
    where order_id IN
    <foreach close=")" collection="list" item="item" open="(" separator=",">
      #{item}
    </foreach>
  </select>

  <select id="selectByOrderIdAndDetailId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_grooming_detail_rel
    where order_id = #{orderId} and pet_detail_id = #{petDetailId}
  </select>
</mapper>