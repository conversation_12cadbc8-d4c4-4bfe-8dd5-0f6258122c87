<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.order.repository.mapper.OrderTipsSplitDetailMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.order.repository.entity.OrderTipsSplitDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="commission_amount" jdbcType="NUMERIC" property="commissionAmount" />
    <result column="commission_rate" jdbcType="NUMERIC" property="commissionRate" />
    <result column="total_amount" jdbcType="NUMERIC" property="totalAmount" />
    <result column="refunded_amount" jdbcType="NUMERIC" property="refundedAmount" />
    <result column="split_amount" jdbcType="NUMERIC" property="splitAmount" />
    <result column="split_rate" jdbcType="NUMERIC" property="splitRate" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, create_time, update_time, order_id, staff_id, business_id, commission_amount, 
    commission_rate, total_amount, refunded_amount, split_amount, split_rate, extra
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from public.order_tips_split_detail
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from public.order_tips_split_detail
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderTipsSplitDetail" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into public.order_tips_split_detail (create_time, update_time, order_id, 
      staff_id, business_id, commission_amount, 
      commission_rate, total_amount, refunded_amount, 
      split_amount, split_rate, extra
      )
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{orderId,jdbcType=BIGINT}, 
      #{staffId,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{commissionAmount,jdbcType=NUMERIC}, 
      #{commissionRate,jdbcType=NUMERIC}, #{totalAmount,jdbcType=NUMERIC}, #{refundedAmount,jdbcType=NUMERIC}, 
      #{splitAmount,jdbcType=NUMERIC}, #{splitRate,jdbcType=NUMERIC}, #{extra,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderTipsSplitDetail" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into public.order_tips_split_detail
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="commissionAmount != null">
        commission_amount,
      </if>
      <if test="commissionRate != null">
        commission_rate,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="refundedAmount != null">
        refunded_amount,
      </if>
      <if test="splitAmount != null">
        split_amount,
      </if>
      <if test="splitRate != null">
        split_rate,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="commissionAmount != null">
        #{commissionAmount,jdbcType=NUMERIC},
      </if>
      <if test="commissionRate != null">
        #{commissionRate,jdbcType=NUMERIC},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=NUMERIC},
      </if>
      <if test="refundedAmount != null">
        #{refundedAmount,jdbcType=NUMERIC},
      </if>
      <if test="splitAmount != null">
        #{splitAmount,jdbcType=NUMERIC},
      </if>
      <if test="splitRate != null">
        #{splitRate,jdbcType=NUMERIC},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.order.repository.entity.OrderTipsSplitDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update public.order_tips_split_detail
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="commissionAmount != null">
        commission_amount = #{commissionAmount,jdbcType=NUMERIC},
      </if>
      <if test="commissionRate != null">
        commission_rate = #{commissionRate,jdbcType=NUMERIC},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=NUMERIC},
      </if>
      <if test="refundedAmount != null">
        refunded_amount = #{refundedAmount,jdbcType=NUMERIC},
      </if>
      <if test="splitAmount != null">
        split_amount = #{splitAmount,jdbcType=NUMERIC},
      </if>
      <if test="splitRate != null">
        split_rate = #{splitRate,jdbcType=NUMERIC},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.order.repository.entity.OrderTipsSplitDetail">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update public.order_tips_split_detail
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      order_id = #{orderId,jdbcType=BIGINT},
      staff_id = #{staffId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      commission_amount = #{commissionAmount,jdbcType=NUMERIC},
      commission_rate = #{commissionRate,jdbcType=NUMERIC},
      total_amount = #{totalAmount,jdbcType=NUMERIC},
      refunded_amount = #{refundedAmount,jdbcType=NUMERIC},
      split_amount = #{splitAmount,jdbcType=NUMERIC},
      split_rate = #{splitRate,jdbcType=NUMERIC},
      extra = #{extra,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <!-- com.moego.svc.order.repository.mapper.OrderTipsSplitDetailMapper.selectByOrderId -->
  <select id="selectByOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from public.order_tips_split_detail
    where order_id = #{orderId,jdbcType=BIGINT}
  </select>
</mapper>