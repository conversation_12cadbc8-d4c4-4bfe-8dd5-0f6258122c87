<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.order.repository.mapper.StaffPayrollChangeLogMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.order.repository.entity.StaffPayrollChangeLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="ref_type" jdbcType="VARCHAR" property="refType" />
    <result column="ref_id" jdbcType="BIGINT" property="refId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="amount_type" jdbcType="VARCHAR" property="amountType" />
    <result column="before_commission_amount" jdbcType="NUMERIC" property="beforeCommissionAmount" />
    <result column="commission_amount" jdbcType="NUMERIC" property="commissionAmount" />
    <result column="before_commission_rate" jdbcType="NUMERIC" property="beforeCommissionRate" />
    <result column="commission_rate" jdbcType="NUMERIC" property="commissionRate" />
    <result column="before_total_amount" jdbcType="NUMERIC" property="beforeTotalAmount" />
    <result column="total_amount" jdbcType="NUMERIC" property="totalAmount" />
    <result column="before_split_amount" jdbcType="NUMERIC" property="beforeSplitAmount" />
    <result column="split_amount" jdbcType="NUMERIC" property="splitAmount" />
    <result column="before_refunded_amount" jdbcType="NUMERIC" property="beforeRefundedAmount" />
    <result column="refunded_amount" jdbcType="NUMERIC" property="refundedAmount" />
    <result column="before_split_rate" jdbcType="NUMERIC" property="beforeSplitRate" />
    <result column="split_rate" jdbcType="NUMERIC" property="splitRate" />
    <result column="extra" jdbcType="VARCHAR" property="extra" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, create_time, update_time, ref_type, ref_id, order_id, staff_id, business_id, 
    amount_type, before_commission_amount, commission_amount, before_commission_rate, 
    commission_rate, before_total_amount, total_amount, before_split_amount, split_amount, 
    before_refunded_amount, refunded_amount, before_split_rate, split_rate, extra
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from public.staff_payroll_change_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from public.staff_payroll_change_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.StaffPayrollChangeLog" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into public.staff_payroll_change_log (create_time, update_time, ref_type, 
      ref_id, order_id, staff_id, 
      business_id, amount_type, before_commission_amount, 
      commission_amount, before_commission_rate, 
      commission_rate, before_total_amount, total_amount, 
      before_split_amount, split_amount, before_refunded_amount, 
      refunded_amount, before_split_rate, split_rate, 
      extra)
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{refType,jdbcType=VARCHAR}, 
      #{refId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{staffId,jdbcType=BIGINT}, 
      #{businessId,jdbcType=BIGINT}, #{amountType,jdbcType=VARCHAR}, #{beforeCommissionAmount,jdbcType=NUMERIC}, 
      #{commissionAmount,jdbcType=NUMERIC}, #{beforeCommissionRate,jdbcType=NUMERIC}, 
      #{commissionRate,jdbcType=NUMERIC}, #{beforeTotalAmount,jdbcType=NUMERIC}, #{totalAmount,jdbcType=NUMERIC}, 
      #{beforeSplitAmount,jdbcType=NUMERIC}, #{splitAmount,jdbcType=NUMERIC}, #{beforeRefundedAmount,jdbcType=NUMERIC}, 
      #{refundedAmount,jdbcType=NUMERIC}, #{beforeSplitRate,jdbcType=NUMERIC}, #{splitRate,jdbcType=NUMERIC}, 
      #{extra,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.StaffPayrollChangeLog" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into public.staff_payroll_change_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="refType != null">
        ref_type,
      </if>
      <if test="refId != null">
        ref_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="amountType != null">
        amount_type,
      </if>
      <if test="beforeCommissionAmount != null">
        before_commission_amount,
      </if>
      <if test="commissionAmount != null">
        commission_amount,
      </if>
      <if test="beforeCommissionRate != null">
        before_commission_rate,
      </if>
      <if test="commissionRate != null">
        commission_rate,
      </if>
      <if test="beforeTotalAmount != null">
        before_total_amount,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="beforeSplitAmount != null">
        before_split_amount,
      </if>
      <if test="splitAmount != null">
        split_amount,
      </if>
      <if test="beforeRefundedAmount != null">
        before_refunded_amount,
      </if>
      <if test="refundedAmount != null">
        refunded_amount,
      </if>
      <if test="beforeSplitRate != null">
        before_split_rate,
      </if>
      <if test="splitRate != null">
        split_rate,
      </if>
      <if test="extra != null">
        extra,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refType != null">
        #{refType,jdbcType=VARCHAR},
      </if>
      <if test="refId != null">
        #{refId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="amountType != null">
        #{amountType,jdbcType=VARCHAR},
      </if>
      <if test="beforeCommissionAmount != null">
        #{beforeCommissionAmount,jdbcType=NUMERIC},
      </if>
      <if test="commissionAmount != null">
        #{commissionAmount,jdbcType=NUMERIC},
      </if>
      <if test="beforeCommissionRate != null">
        #{beforeCommissionRate,jdbcType=NUMERIC},
      </if>
      <if test="commissionRate != null">
        #{commissionRate,jdbcType=NUMERIC},
      </if>
      <if test="beforeTotalAmount != null">
        #{beforeTotalAmount,jdbcType=NUMERIC},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=NUMERIC},
      </if>
      <if test="beforeSplitAmount != null">
        #{beforeSplitAmount,jdbcType=NUMERIC},
      </if>
      <if test="splitAmount != null">
        #{splitAmount,jdbcType=NUMERIC},
      </if>
      <if test="beforeRefundedAmount != null">
        #{beforeRefundedAmount,jdbcType=NUMERIC},
      </if>
      <if test="refundedAmount != null">
        #{refundedAmount,jdbcType=NUMERIC},
      </if>
      <if test="beforeSplitRate != null">
        #{beforeSplitRate,jdbcType=NUMERIC},
      </if>
      <if test="splitRate != null">
        #{splitRate,jdbcType=NUMERIC},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.order.repository.entity.StaffPayrollChangeLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update public.staff_payroll_change_log
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="refType != null">
        ref_type = #{refType,jdbcType=VARCHAR},
      </if>
      <if test="refId != null">
        ref_id = #{refId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="amountType != null">
        amount_type = #{amountType,jdbcType=VARCHAR},
      </if>
      <if test="beforeCommissionAmount != null">
        before_commission_amount = #{beforeCommissionAmount,jdbcType=NUMERIC},
      </if>
      <if test="commissionAmount != null">
        commission_amount = #{commissionAmount,jdbcType=NUMERIC},
      </if>
      <if test="beforeCommissionRate != null">
        before_commission_rate = #{beforeCommissionRate,jdbcType=NUMERIC},
      </if>
      <if test="commissionRate != null">
        commission_rate = #{commissionRate,jdbcType=NUMERIC},
      </if>
      <if test="beforeTotalAmount != null">
        before_total_amount = #{beforeTotalAmount,jdbcType=NUMERIC},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=NUMERIC},
      </if>
      <if test="beforeSplitAmount != null">
        before_split_amount = #{beforeSplitAmount,jdbcType=NUMERIC},
      </if>
      <if test="splitAmount != null">
        split_amount = #{splitAmount,jdbcType=NUMERIC},
      </if>
      <if test="beforeRefundedAmount != null">
        before_refunded_amount = #{beforeRefundedAmount,jdbcType=NUMERIC},
      </if>
      <if test="refundedAmount != null">
        refunded_amount = #{refundedAmount,jdbcType=NUMERIC},
      </if>
      <if test="beforeSplitRate != null">
        before_split_rate = #{beforeSplitRate,jdbcType=NUMERIC},
      </if>
      <if test="splitRate != null">
        split_rate = #{splitRate,jdbcType=NUMERIC},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.order.repository.entity.StaffPayrollChangeLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update public.staff_payroll_change_log
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      ref_type = #{refType,jdbcType=VARCHAR},
      ref_id = #{refId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      staff_id = #{staffId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      amount_type = #{amountType,jdbcType=VARCHAR},
      before_commission_amount = #{beforeCommissionAmount,jdbcType=NUMERIC},
      commission_amount = #{commissionAmount,jdbcType=NUMERIC},
      before_commission_rate = #{beforeCommissionRate,jdbcType=NUMERIC},
      commission_rate = #{commissionRate,jdbcType=NUMERIC},
      before_total_amount = #{beforeTotalAmount,jdbcType=NUMERIC},
      total_amount = #{totalAmount,jdbcType=NUMERIC},
      before_split_amount = #{beforeSplitAmount,jdbcType=NUMERIC},
      split_amount = #{splitAmount,jdbcType=NUMERIC},
      before_refunded_amount = #{beforeRefundedAmount,jdbcType=NUMERIC},
      refunded_amount = #{refundedAmount,jdbcType=NUMERIC},
      before_split_rate = #{beforeSplitRate,jdbcType=NUMERIC},
      split_rate = #{splitRate,jdbcType=NUMERIC},
      extra = #{extra,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <insert id="batchInsert" parameterType="java.util.List">
    insert into public.staff_payroll_change_log (created_time, updated_time, ref_type,
    ref_id, order_id, staff_id,
    business_id, amount_type, before_commission_amount,
    commission_amount, before_commission_rate,
    commission_rate, before_total_amount, total_amount,
    before_split_amount, split_amount, before_refunded_amount,
    refunded_amount, before_split_rate, split_rate,
    extra)
    values
    <foreach collection="list" item="item" separator=",">
      (
      #{item.createdTime,jdbcType=TIMESTAMP},
      #{item.updatedTime,jdbcType=TIMESTAMP},
      #{item.refType,jdbcType=VARCHAR},
      #{item.refId,jdbcType=BIGINT},
      #{item.orderId,jdbcType=BIGINT},
      #{item.staffId,jdbcType=BIGINT},
      #{item.businessId,jdbcType=BIGINT},
      #{item.amountType,jdbcType=VARCHAR},
      #{item.beforeCommissionAmount,jdbcType=NUMERIC},
      #{item.commissionAmount,jdbcType=NUMERIC},
      #{item.beforeCommissionRate,jdbcType=NUMERIC},
      #{item.commissionRate,jdbcType=NUMERIC},
      #{item.beforeTotalAmount,jdbcType=NUMERIC},
      #{item.totalAmount,jdbcType=NUMERIC},
      #{item.beforeSplitAmount,jdbcType=NUMERIC},
      #{item.splitAmount,jdbcType=NUMERIC},
      #{item.beforeRefundedAmount,jdbcType=NUMERIC},
      #{item.refundedAmount,jdbcType=NUMERIC},
      #{item.beforeSplitRate,jdbcType=NUMERIC},
      #{item.splitRate,jdbcType=NUMERIC},
      #{item.extra,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

</mapper>