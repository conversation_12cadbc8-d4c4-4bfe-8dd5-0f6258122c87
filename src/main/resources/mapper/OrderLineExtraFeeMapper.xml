<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.order.repository.mapper.OrderLineExtraFeeMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.order.repository.entity.OrderLineExtraFee">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_item_id" jdbcType="BIGINT" property="orderItemId" />
    <result column="apply_type" jdbcType="VARCHAR" property="applyType" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="fee_type" jdbcType="VARCHAR" property="feeType" />
    <result column="amount" jdbcType="NUMERIC" property="amount" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="collect_type" jdbcType="VARCHAR" property="collectType" />
    <result column="apply_by" jdbcType="BIGINT" property="applyBy" />
    <result column="apply_sequence" jdbcType="INTEGER" property="applySequence" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, order_id, order_item_id, apply_type, is_deleted, fee_type, amount, 
    name, description, collect_type, apply_by, apply_sequence, create_time, update_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_line_extra_fee
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from order_line_extra_fee
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderLineExtraFee" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_line_extra_fee (business_id, order_id, order_item_id, 
      apply_type, is_deleted, fee_type, 
      amount, name, description, 
      collect_type, apply_by, apply_sequence, 
      create_time, update_time)
    values (#{businessId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{orderItemId,jdbcType=BIGINT}, 
      #{applyType,jdbcType=VARCHAR}, #{isDeleted,jdbcType=BIT}, #{feeType,jdbcType=VARCHAR}, 
      #{amount,jdbcType=NUMERIC}, #{name,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, 
      #{collectType,jdbcType=VARCHAR}, #{applyBy,jdbcType=BIGINT}, #{applySequence,jdbcType=INTEGER}, 
      #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderLineExtraFee" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_line_extra_fee
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderItemId != null">
        order_item_id,
      </if>
      <if test="applyType != null">
        apply_type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="feeType != null">
        fee_type,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="collectType != null">
        collect_type,
      </if>
      <if test="applyBy != null">
        apply_by,
      </if>
      <if test="applySequence != null">
        apply_sequence,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderItemId != null">
        #{orderItemId,jdbcType=BIGINT},
      </if>
      <if test="applyType != null">
        #{applyType,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="feeType != null">
        #{feeType,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=NUMERIC},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="collectType != null">
        #{collectType,jdbcType=VARCHAR},
      </if>
      <if test="applyBy != null">
        #{applyBy,jdbcType=BIGINT},
      </if>
      <if test="applySequence != null">
        #{applySequence,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.order.repository.entity.OrderLineExtraFee">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_line_extra_fee
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderItemId != null">
        order_item_id = #{orderItemId,jdbcType=BIGINT},
      </if>
      <if test="applyType != null">
        apply_type = #{applyType,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="feeType != null">
        fee_type = #{feeType,jdbcType=VARCHAR},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=NUMERIC},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="collectType != null">
        collect_type = #{collectType,jdbcType=VARCHAR},
      </if>
      <if test="applyBy != null">
        apply_by = #{applyBy,jdbcType=BIGINT},
      </if>
      <if test="applySequence != null">
        apply_sequence = #{applySequence,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.order.repository.entity.OrderLineExtraFee">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_line_extra_fee
    set business_id = #{businessId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      order_item_id = #{orderItemId,jdbcType=BIGINT},
      apply_type = #{applyType,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIT},
      fee_type = #{feeType,jdbcType=VARCHAR},
      amount = #{amount,jdbcType=NUMERIC},
      name = #{name,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      collect_type = #{collectType,jdbcType=VARCHAR},
      apply_by = #{applyBy,jdbcType=BIGINT},
      apply_sequence = #{applySequence,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "order_line_extra_fee"
    where order_id = #{orderId,jdbcType=BIGINT}
    and is_deleted = false
  </select>

  <insert id="batchInsertRecords" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderLineExtraFee" useGeneratedKeys="true">
    insert into "order_line_extra_fee" (business_id, order_id, order_item_id, apply_type,
    is_deleted, fee_type, amount, name, description, collect_type, apply_by,
    apply_sequence, create_time, update_time)
    values
    <foreach collection="records" index="index" item="record" separator=",">
      (#{record.businessId}, #{record.orderId}, #{record.orderItemId}, #{record.applyType},
      #{record.isDeleted}, #{record.feeType}, #{record.amount},
      #{record.name}, #{record.description}, #{record.collectType}, #{record.applyBy},
      #{record.applySequence},
      <choose>
        <when test="record.createTime != null">
          #{record.createTime,jdbcType=TIMESTAMP},
        </when>
        <otherwise>
          NOW(),
        </otherwise>
      </choose>
      <choose>
        <when test="record.updateTime != null">
          #{record.updateTime,jdbcType=TIMESTAMP}
        </when>
        <otherwise>
          NOW()
        </otherwise>
      </choose>
      )
    </foreach>
  </insert>

</mapper>