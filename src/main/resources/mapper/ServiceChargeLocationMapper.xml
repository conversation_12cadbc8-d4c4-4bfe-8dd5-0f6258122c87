<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.order.repository.mapper.ServiceChargeLocationMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.order.repository.entity.ServiceChargeLocation">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="service_charge_id" jdbcType="BIGINT" property="serviceChargeId" />
    <result column="price" jdbcType="NUMERIC" property="price" />
    <result column="tax_id" jdbcType="INTEGER" property="taxId" />
    <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
  </resultMap>
  <sql id="Base_Column_List">
    id, company_id, business_id, service_charge_id, price, tax_id, deleted_at, created_at, updated_at
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from "service_charge_location"
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from "service_charge_location"
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.ServiceChargeLocation" useGeneratedKeys="true">
    insert into "service_charge_location" (company_id, business_id, service_charge_id,
      price, tax_id, deleted_at, 
      created_at, updated_at)
    values (#{companyId,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, #{serviceChargeId,jdbcType=BIGINT},
      #{price,jdbcType=NUMERIC}, #{taxId,jdbcType=INTEGER}, #{deletedAt,jdbcType=TIMESTAMP}, 
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.ServiceChargeLocation" useGeneratedKeys="true">
    insert into "service_charge_location"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        company_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="serviceChargeId != null">
        service_charge_id,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="taxId != null">
        tax_id,
      </if>
      <if test="deletedAt != null">
        deleted_at,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="serviceChargeId != null">
        #{serviceChargeId,jdbcType=BIGINT},
      </if>
      <if test="price != null">
        #{price,jdbcType=NUMERIC},
      </if>
      <if test="taxId != null">
        #{taxId,jdbcType=INTEGER},
      </if>
      <if test="deletedAt != null">
        #{deletedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.order.repository.entity.ServiceChargeLocation">
    update "service_charge_location"
    <set>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="serviceChargeId != null">
        service_charge_id = #{serviceChargeId,jdbcType=BIGINT},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=NUMERIC},
      </if>
      <if test="taxId != null">
        tax_id = #{taxId,jdbcType=INTEGER},
      </if>
      <if test="deletedAt != null">
        deleted_at = #{deletedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.order.repository.entity.ServiceChargeLocation">
    update "service_charge_location"
    set company_id = #{companyId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      service_charge_id = #{serviceChargeId,jdbcType=BIGINT},
      price = #{price,jdbcType=NUMERIC},
      tax_id = #{taxId,jdbcType=INTEGER},
      deleted_at = #{deletedAt,jdbcType=TIMESTAMP},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByCidAndServiceChargeIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from "service_charge_location"
    where company_id = #{companyId,jdbcType=BIGINT}
    <if test="serviceChargeIds != null">
      and service_charge_id in
      <foreach collection="serviceChargeIds" open="(" close=")" item="item" separator=",">
        #{item}
      </foreach>
    </if>
    and deleted_at is null
  </select>
  <select id="selectAllWithDeletedByServiceChargeId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from "service_charge_location"
    where company_id = #{companyId,jdbcType=BIGINT}
    and service_charge_id = #{serviceChargeId,jdbcType=BIGINT}
  </select>
  <select id="selectOneWithDeletedByServiceChargeId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from "service_charge_location"
    where company_id = #{companyId,jdbcType=BIGINT}
    and business_id = #{businessId,jdbcType=BIGINT}
    and service_charge_id = #{serviceChargeId,jdbcType=BIGINT}
  </select>
  <update id="updateSetDeletedByUniqueIndex">
    update "service_charge_location"
    set deleted_at = now()
    where company_id = #{companyId,jdbcType=BIGINT}
      and business_id = #{businessId,jdbcType=BIGINT}
      and service_charge_id = #{serviceChargeId,jdbcType=BIGINT}
  </update>
</mapper>