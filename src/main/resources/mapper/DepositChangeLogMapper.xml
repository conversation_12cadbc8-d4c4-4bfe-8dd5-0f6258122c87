<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.order.repository.mapper.DepositChangeLogMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.order.repository.entity.DepositChangeLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="deposit_order_id" jdbcType="BIGINT" property="depositOrderId" />
    <result column="change_type" jdbcType="VARCHAR" property="changeType" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="dest_order_id" jdbcType="BIGINT" property="destOrderId" />
    <result column="changed_amount" jdbcType="NUMERIC" property="changedAmount" />
    <result column="balance" jdbcType="NUMERIC" property="balance" />
    <result column="currency_code" jdbcType="VARCHAR" property="currencyCode" />
    <result column="previous_log_id" jdbcType="BIGINT" property="previousLogId" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, deposit_order_id, change_type, reason, dest_order_id, changed_amount, balance, 
    currency_code, previous_log_id, company_id, business_id, customer_id, create_time, 
    update_time, staff_id
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from deposit_change_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from deposit_change_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.DepositChangeLog" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into deposit_change_log (deposit_order_id, change_type, reason, 
      dest_order_id, changed_amount, balance, 
      currency_code, previous_log_id, company_id, 
      business_id, customer_id, create_time, 
      update_time, staff_id)
    values (#{depositOrderId,jdbcType=BIGINT}, #{changeType,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, 
      #{destOrderId,jdbcType=BIGINT}, #{changedAmount,jdbcType=NUMERIC}, #{balance,jdbcType=NUMERIC}, 
      #{currencyCode,jdbcType=VARCHAR}, #{previousLogId,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}, 
      #{businessId,jdbcType=BIGINT}, #{customerId,jdbcType=BIGINT}, #{createTime,jdbcType=TIMESTAMP}, 
      #{updateTime,jdbcType=TIMESTAMP}, #{staffId,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.DepositChangeLog" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into deposit_change_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="depositOrderId != null">
        deposit_order_id,
      </if>
      <if test="changeType != null">
        change_type,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="destOrderId != null">
        dest_order_id,
      </if>
      <if test="changedAmount != null">
        changed_amount,
      </if>
      <if test="balance != null">
        balance,
      </if>
      <if test="currencyCode != null">
        currency_code,
      </if>
      <if test="previousLogId != null">
        previous_log_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="depositOrderId != null">
        #{depositOrderId,jdbcType=BIGINT},
      </if>
      <if test="changeType != null">
        #{changeType,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="destOrderId != null">
        #{destOrderId,jdbcType=BIGINT},
      </if>
      <if test="changedAmount != null">
        #{changedAmount,jdbcType=NUMERIC},
      </if>
      <if test="balance != null">
        #{balance,jdbcType=NUMERIC},
      </if>
      <if test="currencyCode != null">
        #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="previousLogId != null">
        #{previousLogId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.order.repository.entity.DepositChangeLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update deposit_change_log
    <set>
      <if test="depositOrderId != null">
        deposit_order_id = #{depositOrderId,jdbcType=BIGINT},
      </if>
      <if test="changeType != null">
        change_type = #{changeType,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="destOrderId != null">
        dest_order_id = #{destOrderId,jdbcType=BIGINT},
      </if>
      <if test="changedAmount != null">
        changed_amount = #{changedAmount,jdbcType=NUMERIC},
      </if>
      <if test="balance != null">
        balance = #{balance,jdbcType=NUMERIC},
      </if>
      <if test="currencyCode != null">
        currency_code = #{currencyCode,jdbcType=VARCHAR},
      </if>
      <if test="previousLogId != null">
        previous_log_id = #{previousLogId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.order.repository.entity.DepositChangeLog">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update deposit_change_log
    set deposit_order_id = #{depositOrderId,jdbcType=BIGINT},
      change_type = #{changeType,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      dest_order_id = #{destOrderId,jdbcType=BIGINT},
      changed_amount = #{changedAmount,jdbcType=NUMERIC},
      balance = #{balance,jdbcType=NUMERIC},
      currency_code = #{currencyCode,jdbcType=VARCHAR},
      previous_log_id = #{previousLogId,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      customer_id = #{customerId,jdbcType=BIGINT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      staff_id = #{staffId,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectLatestByDepositOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from deposit_change_log
    where deposit_order_id = #{depositOrderId,jdbcType=BIGINT}
    order by create_time desc limit 1
  </select>
</mapper>