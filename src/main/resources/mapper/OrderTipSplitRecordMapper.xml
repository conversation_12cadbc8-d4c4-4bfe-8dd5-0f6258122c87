<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.order.repository.mapper.OrderTipSplitRecordMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.order.repository.entity.OrderTipSplitRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="split_method" jdbcType="SMALLINT" property="splitMethod" />
    <result column="customized_type" jdbcType="SMALLINT" property="customizedType" />
    <result column="customized_config" jdbcType="VARCHAR" property="customizedConfig" />
    <result column="apply_by" jdbcType="BIGINT" property="applyBy" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="business_tip_amount" jdbcType="NUMERIC" property="businessTipAmount" />
    <result column="is_business_tip_amount_effective" jdbcType="BIT" property="isBusinessTipAmountEffective" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, order_id, split_method, customized_type, customized_config, apply_by,
    is_deleted, create_time, update_time, business_tip_amount, is_business_tip_amount_effective
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select
    <include refid="Base_Column_List" />
    from order_tip_split_record
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from order_tip_split_record
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderTipSplitRecord" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_tip_split_record (business_id, order_id, split_method,
      customized_type, customized_config, apply_by,
      is_deleted, create_time, update_time,
      business_tip_amount, is_business_tip_amount_effective
      )
    values (#{businessId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{splitMethod,jdbcType=SMALLINT},
      #{customizedType,jdbcType=SMALLINT}, #{customizedConfig,jdbcType=VARCHAR}, #{applyBy,jdbcType=BIGINT},
      #{isDeleted,jdbcType=BIT}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP},
      #{businessTipAmount,jdbcType=NUMERIC}, #{isBusinessTipAmountEffective,jdbcType=BIT}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderTipSplitRecord" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_tip_split_record
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="splitMethod != null">
        split_method,
      </if>
      <if test="customizedType != null">
        customized_type,
      </if>
      <if test="customizedConfig != null">
        customized_config,
      </if>
      <if test="applyBy != null">
        apply_by,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="businessTipAmount != null">
        business_tip_amount,
      </if>
      <if test="isBusinessTipAmountEffective != null">
        is_business_tip_amount_effective,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="splitMethod != null">
        #{splitMethod,jdbcType=SMALLINT},
      </if>
      <if test="customizedType != null">
        #{customizedType,jdbcType=SMALLINT},
      </if>
      <if test="customizedConfig != null">
        #{customizedConfig,jdbcType=VARCHAR},
      </if>
      <if test="applyBy != null">
        #{applyBy,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessTipAmount != null">
        #{businessTipAmount,jdbcType=NUMERIC},
      </if>
      <if test="isBusinessTipAmountEffective != null">
        #{isBusinessTipAmountEffective,jdbcType=BIT},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.order.repository.entity.OrderTipSplitRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_tip_split_record
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="splitMethod != null">
        split_method = #{splitMethod,jdbcType=SMALLINT},
      </if>
      <if test="customizedType != null">
        customized_type = #{customizedType,jdbcType=SMALLINT},
      </if>
      <if test="customizedConfig != null">
        customized_config = #{customizedConfig,jdbcType=VARCHAR},
      </if>
      <if test="applyBy != null">
        apply_by = #{applyBy,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessTipAmount != null">
        business_tip_amount = #{businessTipAmount,jdbcType=NUMERIC},
      </if>
      <if test="isBusinessTipAmountEffective != null">
        is_business_tip_amount_effective = #{isBusinessTipAmountEffective,jdbcType=BIT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.order.repository.entity.OrderTipSplitRecord">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_tip_split_record
    set business_id = #{businessId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      split_method = #{splitMethod,jdbcType=SMALLINT},
      customized_type = #{customizedType,jdbcType=SMALLINT},
      customized_config = #{customizedConfig,jdbcType=VARCHAR},
      apply_by = #{applyBy,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=BIT},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      business_tip_amount = #{businessTipAmount,jdbcType=NUMERIC},
      is_business_tip_amount_effective = #{isBusinessTipAmountEffective,jdbcType=BIT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByBusinessIdAndOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "order_tip_split_record"
    where business_id = #{businessId} and order_id = #{orderId} and is_deleted = false
  </select>

  <select id="selectByBusinessIdsAndOrderIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "order_tip_split_record"
    <where>
      <if test="businessIds != null and !businessIds.isEmpty()">
        and business_id in
        <foreach close=")" collection="businessIds" item="businessId" open="(" separator=",">
          #{businessId}
        </foreach>
      </if>
      <if test="orderIds != null and !orderIds.isEmpty()">
        and order_id in
        <foreach close=")" collection="orderIds" item="orderId" open="(" separator=",">
          #{orderId}
        </foreach>
      </if>
      and is_deleted = false
    </where>
  </select>

  <update id="updateByBusinessIdAndOrderId" parameterType="com.moego.svc.order.repository.entity.OrderTipSplitRecord">
    update "order_tip_split_record"
    <set>
      <if test="splitMethod != null">
        split_method = #{splitMethod,jdbcType=SMALLINT},
      </if>
      <if test="customizedType != null">
        customized_type = #{customizedType,jdbcType=SMALLINT},
      </if>
      <if test="customizedConfig != null">
        customized_config = #{customizedConfig,jdbcType=VARCHAR},
      </if>
      <if test="applyBy != null">
        apply_by = #{applyBy,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BOOLEAN},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="businessTipAmount != null">
        business_tip_amount = #{businessTipAmount,jdbcType=NUMERIC},
      </if>
      <if test="isBusinessTipAmountEffective != null">
        is_business_tip_amount_effective = #{isBusinessTipAmountEffective,jdbcType=BIT},
      </if>
    </set>
    where business_id = #{businessId} and order_id = #{orderId}
  </update>

</mapper>