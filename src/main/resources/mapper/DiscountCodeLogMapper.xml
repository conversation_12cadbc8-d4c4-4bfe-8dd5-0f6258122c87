<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.marketing.mapper.DiscountCodeLogMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.marketing.entity.DiscountCodeLog">
    <!--@mbg.generated-->
    <!--@Table discount_code_log-->
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="business_id" jdbcType="BIGINT" property="businessId"/>
    <result column="client_id" jdbcType="BIGINT" property="clientId"/>
    <result column="code_id" jdbcType="BIGINT" property="codeId"/>
    <result column="pet_ids" jdbcType="VARCHAR" property="petIds"/>
    <result column="redeem_type" jdbcType="SMALLINT" property="redeemType"/>
    <result column="redeem_time" jdbcType="TIMESTAMP" property="redeemTime"/>
    <result column="discount_sales" jdbcType="NUMERIC" property="discountSales"/>
    <result column="redeem_id" jdbcType="BIGINT" property="redeemId"/>
    <result column="redeem_by" jdbcType="BIGINT" property="redeemBy"/>
    <result column="company_id" jdbcType="BIGINT" property="companyId"/>
    <result column="order_id" jdbcType="BIGINT" property="orderId"/>
    <result column="invoice_sales" jdbcType="NUMERIC" property="invoiceSales"/>
  </resultMap>
  <sql id="Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <!--@mbg.generated-->
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    <!--@mbg.generated-->
    id,
    business_id,
    client_id,
    code_id,
    pet_ids,
    redeem_type,
    redeem_time,
    discount_sales,
    redeem_id,
    redeem_by,
    company_id,
    order_id,
    invoice_sales
  </sql>
  <select id="selectByExample" parameterType="com.moego.svc.marketing.entity.DiscountCodeLogExample"
          resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List"/>
    from discount_code_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByCompanyIdWithPage" parameterType="com.moego.svc.marketing.entity.DiscountCodeLogExample"
          resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from discount_code_log
    where
        company_id = #{companyId}
    and client_id  in
    <foreach collection="clientIds" item="clientId" open="(" close=")" separator=",">
        #{clientId}
    </foreach>
    limit #{limit} offset #{offset}
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--@mbg.generated-->
    select
    <include refid="Base_Column_List"/>
    from discount_code_log
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--@mbg.generated-->
    delete
    from discount_code_log
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.moego.svc.marketing.entity.DiscountCodeLogExample">
    <!--@mbg.generated-->
    delete
    from discount_code_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.marketing.entity.DiscountCodeLog"
          useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into discount_code_log (business_id, client_id, code_id,
                                   pet_ids, redeem_type, redeem_time,
                                   discount_sales, redeem_id, redeem_by,
                                   company_id, order_id, invoice_sales)
    values (#{businessId,jdbcType=BIGINT}, #{clientId,jdbcType=BIGINT}, #{codeId,jdbcType=BIGINT},
            #{petIds,jdbcType=VARCHAR}, #{redeemType,jdbcType=SMALLINT}, #{redeemTime,jdbcType=TIMESTAMP},
            #{discountSales,jdbcType=NUMERIC}, #{redeemId,jdbcType=BIGINT}, #{redeemBy,jdbcType=BIGINT},
            #{companyId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{invoiceSales,jdbcType=NUMERIC})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id"
          parameterType="com.moego.svc.marketing.entity.DiscountCodeLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into discount_code_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="clientId != null">
        client_id,
      </if>
      <if test="codeId != null">
        code_id,
      </if>
      <if test="petIds != null and petIds != ''">
        pet_ids,
      </if>
      <if test="redeemType != null">
        redeem_type,
      </if>
      <if test="redeemTime != null">
        redeem_time,
      </if>
      <if test="discountSales != null">
        discount_sales,
      </if>
      <if test="redeemId != null">
        redeem_id,
      </if>
      <if test="redeemBy != null">
        redeem_by,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="invoiceSales != null">
        invoice_sales,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="clientId != null">
        #{clientId,jdbcType=BIGINT},
      </if>
      <if test="codeId != null">
        #{codeId,jdbcType=BIGINT},
      </if>
      <if test="petIds != null and petIds != ''">
        #{petIds,jdbcType=VARCHAR},
      </if>
      <if test="redeemType != null">
        #{redeemType,jdbcType=SMALLINT},
      </if>
      <if test="redeemTime != null">
        #{redeemTime,jdbcType=TIMESTAMP},
      </if>
      <if test="discountSales != null">
        #{discountSales,jdbcType=NUMERIC},
      </if>
      <if test="redeemId != null">
        #{redeemId,jdbcType=BIGINT},
      </if>
      <if test="redeemBy != null">
        #{redeemBy,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="invoiceSales != null">
        #{invoiceSales,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.moego.svc.marketing.entity.DiscountCodeLogExample"
          resultType="java.lang.Long">
    <!--@mbg.generated-->
    select count(*)
    from discount_code_log
    <if test="_parameter != null">
      <include refid="Example_Where_Clause"/>
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    <!--@mbg.generated-->
    update discount_code_log
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.businessId != null">
        business_id = #{record.businessId,jdbcType=BIGINT},
      </if>
      <if test="record.clientId != null">
        client_id = #{record.clientId,jdbcType=BIGINT},
      </if>
      <if test="record.codeId != null">
        code_id = #{record.codeId,jdbcType=BIGINT},
      </if>
      <if test="record.petIds != null">
        pet_ids = #{record.petIds,jdbcType=VARCHAR},
      </if>
      <if test="record.redeemType != null">
        redeem_type = #{record.redeemType,jdbcType=SMALLINT},
      </if>
      <if test="record.redeemTime != null">
        redeem_time = #{record.redeemTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.discountSales != null">
        discount_sales = #{record.discountSales,jdbcType=NUMERIC},
      </if>
      <if test="record.redeemId != null">
        redeem_id = #{record.redeemId,jdbcType=BIGINT},
      </if>
      <if test="record.redeemBy != null">
        redeem_by = #{record.redeemBy,jdbcType=BIGINT},
      </if>
      <if test="record.companyId != null">
        company_id = #{record.companyId,jdbcType=BIGINT},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=BIGINT},
      </if>
      <if test="record.invoiceSales != null">
        invoice_sales = #{record.invoiceSales,jdbcType=NUMERIC},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    <!--@mbg.generated-->
    update discount_code_log
    set id             = #{record.id,jdbcType=BIGINT},
        business_id    = #{record.businessId,jdbcType=BIGINT},
        client_id      = #{record.clientId,jdbcType=BIGINT},
        code_id        = #{record.codeId,jdbcType=BIGINT},
        pet_ids        = #{record.petIds,jdbcType=VARCHAR},
        redeem_type    = #{record.redeemType,jdbcType=SMALLINT},
        redeem_time    = #{record.redeemTime,jdbcType=TIMESTAMP},
        discount_sales = #{record.discountSales,jdbcType=NUMERIC},
        redeem_id      = #{record.redeemId,jdbcType=BIGINT},
        redeem_by      = #{record.redeemBy,jdbcType=BIGINT},
        company_id     = #{record.companyId,jdbcType=BIGINT},
        order_id       = #{record.orderId,jdbcType=BIGINT},
        invoice_sales  = #{record.invoiceSales,jdbcType=NUMERIC}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause"/>
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.marketing.entity.DiscountCodeLog">
    <!--@mbg.generated-->
    update discount_code_log
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="clientId != null">
        client_id = #{clientId,jdbcType=BIGINT},
      </if>
      <if test="codeId != null">
        code_id = #{codeId,jdbcType=BIGINT},
      </if>
      <if test="petIds != null and petIds != ''">
        pet_ids = #{petIds,jdbcType=VARCHAR},
      </if>
      <if test="redeemType != null">
        redeem_type = #{redeemType,jdbcType=SMALLINT},
      </if>
      <if test="redeemTime != null">
        redeem_time = #{redeemTime,jdbcType=TIMESTAMP},
      </if>
      <if test="discountSales != null">
        discount_sales = #{discountSales,jdbcType=NUMERIC},
      </if>
      <if test="redeemId != null">
        redeem_id = #{redeemId,jdbcType=BIGINT},
      </if>
      <if test="redeemBy != null">
        redeem_by = #{redeemBy,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="invoiceSales != null">
        invoice_sales = #{invoiceSales,jdbcType=NUMERIC},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.marketing.entity.DiscountCodeLog">
    <!--@mbg.generated-->
    update discount_code_log
    set business_id    = #{businessId,jdbcType=BIGINT},
        client_id      = #{clientId,jdbcType=BIGINT},
        code_id        = #{codeId,jdbcType=BIGINT},
        pet_ids        = #{petIds,jdbcType=VARCHAR},
        redeem_type    = #{redeemType,jdbcType=SMALLINT},
        redeem_time    = #{redeemTime,jdbcType=TIMESTAMP},
        discount_sales = #{discountSales,jdbcType=NUMERIC},
        redeem_id      = #{redeemId,jdbcType=BIGINT},
        redeem_by      = #{redeemBy,jdbcType=BIGINT},
        company_id     = #{companyId,jdbcType=BIGINT},
        order_id       = #{orderId,jdbcType=BIGINT},
        invoice_sales  = #{invoiceSales,jdbcType=NUMERIC}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertOrUpdate" keyColumn="id" keyProperty="id"
          parameterType="com.moego.svc.marketing.entity.DiscountCodeLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into discount_code_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      business_id,
      client_id,
      code_id,
      pet_ids,
      redeem_type,
      redeem_time,
      discount_sales,
      redeem_id,
      redeem_by,
      company_id,
      order_id,
      invoice_sales,
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      #{businessId,jdbcType=BIGINT},
      #{clientId,jdbcType=BIGINT},
      #{codeId,jdbcType=BIGINT},
      #{petIds,jdbcType=VARCHAR},
      #{redeemType,jdbcType=SMALLINT},
      #{redeemTime,jdbcType=TIMESTAMP},
      #{discountSales,jdbcType=NUMERIC},
      #{redeemId,jdbcType=BIGINT},
      #{redeemBy,jdbcType=BIGINT},
      #{companyId,jdbcType=BIGINT},
      #{orderId,jdbcType=BIGINT},
      #{invoiceSales,jdbcType=NUMERIC},
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      business_id = #{businessId,jdbcType=BIGINT}, client_id = #{clientId,jdbcType=BIGINT}, code_id = #{codeId,jdbcType=BIGINT}, pet_ids = #{petIds,jdbcType=VARCHAR}, redeem_type = #{redeemType,jdbcType=SMALLINT}, redeem_time = #{redeemTime,jdbcType=TIMESTAMP}, discount_sales = #{discountSales,jdbcType=NUMERIC}, redeem_id = #{redeemId,jdbcType=BIGINT}, redeem_by = #{redeemBy,jdbcType=BIGINT}, company_id = #{companyId,jdbcType=BIGINT}, order_id = #{orderId,jdbcType=BIGINT}, invoice_sales = #{invoiceSales,jdbcType=NUMERIC},
    </trim>
  </insert>
  <insert id="insertOrUpdateSelective" keyColumn="id" keyProperty="id"
          parameterType="com.moego.svc.marketing.entity.DiscountCodeLog" useGeneratedKeys="true">
    <!--@mbg.generated-->
    insert into discount_code_log
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="clientId != null">
        client_id,
      </if>
      <if test="codeId != null">
        code_id,
      </if>
      <if test="petIds != null and petIds != ''">
        pet_ids,
      </if>
      <if test="redeemType != null">
        redeem_type,
      </if>
      <if test="redeemTime != null">
        redeem_time,
      </if>
      <if test="discountSales != null">
        discount_sales,
      </if>
      <if test="redeemId != null">
        redeem_id,
      </if>
      <if test="redeemBy != null">
        redeem_by,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="invoiceSales != null">
        invoice_sales,
      </if>
    </trim>
    values
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="clientId != null">
        #{clientId,jdbcType=BIGINT},
      </if>
      <if test="codeId != null">
        #{codeId,jdbcType=BIGINT},
      </if>
      <if test="petIds != null and petIds != ''">
        #{petIds,jdbcType=VARCHAR},
      </if>
      <if test="redeemType != null">
        #{redeemType,jdbcType=SMALLINT},
      </if>
      <if test="redeemTime != null">
        #{redeemTime,jdbcType=TIMESTAMP},
      </if>
      <if test="discountSales != null">
        #{discountSales,jdbcType=NUMERIC},
      </if>
      <if test="redeemId != null">
        #{redeemId,jdbcType=BIGINT},
      </if>
      <if test="redeemBy != null">
        #{redeemBy,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="invoiceSales != null">
        #{invoiceSales,jdbcType=NUMERIC},
      </if>
    </trim>
    on duplicate key update
    <trim suffixOverrides=",">
      <if test="id != null">
        id = #{id,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="clientId != null">
        client_id = #{clientId,jdbcType=BIGINT},
      </if>
      <if test="codeId != null">
        code_id = #{codeId,jdbcType=BIGINT},
      </if>
      <if test="petIds != null and petIds != ''">
        pet_ids = #{petIds,jdbcType=VARCHAR},
      </if>
      <if test="redeemType != null">
        redeem_type = #{redeemType,jdbcType=SMALLINT},
      </if>
      <if test="redeemTime != null">
        redeem_time = #{redeemTime,jdbcType=TIMESTAMP},
      </if>
      <if test="discountSales != null">
        discount_sales = #{discountSales,jdbcType=NUMERIC},
      </if>
      <if test="redeemId != null">
        redeem_id = #{redeemId,jdbcType=BIGINT},
      </if>
      <if test="redeemBy != null">
        redeem_by = #{redeemBy,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="invoiceSales != null">
        invoice_sales = #{invoiceSales,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>

  <resultMap id="DiscountSalesResult" type="com.moego.svc.marketing.dto.DiscountSalesResult">
    <result column="totalUsage" property="totalUsage"/>
    <result column="totalClients" property="totalClients"/>
    <result column="discountSales" property="discountSales"/>
    <result column="invoiceSales" property="invoiceSales"/>
  </resultMap>

  <select id="countByDiscountIdAndBusinessId" resultMap="DiscountSalesResult">
    select count(*)                         totalUsage,
           count(distinct client_id)        totalClients,
           coalesce(sum(discount_sales), 0) discountSales,
           coalesce(sum(invoice_sales), 0)  invoiceSales
    from discount_code_log
    where code_id = #{discountId,jdbcType=BIGINT}
      and business_id = #{businessId,jdbcType=BIGINT}
  </select>

  <select id="countByDiscountIdAndCompanyId" resultMap="DiscountSalesResult">
    select count(*)                         totalUsage,
           count(distinct client_id)        totalClients,
           coalesce(sum(discount_sales), 0) discountSales,
           coalesce(sum(invoice_sales), 0)  invoiceSales
    from discount_code_log
    where code_id = #{discountId,jdbcType=BIGINT}
      and company_id = #{companyId,jdbcType=BIGINT}
  </select>

  <insert id="batchInsert">
    insert into discount_code_log
    (business_id, code_id, client_id, pet_ids, redeem_type, redeem_id,
     discount_sales, invoice_sales, redeem_by, order_id, company_id)
    values
    <foreach collection="list" index="index" item="item" separator=",">
      (#{item.businessId,jdbcType=BIGINT}, #{item.codeId,jdbcType=BIGINT},
       #{item.clientId,jdbcType=BIGINT}, #{item.petIds,jdbcType=VARCHAR}, #{item.redeemType,jdbcType=SMALLINT},
       #{item.redeemId,jdbcType=BIGINT}, #{item.discountSales,jdbcType=NUMERIC}, #{item.invoiceSales,jdbcType=NUMERIC},
       #{item.redeemBy,jdbcType=BIGINT}, #{item.orderId,jdbcType=BIGINT}, #{item.companyId,jdbcType=BIGINT})
    </foreach>
  </insert>

  <select id="selectByRedeemId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from discount_code_log
    where redeem_id = #{redeemId,jdbcType=BIGINT}
  </select>

  <select id="selectByOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List"/>
    from discount_code_log
    where order_id = #{orderId,jdbcType=BIGINT}
  </select>
</mapper>
