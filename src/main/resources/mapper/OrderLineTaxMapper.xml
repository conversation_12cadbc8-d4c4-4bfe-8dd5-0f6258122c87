<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.order.repository.mapper.OrderLineTaxMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.order.repository.entity.OrderLineTax">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="order_item_id" jdbcType="BIGINT" property="orderItemId" />
    <result column="apply_type" jdbcType="VARCHAR" property="applyType" />
    <result column="is_deleted" jdbcType="BIT" property="isDeleted" />
    <result column="tax_id" jdbcType="BIGINT" property="taxId" />
    <result column="tax_rate" jdbcType="NUMERIC" property="taxRate" />
    <result column="tax_amount" jdbcType="NUMERIC" property="taxAmount" />
    <result column="apply_by" jdbcType="BIGINT" property="applyBy" />
    <result column="apply_sequence" jdbcType="INTEGER" property="applySequence" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="tax_name" jdbcType="VARCHAR" property="taxName" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, business_id, order_id, order_item_id, apply_type, is_deleted, tax_id, tax_rate, 
    tax_amount, apply_by, apply_sequence, create_time, update_time, tax_name
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from order_line_tax
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from order_line_tax
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderLineTax" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_line_tax (business_id, order_id, order_item_id, 
      apply_type, is_deleted, tax_id, 
      tax_rate, tax_amount, apply_by, 
      apply_sequence, create_time, update_time, 
      tax_name)
    values (#{businessId,jdbcType=BIGINT}, #{orderId,jdbcType=BIGINT}, #{orderItemId,jdbcType=BIGINT}, 
      #{applyType,jdbcType=VARCHAR}, #{isDeleted,jdbcType=BIT}, #{taxId,jdbcType=BIGINT}, 
      #{taxRate,jdbcType=NUMERIC}, #{taxAmount,jdbcType=NUMERIC}, #{applyBy,jdbcType=BIGINT}, 
      #{applySequence,jdbcType=INTEGER}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{taxName,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderLineTax" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into order_line_tax
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        business_id,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderItemId != null">
        order_item_id,
      </if>
      <if test="applyType != null">
        apply_type,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="taxId != null">
        tax_id,
      </if>
      <if test="taxRate != null">
        tax_rate,
      </if>
      <if test="taxAmount != null">
        tax_amount,
      </if>
      <if test="applyBy != null">
        apply_by,
      </if>
      <if test="applySequence != null">
        apply_sequence,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="taxName != null">
        tax_name,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderItemId != null">
        #{orderItemId,jdbcType=BIGINT},
      </if>
      <if test="applyType != null">
        #{applyType,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=BIT},
      </if>
      <if test="taxId != null">
        #{taxId,jdbcType=BIGINT},
      </if>
      <if test="taxRate != null">
        #{taxRate,jdbcType=NUMERIC},
      </if>
      <if test="taxAmount != null">
        #{taxAmount,jdbcType=NUMERIC},
      </if>
      <if test="applyBy != null">
        #{applyBy,jdbcType=BIGINT},
      </if>
      <if test="applySequence != null">
        #{applySequence,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxName != null">
        #{taxName,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.order.repository.entity.OrderLineTax">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_line_tax
    <set>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="orderItemId != null">
        order_item_id = #{orderItemId,jdbcType=BIGINT},
      </if>
      <if test="applyType != null">
        apply_type = #{applyType,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=BIT},
      </if>
      <if test="taxId != null">
        tax_id = #{taxId,jdbcType=BIGINT},
      </if>
      <if test="taxRate != null">
        tax_rate = #{taxRate,jdbcType=NUMERIC},
      </if>
      <if test="taxAmount != null">
        tax_amount = #{taxAmount,jdbcType=NUMERIC},
      </if>
      <if test="applyBy != null">
        apply_by = #{applyBy,jdbcType=BIGINT},
      </if>
      <if test="applySequence != null">
        apply_sequence = #{applySequence,jdbcType=INTEGER},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="taxName != null">
        tax_name = #{taxName,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.order.repository.entity.OrderLineTax">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update order_line_tax
    set business_id = #{businessId,jdbcType=BIGINT},
      order_id = #{orderId,jdbcType=BIGINT},
      order_item_id = #{orderItemId,jdbcType=BIGINT},
      apply_type = #{applyType,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=BIT},
      tax_id = #{taxId,jdbcType=BIGINT},
      tax_rate = #{taxRate,jdbcType=NUMERIC},
      tax_amount = #{taxAmount,jdbcType=NUMERIC},
      apply_by = #{applyBy,jdbcType=BIGINT},
      apply_sequence = #{applySequence,jdbcType=INTEGER},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      tax_name = #{taxName,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByOrderId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "order_line_tax"
    where order_id = #{orderId,jdbcType=BIGINT}
    and is_deleted = false
  </select>

  <select id="selectByOrderItemIds" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "order_line_tax"
    where order_item_id in
    <foreach close=")" collection="orderItemIds" item="item" open="(" separator=",">
      #{item}
    </foreach>
    and is_deleted = false
  </select>

  <insert id="batchInsertRecords" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderLineTax" useGeneratedKeys="true">
    insert into "order_line_tax" (business_id, order_id, order_item_id, apply_type,
    is_deleted, tax_id, tax_rate, tax_amount,tax_name,apply_by, apply_sequence, create_time, update_time)
    values
    <foreach collection="records" index="index" item="record" separator=",">
      (#{record.businessId}, #{record.orderId}, #{record.orderItemId}, #{record.applyType},
      #{record.isDeleted}, #{record.taxId}, #{record.taxRate},
      #{record.taxAmount},#{record.taxName}, #{record.applyBy}, #{record.applySequence},
      <choose>
        <when test="record.createTime != null">
          #{record.createTime,jdbcType=TIMESTAMP},
        </when>
        <otherwise>
          NOW(),
        </otherwise>
      </choose>
      <choose>
        <when test="record.updateTime != null">
          #{record.updateTime,jdbcType=TIMESTAMP}
        </when>
        <otherwise>
          NOW()
        </otherwise>
      </choose>
      )
    </foreach>
  </insert>

</mapper>