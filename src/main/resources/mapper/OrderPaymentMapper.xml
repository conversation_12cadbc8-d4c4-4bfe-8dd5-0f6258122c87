<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.order.repository.mapper.OrderPaymentMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.order.repository.entity.OrderPayment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="order_id" jdbcType="BIGINT" property="orderId" />
    <result column="company_id" jdbcType="BIGINT" property="companyId" />
    <result column="business_id" jdbcType="BIGINT" property="businessId" />
    <result column="staff_id" jdbcType="BIGINT" property="staffId" />
    <result column="customer_id" jdbcType="BIGINT" property="customerId" />
    <result column="payment_method_id" jdbcType="BIGINT" property="paymentMethodId" />
    <result column="payment_method_extra" jdbcType="OTHER" property="paymentMethodExtra" typeHandler="com.moego.svc.order.utils.StringToJsonbTypeHandler" />
    <result column="payment_method_vendor" jdbcType="VARCHAR" property="paymentMethodVendor" />
    <result column="is_online" jdbcType="BIT" property="isOnline" />
    <result column="is_deposit" jdbcType="BIT" property="isDeposit" />
    <result column="paid_by" jdbcType="VARCHAR" property="paidBy" />
    <result column="currency" jdbcType="VARCHAR" property="currency" />
    <result column="total_amount" jdbcType="NUMERIC" property="totalAmount" />
    <result column="amount" jdbcType="NUMERIC" property="amount" />
    <result column="refunded_amount" jdbcType="NUMERIC" property="refundedAmount" />
    <result column="processing_fee" jdbcType="NUMERIC" property="processingFee" />
    <result column="payment_tips" jdbcType="NUMERIC" property="paymentTips" />
    <result column="payment_tips_before_create" jdbcType="NUMERIC" property="paymentTipsBeforeCreate" />
    <result column="payment_tips_after_create" jdbcType="NUMERIC" property="paymentTipsAfterCreate" />
    <result column="payment_status" jdbcType="VARCHAR" property="paymentStatus" />
    <result column="reason" jdbcType="VARCHAR" property="reason" />
    <result column="pay_time" jdbcType="TIMESTAMP" property="payTime" />
    <result column="cancel_time" jdbcType="TIMESTAMP" property="cancelTime" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="payment_method" jdbcType="VARCHAR" property="paymentMethod" />
    <result column="convenience_fee" jdbcType="NUMERIC" property="convenienceFee" />
    <result column="fail_time" jdbcType="TIMESTAMP" property="failTime" />
    <result column="payment_id" jdbcType="BIGINT" property="paymentId" />
    <result column="refunded_convenience_fee" jdbcType="NUMERIC" property="refundedConvenienceFee" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, order_id, company_id, business_id, staff_id, customer_id, payment_method_id, 
    payment_method_extra, payment_method_vendor, is_online, is_deposit, paid_by, currency, 
    total_amount, amount, refunded_amount, processing_fee, payment_tips, payment_tips_before_create, 
    payment_tips_after_create, payment_status, reason, pay_time, cancel_time, create_time, 
    update_time, payment_method, convenience_fee, fail_time, payment_id, refunded_convenience_fee
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from public.order_payment
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from public.order_payment
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderPayment" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into public.order_payment (order_id, company_id, business_id, 
      staff_id, customer_id, payment_method_id, 
      payment_method_extra, 
      payment_method_vendor, is_online, is_deposit, 
      paid_by, currency, total_amount, 
      amount, refunded_amount, processing_fee, 
      payment_tips, payment_tips_before_create, payment_tips_after_create, 
      payment_status, reason, pay_time, 
      cancel_time, create_time, update_time, 
      payment_method, convenience_fee, fail_time, 
      payment_id, refunded_convenience_fee)
    values (#{orderId,jdbcType=BIGINT}, #{companyId,jdbcType=BIGINT}, #{businessId,jdbcType=BIGINT}, 
      #{staffId,jdbcType=BIGINT}, #{customerId,jdbcType=BIGINT}, #{paymentMethodId,jdbcType=BIGINT}, 
      #{paymentMethodExtra,jdbcType=OTHER,typeHandler=com.moego.svc.order.utils.StringToJsonbTypeHandler}, 
      #{paymentMethodVendor,jdbcType=VARCHAR}, #{isOnline,jdbcType=BIT}, #{isDeposit,jdbcType=BIT}, 
      #{paidBy,jdbcType=VARCHAR}, #{currency,jdbcType=VARCHAR}, #{totalAmount,jdbcType=NUMERIC}, 
      #{amount,jdbcType=NUMERIC}, #{refundedAmount,jdbcType=NUMERIC}, #{processingFee,jdbcType=NUMERIC}, 
      #{paymentTips,jdbcType=NUMERIC}, #{paymentTipsBeforeCreate,jdbcType=NUMERIC}, #{paymentTipsAfterCreate,jdbcType=NUMERIC}, 
      #{paymentStatus,jdbcType=VARCHAR}, #{reason,jdbcType=VARCHAR}, #{payTime,jdbcType=TIMESTAMP}, 
      #{cancelTime,jdbcType=TIMESTAMP}, #{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, 
      #{paymentMethod,jdbcType=VARCHAR}, #{convenienceFee,jdbcType=NUMERIC}, #{failTime,jdbcType=TIMESTAMP}, 
      #{paymentId,jdbcType=BIGINT}, #{refundedConvenienceFee,jdbcType=NUMERIC})
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.OrderPayment" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into public.order_payment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        order_id,
      </if>
      <if test="companyId != null">
        company_id,
      </if>
      <if test="businessId != null">
        business_id,
      </if>
      <if test="staffId != null">
        staff_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="paymentMethodId != null">
        payment_method_id,
      </if>
      <if test="paymentMethodExtra != null">
        payment_method_extra,
      </if>
      <if test="paymentMethodVendor != null">
        payment_method_vendor,
      </if>
      <if test="isOnline != null">
        is_online,
      </if>
      <if test="isDeposit != null">
        is_deposit,
      </if>
      <if test="paidBy != null">
        paid_by,
      </if>
      <if test="currency != null">
        currency,
      </if>
      <if test="totalAmount != null">
        total_amount,
      </if>
      <if test="amount != null">
        amount,
      </if>
      <if test="refundedAmount != null">
        refunded_amount,
      </if>
      <if test="processingFee != null">
        processing_fee,
      </if>
      <if test="paymentTips != null">
        payment_tips,
      </if>
      <if test="paymentTipsBeforeCreate != null">
        payment_tips_before_create,
      </if>
      <if test="paymentTipsAfterCreate != null">
        payment_tips_after_create,
      </if>
      <if test="paymentStatus != null">
        payment_status,
      </if>
      <if test="reason != null">
        reason,
      </if>
      <if test="payTime != null">
        pay_time,
      </if>
      <if test="cancelTime != null">
        cancel_time,
      </if>
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="paymentMethod != null">
        payment_method,
      </if>
      <if test="convenienceFee != null">
        convenience_fee,
      </if>
      <if test="failTime != null">
        fail_time,
      </if>
      <if test="paymentId != null">
        payment_id,
      </if>
      <if test="refundedConvenienceFee != null">
        refunded_convenience_fee,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="orderId != null">
        #{orderId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        #{businessId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        #{staffId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=BIGINT},
      </if>
      <if test="paymentMethodId != null">
        #{paymentMethodId,jdbcType=BIGINT},
      </if>
      <if test="paymentMethodExtra != null">
        #{paymentMethodExtra,jdbcType=OTHER,typeHandler=com.moego.svc.order.utils.StringToJsonbTypeHandler},
      </if>
      <if test="paymentMethodVendor != null">
        #{paymentMethodVendor,jdbcType=VARCHAR},
      </if>
      <if test="isOnline != null">
        #{isOnline,jdbcType=BIT},
      </if>
      <if test="isDeposit != null">
        #{isDeposit,jdbcType=BIT},
      </if>
      <if test="paidBy != null">
        #{paidBy,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        #{currency,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        #{totalAmount,jdbcType=NUMERIC},
      </if>
      <if test="amount != null">
        #{amount,jdbcType=NUMERIC},
      </if>
      <if test="refundedAmount != null">
        #{refundedAmount,jdbcType=NUMERIC},
      </if>
      <if test="processingFee != null">
        #{processingFee,jdbcType=NUMERIC},
      </if>
      <if test="paymentTips != null">
        #{paymentTips,jdbcType=NUMERIC},
      </if>
      <if test="paymentTipsBeforeCreate != null">
        #{paymentTipsBeforeCreate,jdbcType=NUMERIC},
      </if>
      <if test="paymentTipsAfterCreate != null">
        #{paymentTipsAfterCreate,jdbcType=NUMERIC},
      </if>
      <if test="paymentStatus != null">
        #{paymentStatus,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        #{reason,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTime != null">
        #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentMethod != null">
        #{paymentMethod,jdbcType=VARCHAR},
      </if>
      <if test="convenienceFee != null">
        #{convenienceFee,jdbcType=NUMERIC},
      </if>
      <if test="failTime != null">
        #{failTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentId != null">
        #{paymentId,jdbcType=BIGINT},
      </if>
      <if test="refundedConvenienceFee != null">
        #{refundedConvenienceFee,jdbcType=NUMERIC},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.order.repository.entity.OrderPayment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update public.order_payment
    <set>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=BIGINT},
      </if>
      <if test="companyId != null">
        company_id = #{companyId,jdbcType=BIGINT},
      </if>
      <if test="businessId != null">
        business_id = #{businessId,jdbcType=BIGINT},
      </if>
      <if test="staffId != null">
        staff_id = #{staffId,jdbcType=BIGINT},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=BIGINT},
      </if>
      <if test="paymentMethodId != null">
        payment_method_id = #{paymentMethodId,jdbcType=BIGINT},
      </if>
      <if test="paymentMethodExtra != null">
        payment_method_extra = #{paymentMethodExtra,jdbcType=OTHER,typeHandler=com.moego.svc.order.utils.StringToJsonbTypeHandler},
      </if>
      <if test="paymentMethodVendor != null">
        payment_method_vendor = #{paymentMethodVendor,jdbcType=VARCHAR},
      </if>
      <if test="isOnline != null">
        is_online = #{isOnline,jdbcType=BIT},
      </if>
      <if test="isDeposit != null">
        is_deposit = #{isDeposit,jdbcType=BIT},
      </if>
      <if test="paidBy != null">
        paid_by = #{paidBy,jdbcType=VARCHAR},
      </if>
      <if test="currency != null">
        currency = #{currency,jdbcType=VARCHAR},
      </if>
      <if test="totalAmount != null">
        total_amount = #{totalAmount,jdbcType=NUMERIC},
      </if>
      <if test="amount != null">
        amount = #{amount,jdbcType=NUMERIC},
      </if>
      <if test="refundedAmount != null">
        refunded_amount = #{refundedAmount,jdbcType=NUMERIC},
      </if>
      <if test="processingFee != null">
        processing_fee = #{processingFee,jdbcType=NUMERIC},
      </if>
      <if test="paymentTips != null">
        payment_tips = #{paymentTips,jdbcType=NUMERIC},
      </if>
      <if test="paymentTipsBeforeCreate != null">
        payment_tips_before_create = #{paymentTipsBeforeCreate,jdbcType=NUMERIC},
      </if>
      <if test="paymentTipsAfterCreate != null">
        payment_tips_after_create = #{paymentTipsAfterCreate,jdbcType=NUMERIC},
      </if>
      <if test="paymentStatus != null">
        payment_status = #{paymentStatus,jdbcType=VARCHAR},
      </if>
      <if test="reason != null">
        reason = #{reason,jdbcType=VARCHAR},
      </if>
      <if test="payTime != null">
        pay_time = #{payTime,jdbcType=TIMESTAMP},
      </if>
      <if test="cancelTime != null">
        cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      </if>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentMethod != null">
        payment_method = #{paymentMethod,jdbcType=VARCHAR},
      </if>
      <if test="convenienceFee != null">
        convenience_fee = #{convenienceFee,jdbcType=NUMERIC},
      </if>
      <if test="failTime != null">
        fail_time = #{failTime,jdbcType=TIMESTAMP},
      </if>
      <if test="paymentId != null">
        payment_id = #{paymentId,jdbcType=BIGINT},
      </if>
      <if test="refundedConvenienceFee != null">
        refunded_convenience_fee = #{refundedConvenienceFee,jdbcType=NUMERIC},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.order.repository.entity.OrderPayment">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update public.order_payment
    set order_id = #{orderId,jdbcType=BIGINT},
      company_id = #{companyId,jdbcType=BIGINT},
      business_id = #{businessId,jdbcType=BIGINT},
      staff_id = #{staffId,jdbcType=BIGINT},
      customer_id = #{customerId,jdbcType=BIGINT},
      payment_method_id = #{paymentMethodId,jdbcType=BIGINT},
      payment_method_extra = #{paymentMethodExtra,jdbcType=OTHER,typeHandler=com.moego.svc.order.utils.StringToJsonbTypeHandler},
      payment_method_vendor = #{paymentMethodVendor,jdbcType=VARCHAR},
      is_online = #{isOnline,jdbcType=BIT},
      is_deposit = #{isDeposit,jdbcType=BIT},
      paid_by = #{paidBy,jdbcType=VARCHAR},
      currency = #{currency,jdbcType=VARCHAR},
      total_amount = #{totalAmount,jdbcType=NUMERIC},
      amount = #{amount,jdbcType=NUMERIC},
      refunded_amount = #{refundedAmount,jdbcType=NUMERIC},
      processing_fee = #{processingFee,jdbcType=NUMERIC},
      payment_tips = #{paymentTips,jdbcType=NUMERIC},
      payment_tips_before_create = #{paymentTipsBeforeCreate,jdbcType=NUMERIC},
      payment_tips_after_create = #{paymentTipsAfterCreate,jdbcType=NUMERIC},
      payment_status = #{paymentStatus,jdbcType=VARCHAR},
      reason = #{reason,jdbcType=VARCHAR},
      pay_time = #{payTime,jdbcType=TIMESTAMP},
      cancel_time = #{cancelTime,jdbcType=TIMESTAMP},
      create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      payment_method = #{paymentMethod,jdbcType=VARCHAR},
      convenience_fee = #{convenienceFee,jdbcType=NUMERIC},
      fail_time = #{failTime,jdbcType=TIMESTAMP},
      payment_id = #{paymentId,jdbcType=BIGINT},
      refunded_convenience_fee = #{refundedConvenienceFee,jdbcType=NUMERIC}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByPaymentStatusAndLimit" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_payment
    where create_time &lt;= now() - (interval '1 minutes' * #{beforeMinutes})
    and payment_status = #{paymentStatus}
    limit #{limit,jdbcType=INTEGER}
  </select>

  <select id="selectByOrderIdAndPaymentId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from order_payment
    where order_id = #{orderId} and payment_id = #{paymentId}
  </select>
</mapper>