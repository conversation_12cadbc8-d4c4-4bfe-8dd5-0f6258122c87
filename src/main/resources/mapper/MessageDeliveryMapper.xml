<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.moego.svc.order.repository.mapper.MessageDeliveryMapper">
  <resultMap id="BaseResultMap" type="com.moego.svc.order.repository.entity.MessageDelivery">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="message_type" jdbcType="VARCHAR" property="messageType" />
    <result column="reference_id" jdbcType="VARCHAR" property="referenceId" />
    <result column="payload" jdbcType="VARCHAR" property="payload" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="retry_count" jdbcType="INTEGER" property="retryCount" />
    <result column="last_attempt_time" jdbcType="TIMESTAMP" property="lastAttemptTime" />
    <result column="delete_time" jdbcType="TIMESTAMP" property="deleteTime" />
  </resultMap>
  <sql id="Base_Column_List">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    id, create_time, update_time, message_type, reference_id, payload, status, retry_count, 
    last_attempt_time, delete_time
  </sql>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select 
    <include refid="Base_Column_List" />
    from message_delivery
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from message_delivery
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.MessageDelivery" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into message_delivery (create_time, update_time, message_type, 
      reference_id, payload, status, 
      retry_count, last_attempt_time, delete_time
      )
    values (#{createTime,jdbcType=TIMESTAMP}, #{updateTime,jdbcType=TIMESTAMP}, #{messageType,jdbcType=VARCHAR}, 
      #{referenceId,jdbcType=VARCHAR}, #{payload,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, 
      #{retryCount,jdbcType=INTEGER}, #{lastAttemptTime,jdbcType=TIMESTAMP}, #{deleteTime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.moego.svc.order.repository.entity.MessageDelivery" useGeneratedKeys="true">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    insert into message_delivery
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        create_time,
      </if>
      <if test="updateTime != null">
        update_time,
      </if>
      <if test="messageType != null">
        message_type,
      </if>
      <if test="referenceId != null">
        reference_id,
      </if>
      <if test="payload != null">
        payload,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="retryCount != null">
        retry_count,
      </if>
      <if test="lastAttemptTime != null">
        last_attempt_time,
      </if>
      <if test="deleteTime != null">
        delete_time,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="createTime != null">
        #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="messageType != null">
        #{messageType,jdbcType=VARCHAR},
      </if>
      <if test="referenceId != null">
        #{referenceId,jdbcType=VARCHAR},
      </if>
      <if test="payload != null">
        #{payload,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="retryCount != null">
        #{retryCount,jdbcType=INTEGER},
      </if>
      <if test="lastAttemptTime != null">
        #{lastAttemptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteTime != null">
        #{deleteTime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <update id="updateByPrimaryKeySelective" parameterType="com.moego.svc.order.repository.entity.MessageDelivery">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update message_delivery
    <set>
      <if test="createTime != null">
        create_time = #{createTime,jdbcType=TIMESTAMP},
      </if>
      <if test="updateTime != null">
        update_time = #{updateTime,jdbcType=TIMESTAMP},
      </if>
      <if test="messageType != null">
        message_type = #{messageType,jdbcType=VARCHAR},
      </if>
      <if test="referenceId != null">
        reference_id = #{referenceId,jdbcType=VARCHAR},
      </if>
      <if test="payload != null">
        payload = #{payload,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=VARCHAR},
      </if>
      <if test="retryCount != null">
        retry_count = #{retryCount,jdbcType=INTEGER},
      </if>
      <if test="lastAttemptTime != null">
        last_attempt_time = #{lastAttemptTime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleteTime != null">
        delete_time = #{deleteTime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.moego.svc.order.repository.entity.MessageDelivery">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update message_delivery
    set create_time = #{createTime,jdbcType=TIMESTAMP},
      update_time = #{updateTime,jdbcType=TIMESTAMP},
      message_type = #{messageType,jdbcType=VARCHAR},
      reference_id = #{referenceId,jdbcType=VARCHAR},
      payload = #{payload,jdbcType=VARCHAR},
      status = #{status,jdbcType=VARCHAR},
      retry_count = #{retryCount,jdbcType=INTEGER},
      last_attempt_time = #{lastAttemptTime,jdbcType=TIMESTAMP},
      delete_time = #{deleteTime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByMsgTypeAndRefId" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from message_delivery
    where message_type = #{messageType}
    and reference_id = #{referenceId}
    and delete_time is null
  </select>

  <update id="addRetryCountByMsgTypeAndRefId">
    update message_delivery
    set retry_count = retry_count + 1
    where message_type = #{messageType}
    and reference_id = #{referenceId}
    and delete_time is null
  </update>

  <select id="selectByStatus" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from message_delivery
    where status = #{status}
      and delete_time is null
  </select>
</mapper>