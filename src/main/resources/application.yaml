spring:
  application:
    name: moego-svc-order
  profiles:
    active: local
  cloud:
    aws:
      region:
        static: ${AWS_REGION:us-west-2}
  datasource:
    driver-class-name: software.amazon.jdbc.Driver
    url: jdbc:aws-wrapper:postgresql://${secret.datasource.postgres.url}:${secret.datasource.postgres.port}/moego_order
    username: ${secret.datasource.postgres.moego_order.username}
    password: ${secret.datasource.postgres.moego_order.password}
    hikari:
      # aws jdbc driver 配置
      data-source-properties:
        keepSessionStateOnFailover: true
        wrapperPlugins: failover2,efm2
  data:
    redis:
      host: ${secret.redis.host}
      password: ${secret.redis.password}
      port: ${secret.redis.port}
      ssl:
        enabled: ${secret.redis.tls}
      timeout: 60000
      key:
        delimiter: ':'
        prefix: local

#logging:
#  level:
#    com.moego.svc.order.repository: debug

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: com.moego.svc.order.repository.entity

moego:
  grpc:
    server:
      debug-enabled: true
      port: 9090
    client:
      stubs:
        - service: moego.service.marketing.**
          authority: moego-svc-marketing:9090
        - service: moego.service.organization.**
          authority: moego-svc-organization:9090
        - service: moego.service.appointment.**
          authority: moego-svc-appointment:9090
        - service: moego.service.order.**
          authority: moego-svc-order-v2:9090
        - service: moego.service.offering.**
          authority: moego-svc-offering:9090
        - service: moego.service.business_customer.**
          authority: moego-svc-business-customer:9090
        - service: moego.service.online_booking.**
          authority: moego-svc-online-booking:9090
  server:
    url:
      business: http://moego-service-business:9203
      grooming: http://moego-service-grooming:9206
      payment: http://moego-service-payment:9204
  feature-flag:
    growth-book:
      api-host: ${secret.growthbook.host}
      client-key: ${secret.growthbook.client_key}
  event-bus:
    brokers:
      # 可以添加多个 broker 集群的配置, 但是至少要有一个
      # 每个 broker 集群需要指定一个唯一的 name, 以及 broker 的地址列表
      - name: default
        # 一个 broker 集群可以有多个地址, 用逗号分隔或者使用数组, 推荐使用数组, 方便更好的阅读
        addresses:
          - ${secret.mq.kafka.broker_url_0}
          - ${secret.mq.kafka.broker_url_1}
          - ${secret.mq.kafka.broker_url_2}
        security:
          enabled: true
          properties:
            security.protocol: SASL_SSL
            sasl.mechanism: AWS_MSK_IAM
            sasl.jaas.config: software.amazon.msk.auth.iam.IAMLoginModule required;
            sasl.client.callback.handler.class: software.amazon.msk.auth.iam.IAMClientCallbackHandler
    producer:
      # 如果 enabled 为 false (默认值), 则不会初始化 producer, 此时如果代码里依赖了 producer, 则会抛出异常
      enabled: true
      # 发送消息成功时是否打印日志, 默认为 false
      log-success: false
      # 发送消息失败时是否打印日志, 默认为 true
      log-failure: true