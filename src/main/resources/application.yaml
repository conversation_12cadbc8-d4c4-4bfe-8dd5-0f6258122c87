spring:
  application:
    name: moego-svc-marketing
  cloud:
    aws:
      region:
        static: ${AWS_REGION:us-west-2}
  profiles:
    active: local
  datasource:
    driver-class-name: org.postgresql.Driver
    url: jdbc:postgresql://${secret.datasource.postgres.url}:${secret.datasource.postgres.port}/moego_marketing?ApplicationName=${POD_NAME}
    username: ${secret.datasource.postgres.moego_marketing.username}
    password: ${secret.datasource.postgres.moego_marketing.password}

mybatis:
  mapper-locations:
    - classpath:mapper/*.xml
  type-aliases-package: com.moego.svc.marketing.entity
  configuration:
    cache-enabled: false
    local-cache-scope: statement

moego:
  grpc:
    server:
      empty-server-enabled: true
      debug-enabled: true
      port: 9090
    client:
      stubs:
        - service: moego.service.order.**
          authority: moego-svc-order:9090
        - service: moego.service.organization.**
          authority: moego-svc-organization:9090
        - service: moego.service.appointment.**
          authority: moego-svc-appointment:9090
        - service: moego.service.online_booking.**
          authority: moego-svc-online-booking:9090
  server:
    url:
      customer: moego-service-customer:9201
      business: moego-service-business:9203
      grooming: moego-service-grooming:9206
      retail: moego-service-retail:9207

pagehelper:
  reasonable: false
  defaultCount: true
