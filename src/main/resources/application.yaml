spring:
  cloud:
    aws:
      region:
        static: ${AWS_REGION:us-west-2}
  application:
    name: moego-svc-google-partner
  profiles:
    active: local
  datasource:
    url: jdbc:postgresql://${secret.datasource.postgres.url}:${secret.datasource.postgres.port}/moego_google_partner?ApplicationName=${POD_NAME}
    username: ${secret.datasource.postgres.moego_google_partner.username}
    password: ${secret.datasource.postgres.moego_google_partner.password}

moego:
  server:
    url:
      grooming: moego-service-grooming:9206
      business: moego-service-business:9203
      payment: moego-service-payment:9204
      message: moego-service-message:9205
  ssh:
    sandbox:
      host: partnerupload.google.com
      port: 19321
      username: feeds-hpu8g7
      private-key-location: classpath:key/google-partner
    prod:
      host: partnerupload.google.com
      port: 19321
      username: feeds-r57pnw
      private-key-location: classpath:key/google-partner
  google-reserve:
    partner-id: 20002025
    merchant-status-query:
      credentials-path: classpath:key/credentials.json
