<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
  "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<!-- use mbGenerator: https://moego.atlassian.net/wiki/spaces/~************************/pages/79233368 -->
<generatorConfiguration>
  <context id="Postgres" targetRuntime="MyBatis3" defaultModelType="flat">
    <property name="javaFileEncoding" value="UTF-8" />
    <property name="beginningDelimiter" value='"' />
    <property name="endingDelimiter" value='"' />
    <property name="autoDelimitKeywords" value='true' />
    <plugin type="org.mybatis.generator.plugins.UnmergeableXmlMappersPlugin" />
    <plugin type="com.softwareloop.mybatis.generator.plugins.LombokPlugin">
      <property name="builder" value="true" />
      <property name="builder.toBuilder" value="true" />
    </plugin>
    <commentGenerator>
      <property name="suppressAllComments" value="true" />
    </commentGenerator>
    <jdbcConnection
      driverClass="org.postgresql.Driver"
      connectionURL="*************************************************************"
      userId="moego_developer_240310_eff7a0dc"
      password="G0MxI7NM_jX_f7Ky73vnrwej97xg1tly"
    />
    <javaTypeResolver>
      <property name="useJSR310Types" value="true" />
    </javaTypeResolver>
    <javaModelGenerator targetPackage="com.moego.svc.marketing.entity" targetProject="src/main/java" />
    <sqlMapGenerator targetPackage="mapper" targetProject="src/main/resources" />
    <javaClientGenerator
      targetPackage="com.moego.svc.marketing.mapper"
      targetProject="src/main/java"
      type="XMLMAPPER"
    />

    <table
      tableName="discount_code"
      enableCountByExample="true"
      enableSelectByExample="true"
      enableDeleteByExample="true"
      enableUpdateByExample="true"
      mapperName="DiscountCodeMapper"
    >
      <generatedKey column="id" sqlStatement="JDBC"/>
    </table>

  </context>
</generatorConfiguration>
