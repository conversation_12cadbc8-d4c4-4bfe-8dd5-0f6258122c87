spring:
  datasource:
    url: jdbc:aws-wrapper:mysql://mysql.t2.moego.dev:40106/moe_customer?useUnicode=true&characterEncoding=utf8&allowMultiQueries=true&useSSL=false
    username: moego_developer_240310_eff7a0dc
    password: G0MxI7NM_jX_f7Ky73vnrwej97xg1tly
  data:
    redis:
      host: redis.t2.moego.dev
      password: hnW6da5EfB2eVArf8JMKIste0CTJx7lRkW2YoM0Kb0w=
      port: 40179
      timeout: 60000
      key:
        delimiter: ':'
        prefix: local
      ssl:
        enabled: false
moego:
  event-bus:
    brokers:
      - name: default
        addresses:
          - kafka-controller-0.kafka-controller-headless.kafka.svc.cluster.local:9092
          - kafka-controller-1.kafka-controller-headless.kafka.svc.cluster.local:9092
          - kafka-controller-2.kafka-controller-headless.kafka.svc.cluster.local:9092
    producer:
      enabled: true
  feature-flag:
    growth-book:
      api-host: https://growthbook.moego.pet/growthbook-api
      client-key: sdk-qygeRRneunZQJxf

logging:
  level:
    org.jooq.tools.LoggerListener: DEBUG
