package com.moego.svc.marketing.service;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

import com.moego.server.grooming.dto.CustomerPetMergeRelationDTO;
import com.moego.server.grooming.dto.PetMergeRelationDTO;
import com.moego.svc.marketing.entity.DiscountCode;
import com.moego.svc.marketing.entity.DiscountCodeLog;
import com.moego.svc.marketing.mapper.DiscountCodeLogMapper;
import com.moego.svc.marketing.mapper.DiscountCodeMapper;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.transaction.annotation.Transactional;

@ExtendWith(MockitoExtension.class)
@Transactional
public class MarketingMergeServiceTest {
    @Mock
    DiscountCodeMapper discountCodeMapper;

    @Mock
    DiscountCodeLogMapper discountCodeLogMapper;

    @InjectMocks
    MarketingMergeService marketingMergeService;

    @Test
    void testMergeDiscountCodeData() {
        when(discountCodeMapper.selectByCompanyIdWithPage(any(), any(), any())).thenReturn(List.of());
        when(discountCodeLogMapper.selectByCompanyIdWithPage(any(), any(), any(), any()))
                .thenReturn(List.of());
        marketingMergeService.mergeDiscountCodeData(buildCustomerPetMergeRelationDTO(false));
        marketingMergeService.mergeDiscountCodeData(buildCustomerPetMergeRelationDTO(true));
        when(discountCodeMapper.selectByCompanyIdWithPage(any(), any(), any())).thenReturn(buildDiscountCode());
        when(discountCodeLogMapper.selectByCompanyIdWithPage(any(), any(), any(), any()))
                .thenReturn(buildDiscountCodeLog());
        marketingMergeService.mergeDiscountCodeData(buildCustomerPetMergeRelationDTO(true));
    }

    private List<DiscountCode> buildDiscountCode() {
        List<DiscountCode> discountCodeList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            var obj = new DiscountCode();
            obj.setId(1L);
            obj.setClientIds("[2,3]");
            discountCodeList.add(obj);
        }
        return discountCodeList;
    }

    private List<DiscountCodeLog> buildDiscountCodeLog() {
        List<DiscountCodeLog> discountCodeLogList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            var obj = new DiscountCodeLog();
            obj.setId(1L);
            obj.setClientId(2L);
            obj.setPetIds("[3,4]");
            discountCodeLogList.add(obj);
        }
        return discountCodeLogList;
    }

    private CustomerPetMergeRelationDTO buildCustomerPetMergeRelationDTO(boolean withPet) {
        CustomerPetMergeRelationDTO mergeRelation = new CustomerPetMergeRelationDTO();
        mergeRelation.setCompanyId(1L);
        mergeRelation.setTargetCustomerId(1);
        mergeRelation.setAllLocationIds(List.of(1L, 2L, 3L));
        mergeRelation.setSourceCustomerIds(List.of(2, 3));
        if (withPet) {
            var petDto = new PetMergeRelationDTO();
            petDto.setTargetPetId(1);
            petDto.setSourcePetIds(List.of(2, 3));
            mergeRelation.setPetMergeRelations(List.of(petDto));
        }
        return mergeRelation;
    }
}
