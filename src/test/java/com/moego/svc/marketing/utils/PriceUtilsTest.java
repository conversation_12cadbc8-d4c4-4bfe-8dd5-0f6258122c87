package com.moego.svc.marketing.utils;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

import com.moego.svc.marketing.entity.DiscountCode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalTime;
import java.time.OffsetDateTime;
import java.time.ZoneOffset;
import org.junit.jupiter.api.Test;

public class PriceUtilsTest {

    @Test
    void testGetDiscountPrice() {
        BigDecimal price = new BigDecimal("2.3");

        DiscountCode discountCode = new DiscountCode();
        discountCode.setAddOnIds("Add On Ids");
        discountCode.setAllowedAllClients(true);
        discountCode.setAllowedAllProducts(true);
        discountCode.setAllowedAllServices(true);
        discountCode.setAllowedAllThing(true);
        discountCode.setAllowedNewClients(true);
        discountCode.setAmount(new BigDecimal("2.3"));
        discountCode.setBusinessId(1L);
        discountCode.setClientIds("Client Ids");
        discountCode.setClientsGroup("Clients Group");
        discountCode.setCompanyId(1L);
        discountCode.setCreateBy(1L);
        discountCode.setCreateTime(OffsetDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC));
        discountCode.setDescription("The characteristics of someone or something");
        discountCode.setDiscountCode("3");
        discountCode.setDiscountSales(new BigDecimal("2.3"));
        discountCode.setEndDate("2020-03-01");
        discountCode.setId(1L);
        discountCode.setLimitBudget(1);
        discountCode.setLimitNumberPerClient(10);
        discountCode.setLimitUsage(1);
        discountCode.setProductIds("Product Ids");
        discountCode.setServiceIds("Service Ids");
        discountCode.setStartDate("2020-03-01");
        discountCode.setStatus(1);
        discountCode.setTotalUsage(1);
        discountCode.setType(1);
        discountCode.setUpdateBy(1L);
        discountCode.setUpdateTime(OffsetDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC));
        Pair<BigDecimal, BigDecimal> actualDiscountPrice = PriceUtils.getDiscountPrice(price, discountCode);
        BigDecimal expectedSecondResult = new BigDecimal("0.05");
        assertEquals(expectedSecondResult, actualDiscountPrice.second());
        BigDecimal expectedFirstResult = new BigDecimal("2.25");
        assertEquals(expectedFirstResult, actualDiscountPrice.first());
    }

    @Test
    void testGetDiscountPrice2() {
        BigDecimal price = new BigDecimal("2.3");
        DiscountCode discountCode = new DiscountCode();
        discountCode.setAddOnIds("Add On Ids");
        discountCode.setAllowedAllClients(true);
        discountCode.setAllowedAllProducts(true);
        discountCode.setAllowedAllServices(true);
        discountCode.setAllowedAllThing(true);
        discountCode.setAllowedNewClients(true);
        discountCode.setAmount(new BigDecimal("2.3"));
        discountCode.setBusinessId(1L);
        discountCode.setClientIds("Client Ids");
        discountCode.setClientsGroup("Clients Group");
        discountCode.setCompanyId(1L);
        discountCode.setCreateBy(1L);
        discountCode.setCreateTime(OffsetDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC));
        discountCode.setDescription("The characteristics of someone or something");
        discountCode.setDiscountCode("3");
        discountCode.setDiscountSales(new BigDecimal("2.3"));
        discountCode.setEndDate("2020-03-01");
        discountCode.setId(1L);
        discountCode.setLimitBudget(1);
        discountCode.setLimitNumberPerClient(10);
        discountCode.setLimitUsage(1);
        discountCode.setProductIds("Product Ids");
        discountCode.setServiceIds("Service Ids");
        discountCode.setStartDate("2020-03-01");
        discountCode.setStatus(1);
        discountCode.setTotalUsage(1);
        discountCode.setType(4);
        discountCode.setUpdateBy(1L);
        discountCode.setUpdateTime(OffsetDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC));
        Pair<BigDecimal, BigDecimal> actualDiscountPrice = PriceUtils.getDiscountPrice(price, discountCode);
        assertNull(actualDiscountPrice);
    }

    @Test
    void testGetDiscountPrice3() {
        BigDecimal price = new BigDecimal("2.3");

        DiscountCode discountCode = new DiscountCode();
        discountCode.setAddOnIds("Add On Ids");
        discountCode.setAllowedAllClients(true);
        discountCode.setAllowedAllProducts(true);
        discountCode.setAllowedAllServices(true);
        discountCode.setAllowedAllThing(true);
        discountCode.setAllowedNewClients(true);
        discountCode.setAmount(new BigDecimal("2.3"));
        discountCode.setBusinessId(1L);
        discountCode.setClientIds("Client Ids");
        discountCode.setClientsGroup("Clients Group");
        discountCode.setCompanyId(1L);
        discountCode.setCreateBy(1L);
        discountCode.setCreateTime(OffsetDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC));
        discountCode.setDescription("The characteristics of someone or something");
        discountCode.setDiscountCode("3");
        discountCode.setDiscountSales(new BigDecimal("2.3"));
        discountCode.setEndDate("2020-03-01");
        discountCode.setId(1L);
        discountCode.setLimitBudget(1);
        discountCode.setLimitNumberPerClient(10);
        discountCode.setLimitUsage(1);
        discountCode.setProductIds("Product Ids");
        discountCode.setServiceIds("Service Ids");
        discountCode.setStartDate("2020-03-01");
        discountCode.setStatus(1);
        discountCode.setTotalUsage(1);
        discountCode.setType(2);
        discountCode.setUpdateBy(1L);
        discountCode.setUpdateTime(OffsetDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.MIDNIGHT, ZoneOffset.UTC));
        Pair<BigDecimal, BigDecimal> actualDiscountPrice = PriceUtils.getDiscountPrice(price, discountCode);
        BigDecimal expectedAmount = new BigDecimal("0");
        assertEquals(expectedAmount, discountCode.getAmount());
        BigDecimal expectedFirstResult = new BigDecimal("0.0");
        assertEquals(expectedFirstResult, actualDiscountPrice.first());
        BigDecimal expectedSecondResult = new BigDecimal("2.3");
        assertEquals(expectedSecondResult, actualDiscountPrice.second());
    }
}
