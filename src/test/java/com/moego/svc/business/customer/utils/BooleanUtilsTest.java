package com.moego.svc.business.customer.utils;

import com.moego.svc.business.customer.enums.BooleanEnum;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class BooleanUtilsTest {

    @Test
    void isTrue_int_test() {
        Assertions.assertTrue(BooleanUtils.isTrue(BooleanEnum.TRUE.getValue()));
        Assertions.assertFalse(BooleanUtils.isTrue(BooleanEnum.FALSE.getValue()));

        Assertions.assertTrue(BooleanUtils.isTrue(1));
        Assertions.assertFalse(BooleanUtils.isTrue(0));
        Assertions.assertFalse(BooleanUtils.isTrue(2));
        Integer nullValue = null;
        Assertions.assertFalse(BooleanUtils.isTrue(nullValue));
    }

    @Test
    void isTrue_enum_test() {
        Assertions.assertTrue(BooleanUtils.isTrue(BooleanEnum.TRUE));
        Assertions.assertFalse(BooleanUtils.isTrue(BooleanEnum.FALSE));

        BooleanEnum nullValue = null;
        Assertions.assertFalse(BooleanUtils.isTrue(nullValue));
    }
}
