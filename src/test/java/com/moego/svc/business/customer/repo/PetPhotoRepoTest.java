package com.moego.svc.business.customer.repo;

import static com.moego.svc.business.customer.repository.jooq.Tables.MOE_PET_PHOTO;

import com.moego.svc.business.customer.enums.StatusEnum;
import com.moego.svc.business.customer.utils.PageParams;
import org.jooq.DSLContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@ActiveProfiles("unit-test")
@Transactional
public class PetPhotoRepoTest {

    @Autowired
    PetPhotoRepo petPhotoRepo;

    @Autowired
    DSLContext dsl;

    @Test
    public void testGetPetPhoto() {
        var record = dsl.newRecord(MOE_PET_PHOTO)
                .setPetId(1)
                .setPhotoUrl("https://www.moego.pet")
                .setDescription("This is a pet photo");

        record.insert();
        var id = record.getId();

        var getRecord = petPhotoRepo.getPetPhoto(id);
        Assertions.assertEquals(record.getId(), getRecord.getId());
        Assertions.assertEquals(record.getPetId(), getRecord.getPetId());
        Assertions.assertEquals(record.getPhotoUrl(), getRecord.getPhotoUrl());
        Assertions.assertEquals(record.getDescription(), getRecord.getDescription());
        Assertions.assertEquals(StatusEnum.NORMAL.getValue(), getRecord.getStatus());
    }

    @Test
    public void testListPetPhoto() {
        var url = "https://www.moego.pet";
        var record1 = dsl.newRecord(MOE_PET_PHOTO).setPetId(1).setPhotoUrl(url).setDescription("photo1");
        var record2 = dsl.newRecord(MOE_PET_PHOTO).setPetId(1).setPhotoUrl(url).setDescription("photo2");
        var record3 = dsl.newRecord(MOE_PET_PHOTO).setPetId(1).setPhotoUrl(url).setDescription("photo3");
        var record4 = dsl.newRecord(MOE_PET_PHOTO).setPetId(1).setPhotoUrl(url).setDescription("photo4");

        record1.insert();
        record2.insert();
        record3.insert();
        record4.insert();

        var photos = petPhotoRepo.listPetPhoto(1, null);
        Assertions.assertEquals(4, photos.size());
        Assertions.assertEquals("photo4", photos.get(0).getDescription());
        Assertions.assertEquals("photo3", photos.get(1).getDescription());
        Assertions.assertEquals("photo2", photos.get(2).getDescription());
        Assertions.assertEquals("photo1", photos.get(3).getDescription());

        var page = new PageParams(1, 3);
        photos = petPhotoRepo.listPetPhoto(1, page);
        Assertions.assertEquals(3, photos.size());
        Assertions.assertEquals("photo4", photos.get(0).getDescription());
        Assertions.assertEquals("photo3", photos.get(1).getDescription());
        Assertions.assertEquals("photo2", photos.get(2).getDescription());

        page = new PageParams(2, 3);
        photos = petPhotoRepo.listPetPhoto(1, page);
        Assertions.assertEquals(1, photos.size());
        Assertions.assertEquals("photo1", photos.get(0).getDescription());
    }
}
