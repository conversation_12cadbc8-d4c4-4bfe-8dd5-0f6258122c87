/*
 * @since 2023-05-30 19:03:06
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.svc.business.customer.utils;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrowsExactly;

import com.moego.lib.common.exception.ExceptionUtil;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import org.junit.jupiter.api.function.Executable;
import org.springframework.stereotype.Component;

@Component
public class TestUtils {

    public static void badParams(String message, Executable executable) {
        var e = assertThrowsExactly(StatusRuntimeException.class, executable);
        assertEquals(Status.Code.INVALID_ARGUMENT, e.getStatus().getCode());
        try {
            var d = ExceptionUtil.extractCommonError(e);
            assertEquals(message, d.getMessage());
        } catch (Throwable ignored) {
            assertThat(e.getMessage()).contains(message);
        }
    }
}
