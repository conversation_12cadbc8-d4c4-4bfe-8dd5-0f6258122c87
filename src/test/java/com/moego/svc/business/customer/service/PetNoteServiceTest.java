package com.moego.svc.business.customer.service;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.business_customer.v1.BusinessPetNoteCreateDef;
import com.moego.idl.models.business_customer.v1.BusinessPetNoteUpdateDef;
import com.moego.lib.common.exception.BizException;
import com.moego.svc.business.customer.enums.BooleanEnum;
import com.moego.svc.business.customer.enums.option.GetOption;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoePetNoteRecord;
import com.moego.svc.business.customer.utils.BooleanUtils;
import com.moego.svc.business.customer.utils.TimeUtils;
import java.time.Instant;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@ActiveProfiles("unit-test")
@Transactional
public class PetNoteServiceTest {

    @Autowired
    PetNoteService petNoteService;

    @Test
    void testPetNote() throws InterruptedException {
        var petId = 1;
        var noteString = "This is a note";
        var createdBy = 1L;

        // 初始没有 note
        var latestNote = petNoteService.getPetLatestNote(petId);
        assertThat(latestNote).isNull();
        var notes = petNoteService.listPetNote(petId);
        assertThat(notes).isEmpty();

        var createDef =
                BusinessPetNoteCreateDef.newBuilder().setNote(noteString).build();

        // 创建一个正常的 note
        var id1 = petNoteService.createPetNote(petId, createdBy, createDef);
        var note1 = petNoteService.getPetNote(id1);
        assertThat(note1.getPetId()).isEqualTo(petId);
        assertThat(note1.getNote()).isEqualTo(noteString);
        assertThat(note1.getCreateBy()).isEqualTo(createdBy);
        assertThat(note1.getUpdateBy()).isEqualTo(createdBy);
        assertThat(note1.getCreateTime()).isNotNull();
        assertThat(note1.getCreateTime()).isGreaterThan(0L);
        assertThat(note1.getUpdateTime()).isNotNull();
        assertThat(note1.getUpdateTime()).isGreaterThan(0L);

        latestNote = petNoteService.getPetLatestNote(petId);
        assertThat(latestNote).isNotNull();
        assertThat(latestNote.getId()).isEqualTo(id1);

        // 不带 createdBy 创建 note
        var id2 = petNoteService.createPetNote(petId, null, createDef);
        var note2 = petNoteService.getPetNote(id2);
        assertThat(note2.getPetId()).isEqualTo(petId);
        assertThat(note2.getNote()).isEqualTo(noteString);
        assertThat(note2.getCreateBy()).isEqualTo(0L);
        assertThat(note2.getUpdateBy()).isEqualTo(0L);
        assertThat(note2.getCreateTime()).isNotNull();
        assertThat(note2.getCreateTime()).isGreaterThan(0L);
        assertThat(note2.getUpdateTime()).isNotNull();
        assertThat(note2.getUpdateTime()).isGreaterThan(0L);

        latestNote = petNoteService.getPetLatestNote(petId);
        assertThat(latestNote).isNotNull();
        assertThat(latestNote.getId()).isEqualTo(id2);

        // 指定 createdAt 和 updatedAt
        long createdAt = Instant.now().getEpochSecond() - 10000;
        long updatedAt = Instant.now().getEpochSecond() - 5000;
        var createDef2 = BusinessPetNoteCreateDef.newBuilder()
                .setNote(noteString)
                .setCreatedAt(TimeUtils.secondToTimestamp(createdAt))
                .setUpdatedAt(TimeUtils.secondToTimestamp(updatedAt))
                .build();

        var id3 = petNoteService.createPetNote(petId, createdBy, createDef2);
        var note3 = petNoteService.getPetNote(id3);
        assertThat(note3.getPetId()).isEqualTo(petId);
        assertThat(note3.getNote()).isEqualTo(noteString);
        assertThat(note3.getCreateBy()).isEqualTo(createdBy);
        assertThat(note3.getUpdateBy()).isEqualTo(createdBy);
        assertThat(note3.getCreateTime()).isNotNull();
        assertThat(note3.getCreateTime()).isEqualTo(createdAt);
        assertThat(note3.getUpdateTime()).isNotNull();
        assertThat(note3.getUpdateTime()).isEqualTo(updatedAt);

        // note3 指定了 createdAt 比 note2 早, 所以 note2 仍是最新的
        latestNote = petNoteService.getPetLatestNote(petId);
        assertThat(latestNote).isNotNull();
        assertThat(latestNote.getId()).isEqualTo(id2);

        // list notes
        notes = petNoteService.listPetNote(petId);
        assertThat(notes.size()).isEqualTo(3);
        var ids = notes.stream()
                .map(MoePetNoteRecord::getId)
                .map(Integer::longValue)
                .collect(Collectors.toSet());
        assertThat(ids).contains(id1, id2, id3);

        // update note
        TimeUnit.SECONDS.sleep(1);
        var newNoteString = "This is a new note";
        var updatedBy = 2L;
        var updateDef =
                BusinessPetNoteUpdateDef.newBuilder().setNote(newNoteString).build();
        petNoteService.updatePetNote(id1, updatedBy, updateDef);
        var updatedNote = petNoteService.getPetNote(id1);
        assertThat(updatedNote.getPetId()).isEqualTo(petId);
        assertThat(updatedNote.getNote()).isEqualTo(newNoteString);
        assertThat(updatedNote.getCreateBy()).isEqualTo(createdBy);
        assertThat(updatedNote.getUpdateBy()).isEqualTo(updatedBy);
        assertThat(updatedNote.getCreateTime()).isNotNull();
        assertThat(updatedNote.getCreateTime()).isGreaterThan(0L);
        assertThat(updatedNote.getUpdateTime()).isNotNull();
        assertThat(updatedNote.getUpdateTime()).isGreaterThan(updatedNote.getCreateTime());

        // delete note
        petNoteService.deletePetNote(id2);
        petNoteService.deletePetNote(id2);

        // list notes
        notes = petNoteService.listPetNote(petId);
        assertThat(notes.size()).isEqualTo(2);
        ids = notes.stream()
                .map(MoePetNoteRecord::getId)
                .map(Integer::longValue)
                .collect(Collectors.toSet());
        assertThat(ids).contains(id1, id3);
    }

    @Test
    void testBatchCreatePetNote() {
        var petId = 1;
        var createdAt = TimeUtils.secondToTimestamp(Instant.now().getEpochSecond() - 10000);
        var updatedAt = TimeUtils.secondToTimestamp(Instant.now().getEpochSecond() - 5000);
        var createDefs = List.of(
                BusinessPetNoteCreateDef.newBuilder().setNote("Note 1").build(),
                BusinessPetNoteCreateDef.newBuilder()
                        .setNote("Note 2")
                        .setCreatedAt(createdAt)
                        .setUpdatedAt(updatedAt)
                        .build());

        petNoteService.batchCreatePetNote(petId, null, createDefs);

        var notes = petNoteService.listPetNote(petId);
        assertThat(notes.size()).isEqualTo(2);
        notes.forEach(note -> {
            assertThat(note.getPetId()).isEqualTo(petId);
            assertThat(note.getCreateBy()).isEqualTo(0);
            assertThat(note.getUpdateBy()).isEqualTo(0);
            assertThat(note.getCreateTime()).isNotNull();
            assertThat(note.getUpdateTime()).isNotNull();

            if (note.getNote().equals("Note 1")) {
                assertThat(note.getCreateTime()).isGreaterThan(0L);
                assertThat(note.getUpdateTime()).isGreaterThan(0L);
            } else if (note.getNote().equals("Note 2")) {
                assertThat(note.getCreateTime()).isEqualTo(createdAt.getSeconds());
                assertThat(note.getUpdateTime()).isEqualTo(updatedAt.getSeconds());
            } else {
                Assertions.fail();
            }
        });
    }

    @Test
    void testNoteLimit() {
        var petId = 1;
        // 使用 stream 生产 1~200 的数字
        var createDefs = Stream.iterate(1, i -> i + 1)
                .limit(PetNoteService.MAX_PET_NOTE_COUNT - 1)
                .map(i -> BusinessPetNoteCreateDef.newBuilder()
                        .setNote("Note " + i)
                        .build())
                .toList();

        // 批量插入成功
        Assertions.assertDoesNotThrow(() -> petNoteService.batchCreatePetNote(petId, null, createDefs));
        // 再次批量插入失败
        Assertions.assertThrows(BizException.class, () -> petNoteService.batchCreatePetNote(petId, null, createDefs));

        var createDef = BusinessPetNoteCreateDef.newBuilder().setNote("Note").build();
        // 因为留了一个, 可以插入成功
        Assertions.assertDoesNotThrow(() -> petNoteService.createPetNote(petId, null, createDef));
        // 再次插入失败
        Assertions.assertThrows(BizException.class, () -> petNoteService.createPetNote(petId, null, createDef));
    }

    @Test
    void testGetOptions() {
        var petId = 1L;
        var nonExistId = -1L;

        Assertions.assertThrows(BizException.class, () -> petNoteService.getPetNote(nonExistId));
        Assertions.assertNull(petNoteService.getPetNote(nonExistId, GetOption.ACCEPT_NULL));

        var createDef = BusinessPetNoteCreateDef.newBuilder().setNote("note").build();

        // 创建一个正常的 note
        var id = petNoteService.createPetNote(petId, null, createDef);
        var note = petNoteService.getPetNote(id);
        assertThat(note).isNotNull();

        petNoteService.deletePetNote(id);

        Assertions.assertThrows(BizException.class, () -> petNoteService.getPetNote(id));

        note = petNoteService.getPetNote(id, GetOption.INCLUDE_DELETED);
        assertThat(note).isNotNull();
    }

    @Test
    void testPinPetNote() throws InterruptedException {
        var petId = 1L;
        var noteString = "This is a note for pin test";
        Long createdBy = 1L;
        Long updatedBy = 2L;

        // 创建一个正常的 note
        var createDef =
                BusinessPetNoteCreateDef.newBuilder().setNote(noteString).build();
        var noteId = petNoteService.createPetNote(petId, createdBy, createDef);
        var note = petNoteService.getPetNote(noteId);

        // 验证初始状态：未置顶
        assertThat(note.getIsPinned()).isEqualTo(BooleanEnum.FALSE.getValue());
        assertThat(note.getPinnedAt()).isEqualTo(0L);

        // 记录原始更新时间
        var originalUpdateTime = note.getUpdateTime();

        // 等待1秒，确保时间戳变化可以被检测到
        TimeUnit.SECONDS.sleep(1);

        // 测试置顶功能
        var pinnedNote = petNoteService.pinPetNote(noteId, updatedBy, true);

        // 验证置顶状态
        assertThat(pinnedNote.getIsPinned()).isEqualTo(BooleanEnum.TRUE.getValue());
        assertThat(pinnedNote.getPinnedAt()).isGreaterThan(0L);
        assertThat(pinnedNote.getUpdateBy()).isEqualTo(updatedBy.intValue());
        assertThat(pinnedNote.getUpdateTime()).isGreaterThanOrEqualTo(originalUpdateTime);

        // 记录置顶时的更新时间
        var pinnedUpdateTime = pinnedNote.getUpdateTime();

        // 等待1秒，确保时间戳变化可以被检测到
        TimeUnit.SECONDS.sleep(1);

        // 测试取消置顶功能
        var unpinnedNote = petNoteService.pinPetNote(noteId, updatedBy, false);

        // 验证取消置顶状态
        assertThat(unpinnedNote.getIsPinned()).isEqualTo(BooleanEnum.FALSE.getValue());
        // pinnedAt应该保留最后一次置顶的时间戳
        assertThat(unpinnedNote.getPinnedAt()).isGreaterThan(0L);
        assertThat(unpinnedNote.getUpdateBy()).isEqualTo(updatedBy.intValue());
        assertThat(unpinnedNote.getUpdateTime()).isGreaterThanOrEqualTo(pinnedUpdateTime);

        // 测试不提供updatedBy的情况
        TimeUnit.SECONDS.sleep(1);
        var pinnedNoteNoUpdater = petNoteService.pinPetNote(noteId, null, true);

        // 验证置顶状态，但updateBy应该保持不变
        assertThat(pinnedNoteNoUpdater.getIsPinned()).isEqualTo(BooleanEnum.TRUE.getValue());
        assertThat(pinnedNoteNoUpdater.getPinnedAt()).isGreaterThanOrEqualTo(unpinnedNote.getPinnedAt());
        // 由于没有提供updatedBy，应该保持之前的值
        assertThat(pinnedNoteNoUpdater.getUpdateBy()).isEqualTo(updatedBy.intValue());
        assertThat(pinnedNoteNoUpdater.getUpdateTime()).isGreaterThanOrEqualTo(unpinnedNote.getUpdateTime());
    }

    @Test
    void testBatchListPetNoteOrder() throws InterruptedException {
        var petId = 1L;
        var createdBy = 1L;
        var updatedBy = 2L;

        // 创建三个note，按创建时间从老到新排序
        var createDef1 = BusinessPetNoteCreateDef.newBuilder().setNote("Note 1").build();
        var noteId1 = petNoteService.createPetNote(petId, createdBy, createDef1);

        TimeUnit.SECONDS.sleep(1);

        var createDef2 = BusinessPetNoteCreateDef.newBuilder().setNote("Note 2").build();
        var noteId2 = petNoteService.createPetNote(petId, createdBy, createDef2);

        TimeUnit.SECONDS.sleep(1);

        var createDef3 = BusinessPetNoteCreateDef.newBuilder().setNote("Note 3").build();
        var noteId3 = petNoteService.createPetNote(petId, createdBy, createDef3);

        // 验证初始排序：所有笔记都存在
        var notes = petNoteService.batchListPetNote(List.of(petId));
        assertThat(notes.size()).isEqualTo(3);
        // 验证所有笔记都存在，不验证特定的排序
        assertThat(notes.stream().map(MoePetNoteRecord::getNote).collect(Collectors.toList()))
                .containsExactlyInAnyOrder("Note 1", "Note 2", "Note 3");

        // 验证所有笔记的初始状态都是未置顶
        assertThat(notes.stream().noneMatch(n -> BooleanUtils.isTrue(n.getIsPinned())))
                .isTrue();

        // 置顶note 1
        petNoteService.pinPetNote(noteId1, updatedBy, true);

        // 验证排序：置顶的在前面
        notes = petNoteService.batchListPetNote(List.of(petId));
        assertThat(notes.size()).isEqualTo(3);

        // 验证第一个笔记是置顶的
        assertThat(notes.get(0).getIsPinned()).isEqualTo(BooleanEnum.TRUE.getValue());
        assertThat(notes.get(0).getNote()).isEqualTo("Note 1");

        // 验证其他笔记存在
        assertThat(notes.stream().map(MoePetNoteRecord::getNote).collect(Collectors.toList()))
                .contains("Note 2", "Note 3");

        // 置顶note 2
        TimeUnit.SECONDS.sleep(1);
        petNoteService.pinPetNote(noteId2, updatedBy, true);

        // 验证排序：多个置顶的按置顶时间降序
        notes = petNoteService.batchListPetNote(List.of(petId));
        assertThat(notes.size()).isEqualTo(3);

        // 验证前两个笔记是置顶的
        assertThat(notes.get(0).getIsPinned()).isEqualTo(BooleanEnum.TRUE.getValue());
        assertThat(notes.get(1).getIsPinned()).isEqualTo(BooleanEnum.TRUE.getValue());

        // 验证最新置顶的在前面
        var firstPinnedAt = notes.get(0).getPinnedAt();
        var secondPinnedAt = notes.get(1).getPinnedAt();
        assertThat(firstPinnedAt).isGreaterThanOrEqualTo(secondPinnedAt);

        // 验证所有笔记存在
        assertThat(notes.stream().map(MoePetNoteRecord::getNote).collect(Collectors.toList()))
                .containsExactlyInAnyOrder("Note 1", "Note 2", "Note 3");

        // 取消置顶note 1
        petNoteService.pinPetNote(noteId1, updatedBy, false);

        // 验证排序：取消置顶后的note应该在非置顶组
        notes = petNoteService.batchListPetNote(List.of(petId));
        assertThat(notes.size()).isEqualTo(3);

        // 找到置顶和非置顶的笔记
        var pinnedNotes =
                notes.stream().filter(n -> BooleanUtils.isTrue(n.getIsPinned())).toList();
        var unpinnedNotes = notes.stream()
                .filter(n -> !BooleanUtils.isTrue(n.getIsPinned()))
                .toList();

        // 验证置顶和非置顶的数量
        assertThat(pinnedNotes.size()).isEqualTo(1);
        assertThat(unpinnedNotes.size()).isEqualTo(2);

        // 验证置顶的笔记是Note 2
        assertThat(pinnedNotes.get(0).getNote()).isEqualTo("Note 2");

        // 验证非置顶的笔记中，Note 3（最新创建）应该在Note 1前面
        assertThat(unpinnedNotes.get(0).getNote()).isEqualTo("Note 3");
        assertThat(unpinnedNotes.get(1).getNote()).isEqualTo("Note 1");
    }
}
