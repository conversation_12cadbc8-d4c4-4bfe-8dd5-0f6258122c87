package com.moego.svc.business.customer.converter;

import static com.moego.svc.business.customer.repository.jooq.Tables.MOE_CUSTOMER_ADDRESS;

import com.google.type.LatLng;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressCreateDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressUpdateDef;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.svc.business.customer.enums.BooleanEnum;
import org.jooq.DSLContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest
@ActiveProfiles("unit-test")
public class CustomerAddressConverterTest {

    @Autowired
    private CustomerAddressConverter customerAddressConverter;

    @Autowired
    private DSLContext dsl;

    @Test
    void testCreateRecord() {
        var companyId = 1;
        var businessId = 2;
        var customerId = 3L;
        var tenant = Tenant.newBuilder()
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .build();
        var latlng = LatLng.newBuilder().setLatitude(1.1).setLongitude(2.2).build();
        var address1 = "address1";

        var builder = BusinessCustomerAddressCreateDef.newBuilder();
        builder.setAddress1(address1);

        var record = customerAddressConverter.createRecord(tenant, customerId, builder.build());
        Assertions.assertEquals(companyId, record.getCompanyId());
        Assertions.assertEquals(businessId, record.getBusinessId());
        Assertions.assertEquals(address1, record.getAddress1());
        Assertions.assertEquals(BooleanEnum.FALSE.getValue(), record.getIsPrimary());
        Assertions.assertNull(record.getId());
        Assertions.assertNull(record.getLat());
        Assertions.assertNull(record.getLng());
        Assertions.assertNotNull(record.getCreateTime());
        Assertions.assertNotNull(record.getUpdateTime());

        builder.setCoordinate(latlng);
        record = customerAddressConverter.createRecord(tenant, customerId, builder.build());
        Assertions.assertEquals(String.valueOf(latlng.getLatitude()), record.getLat());
        Assertions.assertEquals(String.valueOf(latlng.getLongitude()), record.getLng());
    }

    @Test
    void testUpdateRecord() {
        var address = dsl.newRecord(MOE_CUSTOMER_ADDRESS)
                .setId(123)
                .setCustomerId(456)
                .setCompanyId(1L)
                .setBusinessId(2)
                .setAddress1("address1")
                .setLat("0.1")
                .setLng("0.2")
                .setUpdateTime(1L)
                .setIsPrimary(BooleanEnum.TRUE.getValue());

        var builder = BusinessCustomerAddressUpdateDef.newBuilder().setIsPrimary(false);

        // 只更新 isPrimary, 经纬度不变
        customerAddressConverter.updateRecord(builder.build(), address);
        Assertions.assertEquals(BooleanEnum.FALSE.getValue(), address.getIsPrimary());
        Assertions.assertNotEquals(1L, address.getUpdateTime());
        Assertions.assertEquals("0.1", address.getLat());
        Assertions.assertEquals("0.2", address.getLng());

        // 更新经纬度
        var latlng = LatLng.newBuilder().setLatitude(1.1).setLongitude(2.2).build();
        builder.setCoordinate(latlng);
        customerAddressConverter.updateRecord(builder.build(), address);
        Assertions.assertEquals(String.valueOf(latlng.getLatitude()), address.getLat());
        Assertions.assertEquals(String.valueOf(latlng.getLongitude()), address.getLng());

        // 更新任意地址字段（除了 address2）但是不提供经纬度，经纬度应该被清空
        builder = BusinessCustomerAddressUpdateDef.newBuilder().setAddress2("address2");
        customerAddressConverter.updateRecord(builder.build(), address);
        Assertions.assertEquals("address2", address.getAddress2());
        Assertions.assertEquals("2.2", address.getLng());
        Assertions.assertEquals("1.1", address.getLat());

        builder = BusinessCustomerAddressUpdateDef.newBuilder().setAddress1("address1");
        customerAddressConverter.updateRecord(builder.build(), address);
        Assertions.assertEquals("address1", address.getAddress1());
        Assertions.assertEquals("2.2", address.getLng());
        Assertions.assertEquals("1.1", address.getLat());

        builder = BusinessCustomerAddressUpdateDef.newBuilder().setAddress1("address111");
        customerAddressConverter.updateRecord(builder.build(), address);
        Assertions.assertEquals("address111", address.getAddress1());
        Assertions.assertEquals("", address.getLng());
        Assertions.assertEquals("", address.getLat());
    }

    @Test
    void testToModel() {
        var address = dsl.newRecord(MOE_CUSTOMER_ADDRESS)
                .setId(123)
                .setCustomerId(456)
                .setCompanyId(1L)
                .setBusinessId(2)
                .setAddress1("address1")
                .setUpdateTime(1L)
                .setIsPrimary(BooleanEnum.TRUE.getValue());

        // no lat lng
        var model = customerAddressConverter.toModel(address);
        Assertions.assertEquals(address.getId().longValue(), model.getId());
        Assertions.assertEquals(address.getCustomerId().longValue(), model.getCustomerId());
        Assertions.assertEquals(address.getAddress1(), model.getAddress1());
        Assertions.assertTrue(model.getIsPrimary());
        Assertions.assertFalse(model.getDeleted());
        Assertions.assertFalse(model.hasCoordinate());

        // empty lat lng
        address.setLat("").setLng("");
        model = customerAddressConverter.toModel(address);
        Assertions.assertFalse(model.hasCoordinate());

        // has lat lng
        address.setLat("0.1").setLng("0.2");
        model = customerAddressConverter.toModel(address);
        Assertions.assertEquals(
                Double.valueOf(address.getLat()), model.getCoordinate().getLatitude());
        Assertions.assertEquals(
                Double.valueOf(address.getLng()), model.getCoordinate().getLongitude());
    }
}
