package com.moego.svc.business.customer.service.customer_merge;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.business_customer.v1.BusinessCustomerCreateDef;
import com.moego.idl.models.business_customer.v1.MergeRelationDef;
import com.moego.svc.business.customer.enums.CustomerStatus;
import com.moego.svc.business.customer.enums.option.GetOption;
import com.moego.svc.business.customer.params.ListContactParam;
import com.moego.svc.business.customer.repo.CustomerMergeProcessRepo;
import com.moego.svc.business.customer.service.CustomerContactService;
import com.moego.svc.business.customer.service.CustomerServiceV2;
import com.moego.svc.business.customer.utils.PhoneNumberHelper;
import com.moego.svc.business.customer.utils.TenantUtils;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@ActiveProfiles("unit-test")
@Transactional
public class CustomerMergeServiceTest {

    @Autowired
    CustomerServiceV2 customerService;

    @Autowired
    CustomerMergeService customerMergeService;

    @Autowired
    CustomerContactService customerContactService;

    @MockBean
    PhoneNumberHelper phoneNumberHelper;

    @Autowired
    CustomerMergeProcessRepo customerMergeProcessRepo;

    @Test
    void testMerge() {
        var tenant = TenantUtils.of(1L);

        Mockito.when(phoneNumberHelper.getPhoneNumberSuffixLength(tenant)).thenReturn(9);

        var createDef1 = BusinessCustomerCreateDef.newBuilder()
                .setFirstName("moego")
                .setLastName("test")
                .setEmail("<EMAIL>")
                .build();
        var createDef2 = BusinessCustomerCreateDef.newBuilder()
                .setFirstName("moego")
                .setLastName("test")
                .setEmail("<EMAIL>")
                .build();

        var customerId1 = customerService.createCustomer(tenant, null, createDef1);
        var customerId2 = customerService.createCustomer(tenant, null, createDef2);

        var mergeRelation = MergeRelationDef.newBuilder()
                .setTargetId(customerId1)
                .addSourceIds(customerId2)
                .build();
        customerMergeService.mergeCustomers(tenant, mergeRelation);

        var processes = customerMergeProcessRepo.listProcesses(List.of(customerId1, customerId2));
        assertThat(processes).isNotEmpty();
        assertThat(processes.size()).isEqualTo(1);
        var process = processes.get(0);
        assertThat(process.getTargetCustomerId()).isEqualTo(customerId1);
        assertThat(process.getCompanyId()).isEqualTo(tenant.getCompanyId());

        var customer1 = customerService.getCustomer(tenant, customerId1, GetOption.INCLUDE_DELETED);
        assertThat(customer1).isNotNull();
        assertThat(customer1.getStatus()).isEqualTo(CustomerStatus.NORMAL);

        var customer2 = customerService.getCustomer(tenant, customerId2, GetOption.INCLUDE_DELETED);
        assertThat(customer2).isNotNull();
        assertThat(customer2.getStatus()).isEqualTo(CustomerStatus.DELETED);

        var contactParam = new ListContactParam();
        contactParam.setCustomerId(customerId1);
        var contacts = customerContactService.listCustomerContact(contactParam);
        assertThat(contacts).isNotEmpty();
        assertThat(contacts.size()).isEqualTo(2);
    }
}
