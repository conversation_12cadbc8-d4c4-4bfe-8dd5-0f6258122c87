package com.moego.svc.business.customer.validator;

import static com.moego.svc.business.customer.validator.DefValidator.validateDef;

import com.google.type.Date;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressCreateDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetCreateDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetUpdateDef;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRecordCreateDef;
import com.moego.lib.common.exception.BizException;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class DefValidatorTest {

    @Test
    void testBusinessCustomerAddressCreateDef() {
        // 空地址校验不通过
        var request = BusinessCustomerAddressCreateDef.newBuilder().build();
        Assertions.assertThrows(BizException.class, () -> validateDef(request));
    }

    @Test
    void testBusinessCustomerPetCreateDef() {
        // 检查体重
        var request1 =
                BusinessCustomerPetCreateDef.newBuilder().setWeight("asdadas").build();
        Assertions.assertThrows(BizException.class, () -> validateDef(request1));

        // 检查日期
        var request2 = BusinessCustomerPetCreateDef.newBuilder()
                .setBirthday(
                        Date.newBuilder().setYear(2024).setMonth(0).setDay(0).build())
                .build();
        Assertions.assertThrows(BizException.class, () -> validateDef(request2));
    }

    @Test
    void testBusinessCustomerPetUpdateDef() {
        // 检查体重
        var request1 =
                BusinessCustomerPetUpdateDef.newBuilder().setWeight("asdadas").build();
        Assertions.assertThrows(BizException.class, () -> validateDef(request1));

        // 检查日期
        var request2 = BusinessCustomerPetUpdateDef.newBuilder()
                .setBirthday(
                        Date.newBuilder().setYear(2024).setMonth(0).setDay(0).build())
                .build();
        Assertions.assertThrows(BizException.class, () -> validateDef(request2));
    }

    @Test
    void testBusinessPetVaccineRecordCreateDef() {
        // 检查日期
        var request = BusinessPetVaccineRecordCreateDef.newBuilder()
                .setExpirationDate(
                        Date.newBuilder().setYear(2024).setMonth(0).setDay(0).build())
                .build();
        Assertions.assertThrows(BizException.class, () -> validateDef(request));
    }
}
