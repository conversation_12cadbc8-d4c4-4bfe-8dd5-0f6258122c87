package com.moego.svc.business.customer.service;

import static com.moego.svc.business.customer.repository.jooq.Tables.MOE_CUSTOMER_ADDRESS;

import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressUpdateDef;
import com.moego.lib.common.exception.BizException;
import com.moego.svc.business.customer.converter.CustomerAddressConverter;
import com.moego.svc.business.customer.enums.BooleanEnum;
import com.moego.svc.business.customer.enums.StatusEnum;
import org.jooq.DSLContext;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@ActiveProfiles("unit-test")
@Transactional
public class CustomerAddressServiceTest {

    @Autowired
    private CustomerAddressConverter customerAddressConverter;

    @Autowired
    private CustomerAddressService customerAddressService;

    @Autowired
    private DSLContext dsl;

    @Test
    void testPrimaryAddress() {
        var customerId = 1;
        var companyId = 2L;
        var businessId = 3;
        var address1 = "address1";
        var address2 = "address2";
        var city = "city";
        var state = "state";
        var country = "country";
        var zipcode = "zipcode";
        var lat = "1.1";
        var lng = "2.2";

        // 插入第一条 address，默认会设置为 primary
        var record1 = dsl.newRecord(MOE_CUSTOMER_ADDRESS)
                .setCustomerId(customerId)
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setAddress1(address1)
                .setAddress2(address2)
                .setCity(city)
                .setState(state)
                .setCountry(country)
                .setZipcode(zipcode)
                .setLat(lat)
                .setLng(lng);
        record1 = customerAddressService.createCustomerAddress(record1);
        var id1 = record1.getId();

        Assertions.assertEquals(customerId, record1.getCustomerId());
        Assertions.assertEquals(companyId, record1.getCompanyId());
        Assertions.assertEquals(businessId, record1.getBusinessId());
        Assertions.assertEquals(address1, record1.getAddress1());
        Assertions.assertEquals(address2, record1.getAddress2());
        Assertions.assertEquals(city, record1.getCity());
        Assertions.assertEquals(state, record1.getState());
        Assertions.assertEquals(country, record1.getCountry());
        Assertions.assertEquals(zipcode, record1.getZipcode());
        Assertions.assertEquals(lat, record1.getLat());
        Assertions.assertEquals(lng, record1.getLng());
        Assertions.assertEquals(StatusEnum.NORMAL.getValue(), record1.getStatus());
        Assertions.assertEquals(BooleanEnum.TRUE.getValue(), record1.getIsPrimary());

        // 插入第二条 address，不设置为 primary
        var record2 = dsl.newRecord(MOE_CUSTOMER_ADDRESS)
                .setCustomerId(customerId)
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setAddress1(address1);
        record2 = customerAddressService.createCustomerAddress(record2);
        var id2 = record2.getId();

        Assertions.assertEquals(customerId, record2.getCustomerId());
        Assertions.assertEquals(companyId, record2.getCompanyId());
        Assertions.assertEquals(businessId, record2.getBusinessId());
        Assertions.assertEquals(address1, record2.getAddress1());
        Assertions.assertEquals(StatusEnum.NORMAL.getValue(), record2.getStatus());
        Assertions.assertEquals(BooleanEnum.FALSE.getValue(), record2.getIsPrimary());

        // 插入第三条 address，设置为 primary，会将其它 address 设置为非 primary
        var record3 = dsl.newRecord(MOE_CUSTOMER_ADDRESS)
                .setCustomerId(customerId)
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setAddress1(address1)
                .setIsPrimary(BooleanEnum.TRUE.getValue());
        record3 = customerAddressService.createCustomerAddress(record3);
        var id3 = record3.getId();

        Assertions.assertEquals(customerId, record3.getCustomerId());
        Assertions.assertEquals(companyId, record3.getCompanyId());
        Assertions.assertEquals(businessId, record3.getBusinessId());
        Assertions.assertEquals(address1, record3.getAddress1());
        Assertions.assertEquals(StatusEnum.NORMAL.getValue(), record3.getStatus());
        Assertions.assertEquals(BooleanEnum.TRUE.getValue(), record3.getIsPrimary());

        var record1_new = customerAddressService.getCustomerAddress(null, id1, false);
        Assertions.assertEquals(BooleanEnum.FALSE.getValue(), record1_new.getIsPrimary());
        var record2_new = customerAddressService.getCustomerAddress(null, id2, false);
        Assertions.assertEquals(BooleanEnum.FALSE.getValue(), record2_new.getIsPrimary());

        // 更新第二条为 primary，会将其它 address 设置为非 primary
        record2_new.setIsPrimary(BooleanEnum.TRUE.getValue());
        customerAddressService.updateCustomerAddress(record2_new);

        record2_new = customerAddressService.getCustomerAddress(null, id2, false);
        Assertions.assertEquals(BooleanEnum.TRUE.getValue(), record2_new.getIsPrimary());

        record1_new = customerAddressService.getCustomerAddress(null, id1, false);
        Assertions.assertEquals(BooleanEnum.FALSE.getValue(), record1_new.getIsPrimary());

        var record3_new = customerAddressService.getCustomerAddress(null, id3, false);
        Assertions.assertEquals(BooleanEnum.FALSE.getValue(), record3_new.getIsPrimary());

        var primaryAddress = customerAddressService.getCustomerPrimaryAddress(null, customerId);
        Assertions.assertEquals(record2.getId(), primaryAddress.getId());

        var addresses = customerAddressService.listCustomerAddress(null, customerId);
        Assertions.assertEquals(3, addresses.size());

        // 删除第一条 address
        customerAddressService.deleteCustomerAddress(null, id1);
        var address1_new = customerAddressService.getCustomerAddress(null, id1, true);
        Assertions.assertEquals(StatusEnum.DELETED.getValue(), address1_new.getStatus());
        Assertions.assertThrowsExactly(
                BizException.class, () -> customerAddressService.getCustomerAddress(null, id1, false));

        addresses = customerAddressService.listCustomerAddress(null, customerId);
        Assertions.assertEquals(2, addresses.size());

        // 删除第二条 address (删除失败，因为是 primary address)
        Assertions.assertThrowsExactly(
                BizException.class, () -> customerAddressService.deleteCustomerAddress(null, id2));

        addresses = customerAddressService.listCustomerAddress(null, customerId);
        Assertions.assertEquals(2, addresses.size());
    }

    @Test
    void testUpdateAddress() {
        var customerId = 1;
        var companyId = 2L;
        var businessId = 3;
        var address1 = "address1";
        var address2 = "address2";
        var city = "city";
        var state = "state";
        var country = "country";
        var zipcode = "zipcode";
        var lat = "1.1";
        var lng = "2.2";

        // 插入 address
        var record = dsl.newRecord(MOE_CUSTOMER_ADDRESS)
                .setCustomerId(customerId)
                .setCompanyId(companyId)
                .setBusinessId(businessId)
                .setAddress1(address1)
                .setAddress2(address2)
                .setCity(city)
                .setState(state)
                .setCountry(country)
                .setZipcode(zipcode)
                .setLat(lat)
                .setLng(lng);
        record = customerAddressService.createCustomerAddress(record);

        Assertions.assertEquals(customerId, record.getCustomerId());
        Assertions.assertEquals(companyId, record.getCompanyId());
        Assertions.assertEquals(businessId, record.getBusinessId());
        Assertions.assertEquals(address1, record.getAddress1());
        Assertions.assertEquals(address2, record.getAddress2());
        Assertions.assertEquals(city, record.getCity());
        Assertions.assertEquals(state, record.getState());
        Assertions.assertEquals(country, record.getCountry());
        Assertions.assertEquals(zipcode, record.getZipcode());
        Assertions.assertEquals(lat, record.getLat());
        Assertions.assertEquals(lng, record.getLng());
        Assertions.assertEquals(StatusEnum.NORMAL.getValue(), record.getStatus());
        Assertions.assertEquals(BooleanEnum.TRUE.getValue(), record.getIsPrimary());

        // 只更新 address1
        var updateDef = BusinessCustomerAddressUpdateDef.newBuilder()
                .setAddress1(address1 + address1)
                .build();

        customerAddressConverter.updateRecord(updateDef, record);
        customerAddressService.updateCustomerAddress(record);

        var record_new = customerAddressService.getCustomerAddress(null, record.getId(), false);
        Assertions.assertEquals(address1 + address1, record_new.getAddress1());
        Assertions.assertEquals(address2, record_new.getAddress2());
        Assertions.assertEquals(city, record_new.getCity());
        Assertions.assertEquals(state, record_new.getState());
        Assertions.assertEquals(country, record_new.getCountry());
        Assertions.assertEquals(zipcode, record_new.getZipcode());
        // 经纬度应当被清空
        Assertions.assertEquals("", record_new.getLat());
        Assertions.assertEquals("", record_new.getLng());
        Assertions.assertEquals(StatusEnum.NORMAL.getValue(), record_new.getStatus());
        Assertions.assertEquals(BooleanEnum.TRUE.getValue(), record_new.getIsPrimary());
    }
}
