package com.moego.svc.business.customer.repo;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.when;

import com.moego.idl.models.organization.v1.Tenant;
import com.moego.svc.business.customer.enums.CustomerContactType;
import com.moego.svc.business.customer.params.ListContactParam;
import com.moego.svc.business.customer.repository.jooq.Tables;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoeCustomerContactRecord;
import com.moego.svc.business.customer.utils.PageParams;
import com.moego.svc.business.customer.utils.PhoneNumberHelper;
import java.util.List;
import org.jooq.DSLContext;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@ActiveProfiles("unit-test")
@Transactional
public class CustomerContactRepoTest {

    @Autowired
    private DSLContext dsl;

    @Autowired
    private CustomerContactRepo customerContactRepo;

    @MockBean
    private PhoneNumberHelper phoneNumberHelper;

    @Test
    void testListCustomerContact() {

        var c11 = dsl.newRecord(Tables.MOE_CUSTOMER_CONTACT)
                .setCustomerId(1)
                .setCompanyId(1L)
                .setPhoneNumber("*********0")
                .setType(CustomerContactType.MAIN.getValue());
        c11.insert();

        var c12 = dsl.newRecord(Tables.MOE_CUSTOMER_CONTACT)
                .setCustomerId(1)
                .setCompanyId(1L)
                .setPhoneNumber("*********")
                .setType(CustomerContactType.ADDITIONAL.getValue());
        c12.insert();

        var c21 = dsl.newRecord(Tables.MOE_CUSTOMER_CONTACT)
                .setCustomerId(2)
                .setCompanyId(1L)
                .setPhoneNumber("9876543210")
                .setType(CustomerContactType.MAIN.getValue());
        c21.insert();

        var c22 = dsl.newRecord(Tables.MOE_CUSTOMER_CONTACT)
                .setCustomerId(2)
                .setCompanyId(1L)
                .setPhoneNumber("0111*********")
                .setType(CustomerContactType.ADDITIONAL.getValue());
        c22.insert();

        // 根据 customer id 查询
        var param = new ListContactParam();
        param.setCustomerId(1L);
        var contacts = customerContactRepo.listCustomerContact(param);
        assertEquals(2, contacts.size());
        var ids = contacts.stream().map(MoeCustomerContactRecord::getId).toList();
        assertTrue(ids.containsAll(List.of(c11.getId(), c12.getId())));

        when(phoneNumberHelper.getPhoneNumberSuffixLength(Mockito.any())).thenReturn(9);

        // 根据 company id 查询
        param = new ListContactParam();
        param.setTenant(Tenant.newBuilder().setCompanyId(1).build());
        param.setPagination(new PageParams(1, 100));
        contacts = customerContactRepo.listCustomerContact(param);
        assertEquals(4, contacts.size());
        ids = contacts.stream().map(MoeCustomerContactRecord::getId).toList();
        assertTrue(ids.containsAll(List.of(c11.getId(), c12.getId(), c21.getId(), c22.getId())));

        // 根据 company id 查询, 过滤 type 和 phone number
        param = new ListContactParam();
        param.setTenant(Tenant.newBuilder().setCompanyId(1).build());
        param.setPhoneNumber("0*********");
        param.setTypes(List.of(CustomerContactType.ADDITIONAL));
        contacts = customerContactRepo.listCustomerContact(param);
        assertEquals(2, contacts.size());
        ids = contacts.stream().map(MoeCustomerContactRecord::getId).toList();
        assertTrue(ids.containsAll(List.of(c12.getId(), c22.getId())));
    }
}
