/*
 * @since 2023-06-02 21:16:21
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.svc.business.customer.utils;

import java.util.ArrayList;
import java.util.Collection;

public class TestList<T> extends ArrayList<T> {

    public TestList() {
        super();
    }

    public TestList(Collection<? extends T> c) {
        super(c);
    }

    public T first() {
        return get(0);
    }

    public T first(int index) {
        return get(index);
    }

    public T second() {
        return get(1);
    }

    public T last() {
        return get(size() - 1);
    }

    public T last(int index) {
        return get(size() - index - 1);
    }
}
