package com.moego.svc.business.customer.service;

import com.google.type.CalendarPeriod;
import com.moego.idl.utils.v1.TimePeriod;
import com.moego.svc.business.customer.converter.DefaultPreferredFrequencyConverter;
import com.moego.svc.business.customer.enums.DefaultPreferredFrequencyType;
import com.moego.svc.business.customer.repo.DefaultPreferredFrequencyRepo;
import com.moego.svc.business.customer.utils.TenantUtils;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@ActiveProfiles("unit-test")
@Transactional
public class DefaultPreferredFrequencyServiceTest {

    @Autowired
    private DefaultPreferredFrequencyRepo defaultPreferredFrequencyRepo;

    @Autowired
    private DefaultPreferredFrequencyService defaultPreferredFrequencyService;

    @Test
    void testUpdateSetting() throws InterruptedException {
        var tenant = TenantUtils.of(1L);
        var type = DefaultPreferredFrequencyType.GROOMING;

        // 初始时没有 frequency 记录，repo 查到 null
        var noFrequency = defaultPreferredFrequencyRepo.getDefaultPreferredFrequency(tenant, type);
        Assertions.assertNull(noFrequency);

        // 没有记录时，service 返回默认值
        var frequency = defaultPreferredFrequencyService.getDefaultPreferredFrequency(tenant, type);
        checkFrequency(frequency, DefaultPreferredFrequencyConverter.DEFAULT_GROOMING_FREQUENCY);

        // 更新 frequency 记录
        var oneMonth = TimePeriod.newBuilder()
                .setPeriod(CalendarPeriod.MONTH)
                .setValue(1)
                .build();
        defaultPreferredFrequencyService.upsertDefaultPreferredFrequency(tenant, type, oneMonth);

        // 更新后，repo 查到记录
        var oneMonthFrequency = defaultPreferredFrequencyRepo.getDefaultPreferredFrequency(tenant, type);
        Assertions.assertNotNull(oneMonthFrequency);
        Assertions.assertNotNull(oneMonthFrequency.getCreatedAt());
        Assertions.assertNotNull(oneMonthFrequency.getUpdatedAt());
        Assertions.assertEquals(oneMonthFrequency.getCreatedAt(), oneMonthFrequency.getUpdatedAt());

        // 更新后，service 返回更新后的值
        frequency = defaultPreferredFrequencyService.getDefaultPreferredFrequency(tenant, type);
        checkFrequency(frequency, oneMonth);

        // 再次更新 frequency 记录
        TimeUnit.SECONDS.sleep(1);
        var twoMonth = TimePeriod.newBuilder()
                .setPeriod(CalendarPeriod.MONTH)
                .setValue(2)
                .build();
        defaultPreferredFrequencyService.upsertDefaultPreferredFrequency(tenant, type, twoMonth);

        // 再次更新后，repo 查到记录
        var twoMonthFrequencyRecord = defaultPreferredFrequencyRepo.getDefaultPreferredFrequency(tenant, type);
        Assertions.assertNotNull(twoMonthFrequencyRecord);
        // 检查 updatedAt 是否更新
        Assertions.assertTrue(twoMonthFrequencyRecord.getUpdatedAt().isAfter(oneMonthFrequency.getUpdatedAt()));

        // 再次更新后，service 返回更新后的值
        frequency = defaultPreferredFrequencyService.getDefaultPreferredFrequency(tenant, type);
        checkFrequency(frequency, twoMonth);

        // 相同参数再次更新，不会更新 updatedAt
        TimeUnit.SECONDS.sleep(1);
        defaultPreferredFrequencyService.upsertDefaultPreferredFrequency(tenant, type, twoMonth);

        var twoMonthFrequencyRecordAgain = defaultPreferredFrequencyRepo.getDefaultPreferredFrequency(tenant, type);
        Assertions.assertNotNull(twoMonthFrequencyRecordAgain);
        Assertions.assertEquals(twoMonthFrequencyRecord, twoMonthFrequencyRecordAgain);
    }

    private void checkFrequency(TimePeriod frequency, TimePeriod expected) {
        Assertions.assertNotNull(frequency);
        Assertions.assertEquals(frequency.getPeriod(), expected.getPeriod());
        Assertions.assertEquals(frequency.getValue(), expected.getValue());
    }
}
