package com.moego.svc.business.customer.service.customer_merge.grouper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import com.moego.idl.models.customer.v1.PetType;
import org.junit.jupiter.api.Test;

public class PetDuplicationCheckDTOTest {

    @Test
    void testGetPetKey() {
        // null pet name, type and breed
        var dto = new PetDuplicationCheckDTO();
        dto.setPetTypeId(PetType.PET_TYPE_DOG_VALUE);
        assertEquals("", dto.getPetKey());

        // empty pet name and breed
        dto.setPetName("");
        dto.setBreed("");
        assertEquals("", dto.getPetKey());

        // blank pet name and breed
        dto.setPetName("  \t\n");
        dto.setBreed("  \t\n");
        assertEquals("", dto.getPetKey());

        // normal pet name and breed
        dto.setPetName("max");
        dto.setBreed("breed");
        assertEquals("max-1-breed", dto.getPetKey());

        // case insensitive
        dto.setPetName("Max");
        dto.setBreed("Breed");
        assertEquals("max-1-breed", dto.getPetKey());

        // trim
        dto.setPetName("  Max  ");
        dto.setBreed("  Breed  ");
        assertEquals("max-1-breed", dto.getPetKey());
    }
}
