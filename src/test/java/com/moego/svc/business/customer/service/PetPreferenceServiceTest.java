package com.moego.svc.business.customer.service;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.business_customer.v1.BusinessCustomerPetCreateDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetPreferenceUpdateDef;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.svc.business.customer.dto.CustomerDTO;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@ActiveProfiles("unit-test")
@Transactional
public class PetPreferenceServiceTest {

    @Autowired
    PetService petService;

    @Autowired
    PetPreferenceService petPreferenceService;

    @Test
    void testGetPetPreference() {
        var tenant = Tenant.newBuilder().setCompanyId(1111L).build();

        var customer = new CustomerDTO();
        customer.setId(111L);
        customer.setCompanyId(tenant.getCompanyId());
        customer.setPreferredBusinessId(1111L);

        var createDef = BusinessCustomerPetCreateDef.newBuilder()
                .setPetName("Mini")
                .setPetType(PetType.PET_TYPE_DOG)
                .setBreed("French Bulldog")
                .build();
        long id = petService.createPet(customer, createDef);

        var preference = petPreferenceService.getPreference(tenant, id);
        assertThat(preference).isNotNull();
        assertThat(preference.getPetId()).isEqualTo(id);
        assertThat(preference.isEnableVaccineExpiryNotification()).isTrue();

        // disable expiry notification
        var updateDef = BusinessCustomerPetPreferenceUpdateDef.newBuilder()
                .setEnableVaccineExpiryNotification(false)
                .build();
        petPreferenceService.updatePreference(tenant, id, updateDef);

        preference = petPreferenceService.getPreference(tenant, id);
        assertThat(preference).isNotNull();
        assertThat(preference.getPetId()).isEqualTo(id);
        assertThat(preference.isEnableVaccineExpiryNotification()).isFalse();

        // enable expiry notification
        updateDef = BusinessCustomerPetPreferenceUpdateDef.newBuilder()
                .setEnableVaccineExpiryNotification(true)
                .build();
        petPreferenceService.updatePreference(tenant, id, updateDef);

        preference = petPreferenceService.getPreference(tenant, id);
        assertThat(preference).isNotNull();
        assertThat(preference.getPetId()).isEqualTo(id);
        assertThat(preference.isEnableVaccineExpiryNotification()).isTrue();
    }
}
