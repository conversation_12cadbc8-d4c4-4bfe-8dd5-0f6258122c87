/*
 * @since 2023-06-06 20:05:49
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.svc.business.customer.utils;

import static java.time.ZoneOffset.UTC;
import static org.mockito.Answers.CALLS_REAL_METHODS;
import static org.mockito.Mockito.when;

import java.time.Instant;
import java.time.LocalDateTime;
import java.util.TimeZone;
import org.junit.jupiter.api.BeforeEach;
import org.springframework.boot.test.mock.mockito.MockBean;

public abstract class MockStubs {

    @MockBean(answer = CALLS_REAL_METHODS)
    protected TimeUtils timeUtils;

    @BeforeEach
    void beforeEach() {
        TimeZone.setDefault(TimeZone.getTimeZone(UTC));
        initTime();
    }

    protected void initTime() {
        when(timeUtils.now()).thenAnswer(invocation -> {
            var now = Instant.now();
            return LocalDateTime.ofEpochSecond(now.getEpochSecond(), now.getNano() / 1000 * 1000, UTC);
        });
    }
}
