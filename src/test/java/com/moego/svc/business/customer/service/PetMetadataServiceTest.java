package com.moego.svc.business.customer.service;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.business_customer.v1.BusinessPetMetadataName;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.business.customer.repository.jooq.tables.records.PetMetadataRecord;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

@SpringBootTest
public class PetMetadataServiceTest {

    @Test
    void testBuildInitRecord() {
        var companyId = 1L;

        // 调用buildInitRecord方法
        var initRecords = PetMetadataService.buildInitRecord(companyId);

        // 验证返回的Map不为空
        assertThat(initRecords).isNotNull();
        assertThat(initRecords).isNotEmpty();

        // 验证包含所有预期的元数据类型
        assertThat(initRecords)
                .containsKeys(
                        BusinessPetMetadataName.FEEDING_SCHEDULE,
                        BusinessPetMetadataName.FEEDING_UNIT,
                        BusinessPetMetadataName.FEEDING_TYPE,
                        BusinessPetMetadataName.FEEDING_SOURCE,
                        BusinessPetMetadataName.FEEDING_INSTRUCTION,
                        BusinessPetMetadataName.MEDICATION_SCHEDULE,
                        BusinessPetMetadataName.MEDICATION_UNIT,
                        BusinessPetMetadataName.FEEDING_FEEDBACK,
                        BusinessPetMetadataName.MEDICATION_FEEDBACK);

        // 验证每个类型都有对应的记录列表
        initRecords.forEach((metadataName, records) -> {
            assertThat(records).isNotNull();
            assertThat(records).isNotEmpty();

            // 验证每个记录的基本字段
            records.forEach(record -> {
                assertThat(record.getCompanyId()).isEqualTo(companyId);
                assertThat(record.getMetadataName()).isNotNull();
                assertThat(record.getMetadataValue()).isNotNull();
                assertThat(record.getSort()).isNotNull();
                assertThat(record.getSort()).isGreaterThan(0);
            });
        });
    }

    @Test
    void testBuildInitRecordFeedingSchedule() {
        var companyId = 1L;
        var initRecords = PetMetadataService.buildInitRecord(companyId);

        // 验证FEEDING_SCHEDULE的记录
        var feedingScheduleRecords = initRecords.get(BusinessPetMetadataName.FEEDING_SCHEDULE);
        assertThat(feedingScheduleRecords).hasSize(3);

        // 验证AM记录 (540分钟 = 9:00 AM)
        var amRecord = feedingScheduleRecords.stream()
                .filter(r -> "540".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(amRecord).isNotNull();
        assertThat(amRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(amRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_SCHEDULE_VALUE);
        assertThat(amRecord.getSort()).isEqualTo(3);
        assertThat(amRecord.getExtraJson()).isNotNull();

        // 验证extraJson包含label
        var amExtraJson = JsonUtil.toBean(amRecord.getExtraJson().data(), Map.class);
        assertThat(amExtraJson).containsEntry("label", "AM");

        // 验证Noon记录 (720分钟 = 12:00 PM)
        var noonRecord = feedingScheduleRecords.stream()
                .filter(r -> "720".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(noonRecord).isNotNull();
        assertThat(noonRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(noonRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_SCHEDULE_VALUE);
        assertThat(noonRecord.getSort()).isEqualTo(2);

        var noonExtraJson = JsonUtil.toBean(noonRecord.getExtraJson().data(), Map.class);
        assertThat(noonExtraJson).containsEntry("label", "Noon");

        // 验证PM记录 (1080分钟 = 6:00 PM)
        var pmRecord = feedingScheduleRecords.stream()
                .filter(r -> "1080".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(pmRecord).isNotNull();
        assertThat(pmRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(pmRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_SCHEDULE_VALUE);
        assertThat(pmRecord.getSort()).isEqualTo(1);

        var pmExtraJson = JsonUtil.toBean(pmRecord.getExtraJson().data(), Map.class);
        assertThat(pmExtraJson).containsEntry("label", "PM");
    }

    @Test
    void testBuildInitRecordFeedingUnit() {
        var companyId = 1L;
        var initRecords = PetMetadataService.buildInitRecord(companyId);

        // 验证FEEDING_UNIT的记录
        var feedingUnitRecords = initRecords.get(BusinessPetMetadataName.FEEDING_UNIT);
        assertThat(feedingUnitRecords).hasSize(2);

        // 验证Oz记录
        var ozRecord = feedingUnitRecords.stream()
                .filter(r -> "Oz".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(ozRecord).isNotNull();
        assertThat(ozRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(ozRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_UNIT_VALUE);
        assertThat(ozRecord.getSort()).isEqualTo(2);
        assertThat(ozRecord.getExtraJson()).isNull();

        // 验证Cup记录
        var cupRecord = feedingUnitRecords.stream()
                .filter(r -> "Cup".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(cupRecord).isNotNull();
        assertThat(cupRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(cupRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_UNIT_VALUE);
        assertThat(cupRecord.getSort()).isEqualTo(1);
        assertThat(cupRecord.getExtraJson()).isNull();
    }

    @Test
    void testBuildInitRecordFeedingType() {
        var companyId = 1L;
        var initRecords = PetMetadataService.buildInitRecord(companyId);

        // 验证FEEDING_TYPE的记录
        var feedingTypeRecords = initRecords.get(BusinessPetMetadataName.FEEDING_TYPE);
        assertThat(feedingTypeRecords).hasSize(2);

        // 验证Wet food记录
        var wetFoodRecord = feedingTypeRecords.stream()
                .filter(r -> "Wet food".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(wetFoodRecord).isNotNull();
        assertThat(wetFoodRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(wetFoodRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_TYPE_VALUE);
        assertThat(wetFoodRecord.getSort()).isEqualTo(2);

        // 验证Dry food记录
        var dryFoodRecord = feedingTypeRecords.stream()
                .filter(r -> "Dry food".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(dryFoodRecord).isNotNull();
        assertThat(dryFoodRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(dryFoodRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_TYPE_VALUE);
        assertThat(dryFoodRecord.getSort()).isEqualTo(1);
    }

    @Test
    void testBuildInitRecordFeedingSource() {
        var companyId = 1L;
        var initRecords = PetMetadataService.buildInitRecord(companyId);

        // 验证FEEDING_SOURCE的记录
        var feedingSourceRecords = initRecords.get(BusinessPetMetadataName.FEEDING_SOURCE);
        assertThat(feedingSourceRecords).hasSize(2);

        // 验证Owner provide记录
        var ownerProvideRecord = feedingSourceRecords.stream()
                .filter(r -> "Owner provide".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(ownerProvideRecord).isNotNull();
        assertThat(ownerProvideRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(ownerProvideRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_SOURCE_VALUE);
        assertThat(ownerProvideRecord.getSort()).isEqualTo(2);

        // 验证House provide记录
        var houseProvideRecord = feedingSourceRecords.stream()
                .filter(r -> "House provide".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(houseProvideRecord).isNotNull();
        assertThat(houseProvideRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(houseProvideRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_SOURCE_VALUE);
        assertThat(houseProvideRecord.getSort()).isEqualTo(1);
    }

    @Test
    void testBuildInitRecordFeedingInstruction() {
        var companyId = 1L;
        var initRecords = PetMetadataService.buildInitRecord(companyId);

        // 验证FEEDING_INSTRUCTION的记录
        var feedingInstructionRecords = initRecords.get(BusinessPetMetadataName.FEEDING_INSTRUCTION);
        assertThat(feedingInstructionRecords).hasSize(2);

        // 验证Free feed记录
        var freeFeedRecord = feedingInstructionRecords.stream()
                .filter(r -> "Free feed".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(freeFeedRecord).isNotNull();
        assertThat(freeFeedRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(freeFeedRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_INSTRUCTION_VALUE);
        assertThat(freeFeedRecord.getSort()).isEqualTo(2);

        // 验证Feed alone记录
        var feedAloneRecord = feedingInstructionRecords.stream()
                .filter(r -> "Feed alone".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(feedAloneRecord).isNotNull();
        assertThat(feedAloneRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(feedAloneRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_INSTRUCTION_VALUE);
        assertThat(feedAloneRecord.getSort()).isEqualTo(1);
    }

    @Test
    void testBuildInitRecordMedicationSchedule() {
        var companyId = 1L;
        var initRecords = PetMetadataService.buildInitRecord(companyId);

        // 验证MEDICATION_SCHEDULE的记录
        var medicationScheduleRecords = initRecords.get(BusinessPetMetadataName.MEDICATION_SCHEDULE);
        assertThat(medicationScheduleRecords).hasSize(3);

        // 验证AM记录 (540分钟 = 9:00 AM)
        var amRecord = medicationScheduleRecords.stream()
                .filter(r -> "540".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(amRecord).isNotNull();
        assertThat(amRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(amRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.MEDICATION_SCHEDULE_VALUE);
        assertThat(amRecord.getSort()).isEqualTo(3);
        assertThat(amRecord.getExtraJson()).isNotNull();

        // 验证extraJson包含label
        var amExtraJson = JsonUtil.toBean(amRecord.getExtraJson().data(), Map.class);
        assertThat(amExtraJson).containsEntry("label", "AM");

        // 验证Noon记录 (720分钟 = 12:00 PM)
        var noonRecord = medicationScheduleRecords.stream()
                .filter(r -> "720".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(noonRecord).isNotNull();
        assertThat(noonRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(noonRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.MEDICATION_SCHEDULE_VALUE);
        assertThat(noonRecord.getSort()).isEqualTo(2);

        var noonExtraJson = JsonUtil.toBean(noonRecord.getExtraJson().data(), Map.class);
        assertThat(noonExtraJson).containsEntry("label", "Noon");

        // 验证PM记录 (1080分钟 = 6:00 PM)
        var pmRecord = medicationScheduleRecords.stream()
                .filter(r -> "1080".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(pmRecord).isNotNull();
        assertThat(pmRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(pmRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.MEDICATION_SCHEDULE_VALUE);
        assertThat(pmRecord.getSort()).isEqualTo(1);

        var pmExtraJson = JsonUtil.toBean(pmRecord.getExtraJson().data(), Map.class);
        assertThat(pmExtraJson).containsEntry("label", "PM");
    }

    @Test
    void testBuildInitRecordMedicationUnit() {
        var companyId = 1L;
        var initRecords = PetMetadataService.buildInitRecord(companyId);

        // 验证MEDICATION_UNIT的记录
        var medicationUnitRecords = initRecords.get(BusinessPetMetadataName.MEDICATION_UNIT);
        assertThat(medicationUnitRecords).hasSize(2);

        // 验证Oz记录
        var ozRecord = medicationUnitRecords.stream()
                .filter(r -> "Oz".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(ozRecord).isNotNull();
        assertThat(ozRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(ozRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.MEDICATION_UNIT_VALUE);
        assertThat(ozRecord.getSort()).isEqualTo(2);
        assertThat(ozRecord.getExtraJson()).isNull();

        // 验证Cup记录
        var cupRecord = medicationUnitRecords.stream()
                .filter(r -> "Cup".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(cupRecord).isNotNull();
        assertThat(cupRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(cupRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.MEDICATION_UNIT_VALUE);
        assertThat(cupRecord.getSort()).isEqualTo(1);
        assertThat(cupRecord.getExtraJson()).isNull();
    }

    @Test
    void testBuildInitRecordFeedingFeedback() {
        var companyId = 1L;
        var initRecords = PetMetadataService.buildInitRecord(companyId);

        // 验证FEEDING_FEEDBACK的记录
        var feedingFeedbackRecords = initRecords.get(BusinessPetMetadataName.FEEDING_FEEDBACK);
        assertThat(feedingFeedbackRecords).hasSize(5);

        // 验证🍽️ Ate all (100%)记录
        var ateAllRecord = feedingFeedbackRecords.stream()
                .filter(r -> "🍽️ Ate all (100%)".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(ateAllRecord).isNotNull();
        assertThat(ateAllRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(ateAllRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_FEEDBACK_VALUE);
        assertThat(ateAllRecord.getSort()).isEqualTo(5);

        // 验证😋 Ate most (70%)记录
        var ateMostRecord = feedingFeedbackRecords.stream()
                .filter(r -> "😋 Ate most (70%)".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(ateMostRecord).isNotNull();
        assertThat(ateMostRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(ateMostRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_FEEDBACK_VALUE);
        assertThat(ateMostRecord.getSort()).isEqualTo(4);

        // 验证🥄 Ate some (50%)记录
        var ateSomeRecord = feedingFeedbackRecords.stream()
                .filter(r -> "🥄 Ate some (50%)".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(ateSomeRecord).isNotNull();
        assertThat(ateSomeRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(ateSomeRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_FEEDBACK_VALUE);
        assertThat(ateSomeRecord.getSort()).isEqualTo(3);

        // 验证👅 Ate a little (25%)记录
        var ateALittleRecord = feedingFeedbackRecords.stream()
                .filter(r -> "👅 Ate a little (25%)".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(ateALittleRecord).isNotNull();
        assertThat(ateALittleRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(ateALittleRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_FEEDBACK_VALUE);
        assertThat(ateALittleRecord.getSort()).isEqualTo(2);

        // 验证❌ Refused to eat (0%)记录
        var refusedRecord = feedingFeedbackRecords.stream()
                .filter(r -> "❌ Refused to eat (0%)".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(refusedRecord).isNotNull();
        assertThat(refusedRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(refusedRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.FEEDING_FEEDBACK_VALUE);
        assertThat(refusedRecord.getSort()).isEqualTo(1);
    }

    @Test
    void testBuildInitRecordMedicationFeedback() {
        var companyId = 1L;
        var initRecords = PetMetadataService.buildInitRecord(companyId);

        // 验证MEDICATION_FEEDBACK的记录
        var medicationFeedbackRecords = initRecords.get(BusinessPetMetadataName.MEDICATION_FEEDBACK);
        assertThat(medicationFeedbackRecords).hasSize(4);

        // 验证💊 Took all on time记录
        var tookAllRecord = medicationFeedbackRecords.stream()
                .filter(r -> "💊 Took all on time".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(tookAllRecord).isNotNull();
        assertThat(tookAllRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(tookAllRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.MEDICATION_FEEDBACK_VALUE);
        assertThat(tookAllRecord.getSort()).isEqualTo(4);

        // 验证🙂 Took partial dose记录
        var tookPartialRecord = medicationFeedbackRecords.stream()
                .filter(r -> "🙂 Took partial dose".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(tookPartialRecord).isNotNull();
        assertThat(tookPartialRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(tookPartialRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.MEDICATION_FEEDBACK_VALUE);
        assertThat(tookPartialRecord.getSort()).isEqualTo(3);

        // 验证🧁 Administered in treat记录
        var administeredInTreatRecord = medicationFeedbackRecords.stream()
                .filter(r -> "🧁 Administered in treat".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(administeredInTreatRecord).isNotNull();
        assertThat(administeredInTreatRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(administeredInTreatRecord.getMetadataName())
                .isEqualTo(BusinessPetMetadataName.MEDICATION_FEEDBACK_VALUE);
        assertThat(administeredInTreatRecord.getSort()).isEqualTo(2);

        // 验证🙋‍♀️ Need assistance记录
        var needAssistanceRecord = medicationFeedbackRecords.stream()
                .filter(r -> "🙋‍♀️ Need assistance".equals(r.getMetadataValue()))
                .findFirst()
                .orElse(null);
        assertThat(needAssistanceRecord).isNotNull();
        assertThat(needAssistanceRecord.getCompanyId()).isEqualTo(companyId);
        assertThat(needAssistanceRecord.getMetadataName()).isEqualTo(BusinessPetMetadataName.MEDICATION_FEEDBACK_VALUE);
        assertThat(needAssistanceRecord.getSort()).isEqualTo(1);
    }

    @Test
    void testBuildInitRecordWithDifferentCompanyId() {
        var companyId1 = 1L;
        var companyId2 = 2L;

        // 测试不同companyId生成的记录
        var initRecords1 = PetMetadataService.buildInitRecord(companyId1);
        var initRecords2 = PetMetadataService.buildInitRecord(companyId2);

        // 验证两个Map的结构相同
        assertThat(initRecords1.keySet()).isEqualTo(initRecords2.keySet());

        // 验证每个类型的记录数量相同
        initRecords1.forEach((metadataName, records1) -> {
            var records2 = initRecords2.get(metadataName);
            assertThat(records1.size()).isEqualTo(records2.size());

            // 验证companyId不同
            records1.forEach(record -> assertThat(record.getCompanyId()).isEqualTo(companyId1));
            records2.forEach(record -> assertThat(record.getCompanyId()).isEqualTo(companyId2));
        });
    }

    @Test
    void testBuildInitRecordSortValues() {
        var companyId = 1L;
        var initRecords = PetMetadataService.buildInitRecord(companyId);

        // 验证每个类型的记录都有正确的排序值
        initRecords.forEach((metadataName, records) -> {
            var sortValues =
                    records.stream().map(PetMetadataRecord::getSort).sorted().toList();

            // 验证排序值从1开始连续
            for (int i = 0; i < sortValues.size(); i++) {
                assertThat(sortValues.get(i)).isEqualTo(i + 1);
            }
        });
    }
}
