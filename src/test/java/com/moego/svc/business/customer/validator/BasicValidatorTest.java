package com.moego.svc.business.customer.validator;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;

import com.google.type.CalendarPeriod;
import com.google.type.Date;
import com.google.type.LatLng;
import com.google.type.TimeOfDay;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.idl.utils.v1.TimeOfDayInterval;
import com.moego.idl.utils.v1.TimePeriod;
import com.moego.lib.common.exception.BizException;
import java.util.List;
import org.junit.jupiter.api.Test;

public class BasicValidatorTest {

    @Test
    void testValidatePhoneNumberFormat() {
        // valid
        var validValues = List.of("123", "000", "1234", "12345678901234567890");
        validValues.forEach(value -> assertDoesNotThrow(() -> BasicValidator.validatePhoneNumberFormat(value)));

        // invalid
        var invalidValues = List.of("", "12", "12 ", "12\t", "2131a", "2eqwkjdsc.@#", "123456789012345678901");
        invalidValues.forEach(
                value -> assertThrows(BizException.class, () -> BasicValidator.validatePhoneNumberFormat(value)));
    }

    @Test
    void testValidateEmailFormat() {
        // valid
        var validValues = List.of("a@a", "<EMAIL>", "<EMAIL>");
        validValues.forEach(value -> assertDoesNotThrow(() -> BasicValidator.validateEmailFormat((value))));

        // invalid
        var invalidValues = List.of("", "12", "12 ", "12\t", "2131a@", "@example.com");
        invalidValues.forEach(
                value -> assertThrows(BizException.class, () -> BasicValidator.validateEmailFormat(value)));
    }

    @Test
    void testValidateNonNegativeDouble() {
        // valid
        var validValues = List.of("0", "0.0", "0.1", "1", "1e10", "1e-10");
        validValues.forEach(value -> assertDoesNotThrow(() -> BasicValidator.validateNonNegativeDouble((value))));

        // invalid
        var invalidValues = List.of("  ", "abc", "-1", "-1.0", "1.1.1", "1a", "1e", "1e0.1");
        invalidValues.forEach(
                value -> assertThrows(BizException.class, () -> BasicValidator.validateNonNegativeDouble(value)));
    }

    @Test
    void testValidateTimePeriod() {
        // valid
        var validValues = List.of(
                TimePeriod.newBuilder()
                        .setPeriod(CalendarPeriod.DAY)
                        .setValue(1)
                        .build(),
                TimePeriod.newBuilder()
                        .setPeriod(CalendarPeriod.WEEK)
                        .setValue(2)
                        .build(),
                TimePeriod.newBuilder()
                        .setPeriod(CalendarPeriod.MONTH)
                        .setValue(3)
                        .build());
        validValues.forEach(value -> assertDoesNotThrow(() -> BasicValidator.validateTimePeriod((value))));

        // invalid
        var invalidValues = List.of(
                TimePeriod.getDefaultInstance(),
                TimePeriod.newBuilder()
                        .setPeriod(CalendarPeriod.CALENDAR_PERIOD_UNSPECIFIED)
                        .setValue(1)
                        .build(),
                TimePeriod.newBuilder()
                        .setPeriod(CalendarPeriod.DAY)
                        .setValue(-2)
                        .build(),
                TimePeriod.newBuilder()
                        .setPeriod(CalendarPeriod.WEEK)
                        .setValue(0)
                        .build(),
                TimePeriod.newBuilder().setPeriod(CalendarPeriod.MONTH).build(),
                TimePeriod.newBuilder().setValue(1).build());
        invalidValues.forEach(
                value -> assertThrows(BizException.class, () -> BasicValidator.validateTimePeriod(value)));
    }

    @Test
    void testValidateTimeOfDayInterval() {

        var t0 = TimeOfDay.newBuilder().setHours(0).setMinutes(0).setSeconds(0);
        var t1 = TimeOfDay.newBuilder().setHours(1).setMinutes(0).setSeconds(0);
        var t2_30 = TimeOfDay.newBuilder().setHours(2).setMinutes(30).setSeconds(0);
        var t3_40_50 = TimeOfDay.newBuilder().setHours(3).setMinutes(40).setSeconds(50);
        var t24 = TimeOfDay.newBuilder().setHours(24).setMinutes(0).setSeconds(0);

        // valid
        var validValues = List.of(
                TimeOfDayInterval.newBuilder().setStart(t0).setEnd(t1).build(),
                TimeOfDayInterval.newBuilder().setStart(t0).setEnd(t24).build(),
                TimeOfDayInterval.newBuilder().setStart(t1).setEnd(t2_30).build(),
                TimeOfDayInterval.newBuilder().setStart(t2_30).setEnd(t3_40_50).build());
        validValues.forEach(value -> assertDoesNotThrow(() -> BasicValidator.validateTimeOfDayInterval((value))));

        // invalid
        var invalidValues = List.of(
                TimeOfDayInterval.getDefaultInstance(),
                TimeOfDayInterval.newBuilder().setStart(t1).build(),
                TimeOfDayInterval.newBuilder().setEnd(t1).build(),
                TimeOfDayInterval.newBuilder().setStart(t1).setEnd(t1).build(),
                TimeOfDayInterval.newBuilder().setStart(t1).setEnd(t0).build(),
                TimeOfDayInterval.newBuilder().setStart(t2_30).setEnd(t2_30).build(),
                TimeOfDayInterval.newBuilder().setStart(t2_30).setEnd(t1).build(),
                TimeOfDayInterval.newBuilder()
                        .setStart(t3_40_50)
                        .setEnd(t3_40_50)
                        .build(),
                TimeOfDayInterval.newBuilder().setStart(t3_40_50).setEnd(t2_30).build(),
                TimeOfDayInterval.newBuilder().setStart(t24).setEnd(t0).build());
        invalidValues.forEach(
                value -> assertThrows(BizException.class, () -> BasicValidator.validateTimeOfDayInterval(value)));
    }

    @Test
    void testValidateTimeOfDay() {
        // valid
        var validValues = List.of(
                TimeOfDay.newBuilder().setHours(0).setMinutes(0).setSeconds(0).build(),
                TimeOfDay.newBuilder().setHours(0).setMinutes(59).setSeconds(0).build(),
                TimeOfDay.newBuilder().setHours(0).setMinutes(0).setSeconds(59).build(),
                TimeOfDay.newBuilder()
                        .setHours(23)
                        .setMinutes(59)
                        .setSeconds(59)
                        .build(),
                TimeOfDay.newBuilder().setHours(24).setMinutes(0).setSeconds(0).build());
        validValues.forEach(value -> assertDoesNotThrow(() -> BasicValidator.validateTimeOfDay((value))));

        // invalid
        var invalidValues = List.of(
                TimeOfDay.newBuilder().setHours(-1).setMinutes(0).setSeconds(0).build(),
                TimeOfDay.newBuilder().setHours(0).setMinutes(-1).setSeconds(0).build(),
                TimeOfDay.newBuilder().setHours(0).setMinutes(0).setSeconds(-1).build(),
                TimeOfDay.newBuilder().setHours(25).setMinutes(0).setSeconds(0).build(),
                TimeOfDay.newBuilder().setHours(0).setMinutes(60).setSeconds(0).build(),
                TimeOfDay.newBuilder().setHours(0).setMinutes(0).setSeconds(60).build(),
                TimeOfDay.newBuilder().setHours(24).setMinutes(1).setSeconds(1).build());
        invalidValues.forEach(value -> assertThrows(BizException.class, () -> BasicValidator.validateTimeOfDay(value)));
    }

    @Test
    void testValidateLatLng() {
        // valid
        var validValues = List.of(
                LatLng.newBuilder().setLatitude(0).setLongitude(0).build(),
                LatLng.newBuilder().setLatitude(20).setLongitude(60).build(),
                LatLng.newBuilder().setLatitude(-90).setLongitude(-180).build(),
                LatLng.newBuilder().setLatitude(90).setLongitude(180).build(),
                LatLng.newBuilder().setLatitude(-90.0).setLongitude(-180.0).build(),
                LatLng.newBuilder().setLatitude(90.0).setLongitude(180.0).build());
        validValues.forEach(value -> assertDoesNotThrow(() -> BasicValidator.validateLatLng((value))));

        // invalid
        var invalidValues = List.of(
                LatLng.newBuilder().setLatitude(-90.1).setLongitude(-180).build(),
                LatLng.newBuilder().setLatitude(-90).setLongitude(-180.1).build(),
                LatLng.newBuilder().setLatitude(90.1).setLongitude(180).build(),
                LatLng.newBuilder().setLatitude(90).setLongitude(180.1).build(),
                LatLng.newBuilder().setLatitude(-90.1).setLongitude(-180.1).build());
        invalidValues.forEach(value -> assertThrows(BizException.class, () -> BasicValidator.validateLatLng(value)));
    }

    @Test
    void testValidateDate() {
        // valid
        var validValues = List.of(
                Date.newBuilder().setYear(0).setMonth(0).setDay(0).build(),
                Date.newBuilder().setYear(2024).setMonth(1).setDay(1).build(),
                Date.newBuilder().setYear(2024).setMonth(2).setDay(29).build(),
                Date.newBuilder().setYear(2024).setMonth(12).setDay(31).build());
        validValues.forEach(value -> assertDoesNotThrow(() -> BasicValidator.validateDate((value))));

        // invalid
        var invalidValues = List.of(
                Date.newBuilder().setYear(2024).setMonth(0).setDay(0).build(),
                Date.newBuilder().setYear(2024).setMonth(1).setDay(0).build(),
                Date.newBuilder().setYear(2023).setMonth(2).setDay(29).build(),
                Date.newBuilder().setYear(2023).setMonth(1).setDay(32).build(),
                Date.newBuilder().setYear(2023).setMonth(4).setDay(31).build());
        invalidValues.forEach(value -> assertThrows(BizException.class, () -> BasicValidator.validateDate(value)));
    }

    @Test
    void testValidateTenantWithoutBusinessId() {
        // valid
        var validValues = List.of(
                Tenant.newBuilder().setCompanyId(1).build(),
                Tenant.newBuilder().setCompanyId(1).setBusinessId(0).build());
        validValues.forEach(value -> assertDoesNotThrow(() -> BasicValidator.validateTenantWithoutBusinessId((value))));

        // invalid
        var invalidValues =
                List.of(Tenant.newBuilder().setCompanyId(1).setBusinessId(1).build());
        invalidValues.forEach(
                value -> assertThrows(BizException.class, () -> BasicValidator.validateTenantWithoutBusinessId(value)));
    }
}
