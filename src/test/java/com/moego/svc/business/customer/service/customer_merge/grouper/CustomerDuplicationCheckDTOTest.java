package com.moego.svc.business.customer.service.customer_merge.grouper;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

public class CustomerDuplicationCheckDTOTest {

    @Test
    void testEmailKey() {
        // null email
        var dto = new CustomerDuplicationCheckDTO();
        assertEquals("", dto.getEmailKey());

        // empty email
        dto.setEmail("");
        assertEquals("", dto.getEmailKey());

        // blank email
        dto.setEmail("  \t\n");
        assertEquals("", dto.getEmailKey());

        // normal email
        dto.setEmail("<EMAIL>");
        assertEquals("<EMAIL>", dto.getEmailKey());

        // case insensitive
        dto.setEmail("<EMAIL>");
        assertEquals("<EMAIL>", dto.getEmailKey());

        // trim
        dto.setEmail("  <EMAIL>  ");
        assertEquals("<EMAIL>", dto.getEmailKey());
    }

    @Test
    void testNameKey() {
        // null first name and last name
        var dto = new CustomerDuplicationCheckDTO();
        assertEquals("", dto.getNameKey());

        // empty first name and last name
        dto.setFirstName("");
        dto.setLastName("");
        assertEquals("", dto.getNameKey());

        // blank first name and last name
        dto.setFirstName("  \t\n");
        dto.setLastName("  \t\n");
        assertEquals("", dto.getNameKey());

        // normal first name and last name
        dto.setFirstName("John");
        dto.setLastName("Doe");
        assertEquals("john<|>doe", dto.getNameKey());

        // case insensitive
        dto.setFirstName("JOHN");
        dto.setLastName("DOE");
        assertEquals("john<|>doe", dto.getNameKey());

        // trim
        dto.setFirstName("  JOHN  ");
        dto.setLastName("  DOE  ");
        assertEquals("john<|>doe", dto.getNameKey());
    }

    @Test
    void testPhoneNumberKey() {
        var suffixLength = 10;

        // null phone number
        var dto = new CustomerDuplicationCheckDTO();
        assertEquals("", dto.getPhoneNumberKey(suffixLength));

        // empty phone number
        dto.setPhoneNumber("");
        assertEquals("", dto.getPhoneNumberKey(suffixLength));

        // blank phone number
        dto.setPhoneNumber("  \t\n");
        assertEquals("", dto.getPhoneNumberKey(suffixLength));

        // phone number length <= suffix length
        dto.setPhoneNumber("1234");
        assertEquals("1234", dto.getPhoneNumberKey(suffixLength));
        dto.setPhoneNumber("1234567890");
        assertEquals("1234567890", dto.getPhoneNumberKey(suffixLength));

        // phone number length > suffix length
        dto.setPhoneNumber("11111234567890");
        assertEquals("1234567890", dto.getPhoneNumberKey(suffixLength));
    }
}
