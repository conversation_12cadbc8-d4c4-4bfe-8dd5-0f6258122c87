package com.moego.svc.business.customer.service;

import com.moego.svc.business.customer.params.CustomerCreationSettingParam;
import com.moego.svc.business.customer.repo.CustomerCreationSettingRepo;
import com.moego.svc.business.customer.utils.TenantUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@ActiveProfiles("unit-test")
@Transactional
public class CustomerCreationSettingServiceTest {

    @Autowired
    private CustomerCreationSettingRepo customerCreationSettingRepo;

    @Autowired
    private CustomerSettingService customerSettingService;

    @Test
    void testUpdateSetting() {
        var companyId = 1L;
        var tenant = TenantUtils.of(companyId);

        // 初始时没有 setting 记录，repo 查到 null
        var setting = customerCreationSettingRepo.getCustomerCreationSetting(companyId);
        Assertions.assertNull(setting);

        // 没有记录时，service 返回默认值
        var settingDTO = customerSettingService.getCustomerCreationSetting(tenant);
        Assertions.assertNotNull(settingDTO);
        Assertions.assertTrue(settingDTO.enableCreationFromSms());
        Assertions.assertTrue(settingDTO.enableCreationFromCall());

        // 更新 setting 记录
        var param = new CustomerCreationSettingParam();
        param.setTenant(TenantUtils.of(companyId));
        param.setEnableCreationFromSms(false);
        customerSettingService.updateCustomerCreationSetting(param);

        // 更新后，repo 查到记录
        setting = customerCreationSettingRepo.getCustomerCreationSetting(companyId);
        Assertions.assertNotNull(setting);

        // 更新后，service 返回更新后的值
        settingDTO = customerSettingService.getCustomerCreationSetting(tenant);
        Assertions.assertNotNull(settingDTO);
        Assertions.assertFalse(settingDTO.enableCreationFromSms());

        // 再次更新 setting 记录
        param.setEnableCreationFromSms(true);
        param.setEnableCreationFromCall(false);
        customerSettingService.updateCustomerCreationSetting(param);

        settingDTO = customerSettingService.getCustomerCreationSetting(tenant);
        Assertions.assertNotNull(settingDTO);
        Assertions.assertTrue(settingDTO.enableCreationFromSms());
        Assertions.assertFalse(settingDTO.enableCreationFromCall());
    }
}
