package com.moego.svc.business.customer.service;

import com.google.type.Date;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineRecordCreateDef;
import com.moego.svc.business.customer.converter.PetVaccineRecordConverter;
import com.moego.svc.business.customer.utils.TimeUtils;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@ActiveProfiles("unit-test")
@Transactional
public class PetVaccineRecordServiceTest {

    @Autowired
    private PetVaccineRecordService petVaccineRecordService;

    @Autowired
    private PetVaccineRecordConverter petVaccineRecordConverter;

    @Test
    void testBatchCreateVaccineRecord() {
        var petId = 1L;

        var vaccineId1 = 1L;
        var vaccineId2 = 2L;
        var vaccineId3 = 3L;

        var date1 = Date.newBuilder().setYear(2024).setMonth(1).setDay(1).build();
        var date2 = Date.newBuilder().setYear(2024).setMonth(1).setDay(2).build();
        var date3 = Date.newBuilder().setYear(2024).setMonth(1).setDay(3).build();

        var url1 = "http://example.com/1";
        var url2 = "http://example.com/2";
        var url3 = "http://example.com/3";

        var createDefs = List.of(
                // vaccine 1, date 1, url 1
                BusinessPetVaccineRecordCreateDef.newBuilder()
                        .setVaccineId(vaccineId1)
                        .setExpirationDate(date1)
                        .addAllDocumentUrls(List.of(url1))
                        .build(),
                // vaccine 2, date 2, url 2
                BusinessPetVaccineRecordCreateDef.newBuilder()
                        .setVaccineId(vaccineId2)
                        .setExpirationDate(date2)
                        .addAllDocumentUrls(List.of(url2))
                        .build());

        petVaccineRecordService.batchCreateVaccineRecord(petId, createDefs, false);

        var records = petVaccineRecordService.listPetVaccineRecord(petId);
        Assertions.assertEquals(2, records.size());

        records.forEach(record -> {
            if (record.getVaccineId() == vaccineId1) {
                Assertions.assertEquals(TimeUtils.toDateString(date1), record.getExpirationDate());
                checkDocumentUrls(List.of(url1), record.getDocumentUrls());
            } else if (record.getVaccineId() == vaccineId2) {
                Assertions.assertEquals(TimeUtils.toDateString(date2), record.getExpirationDate());
                checkDocumentUrls(List.of(url2), record.getDocumentUrls());
            } else {
                Assertions.fail("Unexpected record: " + record);
            }
        });

        // 重复创建, 去重
        createDefs = List.of(
                // vaccine 1, date 1, url 1
                BusinessPetVaccineRecordCreateDef.newBuilder()
                        .setVaccineId(vaccineId1)
                        .setExpirationDate(date1)
                        .addAllDocumentUrls(List.of(url1))
                        .build(),
                // vaccine 2, date 2, url 2
                BusinessPetVaccineRecordCreateDef.newBuilder()
                        .setVaccineId(vaccineId2)
                        .setExpirationDate(date2)
                        .addAllDocumentUrls(List.of(url2))
                        .build(),
                // vaccine 3, date 3, url 3
                BusinessPetVaccineRecordCreateDef.newBuilder()
                        .setVaccineId(vaccineId3)
                        .setExpirationDate(date3)
                        .addAllDocumentUrls(List.of(url3))
                        .build());

        petVaccineRecordService.batchCreateVaccineRecord(petId, createDefs, true);

        records = petVaccineRecordService.listPetVaccineRecord(petId);
        Assertions.assertEquals(3, records.size());

        records.forEach(record -> {
            if (record.getVaccineId() == vaccineId1) {
                Assertions.assertEquals(TimeUtils.toDateString(date1), record.getExpirationDate());
                checkDocumentUrls(List.of(url1), record.getDocumentUrls());
            } else if (record.getVaccineId() == vaccineId2) {
                Assertions.assertEquals(TimeUtils.toDateString(date2), record.getExpirationDate());
                checkDocumentUrls(List.of(url2), record.getDocumentUrls());
            } else if (record.getVaccineId() == vaccineId3) {
                Assertions.assertEquals(TimeUtils.toDateString(date3), record.getExpirationDate());
                checkDocumentUrls(List.of(url3), record.getDocumentUrls());
            } else {
                Assertions.fail("Unexpected record: " + record);
            }
        });

        // 重复创建, 不去重
        petVaccineRecordService.batchCreateVaccineRecord(petId, createDefs, false);

        records = petVaccineRecordService.listPetVaccineRecord(petId);
        Assertions.assertEquals(6, records.size());

        records.forEach(record -> {
            if (record.getVaccineId() == vaccineId1) {
                Assertions.assertEquals(TimeUtils.toDateString(date1), record.getExpirationDate());
                checkDocumentUrls(List.of(url1), record.getDocumentUrls());
            } else if (record.getVaccineId() == vaccineId2) {
                Assertions.assertEquals(TimeUtils.toDateString(date2), record.getExpirationDate());
                checkDocumentUrls(List.of(url2), record.getDocumentUrls());
            } else if (record.getVaccineId() == vaccineId3) {
                Assertions.assertEquals(TimeUtils.toDateString(date3), record.getExpirationDate());
                checkDocumentUrls(List.of(url3), record.getDocumentUrls());
            } else {
                Assertions.fail("Unexpected record: " + record);
            }
        });
    }

    private void checkDocumentUrls(List<String> expectedUrls, String urls) {
        var actualUrls = petVaccineRecordConverter.toDocumentUrls(urls);
        Assertions.assertEquals(expectedUrls.size(), actualUrls.size());
        expectedUrls.forEach(expectedUrl -> Assertions.assertTrue(actualUrls.contains(expectedUrl)));
    }
}
