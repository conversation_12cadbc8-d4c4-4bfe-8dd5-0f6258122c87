/*
 * @since 2023-06-06 20:05:49
 * <AUTHOR> <<EMAIL>>
 */

package com.moego.svc.business.customer.controller;

import static com.moego.idl.models.todo.v1.TodoModel.Status.STATUS_PENDING;
import static org.junit.jupiter.api.Assertions.assertEquals;

import com.moego.idl.models.todo.v1.TodoModel;
import com.moego.idl.service.todo.v1.AddTodoRequest;
import com.moego.idl.service.todo.v1.TodoServiceGrpc;
import com.moego.svc.business.customer.utils.MockStubs;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@ExtendWith(MockitoExtension.class)
@ActiveProfiles("unit-test")
@SpringBootTest
@Transactional
@Disabled // please remove this line and replace jdbc source to moego_business_customer_unit_test if you want to test
// this file
class TodoControllerTest extends MockStubs {

    @Autowired
    private TodoServiceGrpc.TodoServiceBlockingStub todoServiceClient;

    @Test
    void addTodo() {
        var req = AddTodoRequest.newBuilder().setTitle("L1").setUserId(1L).build();
        var todo = todoServiceClient.addTodo(req);
        var exp = TodoModel.newBuilder()
                .setId(todo.getId())
                .setUserId(1L)
                .setTitle("L1")
                .setStatus(STATUS_PENDING)
                .setCreatedAt(todo.getCreatedAt())
                .build();
        assertEquals(exp, todo);
    }
}
