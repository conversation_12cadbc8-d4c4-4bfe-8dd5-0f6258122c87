package com.moego.svc.business.customer.repo;

import static com.moego.svc.business.customer.repository.jooq.Tables.MOE_CUSTOMER_PET;
import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.customer.v1.PetGender;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.svc.business.customer.enums.PetExpiryNotification;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoeCustomerPetRecord;
import java.time.Instant;
import org.jooq.DSLContext;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@ActiveProfiles("unit-test")
@Transactional
class PetRepoTest {

    @Autowired
    PetRepo petRepo;

    @Autowired
    DSLContext dsl;

    @Test
    void testCreatePet_whenNotSpecifyExpiryNotification_thenShouldEnableByDefault() {
        var pet = new MoeCustomerPetRecord()
                .setCustomerId(111)
                .setCompanyId(1111L)
                .setBusinessId(1111)
                .setPetName("Mini")
                .setPetTypeId(PetType.PET_TYPE_DOG_VALUE)
                .setAvatarPath(
                        "https://moegonew.s3-us-west-2.amazonaws.com/Public/Uploads/16007669509ef3fa8124a044c583fb08f5f681e7ab.png?name=Screenshot%202020-09-22%20at%205.21.59%20PM.png")
                .setBreed("French Bulldog")
                .setGender(PetGender.PET_GENDER_MALE_VALUE)
                .setHairLength("Short coat")
                .setBehavior("Friendly")
                .setWeight("10")
                .setFixed("Spayed")
                .setCreateTime(Instant.now().getEpochSecond())
                .setUpdateTime(Instant.now().getEpochSecond());

        long id = petRepo.createPet(pet);

        var insertedPet = dsl.select()
                .from(MOE_CUSTOMER_PET)
                .where(MOE_CUSTOMER_PET.ID.eq((int) id))
                .fetchOneInto(MoeCustomerPetRecord.class);

        assertThat(insertedPet).isNotNull();
        assertThat(insertedPet.getExpiryNotification()).isEqualTo(PetExpiryNotification.OPEN.getValue());
    }
}
