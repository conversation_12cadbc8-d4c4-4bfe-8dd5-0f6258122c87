package com.moego.svc.business.customer.service;

import com.moego.idl.models.business_customer.v1.BusinessCustomerPetCreateDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerPetUpdateDef;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.lib.common.exception.BizException;
import com.moego.svc.business.customer.dto.CustomerDTO;
import com.moego.svc.business.customer.enums.option.GetOption;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@SpringBootTest
@ActiveProfiles("unit-test")
@Transactional
public class PetServiceTest {

    @Autowired
    PetService petService;

    @Test
    public void testGetPetDTO() {

        Assertions.assertThrows(BizException.class, () -> petService.getPetDTO(null, 0));

        var pet = petService.getPetDTO(null, 0, GetOption.ACCEPT_NULL);
        Assertions.assertNull(pet);

        var customer = new CustomerDTO();
        customer.setId(1L);
        customer.setCompanyId(1L);
        customer.setPreferredBusinessId(1L);

        var createDef = BusinessCustomerPetCreateDef.newBuilder()
                .setPetType(PetType.PET_TYPE_DOG)
                .setBreed("breed")
                .setPetName("Max")
                .addAllPetCodeIds(List.of(11L, 22L, 33L))
                .build();

        var id = petService.createPet(customer, createDef);

        pet = petService.getPetDTO(null, id);
        Assertions.assertNotNull(pet);
        Assertions.assertTrue(CollectionUtils.isEmpty(pet.getPetCodeIds()));

        pet = petService.getPetDTO(null, id, GetOption.INCLUDE_PET_CODE);
        Assertions.assertNotNull(pet);
        Assertions.assertFalse(CollectionUtils.isEmpty(pet.getPetCodeIds()));
        Assertions.assertEquals(3, pet.getPetCodeIds().size());
        Assertions.assertEquals(11L, pet.getPetCodeIds().get(0));
        Assertions.assertEquals(22L, pet.getPetCodeIds().get(1));
        Assertions.assertEquals(33L, pet.getPetCodeIds().get(2));
    }

    @Test
    public void testUpdatePet() {
        var customer = new CustomerDTO();
        customer.setId(1L);
        customer.setCompanyId(1L);
        customer.setPreferredBusinessId(1L);

        var createDef = BusinessCustomerPetCreateDef.newBuilder()
                .setPetType(PetType.PET_TYPE_DOG)
                .setBreed("breed")
                .setPetName("Max")
                .addAllPetCodeIds(List.of(11L, 22L, 33L))
                .build();

        var id = petService.createPet(customer, createDef);

        var updateDef = BusinessCustomerPetUpdateDef.newBuilder()
                .setPetName("Maxi")
                .setBreed("breed1")
                .setPetCodes(
                        BusinessCustomerPetUpdateDef.PetCodeList.newBuilder().addAllIds(List.of(44L, 55L)))
                .build();

        petService.updatePet(null, id, updateDef);

        var pet = petService.getPetDTO(null, id, GetOption.INCLUDE_PET_CODE);
        Assertions.assertNotNull(pet);
        Assertions.assertEquals("Maxi", pet.getPetName());
        Assertions.assertEquals("breed1", pet.getBreed());
        Assertions.assertFalse(CollectionUtils.isEmpty(pet.getPetCodeIds()));
        Assertions.assertEquals(2, pet.getPetCodeIds().size());
        Assertions.assertEquals(44L, pet.getPetCodeIds().get(0));
        Assertions.assertEquals(55L, pet.getPetCodeIds().get(1));
    }
}
