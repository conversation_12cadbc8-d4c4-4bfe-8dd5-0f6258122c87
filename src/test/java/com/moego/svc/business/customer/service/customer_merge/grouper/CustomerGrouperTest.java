package com.moego.svc.business.customer.service.customer_merge.grouper;

import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class CustomerGrouperTest {

    private static final List<CustomerDuplicationCheckDTO> customers = List.of(
            new CustomerDuplicationCheckDTO(0, "", "", "", "", List.of()),
            new CustomerDuplicationCheckDTO(1, "first1", "last1", "", "1234567890", List.of()),
            new CustomerDuplicationCheckDTO(2, "first2", "last2", "  ", "1234567890", List.of()),
            new CustomerDuplicationCheckDTO(3, "first3", "last3", "  ", "***********", List.of()),
            new CustomerDuplicationCheckDTO(4, "first3", "last3", null, "234567890", List.of()),
            new CustomerDuplicationCheckDTO(5, "first4", "last4", null, "234567890", List.of()),
            new CustomerDuplicationCheckDTO(6, "first4", "last4 ", "<EMAIL>", "222333444", List.of()),
            new CustomerDuplicationCheckDTO(7, "first4 ", "last4", "<EMAIL>", "333444555", List.of()),
            new CustomerDuplicationCheckDTO(8, "first5", "", "<EMAIL>", "333444555", List.of()),
            new CustomerDuplicationCheckDTO(9, "first5", "", "<EMAIL>", "333444555", List.of()),
            new CustomerDuplicationCheckDTO(10, "", "last6", "<EMAIL>", "", List.of()),
            new CustomerDuplicationCheckDTO(11, "", "last6", "<EMAIL>", "  ", List.of()),
            new CustomerDuplicationCheckDTO(12, "first7", "last7", "<EMAIL>", "  ", List.of()),
            new CustomerDuplicationCheckDTO(13, "", "first7 last7", "<EMAIL>", null, List.of()),
            new CustomerDuplicationCheckDTO(14, "first7 last7", "", "<EMAIL>", null, List.of()),
            new CustomerDuplicationCheckDTO(15, "first7 l", "ast7", "<EMAIL>", "444555666", List.of()),
            new CustomerDuplicationCheckDTO(16, "first8", "last8", "<EMAIL>", "444555666", List.of()),
            new CustomerDuplicationCheckDTO(17, "first8", "last8", "<EMAIL>", "444555666", List.of()),
            new CustomerDuplicationCheckDTO(18, "first8", "last8", "<EMAIL>", "444555666", List.of()),
            new CustomerDuplicationCheckDTO(19, "first8", "last8", "<EMAIL>", "444555666", List.of()),
            new CustomerDuplicationCheckDTO(20, "first8", "last8", "<EMAIL>", "555666777", List.of()),
            new CustomerDuplicationCheckDTO(21, "first8", "last8", "<EMAIL>", "555666777", List.of()),
            new CustomerDuplicationCheckDTO(22, "  ", "  ", " <EMAIL>", "555666777", List.of()),
            new CustomerDuplicationCheckDTO(23, null, "", "<EMAIL> ", "555666777", List.of()),
            new CustomerDuplicationCheckDTO(24, "", null, " <EMAIL> ", "555666777", List.of()),
            new CustomerDuplicationCheckDTO(25, "  ", "  ", "e5@ moego.pet", "555666777", List.of()));

    private void checkGroup(CustomerGrouper.Group expected, CustomerGrouper.Group actual) {
        Assertions.assertEquals(expected.key(), actual.key());
        Assertions.assertEquals(expected.customers().size(), actual.customers().size());
        for (var i = 0; i < expected.customers().size(); i++) {
            var expectedCustomer = expected.customers().get(i);
            var actualCustomer = actual.customers().get(i);
            Assertions.assertEquals(expectedCustomer.getId(), actualCustomer.getId());
            Assertions.assertEquals(expectedCustomer.getFirstName(), actualCustomer.getFirstName());
            Assertions.assertEquals(expectedCustomer.getLastName(), actualCustomer.getLastName());
            Assertions.assertEquals(expectedCustomer.getEmail(), actualCustomer.getEmail());
            Assertions.assertEquals(expectedCustomer.getPhoneNumber(), actualCustomer.getPhoneNumber());
        }
    }

    private void testGroup(
            Function<CustomerDuplicationCheckDTO, String> keyExtractor, List<CustomerGrouper.Group> expectedGroups) {
        for (int maxGroupCount = 1; maxGroupCount <= expectedGroups.size(); maxGroupCount++) {
            var result = CustomerGrouper.group(customers, keyExtractor, maxGroupCount);
            Assertions.assertEquals(maxGroupCount, result.groups().size());

            for (var i = 0; i < maxGroupCount; i++) {
                checkGroup(expectedGroups.get(i), result.groups().get(i));
            }

            var groupedCustomers = expectedGroups.stream()
                    .limit(maxGroupCount)
                    .flatMap(g -> g.customers().stream())
                    .collect(Collectors.toSet());
            result.ungroupCustomers().forEach(c -> Assertions.assertFalse(groupedCustomers.contains(c)));
        }

        // 只能找到 expectedGroups.size() 组
        var result = CustomerGrouper.group(customers, keyExtractor, expectedGroups.size() + 1);
        Assertions.assertEquals(expectedGroups.size(), result.groups().size());

        for (var i = 0; i < expectedGroups.size(); i++) {
            checkGroup(expectedGroups.get(i), result.groups().get(i));
        }

        var groupedCustomers =
                expectedGroups.stream().flatMap(g -> g.customers().stream()).collect(Collectors.toSet());
        result.ungroupCustomers().forEach(c -> Assertions.assertFalse(groupedCustomers.contains(c)));
    }

    @Test
    void testPhoneNumberGroup() {
        // 后 9 位 phone number
        var expectedGroups = List.of(
                // 234567890 -> customer 1 2 3 4 5
                new CustomerGrouper.Group(
                        "234567890",
                        List.of(
                                customers.get(1),
                                customers.get(2),
                                customers.get(3),
                                customers.get(4),
                                customers.get(5))),
                // 333444555 -> customer 7 8 9
                new CustomerGrouper.Group("333444555", List.of(customers.get(7), customers.get(8), customers.get(9))),
                // 444555666 -> customer 15 16 17 18 19
                new CustomerGrouper.Group(
                        "444555666",
                        List.of(
                                customers.get(15),
                                customers.get(16),
                                customers.get(17),
                                customers.get(18),
                                customers.get(19)))
                // 222333444 -> customer 6 只有一个 customer, 不构成分组
                // 555666777 -> customer 20 21 22 23 24 25 超过 5 个, 不构成分组
                // "" -> customer 0 10 11 12 13 14 空白 phone number, 不构成分组
                );

        testGroup(c -> c.getPhoneNumberKey(9), expectedGroups);

        // 后 10 位 phone number
        expectedGroups = List.of(
                // 1234567890 -> customer 1 2 3(后 10 位), 不包括 4 5
                new CustomerGrouper.Group("1234567890", List.of(customers.get(1), customers.get(2), customers.get(3))),
                // 234567890 -> customer 4 5
                new CustomerGrouper.Group("234567890", List.of(customers.get(4), customers.get(5))),
                // 333444555 -> customer 7 8 9
                new CustomerGrouper.Group("333444555", List.of(customers.get(7), customers.get(8), customers.get(9))),
                // 444555666 -> customer 15 16 17 18 19
                new CustomerGrouper.Group(
                        "444555666",
                        List.of(
                                customers.get(15),
                                customers.get(16),
                                customers.get(17),
                                customers.get(18),
                                customers.get(19)))
                // 222333444 -> customer 6 只有一个 customer, 不构成分组
                // 555666777 -> customer 20 21 22 23 24 25 超过 5 个, 不构成分组
                // "" -> customer 0 10 11 12 13 14 空白 phone number, 不构成分组
                );

        testGroup(c -> c.getPhoneNumberKey(10), expectedGroups);

        // 后 11 位 phone number
        expectedGroups = List.of(
                // 1234567890 -> customer 1 2, 不包括 3
                new CustomerGrouper.Group("1234567890", List.of(customers.get(1), customers.get(2))),
                // 234567890 -> customer 4 5
                new CustomerGrouper.Group("234567890", List.of(customers.get(4), customers.get(5))),
                // 333444555 -> customer 7 8 9
                new CustomerGrouper.Group("333444555", List.of(customers.get(7), customers.get(8), customers.get(9))),
                // 444555666 -> customer 15 16 17 18 19
                new CustomerGrouper.Group(
                        "444555666",
                        List.of(
                                customers.get(15),
                                customers.get(16),
                                customers.get(17),
                                customers.get(18),
                                customers.get(19)))
                // *********** -> customer 3 只有一个 customer, 不构成分组
                // 222333444 -> customer 6 只有一个 customer, 不构成分组
                // 555666777 -> customer 20 21 22 23 24 25 超过 5 个, 不构成分组
                // "" -> customer 0 10 11 12 13 14 空白 phone number, 不构成分组
                );

        testGroup(c -> c.getPhoneNumberKey(11), expectedGroups);
    }

    @Test
    void testEmailGroup() {
        var expectedGroups = List.of(
                // <EMAIL> -> customer 6 7
                new CustomerGrouper.Group("<EMAIL>", List.of(customers.get(6), customers.get(7))),
                // <EMAIL> -> customer 8 9
                new CustomerGrouper.Group("<EMAIL>", List.of(customers.get(8), customers.get(9))),
                // <EMAIL> -> customer 10 11 12 13
                new CustomerGrouper.Group(
                        "<EMAIL>",
                        List.of(customers.get(10), customers.get(11), customers.get(12), customers.get(13))),
                // <EMAIL> -> customer 21 22 23 24
                new CustomerGrouper.Group(
                        "<EMAIL>",
                        List.of(customers.get(21), customers.get(22), customers.get(23), customers.get(24)))
                // "" -> customer 0 1 2 3 4 5 空白 email, 不构成分组
                // <EMAIL> -> customer 14 只有一个 customer, 不构成分组
                // e5@ moego.pet -> customer 25 只有一个 customer, 不构成分组
                // <EMAIL> -> customer 15 16 17 18 19 20 超过 5 个, 不构成分组
                );

        testGroup(CustomerDuplicationCheckDTO::getEmailKey, expectedGroups);
    }

    @Test
    void testNameGroup() {
        var expectedGroups = List.of(
                // "<|>last6" -> customer 10 11
                new CustomerGrouper.Group("<|>last6", List.of(customers.get(10), customers.get(11))),
                // "first3<|>last3" -> customer 3 4
                new CustomerGrouper.Group("first3<|>last3", List.of(customers.get(3), customers.get(4))),
                // "first4<|>last4" -> customer 5 6 7
                new CustomerGrouper.Group(
                        "first4<|>last4", List.of(customers.get(5), customers.get(6), customers.get(7))),
                // "first5<|>" -> customer 8 9
                new CustomerGrouper.Group("first5<|>", List.of(customers.get(8), customers.get(9)))
                // "first1<|>last1" -> customer 1 只有一个 customer, 不构成分组
                // "first2<|>last2" -> customer 2 只有一个 customer, 不构成分组
                // 以下四个不是一个组:
                // "first7<|>last7" -> customer 12
                // "<|>first7 last7" -> customer 13
                // "first7 last7<|>" -> customer 14
                // "first7 l<|>ast7" -> customer 15

                // "first8<|>last8" -> customer 16 17 18 19 20 21 超过 5 个, 不构成分组
                // "" -> customer 0 22 23 24 25 空白 name, 不构成分组
                );

        testGroup(CustomerDuplicationCheckDTO::getNameKey, expectedGroups);
    }
}
