package com.moego.svc.business.customer.service.customer_merge.grouper;

import com.moego.idl.models.customer.v1.PetType;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

public class PetGrouperTest {

    private static final List<PetDuplicationCheckDTO> pets = List.of(
            // not belong to any group
            new PetDuplicationCheckDTO(1, 1, "pet1", "breed1", PetType.PET_TYPE_DOG_VALUE),
            new PetDuplicationCheckDTO(2, 2, "pet2", "breed2", PetType.PET_TYPE_DOG_VALUE),

            // group 3: max-1-husky
            // pet names are case-insensitive
            new PetDuplicationCheckDTO(3, 3, "max", "<PERSON>sky", PetType.PET_TYPE_DOG_VALUE),
            new PetDuplicationCheckDTO(4, 4, "<PERSON>", "<PERSON><PERSON>", PetType.PET_TYPE_DOG_VALUE),

            // group 4: mini-1-corgi
            // customer 6 will not be in group 5
            new PetDuplicationCheckDTO(5, 5, "Mini", "corgi", PetType.PET_TYPE_DOG_VALUE),
            new PetDuplicationCheckDTO(6, 6, "Mini", "corgi", PetType.PET_TYPE_DOG_VALUE),
            new PetDuplicationCheckDTO(7, 6, "NN", "mmm", PetType.PET_TYPE_DOG_VALUE),

            // group 5: nn-1-mmm
            // pet names and breeds are case-insensitive
            // customer 8 will not be in group 6
            new PetDuplicationCheckDTO(8, 7, "NN", "mmm", PetType.PET_TYPE_DOG_VALUE),
            new PetDuplicationCheckDTO(9, 8, "Nn", "MMM", PetType.PET_TYPE_DOG_VALUE),
            new PetDuplicationCheckDTO(16, 8, "xyz", "miao", PetType.PET_TYPE_CAT_VALUE),

            // not belong to group 5 because of pet type
            new PetDuplicationCheckDTO(10, 9, "NN", "mmm", PetType.PET_TYPE_CAT_VALUE),

            // group 6: xyz-2-miao
            new PetDuplicationCheckDTO(11, 10, "xyz", "miao", PetType.PET_TYPE_CAT_VALUE),
            new PetDuplicationCheckDTO(12, 11, "xyz", "miao", PetType.PET_TYPE_CAT_VALUE),
            new PetDuplicationCheckDTO(13, 12, "xyz", "miao", PetType.PET_TYPE_CAT_VALUE),
            new PetDuplicationCheckDTO(14, 13, "xyz", "miao", PetType.PET_TYPE_CAT_VALUE),
            new PetDuplicationCheckDTO(15, 14, "xyz", "miao", PetType.PET_TYPE_CAT_VALUE),

            // not a group (more than 5)
            new PetDuplicationCheckDTO(17, 15, "unknown", "other", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(18, 16, "unknown", "other", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(19, 17, "unknown", "other", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(20, 18, "unknown", "other", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(21, 19, "unknown", "other", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(22, 20, "unknown", "other", PetType.PET_TYPE_OTHER_VALUE),

            // group 1: bb-3-bird
            // customer 21 and 22 are not in group 2
            new PetDuplicationCheckDTO(23, 21, "bb", "bird", PetType.PET_TYPE_BIRD_VALUE),
            new PetDuplicationCheckDTO(27, 21, "dd", "bird", PetType.PET_TYPE_BIRD_VALUE),
            new PetDuplicationCheckDTO(24, 22, "bb", "bird", PetType.PET_TYPE_BIRD_VALUE),
            new PetDuplicationCheckDTO(28, 22, "dd", "bird", PetType.PET_TYPE_BIRD_VALUE),

            // group 2: dd-3-bird
            new PetDuplicationCheckDTO(25, 23, "dd", "bird", PetType.PET_TYPE_BIRD_VALUE),
            new PetDuplicationCheckDTO(26, 24, "dd", "bird", PetType.PET_TYPE_BIRD_VALUE),

            // group 7: zzz-11-other
            // customer 3 belongs to group 3, so the other 5 customers can be grouped together
            new PetDuplicationCheckDTO(27, 25, "zzz", "other", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(28, 26, "zzz", "other", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(29, 3, "zzz", "other", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(30, 28, "zzz", "other", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(31, 29, "zzz", "other", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(32, 30, "zzz", "other", PetType.PET_TYPE_OTHER_VALUE),

            // pets without name are not grouped
            new PetDuplicationCheckDTO(33, 31, "", "other", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(34, 32, "", "other", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(36, 34, "  ", "other", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(37, 35, "  ", "other", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(38, 36, null, "other", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(39, 37, null, "other", PetType.PET_TYPE_OTHER_VALUE),

            // pets without breed are not grouped
            new PetDuplicationCheckDTO(40, 38, "pet1", "", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(41, 39, "pet2", "", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(42, 40, "pet3", "  ", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(43, 41, "pet4", "  ", PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(44, 42, "pet5", null, PetType.PET_TYPE_OTHER_VALUE),
            new PetDuplicationCheckDTO(45, 43, "pet6", null, PetType.PET_TYPE_OTHER_VALUE));

    private static final List<PetGrouper.Group> expectedGroups = List.of(
            new PetGrouper.Group("bb-3-bird", Set.of(21L, 22L)),
            new PetGrouper.Group("dd-3-bird", Set.of(23L, 24L)),
            new PetGrouper.Group("max-1-husky", Set.of(3L, 4L)),
            new PetGrouper.Group("mini-1-corgi", Set.of(5L, 6L)),
            new PetGrouper.Group("nn-1-mmm", Set.of(7L, 8L)),
            new PetGrouper.Group("xyz-2-miao", Set.of(10L, 11L, 12L, 13L, 14L)),
            new PetGrouper.Group("zzz-11-other", Set.of(25L, 26L, 28L, 29L, 30L)));

    private void checkGroup(PetGrouper.Group expected, PetGrouper.Group actual) {
        Assertions.assertEquals(expected.key(), actual.key());
        Assertions.assertEquals(
                expected.customerIds().size(), actual.customerIds().size());
        Assertions.assertEquals(expected.customerIds(), actual.customerIds());
    }

    @Test
    void testGroup() {
        // test for different maxGroupCount
        for (int maxGroupCount = 1; maxGroupCount <= 7; maxGroupCount++) {
            var groups = PetGrouper.group(pets, maxGroupCount);
            Assertions.assertEquals(maxGroupCount, groups.size());

            for (int i = 0; i < maxGroupCount; i++) {
                checkGroup(expectedGroups.get(i), groups.get(i));
            }
        }

        // only 7 groups
        int maxGroupCount = 8;
        var groups = PetGrouper.group(pets, maxGroupCount);
        Assertions.assertEquals(7, groups.size());

        for (int i = 0; i < 7; i++) {
            checkGroup(expectedGroups.get(i), groups.get(i));
        }
    }
}
