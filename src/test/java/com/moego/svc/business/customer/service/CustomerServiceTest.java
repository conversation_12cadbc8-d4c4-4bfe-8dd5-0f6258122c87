package com.moego.svc.business.customer.service;

import static org.mockito.Mockito.when;

import com.moego.idl.models.business_customer.v1.BusinessCustomerCreateDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerInfoModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerUpdateDef;
import com.moego.idl.models.organization.v1.Tenant;
import com.moego.lib.common.exception.BizException;
import com.moego.svc.business.customer.dto.CustomerCreationSettingDTO;
import com.moego.svc.business.customer.enums.option.GetOption;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@SpringBootTest
@ActiveProfiles("unit-test")
@Transactional
public class CustomerServiceTest {

    @Autowired
    CustomerServiceV2 customerService;

    @MockBean
    CustomerSettingService customerSettingService;

    @Test
    void testGetCustomer() {

        Assertions.assertThrows(BizException.class, () -> customerService.getCustomer(null, 0));

        var customer = customerService.getCustomer(null, 0, GetOption.ACCEPT_NULL);
        Assertions.assertNull(customer);

        var tenant = Tenant.newBuilder().setCompanyId(1).build();
        var createDef = BusinessCustomerCreateDef.newBuilder()
                .setFirstName("f")
                .setLastName("l")
                .setPreferredBusinessId(1)
                .addAllTagIds(List.of(11L, 22L, 33L))
                .build();

        var id = customerService.createCustomer(tenant, null, createDef);

        customer = customerService.getCustomer(tenant, id);
        Assertions.assertNotNull(customer);
        Assertions.assertTrue(CollectionUtils.isEmpty(customer.getTagIds()));

        customer = customerService.getCustomer(tenant, id, GetOption.INCLUDE_CUSTOMER_TAG);
        Assertions.assertNotNull(customer);
        Assertions.assertFalse(CollectionUtils.isEmpty(customer.getTagIds()));
        Assertions.assertEquals(3, customer.getTagIds().size());
        Assertions.assertEquals(11L, customer.getTagIds().get(0));
        Assertions.assertEquals(22L, customer.getTagIds().get(1));
        Assertions.assertEquals(33L, customer.getTagIds().get(2));
    }

    @Test
    void testUpdateCustomer() {
        var tenant = Tenant.newBuilder().setCompanyId(1).build();
        var createDef = BusinessCustomerCreateDef.newBuilder()
                .setFirstName("f")
                .setLastName("l")
                .setPreferredBusinessId(1)
                .addAllTagIds(List.of(11L, 22L, 33L))
                .build();

        var id = customerService.createCustomer(tenant, null, createDef);

        var updateDef = BusinessCustomerUpdateDef.newBuilder()
                .setFirstName("f1")
                .setLastName("l1")
                .setCustomerTags(BusinessCustomerUpdateDef.CustomerTagList.newBuilder()
                        .addAllIds(List.of(44L, 55L))
                        .build())
                .build();

        customerService.updateCustomer(tenant, null, id, updateDef);

        var customer = customerService.getCustomer(tenant, id, GetOption.INCLUDE_CUSTOMER_TAG);
        Assertions.assertNotNull(customer);
        Assertions.assertEquals("f1", customer.getFirstName());
        Assertions.assertEquals("l1", customer.getLastName());
        Assertions.assertFalse(CollectionUtils.isEmpty(customer.getTagIds()));
        Assertions.assertEquals(2, customer.getTagIds().size());
        Assertions.assertEquals(44L, customer.getTagIds().get(0));
        Assertions.assertEquals(55L, customer.getTagIds().get(1));
    }

    @Test
    void testCreateCustomer() {
        var companyId = 1L;
        var preferredBusinessId = 1L;
        var tenant = Tenant.newBuilder().setCompanyId(companyId).build();
        var setting = new CustomerCreationSettingDTO(false, false);

        when(customerSettingService.getCustomerCreationSetting(Mockito.any())).thenReturn(setting);

        var createdFromCall = BusinessCustomerCreateDef.newBuilder()
                .setSource(BusinessCustomerInfoModel.Source.CALL_IN)
                .setPreferredBusinessId(preferredBusinessId)
                .setFirstName("f")
                .setLastName("l")
                .build();
        Assertions.assertThrowsExactly(
                BizException.class, () -> customerService.createCustomer(tenant, null, createdFromCall));

        var createdFromSms = BusinessCustomerCreateDef.newBuilder()
                .setSource(BusinessCustomerInfoModel.Source.TEXT_IN)
                .setPreferredBusinessId(preferredBusinessId)
                .setFirstName("f")
                .setLastName("l")
                .build();
        Assertions.assertThrowsExactly(
                BizException.class, () -> customerService.createCustomer(tenant, null, createdFromSms));
    }
}
