package com.moego.svc.business.customer.converter;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.mockStatic;

import com.moego.idl.models.business_customer.v1.BusinessPetVaccineAvailabilityDef;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineCreateDef;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineModel;
import com.moego.idl.models.business_customer.v1.BusinessPetVaccineUpdateDef;
import com.moego.idl.models.customer.v1.PetType;
import com.moego.idl.service.business_customer.v1.CreatePetVaccineRequest;
import com.moego.idl.service.business_customer.v1.UpdatePetVaccineRequest;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoePetVaccineRecord;
import java.time.Instant;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

@SpringBootTest
@ActiveProfiles("unit-test")
class PetVaccineConverterTest {
    @Autowired
    private PetVaccineConverter petVaccineConverter;

    private final Instant instant = Instant.parse("2021-09-09T00:00:00Z");

    @Test
    void createRecord_onlyRequiredFields() {
        try (MockedStatic<Instant> mockedStatic = mockStatic(Instant.class)) {
            mockedStatic.when(Instant::now).thenReturn(instant);

            final var bid = 1L;
            final var cid = 2L;
            final var name = "test vaccine";
            final var input = CreatePetVaccineRequest.newBuilder()
                    .setCompanyId(cid)
                    .setBusinessId(bid)
                    .setVaccine(BusinessPetVaccineCreateDef.newBuilder()
                            .setName(name)
                            .build())
                    .build();
            final var actual = petVaccineConverter.createRecord(input);

            var expected = new MoePetVaccineRecord();
            expected.setCompanyId(cid);
            expected.setBusinessId(Math.toIntExact(bid));
            expected.setName(name);
            expected.setCreateTime(instant.getEpochSecond());
            expected.setUpdateTime(instant.getEpochSecond());

            assertEquals(expected, actual);
        }
    }

    @Test
    void createRecord_withAvailabilityByPetType() {
        try (MockedStatic<Instant> mockedStatic = mockStatic(Instant.class)) {
            mockedStatic.when(Instant::now).thenReturn(instant);

            final var bid = 1L;
            final var cid = 2L;
            final var name = "test vaccine";
            final var input = CreatePetVaccineRequest.newBuilder()
                    .setCompanyId(cid)
                    .setBusinessId(bid)
                    .setVaccine(BusinessPetVaccineCreateDef.newBuilder()
                            .setName(name)
                            .setAvailability(BusinessPetVaccineAvailabilityDef.newBuilder()
                                    .setOnlyForSpecificPetType(true)
                                    .addAvailablePetTypes(PetType.PET_TYPE_DOG)
                                    .addAvailablePetTypes(PetType.PET_TYPE_CAT)
                                    .build())
                            .build())
                    .build();
            final var actual = petVaccineConverter.createRecord(input);

            var expected = new MoePetVaccineRecord();
            expected.setCompanyId(cid);
            expected.setBusinessId(Math.toIntExact(bid));
            expected.setName(name);
            expected.setOnlyForSpecificPetType(1);
            expected.setAvailablePetTypes("[1,2]");
            expected.setCreateTime(instant.getEpochSecond());
            expected.setUpdateTime(instant.getEpochSecond());

            assertEquals(expected, actual);
        }
    }

    @Test
    void updateRecord_onlyRequiredFields() {
        try (MockedStatic<Instant> mockedStatic = mockStatic(Instant.class)) {
            mockedStatic.when(Instant::now).thenReturn(instant);

            final var bid = 1L;
            final var cid = 2L;
            final var vaccineId = 3L;
            final var name = "new name";
            final var input = UpdatePetVaccineRequest.newBuilder()
                    .setId(vaccineId)
                    .setCompanyId(cid)
                    .setBusinessId(bid)
                    .setVaccine(BusinessPetVaccineUpdateDef.newBuilder()
                            .setName(name)
                            .build())
                    .build();

            var record = new MoePetVaccineRecord();
            record.setId(Math.toIntExact(vaccineId));
            record.setCompanyId(cid);
            record.setBusinessId(Math.toIntExact(bid));
            record.setName("old name");
            record.setOnlyForSpecificPetType(0);
            record.setAvailablePetTypes("[]");
            record.setCreateTime(instant.getEpochSecond());
            record.setUpdateTime(instant.getEpochSecond());

            petVaccineConverter.updateRecord(input, record);

            var expected = new MoePetVaccineRecord();
            expected.setId(Math.toIntExact(vaccineId));
            expected.setCompanyId(cid);
            expected.setBusinessId(Math.toIntExact(bid));
            expected.setName(name);
            expected.setOnlyForSpecificPetType(0);
            expected.setAvailablePetTypes("[]");
            expected.setCreateTime(instant.getEpochSecond());
            expected.setUpdateTime(instant.getEpochSecond());

            assertEquals(expected, record);
        }
    }

    @Test
    void updateRecord_withAvailabilityByPetType() {
        try (MockedStatic<Instant> mockedStatic = mockStatic(Instant.class)) {
            mockedStatic.when(Instant::now).thenReturn(instant);

            final var bid = 1L;
            final var cid = 2L;
            final var vaccineId = 3L;
            final var name = "new name";
            final var input = UpdatePetVaccineRequest.newBuilder()
                    .setId(vaccineId)
                    .setCompanyId(cid)
                    .setBusinessId(bid)
                    .setVaccine(BusinessPetVaccineUpdateDef.newBuilder()
                            .setName(name)
                            .setAvailability(BusinessPetVaccineAvailabilityDef.newBuilder()
                                    .setOnlyForSpecificPetType(true)
                                    .addAvailablePetTypes(PetType.PET_TYPE_DOG)
                                    .addAvailablePetTypes(PetType.PET_TYPE_CAT)
                                    .build())
                            .build())
                    .build();

            var record = new MoePetVaccineRecord();
            record.setId(Math.toIntExact(vaccineId));
            record.setCompanyId(cid);
            record.setBusinessId(Math.toIntExact(bid));
            record.setName("old name");
            record.setOnlyForSpecificPetType(0);
            record.setAvailablePetTypes("[]");
            record.setCreateTime(instant.getEpochSecond());
            record.setUpdateTime(instant.getEpochSecond());

            petVaccineConverter.updateRecord(input, record);

            var expected = new MoePetVaccineRecord();
            expected.setId(Math.toIntExact(vaccineId));
            expected.setCompanyId(cid);
            expected.setBusinessId(Math.toIntExact(bid));
            expected.setName(name);
            expected.setOnlyForSpecificPetType(1);
            expected.setAvailablePetTypes("[1,2]");
            expected.setCreateTime(instant.getEpochSecond());
            expected.setUpdateTime(instant.getEpochSecond());

            assertEquals(expected, record);
        }
    }

    @Test
    void toModel() {
        try (MockedStatic<Instant> mockedStatic = mockStatic(Instant.class)) {
            mockedStatic.when(Instant::now).thenReturn(instant);

            final var bid = 1L;
            final var cid = 2L;
            final var vaccineId = 3L;
            final var name = "test vaccine";
            final var sort = 1;

            var record = new MoePetVaccineRecord();
            record.setId(Math.toIntExact(vaccineId));
            record.setName(name);
            record.setBusinessId(Math.toIntExact(bid));
            record.setSort(sort);
            record.setStatus(0);
            record.setCreateTime(instant.getEpochSecond());
            record.setUpdateTime(instant.getEpochSecond());
            record.setCompanyId(cid);
            record.setOnlyForSpecificPetType(1);
            record.setAvailablePetTypes("[1,2]");

            var expected = BusinessPetVaccineModel.newBuilder()
                    .setId(vaccineId)
                    .setName(name)
                    .setSort(sort)
                    .setDeleted(false)
                    .setAvailability(BusinessPetVaccineAvailabilityDef.newBuilder()
                            .setOnlyForSpecificPetType(true)
                            .addAvailablePetTypes(PetType.PET_TYPE_DOG)
                            .addAvailablePetTypes(PetType.PET_TYPE_CAT)
                            .build())
                    .build();

            var actual = petVaccineConverter.toModel(record);

            assertEquals(expected, actual);
        }
    }
}
