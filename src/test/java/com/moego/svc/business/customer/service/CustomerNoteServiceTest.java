package com.moego.svc.business.customer.service;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.idl.models.business_customer.v1.BusinessCustomerNoteCreateDef;
import com.moego.idl.models.business_customer.v1.BusinessCustomerNoteUpdateDef;
import com.moego.lib.common.exception.BizException;
import com.moego.svc.business.customer.enums.option.GetOption;
import com.moego.svc.business.customer.repository.jooq.tables.records.MoeCustomerNoteRecord;
import com.moego.svc.business.customer.utils.TimeUtils;
import java.time.Instant;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@ActiveProfiles("unit-test")
@Transactional
public class CustomerNoteServiceTest {

    @Autowired
    CustomerNoteService customerNoteService;

    @Test
    void testCustomerNote() throws InterruptedException {
        var customerId = 1;
        var noteString = "This is a note";
        var createdBy = 1L;

        // 初始没有 note
        var notes = customerNoteService.listCustomerNote(customerId);
        assertThat(notes).isEmpty();

        var createDef =
                BusinessCustomerNoteCreateDef.newBuilder().setNote(noteString).build();

        // 创建一个正常的 note
        var id1 = customerNoteService.createCustomerNote(customerId, createdBy, createDef);
        var note1 = customerNoteService.getCustomerNote(id1);
        assertThat(note1.getCustomerId()).isEqualTo(customerId);
        assertThat(note1.getNote()).isEqualTo(noteString);
        assertThat(note1.getStaffId()).isEqualTo(createdBy);
        assertThat(note1.getLastStaffId()).isEqualTo(createdBy);
        assertThat(note1.getCreateTime()).isNotNull();
        assertThat(note1.getCreateTime()).isGreaterThan(0L);
        assertThat(note1.getUpdateTime()).isNotNull();
        assertThat(note1.getUpdateTime()).isGreaterThan(0L);

        // 不带 createdBy 创建 note
        var id2 = customerNoteService.createCustomerNote(customerId, null, createDef);
        var note2 = customerNoteService.getCustomerNote(id2);
        assertThat(note2.getCustomerId()).isEqualTo(customerId);
        assertThat(note2.getNote()).isEqualTo(noteString);
        assertThat(note2.getStaffId()).isEqualTo(0L);
        assertThat(note2.getLastStaffId()).isEqualTo(0L);
        assertThat(note2.getCreateTime()).isNotNull();
        assertThat(note2.getCreateTime()).isGreaterThan(0L);
        assertThat(note2.getUpdateTime()).isNotNull();
        assertThat(note2.getUpdateTime()).isGreaterThan(0L);

        // 指定 createdAt 和 updatedAt
        long createdAt = Instant.now().getEpochSecond() - 10000;
        long updatedAt = Instant.now().getEpochSecond() - 5000;
        var createDef2 = BusinessCustomerNoteCreateDef.newBuilder()
                .setNote(noteString)
                .setCreatedAt(TimeUtils.secondToTimestamp(createdAt))
                .setUpdatedAt(TimeUtils.secondToTimestamp(updatedAt))
                .build();

        var id3 = customerNoteService.createCustomerNote(customerId, createdBy, createDef2);
        var note3 = customerNoteService.getCustomerNote(id3);
        assertThat(note3.getCustomerId()).isEqualTo(customerId);
        assertThat(note3.getNote()).isEqualTo(noteString);
        assertThat(note3.getStaffId()).isEqualTo(createdBy);
        assertThat(note3.getLastStaffId()).isEqualTo(createdBy);
        assertThat(note3.getCreateTime()).isNotNull();
        assertThat(note3.getCreateTime()).isEqualTo(createdAt);
        assertThat(note3.getUpdateTime()).isNotNull();
        assertThat(note3.getUpdateTime()).isEqualTo(updatedAt);

        // list notes
        notes = customerNoteService.listCustomerNote(customerId);
        assertThat(notes.size()).isEqualTo(3);
        var ids = notes.stream()
                .map(MoeCustomerNoteRecord::getId)
                .map(Integer::longValue)
                .collect(Collectors.toSet());
        assertThat(ids).contains(id1, id2, id3);

        // update note
        TimeUnit.SECONDS.sleep(1);
        var newNoteString = "This is a new note";
        var updatedBy = 2L;
        var updateDef = BusinessCustomerNoteUpdateDef.newBuilder()
                .setNote(newNoteString)
                .build();
        customerNoteService.updateCustomerNote(id1, updatedBy, updateDef);
        var updatedNote = customerNoteService.getCustomerNote(id1);
        assertThat(updatedNote.getCustomerId()).isEqualTo(customerId);
        assertThat(updatedNote.getNote()).isEqualTo(newNoteString);
        assertThat(updatedNote.getStaffId()).isEqualTo(createdBy);
        assertThat(updatedNote.getLastStaffId()).isEqualTo(updatedBy);
        assertThat(updatedNote.getCreateTime()).isNotNull();
        assertThat(updatedNote.getCreateTime()).isGreaterThan(0L);
        assertThat(updatedNote.getUpdateTime()).isNotNull();
        assertThat(updatedNote.getUpdateTime()).isGreaterThan(updatedNote.getCreateTime());

        // delete note
        customerNoteService.deleteCustomerNote(id2);
        customerNoteService.deleteCustomerNote(id2);

        // list notes
        notes = customerNoteService.listCustomerNote(customerId);
        assertThat(notes.size()).isEqualTo(2);
        ids = notes.stream()
                .map(MoeCustomerNoteRecord::getId)
                .map(Integer::longValue)
                .collect(Collectors.toSet());
        assertThat(ids).contains(id1, id3);
    }

    @Test
    void testBatchCreateCustomerNote() {
        var customerId = 1;
        var createdAt = TimeUtils.secondToTimestamp(Instant.now().getEpochSecond() - 10000);
        var updatedAt = TimeUtils.secondToTimestamp(Instant.now().getEpochSecond() - 5000);
        var createDefs = List.of(
                BusinessCustomerNoteCreateDef.newBuilder().setNote("Note 1").build(),
                BusinessCustomerNoteCreateDef.newBuilder()
                        .setNote("Note 2")
                        .setCreatedAt(createdAt)
                        .setUpdatedAt(updatedAt)
                        .build());

        customerNoteService.batchCreateCustomerNote(customerId, null, createDefs);

        var notes = customerNoteService.listCustomerNote(customerId);
        assertThat(notes.size()).isEqualTo(2);
        notes.forEach(note -> {
            assertThat(note.getCustomerId()).isEqualTo(customerId);
            assertThat(note.getStaffId()).isEqualTo(0);
            assertThat(note.getLastStaffId()).isEqualTo(0);
            assertThat(note.getCreateTime()).isNotNull();
            assertThat(note.getUpdateTime()).isNotNull();

            if (note.getNote().equals("Note 1")) {
                assertThat(note.getCreateTime()).isGreaterThan(0L);
                assertThat(note.getUpdateTime()).isGreaterThan(0L);
            } else if (note.getNote().equals("Note 2")) {
                assertThat(note.getCreateTime()).isEqualTo(createdAt.getSeconds());
                assertThat(note.getUpdateTime()).isEqualTo(updatedAt.getSeconds());
            } else {
                Assertions.fail();
            }
        });
    }

    @Test
    void testNoteLimit() {
        var customerId = 1;
        // 使用 stream 生产 1~200 的数字
        var createDefs = Stream.iterate(1, i -> i + 1)
                .limit(CustomerNoteService.MAX_CUSTOMER_NOTE_COUNT - 1)
                .map(i -> BusinessCustomerNoteCreateDef.newBuilder()
                        .setNote("Note " + i)
                        .build())
                .toList();

        // 批量插入成功
        Assertions.assertDoesNotThrow(() -> customerNoteService.batchCreateCustomerNote(customerId, null, createDefs));
        // 再次批量插入失败
        Assertions.assertThrows(
                BizException.class, () -> customerNoteService.batchCreateCustomerNote(customerId, null, createDefs));

        var createDef =
                BusinessCustomerNoteCreateDef.newBuilder().setNote("Note").build();
        // 因为留了一个, 可以插入成功
        Assertions.assertDoesNotThrow(() -> customerNoteService.createCustomerNote(customerId, null, createDef));
        // 再次插入失败
        Assertions.assertThrows(
                BizException.class, () -> customerNoteService.createCustomerNote(customerId, null, createDef));
    }

    @Test
    void testGetOptions() {
        var customerId = 1L;
        var nonExistId = -1L;

        Assertions.assertThrows(BizException.class, () -> customerNoteService.getCustomerNote(nonExistId));
        Assertions.assertNull(customerNoteService.getCustomerNote(nonExistId, GetOption.ACCEPT_NULL));

        var createDef =
                BusinessCustomerNoteCreateDef.newBuilder().setNote("note").build();

        // 创建一个正常的 note
        var id = customerNoteService.createCustomerNote(customerId, null, createDef);
        var note = customerNoteService.getCustomerNote(id);
        assertThat(note).isNotNull();

        customerNoteService.deleteCustomerNote(id);

        Assertions.assertThrows(BizException.class, () -> customerNoteService.getCustomerNote(id));

        note = customerNoteService.getCustomerNote(id, GetOption.INCLUDE_DELETED);
        assertThat(note).isNotNull();
    }
}
