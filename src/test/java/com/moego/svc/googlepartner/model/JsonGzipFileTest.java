package com.moego.svc.googlepartner.model;

import static org.assertj.core.api.Assertions.assertThat;

import java.nio.charset.StandardCharsets;
import org.junit.jupiter.api.Test;

/**
 * {@link JsonGzipFile} tester.
 */
class JsonGzipFileTest {

    @Test
    void testJsonGzipLocalSourceFile() {
        String jsonString =
                """
                {
                    "foo": "bar",
                    "baz": "qux",
                    "quux": "corge",
                    "grault": "garply",
                    "waldo": "fred",
                    "plugh": "xyzzy"
                }
            """;
        JsonGzipFile file = new JsonGzipFile("foo.json", jsonString);

        assertThat(file.getName()).isEqualTo("foo.json.gz");
        assertThat(file.getRawLength()).isEqualTo(jsonString.getBytes(StandardCharsets.UTF_8).length);
    }
}
