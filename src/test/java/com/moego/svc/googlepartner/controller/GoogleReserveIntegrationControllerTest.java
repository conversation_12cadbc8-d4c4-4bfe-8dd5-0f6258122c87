package com.moego.svc.googlepartner.controller;

import static com.moego.svc.googlepartner.controller.GoogleReserveIntegrationController.getBusinessId;
import static org.assertj.core.api.Assertions.assertThat;

import com.moego.svc.googlepartner.model.google.MerchantStatus;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * {@link GoogleReserveIntegrationController} tester.
 */
class GoogleReserveIntegrationControllerTest {

    /**
     * {@link GoogleReserveIntegrationController#getBusinessId(MerchantStatus)}
     */
    @Test
    void testGetBusinessId() {
        MerchantStatus merchantStatus = new MerchantStatus();
        merchantStatus.setName("partners/20002025/merchants/108341/status");

        assertThat(getBusinessId(merchantStatus)).isEqualTo(108341);
    }

    @Nested
    @SpringBootTest
    @Disabled("local test only")
    class UpdateStatusToUnmatchedTest {

        @Autowired
        GoogleReserveIntegrationController controller;

        /**
         * {@link GoogleReserveIntegrationController#updateStatusToUnmatched(List)}
         */
        @Test
        void testUpdateStatusToUnmatched() {
            controller.updateStatusToUnmatched(List.of(100611));
        }

        /**
         * {@link GoogleReserveIntegrationController#updateStatusToMatched(List)}
         */
        @Test
        void testUpdateStatusToMatched() {
            controller.updateStatusToMatched(List.of(100611));
        }
    }
}
