package com.moego.svc.googlepartner.service.google.appointmentbooking;

import static org.assertj.core.api.Assertions.assertThat;

import java.util.Set;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * {@link EntityFeedService} tester.
 */
class EntityFeedServiceTest {

    /**
     * {@link EntityFeedService#getLandingPageUrl(String, String)}
     */
    @Test
    void getLandingPageUrl() {

        var urlTemplate = "https://booking.moego.pet/ol/landing?name=%s";

        assertThat(EntityFeedService.getLandingPageUrl(urlTemplate, "DTailsPetBoutique&Spa"))
                .isEqualTo("https://booking.moego.pet/ol/landing?name=DTailsPetBoutique%26Spa");

        assertThat(EntityFeedService.getLandingPageUrl(urlTemplate, "DTails Pet Boutique & Spa"))
                .isEqualTo("https://booking.moego.pet/ol/landing?name=DTails%20Pet%20Boutique%20%26%20Spa");

        assertThat(EntityFeedService.getLandingPageUrl(urlTemplate, "DTailsPetBoutiqueSpa"))
                .isEqualTo("https://booking.moego.pet/ol/landing?name=DTailsPetBoutiqueSpa");
    }

    @Nested
    @SpringBootTest
    @Disabled("This test is for local test only.")
    class IT {

        @Autowired
        EntityFeedService entityFeedService;

        /**
         * {@link EntityFeedService#getEntityFeeds}
         */
        @Test
        void testSendEntityFeeds() {
            entityFeedService.getEntityFeeds(Set.of(100611));
        }
    }
}
