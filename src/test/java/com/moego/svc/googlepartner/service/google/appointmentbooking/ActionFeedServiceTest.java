package com.moego.svc.googlepartner.service.google.appointmentbooking;

import java.util.Set;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * {@link ActionFeedService} tester.
 */
@SpringBootTest
@Disabled("This test is for local test only.")
class ActionFeedServiceTest {

    @Autowired
    ActionFeedService actionFeedService;

    /**
     * {@link ActionFeedService#getActionFeeds}
     */
    @Test
    void testSendActionFeeds() {
        actionFeedService.getActionFeeds(Set.of(100611));
    }
}
