package com.moego.svc.googlepartner.service.google.appointmentbooking;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;

import com.moego.server.grooming.api.IGroomingOnlineBookingService;
import com.moego.server.grooming.api.IGroomingServiceCategoryService;
import com.moego.server.grooming.api.IGroomingServiceService;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.GroomingServiceDTO;
import com.moego.server.grooming.dto.ServiceCategoryDTO;
import com.moego.svc.googlepartner.model.google.appointmentbooking.feed.ServiceFeed;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.util.ReflectionTestUtils;

/**
 * {@link ServiceFeedService} tester.
 */
@ExtendWith(MockitoExtension.class)
class ServiceFeedServiceTest {

    @Mock
    IGroomingServiceService serviceApi;

    @Mock
    IGroomingServiceCategoryService serviceCategoryApi;

    @Mock
    IGroomingOnlineBookingService bookOnlineApi;

    @InjectMocks
    ServiceFeedService serviceFeedService;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(serviceFeedService, "obUrlFormat", "https://booking.moego.pet/ol/landing?name=%s");
    }

    @Test
    void testGenerateServiceFeeds_Success() {
        // Arrange
        var businessIds = Set.of(1, 2);

        var s1 = new GroomingServiceDTO();
        s1.setId(101);
        s1.setBusinessId(1);
        s1.setCategoryId(1);
        s1.setName("Bath");
        s1.setDescription("Bath your pet");
        s1.setType((byte) 1);
        s1.setPrice(new BigDecimal("100"));
        s1.setDuration(60);
        s1.setInactive((byte) 0);
        s1.setStatus((byte) 1);
        s1.setShowBasePrice((byte) 1);
        s1.setBookOnlineAvailable((byte) 1);

        var s2 = new GroomingServiceDTO();
        s2.setId(102);
        s2.setBusinessId(1);
        s2.setCategoryId(2);
        s2.setName("Grooming");
        s2.setDescription("Groom your pet");
        s2.setType((byte) 1);
        s2.setPrice(new BigDecimal("200"));
        s2.setDuration(90);
        s2.setInactive((byte) 0);
        s2.setStatus((byte) 1);
        s2.setShowBasePrice((byte) 1);
        s2.setBookOnlineAvailable((byte) 1);

        var s3 = new GroomingServiceDTO();
        s3.setId(201);
        s3.setBusinessId(2);
        s3.setCategoryId(1);
        s3.setName("Nail Trim");
        s3.setDescription("Trim your pet's nails");
        s3.setType((byte) 1);
        s3.setPrice(new BigDecimal("100"));
        s3.setDuration(30);
        s3.setInactive((byte) 0);
        s3.setStatus((byte) 1);
        s3.setShowBasePrice((byte) 1);
        s3.setBookOnlineAvailable((byte) 1);

        var s4 = new GroomingServiceDTO();
        s4.setId(202);
        s4.setBusinessId(2);
        s4.setCategoryId(2);
        s4.setName("Bath");
        s4.setDescription("Bath your pet");
        s4.setType((byte) 1);
        s4.setPrice(new BigDecimal("200"));
        s4.setDuration(60);
        s4.setInactive((byte) 0);
        s4.setStatus((byte) 1);
        s4.setShowBasePrice((byte) 1);
        s4.setBookOnlineAvailable((byte) 1);

        when(serviceApi.getServices(businessIds)).thenReturn(Map.of(1, List.of(s1, s2), 2, List.of(s3, s4)));

        var c1 = new ServiceCategoryDTO();
        c1.setId(1);
        c1.setName("Hygiene");

        var c2 = new ServiceCategoryDTO();
        c2.setId(2);
        c2.setName("Beauty");

        when(serviceCategoryApi.getServiceCategories(Set.of(1, 2))).thenReturn(List.of(c1, c2));

        var b1 = new BookOnlineDTO();
        b1.setBusinessId(1);
        b1.setBookOnlineName("RainyDayGrooming");

        var b2 = new BookOnlineDTO();
        b2.setBusinessId(2);
        b2.setBookOnlineName("DTailsPetBoutique&Spa");

        when(bookOnlineApi.listOBSetting(anyList())).thenReturn(List.of(b1, b2));

        // Act
        var actual = serviceFeedService.getServiceFeeds(businessIds).getData();

        var expected = new ArrayList<ServiceFeed.Service>();

        var service202 = new ServiceFeed.Service();
        service202.setMerchantId("2");
        service202.setServiceId("202");
        service202.setLocalizedServiceName(
                new ServiceFeed.Text().setValue("Bath").setLocalizedValue(null));
        service202.setLocalizedServiceCategory(
                new ServiceFeed.Text().setValue("Beauty").setLocalizedValue(null));
        service202.setLocalizedServiceDescription(
                new ServiceFeed.Text().setValue("Bath your pet").setLocalizedValue(null));
        service202.setServicePrice(new ServiceFeed.ServicePrice()
                .setPriceInterpretation(ServiceFeed.RangeInterpretation.INTERPRETATION_EXACT)
                .setMinPrice(new ServiceFeed.Price()
                        .setPriceMicros(200_000_000L)
                        .setCurrencyCode("USD")
                        .setPricingOptionTag(null))
                .setMaxPrice(null));
        service202.setServiceDuration(new ServiceFeed.ServiceDuration()
                .setDurationInterpretation(ServiceFeed.RangeInterpretation.INTERPRETATION_STARTS_AT)
                .setMinDurationSec(3600L)
                .setMaxDurationSec(null));
        service202.setActionLink(List.of(new ServiceFeed.ActionLink()
                .setUrl("https://booking.moego.pet/ol/landing?name=DTailsPetBoutique%26Spa&source=gr&service=202")));
        service202.setRankingHint(new ServiceFeed.ServiceRankingHint().setScore(0.0));
        expected.add(service202);

        var service201 = new ServiceFeed.Service();
        service201.setMerchantId("2");
        service201.setServiceId("201");
        service201.setLocalizedServiceName(
                new ServiceFeed.Text().setValue("Nail Trim").setLocalizedValue(null));
        service201.setLocalizedServiceCategory(
                new ServiceFeed.Text().setValue("Hygiene").setLocalizedValue(null));
        service201.setLocalizedServiceDescription(
                new ServiceFeed.Text().setValue("Trim your pet's nails").setLocalizedValue(null));
        service201.setServicePrice(new ServiceFeed.ServicePrice()
                .setPriceInterpretation(ServiceFeed.RangeInterpretation.INTERPRETATION_EXACT)
                .setMinPrice(new ServiceFeed.Price()
                        .setPriceMicros(100_000_000L)
                        .setCurrencyCode("USD")
                        .setPricingOptionTag(null))
                .setMaxPrice(null));
        service201.setServiceDuration(new ServiceFeed.ServiceDuration()
                .setDurationInterpretation(ServiceFeed.RangeInterpretation.INTERPRETATION_STARTS_AT)
                .setMinDurationSec(1800L)
                .setMaxDurationSec(null));
        service201.setActionLink(List.of(new ServiceFeed.ActionLink()
                .setUrl("https://booking.moego.pet/ol/landing?name=DTailsPetBoutique%26Spa&source=gr&service=201")));
        service201.setRankingHint(new ServiceFeed.ServiceRankingHint().setScore(0.0));
        expected.add(service201);

        var service102 = new ServiceFeed.Service();
        service102.setMerchantId("1");
        service102.setServiceId("102");
        service102.setLocalizedServiceName(
                new ServiceFeed.Text().setValue("Grooming").setLocalizedValue(null));
        service102.setLocalizedServiceCategory(
                new ServiceFeed.Text().setValue("Beauty").setLocalizedValue(null));
        service102.setLocalizedServiceDescription(
                new ServiceFeed.Text().setValue("Groom your pet").setLocalizedValue(null));
        service102.setServicePrice(new ServiceFeed.ServicePrice()
                .setPriceInterpretation(ServiceFeed.RangeInterpretation.INTERPRETATION_EXACT)
                .setMinPrice(new ServiceFeed.Price()
                        .setPriceMicros(200_000_000L)
                        .setCurrencyCode("USD")
                        .setPricingOptionTag(null))
                .setMaxPrice(null));
        service102.setServiceDuration(new ServiceFeed.ServiceDuration()
                .setDurationInterpretation(ServiceFeed.RangeInterpretation.INTERPRETATION_STARTS_AT)
                .setMinDurationSec(5400L)
                .setMaxDurationSec(null));
        service102.setActionLink(List.of(new ServiceFeed.ActionLink()
                .setUrl("https://booking.moego.pet/ol/landing?name=RainyDayGrooming&source=gr&service=102")));
        service102.setRankingHint(new ServiceFeed.ServiceRankingHint().setScore(0.0));
        expected.add(service102);

        var service101 = new ServiceFeed.Service();
        service101.setMerchantId("1");
        service101.setServiceId("101");
        service101.setLocalizedServiceName(
                new ServiceFeed.Text().setValue("Bath").setLocalizedValue(null));
        service101.setLocalizedServiceCategory(
                new ServiceFeed.Text().setValue("Hygiene").setLocalizedValue(null));
        service101.setLocalizedServiceDescription(
                new ServiceFeed.Text().setValue("Bath your pet").setLocalizedValue(null));
        service101.setServicePrice(new ServiceFeed.ServicePrice()
                .setPriceInterpretation(ServiceFeed.RangeInterpretation.INTERPRETATION_EXACT)
                .setMinPrice(new ServiceFeed.Price()
                        .setPriceMicros(100_000_000L)
                        .setCurrencyCode("USD")
                        .setPricingOptionTag(null))
                .setMaxPrice(null));
        service101.setServiceDuration(new ServiceFeed.ServiceDuration()
                .setDurationInterpretation(ServiceFeed.RangeInterpretation.INTERPRETATION_STARTS_AT)
                .setMinDurationSec(3600L)
                .setMaxDurationSec(null));
        service101.setActionLink(List.of(new ServiceFeed.ActionLink()
                .setUrl("https://booking.moego.pet/ol/landing?name=RainyDayGrooming&source=gr&service=101")));
        service101.setRankingHint(new ServiceFeed.ServiceRankingHint().setScore(0.0));
        expected.add(service101);

        // Assert
        assertThat(actual).containsExactlyInAnyOrderElementsOf(expected);
    }

    /**
     * {@link ServiceFeedService#containsNewline(String)}
     */
    @Test
    void testContainsNewline() {
        assertThat(ServiceFeedService.containsNewline(
                        """
                test
                test
                test"""))
                .isTrue();

        assertThat(ServiceFeedService.containsNewline("a\na")).isTrue();
        assertThat(ServiceFeedService.containsNewline("a\\na")).isFalse();
        assertThat(ServiceFeedService.containsNewline("a\\\na")).isTrue();
    }

    @Nested
    @SpringBootTest
    @Disabled("This test is for local test only.")
    class IT {

        @Autowired
        ServiceFeedService serviceFeedService;

        /**
         * {@link ServiceFeedService#getServiceFeeds}
         */
        @Test
        void testSendServiceFeeds() {
            serviceFeedService.getServiceFeeds(Set.of(100611));
        }
    }
}
