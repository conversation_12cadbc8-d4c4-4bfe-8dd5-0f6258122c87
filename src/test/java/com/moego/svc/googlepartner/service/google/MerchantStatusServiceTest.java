package com.moego.svc.googlepartner.service.google;

import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.googlepartner.model.google.MerchantStatus;
import com.moego.svc.googlepartner.service.param.ListMerchantStatusParam;
import java.util.List;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * {@link MerchantStatusService} tester.
 */
@SpringBootTest
@Disabled("local test only")
class MerchantStatusServiceTest {
    private static final Logger log = LoggerFactory.getLogger(MerchantStatusServiceTest.class);

    @Autowired
    MerchantStatusService merchantStatusService;

    /**
     * {@link MerchantStatusService#listMerchantStatus(ListMerchantStatusParam)}
     */
    @Test
    void testListMerchantStatus() {
        List<MerchantStatus> statuses = merchantStatusService.listMerchantStatus(new ListMerchantStatusParam()
                .setPageSize(20)
                .setGeoMatchRestrict(ListMerchantStatusParam.GeoMatchingStatus.GEO_MATCHED));
        statuses.forEach(s -> log.info("{}", JsonUtil.toJson(s)));
    }
}
