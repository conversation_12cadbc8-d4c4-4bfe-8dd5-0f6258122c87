package com.moego.svc.googlepartner.service.google.appointmentbooking;

import java.util.Set;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

/**
 * {@link GoogleBookingService} tester.
 */
@SpringBootTest
@Disabled("This test is for local test only.")
class GoogleBookingServiceTest {

    @Autowired
    GoogleBookingService service;

    /**
     * {@link GoogleBookingService#sendFeedsToSandbox(Set)}
     */
    @Test
    void testSendFeedsToSandbox() {
        service.sendFeedsToSandbox(Set.of(100611));
    }
}
