package com.moego.svc.googlepartner.util;

import static org.assertj.core.api.Assertions.assertThat;

import com.moego.svc.googlepartner.model.JsonSplitResult;
import org.junit.jupiter.api.Test;
import org.springframework.util.unit.DataSize;

/**
 * {@link JsonSplitter} tester.
 */
class JsonSplitterTest {

    /**
     * {@link JsonSplitter#splitJsonArray}
     */
    @Test
    void testSplitJsonArray() {
        String fileName = "foo.json.gz";
        String jsonArray =
                """
            [
                {
                    "id": 1,
                    "name": "<PERSON>"
                },
                {
                    "id": 2,
                    "name": "XXX"
                },
                {
                    "id": 3,
                    "name": "xxaxaxa"
                }
            ]
            """;

        JsonSplitResult result = JsonSplitter.splitJsonArray(fileName, jsonArray, DataSize.ofBytes(30));
        assertThat(result.getPieces()).hasSize(3);
        assertThat(result.getPieces().get(0).getFileName()).isEqualTo("foo_0.json.gz");
        assertThat(result.getPieces().get(0).getJsonValue())
                .isEqualTo("""
            [{"id":1,"name":"John"}]
            """.trim());

        result = JsonSplitter.splitJsonArray(fileName, jsonArray, DataSize.ofBytes(45));
        assertThat(result.getPieces()).hasSize(2);

        result = JsonSplitter.splitJsonArray(fileName, jsonArray, DataSize.ofBytes(90));
        assertThat(result.getPieces()).hasSize(1);
    }
}
