// Code generated by github.com/jmattheis/goverter, DO NOT EDIT.
//go:build !goverter

package impl

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	converter "github.com/MoeGolibrary/moego-svc-fulfillment/internal/converter"
	filter "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	model "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	opt "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/opt"
	dto "github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	gorm "gorm.io/gorm"
	"time"
)

type GroupClassConverter struct{}

func (c *GroupClassConverter) AttendanceModelToPB(source []*model.GroupClassAttendance) []*v1.GroupClassAttendanceModel {
	var pFulfillmentpbGroupClassAttendanceModelList []*v1.GroupClassAttendanceModel
	if source != nil {
		pFulfillmentpbGroupClassAttendanceModelList = make([]*v1.GroupClassAttendanceModel, len(source))
		for i := 0; i < len(source); i++ {
			pFulfillmentpbGroupClassAttendanceModelList[i] = c.pModelGroupClassAttendanceToPFulfillmentpbGroupClassAttendanceModel(source[i])
		}
	}
	return pFulfillmentpbGroupClassAttendanceModelList
}
func (c *GroupClassConverter) CreatePBToDetailsDTO(source *v1.GroupClassDetailCreateDef) *dto.GroupClassDetailsDTO {
	var pDtoGroupClassDetailsDTO *dto.GroupClassDetailsDTO
	if source != nil {
		var dtoGroupClassDetailsDTO dto.GroupClassDetailsDTO
		dtoGroupClassDetailsDTO.PetID = (*source).PetId
		dtoGroupClassDetailsDTO.GroupClassInstanceID = (*source).GroupClassInstanceId
		pDtoGroupClassDetailsDTO = &dtoGroupClassDetailsDTO
	}
	return pDtoGroupClassDetailsDTO
}
func (c *GroupClassConverter) CreatePBToDetailsDTOs(source []*v1.GroupClassDetailCreateDef) []*dto.GroupClassDetailsDTO {
	var pDtoGroupClassDetailsDTOList []*dto.GroupClassDetailsDTO
	if source != nil {
		pDtoGroupClassDetailsDTOList = make([]*dto.GroupClassDetailsDTO, len(source))
		for i := 0; i < len(source); i++ {
			pDtoGroupClassDetailsDTOList[i] = c.CreatePBToDetailsDTO(source[i])
		}
	}
	return pDtoGroupClassDetailsDTOList
}
func (c *GroupClassConverter) FilterToOpt(source filter.ListGroupClassDetailFilter) opt.ListGroupClassDetailOpt {
	var optListGroupClassDetailOpt opt.ListGroupClassDetailOpt
	if source.IDs != nil {
		optListGroupClassDetailOpt.IDs = make([]int64, len(source.IDs))
		for i := 0; i < len(source.IDs); i++ {
			optListGroupClassDetailOpt.IDs[i] = source.IDs[i]
		}
	}
	if source.FulfillmentIDs != nil {
		optListGroupClassDetailOpt.FulfillmentIDs = make([]int64, len(source.FulfillmentIDs))
		for j := 0; j < len(source.FulfillmentIDs); j++ {
			optListGroupClassDetailOpt.FulfillmentIDs[j] = source.FulfillmentIDs[j]
		}
	}
	if source.PetIDs != nil {
		optListGroupClassDetailOpt.PetIDs = make([]int64, len(source.PetIDs))
		for k := 0; k < len(source.PetIDs); k++ {
			optListGroupClassDetailOpt.PetIDs[k] = source.PetIDs[k]
		}
	}
	if source.GroupClassIDs != nil {
		optListGroupClassDetailOpt.GroupClassIDs = make([]int64, len(source.GroupClassIDs))
		for l := 0; l < len(source.GroupClassIDs); l++ {
			optListGroupClassDetailOpt.GroupClassIDs[l] = source.GroupClassIDs[l]
		}
	}
	if source.GroupClassInstanceIDs != nil {
		optListGroupClassDetailOpt.GroupClassInstanceIDs = make([]int64, len(source.GroupClassInstanceIDs))
		for m := 0; m < len(source.GroupClassInstanceIDs); m++ {
			optListGroupClassDetailOpt.GroupClassInstanceIDs[m] = source.GroupClassInstanceIDs[m]
		}
	}
	if source.Statuses != nil {
		optListGroupClassDetailOpt.Statuses = make([]v1.GroupClassDetailModel_Status, len(source.Statuses))
		for n := 0; n < len(source.Statuses); n++ {
			optListGroupClassDetailOpt.Statuses[n] = c.fulfillmentpbGroupClassDetailModel_StatusToFulfillmentpbGroupClassDetailModel_Status(source.Statuses[n])
		}
	}
	return optListGroupClassDetailOpt
}
func (c *GroupClassConverter) ModelToModel(source *model.GroupClassDetail) *model.GroupClassDetail {
	var pModelGroupClassDetail *model.GroupClassDetail
	if source != nil {
		var modelGroupClassDetail model.GroupClassDetail
		modelGroupClassDetail.ID = (*source).ID
		modelGroupClassDetail.FulfillmentID = (*source).FulfillmentID
		modelGroupClassDetail.PetID = (*source).PetID
		modelGroupClassDetail.GroupClassID = (*source).GroupClassID
		modelGroupClassDetail.GroupClassInstanceID = (*source).GroupClassInstanceID
		modelGroupClassDetail.Status = c.fulfillmentpbGroupClassDetailModel_StatusToFulfillmentpbGroupClassDetailModel_Status((*source).Status)
		modelGroupClassDetail.CreatedAt = c.pTimeTimeToPTimeTime((*source).CreatedAt)
		modelGroupClassDetail.UpdatedAt = c.pTimeTimeToPTimeTime((*source).UpdatedAt)
		modelGroupClassDetail.DeletedAt = c.gormDeletedAtToGormDeletedAt((*source).DeletedAt)
		pModelGroupClassDetail = &modelGroupClassDetail
	}
	return pModelGroupClassDetail
}
func (c *GroupClassConverter) ModelToPB(source []*model.GroupClassDetail) []*v1.GroupClassDetailModel {
	var pFulfillmentpbGroupClassDetailModelList []*v1.GroupClassDetailModel
	if source != nil {
		pFulfillmentpbGroupClassDetailModelList = make([]*v1.GroupClassDetailModel, len(source))
		for i := 0; i < len(source); i++ {
			pFulfillmentpbGroupClassDetailModelList[i] = c.pModelGroupClassDetailToPFulfillmentpbGroupClassDetailModel(source[i])
		}
	}
	return pFulfillmentpbGroupClassDetailModelList
}
func (c *GroupClassConverter) UpdateToOpt(source dto.UpdateGroupClassDetailDTO) opt.UpdateGroupClassDetailOpt {
	var optUpdateGroupClassDetailOpt opt.UpdateGroupClassDetailOpt
	if source.Status != nil {
		fulfillmentpbGroupClassDetailModel_Status := c.fulfillmentpbGroupClassDetailModel_StatusToFulfillmentpbGroupClassDetailModel_Status(*source.Status)
		optUpdateGroupClassDetailOpt.Status = &fulfillmentpbGroupClassDetailModel_Status
	}
	return optUpdateGroupClassDetailOpt
}
func (c *GroupClassConverter) fulfillmentpbGroupClassDetailModel_StatusToFulfillmentpbGroupClassDetailModel_Status(source v1.GroupClassDetailModel_Status) v1.GroupClassDetailModel_Status {
	var fulfillmentpbGroupClassDetailModel_Status v1.GroupClassDetailModel_Status
	switch source {
	case v1.GroupClassDetailModel_COMPLETED:
		fulfillmentpbGroupClassDetailModel_Status = v1.GroupClassDetailModel_COMPLETED
	case v1.GroupClassDetailModel_IN_PROGRESS:
		fulfillmentpbGroupClassDetailModel_Status = v1.GroupClassDetailModel_IN_PROGRESS
	case v1.GroupClassDetailModel_NOT_STARTED:
		fulfillmentpbGroupClassDetailModel_Status = v1.GroupClassDetailModel_NOT_STARTED
	case v1.GroupClassDetailModel_STATUS_UNSPECIFIED:
		fulfillmentpbGroupClassDetailModel_Status = v1.GroupClassDetailModel_STATUS_UNSPECIFIED
	default: // ignored
	}
	return fulfillmentpbGroupClassDetailModel_Status
}
func (c *GroupClassConverter) gormDeletedAtToGormDeletedAt(source gorm.DeletedAt) gorm.DeletedAt {
	var gormDeletedAt gorm.DeletedAt
	gormDeletedAt.Time = c.timeTimeToTimeTime(source.Time)
	gormDeletedAt.Valid = source.Valid
	return gormDeletedAt
}
func (c *GroupClassConverter) pModelGroupClassAttendanceToPFulfillmentpbGroupClassAttendanceModel(source *model.GroupClassAttendance) *v1.GroupClassAttendanceModel {
	var pFulfillmentpbGroupClassAttendanceModel *v1.GroupClassAttendanceModel
	if source != nil {
		var fulfillmentpbGroupClassAttendanceModel v1.GroupClassAttendanceModel
		fulfillmentpbGroupClassAttendanceModel.Id = (*source).ID
		fulfillmentpbGroupClassAttendanceModel.FulfillmentId = (*source).FulfillmentID
		fulfillmentpbGroupClassAttendanceModel.PetId = (*source).PetID
		fulfillmentpbGroupClassAttendanceModel.GroupClassDetailId = (*source).GroupClassDetailID
		fulfillmentpbGroupClassAttendanceModel.GroupClassSessionId = (*source).GroupClassSessionID
		fulfillmentpbGroupClassAttendanceModel.CheckInTime = converter.TimeValueToTimestamp((*source).CheckInTime)
		fulfillmentpbGroupClassAttendanceModel.CreatedAt = converter.TimeToTimestamp((*source).CreatedAt)
		fulfillmentpbGroupClassAttendanceModel.UpdatedAt = converter.TimeToTimestamp((*source).UpdatedAt)
		fulfillmentpbGroupClassAttendanceModel.DeletedAt = converter.DeletedAtToTimestamp((*source).DeletedAt)
		pFulfillmentpbGroupClassAttendanceModel = &fulfillmentpbGroupClassAttendanceModel
	}
	return pFulfillmentpbGroupClassAttendanceModel
}
func (c *GroupClassConverter) pModelGroupClassDetailToPFulfillmentpbGroupClassDetailModel(source *model.GroupClassDetail) *v1.GroupClassDetailModel {
	var pFulfillmentpbGroupClassDetailModel *v1.GroupClassDetailModel
	if source != nil {
		var fulfillmentpbGroupClassDetailModel v1.GroupClassDetailModel
		fulfillmentpbGroupClassDetailModel.Id = (*source).ID
		fulfillmentpbGroupClassDetailModel.FulfillmentId = (*source).FulfillmentID
		fulfillmentpbGroupClassDetailModel.PetId = (*source).PetID
		fulfillmentpbGroupClassDetailModel.GroupClassId = (*source).GroupClassID
		fulfillmentpbGroupClassDetailModel.GroupClassInstanceId = (*source).GroupClassInstanceID
		fulfillmentpbGroupClassDetailModel.Status = c.fulfillmentpbGroupClassDetailModel_StatusToFulfillmentpbGroupClassDetailModel_Status((*source).Status)
		fulfillmentpbGroupClassDetailModel.CreatedAt = converter.TimeToTimestamp((*source).CreatedAt)
		fulfillmentpbGroupClassDetailModel.UpdatedAt = converter.TimeToTimestamp((*source).UpdatedAt)
		fulfillmentpbGroupClassDetailModel.DeletedAt = converter.DeletedAtToTimestamp((*source).DeletedAt)
		pFulfillmentpbGroupClassDetailModel = &fulfillmentpbGroupClassDetailModel
	}
	return pFulfillmentpbGroupClassDetailModel
}
func (c *GroupClassConverter) pTimeTimeToPTimeTime(source *time.Time) *time.Time {
	var pTimeTime *time.Time
	if source != nil {
		var timeTime time.Time
		_ = (*source)
		pTimeTime = &timeTime
	}
	return pTimeTime
}
func (c *GroupClassConverter) timeTimeToTimeTime(source time.Time) time.Time {
	var timeTime time.Time
	_ = source
	return timeTime
}
