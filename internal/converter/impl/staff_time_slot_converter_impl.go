// Code generated by github.com/jmattheis/goverter, DO NOT EDIT.
//go:build !goverter

package impl

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	converter "github.com/MoeGolibrary/moego-svc-fulfillment/internal/converter"
	model "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	opt "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/opt"
	dto "github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	gorm "gorm.io/gorm"
	"time"
)

type StaffTimeSlotConverter struct{}

func (c *StaffTimeSlotConverter) ModelToModel(source *model.StaffTimeSlot) *model.StaffTimeSlot {
	var pModelStaffTimeSlot *model.StaffTimeSlot
	if source != nil {
		var modelStaffTimeSlot model.StaffTimeSlot
		modelStaffTimeSlot.ID = (*source).ID
		modelStaffTimeSlot.CompanyID = (*source).CompanyID
		modelStaffTimeSlot.BusinessID = (*source).BusinessID
		modelStaffTimeSlot.FulfillmentID = (*source).FulfillmentID
		modelStaffTimeSlot.CareType = c.offeringpbServiceItemTypeToOfferingpbServiceItemType((*source).CareType)
		modelStaffTimeSlot.DetailID = (*source).DetailID
		modelStaffTimeSlot.OrderLineItemID = (*source).OrderLineItemID
		modelStaffTimeSlot.StaffID = (*source).StaffID
		modelStaffTimeSlot.PetID = (*source).PetID
		modelStaffTimeSlot.CustomerID = (*source).CustomerID
		modelStaffTimeSlot.StartDatetime = c.timeTimeToTimeTime((*source).StartDatetime)
		modelStaffTimeSlot.EndDatetime = c.timeTimeToTimeTime((*source).EndDatetime)
		modelStaffTimeSlot.CreatedAt = c.pTimeTimeToPTimeTime((*source).CreatedAt)
		modelStaffTimeSlot.UpdatedAt = c.pTimeTimeToPTimeTime((*source).UpdatedAt)
		modelStaffTimeSlot.DeletedAt = c.gormDeletedAtToGormDeletedAt((*source).DeletedAt)
		pModelStaffTimeSlot = &modelStaffTimeSlot
	}
	return pModelStaffTimeSlot
}
func (c *StaffTimeSlotConverter) UpdateToOpt(source dto.UpdateStaffTimeSlotDTO) opt.UpdateStaffTimeSlotOpt {
	var optUpdateStaffTimeSlotOpt opt.UpdateStaffTimeSlotOpt
	if source.OrderLineItemID != nil {
		xint64 := *source.OrderLineItemID
		optUpdateStaffTimeSlotOpt.OrderLineItemID = &xint64
	}
	if source.StaffID != nil {
		xint642 := *source.StaffID
		optUpdateStaffTimeSlotOpt.StaffID = &xint642
	}
	optUpdateStaffTimeSlotOpt.StartDatetime = converter.PTimeToPTime(source.StartDatetime)
	optUpdateStaffTimeSlotOpt.EndDatetime = converter.PTimeToPTime(source.EndDatetime)
	return optUpdateStaffTimeSlotOpt
}
func (c *StaffTimeSlotConverter) gormDeletedAtToGormDeletedAt(source gorm.DeletedAt) gorm.DeletedAt {
	var gormDeletedAt gorm.DeletedAt
	gormDeletedAt.Time = c.timeTimeToTimeTime(source.Time)
	gormDeletedAt.Valid = source.Valid
	return gormDeletedAt
}
func (c *StaffTimeSlotConverter) offeringpbServiceItemTypeToOfferingpbServiceItemType(source v1.ServiceItemType) v1.ServiceItemType {
	var offeringpbServiceItemType v1.ServiceItemType
	switch source {
	case v1.ServiceItemType_BOARDING:
		offeringpbServiceItemType = v1.ServiceItemType_BOARDING
	case v1.ServiceItemType_DAYCARE:
		offeringpbServiceItemType = v1.ServiceItemType_DAYCARE
	case v1.ServiceItemType_DOG_WALKING:
		offeringpbServiceItemType = v1.ServiceItemType_DOG_WALKING
	case v1.ServiceItemType_EVALUATION:
		offeringpbServiceItemType = v1.ServiceItemType_EVALUATION
	case v1.ServiceItemType_GROOMING:
		offeringpbServiceItemType = v1.ServiceItemType_GROOMING
	case v1.ServiceItemType_GROUP_CLASS:
		offeringpbServiceItemType = v1.ServiceItemType_GROUP_CLASS
	case v1.ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED:
		offeringpbServiceItemType = v1.ServiceItemType_SERVICE_ITEM_TYPE_UNSPECIFIED
	default: // ignored
	}
	return offeringpbServiceItemType
}
func (c *StaffTimeSlotConverter) pTimeTimeToPTimeTime(source *time.Time) *time.Time {
	var pTimeTime *time.Time
	if source != nil {
		timeTime := c.timeTimeToTimeTime((*source))
		pTimeTime = &timeTime
	}
	return pTimeTime
}
func (c *StaffTimeSlotConverter) timeTimeToTimeTime(source time.Time) time.Time {
	var timeTime time.Time
	_ = source
	return timeTime
}
