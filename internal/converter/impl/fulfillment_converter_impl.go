// Code generated by github.com/jmattheis/goverter, DO NOT EDIT.
//go:build !goverter

package impl

import (
	v1 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	converter "github.com/MoeGolibrary/moego-svc-fulfillment/internal/converter"
	model "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	opt "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/opt"
	dto "github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	gorm "gorm.io/gorm"
	"time"
)

type FulfillmentConverter struct{}

func (c *FulfillmentConverter) CreatePBToDetailsDTO(source *v1.FulfillmentCreateDef) *dto.CreateFulfillmentWithDetailsDTO {
	var pDtoCreateFulfillmentWithDetailsDTO *dto.CreateFulfillmentWithDetailsDTO
	if source != nil {
		var dtoCreateFulfillmentWithDetailsDTO dto.CreateFulfillmentWithDetailsDTO
		dtoCreateFulfillmentWithDetailsDTO.CompanyID = (*source).CompanyId
		dtoCreateFulfillmentWithDetailsDTO.BusinessID = (*source).BusinessId
		dtoCreateFulfillmentWithDetailsDTO.CustomerID = (*source).CustomerId
		if (*source).BookingRequestId != nil {
			xint64 := *(*source).BookingRequestId
			dtoCreateFulfillmentWithDetailsDTO.BookingRequestID = &xint64
		}
		dtoCreateFulfillmentWithDetailsDTO.StartDateTime = c.pTimestamppbTimestampToPTimeTime((*source).StartDateTime)
		dtoCreateFulfillmentWithDetailsDTO.EndDateTime = c.pTimestamppbTimestampToPTimeTime((*source).EndDateTime)
		if (*source).ColorCode != nil {
			xstring := *(*source).ColorCode
			dtoCreateFulfillmentWithDetailsDTO.ColorCode = &xstring
		}
		dtoCreateFulfillmentWithDetailsDTO.Status = c.fulfillmentpbStatusToFulfillmentpbStatus((*source).Status)
		dtoCreateFulfillmentWithDetailsDTO.Source = c.fulfillmentpbSourceToFulfillmentpbSource((*source).Source)
		pDtoCreateFulfillmentWithDetailsDTO = &dtoCreateFulfillmentWithDetailsDTO
	}
	return pDtoCreateFulfillmentWithDetailsDTO
}
func (c *FulfillmentConverter) DTOToModel(source *dto.CreateFulfillmentWithDetailsDTO) *model.Fulfillment {
	var pModelFulfillment *model.Fulfillment
	if source != nil {
		var modelFulfillment model.Fulfillment
		modelFulfillment.CompanyID = (*source).CompanyID
		modelFulfillment.BusinessID = (*source).BusinessID
		modelFulfillment.CustomerID = (*source).CustomerID
		if (*source).BookingRequestID != nil {
			modelFulfillment.BookingRequestID = *(*source).BookingRequestID
		}
		modelFulfillment.StartDatetime = c.pTimeTimeToPTimeTime((*source).StartDateTime)
		modelFulfillment.EndDatetime = c.pTimeTimeToPTimeTime((*source).EndDateTime)
		modelFulfillment.Status = c.fulfillmentpbStatusToFulfillmentpbStatus((*source).Status)
		modelFulfillment.Source = c.fulfillmentpbSourceToFulfillmentpbSource((*source).Source)
		modelFulfillment.CreatedAt = converter.NowTime()
		modelFulfillment.UpdatedAt = converter.NowTime()
		if (*source).ColorCode != nil {
			xstring := *(*source).ColorCode
			modelFulfillment.ColorCode = &xstring
		}
		pModelFulfillment = &modelFulfillment
	}
	return pModelFulfillment
}
func (c *FulfillmentConverter) ModelToModel(source *model.Fulfillment) *model.Fulfillment {
	var pModelFulfillment *model.Fulfillment
	if source != nil {
		var modelFulfillment model.Fulfillment
		modelFulfillment.ID = (*source).ID
		modelFulfillment.CompanyID = (*source).CompanyID
		modelFulfillment.BusinessID = (*source).BusinessID
		modelFulfillment.CustomerID = (*source).CustomerID
		modelFulfillment.BookingRequestID = (*source).BookingRequestID
		modelFulfillment.StartDatetime = c.pTimeTimeToPTimeTime((*source).StartDatetime)
		modelFulfillment.EndDatetime = c.pTimeTimeToPTimeTime((*source).EndDatetime)
		modelFulfillment.Status = c.fulfillmentpbStatusToFulfillmentpbStatus((*source).Status)
		modelFulfillment.OrderID = (*source).OrderID
		if (*source).ServiceTypeInclude != nil {
			xint32 := *(*source).ServiceTypeInclude
			modelFulfillment.ServiceTypeInclude = &xint32
		}
		modelFulfillment.Source = c.fulfillmentpbSourceToFulfillmentpbSource((*source).Source)
		modelFulfillment.RepeatRuleID = (*source).RepeatRuleID
		modelFulfillment.CreatedAt = c.pTimeTimeToPTimeTime((*source).CreatedAt)
		modelFulfillment.UpdatedAt = c.pTimeTimeToPTimeTime((*source).UpdatedAt)
		modelFulfillment.DeletedAt = c.gormDeletedAtToGormDeletedAt((*source).DeletedAt)
		if (*source).ColorCode != nil {
			xstring := *(*source).ColorCode
			modelFulfillment.ColorCode = &xstring
		}
		pModelFulfillment = &modelFulfillment
	}
	return pModelFulfillment
}
func (c *FulfillmentConverter) ModelToPB(source *model.Fulfillment) *v1.FulfillmentModel {
	var pFulfillmentpbFulfillmentModel *v1.FulfillmentModel
	if source != nil {
		var fulfillmentpbFulfillmentModel v1.FulfillmentModel
		fulfillmentpbFulfillmentModel.Id = (*source).ID
		fulfillmentpbFulfillmentModel.CompanyId = (*source).CompanyID
		fulfillmentpbFulfillmentModel.BusinessId = (*source).BusinessID
		fulfillmentpbFulfillmentModel.CustomerId = (*source).CustomerID
		fulfillmentpbFulfillmentModel.BookingRequestId = (*source).BookingRequestID
		fulfillmentpbFulfillmentModel.OrderId = (*source).OrderID
		fulfillmentpbFulfillmentModel.StartDateTime = converter.TimeToTimestamp((*source).StartDatetime)
		fulfillmentpbFulfillmentModel.EndDateTime = converter.TimeToTimestamp((*source).EndDatetime)
		fulfillmentpbFulfillmentModel.Status = c.fulfillmentpbStatusToFulfillmentpbStatus((*source).Status)
		if (*source).ColorCode != nil {
			fulfillmentpbFulfillmentModel.ColorCode = *(*source).ColorCode
		}
		if (*source).ServiceTypeInclude != nil {
			fulfillmentpbFulfillmentModel.ServiceTypeInclude = *(*source).ServiceTypeInclude
		}
		fulfillmentpbFulfillmentModel.Source = c.fulfillmentpbSourceToFulfillmentpbSource((*source).Source)
		fulfillmentpbFulfillmentModel.CreatedAt = converter.TimeToTimestamp((*source).CreatedAt)
		fulfillmentpbFulfillmentModel.UpdatedAt = converter.TimeToTimestamp((*source).UpdatedAt)
		fulfillmentpbFulfillmentModel.DeletedAt = converter.DeletedAtToTimestamp((*source).DeletedAt)
		pFulfillmentpbFulfillmentModel = &fulfillmentpbFulfillmentModel
	}
	return pFulfillmentpbFulfillmentModel
}
func (c *FulfillmentConverter) ModelsToPB(source []*model.Fulfillment) []*v1.FulfillmentModel {
	var pFulfillmentpbFulfillmentModelList []*v1.FulfillmentModel
	if source != nil {
		pFulfillmentpbFulfillmentModelList = make([]*v1.FulfillmentModel, len(source))
		for i := 0; i < len(source); i++ {
			pFulfillmentpbFulfillmentModelList[i] = c.ModelToPB(source[i])
		}
	}
	return pFulfillmentpbFulfillmentModelList
}
func (c *FulfillmentConverter) UpdateToOpt(source dto.UpdateFulfillmentDTO) opt.UpdateFulfillmentOpt {
	var optUpdateFulfillmentOpt opt.UpdateFulfillmentOpt
	if source.BookingRequestID != nil {
		xint64 := *source.BookingRequestID
		optUpdateFulfillmentOpt.BookingRequestID = &xint64
	}
	optUpdateFulfillmentOpt.StartDatetime = converter.PTimeToPTime(source.StartDatetime)
	optUpdateFulfillmentOpt.EndDatetime = converter.PTimeToPTime(source.EndDatetime)
	if source.Status != nil {
		fulfillmentpbStatus := c.fulfillmentpbStatusToFulfillmentpbStatus(*source.Status)
		optUpdateFulfillmentOpt.Status = &fulfillmentpbStatus
	}
	if source.OrderID != nil {
		xint642 := *source.OrderID
		optUpdateFulfillmentOpt.OrderID = &xint642
	}
	if source.ServiceTypeInclude != nil {
		xint := *source.ServiceTypeInclude
		optUpdateFulfillmentOpt.ServiceTypeInclude = &xint
	}
	if source.ColorCode != nil {
		xstring := *source.ColorCode
		optUpdateFulfillmentOpt.ColorCode = &xstring
	}
	return optUpdateFulfillmentOpt
}
func (c *FulfillmentConverter) fulfillmentpbSourceToFulfillmentpbSource(source v1.Source) v1.Source {
	var fulfillmentpbSource v1.Source
	switch source {
	case v1.Source_ANDROID:
		fulfillmentpbSource = v1.Source_ANDROID
	case v1.Source_AUTO_DM:
		fulfillmentpbSource = v1.Source_AUTO_DM
	case v1.Source_GOOGLE_CALENDAR:
		fulfillmentpbSource = v1.Source_GOOGLE_CALENDAR
	case v1.Source_IOS:
		fulfillmentpbSource = v1.Source_IOS
	case v1.Source_OB:
		fulfillmentpbSource = v1.Source_OB
	case v1.Source_OPEN_API:
		fulfillmentpbSource = v1.Source_OPEN_API
	case v1.Source_SOURCE_UNSPECIFIED:
		fulfillmentpbSource = v1.Source_SOURCE_UNSPECIFIED
	case v1.Source_WEB:
		fulfillmentpbSource = v1.Source_WEB
	default: // ignored
	}
	return fulfillmentpbSource
}
func (c *FulfillmentConverter) fulfillmentpbStatusToFulfillmentpbStatus(source v1.Status) v1.Status {
	var fulfillmentpbStatus v1.Status
	switch source {
	case v1.Status_CANCELED:
		fulfillmentpbStatus = v1.Status_CANCELED
	case v1.Status_CHECK_IN:
		fulfillmentpbStatus = v1.Status_CHECK_IN
	case v1.Status_COMPLETED:
		fulfillmentpbStatus = v1.Status_COMPLETED
	case v1.Status_CONFIRMED:
		fulfillmentpbStatus = v1.Status_CONFIRMED
	case v1.Status_FINISHED:
		fulfillmentpbStatus = v1.Status_FINISHED
	case v1.Status_PENDING_PAYMENT:
		fulfillmentpbStatus = v1.Status_PENDING_PAYMENT
	case v1.Status_READY:
		fulfillmentpbStatus = v1.Status_READY
	case v1.Status_STATUS_UNSPECIFIED:
		fulfillmentpbStatus = v1.Status_STATUS_UNSPECIFIED
	case v1.Status_UNCONFIRMED:
		fulfillmentpbStatus = v1.Status_UNCONFIRMED
	default: // ignored
	}
	return fulfillmentpbStatus
}
func (c *FulfillmentConverter) gormDeletedAtToGormDeletedAt(source gorm.DeletedAt) gorm.DeletedAt {
	var gormDeletedAt gorm.DeletedAt
	gormDeletedAt.Time = c.timeTimeToTimeTime(source.Time)
	gormDeletedAt.Valid = source.Valid
	return gormDeletedAt
}
func (c *FulfillmentConverter) pTimeTimeToPTimeTime(source *time.Time) *time.Time {
	var pTimeTime *time.Time
	if source != nil {
		var timeTime time.Time
		_ = (*source)
		pTimeTime = &timeTime
	}
	return pTimeTime
}
func (c *FulfillmentConverter) pTimestamppbTimestampToPTimeTime(source *timestamppb.Timestamp) *time.Time {
	var pTimeTime *time.Time
	if source != nil {
		var timeTime time.Time
		_ = (*source)
		pTimeTime = &timeTime
	}
	return pTimeTime
}
func (c *FulfillmentConverter) timeTimeToTimeTime(source time.Time) time.Time {
	var timeTime time.Time
	_ = source
	return timeTime
}
