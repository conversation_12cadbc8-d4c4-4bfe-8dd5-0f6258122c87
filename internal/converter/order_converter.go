package converter

import (
	"fmt"
	"strings"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	fulfillmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	groomingpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/grooming/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/consts"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	"github.com/samber/lo"
	decimal2 "github.com/shopspring/decimal"
	"google.golang.org/genproto/googleapis/type/decimal"
)

func BuildOrderItemKey(PetID int64, ObjectID int64, Type string, UnitPrice float64) string {
	return fmt.Sprintf("%d-%d-%s-%.2f", PetID, ObjectID, Type, UnitPrice)
}

func buildOrderItemKeyByItem(item *orderpb.OrderLineItemModel) string {
	return fmt.Sprintf("%d-%d-%s-%.2f", item.PetId, item.ObjectId, item.Type, item.UnitPrice)
}

func GroupByItemKey(items []*orderpb.OrderLineItemModel) map[string]*orderpb.OrderLineItemModel {
	result := make(map[string]*orderpb.OrderLineItemModel)

	for _, item := range items {
		result[buildOrderItemKeyByItem(item)] = item
	}

	return result
}

func FulfillmentToOrder(
	fulfillment *model.Fulfillment,
	customer *businesscustomerpb.BusinessCustomerInfoModel,
	services []*offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo,
	createdBy *int64,
) *orderpb.OrderModel {
	if fulfillment == nil {
		return nil
	}
	appointmentSource := groomingpb.AppointmentSource(fulfillment.Source.Number())
	return &orderpb.OrderModel{
		CompanyId:   &fulfillment.CompanyID,
		BusinessId:  fulfillment.BusinessID,
		CustomerId:  fulfillment.CustomerID,
		Status:      int32(orderpb.OrderStatus_CREATED),
		SourceType:  consts.OrderSourceType_Fulfillment,
		SourceId:    &fulfillment.ID,
		Title:       lo.ToPtr(buildTitleFromServices(services)),
		CreateBy:    createdBy,
		UpdateBy:    createdBy,
		Description: lo.ToPtr(customer.FirstName + " " + customer.LastName),
		Source:      &appointmentSource,
	}
}

func buildTitleFromServices(services []*offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo) string {
	serviceNames := lo.Map(services, func(customizedService *offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo, _ int) string {
		return customizedService.CustomizedService.Name
	})
	return strings.Join(lo.Uniq(serviceNames), " ")
}

func GroupClassToOrderLineItems(
	fulfillment *model.Fulfillment,
	details []*model.GroupClassDetail,
	services []*offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo,
	taxes []*organizationpb.TaxRuleModel,
	instances []*offeringpb.GroupClassInstance,
	createdBy *int64,
) []*orderpb.OrderLineItemModel {
	if fulfillment == nil || len(details) == 0 {
		return nil
	}
	idToTax := lo.KeyBy(taxes, func(item *organizationpb.TaxRuleModel) int64 {
		return item.Id
	})
	idToInstance := lo.KeyBy(instances, func(item *offeringpb.GroupClassInstance) int64 {
		return item.Id
	})
	return lo.Map(details, func(detail *model.GroupClassDetail, _ int) *orderpb.OrderLineItemModel {
		instance := idToInstance[detail.GroupClassInstanceID]
		condition := &offeringsvcpb.CustomizedServiceQueryCondition{
			ServiceId:  detail.GroupClassID,
			BusinessId: &fulfillment.BusinessID,
			PetId:      &detail.PetID,
			StaffId:    &instance.StaffId,
		}
		customizedService := FilterCustomizedService(services, condition)
		tax := idToTax[customizedService.TaxId]
		return &orderpb.OrderLineItemModel{
			BusinessId:  fulfillment.BusinessID,
			ObjectId:    customizedService.Id,
			Type:        string(consts.OrderItemType_Service),
			Name:        customizedService.Name,
			UnitPrice:   customizedService.Price,
			Quantity:    1,
			StaffId:     &instance.StaffId,
			Description: &customizedService.Description,
			PetId:       detail.PetID,
			TaxId:       tax.Id,
			TaxRate:     lo.ToPtr(decimal.Decimal{Value: decimal2.NewFromFloat(tax.Rate).String()}),
			TaxName:     tax.Name,
			LineTaxes:   []*orderpb.OrderLineTaxModel{TaxModelToOrderLineTax(fulfillment, tax, createdBy)},
		}
	})
}

func TaxModelToOrderLineTax(
	fulfillment *model.Fulfillment,
	tax *organizationpb.TaxRuleModel,
	createdBy *int64,
) *orderpb.OrderLineTaxModel {
	if tax == nil {
		return nil
	}
	taxModel := &orderpb.OrderLineTaxModel{
		BusinessId: fulfillment.BusinessID,
		ApplyType:  consts.OrderLineItemApplyType_Item,
		TaxId:      tax.Id,
		TaxRate:    tax.Rate,
		TaxName:    tax.Name,
	}
	if createdBy != nil {
		taxModel.ApplyBy = *createdBy
	}
	return taxModel
}

func ConvertSource(source fulfillmentpb.Source) groomingpb.AppointmentSource {
	return groomingpb.AppointmentSource(source.Number())
}
