package converter

import (
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/opt"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
)

// goverter:converter
// goverter:name StaffTimeSlotConverter
// goverter:output:file impl/staff_time_slot_converter_impl.go
// goverter:output:package impl
// goverter:useZeroValueOnPointerInconsistency
// goverter:matchIgnoreCase
// goverter:ignoreUnexported
// goverter:enum:unknown @ignore
type StaffTimeSlotConverter interface {
	ModelToModel(source *model.StaffTimeSlot) *model.StaffTimeSlot

	// goverter:map StartDatetime StartDatetime | PTimeToPTime
	// goverter:map EndDatetime EndDatetime | PTimeToPTime
	UpdateToOpt(source dto.UpdateStaffTimeSlotDTO) opt.UpdateStaffTimeSlotOpt
}

//go:generate go run github.com/jmattheis/goverter/cmd/goverter gen ./
