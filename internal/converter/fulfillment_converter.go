package converter

import (
	fulfillmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/opt"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/gorm"
	"time"
)

// goverter:converter
// goverter:name FulfillmentConverter
// goverter:output:file impl/fulfillment_converter_impl.go
// goverter:output:package impl
// goverter:useZeroValueOnPointerInconsistency
// goverter:matchIgnoreCase
// goverter:ignoreUnexported
// goverter:enum:unknown @ignore
// goverter:extend TimeToTimestamp
// goverter:extend DeletedAtToTimestamp
// goverter:extend ExtractServiceTypeInclude
type FulfillmentConverter interface {
	// goverter:ignore ID
	// goverter:ignore OrderID
	// goverter:ignore ServiceTypeInclude
	// goverter:ignore RepeatRuleID
	// goverter:map CreatedAt | NowTime
	// goverter:map UpdatedAt | NowTime
	// goverter:ignore DeletedAt
	DTOToModel(source *dto.CreateFulfillmentWithDetailsDTO) *model.Fulfillment

	ModelToPB(source *model.Fulfillment) *fulfillmentpb.FulfillmentModel

	ModelsToPB(source []*model.Fulfillment) []*fulfillmentpb.FulfillmentModel

	// goverter:ignore GroupClasses
	// goverter:ignore CreatedBy
	CreatePBToDetailsDTO(source *fulfillmentpb.FulfillmentCreateDef) *dto.CreateFulfillmentWithDetailsDTO

	ModelToModel(source *model.Fulfillment) *model.Fulfillment

	// goverter:map StartDatetime StartDatetime | PTimeToPTime
	// goverter:map EndDatetime EndDatetime | PTimeToPTime
	UpdateToOpt(source dto.UpdateFulfillmentDTO) opt.UpdateFulfillmentOpt
}

func NowTime() *time.Time {
	t := time.Now()
	return &t
}

func TimeToTime(t time.Time) time.Time {
	return t
}

func PTimeToPTime(t *time.Time) *time.Time {
	return t
}

func TimeToTimestamp(t *time.Time) *timestamppb.Timestamp {
	if t == nil {
		return nil
	}
	return timestamppb.New(*t)
}

func DeletedAtToTimestamp(deletedAt gorm.DeletedAt) *timestamppb.Timestamp {
	if !deletedAt.Valid {
		return nil
	}
	return timestamppb.New(deletedAt.Time)
}

func ExtractServiceTypeInclude(source *dto.CreateFulfillmentWithDetailsDTO) *int32 {
	var bitmap int32 = 0
	if len(source.GroupClasses) != 0 {
		bitmap |= 1 << int32(offeringpb.ServiceItemType_GROUP_CLASS)
	}
	return &bitmap
}

func ServiceItemTypesToIncludeValue(serviceItemTypes []offeringpb.ServiceItemType) *int32 {
	if len(serviceItemTypes) == 0 {
		return nil
	}
	var bitmap int32 = 0
	for serviceItemType := range serviceItemTypes {
		bitmap |= 1 << serviceItemType
	}
	return &bitmap
}

func GetPrePaymentActiveStatuses() []fulfillmentpb.Status {
	return []fulfillmentpb.Status{
		fulfillmentpb.Status_UNCONFIRMED,
		fulfillmentpb.Status_CONFIRMED,
		fulfillmentpb.Status_CHECK_IN,
		fulfillmentpb.Status_READY,
		fulfillmentpb.Status_FINISHED,
	}
}

//go:generate go run github.com/jmattheis/goverter/cmd/goverter gen ./
