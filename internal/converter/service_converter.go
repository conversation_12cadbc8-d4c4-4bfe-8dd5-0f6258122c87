package converter

import (
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	"github.com/samber/lo"
)

func FilterCustomizedService(
	services []*offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo,
	condition *offeringsvcpb.CustomizedServiceQueryCondition,
) *offeringpb.CustomizedServiceView {
	// 定义帮助函数，比较可能为nil的指针字段
	compareNullableInt64 := func(a, b *int64) bool {
		return (a == nil && b == nil) || (a != nil && b != nil && *a == *b)
	}

	service, _ := lo.Find(services, func(item *offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo) bool {
		q := item.QueryCondition
		return q.ServiceId == condition.ServiceId &&
			compareNullableInt64(q.BusinessId, condition.BusinessId) &&
			compareNullableInt64(q.PetId, condition.PetId) &&
			compareNullableInt64(q.StaffId, condition.StaffId)
	})

	if service != nil {
		return service.CustomizedService
	}
	return nil
}
