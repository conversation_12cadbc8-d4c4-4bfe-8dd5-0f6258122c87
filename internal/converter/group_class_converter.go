package converter

import (
	fulfillmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/opt"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	"google.golang.org/protobuf/types/known/timestamppb"
	"time"
)

// goverter:converter
// goverter:name GroupClassConverter
// goverter:output:file impl/group_class_converter_impl.go
// goverter:output:package impl
// goverter:useZeroValueOnPointerInconsistency
// goverter:matchIgnoreCase
// goverter:ignoreUnexported
// goverter:enum:unknown @ignore
// goverter:extend TimeToTimestamp
// goverter:extend TimeValueToTimestamp
// goverter:extend DeletedAtToTimestamp
type GroupClassConverter interface {
	CreatePBToDetailsDTO(source *fulfillmentpb.GroupClassDetailCreateDef) *dto.GroupClassDetailsDTO
	CreatePBToDetailsDTOs(source []*fulfillmentpb.GroupClassDetailCreateDef) []*dto.GroupClassDetailsDTO
	ModelToModel(source *model.GroupClassDetail) *model.GroupClassDetail
	ModelToPB(sources []*model.GroupClassDetail) []*fulfillmentpb.GroupClassDetailModel
	AttendanceModelToPB(sources []*model.GroupClassAttendance) []*fulfillmentpb.GroupClassAttendanceModel
	UpdateToOpt(source dto.UpdateGroupClassDetailDTO) opt.UpdateGroupClassDetailOpt
	FilterToOpt(source filter.ListGroupClassDetailFilter) opt.ListGroupClassDetailOpt
}

func TimeValueToTimestamp(t time.Time) *timestamppb.Timestamp {
	return timestamppb.New(t)
}

//go:generate go run github.com/jmattheis/goverter/cmd/goverter gen ./
