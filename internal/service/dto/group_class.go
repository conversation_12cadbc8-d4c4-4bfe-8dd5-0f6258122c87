package dto

import (
	fulfillmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	"google.golang.org/genproto/googleapis/type/interval"
	"time"
)

type ListUncheckInPetsDTO struct {
	CompanyID           int64
	BusinessID          int64
	SessionDate         *time.Time
	GroupClassSessionID *int64
	PetIDs              []int64
}

type GroupClassAttendanceDTO struct {
	FulfillmentID       int64
	GroupClassDetailID  int64
	GroupClassSessionID int64
	PetID               int64
}

type CheckInSessionDTO struct {
	CompanyID           int64
	BusinessID          int64
	GroupClassSessionID *int64
	PetIDs              []int64
}

type CheckInSessionResultDTO struct {
	GroupClassInstanceID int64
	GroupClassSessionID  int64
	PetID                int64
}

type ListGroupClassDetailsDTO struct {
	FulfillmentIDs        []int64
	GroupClassInstanceIDs []int64
	PetIDs                []int64
	Statuses              []fulfillmentpb.GroupClassDetailModel_Status
}

type ListGroupClassAttendanceDTO struct {
	FulfillmentIDs       []int64
	GroupClassSessionIDs []int64
}

type UpdateGroupClassDetailDTO struct {
	GroupClassDetailID int64
	Status             *fulfillmentpb.GroupClassDetailModel_Status
}

type UpdateGroupClassSessionDTO struct {
	GroupClassSessionID  int64
	GroupClassInstanceID int64
	BeforeInterval       *interval.Interval
	AfterInterval        *interval.Interval
}
