package dto

import (
	"time"

	fulfillmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	utilsV2 "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/utils/v2"
)

type PetEnrollmentDTO struct {
	CompanyID            int64
	BusinessID           int64
	PetID                int64
	GroupClassInstanceID int64
	Source               fulfillmentpb.Source
	StaffID              *int64
}

type CreateFulfillmentWithDetailsDTO struct {
	CompanyID        int64
	BusinessID       int64
	CustomerID       int64
	BookingRequestID *int64
	StartDateTime    *time.Time
	EndDateTime      *time.Time
	ColorCode        *string
	Status           fulfillmentpb.Status
	Source           fulfillmentpb.Source
	CreatedBy        *int64

	GroupClasses []*GroupClassDetailsDTO
}

type GroupClassDetailsDTO struct {
	PetID                int64
	GroupClassInstanceID int64
}

type CreateGroupClassDTO struct {
	CompanyID          int64
	BusinessID         int64
	FulfillmentID      int64
	PetID              int64
	CustomerID         int64
	GroupClassInstance *offeringpb.GroupClassInstance
}

type ListFulfillmentsDTO struct {
	CompanyID         int64
	BusinessIDs       []int64
	GroupClassFilter  *GroupClassDetailFilter
	FulfillmentFilter *FulfillmentFilter
	Pagination        *utilsV2.PaginationRequest
}

type GroupClassDetailFilter struct {
	GroupClassIDs []int64
	PetIDs        []int64
}

type FulfillmentFilter struct {
	IDs          []int64
	CustomerIds  []int64
	Statuses     []fulfillmentpb.Status
	CreatedAtMin *time.Time
}

type UpdateFulfillmentDTO struct {
	FulfillmentID      int64
	BookingRequestID   *int64
	StartDatetime      *time.Time
	EndDatetime        *time.Time
	Status             *fulfillmentpb.Status
	OrderID            *int64
	ServiceTypeInclude *int
	ColorCode          *string

	GroupClasses   []UpdateGroupClassDetailDTO
	StaffTimeSlots []UpdateStaffTimeSlotDTO
}
