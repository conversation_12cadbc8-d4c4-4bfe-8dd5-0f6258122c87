package service

import (
	"context"

	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/query"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/util"
	"github.com/samber/lo"
)

type StaffTimeSlotService interface {
	BatchUpdate(context.Context, []dto.UpdateStaffTimeSlotDTO) error
}

type staffTimeSlotService struct {
	query                 *query.Query
	repository            fulfillment.StaffTimeSlotRepository
	fulfillmentRepository fulfillment.Repository
}

func (s staffTimeSlotService) BatchUpdate(ctx context.Context, updates []dto.UpdateStaffTimeSlotDTO) error {
	err := s.query.Transaction(func(tx *query.Query) error {
		if err := s.repository.WithTX(tx).BatchUpdateSelective(ctx, updates); err != nil {
			return err
		}
		staffTimeSlotIDs := lo.Map(updates, func(u dto.UpdateStaffTimeSlotDTO, _ int) int64 {
			return u.StaffTimeSlotID
		})
		staffTimeSlots, err := s.repository.WithTX(tx).ListByFilter(ctx, filter.ListStaffTimeSlotFilter{StaffTimeSlotIDs: staffTimeSlotIDs})
		if err != nil {
			return err
		}
		fulfillmentIDs := lo.Map(staffTimeSlots, func(slot *model.StaffTimeSlot, _ int) int64 {
			return slot.FulfillmentID
		})
		allStaffTimeSlots, err := s.repository.WithTX(tx).ListByFilter(ctx, filter.ListStaffTimeSlotFilter{
			FulfillmentIDs: fulfillmentIDs,
		})
		if err != nil {
			return err
		}
		fulfillmentIDToStaffTimeSlots := lo.GroupBy(allStaffTimeSlots, func(slot *model.StaffTimeSlot) int64 {
			return slot.FulfillmentID
		})
		for fulfillmentID, slots := range fulfillmentIDToStaffTimeSlots {
			startTime, endTime := util.GetTimeRange(slots)
			if err = s.fulfillmentRepository.WithTX(tx).UpdateSelective(ctx, dto.UpdateFulfillmentDTO{
				FulfillmentID: fulfillmentID,
				StartDatetime: &startTime,
				EndDatetime:   &endTime,
			}); err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

func NewStaffTimeSlotService(
	q *query.Query,
	repository fulfillment.StaffTimeSlotRepository,
	fulfillmentRepository fulfillment.Repository,
) StaffTimeSlotService {
	return &staffTimeSlotService{
		query:                 q,
		repository:            repository,
		fulfillmentRepository: fulfillmentRepository,
	}
}
