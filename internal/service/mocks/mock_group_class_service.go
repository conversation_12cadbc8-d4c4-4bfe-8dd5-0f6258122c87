// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-fulfillment/internal/service (interfaces: GroupClassService)
//
// Generated by this command:
//
//	mockgen -package=mocks -destination=mocks/mock_group_class_service.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/service GroupClassService
//

// Package mocks is a generated GoMock package.
package mocks

import (
	context "context"
	reflect "reflect"

	model "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	query "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/query"
	service "github.com/MoeGolibrary/moego-svc-fulfillment/internal/service"
	dto "github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	gomock "go.uber.org/mock/gomock"
)

// MockGroupClassService is a mock of GroupClassService interface.
type MockGroupClassService struct {
	ctrl     *gomock.Controller
	recorder *MockGroupClassServiceMockRecorder
	isgomock struct{}
}

// MockGroupClassServiceMockRecorder is the mock recorder for MockGroupClassService.
type MockGroupClassServiceMockRecorder struct {
	mock *MockGroupClassService
}

// NewMockGroupClassService creates a new mock instance.
func NewMockGroupClassService(ctrl *gomock.Controller) *MockGroupClassService {
	mock := &MockGroupClassService{ctrl: ctrl}
	mock.recorder = &MockGroupClassServiceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGroupClassService) EXPECT() *MockGroupClassServiceMockRecorder {
	return m.recorder
}

// BatchCreate mocks base method.
func (m *MockGroupClassService) BatchCreate(arg0 context.Context, arg1 dto.CreateGroupClassDTO) (*model.GroupClassDetail, []*model.StaffTimeSlot, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchCreate", arg0, arg1)
	ret0, _ := ret[0].(*model.GroupClassDetail)
	ret1, _ := ret[1].([]*model.StaffTimeSlot)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// BatchCreate indicates an expected call of BatchCreate.
func (mr *MockGroupClassServiceMockRecorder) BatchCreate(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchCreate", reflect.TypeOf((*MockGroupClassService)(nil).BatchCreate), arg0, arg1)
}

// CheckInGroupClassSession mocks base method.
func (m *MockGroupClassService) CheckInGroupClassSession(arg0 context.Context, arg1 dto.CheckInSessionDTO) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CheckInGroupClassSession", arg0, arg1)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckInGroupClassSession indicates an expected call of CheckInGroupClassSession.
func (mr *MockGroupClassServiceMockRecorder) CheckInGroupClassSession(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckInGroupClassSession", reflect.TypeOf((*MockGroupClassService)(nil).CheckInGroupClassSession), arg0, arg1)
}

// ListUncheckInPets mocks base method.
func (m *MockGroupClassService) ListUncheckInPets(arg0 context.Context, arg1 dto.ListUncheckInPetsDTO) ([]dto.GroupClassAttendanceDTO, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListUncheckInPets", arg0, arg1)
	ret0, _ := ret[0].([]dto.GroupClassAttendanceDTO)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListUncheckInPets indicates an expected call of ListUncheckInPets.
func (mr *MockGroupClassServiceMockRecorder) ListUncheckInPets(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListUncheckInPets", reflect.TypeOf((*MockGroupClassService)(nil).ListUncheckInPets), arg0, arg1)
}

// WithQuery mocks base method.
func (m *MockGroupClassService) WithQuery(arg0 *query.Query) service.GroupClassService {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTX", arg0)
	ret0, _ := ret[0].(service.GroupClassService)
	return ret0
}

// WithQuery indicates an expected call of WithQuery.
func (mr *MockGroupClassServiceMockRecorder) WithQuery(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTX", reflect.TypeOf((*MockGroupClassService)(nil).WithQuery), arg0)
}
