package handler

import (
	"context"
	fulfillmentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/fulfillment/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
)

type GroupClassAttendanceHandler struct {
	fulfillmentsvcpb.UnimplementedGroupClassAttendanceServiceServer
	service service.GroupClassService
}

func (h *GroupClassAttendanceHandler) ListAttendances(ctx context.Context, request *fulfillmentsvcpb.ListAttendancesRequest) (*fulfillmentsvcpb.ListAttendancesResponse, error) {
	groupClassAttendances, err := h.service.ListGroupClassAttendances(ctx, dto.ListGroupClassAttendanceDTO{
		GroupClassSessionIDs: request.GroupClassSessionId,
	})
	if err != nil {
		return nil, err
	}
	return &fulfillmentsvcpb.ListAttendancesResponse{
		Attendances: GroupClassConverter.AttendanceModelToPB(groupClassAttendances),
	}, nil
}

func NewGroupClassAttendanceHandler(service service.GroupClassService) *GroupClassAttendanceHandler {
	return &GroupClassAttendanceHandler{
		service: service,
	}
}
