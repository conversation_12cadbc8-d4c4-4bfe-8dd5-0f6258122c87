package handler

import (
	"context"
	fulfillmentsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/fulfillment/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/util"
	"github.com/samber/lo"
)

type GroupClassDetailHandler struct {
	fulfillmentsvcpb.UnimplementedGroupClassDetailServiceServer
	service service.GroupClassService
}

func (h *GroupClassDetailHandler) ListGroupClassDetails(ctx context.Context, request *fulfillmentsvcpb.ListGroupClassDetailsRequest) (*fulfillmentsvcpb.ListGroupClassDetailsResponse, error) {
	groupClassDetails, err := h.service.ListGroupClassDetails(ctx, dto.ListGroupClassDetailsDTO{
		FulfillmentIDs:        request.Filter.FulfillmentIds,
		GroupClassInstanceIDs: request.Filter.GroupClassInstanceIds,
		PetIDs:                request.Filter.PetIds,
		Statuses:              request.Filter.Statuses,
	})
	if err != nil {
		return nil, err
	}
	return &fulfillmentsvcpb.ListGroupClassDetailsResponse{
		GroupClassDetails: GroupClassConverter.ModelToPB(groupClassDetails),
	}, nil
}

func (h *GroupClassDetailHandler) ListUncheckInPets(ctx context.Context, req *fulfillmentsvcpb.ListUncheckInPetsRequest) (*fulfillmentsvcpb.ListUncheckInPetsResponse, error) {
	petsDTO := dto.ListUncheckInPetsDTO{
		CompanyID:           req.CompanyId,
		BusinessID:          req.BusinessId,
		SessionDate:         util.DateToTime(req.Date),
		GroupClassSessionID: req.GroupClassSessionId,
	}
	uncheckedInPets, err := h.service.ListUncheckInPets(ctx, petsDTO)
	if err != nil {
		return nil, err
	}
	return &fulfillmentsvcpb.ListUncheckInPetsResponse{
		PetSessions: lo.Map(uncheckedInPets, func(item dto.GroupClassAttendanceDTO, _ int) *fulfillmentsvcpb.ListUncheckInPetsResponse_PetSession {
			return &fulfillmentsvcpb.ListUncheckInPetsResponse_PetSession{
				PetId:               item.PetID,
				GroupClassSessionId: item.GroupClassSessionID,
			}
		}),
	}, nil
}

func (h *GroupClassDetailHandler) CheckInGroupClassSession(ctx context.Context, request *fulfillmentsvcpb.CheckInGroupClassSessionRequest) (*fulfillmentsvcpb.CheckInGroupClassSessionResponse, error) {
	result, err := h.service.CheckInGroupClassSession(ctx, dto.CheckInSessionDTO{
		CompanyID:           request.CompanyId,
		BusinessID:          request.BusinessId,
		GroupClassSessionID: request.GroupClassSessionId,
		PetIDs:              request.PetIds,
	})
	if err != nil {
		return nil, err
	}

	return &fulfillmentsvcpb.CheckInGroupClassSessionResponse{
		CheckedInPetIds: lo.Uniq(lo.Map(result, func(r dto.CheckInSessionResultDTO, _ int) int64 {
			return r.PetID
		})),
		CheckedInInstanceIds: lo.Uniq(lo.Map(result, func(r dto.CheckInSessionResultDTO, _ int) int64 {
			return r.GroupClassInstanceID
		})),
	}, nil
}

func NewGroupClassDetailHandler(service service.GroupClassService) *GroupClassDetailHandler {
	return &GroupClassDetailHandler{
		service: service,
	}
}
