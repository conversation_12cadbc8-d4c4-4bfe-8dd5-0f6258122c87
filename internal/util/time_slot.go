package util

import (
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	"github.com/samber/lo"
	"time"
)

func GetTimeRange(staffTimeSlots []*model.StaffTimeSlot) (time.Time, time.Time) {
	if len(staffTimeSlots) == 0 {
		return time.Time{}, time.Time{}
	}
	startTimes := lo.Map(staffTimeSlots, func(timeslot *model.StaffTimeSlot, _ int) time.Time {
		return timeslot.StartDatetime
	})
	endTimes := lo.Map(staffTimeSlots, func(timeslot *model.StaffTimeSlot, _ int) time.Time {
		return timeslot.EndDatetime
	})
	dst := make([]time.Time, len(startTimes))
	copy(dst, startTimes)
	minTime, _ := GetMinMaxTime(dst)
	copy(dst, endTimes)
	_, maxTime := GetMinMaxTime(endTimes)

	return minTime, maxTime
}
