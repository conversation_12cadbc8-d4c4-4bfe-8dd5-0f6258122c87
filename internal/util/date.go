package util

import (
	"google.golang.org/genproto/googleapis/type/date"
	"google.golang.org/protobuf/types/known/timestamppb"
	"gorm.io/datatypes"
	"slices"
	"time"
)

func GetMinMaxTime(times []time.Time) (time.Time, time.Time) {
	slices.SortFunc(times, func(a, b time.Time) int {
		if a.Before(b) {
			return -1
		}
		return 1
	})
	return times[0], times[len(times)-1]
}

func CombineDateTime(dateOnly time.Time, timeOnly datatypes.Time) time.Time {
	parsedTime, err := time.Parse(time.TimeOnly, timeOnly.String())
	if err != nil {
		return time.Time{}
	}

	combined := time.Date(
		dateOnly.Year(), dateOnly.Month(), dateOnly.Day(),
		parsedTime.Hour(), parsedTime.Minute(), parsedTime.Second(),
		0, time.Local,
	)
	return combined
}

func DateToTime(dateOnly *date.Date) *time.Time {
	if dateOnly == nil {
		return nil
	}
	t := time.Date(int(dateOnly.Year), time.Month(dateOnly.Month), int(dateOnly.Day), 0, 0, 0, 0, time.UTC)
	return &t
}

func TimestampToTimeOnly(ts *timestamppb.Timestamp) *datatypes.Time {
	if ts == nil {
		return nil
	}
	t := ts.AsTime()
	timeOnly := datatypes.NewTime(t.Hour(), t.Minute(), t.Second(), t.Nanosecond())
	return &timeOnly
}

func GetStartOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day(), 0, 0, 0, 0, t.Location())
}

func GetEndOfDay(t time.Time) time.Time {
	return time.Date(t.Year(), t.Month(), t.Day()+1, 0, 0, 0, 0, t.Location()).Add(-time.Second)
}
