// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	fulfillmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	"gorm.io/gorm"
)

const TableNameFulfillment = "fulfillment"

// Fulfillment mapped from table <fulfillment>
type Fulfillment struct {
	ID                 int64                `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	CompanyID          int64                `gorm:"column:company_id;type:bigint;not null;index:fulfillment_idx_tenant_customer,priority:1" json:"company_id"`
	BusinessID         int64                `gorm:"column:business_id;type:bigint;not null;index:fulfillment_idx_tenant_customer,priority:2" json:"business_id"`
	CustomerID         int64                `gorm:"column:customer_id;type:bigint;not null;index:fulfillment_idx_tenant_customer,priority:3" json:"customer_id"`
	BookingRequestID   int64                `gorm:"column:booking_request_id;type:bigint;not null;comment:booking_request.id, A booking request may be scheduled for multiple fulfillment" json:"booking_request_id"` // booking_request.id, A booking request may be scheduled for multiple fulfillment
	StartDatetime      *time.Time           `gorm:"column:start_datetime;type:timestamp without time zone;comment:Minimum start date time for all service details in the current fulfillment" json:"start_datetime"`  // Minimum start date time for all service details in the current fulfillment
	EndDatetime        *time.Time           `gorm:"column:end_datetime;type:timestamp without time zone;comment:Maximum start date time for all service details in the current fulfillment" json:"end_datetime"`      // Maximum start date time for all service details in the current fulfillment
	Status             fulfillmentpb.Status `gorm:"column:status;type:integer;not null;default:1;comment:1-unconfirmed, 2-confirmed, 3-finished, 4-canceled, 5-ready, 6-checked-in" json:"status"`                    // 1-unconfirmed, 2-confirmed, 3-finished, 4-canceled, 5-ready, 6-checked-in
	ColorCode          *string              `gorm:"column:color_code;type:character varying(20);not null;default:#000000" json:"color_code"`
	ServiceTypeInclude *int32               `gorm:"column:service_type_include;type:integer;not null;default:1;comment:The value of the bit composition of the various types of services, 1-boarding, 2-daycare, 3-grooming, 4-evaluation, 5-dog_walking" json:"service_type_include"` // The value of the bit composition of the various types of services, 1-boarding, 2-daycare, 3-grooming, 4-evaluation, 5-dog_walking
	Source             fulfillmentpb.Source `gorm:"column:source;type:integer;not null;default:22018;comment:22018-web, 22168-ob, 17216-android, 17802-ios, 23426-dm, 19826-gc, 23333-openapi" json:"source"`                                                                          // 22018-web, 22168-ob, 17216-android, 17802-ios, 23426-dm, 19826-gc, 23333-openapi
	RepeatRuleID       int32                `gorm:"column:repeat_rule_id;type:integer;not null;comment:fulfillment_repeat_rule.id" json:"repeat_rule_id"`                                                                                                                              // fulfillment_repeat_rule.id
	CreatedAt          *time.Time           `gorm:"column:created_at;type:timestamp without time zone;not null;default:now()" json:"created_at"`
	UpdatedAt          *time.Time           `gorm:"column:updated_at;type:timestamp without time zone;not null;default:now()" json:"updated_at"`
	DeletedAt          gorm.DeletedAt       `gorm:"column:deleted_at;type:timestamp without time zone" json:"deleted_at"`
	OrderID            int64                `gorm:"column:order_id;type:bigint;not null" json:"order_id"`
}

// TableName Fulfillment's table name
func (*Fulfillment) TableName() string {
	return TableNameFulfillment
}
