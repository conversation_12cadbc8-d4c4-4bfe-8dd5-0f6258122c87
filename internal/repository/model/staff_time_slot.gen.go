// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

import (
	"time"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	"gorm.io/gorm"
)

const TableNameStaffTimeSlot = "staff_time_slot"

// StaffTimeSlot mapped from table <staff_time_slot>
type StaffTimeSlot struct {
	ID              int64                      `gorm:"column:id;type:bigint;primaryKey;autoIncrement:true" json:"id"`
	CompanyID       int64                      `gorm:"column:company_id;type:bigint;not null;index:staff_time_slot_idx_tenant_date,priority:1" json:"company_id"`
	BusinessID      int64                      `gorm:"column:business_id;type:bigint;not null;index:staff_time_slot_idx_tenant_date,priority:2" json:"business_id"`
	FulfillmentID   int64                      `gorm:"column:fulfillment_id;type:bigint;not null;index:staff_time_slot_idx_fulfillment,priority:1;comment:fulfillment.id" json:"fulfillment_id"`                                                                                // fulfillment.id
	CareType        offeringpb.ServiceItemType `gorm:"column:care_type;type:integer;not null;index:staff_time_slot_idx_detail_id,priority:1;comment:1-Grooming, 2-Boarding, 3-Daycare, 4-Evaluation, 5-Dog_walking, 6-Training group class" json:"care_type"`                   // 1-Grooming, 2-Boarding, 3-Daycare, 4-Evaluation, 5-Dog_walking, 6-Training group class
	DetailID        int64                      `gorm:"column:detail_id;type:bigint;not null;index:staff_time_slot_idx_detail_id,priority:2;comment:care_type = 1 grooming_detail.id, care_type = 4 evaluation_detail.id, care_type = 5 dog_walking_detail.id" json:"detail_id"` // care_type = 1 grooming_detail.id, care_type = 4 evaluation_detail.id, care_type = 5 dog_walking_detail.id
	OrderLineItemID int64                      `gorm:"column:order_line_item_id;type:bigint;not null;comment:order_line_item.id, Used to record whether the service has created an order" json:"order_line_item_id"`                                                            // order_line_item.id, Used to record whether the service has created an order
	StaffID         int64                      `gorm:"column:staff_id;type:bigint;not null;comment:The staff id who performing the service or add-on" json:"staff_id"`                                                                                                          // The staff id who performing the service or add-on
	PetID           int64                      `gorm:"column:pet_id;type:bigint;not null" json:"pet_id"`
	CustomerID      int64                      `gorm:"column:customer_id;type:bigint;not null" json:"customer_id"`
	StartDatetime   time.Time                  `gorm:"column:start_datetime;type:timestamp without time zone;not null;index:staff_time_slot_idx_tenant_date,priority:3;comment:The start date time of the this time slot" json:"start_datetime"` // The start date time of the this time slot
	EndDatetime     time.Time                  `gorm:"column:end_datetime;type:timestamp without time zone;not null;comment:The end date time of the this time slot" json:"end_datetime"`                                                        // The end date time of the this time slot
	CreatedAt       *time.Time                 `gorm:"column:created_at;type:timestamp without time zone;not null;default:now()" json:"created_at"`
	UpdatedAt       *time.Time                 `gorm:"column:updated_at;type:timestamp without time zone;not null;default:now()" json:"updated_at"`
	DeletedAt       gorm.DeletedAt             `gorm:"column:deleted_at;type:timestamp without time zone" json:"deleted_at"`
}

// TableName StaffTimeSlot's table name
func (*StaffTimeSlot) TableName() string {
	return TableNameStaffTimeSlot
}
