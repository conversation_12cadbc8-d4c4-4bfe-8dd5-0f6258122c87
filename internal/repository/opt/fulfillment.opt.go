package opt

import (
	fulfillmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	"time"
)

type UpdateFulfillmentOpt struct {
	BookingRequestID   *int64                `gorm:"column:booking_request_id"`
	StartDatetime      *time.Time            `gorm:"column:start_datetime"`
	EndDatetime        *time.Time            `gorm:"column:end_datetime"`
	Status             *fulfillmentpb.Status `gorm:"column:status"`
	OrderID            *int64                `gorm:"column:order_id"`
	ServiceTypeInclude *int                  `gorm:"column:service_type_include"`
	ColorCode          *string               `gorm:"column:color_code"`
}
