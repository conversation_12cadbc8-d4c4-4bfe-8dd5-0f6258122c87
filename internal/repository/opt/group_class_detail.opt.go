package opt

import (
	fulfillmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
)

type UpdateGroupClassDetailOpt struct {
	Status *fulfillmentpb.GroupClassDetailModel_Status `gorm:"column:status"`
}

type ListGroupClassDetailOpt struct {
	IDs                   []int64                                      `gorm:"column:id,query_expr:in"`
	FulfillmentIDs        []int64                                      `gorm:"column:fulfillment_id,query_expr:in"`
	PetIDs                []int64                                      `gorm:"column:pet_id,query_expr:in"`
	GroupClassIDs         []int64                                      `gorm:"column:group_class_id,query_expr:in"`
	GroupClassInstanceIDs []int64                                      `gorm:"column:group_class_instance_id,query_expr:in"`
	Statuses              []fulfillmentpb.GroupClassDetailModel_Status `gorm:"column:status,query_expr:in"`
}
