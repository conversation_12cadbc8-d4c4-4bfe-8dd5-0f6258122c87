package offering

import (
	"context"
	"github.com/MoeGolibrary/go-lib/grpc"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/consts"
)

//go:generate mockgen -package=offering -destination=mocks/mock_service_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/offering ServiceRepository
type ServiceRepository interface {
	GetServiceDetail(ctx context.Context, serviceId int64) (*offeringpb.ServiceModel, error)
	ListServices(ctx context.Context, serviceIds []int64) ([]*offeringpb.ServiceBriefView, error)
	BatchGetCustomizedServices(ctx context.Context, companyId int64, conditions []*offeringsvcpb.CustomizedServiceQueryCondition) ([]*offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo, error)
}

type serviceRepository struct {
	client offeringsvcpb.ServiceManagementServiceClient
}

func (r serviceRepository) BatchGetCustomizedServices(ctx context.Context, companyId int64, conditions []*offeringsvcpb.CustomizedServiceQueryCondition) ([]*offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo, error) {
	response, err := r.client.BatchGetCustomizedService(ctx, &offeringsvcpb.BatchGetCustomizedServiceRequest{
		CompanyId:          companyId,
		QueryConditionList: conditions,
	})
	if err != nil {
		return nil, err
	}
	return response.GetCustomizedServiceList(), nil
}

func (r serviceRepository) ListServices(ctx context.Context, serviceIds []int64) ([]*offeringpb.ServiceBriefView, error) {
	response, err := r.client.GetServiceListByIds(ctx, &offeringsvcpb.GetServiceListByIdsRequest{
		ServiceIds: serviceIds,
	})
	if err != nil {
		return nil, err
	}
	return response.GetServices(), nil
}

func (r serviceRepository) GetServiceDetail(ctx context.Context, serviceId int64) (*offeringpb.ServiceModel, error) {
	response, err := r.client.GetServiceDetail(ctx, &offeringsvcpb.GetServiceDetailRequest{
		ServiceId: serviceId,
	})
	if err != nil {
		return nil, err
	}
	return response.GetService(), nil
}

func NewServiceRepository() ServiceRepository {
	return &serviceRepository{
		client: grpc.NewClient(consts.OfferingEndpoint, offeringsvcpb.NewServiceManagementServiceClient),
	}
}
