// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/offering (interfaces: ServiceRepository)
//
// Generated by this command:
//
//	mockgen -package=offering -destination=mocks/mock_service_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/offering ServiceRepository
//

// Package offering is a generated GoMock package.
package offering

import (
	context "context"
	reflect "reflect"

	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
	offeringsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/offering/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockServiceRepository is a mock of ServiceRepository interface.
type MockServiceRepository struct {
	ctrl     *gomock.Controller
	recorder *MockServiceRepositoryMockRecorder
	isgomock struct{}
}

// MockServiceRepositoryMockRecorder is the mock recorder for MockServiceRepository.
type MockServiceRepositoryMockRecorder struct {
	mock *MockServiceRepository
}

// NewMockServiceRepository creates a new mock instance.
func NewMockServiceRepository(ctrl *gomock.Controller) *MockServiceRepository {
	mock := &MockServiceRepository{ctrl: ctrl}
	mock.recorder = &MockServiceRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceRepository) EXPECT() *MockServiceRepositoryMockRecorder {
	return m.recorder
}

// BatchGetCustomizedServices mocks base method.
func (m *MockServiceRepository) BatchGetCustomizedServices(ctx context.Context, companyId int64, conditions []*offeringsvcpb.CustomizedServiceQueryCondition) ([]*offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetCustomizedServices", ctx, companyId, conditions)
	ret0, _ := ret[0].([]*offeringsvcpb.BatchGetCustomizedServiceResponse_ServiceWithCustomizedInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetCustomizedServices indicates an expected call of BatchGetCustomizedServices.
func (mr *MockServiceRepositoryMockRecorder) BatchGetCustomizedServices(ctx, companyId, conditions any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetCustomizedServices", reflect.TypeOf((*MockServiceRepository)(nil).BatchGetCustomizedServices), ctx, companyId, conditions)
}

// GetServiceDetail mocks base method.
func (m *MockServiceRepository) GetServiceDetail(ctx context.Context, serviceId int64) (*offeringpb.ServiceModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceDetail", ctx, serviceId)
	ret0, _ := ret[0].(*offeringpb.ServiceModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceDetail indicates an expected call of GetServiceDetail.
func (mr *MockServiceRepositoryMockRecorder) GetServiceDetail(ctx, serviceId any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceDetail", reflect.TypeOf((*MockServiceRepository)(nil).GetServiceDetail), ctx, serviceId)
}

// ListServices mocks base method.
func (m *MockServiceRepository) ListServices(ctx context.Context, serviceIds []int64) ([]*offeringpb.ServiceBriefView, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListServices", ctx, serviceIds)
	ret0, _ := ret[0].([]*offeringpb.ServiceBriefView)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListServices indicates an expected call of ListServices.
func (mr *MockServiceRepositoryMockRecorder) ListServices(ctx, serviceIds any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListServices", reflect.TypeOf((*MockServiceRepository)(nil).ListServices), ctx, serviceIds)
}
