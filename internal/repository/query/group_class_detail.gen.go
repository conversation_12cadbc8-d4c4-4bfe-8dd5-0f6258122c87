// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
)

func newGroupClassDetail(db *gorm.DB, opts ...gen.DOOption) groupClassDetail {
	_groupClassDetail := groupClassDetail{}

	_groupClassDetail.groupClassDetailDo.UseDB(db, opts...)
	_groupClassDetail.groupClassDetailDo.UseModel(&model.GroupClassDetail{})

	tableName := _groupClassDetail.groupClassDetailDo.TableName()
	_groupClassDetail.ALL = field.NewAsterisk(tableName)
	_groupClassDetail.ID = field.NewInt64(tableName, "id")
	_groupClassDetail.FulfillmentID = field.NewInt64(tableName, "fulfillment_id")
	_groupClassDetail.PetID = field.NewInt64(tableName, "pet_id")
	_groupClassDetail.GroupClassID = field.NewInt64(tableName, "group_class_id")
	_groupClassDetail.GroupClassInstanceID = field.NewInt64(tableName, "group_class_instance_id")
	_groupClassDetail.Status = field.NewField(tableName, "status")
	_groupClassDetail.CreatedAt = field.NewTime(tableName, "created_at")
	_groupClassDetail.UpdatedAt = field.NewTime(tableName, "updated_at")
	_groupClassDetail.DeletedAt = field.NewField(tableName, "deleted_at")

	_groupClassDetail.fillFieldMap()

	return _groupClassDetail
}

type groupClassDetail struct {
	groupClassDetailDo groupClassDetailDo

	ALL                  field.Asterisk
	ID                   field.Int64
	FulfillmentID        field.Int64 // fulfillment.id
	PetID                field.Int64 // moe_customer_pet.id
	GroupClassID         field.Int64 // group class id, service.id
	GroupClassInstanceID field.Int64 // group_class_instance.id
	Status               field.Field // 1-Not started, 2-In progress, 3-Completed
	CreatedAt            field.Time
	UpdatedAt            field.Time
	DeletedAt            field.Field

	fieldMap map[string]field.Expr
}

func (g groupClassDetail) Table(newTableName string) *groupClassDetail {
	g.groupClassDetailDo.UseTable(newTableName)
	return g.updateTableName(newTableName)
}

func (g groupClassDetail) As(alias string) *groupClassDetail {
	g.groupClassDetailDo.DO = *(g.groupClassDetailDo.As(alias).(*gen.DO))
	return g.updateTableName(alias)
}

func (g *groupClassDetail) updateTableName(table string) *groupClassDetail {
	g.ALL = field.NewAsterisk(table)
	g.ID = field.NewInt64(table, "id")
	g.FulfillmentID = field.NewInt64(table, "fulfillment_id")
	g.PetID = field.NewInt64(table, "pet_id")
	g.GroupClassID = field.NewInt64(table, "group_class_id")
	g.GroupClassInstanceID = field.NewInt64(table, "group_class_instance_id")
	g.Status = field.NewField(table, "status")
	g.CreatedAt = field.NewTime(table, "created_at")
	g.UpdatedAt = field.NewTime(table, "updated_at")
	g.DeletedAt = field.NewField(table, "deleted_at")

	g.fillFieldMap()

	return g
}

func (g *groupClassDetail) WithContext(ctx context.Context) *groupClassDetailDo {
	return g.groupClassDetailDo.WithContext(ctx)
}

func (g groupClassDetail) TableName() string { return g.groupClassDetailDo.TableName() }

func (g groupClassDetail) Alias() string { return g.groupClassDetailDo.Alias() }

func (g groupClassDetail) Columns(cols ...field.Expr) gen.Columns {
	return g.groupClassDetailDo.Columns(cols...)
}

func (g *groupClassDetail) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := g.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (g *groupClassDetail) fillFieldMap() {
	g.fieldMap = make(map[string]field.Expr, 9)
	g.fieldMap["id"] = g.ID
	g.fieldMap["fulfillment_id"] = g.FulfillmentID
	g.fieldMap["pet_id"] = g.PetID
	g.fieldMap["group_class_id"] = g.GroupClassID
	g.fieldMap["group_class_instance_id"] = g.GroupClassInstanceID
	g.fieldMap["status"] = g.Status
	g.fieldMap["created_at"] = g.CreatedAt
	g.fieldMap["updated_at"] = g.UpdatedAt
	g.fieldMap["deleted_at"] = g.DeletedAt
}

func (g groupClassDetail) clone(db *gorm.DB) groupClassDetail {
	g.groupClassDetailDo.ReplaceConnPool(db.Statement.ConnPool)
	return g
}

func (g groupClassDetail) replaceDB(db *gorm.DB) groupClassDetail {
	g.groupClassDetailDo.ReplaceDB(db)
	return g
}

type groupClassDetailDo struct{ gen.DO }

func (g groupClassDetailDo) Debug() *groupClassDetailDo {
	return g.withDO(g.DO.Debug())
}

func (g groupClassDetailDo) WithContext(ctx context.Context) *groupClassDetailDo {
	return g.withDO(g.DO.WithContext(ctx))
}

func (g groupClassDetailDo) ReadDB() *groupClassDetailDo {
	return g.Clauses(dbresolver.Read)
}

func (g groupClassDetailDo) WriteDB() *groupClassDetailDo {
	return g.Clauses(dbresolver.Write)
}

func (g groupClassDetailDo) Session(config *gorm.Session) *groupClassDetailDo {
	return g.withDO(g.DO.Session(config))
}

func (g groupClassDetailDo) Clauses(conds ...clause.Expression) *groupClassDetailDo {
	return g.withDO(g.DO.Clauses(conds...))
}

func (g groupClassDetailDo) Returning(value interface{}, columns ...string) *groupClassDetailDo {
	return g.withDO(g.DO.Returning(value, columns...))
}

func (g groupClassDetailDo) Not(conds ...gen.Condition) *groupClassDetailDo {
	return g.withDO(g.DO.Not(conds...))
}

func (g groupClassDetailDo) Or(conds ...gen.Condition) *groupClassDetailDo {
	return g.withDO(g.DO.Or(conds...))
}

func (g groupClassDetailDo) Select(conds ...field.Expr) *groupClassDetailDo {
	return g.withDO(g.DO.Select(conds...))
}

func (g groupClassDetailDo) Where(conds ...gen.Condition) *groupClassDetailDo {
	return g.withDO(g.DO.Where(conds...))
}

func (g groupClassDetailDo) Order(conds ...field.Expr) *groupClassDetailDo {
	return g.withDO(g.DO.Order(conds...))
}

func (g groupClassDetailDo) Distinct(cols ...field.Expr) *groupClassDetailDo {
	return g.withDO(g.DO.Distinct(cols...))
}

func (g groupClassDetailDo) Omit(cols ...field.Expr) *groupClassDetailDo {
	return g.withDO(g.DO.Omit(cols...))
}

func (g groupClassDetailDo) Join(table schema.Tabler, on ...field.Expr) *groupClassDetailDo {
	return g.withDO(g.DO.Join(table, on...))
}

func (g groupClassDetailDo) LeftJoin(table schema.Tabler, on ...field.Expr) *groupClassDetailDo {
	return g.withDO(g.DO.LeftJoin(table, on...))
}

func (g groupClassDetailDo) RightJoin(table schema.Tabler, on ...field.Expr) *groupClassDetailDo {
	return g.withDO(g.DO.RightJoin(table, on...))
}

func (g groupClassDetailDo) Group(cols ...field.Expr) *groupClassDetailDo {
	return g.withDO(g.DO.Group(cols...))
}

func (g groupClassDetailDo) Having(conds ...gen.Condition) *groupClassDetailDo {
	return g.withDO(g.DO.Having(conds...))
}

func (g groupClassDetailDo) Limit(limit int) *groupClassDetailDo {
	return g.withDO(g.DO.Limit(limit))
}

func (g groupClassDetailDo) Offset(offset int) *groupClassDetailDo {
	return g.withDO(g.DO.Offset(offset))
}

func (g groupClassDetailDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *groupClassDetailDo {
	return g.withDO(g.DO.Scopes(funcs...))
}

func (g groupClassDetailDo) Unscoped() *groupClassDetailDo {
	return g.withDO(g.DO.Unscoped())
}

func (g groupClassDetailDo) Create(values ...*model.GroupClassDetail) error {
	if len(values) == 0 {
		return nil
	}
	return g.DO.Create(values)
}

func (g groupClassDetailDo) CreateInBatches(values []*model.GroupClassDetail, batchSize int) error {
	return g.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (g groupClassDetailDo) Save(values ...*model.GroupClassDetail) error {
	if len(values) == 0 {
		return nil
	}
	return g.DO.Save(values)
}

func (g groupClassDetailDo) First() (*model.GroupClassDetail, error) {
	if result, err := g.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.GroupClassDetail), nil
	}
}

func (g groupClassDetailDo) Take() (*model.GroupClassDetail, error) {
	if result, err := g.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.GroupClassDetail), nil
	}
}

func (g groupClassDetailDo) Last() (*model.GroupClassDetail, error) {
	if result, err := g.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.GroupClassDetail), nil
	}
}

func (g groupClassDetailDo) Find() ([]*model.GroupClassDetail, error) {
	result, err := g.DO.Find()
	return result.([]*model.GroupClassDetail), err
}

func (g groupClassDetailDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.GroupClassDetail, err error) {
	buf := make([]*model.GroupClassDetail, 0, batchSize)
	err = g.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (g groupClassDetailDo) FindInBatches(result *[]*model.GroupClassDetail, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return g.DO.FindInBatches(result, batchSize, fc)
}

func (g groupClassDetailDo) Attrs(attrs ...field.AssignExpr) *groupClassDetailDo {
	return g.withDO(g.DO.Attrs(attrs...))
}

func (g groupClassDetailDo) Assign(attrs ...field.AssignExpr) *groupClassDetailDo {
	return g.withDO(g.DO.Assign(attrs...))
}

func (g groupClassDetailDo) Joins(fields ...field.RelationField) *groupClassDetailDo {
	for _, _f := range fields {
		g = *g.withDO(g.DO.Joins(_f))
	}
	return &g
}

func (g groupClassDetailDo) Preload(fields ...field.RelationField) *groupClassDetailDo {
	for _, _f := range fields {
		g = *g.withDO(g.DO.Preload(_f))
	}
	return &g
}

func (g groupClassDetailDo) FirstOrInit() (*model.GroupClassDetail, error) {
	if result, err := g.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.GroupClassDetail), nil
	}
}

func (g groupClassDetailDo) FirstOrCreate() (*model.GroupClassDetail, error) {
	if result, err := g.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.GroupClassDetail), nil
	}
}

func (g groupClassDetailDo) FindByPage(offset int, limit int) (result []*model.GroupClassDetail, count int64, err error) {
	result, err = g.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = g.Offset(-1).Limit(-1).Count()
	return
}

func (g groupClassDetailDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = g.Count()
	if err != nil {
		return
	}

	err = g.Offset(offset).Limit(limit).Scan(result)
	return
}

func (g groupClassDetailDo) Scan(result interface{}) (err error) {
	return g.DO.Scan(result)
}

func (g groupClassDetailDo) Delete(models ...*model.GroupClassDetail) (result gen.ResultInfo, err error) {
	return g.DO.Delete(models)
}

func (g *groupClassDetailDo) withDO(do gen.Dao) *groupClassDetailDo {
	g.DO = *do.(*gen.DO)
	return g
}
