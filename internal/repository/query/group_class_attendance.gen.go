// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
)

func newGroupClassAttendance(db *gorm.DB, opts ...gen.DOOption) groupClassAttendance {
	_groupClassAttendance := groupClassAttendance{}

	_groupClassAttendance.groupClassAttendanceDo.UseDB(db, opts...)
	_groupClassAttendance.groupClassAttendanceDo.UseModel(&model.GroupClassAttendance{})

	tableName := _groupClassAttendance.groupClassAttendanceDo.TableName()
	_groupClassAttendance.ALL = field.NewAsterisk(tableName)
	_groupClassAttendance.ID = field.NewInt64(tableName, "id")
	_groupClassAttendance.CompanyID = field.NewInt64(tableName, "company_id")
	_groupClassAttendance.BusinessID = field.NewInt64(tableName, "business_id")
	_groupClassAttendance.FulfillmentID = field.NewInt64(tableName, "fulfillment_id")
	_groupClassAttendance.GroupClassDetailID = field.NewInt64(tableName, "group_class_detail_id")
	_groupClassAttendance.GroupClassSessionID = field.NewInt64(tableName, "group_class_session_id")
	_groupClassAttendance.PetID = field.NewInt64(tableName, "pet_id")
	_groupClassAttendance.CheckInTime = field.NewTime(tableName, "check_in_time")
	_groupClassAttendance.CreatedAt = field.NewTime(tableName, "created_at")
	_groupClassAttendance.UpdatedAt = field.NewTime(tableName, "updated_at")
	_groupClassAttendance.DeletedAt = field.NewField(tableName, "deleted_at")

	_groupClassAttendance.fillFieldMap()

	return _groupClassAttendance
}

type groupClassAttendance struct {
	groupClassAttendanceDo groupClassAttendanceDo

	ALL                 field.Asterisk
	ID                  field.Int64
	CompanyID           field.Int64
	BusinessID          field.Int64
	FulfillmentID       field.Int64 // fulfillment.id
	GroupClassDetailID  field.Int64 // group_class_detail.id
	GroupClassSessionID field.Int64 // training_session.id
	PetID               field.Int64
	CheckInTime         field.Time // Check in time of pet
	CreatedAt           field.Time
	UpdatedAt           field.Time
	DeletedAt           field.Field

	fieldMap map[string]field.Expr
}

func (g groupClassAttendance) Table(newTableName string) *groupClassAttendance {
	g.groupClassAttendanceDo.UseTable(newTableName)
	return g.updateTableName(newTableName)
}

func (g groupClassAttendance) As(alias string) *groupClassAttendance {
	g.groupClassAttendanceDo.DO = *(g.groupClassAttendanceDo.As(alias).(*gen.DO))
	return g.updateTableName(alias)
}

func (g *groupClassAttendance) updateTableName(table string) *groupClassAttendance {
	g.ALL = field.NewAsterisk(table)
	g.ID = field.NewInt64(table, "id")
	g.CompanyID = field.NewInt64(table, "company_id")
	g.BusinessID = field.NewInt64(table, "business_id")
	g.FulfillmentID = field.NewInt64(table, "fulfillment_id")
	g.GroupClassDetailID = field.NewInt64(table, "group_class_detail_id")
	g.GroupClassSessionID = field.NewInt64(table, "group_class_session_id")
	g.PetID = field.NewInt64(table, "pet_id")
	g.CheckInTime = field.NewTime(table, "check_in_time")
	g.CreatedAt = field.NewTime(table, "created_at")
	g.UpdatedAt = field.NewTime(table, "updated_at")
	g.DeletedAt = field.NewField(table, "deleted_at")

	g.fillFieldMap()

	return g
}

func (g *groupClassAttendance) WithContext(ctx context.Context) *groupClassAttendanceDo {
	return g.groupClassAttendanceDo.WithContext(ctx)
}

func (g groupClassAttendance) TableName() string { return g.groupClassAttendanceDo.TableName() }

func (g groupClassAttendance) Alias() string { return g.groupClassAttendanceDo.Alias() }

func (g groupClassAttendance) Columns(cols ...field.Expr) gen.Columns {
	return g.groupClassAttendanceDo.Columns(cols...)
}

func (g *groupClassAttendance) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := g.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (g *groupClassAttendance) fillFieldMap() {
	g.fieldMap = make(map[string]field.Expr, 11)
	g.fieldMap["id"] = g.ID
	g.fieldMap["company_id"] = g.CompanyID
	g.fieldMap["business_id"] = g.BusinessID
	g.fieldMap["fulfillment_id"] = g.FulfillmentID
	g.fieldMap["group_class_detail_id"] = g.GroupClassDetailID
	g.fieldMap["group_class_session_id"] = g.GroupClassSessionID
	g.fieldMap["pet_id"] = g.PetID
	g.fieldMap["check_in_time"] = g.CheckInTime
	g.fieldMap["created_at"] = g.CreatedAt
	g.fieldMap["updated_at"] = g.UpdatedAt
	g.fieldMap["deleted_at"] = g.DeletedAt
}

func (g groupClassAttendance) clone(db *gorm.DB) groupClassAttendance {
	g.groupClassAttendanceDo.ReplaceConnPool(db.Statement.ConnPool)
	return g
}

func (g groupClassAttendance) replaceDB(db *gorm.DB) groupClassAttendance {
	g.groupClassAttendanceDo.ReplaceDB(db)
	return g
}

type groupClassAttendanceDo struct{ gen.DO }

func (g groupClassAttendanceDo) Debug() *groupClassAttendanceDo {
	return g.withDO(g.DO.Debug())
}

func (g groupClassAttendanceDo) WithContext(ctx context.Context) *groupClassAttendanceDo {
	return g.withDO(g.DO.WithContext(ctx))
}

func (g groupClassAttendanceDo) ReadDB() *groupClassAttendanceDo {
	return g.Clauses(dbresolver.Read)
}

func (g groupClassAttendanceDo) WriteDB() *groupClassAttendanceDo {
	return g.Clauses(dbresolver.Write)
}

func (g groupClassAttendanceDo) Session(config *gorm.Session) *groupClassAttendanceDo {
	return g.withDO(g.DO.Session(config))
}

func (g groupClassAttendanceDo) Clauses(conds ...clause.Expression) *groupClassAttendanceDo {
	return g.withDO(g.DO.Clauses(conds...))
}

func (g groupClassAttendanceDo) Returning(value interface{}, columns ...string) *groupClassAttendanceDo {
	return g.withDO(g.DO.Returning(value, columns...))
}

func (g groupClassAttendanceDo) Not(conds ...gen.Condition) *groupClassAttendanceDo {
	return g.withDO(g.DO.Not(conds...))
}

func (g groupClassAttendanceDo) Or(conds ...gen.Condition) *groupClassAttendanceDo {
	return g.withDO(g.DO.Or(conds...))
}

func (g groupClassAttendanceDo) Select(conds ...field.Expr) *groupClassAttendanceDo {
	return g.withDO(g.DO.Select(conds...))
}

func (g groupClassAttendanceDo) Where(conds ...gen.Condition) *groupClassAttendanceDo {
	return g.withDO(g.DO.Where(conds...))
}

func (g groupClassAttendanceDo) Order(conds ...field.Expr) *groupClassAttendanceDo {
	return g.withDO(g.DO.Order(conds...))
}

func (g groupClassAttendanceDo) Distinct(cols ...field.Expr) *groupClassAttendanceDo {
	return g.withDO(g.DO.Distinct(cols...))
}

func (g groupClassAttendanceDo) Omit(cols ...field.Expr) *groupClassAttendanceDo {
	return g.withDO(g.DO.Omit(cols...))
}

func (g groupClassAttendanceDo) Join(table schema.Tabler, on ...field.Expr) *groupClassAttendanceDo {
	return g.withDO(g.DO.Join(table, on...))
}

func (g groupClassAttendanceDo) LeftJoin(table schema.Tabler, on ...field.Expr) *groupClassAttendanceDo {
	return g.withDO(g.DO.LeftJoin(table, on...))
}

func (g groupClassAttendanceDo) RightJoin(table schema.Tabler, on ...field.Expr) *groupClassAttendanceDo {
	return g.withDO(g.DO.RightJoin(table, on...))
}

func (g groupClassAttendanceDo) Group(cols ...field.Expr) *groupClassAttendanceDo {
	return g.withDO(g.DO.Group(cols...))
}

func (g groupClassAttendanceDo) Having(conds ...gen.Condition) *groupClassAttendanceDo {
	return g.withDO(g.DO.Having(conds...))
}

func (g groupClassAttendanceDo) Limit(limit int) *groupClassAttendanceDo {
	return g.withDO(g.DO.Limit(limit))
}

func (g groupClassAttendanceDo) Offset(offset int) *groupClassAttendanceDo {
	return g.withDO(g.DO.Offset(offset))
}

func (g groupClassAttendanceDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *groupClassAttendanceDo {
	return g.withDO(g.DO.Scopes(funcs...))
}

func (g groupClassAttendanceDo) Unscoped() *groupClassAttendanceDo {
	return g.withDO(g.DO.Unscoped())
}

func (g groupClassAttendanceDo) Create(values ...*model.GroupClassAttendance) error {
	if len(values) == 0 {
		return nil
	}
	return g.DO.Create(values)
}

func (g groupClassAttendanceDo) CreateInBatches(values []*model.GroupClassAttendance, batchSize int) error {
	return g.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (g groupClassAttendanceDo) Save(values ...*model.GroupClassAttendance) error {
	if len(values) == 0 {
		return nil
	}
	return g.DO.Save(values)
}

func (g groupClassAttendanceDo) First() (*model.GroupClassAttendance, error) {
	if result, err := g.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.GroupClassAttendance), nil
	}
}

func (g groupClassAttendanceDo) Take() (*model.GroupClassAttendance, error) {
	if result, err := g.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.GroupClassAttendance), nil
	}
}

func (g groupClassAttendanceDo) Last() (*model.GroupClassAttendance, error) {
	if result, err := g.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.GroupClassAttendance), nil
	}
}

func (g groupClassAttendanceDo) Find() ([]*model.GroupClassAttendance, error) {
	result, err := g.DO.Find()
	return result.([]*model.GroupClassAttendance), err
}

func (g groupClassAttendanceDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.GroupClassAttendance, err error) {
	buf := make([]*model.GroupClassAttendance, 0, batchSize)
	err = g.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (g groupClassAttendanceDo) FindInBatches(result *[]*model.GroupClassAttendance, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return g.DO.FindInBatches(result, batchSize, fc)
}

func (g groupClassAttendanceDo) Attrs(attrs ...field.AssignExpr) *groupClassAttendanceDo {
	return g.withDO(g.DO.Attrs(attrs...))
}

func (g groupClassAttendanceDo) Assign(attrs ...field.AssignExpr) *groupClassAttendanceDo {
	return g.withDO(g.DO.Assign(attrs...))
}

func (g groupClassAttendanceDo) Joins(fields ...field.RelationField) *groupClassAttendanceDo {
	for _, _f := range fields {
		g = *g.withDO(g.DO.Joins(_f))
	}
	return &g
}

func (g groupClassAttendanceDo) Preload(fields ...field.RelationField) *groupClassAttendanceDo {
	for _, _f := range fields {
		g = *g.withDO(g.DO.Preload(_f))
	}
	return &g
}

func (g groupClassAttendanceDo) FirstOrInit() (*model.GroupClassAttendance, error) {
	if result, err := g.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.GroupClassAttendance), nil
	}
}

func (g groupClassAttendanceDo) FirstOrCreate() (*model.GroupClassAttendance, error) {
	if result, err := g.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.GroupClassAttendance), nil
	}
}

func (g groupClassAttendanceDo) FindByPage(offset int, limit int) (result []*model.GroupClassAttendance, count int64, err error) {
	result, err = g.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = g.Offset(-1).Limit(-1).Count()
	return
}

func (g groupClassAttendanceDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = g.Count()
	if err != nil {
		return
	}

	err = g.Offset(offset).Limit(limit).Scan(result)
	return
}

func (g groupClassAttendanceDo) Scan(result interface{}) (err error) {
	return g.DO.Scan(result)
}

func (g groupClassAttendanceDo) Delete(models ...*model.GroupClassAttendance) (result gen.ResultInfo, err error) {
	return g.DO.Delete(models)
}

func (g *groupClassAttendanceDo) withDO(do gen.Dao) *groupClassAttendanceDo {
	g.DO = *do.(*gen.DO)
	return g
}
