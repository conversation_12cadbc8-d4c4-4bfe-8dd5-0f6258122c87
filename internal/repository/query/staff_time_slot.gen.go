// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
)

func newStaffTimeSlot(db *gorm.DB, opts ...gen.DOOption) staffTimeSlot {
	_staffTimeSlot := staffTimeSlot{}

	_staffTimeSlot.staffTimeSlotDo.UseDB(db, opts...)
	_staffTimeSlot.staffTimeSlotDo.UseModel(&model.StaffTimeSlot{})

	tableName := _staffTimeSlot.staffTimeSlotDo.TableName()
	_staffTimeSlot.ALL = field.NewAsterisk(tableName)
	_staffTimeSlot.ID = field.NewInt64(tableName, "id")
	_staffTimeSlot.CompanyID = field.NewInt64(tableName, "company_id")
	_staffTimeSlot.BusinessID = field.NewInt64(tableName, "business_id")
	_staffTimeSlot.FulfillmentID = field.NewInt64(tableName, "fulfillment_id")
	_staffTimeSlot.CareType = field.NewField(tableName, "care_type")
	_staffTimeSlot.DetailID = field.NewInt64(tableName, "detail_id")
	_staffTimeSlot.OrderLineItemID = field.NewInt64(tableName, "order_line_item_id")
	_staffTimeSlot.StaffID = field.NewInt64(tableName, "staff_id")
	_staffTimeSlot.PetID = field.NewInt64(tableName, "pet_id")
	_staffTimeSlot.CustomerID = field.NewInt64(tableName, "customer_id")
	_staffTimeSlot.StartDatetime = field.NewTime(tableName, "start_datetime")
	_staffTimeSlot.EndDatetime = field.NewTime(tableName, "end_datetime")
	_staffTimeSlot.CreatedAt = field.NewTime(tableName, "created_at")
	_staffTimeSlot.UpdatedAt = field.NewTime(tableName, "updated_at")
	_staffTimeSlot.DeletedAt = field.NewField(tableName, "deleted_at")

	_staffTimeSlot.fillFieldMap()

	return _staffTimeSlot
}

type staffTimeSlot struct {
	staffTimeSlotDo staffTimeSlotDo

	ALL             field.Asterisk
	ID              field.Int64
	CompanyID       field.Int64
	BusinessID      field.Int64
	FulfillmentID   field.Int64 // fulfillment.id
	CareType        field.Field // 1-Grooming, 2-Boarding, 3-Daycare, 4-Evaluation, 5-Dog_walking, 6-Training group class
	DetailID        field.Int64 // care_type = 1 grooming_detail.id, care_type = 4 evaluation_detail.id, care_type = 5 dog_walking_detail.id
	OrderLineItemID field.Int64 // order_line_item.id, Used to record whether the service has created an order
	StaffID         field.Int64 // The staff id who performing the service or add-on
	PetID           field.Int64
	CustomerID      field.Int64
	StartDatetime   field.Time // The start date time of the this time slot
	EndDatetime     field.Time // The end date time of the this time slot
	CreatedAt       field.Time
	UpdatedAt       field.Time
	DeletedAt       field.Field

	fieldMap map[string]field.Expr
}

func (s staffTimeSlot) Table(newTableName string) *staffTimeSlot {
	s.staffTimeSlotDo.UseTable(newTableName)
	return s.updateTableName(newTableName)
}

func (s staffTimeSlot) As(alias string) *staffTimeSlot {
	s.staffTimeSlotDo.DO = *(s.staffTimeSlotDo.As(alias).(*gen.DO))
	return s.updateTableName(alias)
}

func (s *staffTimeSlot) updateTableName(table string) *staffTimeSlot {
	s.ALL = field.NewAsterisk(table)
	s.ID = field.NewInt64(table, "id")
	s.CompanyID = field.NewInt64(table, "company_id")
	s.BusinessID = field.NewInt64(table, "business_id")
	s.FulfillmentID = field.NewInt64(table, "fulfillment_id")
	s.CareType = field.NewField(table, "care_type")
	s.DetailID = field.NewInt64(table, "detail_id")
	s.OrderLineItemID = field.NewInt64(table, "order_line_item_id")
	s.StaffID = field.NewInt64(table, "staff_id")
	s.PetID = field.NewInt64(table, "pet_id")
	s.CustomerID = field.NewInt64(table, "customer_id")
	s.StartDatetime = field.NewTime(table, "start_datetime")
	s.EndDatetime = field.NewTime(table, "end_datetime")
	s.CreatedAt = field.NewTime(table, "created_at")
	s.UpdatedAt = field.NewTime(table, "updated_at")
	s.DeletedAt = field.NewField(table, "deleted_at")

	s.fillFieldMap()

	return s
}

func (s *staffTimeSlot) WithContext(ctx context.Context) *staffTimeSlotDo {
	return s.staffTimeSlotDo.WithContext(ctx)
}

func (s staffTimeSlot) TableName() string { return s.staffTimeSlotDo.TableName() }

func (s staffTimeSlot) Alias() string { return s.staffTimeSlotDo.Alias() }

func (s staffTimeSlot) Columns(cols ...field.Expr) gen.Columns {
	return s.staffTimeSlotDo.Columns(cols...)
}

func (s *staffTimeSlot) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := s.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (s *staffTimeSlot) fillFieldMap() {
	s.fieldMap = make(map[string]field.Expr, 15)
	s.fieldMap["id"] = s.ID
	s.fieldMap["company_id"] = s.CompanyID
	s.fieldMap["business_id"] = s.BusinessID
	s.fieldMap["fulfillment_id"] = s.FulfillmentID
	s.fieldMap["care_type"] = s.CareType
	s.fieldMap["detail_id"] = s.DetailID
	s.fieldMap["order_line_item_id"] = s.OrderLineItemID
	s.fieldMap["staff_id"] = s.StaffID
	s.fieldMap["pet_id"] = s.PetID
	s.fieldMap["customer_id"] = s.CustomerID
	s.fieldMap["start_datetime"] = s.StartDatetime
	s.fieldMap["end_datetime"] = s.EndDatetime
	s.fieldMap["created_at"] = s.CreatedAt
	s.fieldMap["updated_at"] = s.UpdatedAt
	s.fieldMap["deleted_at"] = s.DeletedAt
}

func (s staffTimeSlot) clone(db *gorm.DB) staffTimeSlot {
	s.staffTimeSlotDo.ReplaceConnPool(db.Statement.ConnPool)
	return s
}

func (s staffTimeSlot) replaceDB(db *gorm.DB) staffTimeSlot {
	s.staffTimeSlotDo.ReplaceDB(db)
	return s
}

type staffTimeSlotDo struct{ gen.DO }

func (s staffTimeSlotDo) Debug() *staffTimeSlotDo {
	return s.withDO(s.DO.Debug())
}

func (s staffTimeSlotDo) WithContext(ctx context.Context) *staffTimeSlotDo {
	return s.withDO(s.DO.WithContext(ctx))
}

func (s staffTimeSlotDo) ReadDB() *staffTimeSlotDo {
	return s.Clauses(dbresolver.Read)
}

func (s staffTimeSlotDo) WriteDB() *staffTimeSlotDo {
	return s.Clauses(dbresolver.Write)
}

func (s staffTimeSlotDo) Session(config *gorm.Session) *staffTimeSlotDo {
	return s.withDO(s.DO.Session(config))
}

func (s staffTimeSlotDo) Clauses(conds ...clause.Expression) *staffTimeSlotDo {
	return s.withDO(s.DO.Clauses(conds...))
}

func (s staffTimeSlotDo) Returning(value interface{}, columns ...string) *staffTimeSlotDo {
	return s.withDO(s.DO.Returning(value, columns...))
}

func (s staffTimeSlotDo) Not(conds ...gen.Condition) *staffTimeSlotDo {
	return s.withDO(s.DO.Not(conds...))
}

func (s staffTimeSlotDo) Or(conds ...gen.Condition) *staffTimeSlotDo {
	return s.withDO(s.DO.Or(conds...))
}

func (s staffTimeSlotDo) Select(conds ...field.Expr) *staffTimeSlotDo {
	return s.withDO(s.DO.Select(conds...))
}

func (s staffTimeSlotDo) Where(conds ...gen.Condition) *staffTimeSlotDo {
	return s.withDO(s.DO.Where(conds...))
}

func (s staffTimeSlotDo) Order(conds ...field.Expr) *staffTimeSlotDo {
	return s.withDO(s.DO.Order(conds...))
}

func (s staffTimeSlotDo) Distinct(cols ...field.Expr) *staffTimeSlotDo {
	return s.withDO(s.DO.Distinct(cols...))
}

func (s staffTimeSlotDo) Omit(cols ...field.Expr) *staffTimeSlotDo {
	return s.withDO(s.DO.Omit(cols...))
}

func (s staffTimeSlotDo) Join(table schema.Tabler, on ...field.Expr) *staffTimeSlotDo {
	return s.withDO(s.DO.Join(table, on...))
}

func (s staffTimeSlotDo) LeftJoin(table schema.Tabler, on ...field.Expr) *staffTimeSlotDo {
	return s.withDO(s.DO.LeftJoin(table, on...))
}

func (s staffTimeSlotDo) RightJoin(table schema.Tabler, on ...field.Expr) *staffTimeSlotDo {
	return s.withDO(s.DO.RightJoin(table, on...))
}

func (s staffTimeSlotDo) Group(cols ...field.Expr) *staffTimeSlotDo {
	return s.withDO(s.DO.Group(cols...))
}

func (s staffTimeSlotDo) Having(conds ...gen.Condition) *staffTimeSlotDo {
	return s.withDO(s.DO.Having(conds...))
}

func (s staffTimeSlotDo) Limit(limit int) *staffTimeSlotDo {
	return s.withDO(s.DO.Limit(limit))
}

func (s staffTimeSlotDo) Offset(offset int) *staffTimeSlotDo {
	return s.withDO(s.DO.Offset(offset))
}

func (s staffTimeSlotDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *staffTimeSlotDo {
	return s.withDO(s.DO.Scopes(funcs...))
}

func (s staffTimeSlotDo) Unscoped() *staffTimeSlotDo {
	return s.withDO(s.DO.Unscoped())
}

func (s staffTimeSlotDo) Create(values ...*model.StaffTimeSlot) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Create(values)
}

func (s staffTimeSlotDo) CreateInBatches(values []*model.StaffTimeSlot, batchSize int) error {
	return s.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (s staffTimeSlotDo) Save(values ...*model.StaffTimeSlot) error {
	if len(values) == 0 {
		return nil
	}
	return s.DO.Save(values)
}

func (s staffTimeSlotDo) First() (*model.StaffTimeSlot, error) {
	if result, err := s.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.StaffTimeSlot), nil
	}
}

func (s staffTimeSlotDo) Take() (*model.StaffTimeSlot, error) {
	if result, err := s.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.StaffTimeSlot), nil
	}
}

func (s staffTimeSlotDo) Last() (*model.StaffTimeSlot, error) {
	if result, err := s.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.StaffTimeSlot), nil
	}
}

func (s staffTimeSlotDo) Find() ([]*model.StaffTimeSlot, error) {
	result, err := s.DO.Find()
	return result.([]*model.StaffTimeSlot), err
}

func (s staffTimeSlotDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.StaffTimeSlot, err error) {
	buf := make([]*model.StaffTimeSlot, 0, batchSize)
	err = s.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (s staffTimeSlotDo) FindInBatches(result *[]*model.StaffTimeSlot, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return s.DO.FindInBatches(result, batchSize, fc)
}

func (s staffTimeSlotDo) Attrs(attrs ...field.AssignExpr) *staffTimeSlotDo {
	return s.withDO(s.DO.Attrs(attrs...))
}

func (s staffTimeSlotDo) Assign(attrs ...field.AssignExpr) *staffTimeSlotDo {
	return s.withDO(s.DO.Assign(attrs...))
}

func (s staffTimeSlotDo) Joins(fields ...field.RelationField) *staffTimeSlotDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Joins(_f))
	}
	return &s
}

func (s staffTimeSlotDo) Preload(fields ...field.RelationField) *staffTimeSlotDo {
	for _, _f := range fields {
		s = *s.withDO(s.DO.Preload(_f))
	}
	return &s
}

func (s staffTimeSlotDo) FirstOrInit() (*model.StaffTimeSlot, error) {
	if result, err := s.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.StaffTimeSlot), nil
	}
}

func (s staffTimeSlotDo) FirstOrCreate() (*model.StaffTimeSlot, error) {
	if result, err := s.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.StaffTimeSlot), nil
	}
}

func (s staffTimeSlotDo) FindByPage(offset int, limit int) (result []*model.StaffTimeSlot, count int64, err error) {
	result, err = s.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = s.Offset(-1).Limit(-1).Count()
	return
}

func (s staffTimeSlotDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = s.Count()
	if err != nil {
		return
	}

	err = s.Offset(offset).Limit(limit).Scan(result)
	return
}

func (s staffTimeSlotDo) Scan(result interface{}) (err error) {
	return s.DO.Scan(result)
}

func (s staffTimeSlotDo) Delete(models ...*model.StaffTimeSlot) (result gen.ResultInfo, err error) {
	return s.DO.Delete(models)
}

func (s *staffTimeSlotDo) withDO(do gen.Dao) *staffTimeSlotDo {
	s.DO = *do.(*gen.DO)
	return s
}
