// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
)

func newFulfillment(db *gorm.DB, opts ...gen.DOOption) fulfillment {
	_fulfillment := fulfillment{}

	_fulfillment.fulfillmentDo.UseDB(db, opts...)
	_fulfillment.fulfillmentDo.UseModel(&model.Fulfillment{})

	tableName := _fulfillment.fulfillmentDo.TableName()
	_fulfillment.ALL = field.NewAsterisk(tableName)
	_fulfillment.ID = field.NewInt64(tableName, "id")
	_fulfillment.CompanyID = field.NewInt64(tableName, "company_id")
	_fulfillment.BusinessID = field.NewInt64(tableName, "business_id")
	_fulfillment.CustomerID = field.NewInt64(tableName, "customer_id")
	_fulfillment.BookingRequestID = field.NewInt64(tableName, "booking_request_id")
	_fulfillment.StartDatetime = field.NewTime(tableName, "start_datetime")
	_fulfillment.EndDatetime = field.NewTime(tableName, "end_datetime")
	_fulfillment.Status = field.NewField(tableName, "status")
	_fulfillment.ColorCode = field.NewString(tableName, "color_code")
	_fulfillment.ServiceTypeInclude = field.NewInt32(tableName, "service_type_include")
	_fulfillment.Source = field.NewField(tableName, "source")
	_fulfillment.RepeatRuleID = field.NewInt32(tableName, "repeat_rule_id")
	_fulfillment.CreatedAt = field.NewTime(tableName, "created_at")
	_fulfillment.UpdatedAt = field.NewTime(tableName, "updated_at")
	_fulfillment.DeletedAt = field.NewField(tableName, "deleted_at")
	_fulfillment.OrderID = field.NewInt64(tableName, "order_id")

	_fulfillment.fillFieldMap()

	return _fulfillment
}

type fulfillment struct {
	fulfillmentDo fulfillmentDo

	ALL                field.Asterisk
	ID                 field.Int64
	CompanyID          field.Int64
	BusinessID         field.Int64
	CustomerID         field.Int64
	BookingRequestID   field.Int64 // booking_request.id, A booking request may be scheduled for multiple fulfillment
	StartDatetime      field.Time  // Minimum start date time for all service details in the current fulfillment
	EndDatetime        field.Time  // Maximum start date time for all service details in the current fulfillment
	Status             field.Field // 1-unconfirmed, 2-confirmed, 3-finished, 4-canceled, 5-ready, 6-checked-in
	ColorCode          field.String
	ServiceTypeInclude field.Int32 // The value of the bit composition of the various types of services, 1-boarding, 2-daycare, 3-grooming, 4-evaluation, 5-dog_walking
	Source             field.Field // 22018-web, 22168-ob, 17216-android, 17802-ios, 23426-dm, 19826-gc, 23333-openapi
	RepeatRuleID       field.Int32 // fulfillment_repeat_rule.id
	CreatedAt          field.Time
	UpdatedAt          field.Time
	DeletedAt          field.Field
	OrderID            field.Int64

	fieldMap map[string]field.Expr
}

func (f fulfillment) Table(newTableName string) *fulfillment {
	f.fulfillmentDo.UseTable(newTableName)
	return f.updateTableName(newTableName)
}

func (f fulfillment) As(alias string) *fulfillment {
	f.fulfillmentDo.DO = *(f.fulfillmentDo.As(alias).(*gen.DO))
	return f.updateTableName(alias)
}

func (f *fulfillment) updateTableName(table string) *fulfillment {
	f.ALL = field.NewAsterisk(table)
	f.ID = field.NewInt64(table, "id")
	f.CompanyID = field.NewInt64(table, "company_id")
	f.BusinessID = field.NewInt64(table, "business_id")
	f.CustomerID = field.NewInt64(table, "customer_id")
	f.BookingRequestID = field.NewInt64(table, "booking_request_id")
	f.StartDatetime = field.NewTime(table, "start_datetime")
	f.EndDatetime = field.NewTime(table, "end_datetime")
	f.Status = field.NewField(table, "status")
	f.ColorCode = field.NewString(table, "color_code")
	f.ServiceTypeInclude = field.NewInt32(table, "service_type_include")
	f.Source = field.NewField(table, "source")
	f.RepeatRuleID = field.NewInt32(table, "repeat_rule_id")
	f.CreatedAt = field.NewTime(table, "created_at")
	f.UpdatedAt = field.NewTime(table, "updated_at")
	f.DeletedAt = field.NewField(table, "deleted_at")
	f.OrderID = field.NewInt64(table, "order_id")

	f.fillFieldMap()

	return f
}

func (f *fulfillment) WithContext(ctx context.Context) *fulfillmentDo {
	return f.fulfillmentDo.WithContext(ctx)
}

func (f fulfillment) TableName() string { return f.fulfillmentDo.TableName() }

func (f fulfillment) Alias() string { return f.fulfillmentDo.Alias() }

func (f fulfillment) Columns(cols ...field.Expr) gen.Columns { return f.fulfillmentDo.Columns(cols...) }

func (f *fulfillment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := f.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (f *fulfillment) fillFieldMap() {
	f.fieldMap = make(map[string]field.Expr, 16)
	f.fieldMap["id"] = f.ID
	f.fieldMap["company_id"] = f.CompanyID
	f.fieldMap["business_id"] = f.BusinessID
	f.fieldMap["customer_id"] = f.CustomerID
	f.fieldMap["booking_request_id"] = f.BookingRequestID
	f.fieldMap["start_datetime"] = f.StartDatetime
	f.fieldMap["end_datetime"] = f.EndDatetime
	f.fieldMap["status"] = f.Status
	f.fieldMap["color_code"] = f.ColorCode
	f.fieldMap["service_type_include"] = f.ServiceTypeInclude
	f.fieldMap["source"] = f.Source
	f.fieldMap["repeat_rule_id"] = f.RepeatRuleID
	f.fieldMap["created_at"] = f.CreatedAt
	f.fieldMap["updated_at"] = f.UpdatedAt
	f.fieldMap["deleted_at"] = f.DeletedAt
	f.fieldMap["order_id"] = f.OrderID
}

func (f fulfillment) clone(db *gorm.DB) fulfillment {
	f.fulfillmentDo.ReplaceConnPool(db.Statement.ConnPool)
	return f
}

func (f fulfillment) replaceDB(db *gorm.DB) fulfillment {
	f.fulfillmentDo.ReplaceDB(db)
	return f
}

type fulfillmentDo struct{ gen.DO }

func (f fulfillmentDo) Debug() *fulfillmentDo {
	return f.withDO(f.DO.Debug())
}

func (f fulfillmentDo) WithContext(ctx context.Context) *fulfillmentDo {
	return f.withDO(f.DO.WithContext(ctx))
}

func (f fulfillmentDo) ReadDB() *fulfillmentDo {
	return f.Clauses(dbresolver.Read)
}

func (f fulfillmentDo) WriteDB() *fulfillmentDo {
	return f.Clauses(dbresolver.Write)
}

func (f fulfillmentDo) Session(config *gorm.Session) *fulfillmentDo {
	return f.withDO(f.DO.Session(config))
}

func (f fulfillmentDo) Clauses(conds ...clause.Expression) *fulfillmentDo {
	return f.withDO(f.DO.Clauses(conds...))
}

func (f fulfillmentDo) Returning(value interface{}, columns ...string) *fulfillmentDo {
	return f.withDO(f.DO.Returning(value, columns...))
}

func (f fulfillmentDo) Not(conds ...gen.Condition) *fulfillmentDo {
	return f.withDO(f.DO.Not(conds...))
}

func (f fulfillmentDo) Or(conds ...gen.Condition) *fulfillmentDo {
	return f.withDO(f.DO.Or(conds...))
}

func (f fulfillmentDo) Select(conds ...field.Expr) *fulfillmentDo {
	return f.withDO(f.DO.Select(conds...))
}

func (f fulfillmentDo) Where(conds ...gen.Condition) *fulfillmentDo {
	return f.withDO(f.DO.Where(conds...))
}

func (f fulfillmentDo) Order(conds ...field.Expr) *fulfillmentDo {
	return f.withDO(f.DO.Order(conds...))
}

func (f fulfillmentDo) Distinct(cols ...field.Expr) *fulfillmentDo {
	return f.withDO(f.DO.Distinct(cols...))
}

func (f fulfillmentDo) Omit(cols ...field.Expr) *fulfillmentDo {
	return f.withDO(f.DO.Omit(cols...))
}

func (f fulfillmentDo) Join(table schema.Tabler, on ...field.Expr) *fulfillmentDo {
	return f.withDO(f.DO.Join(table, on...))
}

func (f fulfillmentDo) LeftJoin(table schema.Tabler, on ...field.Expr) *fulfillmentDo {
	return f.withDO(f.DO.LeftJoin(table, on...))
}

func (f fulfillmentDo) RightJoin(table schema.Tabler, on ...field.Expr) *fulfillmentDo {
	return f.withDO(f.DO.RightJoin(table, on...))
}

func (f fulfillmentDo) Group(cols ...field.Expr) *fulfillmentDo {
	return f.withDO(f.DO.Group(cols...))
}

func (f fulfillmentDo) Having(conds ...gen.Condition) *fulfillmentDo {
	return f.withDO(f.DO.Having(conds...))
}

func (f fulfillmentDo) Limit(limit int) *fulfillmentDo {
	return f.withDO(f.DO.Limit(limit))
}

func (f fulfillmentDo) Offset(offset int) *fulfillmentDo {
	return f.withDO(f.DO.Offset(offset))
}

func (f fulfillmentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) *fulfillmentDo {
	return f.withDO(f.DO.Scopes(funcs...))
}

func (f fulfillmentDo) Unscoped() *fulfillmentDo {
	return f.withDO(f.DO.Unscoped())
}

func (f fulfillmentDo) Create(values ...*model.Fulfillment) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Create(values)
}

func (f fulfillmentDo) CreateInBatches(values []*model.Fulfillment, batchSize int) error {
	return f.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (f fulfillmentDo) Save(values ...*model.Fulfillment) error {
	if len(values) == 0 {
		return nil
	}
	return f.DO.Save(values)
}

func (f fulfillmentDo) First() (*model.Fulfillment, error) {
	if result, err := f.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.Fulfillment), nil
	}
}

func (f fulfillmentDo) Take() (*model.Fulfillment, error) {
	if result, err := f.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.Fulfillment), nil
	}
}

func (f fulfillmentDo) Last() (*model.Fulfillment, error) {
	if result, err := f.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.Fulfillment), nil
	}
}

func (f fulfillmentDo) Find() ([]*model.Fulfillment, error) {
	result, err := f.DO.Find()
	return result.([]*model.Fulfillment), err
}

func (f fulfillmentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.Fulfillment, err error) {
	buf := make([]*model.Fulfillment, 0, batchSize)
	err = f.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (f fulfillmentDo) FindInBatches(result *[]*model.Fulfillment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return f.DO.FindInBatches(result, batchSize, fc)
}

func (f fulfillmentDo) Attrs(attrs ...field.AssignExpr) *fulfillmentDo {
	return f.withDO(f.DO.Attrs(attrs...))
}

func (f fulfillmentDo) Assign(attrs ...field.AssignExpr) *fulfillmentDo {
	return f.withDO(f.DO.Assign(attrs...))
}

func (f fulfillmentDo) Joins(fields ...field.RelationField) *fulfillmentDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Joins(_f))
	}
	return &f
}

func (f fulfillmentDo) Preload(fields ...field.RelationField) *fulfillmentDo {
	for _, _f := range fields {
		f = *f.withDO(f.DO.Preload(_f))
	}
	return &f
}

func (f fulfillmentDo) FirstOrInit() (*model.Fulfillment, error) {
	if result, err := f.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.Fulfillment), nil
	}
}

func (f fulfillmentDo) FirstOrCreate() (*model.Fulfillment, error) {
	if result, err := f.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.Fulfillment), nil
	}
}

func (f fulfillmentDo) FindByPage(offset int, limit int) (result []*model.Fulfillment, count int64, err error) {
	result, err = f.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = f.Offset(-1).Limit(-1).Count()
	return
}

func (f fulfillmentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = f.Count()
	if err != nil {
		return
	}

	err = f.Offset(offset).Limit(limit).Scan(result)
	return
}

func (f fulfillmentDo) Scan(result interface{}) (err error) {
	return f.DO.Scan(result)
}

func (f fulfillmentDo) Delete(models ...*model.Fulfillment) (result gen.ResultInfo, err error) {
	return f.DO.Delete(models)
}

func (f *fulfillmentDo) withDO(do gen.Dao) *fulfillmentDo {
	f.DO = *do.(*gen.DO)
	return f
}
