// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment (interfaces: GroupClassDetailRepository)
//
// Generated by this command:
//
//	mockgen -package=fulfillment -destination=mocks/mock_group_class_detail_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment GroupClassDetailRepository
//

// Package fulfillment is a generated GoMock package.
package fulfillment

import (
	context "context"
	reflect "reflect"

	fulfillment "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment"
	filter "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	model "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	query "github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/query"
	dto "github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	gomock "go.uber.org/mock/gomock"
)

// MockGroupClassDetailRepository is a mock of GroupClassDetailRepository interface.
type MockGroupClassDetailRepository struct {
	ctrl     *gomock.Controller
	recorder *MockGroupClassDetailRepositoryMockRecorder
	isgomock struct{}
}

// MockGroupClassDetailRepositoryMockRecorder is the mock recorder for MockGroupClassDetailRepository.
type MockGroupClassDetailRepositoryMockRecorder struct {
	mock *MockGroupClassDetailRepository
}

// NewMockGroupClassDetailRepository creates a new mock instance.
func NewMockGroupClassDetailRepository(ctrl *gomock.Controller) *MockGroupClassDetailRepository {
	mock := &MockGroupClassDetailRepository{ctrl: ctrl}
	mock.recorder = &MockGroupClassDetailRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGroupClassDetailRepository) EXPECT() *MockGroupClassDetailRepositoryMockRecorder {
	return m.recorder
}

// BatchUpdateSelective mocks base method.
func (m *MockGroupClassDetailRepository) BatchUpdateSelective(ctx context.Context, updates []dto.UpdateGroupClassDetailDTO) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchUpdateSelective", ctx, updates)
	ret0, _ := ret[0].(error)
	return ret0
}

// BatchUpdateSelective indicates an expected call of BatchUpdateSelective.
func (mr *MockGroupClassDetailRepositoryMockRecorder) BatchUpdateSelective(ctx, updates any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchUpdateSelective", reflect.TypeOf((*MockGroupClassDetailRepository)(nil).BatchUpdateSelective), ctx, updates)
}

// CountByInstanceID mocks base method.
func (m *MockGroupClassDetailRepository) CountByInstanceID(ctx context.Context, groupClassInstanceID int64) (int64, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CountByInstanceID", ctx, groupClassInstanceID)
	ret0, _ := ret[0].(int64)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CountByInstanceID indicates an expected call of CountByInstanceID.
func (mr *MockGroupClassDetailRepositoryMockRecorder) CountByInstanceID(ctx, groupClassInstanceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CountByInstanceID", reflect.TypeOf((*MockGroupClassDetailRepository)(nil).CountByInstanceID), ctx, groupClassInstanceID)
}

// Create mocks base method.
func (m *MockGroupClassDetailRepository) Create(ctx context.Context, arg1 *model.GroupClassDetail) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockGroupClassDetailRepositoryMockRecorder) Create(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockGroupClassDetailRepository)(nil).Create), ctx, arg1)
}

// DeleteByPetAndInstance mocks base method.
func (m *MockGroupClassDetailRepository) DeleteByPetAndInstance(ctx context.Context, petID, instanceID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteByPetAndInstance", ctx, petID, instanceID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteByPetAndInstance indicates an expected call of DeleteByPetAndInstance.
func (mr *MockGroupClassDetailRepositoryMockRecorder) DeleteByPetAndInstance(ctx, petID, instanceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteByPetAndInstance", reflect.TypeOf((*MockGroupClassDetailRepository)(nil).DeleteByPetAndInstance), ctx, petID, instanceID)
}

// GetByID mocks base method.
func (m *MockGroupClassDetailRepository) GetByID(ctx context.Context, id int64) (*model.GroupClassDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByID", ctx, id)
	ret0, _ := ret[0].(*model.GroupClassDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByID indicates an expected call of GetByID.
func (mr *MockGroupClassDetailRepositoryMockRecorder) GetByID(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByID", reflect.TypeOf((*MockGroupClassDetailRepository)(nil).GetByID), ctx, id)
}

// GetByPetAndInstance mocks base method.
func (m *MockGroupClassDetailRepository) GetByPetAndInstance(ctx context.Context, petID, instanceID int64) (*model.GroupClassDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetByPetAndInstance", ctx, petID, instanceID)
	ret0, _ := ret[0].(*model.GroupClassDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetByPetAndInstance indicates an expected call of GetByPetAndInstance.
func (mr *MockGroupClassDetailRepositoryMockRecorder) GetByPetAndInstance(ctx, petID, instanceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetByPetAndInstance", reflect.TypeOf((*MockGroupClassDetailRepository)(nil).GetByPetAndInstance), ctx, petID, instanceID)
}

// ListByFilter mocks base method.
func (m *MockGroupClassDetailRepository) ListByFilter(ctx context.Context, arg1 filter.ListGroupClassDetailFilter) ([]*model.GroupClassDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListByFilter", ctx, arg1)
	ret0, _ := ret[0].([]*model.GroupClassDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListByFilter indicates an expected call of ListByFilter.
func (mr *MockGroupClassDetailRepositoryMockRecorder) ListByFilter(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListByFilter", reflect.TypeOf((*MockGroupClassDetailRepository)(nil).ListByFilter), ctx, arg1)
}

// Update mocks base method.
func (m *MockGroupClassDetailRepository) Update(ctx context.Context, arg1 *model.GroupClassDetail) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Update", ctx, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockGroupClassDetailRepositoryMockRecorder) Update(ctx, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockGroupClassDetailRepository)(nil).Update), ctx, arg1)
}

// WithTX mocks base method.
func (m *MockGroupClassDetailRepository) WithTX(q *query.Query) fulfillment.GroupClassDetailRepository {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "WithTX", q)
	ret0, _ := ret[0].(fulfillment.GroupClassDetailRepository)
	return ret0
}

// WithQuery indicates an expected call of WithQuery.
func (mr *MockGroupClassDetailRepositoryMockRecorder) WithQuery(q any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "WithTX", reflect.TypeOf((*MockGroupClassDetailRepository)(nil).WithTX), q)
}
