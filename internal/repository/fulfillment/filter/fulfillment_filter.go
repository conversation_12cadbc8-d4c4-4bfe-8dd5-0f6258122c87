package filter

import (
	"time"

	fulfillmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	offeringpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/offering/v1"
)

type ListFulfillmentFilter struct {
	IDs          []int64
	CompanyID    *int64
	BusinessIDs  []int64
	Statuses     []fulfillmentpb.Status
	CustomerIDs  []int64
	CreatedAtMin *time.Time
}

type ListGroupClassDetailFilter struct {
	IDs                   []int64
	FulfillmentIDs        []int64
	PetIDs                []int64
	GroupClassIDs         []int64
	GroupClassInstanceIDs []int64
	Statuses              []fulfillmentpb.GroupClassDetailModel_Status
}

type ListStaffTimeSlotFilter struct {
	StaffTimeSlotIDs []int64
	CompanyID        *int64
	BusinessIDs      []int64
	StartTimeMin     *time.Time
	StartTimeMax     *time.Time
	CareTypes        []offeringpb.ServiceItemType
	PetIDs           []int64
	DetailIDs        []int64
	FulfillmentIDs   []int64
}

type ListGroupClassAttendanceFilter struct {
	FulfillmentIDs       []int64
	GroupClassSessionIDs []int64
}
