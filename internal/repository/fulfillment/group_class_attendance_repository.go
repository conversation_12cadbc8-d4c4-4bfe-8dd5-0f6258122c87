package fulfillment

import (
	"context"
	"errors"
	"time"

	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment/filter"
	"gorm.io/gorm"

	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/model"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/query"
)

//go:generate mockgen -package=fulfillment -destination=mocks/mock_group_class_attendance_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/fulfillment GroupClassAttendanceRepository
type GroupClassAttendanceRepository interface {
	WithTX(q *query.Query) GroupClassAttendanceRepository

	Create(ctx context.Context, model *model.GroupClassAttendance) error
	BatchCreate(ctx context.Context, model []*model.GroupClassAttendance) error
	ListByFilter(ctx context.Context, filter filter.ListGroupClassAttendanceFilter) ([]*model.GroupClassAttendance, error)
	DeleteByFulfillmentID(ctx context.Context, fulfillmentID int64) error
}

type groupClassAttendanceRepository struct {
	query *query.Query
}

func (r groupClassAttendanceRepository) BatchCreate(ctx context.Context, models []*model.GroupClassAttendance) error {
	for _, attendance := range models {
		err := r.query.WithContext(ctx).GroupClassAttendance.Create(attendance)
		if err != nil {
			if errors.Is(err, gorm.ErrDuplicatedKey) {
				return nil
			}
			return err
		}
	}
	return nil
}

func (r groupClassAttendanceRepository) ListByFilter(ctx context.Context, filter filter.ListGroupClassAttendanceFilter) ([]*model.GroupClassAttendance, error) {
	if len(filter.FulfillmentIDs) == 0 && len(filter.GroupClassSessionIDs) == 0 {
		return []*model.GroupClassAttendance{}, nil
	}
	selectQuery := r.query.WithContext(ctx).GroupClassAttendance.Select()
	if len(filter.FulfillmentIDs) > 0 {
		selectQuery.Where(r.query.GroupClassAttendance.FulfillmentID.In(filter.FulfillmentIDs...))
	}
	if len(filter.GroupClassSessionIDs) > 0 {
		selectQuery.Where(r.query.GroupClassAttendance.GroupClassSessionID.In(filter.GroupClassSessionIDs...))
	}
	selectQuery.Where(r.query.GroupClassAttendance.DeletedAt.IsNull())
	result, err := selectQuery.Find()
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (r groupClassAttendanceRepository) Create(ctx context.Context, model *model.GroupClassAttendance) error {
	return r.query.WithContext(ctx).GroupClassAttendance.Create(model)
}

func (r groupClassAttendanceRepository) DeleteByFulfillmentID(ctx context.Context, fulfillmentID int64) error {
	_, err := r.query.WithContext(ctx).GroupClassAttendance.Where(r.query.GroupClassAttendance.FulfillmentID.Eq(fulfillmentID)).Update(
		r.query.GroupClassAttendance.DeletedAt, time.Now())
	return err
}

func (r groupClassAttendanceRepository) WithTX(q *query.Query) GroupClassAttendanceRepository {
	if q != nil {
		return NewGroupClassAttendanceRepository(q)
	}
	return r
}

func NewGroupClassAttendanceRepository(q *query.Query) GroupClassAttendanceRepository {
	return &groupClassAttendanceRepository{
		query: q,
	}
}
