// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/organization (interfaces: TaxRepository)
//
// Generated by this command:
//
//	mockgen -package=organization -destination=mocks/mock_tax_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/organization TaxRepository
//

// Package organization is a generated GoMock package.
package organization

import (
	context "context"
	reflect "reflect"

	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockTaxRepository is a mock of TaxRepository interface.
type MockTaxRepository struct {
	ctrl     *gomock.Controller
	recorder *MockTaxRepositoryMockRecorder
	isgomock struct{}
}

// MockTaxRepositoryMockRecorder is the mock recorder for MockTaxRepository.
type MockTaxRepositoryMockRecorder struct {
	mock *MockTaxRepository
}

// NewMockTaxRepository creates a new mock instance.
func NewMockTaxRepository(ctrl *gomock.Controller) *MockTaxRepository {
	mock := &MockTaxRepository{ctrl: ctrl}
	mock.recorder = &MockTaxRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaxRepository) EXPECT() *MockTaxRepositoryMockRecorder {
	return m.recorder
}

// GetTaxRule mocks base method.
func (m *MockTaxRepository) GetTaxRule(ctx context.Context, taxID int64) (*organizationpb.TaxRuleModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaxRule", ctx, taxID)
	ret0, _ := ret[0].(*organizationpb.TaxRuleModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaxRule indicates an expected call of GetTaxRule.
func (mr *MockTaxRepositoryMockRecorder) GetTaxRule(ctx, taxID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaxRule", reflect.TypeOf((*MockTaxRepository)(nil).GetTaxRule), ctx, taxID)
}

// ListTaxRules mocks base method.
func (m *MockTaxRepository) ListTaxRules(ctx context.Context, taxID []int64) ([]*organizationpb.TaxRuleModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListTaxRules", ctx, taxID)
	ret0, _ := ret[0].([]*organizationpb.TaxRuleModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListTaxRules indicates an expected call of ListTaxRules.
func (mr *MockTaxRepositoryMockRecorder) ListTaxRules(ctx, taxID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListTaxRules", reflect.TypeOf((*MockTaxRepository)(nil).ListTaxRules), ctx, taxID)
}
