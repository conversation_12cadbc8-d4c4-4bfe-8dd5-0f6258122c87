package organization

import (
	"context"
	"github.com/MoeGolibrary/go-lib/grpc"
	organizationpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/organization/v1"
	organizationsvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/consts"
)

//go:generate mockgen -package=organization -destination=mocks/mock_tax_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/organization TaxRepository
type TaxRepository interface {
	GetTaxRule(ctx context.Context, taxID int64) (*organizationpb.TaxRuleModel, error)
	ListTaxRules(ctx context.Context, taxID []int64) ([]*organizationpb.TaxRuleModel, error)
}

type taxRepository struct {
	client organizationsvcpb.TaxRuleServiceClient
}

func (r taxRepository) GetTaxRule(ctx context.Context, taxID int64) (*organizationpb.TaxRuleModel, error) {
	request := &organizationsvcpb.GetTaxRuleRequest{
		Id: taxID,
	}
	response, err := r.client.GetTaxRule(ctx, request)
	if err != nil {
		return nil, err
	}
	return response.Rule, nil
}

func (r taxRepository) ListTaxRules(ctx context.Context, taxIDs []int64) ([]*organizationpb.TaxRuleModel, error) {
	request := &organizationsvcpb.BatchGetTaxRuleRequest{
		Ids: taxIDs,
	}
	response, err := r.client.BatchGetTaxRule(ctx, request)
	if err != nil {
		return nil, err
	}
	return response.Rules, nil
}

func NewTaxRepository() TaxRepository {
	return &taxRepository{
		client: grpc.NewClient(consts.OrganizationEndpoint, organizationsvcpb.NewTaxRuleServiceClient),
	}
}
