package order

import (
	"context"
	"github.com/MoeGolibrary/go-lib/grpc"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/consts"
	"github.com/samber/lo"
)

//go:generate mockgen -package=order -destination=mocks/mock_order_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/order Repository
type Repository interface {
	CreateOrder(ctx context.Context, request *ordersvcpb.CreateOrderRequest) (*orderpb.OrderDetailModel, error)
	BatchGetOrder(ctx context.Context, orderIDs []int64) ([]*orderpb.OrderDetailModel, error)
}

type repository struct {
	client ordersvcpb.OrderServiceClient
}

func (r repository) BatchGetOrder(ctx context.Context, orderIDs []int64) ([]*orderpb.OrderDetailModel, error) {
	response, err := r.client.GetOrderList(ctx, &ordersvcpb.GetOrderListRequest{
		OrderIds: orderIDs,
	})
	if err != nil {
		return nil, err
	}
	return response.GetOrderList(), nil
}

func (r repository) CreateOrder(ctx context.Context, request *ordersvcpb.CreateOrderRequest) (*orderpb.OrderDetailModel, error) {
	response, err := r.client.CreateOrder(ctx, request)
	if err != nil {
		return nil, err
	}
	orderDetail, err := r.client.GetOrderDetail(ctx, &ordersvcpb.GetOrderRequest{
		Id: lo.ToPtr(response.GetId()),
	})
	if err != nil {
		return nil, err
	}
	return orderDetail, nil
}

func NewOrderRepository() Repository {
	return &repository{
		client: grpc.NewClient(consts.OrderEndpoint, ordersvcpb.NewOrderServiceClient),
	}
}
