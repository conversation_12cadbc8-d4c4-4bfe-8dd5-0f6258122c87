// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/order (interfaces: Repository)
//
// Generated by this command:
//
//	mockgen -package=order -destination=mocks/mock_order_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/order Repository
//

// Package order is a generated GoMock package.
package order

import (
	context "context"
	reflect "reflect"

	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	ordersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/order/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// BatchGetOrder mocks base method.
func (m *MockRepository) BatchGetOrder(ctx context.Context, orderIDs []int64) ([]*orderpb.OrderDetailModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchGetOrder", ctx, orderIDs)
	ret0, _ := ret[0].([]*orderpb.OrderDetailModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// BatchGetOrder indicates an expected call of BatchGetOrder.
func (mr *MockRepositoryMockRecorder) BatchGetOrder(ctx, orderIDs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchGetOrder", reflect.TypeOf((*MockRepository)(nil).BatchGetOrder), ctx, orderIDs)
}

// CreateOrder mocks base method.
func (m *MockRepository) CreateOrder(ctx context.Context, request *ordersvcpb.CreateOrderRequest) (*orderpb.OrderDetailModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrder", ctx, request)
	ret0, _ := ret[0].(*orderpb.OrderDetailModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrder indicates an expected call of CreateOrder.
func (mr *MockRepositoryMockRecorder) CreateOrder(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrder", reflect.TypeOf((*MockRepository)(nil).CreateOrder), ctx, request)
}
