package customer

import (
	"context"
	"github.com/MoeGolibrary/go-lib/grpc"
	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	businesscustomersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/consts"
)

//go:generate mockgen -package=customer -destination=mocks/mock_customer_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/customer Repository
type Repository interface {
	GetCustomerInfo(ctx context.Context, customerID int64) (*businesscustomerpb.BusinessCustomerInfoModel, error)
}

type customerRepository struct {
	client businesscustomersvcpb.BusinessCustomerServiceClient
}

func (c customerRepository) GetCustomerInfo(ctx context.Context, customerID int64) (*businesscustomerpb.BusinessCustomerInfoModel, error) {
	response, err := c.client.GetCustomerInfo(ctx, &businesscustomersvcpb.GetCustomerInfoRequest{
		Id: customerID,
	})
	if err != nil {
		return nil, err
	}
	return response.GetCustomer(), nil
}

func NewRepository() Repository {
	return &customerRepository{
		client: grpc.NewClient(consts.BusinessCustomerEndpoint, businesscustomersvcpb.NewBusinessCustomerServiceClient),
	}
}
