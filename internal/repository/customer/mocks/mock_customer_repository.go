// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/customer (interfaces: Repository)
//
// Generated by this command:
//
//	mockgen -package=customer -destination=mocks/mock_customer_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/customer Repository
//

// Package customer is a generated GoMock package.
package customer

import (
	context "context"
	reflect "reflect"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// GetCustomerInfo mocks base method.
func (m *MockRepository) GetCustomerInfo(ctx context.Context, customerID int64) (*businesscustomerpb.BusinessCustomerInfoModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCustomerInfo", ctx, customerID)
	ret0, _ := ret[0].(*businesscustomerpb.BusinessCustomerInfoModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCustomerInfo indicates an expected call of GetCustomerInfo.
func (mr *MockRepositoryMockRecorder) GetCustomerInfo(ctx, customerID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCustomerInfo", reflect.TypeOf((*MockRepository)(nil).GetCustomerInfo), ctx, customerID)
}
