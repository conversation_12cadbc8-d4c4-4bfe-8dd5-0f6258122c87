// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/customer (interfaces: PetRepository)
//
// Generated by this command:
//
//	mockgen -package=customer -destination=mocks/mock_pet_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/customer PetRepository
//

// Package customer is a generated GoMock package.
package customer

import (
	context "context"
	reflect "reflect"

	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	gomock "go.uber.org/mock/gomock"
)

// MockPetRepository is a mock of PetRepository interface.
type MockPetRepository struct {
	ctrl     *gomock.Controller
	recorder *MockPetRepositoryMockRecorder
	isgomock struct{}
}

// MockPetRepositoryMockRecorder is the mock recorder for MockPetRepository.
type MockPetRepositoryMockRecorder struct {
	mock *MockPetRepository
}

// NewMockPetRepository creates a new mock instance.
func NewMockPetRepository(ctrl *gomock.Controller) *MockPetRepository {
	mock := &MockPetRepository{ctrl: ctrl}
	mock.recorder = &MockPetRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPetRepository) EXPECT() *MockPetRepositoryMockRecorder {
	return m.recorder
}

// GetPetInfo mocks base method.
func (m *MockPetRepository) GetPetInfo(ctx context.Context, petID int64) (*businesscustomerpb.BusinessCustomerPetInfoModel, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPetInfo", ctx, petID)
	ret0, _ := ret[0].(*businesscustomerpb.BusinessCustomerPetInfoModel)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPetInfo indicates an expected call of GetPetInfo.
func (mr *MockPetRepositoryMockRecorder) GetPetInfo(ctx, petID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPetInfo", reflect.TypeOf((*MockPetRepository)(nil).GetPetInfo), ctx, petID)
}
