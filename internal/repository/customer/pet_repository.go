package customer

import (
	"context"
	"github.com/MoeGolibrary/go-lib/grpc"
	businesscustomerpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/business_customer/v1"
	businesscustomersvcpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/business_customer/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/consts"
)

//go:generate mockgen -package=customer -destination=mocks/mock_pet_repository.go github.com/MoeGolibrary/moego-svc-fulfillment/internal/repository/customer PetRepository
type PetRepository interface {
	GetPetInfo(ctx context.Context, petID int64) (*businesscustomerpb.BusinessCustomerPetInfoModel, error)
}

type petRepository struct {
	client businesscustomersvcpb.BusinessCustomerPetServiceClient
}

func NewPetRepository() PetRepository {
	return &petRepository{
		client: grpc.NewClient(consts.BusinessCustomerEndpoint, businesscustomersvcpb.NewBusinessCustomerPetServiceClient),
	}
}

func (r petRepository) GetPetInfo(ctx context.Context, petID int64) (*businesscustomerpb.BusinessCustomerPetInfoModel, error) {
	response, err := r.client.GetPetInfo(ctx, &businesscustomersvcpb.GetPetInfoRequest{
		Id: petID,
	})
	if err != nil {
		return nil, err
	}
	return response.GetPet(), nil
}
