package growthbook

import (
	"context"
	"strconv"

	"github.com/MoeGolibrary/moego-svc-fulfillment/config"
	"github.com/growthbook/growthbook-golang"
	"go.uber.org/zap"

	"github.com/MoeGolibrary/go-lib/zlog"
)

const (
	GroupClassPayLater FeatureFlag = "group_class_pay_later"
)

type FeatureFlag string

type Attr struct {
	EnterpriseID *int64
	CompanyID    *int64
	BusinessId   *int64
}

type API interface {
	IsOn(ctx context.Context, featureFlag FeatureFlag, attr Attr) bool
}

type impl struct {
	cli *growthbook.Client
}

func New() API {
	cli, err := growthbook.NewClient(context.Background(),
		growthbook.WithApiHost(config.GetConfig().GrowthBook.Host),
		growthbook.WithClientKey(config.GetConfig().GrowthBook.ClientKey),
		growthbook.WithPollDataSource(config.GetConfig().GrowthBook.Interval),
	)
	if err != nil {
		panic(err)
	}
	if err := cli.EnsureLoaded(context.Background()); err != nil {
		panic(err)
	}
	return &impl{
		cli: cli,
	}
}

func (i *impl) IsOn(ctx context.Context, featureFlag FeatureFlag, attr Attr) bool {
	client, err := i.cli.WithAttributeOverrides(toGrowthBookAttr(attr))
	if err != nil {
		zlog.Error(ctx, "growth book WithAttributeOverrides err", zap.Error(err))
		return false
	}

	var enabled bool
	if v, ok := client.EvalFeature(ctx, string(featureFlag)).Value.(bool); ok {
		enabled = v
	}
	return enabled
}

func toGrowthBookAttr(attr Attr) growthbook.Attributes {
	result := growthbook.Attributes{}
	if attr.EnterpriseID != nil && *attr.EnterpriseID != 0 {
		result["enterprise"] = strconv.FormatInt(*attr.EnterpriseID, 10)
	}
	if attr.CompanyID != nil && *attr.CompanyID != 0 {
		result["company"] = strconv.FormatInt(*attr.CompanyID, 10)
	}
	if attr.BusinessId != nil && *attr.BusinessId != 0 {
		result["business"] = strconv.FormatInt(*attr.BusinessId, 10)
	}
	return result
}
