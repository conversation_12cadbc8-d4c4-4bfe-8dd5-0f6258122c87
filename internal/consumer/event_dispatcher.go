package consumer

import (
	"context"
	eventbus "github.com/MoeGolibrary/go-lib/event-bus"
	"github.com/MoeGolibrary/go-lib/zlog"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/config"
	"google.golang.org/protobuf/types/known/timestamppb"
	"sync"
)

// EventHandler defines the signature for event handlers
type EventHandler func(ctx context.Context, eventId string, createTime *timestamppb.Timestamp, eventData *eventbuspb.EventData) error

// EventDispatcher manages event handlers and dispatches events to appropriate handlers
type EventDispatcher struct {
	handlers map[eventbuspb.EventType]EventHandler
	mu       sync.RWMutex
}

// NewEventDispatcher creates a new event dispatcher
func NewEventDispatcher() *EventDispatcher {
	return &EventDispatcher{
		handlers: make(map[eventbuspb.EventType]EventHandler),
	}
}

// RegisterHandler registers a handler for a specific event type
func (d *EventDispatcher) RegisterHandler(eventType eventbuspb.EventType, handler EventHandler) {
	d.mu.Lock()
	defer d.mu.Unlock()
	d.handlers[eventType] = handler
}

// HandleEvent dispatches the event to the appropriate handler
func (d *EventDispatcher) HandleEvent(ctx context.Context, eventId string, createTime *timestamppb.Timestamp, eventType eventbuspb.EventType, eventData *eventbuspb.EventData) error {
	d.mu.RLock()
	handler, exists := d.handlers[eventType]
	d.mu.RUnlock()

	if !exists {
		return nil
	}

	return handler(ctx, eventId, createTime, eventData)
}

// Consumer represents the event bus consumer
type Consumer struct {
	consumer   *eventbus.Consumer
	dispatcher *EventDispatcher
}

// NewConsumer creates a new event bus consumer with dispatcher
func NewConsumer(
	dispatcher *EventDispatcher,
	orderHandlers *OrderHandler,
	offeringHandlers *OfferingHandler,
) *Consumer {
	registerOrderHandlers(dispatcher, orderHandlers)
	registerOfferingHandlers(dispatcher, offeringHandlers)

	consumer, err := eventbus.NewConsumer(config.GetConfig().EventBus, dispatcher.HandleEvent)

	if err != nil {
		panic(err)
	}

	return &Consumer{
		consumer:   consumer,
		dispatcher: dispatcher,
	}
}

// Start starts the event bus consumer
func (c *Consumer) Start() {
	err := c.consumer.Start()
	if err != nil {
		panic(err)
	}
}

// Stop stops the event bus consumer
func (c *Consumer) Stop() {
	if c.consumer != nil {
		c.consumer.Stop()
		zlog.Info(context.Background(), "Event bus consumer stopped")
	}
}

func registerOrderHandlers(dispatcher *EventDispatcher, orderHandler *OrderHandler) {
	dispatcher.RegisterHandler(eventbuspb.EventType_ORDER_COMPLETED, orderHandler.HandleOrderCompleted)
}

func registerOfferingHandlers(dispatcher *EventDispatcher, offeringHandler *OfferingHandler) {
	dispatcher.RegisterHandler(eventbuspb.EventType_OFFERING_GROUP_CLASS_SESSION_UPDATED, offeringHandler.HandleOfferingGroupClassSessionUpdated)
}
