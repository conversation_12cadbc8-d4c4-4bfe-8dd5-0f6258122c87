package consumer

import (
	"context"
	"github.com/MoeGolibrary/go-lib/zlog"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	fulfillmentpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/fulfillment/v1"
	orderpb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type OrderHandler struct {
	fulfillmentService service.FulfillmentService
}

func NewOrderHandler(fulfillmentService service.FulfillmentService) *OrderHandler {
	return &OrderHandler{
		fulfillmentService: fulfillmentService,
	}
}

// HandleOrderCompleted processes the ORDER_COMPLETED event
func (h *OrderHandler) HandleOrderCompleted(ctx context.Context, eventId string, createTime *timestamppb.Timestamp, eventData *eventbuspb.EventData) error {
	orderEvent := eventData.GetOrderEvent()
	if orderEvent == nil ||
		orderEvent.OrderStatus != orderpb.OrderStatus_COMPLETED {
		return nil
	}
	if orderEvent.GetSourceType() != orderpb.OrderSourceType_FULFILLMENT {
		return nil
	}
	zlog.Info(ctx, "Processing order completed event",
		zap.String("eventId", eventId),
		zap.String("eventData", eventData.String()),
		zap.Time("createTime", createTime.AsTime()),
	)

	// update fulfillment status to unconfirmed
	err := h.fulfillmentService.UpdateFulfillment(ctx, dto.UpdateFulfillmentDTO{
		FulfillmentID: orderEvent.SourceId,
		Status:        lo.ToPtr(fulfillmentpb.Status_UNCONFIRMED),
	})
	if err != nil {
		return err
	}

	return nil
}
