package consumer

import (
	"context"
	"github.com/MoeGolibrary/go-lib/zlog"
	eventbuspb "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/event_bus/v1"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service"
	"github.com/MoeGolibrary/moego-svc-fulfillment/internal/service/dto"
	"go.uber.org/zap"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type OfferingHandler struct {
	groupClassService service.GroupClassService
}

func NewOfferingHandler(groupClassService service.GroupClassService) *OfferingHandler {
	return &OfferingHandler{
		groupClassService: groupClassService,
	}
}

// HandleOfferingGroupClassSessionUpdated processes the OFFERING_GROUP_CLASS_SESSION_UPDATED event
func (h *OfferingHandler) HandleOfferingGroupClassSessionUpdated(ctx context.Context, eventId string, createTime *timestamppb.Timestamp, eventData *eventbuspb.EventData) error {
	sessionEvent := eventData.GetGroupClassSessionEvent()
	if sessionEvent == nil {
		return nil
	}
	zlog.Info(ctx, "Processing offering group class session updated event",
		zap.String("eventId", eventId),
		zap.String("eventData", eventData.String()),
		zap.Time("createTime", createTime.AsTime()),
	)

	if err := h.groupClassService.UpdateGroupClassSession(ctx, dto.UpdateGroupClassSessionDTO{
		GroupClassSessionID:  sessionEvent.Session.Id,
		GroupClassInstanceID: sessionEvent.Session.GroupClassInstanceId,
		BeforeInterval:       sessionEvent.BeforeInterval,
		AfterInterval:        sessionEvent.Session.Interval,
	}); err != nil {
		return err
	}

	return nil
}
