import com.maxmind.db.CHMCache;
import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.exception.GeoIp2Exception;
import com.maxmind.geoip2.model.CityResponse;
import java.io.IOException;
import java.net.InetAddress;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.core.io.ClassPathResource;
import org.springframework.util.Assert;

@Disabled
public class IPTest {

    @Test
    public void testIp() throws IOException, GeoIp2Exception {
        ClassPathResource classPathResource = new ClassPathResource("dbip-city-lite-2023-11.mmdb");
        DatabaseReader databaseReader = new DatabaseReader.Builder(classPathResource.getInputStream())
                .withCache(new CHMCache())
                .build();
        CityResponse city = databaseReader.city(InetAddress.getByName("2603:3021:259c:100:dc8c:6ba3:3d3d:d442"));
        Assert.notNull(city.getCity().getName(), "city name is null");
    }
}
