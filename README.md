# moego-svc-online-booking

## 开发方式

### 基于 Postgresql 和 MyBatis 的 DAO 层

1. DDL/DML 存放在 `sql` 目录, 格式为 `yyyy-MM-dd-<idx>.<title>.sql`, 目前还没有统一的管理 migration 的方案
2. 在 DDL 写入测试库, 执行 `./gradlew mbGenerator` 同步 Mapper 文件
3. 如果有增加/删除表, 需要在 [MyBatisGeneratorConfig.xml](./src/main/resources/MyBatisGeneratorConfig.xml) 中增加/删除对应的
   table.
4. 约定针对一张表, MBG 生产成的 mapper java 文件为 `mapper/base/BaseXxxMapper.java`, mapper xml
   文件为 `mapper/base/BaseXxxMapper.xml`,entity java 文件为 `entity/Xxx.java`,
   比如 `base/BaseTodoMapper.java`, `mapper/base/BaseTodoMapper.xml`, `entity/Todo.java`. **这些文件禁止人为修改**.
5. 业务相关的 mapper java 文件为 `mapper/xxx/XxxMapper.java`, mapper xml 文件为 `mapper/xxx/XxxMapper.xml`, 每一个 MBG
   生成的 base mapper 文件, 都有一个对应的 mapper 文件, 比如 `TodoMapper.java`, `TodoMapper.xml`. **这些文件可以人为修改
   **.

### 单测

1. 参考 `TodoControllerTest.java`
2. 基于 SpringRunner
3. 所有 MockBean 注入同一个基类 `MockStubs`, 所有测试文件都继承这个文件
4. 所有测试文件都需要使用 `@Transactional` 注解, 保证测试结束后, 数据库回滚

### 其它

1. `Todo` 相关的代码均为模板代码, 可以删除
