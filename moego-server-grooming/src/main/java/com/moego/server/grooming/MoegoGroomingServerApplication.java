package com.moego.server.grooming;

import com.moego.lib.aws.S3API;
import com.moego.lib.common.util.ThreadPoolUtil;
import com.moego.server.grooming.properties.GoogleReserveProperties;
import java.time.Duration;
import java.util.TimeZone;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.annotation.Bean;
import org.springframework.jms.annotation.EnableJms;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.web.client.RestTemplate;
import software.amazon.awssdk.regions.Region;

@EnableFeignClients({
    "com.moego.server.business.client",
    "com.moego.server.message.client",
    "com.moego.server.customer.client",
    "com.moego.server.payment.client",
    "com.moego.server.retail.client",
})
@SpringBootApplication(proxyBeanMethods = false)
@EnableConfigurationProperties({GoogleReserveProperties.class})
@MapperScan("com.moego.server.grooming.mapper")
@EnableAsync
@EnableJms
public class MoegoGroomingServerApplication {

    public static void main(String[] args) {
        TimeZone.setDefault(TimeZone.getTimeZone("UTC"));
        SpringApplication.run(MoegoGroomingServerApplication.class, args);
    }

    @Bean
    public RestTemplate restTemplate(RestTemplateBuilder builder) {
        return builder.build();
    }

    @Bean
    public S3API s3API(
            @Value("${s3.key}") String accessKey,
            @Value("${s3.secret}") String accessSecret,
            @Value("${s3.region}") String clientRegion) {
        return new S3API(Region.of(clientRegion), accessKey, accessSecret);
    }

    @Bean("taskExecutor")
    public Executor taskExecutor() {
        return ThreadPoolUtil.newExecutorService(
                20, 200, Duration.ofSeconds(60), 1, "moego-task-executor-", new ThreadPoolExecutor.CallerRunsPolicy());
    }
}
