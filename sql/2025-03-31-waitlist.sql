alter table public.booking_request
    add source smallint default 0 not null;

comment on column public.booking_request.source is 'BookingRequest 创建来源，see BookingRequestModel.Source ';

CREATE TABLE daycare_service_waitlist (
    id BIGSERIAL PRIMARY KEY,
    booking_request_id BIGINT NOT NULL DEFAULT 0,
        service_detail_id BIGINT NOT NULL DEFAULT 0,
    specific_dates jsonb NOT NULL default '[]'::jsonb ,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL
);

CREATE INDEX idx_booking_service_detail
ON daycare_service_waitlist (booking_request_id, service_detail_id);

CREATE TABLE boarding_service_waitlist (
    id BIGSERIAL PRIMARY KEY,
    booking_request_id BIGINT NOT NULL DEFAULT 0,
        service_detail_id BIGINT NOT NULL DEFAULT 0,
    start_date DATE NOT NULL DEFAULT CURRENT_DATE,
    end_date DATE NOT NULL DEFAULT CURRENT_DATE,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL
);
CREATE INDEX idx_boarding_service_waiting
ON daycare_service_waitlist (booking_request_id, service_detail_id);



CREATE TABLE booking_request_note (
    id BIGSERIAL PRIMARY KEY,
    company_id BIGINT NOT NULL DEFAULT 0,
    customer_id BIGINT NOT NULL DEFAULT 0,
    booking_request_id BIGINT NOT NULL DEFAULT 0,
    note varchar(255) NOT NULL DEFAULT '',
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITHOUT TIME ZONE DEFAULT NULL
);
CREATE INDEX idx_booking_request_id
ON booking_request_note (booking_request_id)
