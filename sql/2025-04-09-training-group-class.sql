CREATE TABLE "group_class_service_detail"
(
  "id"                   BIGSERIAL PRIMARY KEY,
  "booking_request_id"   BIGINT       NOT NULL DEFAULT 0,
  "pet_id"               BIGINT       NOT NULL DEFAULT 0,
  "class_instance_id"    BIGINT       NOT NULL DEFAULT 0,
  "staff_id"             BIGINT       NOT NULL DEFAULT 0,
  "service_id"           BIGINT       NOT NULL default 0,
  "service_price"        NUMERIC(10, 2) NOT NULL DEFAULT 0,
  "specific_dates"       JSONB                 DEFAULT '[]'::JSONB,
  "start_time"           INTEGER,
  "end_time"             INTEGER,
  "duration_per_session" INT          NOT NULL DEFAULT 0,
  "created_at"           TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "updated_at"           TIMESTAMP(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
  "deleted_at"           TIMESTAMP(6)
);
CREATE INDEX group_class_service_detail_idx_request_id ON group_class_service_detail (booking_request_id);
COMMENT
    ON TABLE group_class_service_detail IS 'The group class service detail table';
COMMENT
    ON COLUMN group_class_service_detail.booking_request_id IS 'The id of booking request';
COMMENT
    ON COLUMN group_class_service_detail.pet_id IS 'The id of pet, associated with the current service';
COMMENT
    ON COLUMN group_class_service_detail.staff_id IS 'The id of trainer, associated with the current service';
COMMENT
    ON COLUMN group_class_service_detail.service_id IS 'The id of current service';
COMMENT
    ON COLUMN group_class_service_detail.specific_dates IS 'The date list of the group class session, ["yyyy-MM-dd"]';
COMMENT
    ON COLUMN group_class_service_detail.start_time IS 'The start time of the service, unit minute, 540 means 09:00';
COMMENT
    ON COLUMN group_class_service_detail.end_time IS 'The end time of the service, unit minute, 540 means 09:00';
COMMENT
    ON COLUMN group_class_service_detail.duration_per_session IS 'Duration of each session in minutes, only for training';
