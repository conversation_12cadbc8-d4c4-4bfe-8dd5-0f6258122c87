CREATE TABLE "public"."staff_availability" (
    "id" bigserial primary key,
    "company_id" int8 NOT NULL DEFAULT 0,
    "business_id" int8 NOT NULL DEFAULT 0,
    "staff_id" int8 NOT NULL DEFAULT 0,
    "is_available" bool NOT NULL DEFAULT false,
    "created_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON COLUMN "public"."staff_availability"."is_available" IS 'staff available of ob';
COMMENT ON TABLE "public"."staff_availability" IS 'ob staff available setting';

CREATE UNIQUE INDEX "uniqed_staff_id" ON "public"."staff_availability" USING btree (
    "business_id",
    "staff_id"
);


CREATE TABLE "public"."staff_availability_slot_day" (
   "id" bigserial primary key,
   "company_id" int8 NOT NULL DEFAULT 0,
   "business_id" int8 NOT NULL DEFAULT 0,
   "staff_id" int8 NOT NULL DEFAULT 0,
   "day_of_week" int4 NOT NULL DEFAULT 0,
   "is_available" bool NOT NULL DEFAULT false,
   "start_time" int4 NOT NULL DEFAULT 0,
   "end_time" int4 NOT NULL DEFAULT 0,
   "capacity" int4 NOT NULL DEFAULT 0,
   "limit_ids" json NOT NULL DEFAULT '[]'::json,
   "created_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
   "updated_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE "public"."staff_availability_time_day" (
   "id" bigserial primary key,
   "company_id" int8 NOT NULL DEFAULT 0,
   "business_id" int8 NOT NULL DEFAULT 0,
   "staff_id" int8 NOT NULL DEFAULT 0,
   "day_of_week" int4 NOT NULL DEFAULT 0,
   "is_available" bool NOT NULL DEFAULT false,
   "limit_ids" json NOT NULL DEFAULT '[]'::json,
   "created_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
   "updated_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);
CREATE INDEX "idx_staff_slot_day" ON "public"."staff_availability_slot_day" USING btree (
    "staff_id",
    "day_of_week"
);
CREATE INDEX "idx_staff_time_day" ON "public"."staff_availability_time_day" USING btree (
    "staff_id",
    "day_of_week"
);


CREATE TABLE "public"."staff_availability_day_hour" (
    "id" bigserial primary key,
    "day_type" int4 NOT NULL DEFAULT 0,
    "day_id" int8 NOT NULL DEFAULT 0,
    "start_time" int4 NOT NULL DEFAULT 0,
    "end_time" int4 NOT NULL DEFAULT 0,
    "capacity" int4 NOT NULL DEFAULT 0,
    "limit_ids" json NOT NULL DEFAULT '[]'::json,
    "created_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX "idx_day" ON "public"."staff_availability_day_hour" (
      "day_id"
);

CREATE TABLE "public"."day_hour_limit" (
     "id" bigserial primary key,
     "type" int4 NOT NULL DEFAULT 0,
     "pet_size_ids" json NOT NULL DEFAULT '[]'::json,
     "pet_type_id" int8 NOT NULL DEFAULT 0,
     "is_all_breed" bool NOT NULL DEFAULT false,
     "breed_ids" json NOT NULL DEFAULT '[]'::json,
     "service_ids" json NOT NULL DEFAULT '[]'::json,
     "is_all_service" bool NOT NULL DEFAULT false,
     "capacity" int4 NOT NULL DEFAULT 0,
     "created_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
     "updated_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP
);
COMMENT ON TABLE "public"."day_hour_limit" IS 'slot/time limit setting';

