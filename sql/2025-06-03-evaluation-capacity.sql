
ALTER TABLE public.booking_time_range_setting
    ADD COLUMN capacity_setting_id BIGINT NOT NULL DEFAULT 0;

ALTER TABLE public.booking_time_range_setting
DROP CONSTRAINT booking_time_range_setting_company_id_business_id_service_i_key;

ALTER TABLE public.booking_time_range_setting
ADD CONSTRAINT booking_time_range_setting_cid_bid_sitype_csid_key
    UNIQUE (company_id, business_id, service_item_type, capacity_setting_id);

CREATE TABLE public.booking_capacity_setting (
    id bigserial primary key,
    name varchar(255)  default ''::character varying not null,
    service_ids JSONB NOT NULL DEFAULT '[]'::jsonb,
    is_all_service BOOLEAN NOT NULL DEFAULT false,
    created_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITHOUT TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
);
