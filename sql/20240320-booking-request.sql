-- Booking request
CREATE TABLE public.booking_request
(
  id                   bigserial                                   NOT NULL,
  company_id           int8         DEFAULT 0                      NOT NULL,
  business_id          int8         DEFAULT 0                      NOT NULL,
  customer_id          int8         DEFAULT 0                      NOT NULL,
  appointment_id       int8         DEFAULT 0                      NOT NULL,
  start_date           varchar(10)                                 NULL,
  start_time           int4                                        NULL,
  end_date             varchar(10)                                 NULL,
  end_time             int4                                        NULL,
  status               int4         DEFAULT 1                      NOT NULL,
  is_prepaid           bool         DEFAULT false                  NOT NULL,
  additional_note      varchar(255)                                NULL,
  source_platform      varchar(255) DEFAULT '':: character varying NOT NULL,
  service_type_include int4         DEFAULT 1                      NOT NULL,
  created_at           timestamp    DEFAULT CURRENT_TIMESTAMP      NOT NULL,
  updated_at           timestamp    DEFAULT CURRENT_TIMESTAMP      NOT NULL,
  deleted_at           timestamp                                   NULL,
  CONSTRAINT booking_request_pkey PRIMARY KEY (id)
);
CREATE INDEX booking_request_idx_bid_cid ON public.booking_request (customer_id, business_id);
CREATE INDEX booking_request_idx_appt_id ON public.booking_request (business_id, appointment_id);

COMMENT ON TABLE booking_request IS 'Booking request submitted by customer';
COMMENT ON COLUMN booking_request.appointment_id IS 'The appointment id generated after the business schedule';
COMMENT ON COLUMN booking_request.start_date IS 'The start date of the booking request, yyyy-MM-dd';
COMMENT ON COLUMN booking_request.start_time IS 'The start time of the booking request, unit minute, 540 means 09:00';
COMMENT ON COLUMN booking_request.end_date IS 'The end date of the booking request, yyyy-MM-dd';
COMMENT ON COLUMN booking_request.end_time IS 'The end time of the booking request, unit minute, 540 means 09:00';
COMMENT ON COLUMN booking_request.status IS '1: SUBMITTED, 2: WAIT_LIST, 3: SCHEDULED, 4: DECLINED, 5: DELETED';
COMMENT ON COLUMN booking_request.is_prepaid IS 'Whether the booking request is prepaid';
COMMENT ON COLUMN booking_request.additional_note IS 'Additional note for the booking request';
COMMENT ON COLUMN booking_request.source_platform IS 'The platform where the booking request is submitted, RESERVE_WITH_GOOGLE, PET_PARENT_APP';
COMMENT ON COLUMN booking_request.service_type_include IS 'The bitmap value for service, 1: grooming, 2: boarding, 3: daycare, 4: evaluation test';


-- Grooming service detail
CREATE TABLE public.grooming_service_detail
(
  id                 bigserial                                NOT NULL,
  booking_request_id int8           DEFAULT 0                 NOT NULL,
  pet_id             int8           DEFAULT 0                 NOT NULL,
  staff_id           int8           DEFAULT 0                 NOT NULL,
  service_id         int8           DEFAULT 0                 NOT NULL,
  service_time       int4           DEFAULT 0                 NOT NULL,
  service_price      numeric(10, 2) DEFAULT 0.00              NOT NULL,
  start_date         varchar(10)                              NULL,
  start_time         int4                                     NULL,
  end_date           varchar(10)                              NULL,
  end_time           int4                                     NULL,
  created_at         timestamp      DEFAULT CURRENT_TIMESTAMP NOT NULL,
  updated_at         timestamp      DEFAULT CURRENT_TIMESTAMP NOT NULL,
  deleted_at         timestamp                                NULL,
  CONSTRAINT grooming_service_detail_pkey PRIMARY KEY (id)
);
CREATE INDEX grooming_service_detail_idx_request_id ON public.grooming_service_detail (booking_request_id);

COMMENT ON TABLE grooming_service_detail IS 'The grooming service or add-on detail';
COMMENT ON COLUMN grooming_service_detail.booking_request_id IS 'The id of booking request';
COMMENT ON COLUMN grooming_service_detail.pet_id IS 'The id of pet, associated with the current service';
COMMENT ON COLUMN grooming_service_detail.staff_id IS 'The id of staff, associated with the current service';
COMMENT ON COLUMN grooming_service_detail.service_id IS 'The id of current service';
COMMENT ON COLUMN grooming_service_detail.service_time IS 'The time of current service, unit minute';
COMMENT ON COLUMN grooming_service_detail.service_price IS 'The price of current service';
COMMENT ON COLUMN grooming_service_detail.start_date IS 'The start date of the service, yyyy-MM-dd';
COMMENT ON COLUMN grooming_service_detail.start_time IS 'The start time of the service, unit minute, 540 means 09:00';
COMMENT ON COLUMN grooming_service_detail.end_date IS 'The end date of the service, yyyy-MM-dd';
COMMENT ON COLUMN grooming_service_detail.end_time IS 'The end time of the service, unit minute, 540 means 09:00';


-- Grooming add-on detail
CREATE TABLE public.grooming_add_on_detail
(
  id                 bigserial                                NOT NULL,
  booking_request_id int8           DEFAULT 0                 NOT NULL,
  pet_id             int8           DEFAULT 0                 NOT NULL,
  staff_id           int8           DEFAULT 0                 NOT NULL,
  service_detail_id  bigserial                                NOT NULL,
  add_on_id          int8           DEFAULT 0                 NOT NULL,
  service_time       int4           DEFAULT 0                 NOT NULL,
  service_price      numeric(10, 2) DEFAULT 0.00              NOT NULL,
  start_date         varchar(10)                              NULL,
  start_time         int4                                     NULL,
  end_date           varchar(10)                              NULL,
  end_time           int4                                     NULL,
  created_at         timestamp      DEFAULT CURRENT_TIMESTAMP NOT NULL,
  updated_at         timestamp      DEFAULT CURRENT_TIMESTAMP NOT NULL,
  deleted_at         timestamp                                NULL,
  CONSTRAINT grooming_add_on_detail_pkey PRIMARY KEY (id)
);
CREATE INDEX grooming_add_on_detail_idx_request_id ON public.grooming_add_on_detail (booking_request_id);

COMMENT ON TABLE grooming_add_on_detail IS 'The grooming service or add-on detail';
COMMENT ON COLUMN grooming_add_on_detail.booking_request_id IS 'The id of booking request';
COMMENT ON COLUMN grooming_add_on_detail.service_detail_id IS 'The id of grooming service detail, associated with the current add-on';
COMMENT ON COLUMN grooming_add_on_detail.pet_id IS 'The id of pet, associated with the current service';
COMMENT ON COLUMN grooming_add_on_detail.staff_id IS 'The id of staff, associated with the current service';
COMMENT ON COLUMN grooming_add_on_detail.add_on_id IS 'The id of current add-on service';
COMMENT ON COLUMN grooming_add_on_detail.service_time IS 'The time of current service, unit minute';
COMMENT ON COLUMN grooming_add_on_detail.service_price IS 'The price of current service';
COMMENT ON COLUMN grooming_add_on_detail.start_date IS 'The start date of the service, yyyy-MM-dd';
COMMENT ON COLUMN grooming_add_on_detail.start_time IS 'The start time of the service, unit minute, 540 means 09:00';
COMMENT ON COLUMN grooming_add_on_detail.end_date IS 'The end date of the service, yyyy-MM-dd';
COMMENT ON COLUMN grooming_add_on_detail.end_time IS 'The end time of the service, unit minute, 540 means 09:00';


CREATE TABLE public.grooming_auto_assign
(
  id                 bigserial NOT NULL,
  booking_request_id bigint    NOT NULL DEFAULT 0,
  staff_id           bigint             DEFAULT NULL,
  start_time         int                DEFAULT NULL,
  created_at         timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  updated_at         timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  deleted_at         timestamp NULL,
  CONSTRAINT grooming_auto_assign_pkey PRIMARY KEY (id)
);
CREATE INDEX grooming_auto_assign_idx_request_id ON public.grooming_auto_assign (booking_request_id);

COMMENT ON TABLE grooming_auto_assign IS 'appointment auto assign record';
COMMENT ON COLUMN grooming_auto_assign.booking_request_id IS 'The id of booking request';
COMMENT ON COLUMN grooming_auto_assign.staff_id IS 'The id of staff, auto assign staff id';
COMMENT ON COLUMN grooming_auto_assign.start_time IS 'auto assign appointment time, unit minute, 540 means 09:00';
