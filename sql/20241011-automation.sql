create table automation_setting
(
  id                    bigserial
    primary key,
  business_id           bigint    default 0                 not null,
  company_id            bigint    default 0                 not null,
  service_item_type     integer   default 0                 not null,
  auto_accept_condition jsonb     default '{}'::jsonb       not null,
  created_at            timestamp default CURRENT_TIMESTAMP not null,
  updated_at            timestamp default CURRENT_TIMESTAMP not null,
  update_by             bigint    default 0                 not null
);

create unique index automation_setting_idx_bid_service
  on automation_setting (business_id, service_item_type);
