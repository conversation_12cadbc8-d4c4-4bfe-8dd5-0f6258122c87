CREATE TABLE "public"."ob_customize_care_type" (
                                                 "id" bigserial PRIMARY KEY,
                                                 "name" VARCHAR (150) NOT NULL,
                                                 "company_id" BIGINT NOT NULL,
                                                 "business_id" BIGINT NOT NULL,
                                                 "description" TEXT,
                                                 "icon" VARCHAR (50) NOT NULL DEFAULT '',
                                                 "image" jsonb NOT NULL DEFAULT '""'::jsonb,
                                                 "sort" INT NOT NULL DEFAULT '0',
                                                 "service_type" SMALLINT NOT NULL DEFAULT '1',
                                                 "is_all_service_applicable" bool NOT NULL,
                                                 "selected_services" jsonb DEFAULT '[]'::jsonb,
                                                 "service_item_type" SMALLINT NOT NULL DEFAULT '1',
                                                 "updated_by" int8 NOT NULL,
                                                 "created_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                 "updated_at" timestamp(6) NOT NULL DEFAULT CURRENT_TIMESTAMP,
                                                 "deleted_at" timestamp(6)
);

CREATE UNIQUE INDEX idx_name
  ON ob_customize_care_type (company_id, business_id, "name") WHERE deleted_at IS NULL;
