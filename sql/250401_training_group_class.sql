-- fulfillment
drop table fulfillment;
-- auto-generated definition
create table fulfillment
(
    id                   bigint      default nextval('fulfillment_id_seq'::regclass) not null
        primary key,
    company_id           bigint      default 0                                       not null,
    business_id          bigint      default 0                                       not null,
    customer_id          bigint      default 0                                       not null,
    booking_request_id   bigint      default 0                                       not null,
    start_datetime       timestamp,
    end_datetime         timestamp,
    status               integer     default 1                                       not null,
    color_code           varchar(20) default '#000000'::character varying            not null,
    service_type_include integer     default 1                                       not null,
    source               integer     default 22018                                   not null,
    repeat_rule_id       integer     default 0                                       not null,
    created_at           timestamp   default now()                                   not null,
    updated_at           timestamp   default now()                                   not null,
    deleted_at           timestamp,
    order_id             bigint      default 0                                       not null
);

comment on table fulfillment is 'Fulfillment summary information, generated after a scheduled request has been scheduled';

comment on column fulfillment.booking_request_id is 'booking_request.id, A booking request may be scheduled for multiple fulfillment';

comment on column fulfillment.start_datetime is 'Minimum start date time for all service details in the current fulfillment';

comment on column fulfillment.end_datetime is 'Maximum start date time for all service details in the current fulfillment';

comment on column fulfillment.status is '1-unconfirmed, 2-confirmed, 3-finished, 4-canceled, 5-ready, 6-checked-in';

comment on column fulfillment.service_type_include is 'The value of the bit composition of the various types of services, 1-boarding, 2-daycare, 3-grooming, 4-evaluation, 5-dog_walking';

comment on column fulfillment.source is '22018-web, 22168-ob, 17216-android, 17802-ios, 23426-dm, 19826-gc, 23333-openapi';

comment on column fulfillment.repeat_rule_id is 'fulfillment_repeat_rule.id';

create index fulfillment_idx_tenant_customer
    on fulfillment (company_id, business_id, customer_id);

create index fulfillment_idx_created_at
    on fulfillment (created_at);


create table group_class_detail
(
    id                      bigserial
        primary key,
    fulfillment_id          bigint    default 0     not null,
    pet_id                  bigint    default 0     not null,
    group_class_id          bigint    default 0     not null,
    group_class_instance_id bigint    default 0     not null,
    status                  integer   default 0     not null,
    created_at              timestamp default now() not null,
    updated_at              timestamp default now() not null,
    deleted_at              timestamp
);

comment on table group_class_detail is 'Details of pet training group class service';
comment on column group_class_detail.fulfillment_id is 'fulfillment.id';
comment on column group_class_detail.pet_id is 'moe_customer_pet.id';
comment on column group_class_detail.status is '1-Not started, 2-In Progress, 3-Completed';
comment on column group_class_detail.group_class_id is 'group class id, service.id';
comment on column group_class_detail.group_class_instance_id is 'group_class_instance.id';
create index group_class_detail_idx_fulfillment on group_class_detail (fulfillment_id);
create unique index group_class_detail_uni_pet_instance on group_class_detail (group_class_instance_id, pet_id);


create table staff_time_slot
(
    id                 bigserial
        primary key,
    company_id         bigint    default 0     not null,
    business_id        bigint    default 0     not null,
    fulfillment_id     bigint    default 0     not null,
    care_type          integer   default 0     not null,
    detail_id          bigint    default 0     not null,
    order_line_item_id bigint    default 0     not null,
    staff_id           bigint    default 0     not null,
    pet_id             bigint    default 0     not null,
    customer_id        bigint    default 0     not null,
    start_datetime     timestamp               not null,
    end_datetime       timestamp               not null,
    created_at         timestamp default now() not null,
    updated_at         timestamp default now() not null,
    deleted_at         timestamp
);

comment on table staff_time_slot is 'Time slot details of staff';

comment on column staff_time_slot.fulfillment_id is 'fulfillment.id';
comment on column staff_time_slot.care_type is '1-Grooming, 2-Boarding, 3-Daycare, 4-Evaluation, 5-Dog_walking, 6-Training group class';
comment on column staff_time_slot.detail_id is 'care_type = 1 grooming_detail.id, care_type = 4 evaluation_detail.id, care_type = 5 dog_walking_detail.id';
comment on column staff_time_slot.order_line_item_id is 'order_line_item.id, Used to record whether the service has created an order';
comment on column staff_time_slot.staff_id is 'The staff id who performing the service or add-on';
comment on column staff_time_slot.start_datetime is 'The start date time of the this time slot';
comment on column staff_time_slot.end_datetime is 'The end date time of the this time slot';
create index staff_time_slot_idx_tenant_date
    on staff_time_slot (company_id, business_id, start_datetime);

create index staff_time_slot_idx_fulfillment
    on staff_time_slot (fulfillment_id);

create index staff_time_slot_idx_detail_id
    on staff_time_slot (care_type, detail_id);


create table group_class_attendance
(
    id                     bigserial
        primary key,
    company_id             bigint    default 0     not null,
    business_id            bigint    default 0     not null,
    fulfillment_id         bigint    default 0     not null,
    group_class_detail_id  bigint    default 0     not null,
    group_class_session_id bigint    default 0     not null,
    pet_id                 bigint    default 0     not null,
    check_in_time          timestamp               not null,
    created_at             timestamp default now() not null,
    updated_at             timestamp default now() not null,
    deleted_at             timestamp
);

comment on table group_class_attendance is 'Training attendance details of pet';

comment on column group_class_attendance.fulfillment_id is 'fulfillment.id';
comment on column group_class_attendance.group_class_detail_id is 'training_detail.id';
comment on column group_class_attendance.group_class_session_id is 'training_session.id';
comment on column group_class_attendance.check_in_time is 'Check in time of pet';

create unique index group_class_attendance_uni_pet_check_in
    on group_class_attendance (fulfillment_id, group_class_session_id, pet_id);

create index group_class_attendance_idx_session
    on group_class_attendance (group_class_session_id);
