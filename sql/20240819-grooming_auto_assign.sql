alter table public.grooming_auto_assign
  add column if not exists grooming_service_detail_id bigint not null default 0;

drop index grooming_auto_assign_idx_request_id;

create unique index if not exists grooming_auto_assign_grooming_service_detail_id_uindex ON
  public.grooming_auto_assign (booking_request_id, grooming_service_detail_id) WHERE deleted_at IS NULL;

create index if not exists booking_request_appointment_id_index ON
  public.booking_request (appointment_id) WHERE appointment_id != 0 and deleted_at IS NULL;
