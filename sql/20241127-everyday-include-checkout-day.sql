-- postgresql moego_online_booking
alter table public.boarding_add_on_detail
  add date_type integer default 3 not null;

comment on column public.boarding_add_on_detail.date_type is 'date type, 1-every day except checkout day, 2-specific date, 3-date point, 4-everyday';

update boarding_add_on_detail
set date_type = case
                  when is_everyday then 1
                  else 2
  end
where id > 0;
