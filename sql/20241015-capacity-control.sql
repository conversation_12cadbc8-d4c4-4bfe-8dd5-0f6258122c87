create table public.lodging_capacity_setting
(
    id                     bigserial primary key,
    company_id             bigint    default 0                  not null,
    business_id            bigint    default 0                  not null,
    service_item_type      integer   default 0                  not null,
    is_capacity_limited    boolean default false              not null,
    capacity_limit         integer   default 100                not null,
    unique (business_id, service_item_type)
);

comment on column public.lodging_capacity_setting.is_capacity_limited is 'if limit requests based on  lodging/area capacity';
comment on column public.lodging_capacity_setting.capacity_limit is 'limit requests based on service related lodging/area capacity';



