-- create table booking_request_appointment_mapping (Postgres)
\c moego_online_booking

create table if not exists public.booking_request_appointment_mapping
(
  id                 bigserial
    constraint booking_request_appointment_mapping_pk
      primary key,
  booking_request_id bigint    default 0                 not null,
  appointment_id     bigint    default 0                 not null,
  created_at         timestamp default CURRENT_TIMESTAMP not null,
  updated_at         timestamp default CURRENT_TIMESTAMP not null
);

comment on table public.booking_request_appointment_mapping is 'Appointment BookingRequest mapping';

comment on column public.booking_request_appointment_mapping.id is 'id';

comment on column public.booking_request_appointment_mapping.booking_request_id is 'booking request id';

comment on column public.booking_request_appointment_mapping.appointment_id is 'appointment id';

comment on column public.booking_request_appointment_mapping.created_at is 'create time';

comment on column public.booking_request_appointment_mapping.updated_at is 'update time';

create index if not exists booking_request_appointment_mapping_booking_request_id_idx
  on public.booking_request_appointment_mapping (booking_request_id);

create unique index if not exists booking_request_appointment_mapping_appointment_id_uidx
  on public.booking_request_appointment_mapping (appointment_id);
