CREATE SEQUENCE IF NOT EXISTS fulfillment_id_seq START WITH 1000000000;

CREATE TABLE IF NOT EXISTS fulfillment
(
    id                   bigint                        not null default nextval('fulfillment_id_seq'::regclass)
        primary key,
    company_id           bigint      default 0         not null,
    business_id          bigint      default 0         not null,
    customer_id          bigint      default 0         not null,
    booking_request_id   bigint      default 0         not null,
    start_datetime       timestamp   default null,
    end_datetime         timestamp   default null,
    status               int         default 1         not null,
    color_code           varchar(20) default '#000000' not null,
    service_type_include int         default 1         not null,
    source               int         default 22018     not null,
    created_at           timestamp   default now()     not null,
    updated_at           timestamp   default now()     not null,
    deleted_at           timestamp
);

comment on table fulfillment is 'Fulfillment summary information, generated after a scheduled request has been scheduled';

comment on column fulfillment.booking_request_id is 'booking_request.id, A booking request may be scheduled for multiple fulfillment';
comment on column fulfillment.start_datetime is 'Minimum start date time for all service details in the current fulfillment';
comment on column fulfillment.end_datetime is 'Maximum start date time for all service details in the current fulfillment';
comment on column fulfillment.status is '1-unconfirmed, 2-confirmed, 3-finished, 4-canceled, 5-ready, 6-checked-in';
comment on column fulfillment.service_type_include is 'The value of the bit composition of the various types of services, 1-boarding, 2-daycare, 3-grooming, 4-evaluation, 5-dog_walking';
comment on column fulfillment.source is '22018-web, 22168-ob, 17216-android, 17802-ios, 23426-dm, 19826-gc, 23333-openapi';

create index fulfillment_idx_tenant_customer
    on fulfillment (company_id, business_id, customer_id);