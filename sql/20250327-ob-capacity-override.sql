create table lodging_capacity_override
(
  id         bigserial
    constraint lodging_capacity_override_pk
      primary key,
  setting_id bigint    default 0                 not null,
  date_ranges jsonb     default '{}'::jsonb       not null,
  capacity   integer   default 0                 not null,
  unit_type  smallint  default 0                 not null,
  created_at timestamp default CURRENT_TIMESTAMP not null,
  updated_at timestamp default CURRENT_TIMESTAMP not null,
  deleted_at timestamp
);

comment on column lodging_capacity_override.setting_id is 'The id of lodging_capacity_setting';

comment on column lodging_capacity_override.date_ranges is 'array of date range json';

comment on column lodging_capacity_override.unit_type is '1.pet number  2.percent';

create index lodging_capacity_override_setting_id_index
  on lodging_capacity_override (setting_id);
