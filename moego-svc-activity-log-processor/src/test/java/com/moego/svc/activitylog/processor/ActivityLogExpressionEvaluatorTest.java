package com.moego.svc.activitylog.processor;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatExceptionOfType;

import java.lang.reflect.Method;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;
import org.springframework.context.expression.AnnotatedElementKey;
import org.springframework.expression.EvaluationContext;
import org.springframework.expression.spel.SpelEvaluationException;

/**
 * {@link ActivityLogExpressionEvaluator} tester.
 */
class ActivityLogExpressionEvaluatorTest {

    /**
     * {@link ActivityLogExpressionEvaluator#key(String, AnnotatedElementKey, EvaluationContext)}
     */
    @Test
    void key() throws NoSuchMethodException {
        var evaluator = new ActivityLogExpressionEvaluator();

        var t = new ActivityLogExpressionEvaluatorTest();
        Method method = ActivityLogExpressionEvaluatorTest.class.getMethod("group", List.class);
        Object[] args = {List.of("a", "b", "c")};
        var result = Map.of(
                "a", List.of("a"),
                "b", List.of("b"),
                "c", List.of("c"));
        var ctx = evaluator.createEvaluationContext(method, args, t, t.getClass(), result, null);
        AnnotatedElementKey mk = new AnnotatedElementKey(method, t.getClass());

        assertThat(evaluator.key("#root.args[0][0]", mk, ctx)).isEqualTo("a");
        assertThat(evaluator.key("#root.args[0][1]", mk, ctx)).isEqualTo("b");
        assertThat(evaluator.key("#root.args[0][2]", mk, ctx)).isEqualTo("c");
        assertThat(evaluator.key("#p0[0]", mk, ctx)).isEqualTo("a");
        assertThat(evaluator.key("#p0[1]", mk, ctx)).isEqualTo("b");
        assertThat(evaluator.key("#a0[0]", mk, ctx)).isEqualTo("a");
        assertThat(evaluator.key("#a0[1]", mk, ctx)).isEqualTo("b");

        assertThatExceptionOfType(SpelEvaluationException.class)
                .isThrownBy(() -> evaluator.key("p0", mk, ctx))
                .withMessageContaining("Property or field 'p0' cannot be found");
        assertThatExceptionOfType(SpelEvaluationException.class)
                .isThrownBy(() -> evaluator.key("a0", mk, ctx))
                .withMessageContaining("Property or field 'a0' cannot be found");

        // use parameter name directly
        assertThat(evaluator.key("#arr[2]", mk, ctx)).isEqualTo("c");
        assertThat(evaluator.key("#arr[0]", mk, ctx)).isEqualTo("a");
        assertThat(evaluator.key("#arr[1]", mk, ctx)).isEqualTo("b");
        assertThat(evaluator.key("#arr[2]", mk, ctx)).isEqualTo("c");

        assertThat(evaluator.key("#root.target", mk, ctx)).isEqualTo(t);
        assertThat(evaluator.key("#root.method", mk, ctx)).isEqualTo(method);
        assertThat(evaluator.key("#root.targetClass", mk, ctx)).isEqualTo(t.getClass());
        assertThat(evaluator.key("#root.args", mk, ctx)).isEqualTo(args);

        // RootObject properties
        assertThat(evaluator.key("target", mk, ctx)).isEqualTo(t);
        assertThat(evaluator.key("method", mk, ctx)).isEqualTo(method);
        assertThat(evaluator.key("targetClass", mk, ctx)).isEqualTo(t.getClass());
        assertThat(evaluator.key("args", mk, ctx)).isEqualTo(args);

        assertThat(evaluator.key("#result", mk, ctx)).isEqualTo(result);
        assertThatExceptionOfType(SpelEvaluationException.class)
                .isThrownBy(() -> evaluator.key("#root.result", mk, ctx));

        assertThat(evaluator.key("'Hello World'.bytes.length", mk, ctx)).isEqualTo(11);
        assertThat(evaluator.key("2 < 3", mk, ctx)).isEqualTo(true);
        assertThat(evaluator.key("T(java.util.Map).of('a', 1)", mk, ctx)).isEqualTo(Map.of("a", 1));
        assertThat(evaluator.key("{'a': 1}", mk, ctx)).isEqualTo(Map.of("a", 1));
        assertThat(evaluator.key("{'a': #arr[0]}", mk, ctx)).isEqualTo(Map.of("a", "a"));
    }

    public Map<String, List<String>> group(List<String> arr) {
        return arr.stream().collect(Collectors.groupingBy(String::toString));
    }
}
