<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC
        "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="PostgresSQL" targetRuntime="MyBatis3" defaultModelType="flat">
        <commentGenerator>
            <property name="suppressDate" value="true"/>
            <property name="addRemarkComments" value="true"/>
        </commentGenerator>

        <jdbcConnection
                driverClass="org.postgresql.Driver"
                connectionURL="***************************************************"
                userId="moego_developer_240310_eff7a0dc"
                password="G0MxI7NM_jX_f7Ky73vnrwej97xg1tly"
        />

        <javaModelGenerator
                targetProject="src/main/java"
                targetPackage="com.moego.svc.order.repository.entity"
        >
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <sqlMapGenerator
                targetPackage="mapper"
                targetProject="src/main/resources"
        />

        <javaClientGenerator
                targetProject="src/main/java"
                targetPackage="com.moego.svc.order.repository.mapper"
                type="XMLMAPPER"
        >
        </javaClientGenerator>

        <!--                <table tableName="order"-->
        <!--                       enableCountByExample="false"-->
        <!--                       enableSelectByExample="false"-->
        <!--                       enableUpdateByExample="false"-->
        <!--                       enableDeleteByExample="false"-->
        <!--                       schema="public">-->

        <!--                    <generatedKey column="id" sqlStatement="JDBC"/>-->
        <!--                    <columnOverride column="status" javaType="Integer" jdbcType="INTEGER"/>-->
        <!--                </table>-->
        <!--                <table tableName="order_payment"-->
        <!--                       enableCountByExample="false"-->
        <!--                       enableSelectByExample="false"-->
        <!--                       enableUpdateByExample="false"-->
        <!--                       enableDeleteByExample="false"-->
        <!--                       schema="public">-->
        <!--                    <generatedKey column="id" sqlStatement="JDBC"/>-->
        <!--                    <columnOverride column="payment_method_extra" javaType="java.lang.String"-->
        <!--                                    typeHandler="com.moego.svc.order.utils.StringToJsonbTypeHandler"/>-->
        <!--                </table>-->
        <!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
        <!--            <columnOverride column="status" javaType="Integer" jdbcType="INTEGER"/>-->
        <!--        </table>-->

        <!--                        <table tableName="order_payment_retry_log"-->
        <!--                               enableCountByExample="true"-->
        <!--                               enableSelectByExample="true"-->
        <!--                               enableUpdateByExample="true"-->
        <!--                               enableDeleteByExample="true">-->

        <!--                            <generatedKey column="id" sqlStatement="JDBC"/>-->
        <!--                        </table>-->

        <!--                <table tableName="message_delivery"-->
        <!--                       enableCountByExample="false"-->
        <!--                       enableSelectByExample="false"-->
        <!--                       enableUpdateByExample="false"-->
        <!--                       enableDeleteByExample="false">-->

        <!--                    <generatedKey column="id" sqlStatement="JDBC"/>-->
        <!--                </table>-->

        <!--        <table tableName="order_grooming_detail_rel"-->
        <!--               enableCountByExample="false"-->
        <!--               enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"-->
        <!--               enableDeleteByExample="false">-->
        <!--        <table tableName="order_grooming_detail_rel"-->
        <!--               enableCountByExample="false"-->
        <!--               enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"-->
        <!--               enableDeleteByExample="false">-->

        <!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
        <!--        </table>-->

        <!--                <table tableName="order_staff_split_detail"-->
        <!--                       enableCountByExample="false"-->
        <!--                       enableSelectByExample="false"-->
        <!--                       enableUpdateByExample="false"-->
        <!--                       enableDeleteByExample="false"-->
        <!--                       schema="public">-->

        <!--                    <generatedKey column="id" sqlStatement="JDBC"/>-->
        <!--                </table>-->

        <!--        <table tableName="order_tips_split_detail"-->
        <!--               enableCountByExample="false"-->
        <!--               enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"-->
        <!--               enableDeleteByExample="false"-->
        <!--               schema="public">-->

        <!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
        <!--        </table>-->

        <!--        <table tableName="staff_payroll_change_log"-->
        <!--               enableCountByExample="false"-->
        <!--               enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"-->
        <!--               enableDeleteByExample="false"-->
        <!--               schema="public">-->

        <!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
        <!--        </table>-->

<!--                        <table tableName="order_line_item"-->
<!--                               enableCountByExample="false"-->
<!--                               enableSelectByExample="false"-->
<!--                               enableUpdateByExample="false"-->
<!--                               enableDeleteByExample="false">-->

<!--                            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--                            <columnOverride column="sub_type"-->
<!--                                            javaType="com.moego.idl.models.order.v1.ItemSubType"/>-->

<!--                        </table>-->

        <!--                <table tableName="order_line_tax"-->
        <!--                           enableCountByExample="false"-->
        <!--                           enableSelectByExample="false"-->
        <!--                           enableUpdateByExample="false"-->
        <!--                           enableDeleteByExample="false">-->

        <!--                    <generatedKey column="id" sqlStatement="JDBC"/>-->
        <!--                </table>-->

        <!--        <table tableName="order_line_discount"-->
        <!--               enableCountByExample="false"-->
        <!--               enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"-->
        <!--               enableDeleteByExample="false">-->

        <!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
        <!--        </table>-->

        <!--        <table tableName="order_line_extra_fee"-->
        <!--               enableCountByExample="false"-->
        <!--               enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"-->
        <!--               enableDeleteByExample="false">-->

        <!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
        <!--        </table>-->

        <!--                <table tableName="order_tip_split_record"-->
        <!--                       enableCountByExample="false"-->
        <!--                       enableSelectByExample="false"-->
        <!--                       enableUpdateByExample="false"-->
        <!--                       enableDeleteByExample="false">-->

        <!--                    <generatedKey column="id" sqlStatement="JDBC"/>-->
        <!--                </table>-->

<!--        <table tableName="service_charge"-->
<!--               enableCountByExample="true"-->
<!--               enableSelectByExample="true"-->
<!--               enableUpdateByExample="true"-->
<!--               enableDeleteByExample="false">-->
<!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
<!--            <columnOverride column="auto_apply_status"-->
<!--                            javaType="com.moego.idl.models.order.v1.ServiceCharge.AutoApplyStatus"-->
<!--                            typeHandler="com.moego.svc.order.repository.mapper.typehandler.AutoApplyStatusTypeHandler"/>-->
<!--            <columnOverride column="auto_apply_condition"-->
<!--                            javaType="com.moego.idl.models.order.v1.ServiceCharge.AutoApplyCondition"-->
<!--                            typeHandler="com.moego.svc.order.repository.mapper.typehandler.AutoApplyConditionTypeHandler"/>-->
<!--            <columnOverride column="auto_apply_time_type"-->
<!--                            javaType="com.moego.idl.models.order.v1.ServiceCharge.AutoApplyTimeType"-->
<!--                            typeHandler="com.moego.svc.order.repository.mapper.typehandler.AutoApplyTimeTypeHandler"/>-->
<!--            <columnOverride column="service_item_types"-->
<!--                            javaType="java.util.List&lt;com.moego.idl.models.offering.v1.ServiceItemType&gt;"-->
<!--                            typeHandler="com.moego.svc.order.repository.mapper.typehandler.ServiceItemTypesTypeHandler"/>-->
<!--            <columnOverride column="apply_type"-->
<!--                            javaType="com.moego.idl.models.order.v1.ServiceCharge.ApplyType"-->
<!--                            typeHandler="com.moego.svc.order.repository.mapper.typehandler.ApplyTypeHandler"/>-->
<!--            <columnOverride column="food_source_ids"-->
<!--                            javaType="java.util.List&lt;Long&gt;"-->
<!--                            typeHandler="com.moego.svc.order.repository.mapper.typehandler.PostgresArrayTypeHandler"/>-->
<!--            <columnOverride column="charge_method"-->
<!--                            javaType="com.moego.idl.models.order.v1.ChargeMethod"-->
<!--                            typeHandler="com.moego.svc.order.repository.mapper.typehandler.ChargeMethodHandler"/>-->
<!--            <columnOverride column="surcharge_type"-->
<!--                            javaType="java.lang.Integer"-->
<!--                            jdbcType="SMALLINT"/>-->

<!--            <columnOverride column="time_based_pricing_type"-->
<!--                            javaType="com.moego.idl.models.order.v1.ServiceCharge.TimeBasedPricingType"-->
<!--                            typeHandler="com.moego.svc.order.repository.mapper.typehandler.TimeBasedPricingTypeHandler"/>-->
<!--            <columnOverride column="multiple_pets_charge_type"-->
<!--                            javaType="com.moego.idl.models.order.v1.ServiceCharge.MultiplePetsChargeType"-->
<!--                            typeHandler="com.moego.svc.order.repository.mapper.typehandler.MultiplePetsChargeTypeHandler"/>-->
<!--            <columnOverride column="hourly_exceed_rules"-->
<!--                            javaType="java.util.List&lt;com.moego.idl.models.order.v1.ServiceChargeExceedHourRule&gt;"-->
<!--                            typeHandler="com.moego.svc.order.repository.mapper.typehandler.ServiceChargeExceedHourRuleHandler"/>-->

<!--            <columnOverride column="service_filter_rules"-->
<!--                            javaType="java.util.List&lt;com.moego.idl.models.order.v1.ServiceFilter&gt;"-->
<!--                            typeHandler="com.moego.svc.order.repository.mapper.typehandler.ServiceFilterRulesHandler"/>-->
<!--            <columnOverride column="source"-->
<!--                            javaType="com.moego.idl.models.order.v1.ServiceCharge.Source"-->
<!--                            typeHandler="com.moego.svc.order.repository.mapper.typehandler.SourceHandler"/>-->

<!--        </table>-->

        <!--        <table tableName="service_charge_location"-->
        <!--               enableCountByExample="false"-->
        <!--               enableSelectByExample="false"-->
        <!--               enableUpdateByExample="false"-->
        <!--               enableDeleteByExample="false">-->

        <!--            <generatedKey column="id" sqlStatement="JDBC"/>-->
        <!--        </table>-->

<!--        <table tableName="deposit_change_log"-->
<!--               enableCountByExample="false"-->
<!--               enableSelectByExample="false"-->
<!--               enableUpdateByExample="false"-->
<!--               enableDeleteByExample="false">-->
<!--            <generatedKey column="id" sqlStatement="JDBC" />-->
<!--        </table>-->
    </context>
</generatorConfiguration>
