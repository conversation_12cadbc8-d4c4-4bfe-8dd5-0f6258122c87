<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE generatorConfiguration PUBLIC
  "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
  "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">
<!-- use mbGenerator: https://moego.atlassian.net/wiki/spaces/~************************/pages/79233368 -->
<generatorConfiguration>
  <context id="Postgres" targetRuntime="MyBatis3DynamicSql" defaultModelType="flat">
    <property name="javaFileEncoding" value="UTF-8"/>
    <property name="beginningDelimiter" value='"'/>
    <property name="endingDelimiter" value='"'/>
    <property name="autoDelimitKeywords" value='true'/>

    <plugin type="org.mybatis.generator.plugins.ToStringPlugin"/>
    <plugin type="org.mybatis.generator.plugins.EqualsHashCodePlugin"/>
    <plugin type="com.moego.lib.common.mybatisplugins.DisableGeneratedMapperMethodsPlugin"/>
    <plugin type="com.moego.lib.common.mybatisplugins.DynamicDataSourcePlugin"/>
    <plugin type="com.moego.lib.common.mybatisplugins.DeprecatedColumnsPlugin"/>

    <commentGenerator>
      <property name="suppressDate" value="true"/>
      <property name="addRemarkComments" value="true"/>
    </commentGenerator>

    <jdbcConnection
      driverClass="org.postgresql.Driver"
      connectionURL="******************************************************************"
      userId="moego_developer_240310_eff7a0dc"
      password="G0MxI7NM_jX_f7Ky73vnrwej97xg1tly"/>

    <javaModelGenerator
      targetPackage="com.moego.svc.online.booking.entity"
      targetProject="src/main/java"/>

    <javaClientGenerator
      targetPackage="com.moego.svc.online.booking.mapper"
      targetProject="src/main/java">
      <property name="rootInterface" value="org.mybatis.dynamic.sql.util.mybatis3.CommonSelectMapper"/>
    </javaClientGenerator>

    <!-- 不要注释下面的 table！！！每次都全量生成，保证代码和表结构同步 -->
    <table tableName="accept_pet_setting">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="accepted_pet_types" javaType="java.lang.Integer[]"
                      typeHandler="org.apache.ibatis.type.ArrayTypeHandler"/>
    </table>
    <table tableName="boarding_add_on_detail">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="specific_dates"
                      javaType="java.util.List&lt;java.time.LocalDate&gt;"
                      typeHandler="com.moego.svc.online.booking.typehandler.BoardingAddOnDetailSpecificDatesTypeHandler"/>
      <columnOverride column="start_date" javaType="java.time.LocalDate"/>
      <columnOverride column="date_type"
                      javaType="com.moego.idl.models.appointment.v1.PetDetailDateType"
                      typeHandler="com.moego.svc.online.booking.typehandler.PetDetailDateTypeTypeHandler"/>
      <columnOverride column="is_everyday">
        <property name="deprecated" value="true"/>
        <property name="deprecatedSince" value="2025-02-24"/>
        <property name="deprecatedForRemoval" value="false"/>
        <property name="deprecatedDescription" value="Use date_type instead"/>
      </columnOverride>
    </table>
    <table tableName="boarding_auto_assign">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
    </table>
    <table tableName="boarding_service_detail">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="start_date" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToDateTypeHandler"/>
      <columnOverride column="end_date" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToDateTypeHandler"/>
    </table>
    <table tableName="booking_date_range_setting">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
    </table>
    <table tableName="booking_request">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="status"
                      javaType="com.moego.idl.models.online_booking.v1.BookingRequestStatus"
                      typeHandler="com.moego.svc.online.booking.typehandler.BookingRequestStatusTypeHandler"/>
      <columnOverride column="start_date" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToDateTypeHandler"/>
      <columnOverride column="end_date" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToDateTypeHandler"/>
      <columnOverride column="attr" javaType="com.moego.idl.models.online_booking.v1.BookingRequestModel.Attr"
                      typeHandler="com.moego.svc.online.booking.typehandler.BookingRequestAttrTypeHandler"/>
      <columnOverride column="payment_status"
                      javaType="com.moego.idl.models.online_booking.v1.BookingRequestModel.PaymentStatus"
                      typeHandler="com.moego.svc.online.booking.typehandler.BookingRequestPaymentStatusTypeHandler"/>
      <columnOverride column="is_prepaid">
        <property name="deprecated" value="true"/>
        <property name="deprecatedSince" value="2025-01-22"/>
        <property name="deprecatedForRemoval" value="false"/>
        <property name="deprecatedDescription" value="Use payment_status instead"/>
      </columnOverride>
      <columnOverride column="source"
                      javaType="com.moego.idl.models.online_booking.v1.BookingRequestModel.Source"
                      typeHandler="com.moego.svc.online.booking.typehandler.BookingRequestSourceTypeHandler"/>
    </table>
    <table tableName="daycare_add_on_detail">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="specific_dates" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler"/>
    </table>
    <table tableName="daycare_service_detail">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="specific_dates" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler"/>
    </table>
    <table tableName="evaluation_test_detail">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="start_date" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToDateTypeHandler"/>
      <columnOverride column="end_date" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToDateTypeHandler"/>
    </table>
    <table tableName="feeding">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="time" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler"/>
    </table>
    <table tableName="grooming_add_on_detail">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="start_date" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToDateTypeHandler"/>
      <columnOverride column="end_date" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToDateTypeHandler"/>
    </table>
    <table tableName="grooming_auto_assign">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
    </table>
    <table tableName="grooming_service_detail">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="start_date" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToDateTypeHandler"/>
      <columnOverride column="end_date" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToDateTypeHandler"/>
      <columnOverride column="date_type"
                      javaType="com.moego.idl.models.appointment.v1.PetDetailDateType"
                      typeHandler="com.moego.svc.online.booking.typehandler.PetDetailDateTypeTypeHandler"/>
    </table>
    <table tableName="medication">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="time" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler"/>
      <columnOverride column="date_type"
                      javaType="com.moego.idl.models.business_customer.v1.FeedingMedicationScheduleDateType"
                      typeHandler="com.moego.svc.online.booking.typehandler.MedicationDateTypeTypeHandler"/>
      <columnOverride column="specific_dates"
                      javaType="java.util.List&lt;java.lang.String&gt;"
                      typeHandler="com.moego.svc.online.booking.typehandler.MedicationSpecificDatesTypeHandler"/>
    </table>
    <table tableName="block_customer">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
    </table>
    <table tableName="booking_time_range_setting">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
    </table>
    <table tableName="booking_time_range_detail">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="first_week" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler"/>
      <columnOverride column="second_week" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler"/>
      <columnOverride column="third_week" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler"/>
      <columnOverride column="forth_week" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler"/>
    </table>
    <table tableName="accept_customer_setting">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
    </table>

    <table tableName="automation_setting">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="auto_accept_condition" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler"/>
    </table>
    <table tableName="staff_availability">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
    </table>
    <table tableName="staff_availability_slot_day">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="limit_ids"
                      javaType="java.util.List&lt;Long&gt;"
                      typeHandler="com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler"/>
      <columnOverride column="note" property="note" javaType="String"/>
    </table>
    <table tableName="staff_availability_time_day">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="limit_ids"
                      javaType="java.util.List&lt;Long&gt;"
                      typeHandler="com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler"/>
      <columnOverride column="note" property="note" javaType="String"/>
    </table>
    <table tableName="staff_availability_day_hour">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="limit_ids"
                      javaType="java.util.List&lt;Long&gt;"
                      typeHandler="com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler"/>
      <columnOverride column="note" property="note" javaType="String"/>
    </table>
    <table tableName="day_hour_limit">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="pet_size_ids"
                      javaType="java.util.List&lt;Long&gt;"
                      typeHandler="com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler"/>
      <columnOverride column="breed_ids"
                      javaType="java.util.List&lt;Long&gt;"
                      typeHandler="com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler"/>
      <columnOverride column="service_ids"
                      javaType="java.util.List&lt;Long&gt;"
                      typeHandler="com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler"/>
    </table>
    <table tableName="lodging_capacity_setting">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
    </table>
    <table tableName="day_hour_limit_group">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
    </table>

    <table tableName="booking_time_range_override">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="start_date" javaType="java.time.LocalDate"/>
      <columnOverride column="end_date" javaType="java.time.LocalDate"/>
      <columnOverride column="created_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="updated_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="deleted_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="day_time_range" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler"/>

    </table>
    <table tableName="booking_request_appointment_mapping">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
    </table>

    <table tableName="dog_walking_service_detail">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="start_date" javaType="java.time.LocalDate"/>
      <columnOverride column="end_date" javaType="java.time.LocalDate"/>
      <columnOverride column="created_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="updated_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="deleted_at" javaType="java.time.LocalDateTime"/>
    </table>
    <table tableName="daycare_service_waitlist">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="specific_dates" javaType="java.util.List&lt;java.time.LocalDate>"
                      typeHandler="com.moego.svc.online.booking.typehandler.DaycareServiceWaitlistSpecificDatesTypeHandler"/>
      <columnOverride column="created_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="updated_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="deleted_at" javaType="java.time.LocalDateTime"/>
    </table>

    <table tableName="boarding_service_waitlist">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="start_date" javaType="java.time.LocalDate"/>
      <columnOverride column="end_date" javaType="java.time.LocalDate"/>
      <columnOverride column="created_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="updated_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="deleted_at" javaType="java.time.LocalDateTime"/>
    </table>

    <table tableName="booking_request_note">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="created_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="updated_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="deleted_at" javaType="java.time.LocalDateTime"/>
    </table>

    <table tableName="lodging_capacity_override">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="unit_type" javaType="com.moego.idl.models.online_booking.v1.CapacityOverrideUnitType"
                      typeHandler="com.moego.svc.online.booking.typehandler.CapacityOverrideUnitTypeHandler"/>
      <columnOverride column="created_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="updated_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="deleted_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="date_ranges"
                      javaType="java.util.List&lt;com.moego.idl.models.online_booking.v1.CapacityOverrideModel.CapacityDateRange&gt;"
                      typeHandler="com.moego.svc.online.booking.typehandler.CapacityDateRangeListTypeHandler"/>
    </table>

    <table tableName="group_class_service_detail">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="specific_dates" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler"/>
      <columnOverride column="created_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="updated_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="deleted_at" javaType="java.time.LocalDateTime"/>
    </table>
    <table tableName="booking_capacity_setting">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="service_ids"
                      javaType="java.util.List&lt;Long&gt;"
                      typeHandler="com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler"/>
      <columnOverride column="created_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="updated_at" javaType="java.time.LocalDateTime"/>
    </table>

    <table tableName="ob_customize_care_type">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
      <columnOverride column="image" javaType="java.lang.String"
                      typeHandler="com.moego.svc.online.booking.typehandler.StringToJsonbTypeHandler"/>
      <columnOverride column="service_type" javaType="com.moego.idl.models.offering.v1.ServiceType"
                      typeHandler="com.moego.svc.online.booking.typehandler.ServiceTypeHandler"/>
      <columnOverride column="service_item_type" javaType="com.moego.idl.models.offering.v1.ServiceItemType"
                      typeHandler="com.moego.svc.online.booking.typehandler.ServiceItemTypeHandler"/>
      <columnOverride column="selected_services"
                      javaType="java.util.List&lt;Long&gt;"
                      typeHandler="com.moego.svc.online.booking.typehandler.JsonArrayTypeHandler"/>
      <columnOverride column="created_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="updated_at" javaType="java.time.LocalDateTime"/>
      <columnOverride column="deleted_at" javaType="java.time.LocalDateTime"/>
    </table>

    <table tableName="staff_slot_free_service">
      <generatedKey column="id" sqlStatement="JDBC" identity="true"/>
    </table>

  </context>
</generatorConfiguration>
