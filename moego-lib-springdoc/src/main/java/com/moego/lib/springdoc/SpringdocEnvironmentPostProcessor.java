package com.moego.lib.springdoc;

import java.util.Properties;
import org.apache.commons.logging.Log;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.boot.logging.DeferredLogFactory;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.PropertiesPropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

/**
 * <AUTHOR>
 * @since 2024/7/10
 */
public class SpringdocEnvironmentPostProcessor implements EnvironmentPostProcessor {

    private final Log log;

    public SpringdocEnvironmentPostProcessor(DeferredLogFactory logFactory) {
        this.log = logFactory.getLog(getClass());
    }

    /**
     * Configurations to be loaded.
     */
    private static final String[] FILES = {"application-default-config-springdoc.yml"};

    @Override
    public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
        for (String location : FILES) {
            ClassPathResource resource = new ClassPathResource(location);
            environment.getPropertySources().addLast(loadProperties(resource));
            log.info("Loaded springdoc default configuration from " + location);
        }
    }

    private PropertySource<?> loadProperties(Resource resource) {
        String filename = resource.getFilename();
        if (!resource.exists()) {
            log.warn(filename + " doesn't exist");
        }

        // parse yaml
        YamlPropertiesFactoryBean bean = new YamlPropertiesFactoryBean();
        bean.setResources(resource);

        Properties prop = bean.getObject();

        assert filename != null;
        assert prop != null;
        return new PropertiesPropertySource(filename, prop);
    }
}
