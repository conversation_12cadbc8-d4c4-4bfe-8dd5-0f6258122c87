package com.moego.lib.springdoc;

import com.fasterxml.jackson.databind.JavaType;
import io.swagger.v3.core.converter.AnnotatedType;
import io.swagger.v3.core.converter.ModelConverter;
import io.swagger.v3.core.converter.ModelConverterContext;
import io.swagger.v3.oas.models.media.ObjectSchema;
import io.swagger.v3.oas.models.media.Schema;
import io.swagger.v3.oas.models.media.StringSchema;
import java.util.Iterator;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.springdoc.core.providers.ObjectMapperProvider;

/**
 * <AUTHOR>
 */
@SuppressWarnings("rawtypes")
@RequiredArgsConstructor
public class IgnoreStripeClassesModelConverter implements ModelConverter {

    private final ObjectMapperProvider springDocObjectMapper;

    @Override
    public Schema resolve(
            AnnotatedType annotatedType,
            ModelConverterContext modelConverterContext,
            Iterator<ModelConverter> iterator) {
        JavaType javaType = springDocObjectMapper.jsonMapper().constructType(annotatedType.getType());
        Class<?> type = javaType.getRawClass();
        if (type != null && type.getPackageName().startsWith("com.stripe.")) {
            ObjectSchema objectSchema = new ObjectSchema();
            objectSchema.required(List.of("__stripe__"));
            objectSchema.addProperty("__stripe__", new StringSchema().addEnumItem(type.getName()));
            return objectSchema;
        }
        return iterator.hasNext() ? iterator.next().resolve(annotatedType, modelConverterContext, iterator) : null;
    }
}
