package com.moego.lib.springdoc;

import com.moego.lib.common.auth.AuthContext;
import org.springdoc.core.configuration.SpringDocConfiguration;
import org.springdoc.core.providers.ObjectMapperProvider;
import org.springdoc.core.utils.SpringDocUtils;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@AutoConfiguration(after = SpringDocConfiguration.class)
@ConditionalOnBean(ObjectMapperProvider.class) // springdoc is enabled
public class MoeGoSpringdocAutoConfiguration implements InitializingBean {

    @Override
    public void afterPropertiesSet() {
        // TODO(Freeman): 在前端生成 ts 脚本迁移到 openapi 之后，应该不需要这个配置了
        SpringDocUtils.getConfig().replaceWithClass(Byte.class, Integer.class);
    }

    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass(AuthContext.class)
    static class IgnoreAuthContextConfiguration implements InitializingBean {

        @Override
        public void afterPropertiesSet() {
            SpringDocUtils.getConfig().addRequestWrapperToIgnore(AuthContext.class);
        }
    }

    /**
     * 为了兼容以前的魔改逻辑
     *
     * @see <a href="https://moegoworkspace.slack.com/archives/C0194CRRBQC/p1698143623715689?thread_ts=**********.390579&cid=C0194CRRBQC">Slack</a>
     */
    @Configuration(proxyBeanMethods = false)
    @ConditionalOnClass(name = "com.stripe.model.Account")
    static class IgnoreStripeClassesConfiguration {
        @Bean
        public IgnoreStripeClassesModelConverter ignoreStripeClassesModelConverter(
                ObjectMapperProvider springDocObjectMapper) {
            return new IgnoreStripeClassesModelConverter(springDocObjectMapper);
        }
    }
}
